import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { CommercialAutoPolicyInfoComponent } from './commercial-auto-policy-info.component';

describe('CommercialAutoPolicyInfoComponent', () => {
  let component: CommercialAutoPolicyInfoComponent;
  let fixture: ComponentFixture<CommercialAutoPolicyInfoComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ CommercialAutoPolicyInfoComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CommercialAutoPolicyInfoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
