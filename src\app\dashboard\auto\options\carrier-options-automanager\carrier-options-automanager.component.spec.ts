import { StorageGlobalService } from './../../../../shared/services/storage-global.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';


import { StubAgencyUserServiceProvider } from 'testing/stubs/services/agency-user.service.provider';
import { StubOptionsServiceProvider } from 'testing/stubs/services/options.service.provider';
import { StubQuotesServiceProvider } from 'testing/stubs/services/quotes.service.provider';
import { StubSpecsServiceProvider } from 'testing/stubs/services/specs.service.provider';
import { StubSubsServiceProvider } from 'testing/stubs/services/subs.service.provider';

import { CarrierOptionsAutomanagerComponent } from './carrier-options-automanager.component';

describe('CarrierOptionsAutomanagerComponent', () => {
  let component: CarrierOptionsAutomanagerComponent;
  let fixture: ComponentFixture<CarrierOptionsAutomanagerComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [CarrierOptionsAutomanagerComponent],
      providers: [
        StorageService,
        StubAgencyUserServiceProvider,
        StubSpecsServiceProvider,
        StubOptionsServiceProvider,
        StubQuotesServiceProvider,
        StubSubsServiceProvider,
        StorageGlobalService
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CarrierOptionsAutomanagerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });
});
