import {
    generateCarriers,
    generateLabels,
    generateViewFieldIds,
    generateViewURIs,
    runConditions,
} from 'testing/helpers/warning-definitions';

import { WARNINGS_DEFINITIONS_AUTO_DRIVER } from 'app/hints-and-warnings/data';

import { Driver } from 'app/app-model/driver';
import { AdditionalDataI } from '../../../model/warnings';


describe('Definitions: Driver', () => {
    beforeEach(() => {
        jasmine.clock().mockDate(new Date(2017, 3, 27));
    });

    describe('when auto driver validator is used', () => {
        let definitions: any[];
        let additionalData: AdditionalDataI;
        let driver: Driver;

        beforeEach(() => {
            definitions = WARNINGS_DEFINITIONS_AUTO_DRIVER;
            additionalData = {
                quoteSelectedPlans: [],
                quoteSelectedPlansIds: ['25'],
                specLobTowns: []
            };
            driver = {
                coverages: {
                    meta: {
                        href: ''
                    }
                },
                dateOfBirth: '',
                deferredDriver: true,
                exclusions: {},
                firstLicensed: '',
                firstLicensedState: '',
                firstName: '',
                gender: '',
                incidents: {
                    meta: {
                        href: ''
                    }
                },
                lastName: '',
                licenseDate: '',
                licenseNumber: '',
                licenseState: '',
                maritalStatus: '',
                meta: {
                    href: ''
                },
                middleName: '',
                motorcycle: false,
                motorcycleLicenseDate: '',
                motorcycleLicenseType: '',
                overideCarrier: false,
                parentId: '',
                quoteSessionId: '',
                relationshipToInsured: '',
                resourceId: '',
                resourceName: '',
                sdip: ''
            };
        });

        it('allows to check if required fields are provided', () => {
            const errors = runConditions(definitions, driver, additionalData);

            expect(errors).toEqual([
                'firstName', 'lastName', 'dateOfBirth', 'relationshipToInsured', 'licenseNumber',
                'firstLicensed', 'licenseState', 'sdip'
            ]);
        });

        it('allows to check if required fields are provided when driver uses motorcycle', () => {
            driver.motorcycle = true;

            let errors = runConditions(definitions, driver, additionalData);

            expect(errors).toEqual(jasmine.arrayContaining([
                'motorcycleLicenseType'
            ]));

            driver.motorcycleLicenseType = 'License';

            errors = runConditions(definitions, driver, additionalData);

            expect(errors).toEqual(jasmine.arrayContaining([
                'motorcycleLicenseDate'
            ]));
        });

        describe('generate without errors', () => {
            beforeEach(() => {
                driver.motorcycle = true;
                driver.motorcycleLicenseType = 'License';
            });

            it('viewFieldIds', () => {
                expect(() => {
                    generateViewFieldIds(definitions, driver);
                }).not.toThrow();
            });

            it('viewURIs', () => {
                expect(() => {
                    generateViewURIs(definitions, driver);
                }).not.toThrow();
            });

            it('labels', () => {
                expect(() => {
                    generateLabels(definitions, driver);
                }).not.toThrow();
            });

            it('carriers', () => {
                expect(() => {
                    generateCarriers(definitions, driver);
                }).not.toThrow();
            });
        });
    });
});
