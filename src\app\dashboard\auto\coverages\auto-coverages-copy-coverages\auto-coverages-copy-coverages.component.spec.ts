import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { StorageService } from 'app/shared/services/storage-new.service';
import { VehiclesService } from 'app/dashboard/app-services/vehicles.service';
import { OptionsService } from 'app/dashboard/app-services/options.service';
import { PremiumsService } from 'app/dashboard/auto/premiums/premiums.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';


import { AutoCoveragesCopyCoveragesComponent } from './auto-coverages-copy-coverages.component';
import { StubModalboxComponent } from 'testing/stubs/components/modalbox.component';
import { StubAutocompleteComponent } from 'testing/stubs/components/autocomplete.component';
import { FormsModule } from '@angular/forms';
import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { LocationsService } from 'app/dashboard/app-services/locations.service';

describe('AutoCoveragesCopyCoveragesComponent', () => {
  let component: AutoCoveragesCopyCoveragesComponent;
  let fixture: ComponentFixture<AutoCoveragesCopyCoveragesComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [FormsModule],
      declarations: [
        AutoCoveragesCopyCoveragesComponent,
        StubModalboxComponent,
        StubAutocompleteComponent
      ],
      providers: [
        StorageService,
        VehiclesService,
        OptionsService,
        ApiCommonService,
        PremiumsService,
        QuotesService,
        LocationsService
      ]

    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AutoCoveragesCopyCoveragesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
