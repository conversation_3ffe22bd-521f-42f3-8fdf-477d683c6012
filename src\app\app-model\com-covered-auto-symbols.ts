import { ApiResponse, Resource, MetaData } from './_common';

export class CoveredAutoSymbolsApiResponseData extends ApiResponse<CoveredAutoSymbols> { }
export class CoveredAutoSymbolsSpecApiResponseData extends ApiResponse<CoveredAutoSymbols> { }

export class CoveredAutoSymbols extends Resource {
  public symbols: CoveredAutoSymbolItem[] = [];
  public meta: MetaData = { href: '', rel: [] };
}

export class CoveredAutoSymbolItem {
  constructor(
    public linkedCoverageCode: string = '',
    public coveredAutoSymbolType: string = '',
    public meta: any = null,
    public resourceName: string = 'CoveredAutoSymbols',
    public coveredAutoSymbolValues: string[] = []
  ) {}
}

export class SymbolValue {
  public valueId?: string;
  public value = '';
  public description?: string;

  constructor() {
    this.valueId = '';
    this.description = '';
  }
}
