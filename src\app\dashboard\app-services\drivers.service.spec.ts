import { Driver } from './../../app-model/driver';
import { expect } from './../../../testing/helpers/expect';
import { expectLastConnectionUrl, DataCustomMatchers, expectLastConnectionPayload, expectLastCallArgs } from 'testing/helpers/all';
import { inject, TestBed } from '@angular/core/testing';
import { MockBackend } from 'testing/setups/mock-backend';

import { data as DRIVERS } from 'testing/data/quotes/drivers';
import { data as DRIVER } from 'testing/data/quotes/driver';
import { AUTO_QUOTE as AUTO_QUOTE } from 'testing/data/quotes/quote-auto';
import { data as COVERAGES } from 'testing/data/drivers/coverages';
import { setupMockBackend } from 'testing/setups/mock-backend';
import {
    StubQuotesService, StubQuotesServiceProvider
} from 'testing/stubs/services/quotes.service.provider';

import { StorageService } from 'app/shared/services/storage-new.service';

import { Helpers } from '../../utils/helpers';
import { DriversService } from './drivers.service';
import { QuotesService } from './quotes.service';
import { SpecsService } from './specs.service';

describe('Service: Drivers', () => {
  let service: DriversService;
  let mockBackend: MockBackend;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        DriversService,
        StubQuotesServiceProvider,
        StorageService,
        { provide: SpecsService, useValue: {} },  // unused
      ]
    });

    jasmine.addMatchers(DataCustomMatchers);
  });

  beforeEach(inject([DriversService], (_service: DriversService) => {
    service = _service;
  }));

  it('can instantiate service when inject service', () => {
    expect(service).toBeTruthy();
  });

  it('can delete driver by ID', () => {
    mockBackend = setupMockBackend(DRIVER);

    service.deleteDriverById('quote-id', 'driver-id').subscribe();

    expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/drivers/driver-id');
  });

  it('can delete driver by URI when newQuote is available in QuotesService', inject(
    [QuotesService], (quotesService: StubQuotesService) => {
      quotesService.newQuote = Helpers.deepClone(AUTO_QUOTE);
      mockBackend = setupMockBackend(DRIVER);

      service.deleteDriver('/quotes/quote-id/drivers/driver-id').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/drivers/driver-id');
    }));

  it('does not delete driver by URI when newQuote is not available in QuotesService', inject(
    [QuotesService], (quotesService: StubQuotesService) => {
      quotesService.newQuote = undefined;
      mockBackend = setupMockBackend(DRIVER);

      service.deleteDriver('/quotes/quote-id/drivers/driver-id').subscribe();

      expect(mockBackend.connectionsArray.length).toEqual(0);
    }));

  it('can retrieve drivers list by URI', () => {
    mockBackend = setupMockBackend(DRIVERS);

    service.getDriversListByUri('/quotes/quote-id/drivers').subscribe();

    expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/drivers');
  });

  it('can retrieve drivers list by quote ID', () => {
    mockBackend = setupMockBackend(DRIVERS);

    service.getDriversList('quote-id').subscribe();

    expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/drivers');
  });

  it('can create new driver by URI and set tempDriver property', () => {
    mockBackend = setupMockBackend(DRIVER);

    service.createDriverByUri('/quotes/quote-id/drivers', DRIVER).subscribe();

    expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/drivers');
    expectLastConnectionPayload(mockBackend).toEqual(DRIVER);
    expect(service.tempDriver).toEqual(DRIVER);
  });

  it('can create new driver by quote ID and set tempDriver property', () => {
    mockBackend = setupMockBackend(DRIVER);

    service.createDriver('quote-id', DRIVER).subscribe();

    expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/drivers');
    expectLastConnectionPayload(mockBackend).toEqual(DRIVER);
    expect(service.tempDriver).toEqual(DRIVER);
  });

  it('can update driver by URI', () => {
    mockBackend = setupMockBackend(DRIVER);

    service.updateDriverByUri('/quotes/quote-id/drivers/driver-id', DRIVER).subscribe();

    expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/drivers/driver-id');
    expectLastConnectionPayload(mockBackend).toEqual(DRIVER);
  });

  it('can update temporary driver on server and save it in storage', inject(
    [StorageService], (storageService: StorageService) => {
      mockBackend = setupMockBackend(DRIVER);
      spyOn(storageService, 'updateDriversListSingleItem');

      const tempDriver = new Driver();
      tempDriver.meta.href = '/quotes/quote-id/drivers/temp-driver-id';
      service.tempDriver = tempDriver;

      service.updateDriver(DRIVER).subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/drivers/temp-driver-id');
      expectLastConnectionPayload(mockBackend).toEqual(DRIVER);
      expectLastCallArgs(storageService.updateDriversListSingleItem).toEqual([
        DRIVER.parentId,
        DRIVER
      ]);
      expect(service.tempDriver).not.toEqual(DRIVER);
    }));

  it('does not update temporary driver if it does not exist', () => {
    mockBackend = setupMockBackend(DRIVER);

    service.updateDriver(DRIVER).subscribe();

    expect(mockBackend.connectionsArray.length).toEqual(0);
  });

  it('can update single driver by data and save it in storage', inject(
    [StorageService], (storageService: StorageService) => {
      mockBackend = setupMockBackend(DRIVER);
      spyOn(storageService, 'updateDriversListSingleItem');

      service.updateDriverIndependently(DRIVER).subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith(
        '/quotes/5c006258-19ef-46e2-9418-36fbf39ed43c/drivers/e22b5a20-804e-4e09-a620-30537000c21a'
      );
      expectLastConnectionPayload(mockBackend).toEqual(DRIVER);
      expectLastCallArgs(storageService.updateDriversListSingleItem).toEqual([
        DRIVER.parentId,
        DRIVER
      ]);
    }));

  it('does nothing when trying to update single driver by data it has no resourceId', () => {
    mockBackend = setupMockBackend(DRIVER);

    service.updateDriverIndependently({}).subscribe();
    expect(mockBackend.connectionsArray.length).toEqual(0);

    service.updateDriverIndependently(new Driver()).subscribe();
    expect(mockBackend.connectionsArray.length).toEqual(0);
  });

  it('allows to retrieve driver coverages by URI', () => {
    mockBackend = setupMockBackend(COVERAGES);

    service.getDriverOptions('/drivers/driver-id/coverages').subscribe();

    expectLastConnectionUrl(mockBackend).toEndWith('/drivers/driver-id/coverages');
  });

  it('allows to update driver coverages by URI', () => {
    mockBackend = setupMockBackend(COVERAGES.items[0]);

    service.updateDriverOptions('/drivers/driver-id/coverages/coverage-id', COVERAGES.items[0].coverages).subscribe();

    expectLastConnectionUrl(mockBackend).toEndWith('/drivers/driver-id/coverages/coverage-id');
    expectLastConnectionPayload(mockBackend).toEqual(jasmine.objectContaining({
      coverages: COVERAGES.items[0].coverages
    }));
  });

  it('allows to create driver coverages by data', () => {
    mockBackend = setupMockBackend(COVERAGES.items[0]);

    service.setDriverOptions(DRIVER, COVERAGES.items[0].coverages).subscribe();

    expectLastConnectionUrl(mockBackend).toEndWith('/drivers/e22b5a20-804e-4e09-a620-30537000c21a/coverages');
    expectLastConnectionPayload(mockBackend).toEqual(jasmine.objectContaining({
      coverages: COVERAGES.items[0].coverages
    }));
  });

  it('does nothing when trying to create driver coverages by data and it has no resourceId', () => {
    mockBackend = setupMockBackend(COVERAGES.items[0]);

    service.setDriverOptions(new Driver(), COVERAGES.items[0].coverages).subscribe();

    expect(mockBackend.connectionsArray.length).toEqual(0);
  });

  it('can generate driver name string from Driver object', () => {
    expect(service.generateDriverName(new Driver())).toEqual('New Driver');
    expect(service.generateDriverName(DRIVER)).toEqual('Anna Newton');
  });
});
