import {take} from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { BehaviorSubject ,  SubscriptionLike as ISubscription } from 'rxjs';

// Services
import { OptionsService } from 'app/dashboard/app-services/options.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { SubsService } from 'app/dashboard/app-services/subs.service';
import { AgencyUserService } from 'app/shared/services/agency-user.service';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';

// Models
import {
  QuoteHome,
  QuotePlan,
  QuotePlanListAPIResponse
} from 'app/app-model/quote';
import { FilterOption } from 'app/app-model/filter-option';
import {
  Coverage,
  PolicyCoveragesData,
  CoverageItem,
  CoverageItemParsed,
  CoverageItemChild,
  PolicyItemDefaultDataAPIResponse
} from 'app/app-model/coverage';
import { Dwelling, DwellingsSubsystemsAPIResponse, DwellingSubsystems } from 'app/app-model/dwelling';
import { CoverageStandard } from 'app/app-model/basics';

interface TriggerSourceI {
  triggerBy?:
    | 'SelectedPlans'
    | 'QuoteFormType'
    | 'QuoteEffectiveDate'
    | 'Dwelling'
    | 'Basics'
    | 'Options';
}

interface PromiseDataAfterSettingDefaultValuesNew {
  policiesToUpdate: CoverageItemParsed[];
  policyItemsParsed: CoverageItemParsed[];
}

interface PromiseDataAfterSavingToApi {
  status: string;
  policyItemsParsed: CoverageItemParsed[];
}

@Injectable()
export class HomeCarrierOptionsAutomanagerService {
  private serviceIsInitialized = false;

  private subscriptionQuote: ISubscription;
  private subscriptionQuoteIsNew: ISubscription;
  private subscriptionSelectedPlans: ISubscription;
  private subscriptionSelectedQuoteTypes: ISubscription;
  private subscriptionParserTrigger: ISubscription;
  private subscriptionGeneralOptionsPolicyItemHomeParsed: ISubscription;
  private subscriptionDwellingData: ISubscription;
  private subscribtionHomeCarrierOptionsParsedForErrorAndAvailabilityStatus: ISubscription;

  private _parserTrigger: BehaviorSubject<TriggerSourceI> = new BehaviorSubject(
    {}
  );

  private quote: QuoteHome;
  private quoteId = '';
  private quoteLob = '';
  private quoteFormTypes: string[] = [];
  private quoteIsNew = false;
  private selectedPlans: QuotePlan[] = [];
  private selectedPlansIds: string[] = [];
  private quoteBeforeChange: QuoteHome = null;
  public dwelling: Dwelling = new Dwelling();
  formType;
  // Required for updating coverages - we need all options from every view
  private generalOptionsPolicies: CoverageItemParsed[] = [];

  private preventParsingPoliciesUntilDataSavedToAPIServer = false;
  private subscriptionSubsystems: ISubscription;
  private subsystems: DwellingsSubsystemsAPIResponse = new DwellingsSubsystemsAPIResponse();
  public subsystemItems: DwellingSubsystems = new DwellingSubsystems();
  homeQuoteCoverages: CoverageStandard;

  constructor(
    private storageService: StorageService,
    private agencyUserService: AgencyUserService,
    private specsService: SpecsService,
    private optionsService: OptionsService,
    private subsService: SubsService,
    private storageGlobalService: StorageGlobalService
  ) {}

  public initialize(): Promise<void> {
    this.generalOptionsPolicies = [];
    if (this.serviceIsInitialized) {
      return Promise.resolve();
    }

    this.serviceIsInitialized = true;
    console.log('Initialize HomeCarrierOptionsAutomanagerService');

    return Promise.all([
      this.subscribeQuote(),
      this.subscribeQuoteIsNew(),
      this.subscribeSelectedPlans(),
      this.subscribeSelectedQuoteFormTypes(),
      this.subscribeGeneralOptionsPolicyItemHomeParsed(),
      this.subscribeQuoteDwelling(),
      this.subscribeQuoteCoverages()
    ])
      .then(() => {
        return this.initPolicyItemsParser();
      })
      .then(() => {
        return this.subscribeHomeCarrierOptionsParsedForErrorAndAvailabilityStatus();
      })
      .catch(err => {
        console.log(err);
      });
  }

  public destroy() {
    this.serviceIsInitialized = false;

    this.subscriptionQuote && this.subscriptionQuote.unsubscribe();
    this.subscriptionQuoteIsNew && this.subscriptionQuoteIsNew.unsubscribe();
    this.subscriptionSelectedPlans &&
      this.subscriptionSelectedPlans.unsubscribe();
    this.subscriptionSelectedQuoteTypes &&
      this.subscriptionSelectedQuoteTypes.unsubscribe();
    this.subscriptionParserTrigger &&
      this.subscriptionParserTrigger.unsubscribe();
    this.subscriptionGeneralOptionsPolicyItemHomeParsed &&
      this.subscriptionGeneralOptionsPolicyItemHomeParsed.unsubscribe();
    this.subscriptionDwellingData &&
      this.subscriptionDwellingData.unsubscribe();
    this.subscribtionHomeCarrierOptionsParsedForErrorAndAvailabilityStatus &&
      this.subscribtionHomeCarrierOptionsParsedForErrorAndAvailabilityStatus.unsubscribe();
  }

  private subscribeQuote(): Promise<QuoteHome> {
    return new Promise((resolve, reject) => {
      this.subscriptionQuote = this.storageService
        .getStorageData('selectedQuote')
        .subscribe((quote: QuoteHome) => {
          this.quote = JSON.parse(JSON.stringify(quote));
          this.quoteId = quote.resourceId;
          this.quoteLob = quote.lob;
          this.formType = quote.formType;
          resolve(this.quote);

          if (!this.quoteBeforeChange) {
            this.quoteBeforeChange = JSON.parse(JSON.stringify(quote));
          } else if (
            this.quoteBeforeChange.effectiveDate !== this.quote.effectiveDate
          ) {
            this.quoteBeforeChange = JSON.parse(JSON.stringify(quote));

            // INIT POLICY ITEMS PARSING After Quote Effective date change
            // Emit Observable to Parse Items
            this._parserTrigger.next({ triggerBy: 'QuoteEffectiveDate' });
          }
        });
    });
  }

  private subscribeSelectedQuoteFormTypes(): Promise<string[]> {
    return new Promise((resolve, reject) => {
      this.subscriptionSelectedQuoteTypes = this.storageService
        .getStorageData('selectedQuoteFormTypes')
        .subscribe((res: string[]) => {
          this.quoteFormTypes = res;
         if (this.quoteIsNew) { this.formType = res[0]; }
          resolve(this.quoteFormTypes);

          // Emmit Observable to Parse Items
          this._parserTrigger.next({ triggerBy: 'QuoteFormType' });
        });
    });
  }

  private subscribeQuoteIsNew(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.subscriptionQuoteIsNew = this.storageService
        .getStorageData('isNewQuote')
        .subscribe((res: boolean) => {
          this.quoteIsNew = res;
          resolve(this.quoteIsNew);
        });
    });
  }

  private subscribeSelectedPlans(): Promise<QuotePlan[]> {
    return new Promise((resolve, reject) => {
      this.subscriptionSelectedPlans = this.storageService
        .getStorageData('selectedPlan')
        .subscribe((res: QuotePlanListAPIResponse) => {
          if (res && res.items && res.items.length) {
            this.selectedPlans = JSON.parse(JSON.stringify(res.items[0].items));
            this.selectedPlansIds = this.selectedPlans.map(
              plan => plan.ratingPlanId
            );
            resolve(this.selectedPlans);

            // INIT POLICY ITEMS PARSING
            // Emmit Observable to Parse Items
            this._parserTrigger.next({ triggerBy: 'SelectedPlans' });
          }
        });
    });
  }

  private subscribeGeneralOptionsPolicyItemHomeParsed(): void {
    this.subscriptionGeneralOptionsPolicyItemHomeParsed = this.storageService
      .getStorageData('homeGeneralOptionsParsed')
      .subscribe((items: CoverageItemParsed[]) => {
        if (items.length !== 0) {
        this.generalOptionsPolicies = items;
      //      this._parserTrigger.next({ triggerBy: 'Options' });

        }
      });
  }

  private subscribeQuoteDwelling() {
    this.subscriptionDwellingData = this.storageService
      .getStorageData('dwelling')
      .subscribe(data => {
        if (data) {
          this.dwelling = JSON.parse(JSON.stringify(data));

          // INFO:: Please DO NOT invoke the 'this.getOptionsPoliciesListAndParseData' directly
          // Instead please use 'this._parserTrigger' Observable to avoid item parsing before all required data is ready.
          // Emmit Observable to Parse Items
          this._parserTrigger.next({ triggerBy: 'Dwelling' });
        }
      });
  }

   private subscribeQuoteCoverages() {
    this.subscriptionDwellingData = this.storageService
      .getStorageData('homeQuoteCoverages')
      .subscribe(data => {
        if (data) {
          this.homeQuoteCoverages = JSON.parse(JSON.stringify(data));
          // INFO:: Please DO NOT invoke the 'this.getOptionsPoliciesListAndParseData' directly
          // Instead please use 'this._parserTrigger' Observable to avoid item parsing before all required data is ready.
          // Emmit Observable to Parse Items
          this._parserTrigger.next({ triggerBy: 'Basics' });
        }
      });
  }

  private  getSubsystems(): DwellingSubsystems {
    this.subscriptionSubsystems = this.storageService.getStorageData('dwellingSubsystems').subscribe(data => {
      if (data && data.meta && data.meta.href) {
        if (this.dwelling && this.dwelling.subsystems && this.dwelling.subsystems.href
          && this.dwelling.subsystems.href === data.meta.href) {
          this.subsystems = JSON.parse(JSON.stringify(data));
          if (this.subsystems && this.subsystems.items && this.subsystems.items.length) {
            this.subsystemItems = this.subsystems.items[0];
          }
        }
      }
    });
    return this.subsystemItems;
  }

  private initPolicyItemsParser(): Promise<void> {
    const delay = 500;
    let timer;

    return new Promise((resolve, reject) => {
      this.subscriptionParserTrigger = this._parserTrigger
        .asObservable()
        .subscribe(res => {
          timer && clearTimeout(timer);

          // Prevent geting Policies and parse data until data is saved to API with default values - if necessary
          if (this.preventParsingPoliciesUntilDataSavedToAPIServer) { return; }

          timer = setTimeout(() => {
            this.getOptionsPoliciesListAndParseData()
              .then(() => resolve())
              .catch(err => reject(err));
          }, delay);
        });
    });
  }

  private getOptionsPoliciesListAndParseData(): Promise<void> {
    const selectedPlansIdsString = this.selectedPlansIds.join(',');
    const states = this.quote.state;
    const quoteFormTypesString = this.quoteFormTypes.join(',');
    const quoteEffectiveDate =
      this.quote && this.quote.effectiveDate ? this.quote.effectiveDate : '';

    let agencyId;
    this.agencyUserService.userData$.pipe(
      take(1))
      .subscribe(agent => (agencyId = agent.agencyId));

    return new Promise((resolve, reject) => {
      this.specsService
        .getRatingCoverages(
          states,
          this.quoteLob,
          'policyOptionsCarrier',
          selectedPlansIdsString,
          quoteFormTypesString,
          quoteEffectiveDate
        ).pipe(
        take(1))
        .subscribe(
          res => {
            let policiesItemsHome: CoverageItem[] = [];
            if (res && res.items && res.items.length) {
              policiesItemsHome = [...res.items];
            }

            if (policiesItemsHome && this.quoteId) {
              let policyItemsHomeParsed: CoverageItemParsed[] = [];
              this.processPoliciesItemsParsing(
                policiesItemsHome,
                this.selectedPlans,
                this.quoteId,
                this.quoteFormTypes
              )
                .then((res: CoverageItemParsed[]) => {
                  return (policyItemsHomeParsed = JSON.parse(
                    JSON.stringify(res)
                  ));
                })
                .then((res: CoverageItemParsed[]) => {
                  policyItemsHomeParsed = res;
                  return this.setDefaultValuesForOptionsIfThisIsNewQuote(
                    policyItemsHomeParsed,
                    agencyId,
                    this.quoteIsNew
                  );
                })
                .then((res: PromiseDataAfterSettingDefaultValuesNew) => {
                  // Save To Storage
                  policyItemsHomeParsed = res.policyItemsParsed;
                  this.storageService.setStorageData(
                    'homeCarrierOptionsParsed',
                    policyItemsHomeParsed
                  );

                  // Update API with default Values
                  return this.savePoliciesToApiIfNewPoliciesToUpdate(
                    policyItemsHomeParsed,
                    res.policiesToUpdate
                  );
                })
                .then(() => resolve())
                .catch(err => {
                  console.log(err);
                  reject(err);
                });
            }
          },
          err => reject(err)
        );
    });
  }

  public processPoliciesItemsParsing(
    policies: CoverageItem[],
    selectedPlans: QuotePlan[],
    quoteId: string,
    quoteSelectedFormTypes: string[]
  ): Promise<CoverageItemParsed[]> {
    let arrPoliciesItemsParsed = this.optionsService.parsePoliciesItemsToPoliciesItemsParsed(
      policies
    );
    arrPoliciesItemsParsed = this.optionsService.orderObjectsArrayByProperty(
      arrPoliciesItemsParsed,
      'description'
    );

    // Required for setting default values if new Quote - to check if options
    // has been alredy saved in storage or not
    let policiesItemsFromStorage: CoverageItemParsed[] = [];

    this.storageService
      .getStorageData('homeCarrierOptionsParsed').pipe(
      take(1))
      .subscribe(res => (policiesItemsFromStorage = res));
    // --------------------------------------------------------------------------

    return this.loadPolicyCoverages(quoteId).then(
      (data: PolicyCoveragesData) => {
        console.log('%c CARRIER OPTIONS, Loaded Coverages', 'color:blue', data);
        arrPoliciesItemsParsed = arrPoliciesItemsParsed.map(item => {
          item = this.setAdditionalDataForCoverageItemParsed(item);
          item = this.optionsService.sortPolicyItemParsedChildItems(item);
          item = this.optionsService.setPolicyItemStatusBasedOnPolicyCoverages(
            item,
            data.coverages
          );
          item = this.setPolicyItemStatusBasedOnRequirements(
            item,
            quoteSelectedFormTypes
          );

          item.endpointUrl = data.endpointURL;
          item = this.optionsService.setPolicyItemParsedIsNewFromAPIValue(
            item,
            policiesItemsFromStorage
          );
          item.quoteResourceId = quoteId;

          if (item.inputType !== 'Dropdown') {
            item.values = this.optionsService.orderObjectsArrayByProperty(
              item.values,
              'value'
            );
          }

          return item;
        });

        return arrPoliciesItemsParsed;
      }
    );
  }

  private loadPolicyCoverages(quoteId: string): Promise<PolicyCoveragesData> {
    const policyCoverageData: PolicyCoveragesData = new PolicyCoveragesData();

    return new Promise((resolve, reject) => {
      if (quoteId) {
        this.optionsService
          .getPolicies(quoteId).pipe(
          take(1))
          .subscribe(res => {
            if (res.items.length === 0) {
              console.log(
                '%c CARRIER OPTION Creating Resource for coverages',
                'color: red'
              );
              this.optionsService
                .createCoverageByUri(res.meta.href).pipe(
                take(1))
                .subscribe(res => {
                  policyCoverageData.coverages = res.coverages;
                  policyCoverageData.endpointURL = res.meta.href;

                  console.log(
                    '%c CARRIER OPTION Created Resource for coverages',
                    'color: green',
                    res
                  );
                  resolve(policyCoverageData);
                });
            } else {
              policyCoverageData.coverages = res.items[0].coverages;

              if (res.items[0].meta && res.items[0].meta.href) {
                policyCoverageData.endpointURL = res.items[0].meta.href;
              } else if (res.items[0]) {
                policyCoverageData.endpointURL =
                  res.meta.href + '/' + res.items[0].resourceId;
              } else {
                policyCoverageData.endpointURL =
                  'api_does_not_returned_data_to_create_uri';
              }

              resolve(policyCoverageData);
            }
          });
      } else {
        reject('Error Load Policy Coverages, quoteId Not defined');
      }
    });
  }

  // Helper for method setDefaultValuesForOptionsIfThisIsNewQuote
  private helperSetDefaultValues(
    data: PolicyItemDefaultDataAPIResponse,
    policiesParsed: CoverageItemParsed[]
  ): PromiseDataAfterSettingDefaultValuesNew {
    const policiesParsedWithDefaultValues = this.optionsService.setDefaultValuesForOptions(
      policiesParsed,
      data.items
    );

    const policiesToUpdate = policiesParsedWithDefaultValues.filter(
      policyParsed =>
        data.items.some(
          item =>
            item.coverageCode === policyParsed.coverageCode &&
            policyParsed.isNewFromAPI
        )
    );

    console.log(policiesToUpdate);

    const promiseResponse: PromiseDataAfterSettingDefaultValuesNew = {
      policiesToUpdate: policiesToUpdate,
      policyItemsParsed: policiesParsedWithDefaultValues
    };

    return promiseResponse;
  }

  private setDefaultValuesForOptionsIfThisIsNewQuote(
    policiesParsed: CoverageItemParsed[],
    agencyId: string,
    isNewQuote: boolean
  ): Promise<PromiseDataAfterSettingDefaultValuesNew> {
    return new Promise((resolve, reject) => {
      // console.log('Quote is new', isNewQuote);
      if (!isNewQuote) {
        const promiseResponse: PromiseDataAfterSettingDefaultValuesNew = {
          policiesToUpdate: [],
          policyItemsParsed: policiesParsed
        };
        return resolve(promiseResponse);
      }

      this.storageService
        .getStorageData('homeDefaultOptions').pipe(
        take(1))
        .subscribe(
          (res: PolicyItemDefaultDataAPIResponse) => {
            if (res && res.items && res.items.length) {
              const promiseResponse = this.helperSetDefaultValues(
                res,
                policiesParsed
              );
              resolve(promiseResponse);
            } else {
              // API Fallback
              this.subsService
                .getDefaultHomeCarrierOptions(agencyId, this.formType).pipe(
                take(1))
                .subscribe(
                  (res: PolicyItemDefaultDataAPIResponse) => {

                    const promiseResponse = this.helperSetDefaultValues(
                      res,
                      policiesParsed
                    );
                    resolve(promiseResponse);
                  },
                  err => reject(err)
                );
            }
          },
          err => {
            reject(err);
          }
        );

      // this.subsService.getDefaultHomeCarrierOptions(agencyId)
      //   .take(1)
      //   .subscribe(
      //     (res:PolicyItemDefaultDataAPIResponse) => {
      //       let policiesParsedWithDefaultValues = this.optionsService.setDefaultValuesForOptions(policiesParsed, res.items);
      //       let policiesToUpdate = policiesParsedWithDefaultValues.filter(policyParsed => res.items.some(item => item.coverageCode === policyParsed.coverageCode && policyParsed.isNewFromAPI));

      //       let promiseResponse: PromiseDataAfterSettingDefaultValuesNew = {
      //         policiesToUpdate: policiesToUpdate,
      //         policyItemsParsed: policiesParsedWithDefaultValues
      //       }

      //       resolve(promiseResponse);
      //     },
      //     err => reject(err)
      //   );
    });
  }

  // private savePoliciesWithDefaultValuesToAPI(policiesParsed:PolicyItemParsed[])
  private savePoliciesToApiIfNewPoliciesToUpdate(
    policiesParsed: CoverageItemParsed[],
    policiesToUpdate: CoverageItemParsed[]
  ): Promise<PromiseDataAfterSavingToApi> {
    return new Promise((resolve, reject) => {
      const promiseResponse: PromiseDataAfterSavingToApi = {
        status: '',
        policyItemsParsed: policiesParsed
      };

      let agencyId;
      this.agencyUserService.userData$.pipe(
      take(1))
      .subscribe(agent => (agencyId = agent.agencyId));

      let policiesListToUpdate;
      this.storageService
        .getStorageData('homeDefaultOptions').pipe(
        take(1))
        .subscribe(
          (res: PolicyItemDefaultDataAPIResponse) => {
            if (res && res.items && res.items.length) {
                policiesListToUpdate = this.helperSetDefaultValues(
                res,
                policiesParsed
              );
              resolve(promiseResponse);
            } else {
              // API Fallback
              this.subsService
                .getDefaultHomeCarrierOptions(agencyId, this.formType).pipe(
                take(1))
                .subscribe(
                  (resp: PolicyItemDefaultDataAPIResponse) => {

                      policiesListToUpdate = this.helperSetDefaultValues(
                      resp,
                      policiesParsed
                    );
                    resolve(promiseResponse);
                  },
                  err => reject(err)
                );
            }
          },
          err => {
            reject(err);
          }
        );


      // AH: 09-18-2019 Need to update API on effective date change as that can change applicable carrier coverages.
      // Do not send request to update if no new options with default values
      if ( (!policiesToUpdate || !policiesToUpdate.length) && (!this.generalOptionsPolicies || !this.generalOptionsPolicies.length) ) {
        promiseResponse.status =
          'No new options to Update default values, Do not send API Request';
        return resolve(promiseResponse);
      }

      if (policiesListToUpdate && policiesListToUpdate.policiesToUpdate) {
        for (const updatedItem of policiesListToUpdate.policiesToUpdate) {
          const itemIndex = policiesParsed.findIndex(item => item.coverageCode === updatedItem.coverageCode);
          if (itemIndex !== -1) {
            policiesParsed[itemIndex] = updatedItem;
          }
        }
      }
      // We need to also update policies General Options from the Options General View
      // The mechanism of updating policies (coverages) is strange - only options sent to API will be set as selected
      // (if the option was selected before and not sent to API during update, next time data is load, this option will be unselected )
      let allPoliciesToSearchActiveOptions: CoverageItemParsed[] = [];
      allPoliciesToSearchActiveOptions = [
        ...policiesParsed,
        ...this.generalOptionsPolicies
      ];

      const endpointUrl = policiesParsed[0].endpointUrl;

      const activePoliciesToUpdate = allPoliciesToSearchActiveOptions.filter(
        item => item.isActive
      );
      const readyAllPoliciesToUpdate: Coverage[] = this.optionsService.convertPolicyItemParsedArrayToPolicyArray(
        activePoliciesToUpdate
      );

      // Update remote data
      const newCoverageData = {
        coverages: readyAllPoliciesToUpdate
      };

      this.optionsService
         .updatePoliciesByUri(endpointUrl, newCoverageData).pipe(
         take(1))
         .subscribe(
           res => {
             promiseResponse.status =
               'Set and saved Policies default values for new options';
             // console.log(promiseResponse.status);
             this.storageService.setStorageData('homeCarrierOptionsParsed', policiesParsed);
             resolve(promiseResponse);
           },
           err => {
             promiseResponse.status =
               'Error occurred During updating, not set default values';
             // console.log(promiseResponse.status);
             resolve(promiseResponse);
           }
         );
     });
  }

  private setAdditionalDataForCoverageItemParsed(
    item: CoverageItemParsed
  ): CoverageItemParsed {
    if (!item.additionalData) {
      item.additionalData = {};
    }

    switch (item.coverageCode) {
      case 'BSC-HOME-25096':
        item.additionalData.agentCodeGroupPlanAddEmptyOption = true;
        break;
    }

    return item;
  }

  // Check if any element of an array (anyElementArr) is in another array (arrayToCheckIfIsIn)
  private anyElementIsInArray(
    anyElementArr: string[],
    arrayToCheckIfIsIn: string[]
  ): boolean {
    return anyElementArr.some(el => arrayToCheckIfIsIn.indexOf(el) !== -1);
  }

  // Requirements
  // https://bostonsoftware.atlassian.net/browse/SPRC-277
  // https://app.smartsheet.com/b/home?lx=Behy_QSsUAmHoQK4jRSH8A
  // ----------------------------------------------------------------------------
  public setPolicyItemStatusBasedOnRequirements(
    policyItemHomeParsed: CoverageItemParsed,
    selectedQuoteFormTypes: string[]
  ): CoverageItemParsed {



    switch (policyItemHomeParsed.coverageCode) {


      // Barnstable BSC-HOME-25236 PRODCUT-2687
      case 'BSC-HOME-25236':
        if (
          this.anyElementIsInArray(['HO3', 'HO5'], selectedQuoteFormTypes)
        ) {
          policyItemHomeParsed.isRequired = true;
          policyItemHomeParsed.additionalData.requiredForCarriers = [
            '305'
          ];
        }
        break;

      case 'BSC-HOME-25390':
        if (
          this.anyElementIsInArray(['HO3', 'HO5'], selectedQuoteFormTypes)
        ) {
          policyItemHomeParsed.isRequired = true;
          policyItemHomeParsed.additionalData.requiredForCarriers = [
            '332'
          ];
        }
        break;

      case 'BSC-HOME-25391':
        if (
          this.anyElementIsInArray(['HO3', 'HO5'], selectedQuoteFormTypes)
        ) {
          policyItemHomeParsed.isRequired = true;
          policyItemHomeParsed.additionalData.requiredForCarriers = [
            '332'
          ];
        }
        break;

      case 'BSC-HOME-25392':
        if (
          this.anyElementIsInArray(['HO3', 'HO5'], selectedQuoteFormTypes)
        ) {
          policyItemHomeParsed.isRequired = true;
          policyItemHomeParsed.additionalData.requiredForCarriers = [
            '332'
          ];
        }
        break;

      // Arbella -  Company Selection
      case 'BSC-HOME-012101':
        if (
          this.anyElementIsInArray(
            ['HO3', 'HO5', 'HO6'],
            selectedQuoteFormTypes
          )
        ) {
          policyItemHomeParsed.isRequired = true;
          policyItemHomeParsed.additionalData.requiredForCarriers = [
            '31',
            '32',
            '75',
            '76'
          ];
        }
        break;

      // Arbella Additional surcharge for pool selection.
      case 'BSC-HOME-022808':
        policyItemHomeParsed.additionalData.requiredForCarriers = [
          '31',
          '32',
          '75',
          '76'
        ];

        if (
          this.anyElementIsInArray(
            ['HO3', 'HO5', 'HO6'],
            selectedQuoteFormTypes
          ) &&
          this.dwelling.poolOnPremisesInd != null
        ) {
          policyItemHomeParsed.isRequired = true;
        } else if (this.anyElementIsInArray(['HO4'], selectedQuoteFormTypes)) {
          policyItemHomeParsed.isRequired = true;
        } else {
          policyItemHomeParsed.isRequired = false;
        }
        break;

      // ASI - Household Rating Factor
      case 'BSC-HOME-023084':
        if (
          this.anyElementIsInArray(
            ['HO3', 'HO5', 'HO4', 'HO6'],
            selectedQuoteFormTypes
          )
        ) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
      // ASI - Prior Liability Limit
      case 'BSC-HOME-022909':
        if (this.anyElementIsInArray(['HO3', 'HO5'], selectedQuoteFormTypes)) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
      // ASI - Prior Liability Limit
      case 'BSC-HOME-022910':
        if (this.anyElementIsInArray(['HO6'], selectedQuoteFormTypes)) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
      // ASI - Renter Occupied
      case 'BSC-HOME-022927':
        if (this.anyElementIsInArray(['HO6'], selectedQuoteFormTypes)) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
      // ASI - Floors Above Unit
      case 'BSC-HOME-022932':
        if (this.anyElementIsInArray(['HO6'], selectedQuoteFormTypes)) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
      // Bunker Hill - * Agency/Branch
      case 'BSC-HOME-022988':
        if (
          this.anyElementIsInArray(
            ['HO3', 'HO5', 'HO4', 'HO6'],
            selectedQuoteFormTypes
          )
        ) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
      // Bunker Hill - ** OBI Limit
      case 'BSC-HOME-023116':
        if (
          this.anyElementIsInArray(
            ['HO3', 'HO5', 'HO4', 'HO6'],
            selectedQuoteFormTypes
          )
        ) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
      // Bunker Hill - *** Original Date with Agency
      case 'BSC-HOME-023009':
        if (
          this.anyElementIsInArray(
            ['HO3', 'HO5', 'HO4', 'HO6'],
            selectedQuoteFormTypes
          )
        ) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
      // Bunker Hill - **** Prior Home Insurance Carrier
      case 'BSC-HOME-023115':
        if (
          this.anyElementIsInArray(
            ['HO3', 'HO5', 'HO4', 'HO6'],
            selectedQuoteFormTypes
          )
        ) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
      // National General - Company Placement Addl. Information
      case 'BSC-HOME-023052':
        if (
          this.anyElementIsInArray(
            ['HO3', 'HO5', 'HO4', 'HO6'],
            selectedQuoteFormTypes
          )
        ) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
      // National General - Company Placement Required
      case 'BSC-HOME-023043':
        if (
          this.anyElementIsInArray(
            ['HO3', 'HO5', 'HO4', 'HO6'],
            selectedQuoteFormTypes
          )
        ) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
      // National General - Marital Status
      case 'BSC-HOME-023053':
        if (
          this.anyElementIsInArray(
            ['HO3', 'HO5', 'HO4', 'HO6'],
            selectedQuoteFormTypes
          )
        ) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
      // National General - Prior Insurer
      case 'BSC-HOME-023048':
        if (
          this.anyElementIsInArray(
            ['HO3', 'HO5', 'HO4', 'HO6'],
            selectedQuoteFormTypes
          )
        ) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
      // NLC - Experience Rating Modifier
      // 29-06-2018 :: https://bostonsoftware.atlassian.net/browse/SPRC-603
      // case 'BSC-HOME-022805':
      //   if (this.anyElementIsInArray(['HO3', 'HO5', 'HO4', 'HO6'], selectedQuoteFormTypes)) {
      //     policyItemHomeParsed.isRequired = true;
      //   }
      //   break;
      // Providence Mutual - ** Agent Code
      case 'BSC-HOME-022846':
        if (
          this.anyElementIsInArray(
            ['HO3', 'HO5', 'HO4', 'HO6'],
            selectedQuoteFormTypes
          )
        ) {
          policyItemHomeParsed.isRequired = true;
        }
        break;

      case 'BSC-HOME-022867':
        if (
          this.anyElementIsInArray(
            ['HO3', 'HO5', 'HO4', 'HO6'],
            selectedQuoteFormTypes
          )
        ) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
      // Quincy Mutual Group - Multivariant Rating
      case 'BSC-HOME-022804':
        if (
          this.anyElementIsInArray(
            ['HO3', 'HO5', 'HO4', 'HO6'],
            selectedQuoteFormTypes
          )
        ) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
      // Safeco  - ** Agent Code
      case 'BSC-HOME-022733':
        if (
          this.anyElementIsInArray(
            ['HO3', 'HO5', 'HO4', 'HO6'],
            selectedQuoteFormTypes
          )
        ) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
      // Safety  - * Pick a Plan
      case 'BSC-HOME-022729':
        if (
          this.anyElementIsInArray(
            ['HO3', 'HO5', 'HO4', 'HO6'],
            selectedQuoteFormTypes
          )
        ) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
      // Safety  - ** Agent Code
      case 'BSC-HOME-022554':
        if (
          this.anyElementIsInArray(
            ['HO3', 'HO5', 'HO4', 'HO6'],
            selectedQuoteFormTypes
          )
        ) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
        case 'BSC-HOME-25310':
        if (
          this.anyElementIsInArray(
            ['HO3', 'HO5', 'HO4', 'HO6'],
            selectedQuoteFormTypes
          )
        ) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
        case 'BSC-HOME-25316':
          if (
            this.anyElementIsInArray(
              ['HO3', 'HO5', 'HO4', 'HO6'],
              selectedQuoteFormTypes
            )
          ) {
            policyItemHomeParsed.isRequired = true;
          }
          break;
        case 'BSC-HOME-25319':
          if (
            this.anyElementIsInArray(
               ['HO3', 'HO5', 'HO4', 'HO6'],
               selectedQuoteFormTypes
             )
          ) {
             policyItemHomeParsed.isRequired = true;
          }
          break;
        case 'BSC-HOME-25314':
           if (
             this.anyElementIsInArray(
               ['HO3', 'HO5', 'HO4', 'HO6'],
                selectedQuoteFormTypes
             )
           ) {
              policyItemHomeParsed.isRequired = true;
           }
           break;
        case 'BSC-HOME-25320':
          if (
           this.anyElementIsInArray(
             ['HO3', 'HO5', 'HO4', 'HO6'],
             selectedQuoteFormTypes
           )
        ) {
           policyItemHomeParsed.isRequired = true;
         }
         break;


        case 'BSC-HOME-023221':
            policyItemHomeParsed.isRequired = true;
            break;

      // Preferred - Agent code
      case 'BSC-HOME-25213':
        if (
          this.anyElementIsInArray(
            ['HO3', 'HO5', 'HO4', 'HO6'],
            selectedQuoteFormTypes
          )
        ) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
      // Travelers - ** Agent Code
      case 'BSC-HOME-022756':
        if (
          this.anyElementIsInArray(
            ['HO3', 'HO5', 'HO4', 'HO6'],
            selectedQuoteFormTypes
          )
        ) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
      // Hanover New - Basement/Foundation Type (BSC-HOME-25033) (https://bostonsoftware.atlassian.net/browse/SPR-2540)
      case 'BSC-HOME-25033':
        if (this.anyElementIsInArray(['HO3', 'HO5'], selectedQuoteFormTypes)) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
      // Hanover New - Coverage Trim	(BSC-HOME-25005) (HO3;HO5 only)
      case 'BSC-HOME-25005':
        if (this.anyElementIsInArray(['HO3', 'HO5'], selectedQuoteFormTypes)) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
      // Hanover New - Coverage Trim	(BSC-HOME-25006) (HO4 only)
      case 'BSC-HOME-25006':
        if (this.anyElementIsInArray(['HO4'], selectedQuoteFormTypes)) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
      // Hanover New - Coverage Trim	(BSC-HOME-25010) (HO6 Only)
      case 'BSC-HOME-25010':
        if (this.anyElementIsInArray(['HO6'], selectedQuoteFormTypes)) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
      // United P&C - Hurricane Deductible (BSC-HOME‌-023169) needs to be required to rate HO3 for United P&C (Plan ID 206)
      // https://bostonsoftware.atlassian.net/browse/SPRC-614
      case 'BSC-HOME-023169':
        if (this.anyElementIsInArray(['HO3', 'HO5'], selectedQuoteFormTypes)) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
      // Cincinnati - Prior Carrier (BSC-HOME-022865)
      case 'BSC-HOME-022865':
        if (
          this.anyElementIsInArray(
            ['HO3', 'HO5', 'HO4', 'HO6'],
            selectedQuoteFormTypes
          )
        ) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
      // Travelers New - ** Agent Code
      case 'BSC-HOME-25040':
        if (
          this.anyElementIsInArray(
            ['HO3', 'HO5', 'HO4', 'HO6'],
            selectedQuoteFormTypes
          )
        ) {
          policyItemHomeParsed.isRequired = true;
        }
        break;
        // Mapfre New - ** Agent Code
        case 'BSC-HOME-25096':
          if (
            this.anyElementIsInArray(
              ['HO3', 'HO5', 'HO4', 'HO6'],
              selectedQuoteFormTypes
            )
          ) {
            policyItemHomeParsed.isRequired = true;
          }
          break;

        case 'BSC-HOME-023223':
          if (this.anyElementIsInArray(['HO3', 'HO5', 'HO4', 'HO6'], selectedQuoteFormTypes)) {
            policyItemHomeParsed.isRequired = true;
          }
        break;

        case 'BSC-HOME-25370':
          if (this.anyElementIsInArray(['HO3', 'HO5', 'HO4', 'HO6'], selectedQuoteFormTypes)) {
            policyItemHomeParsed.isRequired = true;
          }
        break;

        case 'BSC-HOME-25321':
          if (this.anyElementIsInArray(['HO3', 'HO5', 'HO4', 'HO6'], selectedQuoteFormTypes)) {
            policyItemHomeParsed.isRequired = true;
          }
        break;

        case 'BSC-HOME-25052':
          if (this.anyElementIsInArray(['HO3', 'HO5', 'HO4', 'HO6'], selectedQuoteFormTypes)) {
            const subsystem = this.getSubsystems();
            if (subsystem.heatingSecondaryTypeCode === 'CoalProfessionallyInstalled' ||
            subsystem.heatingSecondaryTypeCode === 'CoalNonProfessionallyInstalled' ||
            subsystem.heatingSecondaryTypeCode === 'PelletStove' ||
            subsystem.heatingSecondaryTypeCode === 'WoodProfessionallyInstalled') {
              if (policyItemHomeParsed.isActive === false) {
                policyItemHomeParsed.isRequired = true;
                policyItemHomeParsed.isDisabled = false;
                policyItemHomeParsed.isActive = false;
              }
            } else {
              policyItemHomeParsed.isDisabled = true;
              policyItemHomeParsed.isRequired = false;
              policyItemHomeParsed.isActive = false;
              policyItemHomeParsed.currentValue = '';
              policyItemHomeParsed.currentValueData.keyValue = [];
              policyItemHomeParsed.currentValueData.values = [];
            }
          }
        break;

        case 'BSC-HOME-023224':
          if (this.anyElementIsInArray(['HO3', 'HO5', 'HO4', 'HO6'], selectedQuoteFormTypes)) {
            const subsystem = this.getSubsystems();
            if (subsystem.roofLastUpdatedYear === null ||
            subsystem.roofLastUpdatedYear === 0) {
              policyItemHomeParsed.isRequired = false;
              policyItemHomeParsed.isDisabled = true;
              policyItemHomeParsed.currentValue = '';
              policyItemHomeParsed.currentValueData.keyValue = [];
              policyItemHomeParsed.currentValueData.values = [];
              policyItemHomeParsed.isActive = false;
            } else {
              if (policyItemHomeParsed.isActive === false) {
                policyItemHomeParsed.isRequired = true;
                policyItemHomeParsed.isDisabled = false;
                policyItemHomeParsed.isActive = false;
              }
            }
          }
        break;

        case 'BSC-HOME-023225':
          if (this.anyElementIsInArray(['HO3', 'HO5', 'HO4', 'HO6'], selectedQuoteFormTypes)) {
            policyItemHomeParsed.isRequired = true;
          }
        break;

        case 'BSC-HOME-023228':
        if (
          this.anyElementIsInArray(['HO6'], selectedQuoteFormTypes)) {
          policyItemHomeParsed.isRequired = true;
        }
        break;

        case 'BSC-HOME-023227':
          if (
            this.anyElementIsInArray(['HO3', 'HO5'], selectedQuoteFormTypes)) {
            policyItemHomeParsed.isRequired = true;
          }
          break;

        case 'BSC-HOME-023229':
        if (
          this.anyElementIsInArray(['HO6'], selectedQuoteFormTypes)) {
          policyItemHomeParsed.isRequired = true;
        }
        break;

        case 'BSC-HOME-023230':
        if (
          this.anyElementIsInArray(['HO6'], selectedQuoteFormTypes)) {
          policyItemHomeParsed.isRequired = true;
        }
        break;

        case 'BSC-HOME-023231':
        if (
          this.anyElementIsInArray(['HO6'], selectedQuoteFormTypes)) {
          policyItemHomeParsed.isRequired = true;
        }
        break;

        // case 'BSC-HOME-25047':
        // if (
        //   this.anyElementIsInArray(['HO3', 'HO6'], selectedQuoteFormTypes)
        // ) {
        //   policyItemHomeParsed.isRequired = true;
        // }
        // break;

    }

    return policyItemHomeParsed;
  }

  // Error Status and Availability Settings - set specific values for specific Options
  // ---------------------------------------------------------------------------
  private subscribeHomeCarrierOptionsParsedForErrorAndAvailabilityStatus(): Promise<
    void
  > {
    let allowUpdating = true; // To prevent infinity loop validation, when storage is updated by this method

    return new Promise(resolve => {
      this.subscribtionHomeCarrierOptionsParsedForErrorAndAvailabilityStatus = this.storageService
        .getStorageData('homeCarrierOptionsParsed')
        .subscribe((items: CoverageItemParsed[]) => {
          if (allowUpdating) {
            // console.log('Update');
            const updatedItems: CoverageItemParsed[] = this.performErrorsStatusAndAvailabilitySettings(
              items
            );

            allowUpdating = false;
            this.storageService.setStorageData(
              'homeCarrierOptionsParsed',
              updatedItems
            );
            allowUpdating = true;
          }

          resolve();
        });
    });
  }

  private performErrorsStatusAndAvailabilitySettings(
    items: CoverageItemParsed[]
  ): CoverageItemParsed[] {
    items = items.map(item => {
      item = this.setPolicyItemErrorStatusAndAvailability(
        items,
        item,
        this.quoteFormTypes
      );
      return item;
    });

    return items;
  }

  private setPolicyItemErrorStatusAndAvailability(
    allItemHomeParsed: CoverageItemParsed[],
    policyItemHomeParsed: CoverageItemParsed,
    selectedQuoteFormTypes: string[]
  ): CoverageItemParsed {
    switch (policyItemHomeParsed.coverageCode) {

      case 'BSC-HOME-25239':
        if ((this.anyElementIsInArray(['HO3', 'HO5'], selectedQuoteFormTypes))) {
          const pol = allItemHomeParsed.find(c => c.coverageCode === 'BSC-HOME-25238');
          if (pol && pol.isActive) {
            policyItemHomeParsed.isDisabled = false;
          } else {
            policyItemHomeParsed.isDisabled = true;
            policyItemHomeParsed.currentValue = null;
            policyItemHomeParsed.isActive = false;
          }
        }
      break;
      // Safety (PC) - Condo Owners Edge Enhancement
      // https://bostonsoftware.atlassian.net/browse/SPR-2139
      // 1. Safety (PC) - Condo Owners Edge Enhancement _ is incompatible with "Safety Standard" value for _Safety - * Pick a Plan If these are selected at the same time, a Safety H&W message should be displayed: Condo Owners Edge Enhancement not Compatible with Selected Plan"
      case 'BSC-HOME-022663':
        if (this.anyElementIsInArray(['HO4', 'HO6'], selectedQuoteFormTypes)) {
          const safetyPickAPlanItem = allItemHomeParsed.find(
            (c: CoverageItemParsed) => c.coverageCode === 'BSC-HOME-022729'
          );

          if (
            safetyPickAPlanItem &&
            safetyPickAPlanItem.currentValue === 'Safety Standard' &&
            policyItemHomeParsed.isActive
          ) {

            policyItemHomeParsed.additionalData.hasError =
              policyItemHomeParsed.isActive;
            policyItemHomeParsed.additionalData.errorMessage =
              'Condo Owners Edge Enhancement not Compatible with Selected Plan';
          } else {
            policyItemHomeParsed.additionalData.hasError = false;
            policyItemHomeParsed.additionalData.errorMessage = '';
          }
        }
        break;
      // Safety - * Pick a Plan
      // https://bostonsoftware.atlassian.net/browse/SPR-2139
      // "Safety Indemnity" value for Safety - * Pick a Plan is not supported for HO4 or HO6. If selected on HO4 or HO6 quote, Safety H&W message to display: "Form not supported by selected plan"
      case 'BSC-HOME-022729':
        if (this.anyElementIsInArray(['HO4', 'HO6'], selectedQuoteFormTypes)) {
          if (
            policyItemHomeParsed.isActive &&
            policyItemHomeParsed.currentValue === 'Safety Indemnity'
          ) {
            policyItemHomeParsed.additionalData.hasError = true;
            policyItemHomeParsed.additionalData.errorMessage =
              'Form not supported by selected plan';
          } else {
            policyItemHomeParsed.additionalData.hasError = false;
            policyItemHomeParsed.additionalData.errorMessage = '';
          }
        }
        break;

      // Safety (STD) - Condo Unit Owners Enhancement
      // https://bostonsoftware.atlassian.net/browse/SPR-2139
      // If "Safety P&C" value for Safety - * Pick a Plan we need to disable the following options from being selected: Safety (STD) - Condo Unit Owners Enhancement
      case 'BSC-HOME-022547':
        if (this.anyElementIsInArray(['HO4', 'HO6'], selectedQuoteFormTypes)) {
          const safetyPickAPlanItem = allItemHomeParsed.find(
            (c: CoverageItemParsed) => c.coverageCode === 'BSC-HOME-022729'
          );

          if (
            safetyPickAPlanItem &&
            safetyPickAPlanItem.currentValue === 'Safety P&C'
          ) {
            policyItemHomeParsed.isDisabled = true;
            policyItemHomeParsed.isActive = false;
          } else {
            policyItemHomeParsed.isDisabled = false;
          }
        }
        break;

        case 'BSC-HOME-022828':
          if (this.anyElementIsInArray(['HO3', 'HO5'], selectedQuoteFormTypes)) {
            policyItemHomeParsed.isRequired = true;
          }
          break;

      // Safety (STD) - Identity Fraud
      // https://bostonsoftware.atlassian.net/browse/SPR-2139
      // If "Safety P&C" value for Safety - * Pick a Plan we need to disable the following options from being selected: Safety (STD) - Identity Fraud
      case 'BSC-HOME-022986':
        if (this.anyElementIsInArray(['HO4', 'HO6'], selectedQuoteFormTypes)) {
          const safetyPickAPlanItem = allItemHomeParsed.find(
            (c: CoverageItemParsed) => c.coverageCode === 'BSC-HOME-022729'
          );

          if (
            safetyPickAPlanItem &&
            safetyPickAPlanItem.currentValue === 'Safety P&C'
          ) {
            policyItemHomeParsed.isDisabled = true;
            policyItemHomeParsed.isActive = false;
          } else {
            policyItemHomeParsed.isDisabled = false;
          }
        }
        break;

      // Safety (STD) - Refrigerated Property Coverage
      // https://bostonsoftware.atlassian.net/browse/SPR-2139
      // If "Safety P&C" value for Safety - * Pick a Plan we need to disable the following options from being selected: Safety (STD) - Refrigerated Property Coverage
      case 'BSC-HOME-022987':
        if (this.anyElementIsInArray(['HO4', 'HO6'], selectedQuoteFormTypes)) {
          const safetyPickAPlanItem = allItemHomeParsed.find(
            (c: CoverageItemParsed) => c.coverageCode === 'BSC-HOME-022729'
          );

          if (
            safetyPickAPlanItem &&
            safetyPickAPlanItem.currentValue === 'Safety P&C'
          ) {
            policyItemHomeParsed.isDisabled = true;
            policyItemHomeParsed.isActive = false;
          } else {
            policyItemHomeParsed.isDisabled = false;
          }
        }
        break;

      // Safety (STD) - Select
      // https://bostonsoftware.atlassian.net/browse/SPR-2139
      // If "Safety P&C" value for Safety - * Pick a Plan we need to disable the following options from being selected: Safety (STD) - Select
      case 'BSC-HOME-022985':
        if (this.anyElementIsInArray(['HO4', 'HO6'], selectedQuoteFormTypes)) {
          const safetyPickAPlanItem = allItemHomeParsed.find(
            (c: CoverageItemParsed) => c.coverageCode === 'BSC-HOME-022729'
          );

          if (
            safetyPickAPlanItem &&
            safetyPickAPlanItem.currentValue === 'Safety P&C'
          ) {
            policyItemHomeParsed.isDisabled = true;
            policyItemHomeParsed.isActive = false;
          } else {
            policyItemHomeParsed.isDisabled = false;
          }
        }
        break;

      case 'BSC-HOME-023221':
        if (this.anyElementIsInArray(['HO3', 'HO5'], selectedQuoteFormTypes)) {
          const safetyStandard = ['BSC-HOME-022652', 'BSC-HOME-022665', 'BSC-HOME-023235'];
          const safetyPandC = ['BSC-HOME-022546', 'BSC-HOME-022541', 'BSC-HOME-022542'];
          const safetyPandCDated = ['BSC-HOME-022652', 'BSC-HOME-022541', 'BSC-HOME-022542', 'BSC-HOME-022546', 'BSC-HOME-022665'];
          const safetyStandardAndPC = ['BSC-HOME-023235', ];
          const safetyNortheast = ['BSC-HOME-022652', 'BSC-HOME-022665', 'BSC-HOME-022542', 'BSC-HOME-022541'];
          const comparisonDate = new Date('2025-04-30');
          const dateOnQuote = new Date(this.quote.effectiveDate);

          if (policyItemHomeParsed.isActive) {
            if (policyItemHomeParsed.currentValue === 'Safety Standard') {
              this.modifyPolicyItemPropertiesBySafetyCodes(allItemHomeParsed, safetyStandard, {
                isDisabled: true,
                isActive: false
              });
            } else if (policyItemHomeParsed.currentValue === 'Safety P&C') {
              if (dateOnQuote > comparisonDate) {
                this.modifyPolicyItemPropertiesBySafetyCodes(allItemHomeParsed, safetyPandCDated, {
                  isDisabled: true,
                  isActive: false
                });
             //   this.updateApiCarrierOptionsForSafety();
              } else {
                this.modifyPolicyItemPropertiesBySafetyCodes(allItemHomeParsed, safetyPandC, {
                  isDisabled: true,
                  isActive: false
                });
              }
            } else if (policyItemHomeParsed.currentValue === 'Safety Northeast') {
              if (dateOnQuote > comparisonDate) {
                this.modifyPolicyItemPropertiesBySafetyCodes(allItemHomeParsed, safetyNortheast, {
                  isDisabled: true,
                  isActive: false
                });
           //     this.updateApiCarrierOptionsForSafety();
              }
            } else {
              this.modifyPolicyItemPropertiesBySafetyCodes(allItemHomeParsed, safetyStandardAndPC, {
                isDisabled: true,
                isActive: false
              });
            }
        }
      }
      break;

      case 'BSC-HOME-022868':
        const capstone = allItemHomeParsed.find(c => c.coverageCode === 'BSC-HOME-022870');

        if (capstone && capstone.isActive) {
          policyItemHomeParsed.isDisabled = true;
          policyItemHomeParsed.isActive = false;
        } else {
          policyItemHomeParsed.isDisabled = false;
        }
      break;

      case 'BSC-HOME-022870':
        const homeowner = allItemHomeParsed.find(c => c.coverageCode === 'BSC-HOME-022868');
        if (homeowner && homeowner.isActive) {
          policyItemHomeParsed.isDisabled = true;
          policyItemHomeParsed.isActive = false;
        } else {
          policyItemHomeParsed.isDisabled = false;
        }
        break;

        case 'BSC-HOME-25374':
          const protectionPlus = allItemHomeParsed.find(c => c.coverageCode === 'BSC-HOME-25378');
          if (protectionPlus && protectionPlus.isActive) {
            policyItemHomeParsed.isDisabled = true;
            policyItemHomeParsed.isActive = false;
          } else {
            policyItemHomeParsed.isDisabled = false;
          }
          break;

          case 'BSC-HOME-25378':
            const enhancedProtectionPlus = allItemHomeParsed.find(c => c.coverageCode === 'BSC-HOME-25374');
            if (enhancedProtectionPlus && enhancedProtectionPlus.isActive) {
              policyItemHomeParsed.isDisabled = true;
              policyItemHomeParsed.isActive = false;
            } else {
              policyItemHomeParsed.isDisabled = false;
            }
            break;



    }

    return policyItemHomeParsed;
  }

  public modifyPolicyItemPropertiesBySafetyCodes(policies: CoverageItemParsed[], policyCodes: string[],
    propValues: { [key: string]: any }): CoverageItemParsed[] {
    for (const policyCode of policyCodes) {
      const policyItem: CoverageItem = policies.find(item => item.coverageCode === policyCode);

      if (policyItem) {
        for (const property in propValues) {
          if (property in policyItem) {
            policyItem[property] = propValues[property];
          }
        }
      }
    }
    return policies;
  }

  public updateApiCarrierOptionsForSafety() {
    const selectedPlansIdsString = this.selectedPlansIds.join(',');
    const quoteEffectiveDate = this.quote && this.quote.effectiveDate ? this.quote.effectiveDate : '';

    this.specsService.getRatingCoverages('MA', this.quote.lob, 'PolicyOptionsCarrier', selectedPlansIdsString, '', quoteEffectiveDate)
    .subscribe((carrierOptions) => {

      this.storageService.getStorageData('homeCarrierOptionsParsed').subscribe((homeCarrierOptionsParsed: any[] = []) => {

        const allPoliciesToUpdateCarrier = homeCarrierOptionsParsed.filter((x: any) =>
          carrierOptions.items.some((y: any) => y.coverageCode === x.coverageCode && x.isActive === true)
        );

        const readyCarrierOptions = this.optionsService.convertPolicyItemParsedArrayToPolicyArray(allPoliciesToUpdateCarrier);

        if (readyCarrierOptions.length === 0) {
          console.log('No carrier options to update.');
          return;
        }

        this.optionsService.updatePoliciesByUri(
          homeCarrierOptionsParsed[0]?.endpointUrl,
          { coverages: readyCarrierOptions }
        ).subscribe({
          next: () => console.log('Update Policies Call Success'),
          error: (err) => console.log('Update Policies Error:', err),
        });
      });
    });
  }
}
