
import {take} from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { BehaviorSubject ,  SubscriptionLike as ISubscription } from 'rxjs';

// Services
import { OptionsService } from 'app/dashboard/app-services/options.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { SubsService } from 'app/dashboard/app-services/subs.service';
import { AgencyUserService } from 'app/shared/services/agency-user.service';

// Models
import {
  QuoteDwelling,
  QuotePlan,
  QuotePlanListAPIResponse
} from 'app/app-model/quote';
import {
  Coverage,
  PolicyCoveragesData,
  CoverageItem,
  CoverageItemParsed,
  PolicyItemDefaultDataAPIResponse
} from 'app/app-model/coverage';

interface TriggerSourceI {
  triggerBy?: 'SelectedPlans' | 'QuoteFormType' | 'QuoteEffectiveDate' | 'Options';
}

interface PromiseDataAfterSettingDefaultValuesNew {
  policiesToUpdate: CoverageItemParsed[];
  policyItemsParsed: CoverageItemParsed[];
}

interface PromiseDataAfterSavingToApi {
  status: string;
  policyItemsParsed: CoverageItemParsed[];
}

@Injectable()
export class DwellingGeneralOptionsAutomanagerService {
  private serviceIsInitialized = false;

  private subscriptionQuote: ISubscription;
  private subscriptionQuoteIsNew: ISubscription;
  private subscriptionSelectedPlans: ISubscription;
  private subscriptionSelectedQuoteTypes: ISubscription;
  private subscriptionParserTrigger: ISubscription;
  private subscriptionCarrierOptionsPolicyItemParsed: ISubscription;

  private _parserTrigger: BehaviorSubject<TriggerSourceI> = new BehaviorSubject(
    {}
  );

  private quote: QuoteDwelling;
  private quoteId = '';
  private quoteLob = '';
  private quoteFormTypes: string[] = [];
  private quoteIsNew = false;
  private selectedPlans: QuotePlan[] = [];
  private selectedPlansIds: string[] = [];
  private quoteBeforeChange: QuoteDwelling = null;
formType;
  // Required for updating coverages - we need all options from every view
  private carrierOptionsPolicies: CoverageItemParsed[] = [];
  subscriptionGeneralOptionsPolicyItemHomeParsed: any;
  generalOptionsPolicies: CoverageItemParsed[];
  preventParsingPoliciesUntilDataSavedToAPIServer: any;

  constructor(
    private storageService: StorageService,
    private agencyUserService: AgencyUserService,
    private specsService: SpecsService,
    private optionsService: OptionsService,
    private subsService: SubsService
  ) {}

  public initialize(): Promise<void> {
    if (this.serviceIsInitialized) {
      return Promise.resolve();
    }

    this.serviceIsInitialized = true;
    console.log('Initialize DwellingGeneralOptionsAutomanagerService');

    return Promise.all([
      this.subscribeQuote(),
      this.subscribeQuoteIsNew(),
      this.subscribeSelectedPlans(),
      this.subscribeSelectedQuoteFormTypes(),
      this.subscribeCarrierOptionsPolicyItemParsed(),
    ])
      .then(() => {
      this.subscribeGeneralOptionsPolicyItemDwellingParsed();

        return this.initPolicyItemsParser();
      })
      .catch(err => {
        console.log(err);
      });
  }

  public destroy(): void {
    this.serviceIsInitialized = false;

    this.subscriptionQuote && this.subscriptionQuote.unsubscribe();
    this.subscriptionQuoteIsNew && this.subscriptionQuoteIsNew.unsubscribe();
    this.subscriptionSelectedPlans &&
      this.subscriptionSelectedPlans.unsubscribe();
    this.subscriptionSelectedQuoteTypes &&
      this.subscriptionSelectedQuoteTypes.unsubscribe();
    this.subscriptionParserTrigger &&
      this.subscriptionParserTrigger.unsubscribe();
    this.subscriptionCarrierOptionsPolicyItemParsed &&
      this.subscriptionCarrierOptionsPolicyItemParsed.unsubscribe();
  }

  private subscribeQuote(): Promise<QuoteDwelling> {
    return new Promise((resolve, reject) => {
      this.subscriptionQuote = this.storageService
        .getStorageData('selectedQuote')
        .subscribe((quote: QuoteDwelling) => {
          this.quote = JSON.parse(JSON.stringify(quote));
          this.quoteId = quote.resourceId;
          this.quoteLob = quote.lob;
          this.formType = quote.formType;
          resolve(this.quote);

          if (!this.quoteBeforeChange) {
            this.quoteBeforeChange = JSON.parse(JSON.stringify(quote));
          } else if (
            this.quoteBeforeChange.effectiveDate !== this.quote.effectiveDate
          ) {
            this.quoteBeforeChange = JSON.parse(JSON.stringify(quote));

            // INIT POLICY ITEMS PARSING After Quote Effective date change
            // Emit Observable to Parse Items
            this._parserTrigger.next({ triggerBy: 'QuoteEffectiveDate' });
          }
        });
    });
  }

  private subscribeGeneralOptionsPolicyItemDwellingParsed(): void {
    this.subscriptionGeneralOptionsPolicyItemHomeParsed = this.storageService.getStorageData('dwellingGeneralOptionsParsed')
      .subscribe((items: CoverageItemParsed[]) => {
        this.generalOptionsPolicies = items;
        this._parserTrigger.next({ triggerBy: 'Options' });
      });
  }

  private subscribeSelectedQuoteFormTypes(): Promise<string[]> {
    return new Promise((resolve, reject) => {
      this.subscriptionSelectedQuoteTypes = this.storageService
        .getStorageData('selectedQuoteFormTypes')
        .subscribe((res: string[]) => {
          this.quoteFormTypes = res;
          resolve(this.quoteFormTypes);

          // Emmit Observable to Parse Items
          this._parserTrigger.next({ triggerBy: 'QuoteFormType' });
        });
    });
  }

  private subscribeQuoteIsNew(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.subscriptionQuoteIsNew = this.storageService
        .getStorageData('isNewQuote')
        .subscribe((res: boolean) => {
          this.quoteIsNew = res;
          resolve(this.quoteIsNew);
        });
    });
  }

  private subscribeSelectedPlans(): Promise<QuotePlan[]> {
    return new Promise((resolve, reject) => {
      this.subscriptionSelectedPlans = this.storageService
        .getStorageData('selectedPlan')
        .subscribe((res: QuotePlanListAPIResponse) => {
          if (res && res.items && res.items.length) {
            this.selectedPlans = JSON.parse(JSON.stringify(res.items[0].items));
            this.selectedPlansIds = this.selectedPlans.map(
              plan => plan.ratingPlanId
            );
            resolve(this.selectedPlans);

            // INIT POLICY ITEMS PARSING
            // Emmit Observable to Parse Items
            this._parserTrigger.next({ triggerBy: 'SelectedPlans' });
          }
        });
    });
  }

  private subscribeCarrierOptionsPolicyItemParsed(): void {
    this.subscriptionCarrierOptionsPolicyItemParsed = this.storageService
      .getStorageData('dwellingCarrierOptionsParsed')
      .subscribe((items: CoverageItemParsed[]) => {
        this.carrierOptionsPolicies = items;
      });
  }

  private initPolicyItemsParser(): Promise<void> {
    const delay = 500;
    let timer;

    return new Promise((resolve, reject) => {
      this.subscriptionParserTrigger = this._parserTrigger
        .asObservable()
        .subscribe(res => {
          timer && clearTimeout(timer);

          // Prevent geting Policies and parse data until data is saved to API with default values - if necessary
          if (this.preventParsingPoliciesUntilDataSavedToAPIServer) { return; }

          timer = setTimeout(() => {
            this.getOptionsPoliciesListAndParseData()
              .then(() => resolve())
              .catch(err => reject(err));
          }, delay);
        });
    });
  }

  private getOptionsPoliciesListAndParseData(): Promise<void> {
    const selectedPlansIdsString = this.selectedPlansIds.join(',');
    const states = this.quote.state;
    const quoteFormTypesString = this.quoteFormTypes.join(',');
    const quoteEffectiveDate =
      this.quote && this.quote.effectiveDate ? this.quote.effectiveDate : '';

    let agencyId;
    this.agencyUserService.userData$.pipe(
      take(1))
      .subscribe(agent => (agencyId = agent.agencyId));

    return new Promise((resolve, reject) => {
      this.specsService
        .getRatingCoverages(
          states,
          this.quoteLob,
          'policyOptionsGeneral',
          selectedPlansIdsString,
          quoteFormTypesString,
          quoteEffectiveDate
        ).pipe(
        take(1))
        .subscribe(res => {
          let policiesItemsHome: CoverageItemParsed[] = [];
          if (res && res.items && res.items.length) {
            policiesItemsHome = [...res.items];
          }

          if (policiesItemsHome && this.quoteId) {
            let policyItemsHomeParsed: CoverageItemParsed[] = [];
            console.log('repeat');
            this.processPoliciesItemsParsing(
              policiesItemsHome,
              this.selectedPlansIds,
              this.quoteId,
              this.quoteFormTypes
            )
              .then((res: CoverageItemParsed[]) => {
                policyItemsHomeParsed = JSON.parse(JSON.stringify(res));
                // ---------------
                // if there are no default values setting opertions, update storage service
                this.storageService.setStorageData(
                  'dwellingGeneralOptionsParsed',
                  policyItemsHomeParsed
                );
                // ----------------
                return policyItemsHomeParsed;
              })
              // // For Now we are not setting default values - waiting for API
              // .then((res:PolicyItemParsed[]) => {
              //   policyItemsHomeParsed = res;
              //   return this.setDefaultValuesForOptionsIfThisIsNewQuote(policyItemsHomeParsed, agencyId, this.quoteIsNew)
              // })
              // .then((res: PromiseDataAfterSettingDefaultValuesNew) => {
              //   // Save To Storage
              //   policyItemsHomeParsed = res.policyItemsParsed;
              //   this.storageService.setStorageData('dwellingGeneralOptionsParsed', policyItemsHomeParsed);

              //   console.log('%c policyItemsHomeParsed', 'color:green', policyItemsHomeParsed);

              //   // Update API with default Values
              //   return this.savePoliciesToApiIfNewPoliciesToUpdate(policyItemsHomeParsed, res.policiesToUpdate);
              // })
              .then(() => resolve())
              .catch(err => {
                console.log(err);
                reject(err);
              });
          }
        },
        err => reject(err)
      );
    });
  }

  public processPoliciesItemsParsing(
    policies: CoverageItem[],
    selectedPlansIds: string[],
    quoteId: string,
    quoteSelectedFormTypes: string[]
  ): Promise<CoverageItemParsed[]> {
    let arrPoliciesItemsParsed = this.optionsService.parsePoliciesItemsToPoliciesItemsParsed(
      policies
    );
    arrPoliciesItemsParsed = this.optionsService.orderObjectsArrayByProperty(
      arrPoliciesItemsParsed,
      'description'
    );

    // Required for setting default values if new Quote - to check if options
    // has been alredy saved in storage or not
    let policiesItemsFromStorage: CoverageItemParsed[] = [];

    this.storageService
      .getStorageData('dwellingGeneralOptionsParsed').pipe(
      take(1))
      .subscribe(res => (policiesItemsFromStorage = res));
    // --------------------------------------------------------------------------

    return this.loadPolicyCoverages(quoteId).then(
      (data: PolicyCoveragesData) => {
        // console.log("%c Loaded Coverages", 'color:red', data);
        // ATTENTION: In 'aside-dwelling-quote.component.ts', Coverages for the
        // quote are overwrites. Since there is the same resource in API as for
        // 'General Options' as 'Coverage Limits' we need there to get all current
        // values for coverages and send them to API
        // The reason is the way how API works.

        arrPoliciesItemsParsed = arrPoliciesItemsParsed.map(item => {
          item = this.setAdditionalDataForCoverageItemParsed(item);
          item = this.optionsService.sortPolicyItemParsedChildItems(item);
          item = this.optionsService.setPolicyItemStatusBasedOnPolicyCoverages(
            item,
            data.coverages
          );
          item = this.setPolicyItemStatusBasedOnRequirements(
            item,
            quoteSelectedFormTypes,
            selectedPlansIds
          );

          item.endpointUrl = data.endpointURL;
          item = this.optionsService.setPolicyItemParsedIsNewFromAPIValue(
            item,
            policiesItemsFromStorage
          );
          item.quoteResourceId = quoteId;

          if (item.inputType !== 'Dropdown') {
            item.values = this.optionsService.orderObjectsArrayByProperty(
              item.values,
              'value'
            );
          }

          return item;
        });
        return arrPoliciesItemsParsed;
      }
    );
  }

  private loadPolicyCoverages(quoteId: string): Promise<PolicyCoveragesData> {
    const policyCoverageData: PolicyCoveragesData = new PolicyCoveragesData();

    return new Promise((resolve, reject) => {
      if (quoteId) {
        this.optionsService
          .getPolicies(quoteId).pipe(
          take(1))
          .subscribe(res => {
            if (res.items.length === 0) {
              this.optionsService
                .createCoverageByUri(res.meta.href).pipe(
                take(1))
                .subscribe(res => {
                  policyCoverageData.coverages = res.coverages;
                  policyCoverageData.endpointURL = res.meta.href;
                  resolve(policyCoverageData);
                });
            } else {
              policyCoverageData.coverages = res.items[0].coverages;

              if (res.items[0].meta && res.items[0].meta.href) {
                policyCoverageData.endpointURL = res.items[0].meta.href;
              } else if (res.items[0]) {
                policyCoverageData.endpointURL =
                  res.meta.href + '/' + res.items[0].resourceId;
              } else {
                policyCoverageData.endpointURL =
                  'api_does_not_returned_data_to_create_uri';
              }

              resolve(policyCoverageData);
            }
          });
      } else {
        reject('Error Load Policy Coverages, quoteId Not defined');
      }
    });
  }

  // Methods for setting default values
  // ----------------------------------------------------------------------------

  // Helper for method setDefaultValuesForOptionsIfThisIsNewQuote
  private helperSetDefaultValues(
    data: PolicyItemDefaultDataAPIResponse,
    policiesParsed: CoverageItemParsed[]
  ): PromiseDataAfterSettingDefaultValuesNew {
    const policiesParsedWithDefaultValues = this.optionsService.setDefaultValuesForOptions(
      policiesParsed,
      data.items
    );
    const policiesToUpdate = policiesParsedWithDefaultValues.filter(
      policyParsed =>
        data.items.some(
          item =>
            item.coverageCode === policyParsed.coverageCode &&
            policyParsed.isNewFromAPI
        )
    );

    const promiseResponse: PromiseDataAfterSettingDefaultValuesNew = {
      policiesToUpdate: policiesToUpdate,
      policyItemsParsed: policiesParsedWithDefaultValues
    };

    return promiseResponse;
  }

  private setDefaultValuesForOptionsIfThisIsNewQuote(
    policiesParsed: CoverageItemParsed[],
    agencyId: string,
    isNewQuote: boolean
  ): Promise<PromiseDataAfterSettingDefaultValuesNew> {
    return new Promise((resolve, reject) => {
      // console.log('Quote is new', isNewQuote);
      if (!isNewQuote) {
        const promiseResponse: PromiseDataAfterSettingDefaultValuesNew = {
          policiesToUpdate: [],
          policyItemsParsed: policiesParsed
        };
        return resolve(promiseResponse);
      }

      // >> TODO:: Change to dwelling default option
      this.storageService
        .getStorageData('homeDefaultOptions').pipe(
        take(1))
        .subscribe(
          (res: PolicyItemDefaultDataAPIResponse) => {
            if (res && res.items && res.items.length) {
              const promiseResponse = this.helperSetDefaultValues(
                res,
                policiesParsed
              );
              resolve(promiseResponse);
            } else {
              // API Fallback
              // >> TODO:: Change to dwelling default option
              this.subsService
                .getDefaultHomeCarrierOptions(agencyId, this.formType).pipe(
                take(1))
                .subscribe(
                  (res: PolicyItemDefaultDataAPIResponse) => {
                    const promiseResponse = this.helperSetDefaultValues(
                      res,
                      policiesParsed
                    );
                    resolve(promiseResponse);
                  },
                  err => reject(err)
                );
            }
          },
          err => {
            reject(err);
          }
        );
    });
  }

  private savePoliciesToApiIfNewPoliciesToUpdate(
    policiesParsed: CoverageItemParsed[],
    policiesToUpdate: CoverageItemParsed[]
  ): Promise<PromiseDataAfterSavingToApi> {
    return new Promise((resolve, reject) => {
      const promiseResponse: PromiseDataAfterSavingToApi = {
        status: '',
        policyItemsParsed: policiesParsed
      };

      // Do not send request to update if no new options with default values
      if (!policiesToUpdate || !policiesToUpdate.length) {
        promiseResponse.status =
          'No new options to Update default values, Do not send API Request';
        return resolve(promiseResponse);
      }

      // We need to also update policies Carrier Options from the Carrier Options View
      // The mechanism of updating policeis (coverages) is strange - only options sent to API will be set as selected
      // (if the option was selected before and not sent to API during update, next time data is load, this option will be unselected )
      let allPoliciesToSearchActiveOptions: CoverageItemParsed[] = [];
      allPoliciesToSearchActiveOptions = [
        ...policiesParsed,
        ...this.carrierOptionsPolicies
      ];

      const endpointUrl = policiesParsed[0].endpointUrl;
      const activePoliciesToUpdate = allPoliciesToSearchActiveOptions.filter(
        item => item.isActive
      );
      const readyAllPoliciesToUpdate: Coverage[] = this.optionsService.convertPolicyItemParsedArrayToPolicyArray(
        activePoliciesToUpdate
      );

      // Update remote data
      const newCoverageData = {
        coverages: readyAllPoliciesToUpdate
      };

      this.optionsService
        .updatePoliciesByUri(endpointUrl, newCoverageData).pipe(
        take(1))
        .subscribe(
          res => {
            promiseResponse.status =
              'Set and saved Policies default values for new options';
            // console.log(promiseResponse.status);
            resolve(promiseResponse);
          },
          err => {
            promiseResponse.status =
              'Error occurred During updating, not set default values';
            // console.log(promiseResponse.status);
            resolve(promiseResponse);
          }
        );
    });
  }

  private setAdditionalDataForCoverageItemParsed(item: CoverageItemParsed): CoverageItemParsed {
    if (!item.additionalData) {
      item.additionalData = {};
    }

    return item;
  }

  // Check if any element of an array (anyElementArr) is in another array (arrayToCheckIfIsIn)
  private anyElementIsInArray(
    anyElementArr: string[],
    arrayToCheckIfIsIn: string[]
  ): boolean {
    return anyElementArr.some(el => arrayToCheckIfIsIn.indexOf(el) !== -1);
  }

  public setPolicyItemStatusBasedOnRequirements(
    policyItemHomeParsed: CoverageItemParsed,
    selectedQuoteFormTypes: string[],
    selectedPlansIds: string[]
  ): CoverageItemParsed {
           const isFeatureEnabled = JSON.parse(localStorage.getItem('features')).find(x => x.name === 'SprApp_FairPlanPIC');
     switch (policyItemHomeParsed.coverageCode) {
       case 'BSC-DFIRE-000481':
        if (this.anyElementIsInArray(['201'], selectedPlansIds) && isFeatureEnabled) {
        const storageService = new StorageService();
        storageService.getStorageData('homeQuoteCoverages').subscribe((res: any) => {
          if (res && res.items && res.items.length) {
            const dwell = res.items[0].coverages.find(c => c.coverageCode === 'DWELL');
      const dwellValue = dwell && dwell.values && dwell.values[0] ? +dwell.values[0].value : 0;
     const opt =  this.generalOptionsPolicies.find(c => c.coverageCode === 'BSC-DFIRE-010067');

              if (dwellValue > 1000000 && !policyItemHomeParsed.isDisabled && !opt.isActive) {
                policyItemHomeParsed.additionalData.requiredForCarriers = ['201'];
                policyItemHomeParsed.isRequired = true;
              } else {
                policyItemHomeParsed.isRequired = false;
              }
          }
        });
      }
         break;
               case 'BSC-DFIRE-010067':
                if (this.anyElementIsInArray(['201'], selectedPlansIds) && isFeatureEnabled) {
        const storageService1 = new StorageService();
        storageService1.getStorageData('homeQuoteCoverages').subscribe((res: any) => {
          if (res && res.items && res.items.length) {
            const dwell = res.items[0].coverages.find(c => c.coverageCode === 'DWELL');
      const dwellValue = dwell && dwell.values && dwell.values[0] ? +dwell.values[0].value : 0;
     const opt =  this.generalOptionsPolicies.find(c => c.coverageCode === 'BSC-DFIRE-000481');

              if (opt && opt.isActive) {
                policyItemHomeParsed.isActive = false;
                policyItemHomeParsed.isDisabled = true;
                policyItemHomeParsed.currentValue = '';
                policyItemHomeParsed.currentValueData.keyValue = [];
                policyItemHomeParsed.currentValueData.values = [];
              } else {
                policyItemHomeParsed.isDisabled = false;
              }
              if (dwellValue > 1000000 && !policyItemHomeParsed.isDisabled && !opt.isActive) {
                policyItemHomeParsed.additionalData.requiredForCarriers = ['201'];
              } else {
                policyItemHomeParsed.isRequired = false;
              }
          }
        });

      } break;
     }

    return policyItemHomeParsed;
  }
}
