export interface InventoryOrderItem {
	inventoryType: string;
	numberOfUnits: string;
}

export interface InventoryOrderRequest {
	inventoryOrderItems: InventoryOrderItem[];
}

export interface InventoryOrder {
	atlasInventoryOrderKey: string;
}

export interface Transaction {
	transactionAccepted: string;
	transactionTimestamp: string;
	atlasTransactionKey: string;
}

export interface TransactionDetail {
	requestID: string;
	transaction: Transaction;
}

export interface InventoryOrderResponse {
	success: boolean;
	inventoryOrder: InventoryOrder;
	transactionDetails: TransactionDetail;
	messages: string[];
}


export interface AvailableInventoryItem {
	inventoryItemId: number;
	inventoryItem: string;
	inventoryItemDescription: string;
	rmvIdentifier: string;
	unitQuantity: number;
	maxAllowableOrder: number;
	applicableEvrType: string;
}

export interface AvailableInventoryResponse {
	agencyFacilityId: number;
	agencyEvrType: string;
	availableInventoryItems: AvailableInventoryItem[];
}
