import { ApiResponse } from './_common';

interface IMetaHrefObj {
  href: string;
}

interface IMetaHref {
  meta: IMetaHrefObj;
}

export class Client {
  constructor(
    public city: string = null,
    public clientIdentifier: string = null,
    public name: string = null,
    public firstName: string = null,
    public businessName: string = null,
    public legalEntity: string = null,
    public importIndex: number = null,
    public lastName: string = null,
    public lastUpdated: string = null,
    public meta = {
      href: <string>''
    },
    public orderIndex: number = null,
    public quoteSessionId: string = null,
    public resourceId: string = null,
    public resourceName: string = 'Client',
    public state: string = null
  ) { }
}

export class ClientBusinessDetails {
  constructor(
    public name: string = '',
    public description: string = '',
    public website: string = '',
    public sicCode: string = '',
    public legalEntity: string = '',
    public taxId: string = '',
    public taxType: string = '',
    public businessStartDt: string = ''
  ) { }
}

export class ClientDetails {
  constructor(
    public business: ClientBusinessDetails = new ClientBusinessDetails(),
    public type: string = null,
    public addresses = {
      href: <string>''
    },
    public clientIdentifier: string = null,
    public comment: string = null,
    public contactMethods = {
      href: <string>''
    },
    public creationDate: string = null,
    public customerNumber: string = null,
    public customerSinceDate: string = null,
    public dob: string = null,
    public firstName: string = null,
    public importIndex: number = null,
    public lastModifiedDate: string = null,
    public lastName: string = null,
    public lastUpdated: string = null,
    public meta = {
      href: <string>''
    },
    public orderIndex: number = null,
    public parentId: string = null,
    public quoteSessionId: string = null,
    public resourceId: string = null,
    public resourceName: string = 'ClientDetails',
    public secondaryDOB: string = null,
    public secondaryFirstName: string = null,
    public secondaryLastName: string = null,
    public secondarySSN: string = null,
    public ssn: string = null,
  ) { }
}

export class ClientDetailsApiResponse extends ApiResponse<ClientDetails> { }

export class ClientContactMethod {
  constructor(
    public meta = {
      href: <string>'',
      rel: <Array<string>>[]
    },
    public parentId: string = null,
    public preferredMethod: string = null,
    public quoteSessionId: string = null,
    public resourceId: string = null,
    public resourceName: string = null,
    public type: string = null,
    public value: string = null
  ) { }
}

export class ClientAddress {
  constructor(
    public address1: string = null,
    public address2: string = null,
    public addressType: string = null,
    public city: string = null,
    public meta = {
      href: <string>'',
      rel: <Array<string>>[]
    },
    public parentId: string = null,
    public quoteSessionId: string = null,
    public residencyDate: string = null,
    public resourceId: string = null,
    public resourceName: string = 'ClientAddress',
    public state: string = null,
    public zip: string = null
  ) { }
}

export class ClientListItemDetails {
  constructor(
    public addresses: IMetaHref = {
      meta: { href: '' }
    },
    public city: string = null,
    public clientIdentifier: string = null,
    public comment: string = null,
    public contactMethods = {
      items: null,
      meta: { href: <string>'' }
    },
    public creationDate: string = null,
    public customerNumber: string = null,
    public customerSinceDate: string = null,
    public dob: string = null,
    public firstName: string = null,
    public importIndex: number = null,
    public lastName: string = null,
    public lastUpdated: string = null,
    public meta = {
      href: <string>''
    },
    public orderIndex: number = null,
    public primaryContactMethod: string = null,
    public primaryContactMethodType: string = null,
    public quoteSessionId: string = null,
    public resourceId: string = null,
    public resourceName: string = null,
    public businessName: string = null,
    public secondaryDOB: string = null,
    public secondaryFirstName: string = null,
    public secondaryLastName: string = null,
    public secondarySSN: string = null,
    public ssn: string = null,
    public state: string = null
  ) { }
}

export class ClientListItemApiResponse extends ApiResponse<ClientListItemDetails> { }
