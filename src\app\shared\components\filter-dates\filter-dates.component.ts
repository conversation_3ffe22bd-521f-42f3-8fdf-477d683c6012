import { Component, OnInit, EventEmitter, Output, ViewChild } from '@angular/core';
import { DateRange } from 'app/app-model/date';
import { FilterOption } from 'app/app-model/filter-option';
import { DatesService } from 'app/dashboard/app-services/dates.service';
import { FilterComponent } from 'app/shared/components/filter/filter.component';
import { format } from 'date-fns';

@Component({
    selector: 'app-filter-dates',
    templateUrl: './filter-dates.component.html',
    styleUrl: './filter-dates.component.scss',
    standalone: false
})
export class FilterDatesComponent implements OnInit {

  constructor(private datesService: DatesService) { }

  @Output() getByFilter: EventEmitter<any> = new EventEmitter();

  @ViewChild('refFilterDates') filter;
  @ViewChild('refFilterDates') public FilterDates: FilterComponent

  public filterDatesLabel: string;
  
  public filterDatesTypesOptions: FilterOption[] = this.datesService.getDatesTypesOptions();
  public filterDatesRangeOptions: FilterOption[] = this.datesService.getDateRangesOptions(this.filterDatesTypesOptions[0]);
  public filterDatesRangeSelectedOption: FilterOption = this.filterDatesRangeOptions[0];
 
  public dateRange: DateRange = new DateRange();
  public dateRangeCustom: DateRange = new DateRange();
  
  public searchQuery: string;
  
  ngOnInit(): void {
     this.setFilterDatesRangesOptions();
    
  }

  public onDatesParamsChangeHandler($ev: any, updateField: string): void {
    // https://bostonsoftware.atlassian.net/browse/SPRC-560
    // When Date Range selection is Custom, filtering shouldn't begin until Ending date is entered
    let allowFiltering = true;

    switch (updateField) {
      case 'dateRange':
        this.onDateRangeChange($ev);
        break;
      case 'dateRangeStart':
        this.updateRangeDateProperty($ev, 'start');
        $ev.selectByClick && this.updateDateRangeOptionIfCustomRange();
        if (!this.dateRange.start || !this.dateRange.end) {
          allowFiltering = false;
        }
        break;
      case 'dateRangeEnd':
        this.updateRangeDateProperty($ev, 'end');
        $ev.selectByClick && this.updateDateRangeOptionIfCustomRange();
        if (!this.dateRange.start || !this.dateRange.end) {
          allowFiltering = false;
        }
        break;
    }

    this.updateFilterDatesLabel();

  }

  getFromFilteredDates(event) {
    this.getByFilter.emit(event);
    this.FilterDates.tooltip.close();
  }

  public setFilterDatesRangesOptions(): void {
    this.filterDatesLabel = this.formatFilterLabelDateTypes();
    this.filterDatesRangeSelectedOption = this.filterDatesRangeOptions[0];
  }

  public prepareResetSearch(): void {
    this.filterDatesRangeSelectedOption = this.filterDatesRangeOptions[0];
    this.setFilterDatesRangesOptions();
    this.filterDatesLabel = 'Last 7 Days';
    this.searchQuery=null;
  }


  public isFilteringAllowed(): boolean {
    let allowFiltering = true;

    if (this.filterDatesRangeSelectedOption.id !== 'all' && (!this.dateRange.start || !this.dateRange.end)) {
      allowFiltering = false;
    }

    if (this.filterDatesRangeSelectedOption.id === 'custom') {
      if (!this.dateRange.start) {
        this.dateRange.start = this.dateRange.end
        allowFiltering = true
      }
      if (!this.dateRange.end) {
        this.dateRange.end = this.dateRange.start
        allowFiltering = true
      }
    }

    return allowFiltering
  }



  private getDateRangeFromSelectedOption($ev: FilterOption, customDateRange: DateRange): DateRange {
    switch ($ev.id) {
      case 'all':
        return new DateRange(null, null);
      case 'custom':
        return customDateRange;
      case 'today':
        return this.datesService.getRangeToday();
      case 'yesterday':
        return this.datesService.getRangeYesterday();
      case 'thisweek':
        return this.datesService.getRangeThisWeek();
      case 'thismonth':
        return this.datesService.getRangeThisMonth();
      case 'thisyear':
        return this.datesService.getRangeThisYear();
      case 'lastweek':
        return this.datesService.getRangeLastWeek();
      case 'lastmonth':
        return this.datesService.getRangeLastMonth();
      case 'last30days':
        return this.datesService.getRangeLast30days();
      case 'lastyear':
        return this.datesService.getRangeLastYear();
      case 'nextweek':
        return this.datesService.getRangeNextWeek();
      case 'nextmonth':
        return this.datesService.getRangeNextMonth();
      case 'nextyear':
        return this.datesService.getRangeNextYear();
      default:
        return new DateRange();
    }
  }
  
  private onDateRangeChange($ev: FilterOption): void {
    this.dateRange = this.getDateRangeFromSelectedOption($ev, this.dateRangeCustom);
    this.filterDatesRangeSelectedOption = $ev;
  }

  private updateRangeDateProperty($ev, fieldName: string): void {
    let tmpDate = $ev.date;

    switch (fieldName) {
      case 'start':
        tmpDate = this.datesService.getDateStartRangeFromDate($ev.date);
        this.dateRangeCustom.start = ($ev.selectByClick) ? tmpDate : this.dateRangeCustom.start;
        if($ev.selectedManually) this.filterDatesRangeSelectedOption =this.filterDatesRangeOptions.find(x => x.id === 'custom')
        break;
      case 'end':
        tmpDate = this.datesService.getDateEndRangeFromDate($ev.date);
        this.dateRangeCustom.end = ($ev.selectByClick) ? tmpDate : this.dateRangeCustom.end;
        if($ev.selectedManually) this.filterDatesRangeSelectedOption =this.filterDatesRangeOptions.find(x => x.id === 'custom')
        break;
    }

    this.dateRange[fieldName] = tmpDate;
  }

  private updateDateRangeOptionIfCustomRange(): void {
    this.filterDatesRangeSelectedOption = this.filterDatesRangeOptions.filter(opt => opt.id === 'custom')[0];
  }

  private updateFilterDatesLabel(): void {
    this.filterDatesLabel = this.formatFilterLabelDateTypes();
  }

  private formatFilterLabelDateTypes(): string {
    let formatedLabel = 'Last 7 days';
   const startDate = (this.dateRange.start) ? format(new Date(this.dateRange.start), 'MMM d, yyyy') : null;
const endDate = (this.dateRange.end) ? format(new Date(this.dateRange.end), 'MMM d, yyyy') : null;

    if (!this.dateRange.start && !this.dateRange.end) {
      formatedLabel = 'Last 7 days';
    } else if (this.dateRange.start && this.dateRange.end) {
      formatedLabel = (startDate === endDate) ? `${startDate}` : `${startDate} - ${endDate}`;
    } else if (this.dateRange.start && !this.dateRange.end) {
      formatedLabel = `From ${startDate}`;
    } else if (!this.dateRange.start && this.dateRange.end) {
      formatedLabel = `To ${endDate}`;
    }

    return formatedLabel;
  }

}
