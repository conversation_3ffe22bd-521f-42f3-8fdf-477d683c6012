import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { StorageService } from 'app/shared/services/storage-new.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { AgencyUserService } from 'app/shared/services/agency-user.service';
import { VehiclesService } from 'app/dashboard/app-services/vehicles.service';
import { OptionsService } from 'app/dashboard/app-services/options.service';
import { PremiumsService } from 'app/dashboard/auto/premiums/premiums.service';


import { AutoStandardCoveragesComponent } from './auto-standard-coverages.component';
import { StubAutocompleteComponent } from 'testing/stubs/components/autocomplete.component';
import { StubModalboxComponent } from 'testing/stubs/components/modalbox.component';
import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { LocationsService } from 'app/dashboard/app-services/locations.service';
import { SymbolsService } from 'app/dashboard/app-services/symbols.service';
import { PlansService } from 'app/dashboard/app-services/plans.service';
import { LookupsService } from 'app/dashboard/app-services/lookups.service';

describe('Components: AutoStandardCoveragesComponent', () => {
  let component: AutoStandardCoveragesComponent;
  let fixture: ComponentFixture<AutoStandardCoveragesComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [
        AutoStandardCoveragesComponent,
        StubAutocompleteComponent,
        StubModalboxComponent
      ],
      providers: [
        StorageService,
        SpecsService,
        AgencyUserService,
        VehiclesService,
        OptionsService,
        PremiumsService,
        QuotesService,
        VehiclesService,
        LocationsService,
        SymbolsService,
        PlansService,
        LookupsService
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AutoStandardCoveragesComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
