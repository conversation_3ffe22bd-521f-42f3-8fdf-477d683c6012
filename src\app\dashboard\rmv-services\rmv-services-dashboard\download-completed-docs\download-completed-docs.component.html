<div class="section">
  <div class="row">
    <div class="col-xs-12">
      <div class="u-flex u-flex--to-middle">
        <div class="search">
          <input #searchInput [id]="'filter_NAME'" [(ngModel)]="searchQuery" (keydown.enter)="filterByName($event)"
            class="search__input search__input--md" type="text" placeholder="Search By {{searchType | titlecase}}"
            changeDetectionDelay />
          <button *ngIf="searchQuery && searchQuery.length > 0" (click)="searchQuery = '';resetSearch()" name="cancel"
            class="reset__button"></button> <button (click)="filterByName($event)" name="searchbtn"
            class="search__button">
          </button>
        </div>

        <div>
          <label class="o-checkable u-spacing--left-2" [appDetectSystem] [removeRadio]="true">
            <input type="radio" name="searchType" value="vin" [(ngModel)]="searchType">
            <i class="o-btn o-btn--radio"></i>
            <span>VIN</span>
          </label>
          <label class="o-checkable u-spacing--left-2" [appDetectSystem] [removeRadio]="true">
            <input type="radio" name="searchType" value="plate" [(ngModel)]="searchType">
            <i class="o-btn o-btn--radio"></i>
            <span>Plate</span>
          </label>
          <label class="o-checkable u-spacing--left-2" [appDetectSystem] [removeRadio]="true">
            <input type="radio" name="searchType" value="lastname" [(ngModel)]="searchType">
            <i class="o-btn o-btn--radio"></i>
            <span>Last Name</span>
          </label>
          <label class="o-checkable u-spacing--left-2" [appDetectSystem] [removeRadio]="true">
            <input type="radio" name="searchType" value="businessname" [(ngModel)]="searchType">
            <i class="o-btn o-btn--radio"></i>
            <span>Business Name</span>
          </label>
        </div>

      </div>
    </div>
  </div>
  <span style="color: red" *ngIf="searchQuery && searchQuery.length < 3">Minimum search length is 3</span>

</div>

<section class="section">
  <div class="row">
    <div class="col-xs-12">
      <app-filter *ngIf="filterRequesterOptions?.length" [id]="'filter_transactionType'" [name]="'TransactionType'"
      [options]="filterTransactionTypeOptions" [selectedOption]="filterTransactionTypeSelectedOption"
      (onChange)="onFilterDataChange($event)"></app-filter>
      <app-filter *ngIf="filterRequesterOptions?.length" [id]="'filter_requester'" [name]="'Requester'"
        [options]="filterRequesterOptions" [selectedOption]="filterLocationsSelectedOption"
        (onChange)="onFilterDataChange($event)"></app-filter>

      <app-filter-dates (getByFilter)="getTransactionsFromFilteredDates()"></app-filter-dates>

    </div>


  </div>
</section>

<section class="section">
  <div class="col-xs-14">Showing Last 7 Days</div>
</section>

<section class="section">
  <div class="row">
    <div class="col-xs-12">
      <table class="table table--compact table--hoverable-grey">
        <thead class="table__thead">
          <tr class="">
            <th class="table__th ">Vin/Plate</th>
            <th class="table__th ">Owner</th>
            <th class="table__th">Agent</th>
            <th class="table__th ">Rmv Transaction</th>
            <th class="table__th">Last Modified</th>
            <th class="table__th ">Documents</th>
          </tr>
        </thead>
        <tbody class="table__tbody" *ngIf="list">
          <tr class="table__tr" *ngFor="let row of list">

            <td class="table__td">
                <span *ngIf="row.vin;else plate">{{row.vin}}</span>
                <ng-template #plate>
                  <span>{{row.plate}}</span>
                </ng-template>
            </td>
            <td class="table__td">{{row.ownerFirstName}} {{row.ownerLastName}}</td>
            <td class="table__td">
              {{ row.userFirstName }} {{ row.userLastName }}
            </td>
            <td class="table__td">{{splitWords(row.transactionType)}}</td>
            <td class="table__td">{{row.lastModifiedDate}}</td>

            <td class="table__td"><button class="o-btn" (click)="downloadDocuments(row)">Download</button></td>

          </tr>
        </tbody>
        <tbody class="table__tbody" *ngIf="!list?.length">
          <tr class="table__tr">
            <td colspan="7">
              <p class="u-padd--bottom-1 u-padd--1">
                There are no results that match your search.
              </p>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

</section>

<section class="section">
  <div class="u-flex u-flex--spread u-flex--to-middle">
    <div class="">
      <app-pagination [currentPage]="paginationCurrentPage" [totalRecords]="size" [recordsLimit]="limit"
        (onPageChange)="paginationPageChange($event)"></app-pagination>
    </div>
    <div class="">
      <app-results-limiter (onChange)="onResultLimitChange($event)"
        [description]="'Transactions to show'"></app-results-limiter>
    </div>
  </div>
  <div style="padding-top:20px;">
    <button class="o-btn" [routerLink]="'/dashboard/rmv-services'">RETURN TO RMV DASHBOARD</button>
  </div>

