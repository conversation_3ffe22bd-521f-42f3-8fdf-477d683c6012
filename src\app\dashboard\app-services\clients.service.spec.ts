import { inject, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { MockBackend } from 'testing/setups/mock-backend';

import { data as ADDRESSES } from 'testing/data/quote-clients/addresses';
import { data as CONTACT_EMAIL } from 'testing/data/quote-clients/contact-email';
import { data as CONTACT_FAX } from 'testing/data/quote-clients/contact-fax';
import { data as CONTACT_HOME } from 'testing/data/quote-clients/contact-home';
import { data as CONTACT_MOBILE } from 'testing/data/quote-clients/contact-mobile';
import { data as CONTACT_WORK } from 'testing/data/quote-clients/contact-work';
import { CLIENTS } from 'testing/data/quotes/clients';
import {
    DataCustomMatchers, expectLastConnectionPayload, expectLastConnectionUrl
} from 'testing/helpers/all';
import { setupMockBackend } from 'testing/setups/mock-backend';

import { ApiResponse } from 'app/app-model/_common';
import { ClientContactMethod, C<PERSON>Deta<PERSON>, ClientAddress } from 'app/app-model/client';

import { ClientsService } from './clients.service';
import { Helpers } from 'app/utils/helpers';

describe('Service: Clients', () => {
  let service: ClientsService;
  let mockBackend: MockBackend;

  const CONTACT_METHODS = new ApiResponse();

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        ClientsService
      ]
    });

    jasmine.addMatchers(DataCustomMatchers);
  });

  beforeEach(inject([ClientsService], (_service: ClientsService) => {
    service = _service;

    CONTACT_METHODS.items = [
      CONTACT_HOME,
      CONTACT_WORK,
      // no fax on purpose
      CONTACT_MOBILE,
      CONTACT_EMAIL
    ];
  }));

  it('can instantiate service when inject service', () => {
    expect(service).toBeTruthy();
  });

  describe('when working with client data', () => {
    it('can retrieve clients list using quote ID', () => {
      mockBackend = setupMockBackend(CLIENTS);

      service.getClientsList('quote-id').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/clients');
    });

    it('can create client using quote ID', () => {
      mockBackend = setupMockBackend(new ClientDetails());

      service.createQuoteClient('quote-id').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/clients');
      expectLastConnectionPayload(mockBackend).toEqual({});
    });

    it('can update client using quote ID and client ID', () => {
      mockBackend = setupMockBackend(CLIENTS.items[0]);

      service.updateClient('quote-id', 'client-id', CLIENTS.items[0]).subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/clients/client-id');
      expectLastConnectionPayload(mockBackend).toEqual(CLIENTS.items[0]);
    });

    it('can retrieve client using quote ID and client ID', () => {
      mockBackend = setupMockBackend(CLIENTS.items[0]);

      service.getClient('quote-id', 'client-id').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/clients/client-id');
    });
  });

  describe('when working with client contact methods', () => {
    it('can retrieve contact methods list using client ID', () => {
      mockBackend = setupMockBackend(CONTACT_METHODS);

      service.getContactMethods('client-id').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quoteclients/client-id/contactMethods');
    });

    it('can create contact method using client ID', () => {
      mockBackend = setupMockBackend(new ClientContactMethod());

      service.createContactMethod('client-id').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quoteclients/client-id/contactMethods');
      expectLastConnectionPayload(mockBackend).toEqual({});
    });

    it('can update contact method using client ID and method ID', () => {
      mockBackend = setupMockBackend(CONTACT_EMAIL);

      service.updateContactMethod('client-id', 'method-id', CONTACT_EMAIL).subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quoteclients/client-id/contactMethods/method-id');
      expectLastConnectionPayload(mockBackend).toEqual(CONTACT_EMAIL);
    });

    it('can retrieve contact method using client ID and method ID', () => {
      mockBackend = setupMockBackend(CONTACT_EMAIL);

      service.getContactMethod('client-id', 'method-id').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quoteclients/client-id/contactMethods/method-id');
    });

    it('can delete contact method using client ID and method ID', () => {
      mockBackend = setupMockBackend(CONTACT_EMAIL);

      service.deleteContactMethod('client-id', 'method-id').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quoteclients/client-id/contactMethods/method-id');
    });

    it('can retrieve contact methods list or create them if not existing', fakeAsync(() => {
      mockBackend = setupMockBackend(CONTACT_METHODS, CONTACT_FAX);

      service.getClientContactMethodsAndCreateNotExisting(CLIENTS.items[0]);
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith('/quoteclients/1943c855-b32e-4557-998b-9d178b8ff940/contactmethods');
      expectLastConnectionPayload(mockBackend).toEqual(jasmine.objectContaining({
        type: 'Fax'
      }));
    }));
  });

  describe('when working with client addresses', () => {
    it('can retrieve addresses list using client ID', () => {
      mockBackend = setupMockBackend(ADDRESSES);

      service.getAddresses('client-id').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quoteclients/client-id/addresses');
    });

    it('can create address using client ID', () => {
      mockBackend = setupMockBackend(new ClientAddress());

      service.createAddress('client-id').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quoteclients/client-id/addresses');
      expectLastConnectionPayload(mockBackend).toEqual({});
    });

    it('can update address using client ID and address ID', () => {
      mockBackend = setupMockBackend(ADDRESSES.items[0]);

      service.updateAddress('client-id', 'address-id', ADDRESSES.items[0]).subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quoteclients/client-id/addresses/address-id');
      expectLastConnectionPayload(mockBackend).toEqual(ADDRESSES.items[0]);
    });

    it('can retrieve address using client ID and address ID', () => {
      mockBackend = setupMockBackend(ADDRESSES.items[0]);

      service.getAddress('client-id', 'address-id').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quoteclients/client-id/addresses/address-id');
    });

    it('can delete address using client ID and address ID', () => {
      mockBackend = setupMockBackend(ADDRESSES.items[0]);

      service.deleteAddress('client-id', 'address-id').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quoteclients/client-id/addresses/address-id');
    });

    it('can retrieve client addresses or create them if not existing', fakeAsync(() => {
      const prevAddr = Helpers.deepClone(ADDRESSES.items[0]);
      prevAddr.addressType = 'PreviousAddress';

      mockBackend = setupMockBackend({ items: [ADDRESSES.items[0]] }, prevAddr);

      service.getClientAddressesAndCreateNotExisting(CLIENTS.items[0]);
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith('/quoteclients/1943c855-b32e-4557-998b-9d178b8ff940/addresses');
      expectLastConnectionPayload(mockBackend).toEqual(jasmine.objectContaining({
        addressType: 'PreviousAddress'
      }));
    }));
  });
});
