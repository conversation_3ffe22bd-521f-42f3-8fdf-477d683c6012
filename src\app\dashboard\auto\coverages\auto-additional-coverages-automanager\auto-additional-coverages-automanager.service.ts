import { debounceTime, take } from 'rxjs/operators';
import { Injectable } from '@angular/core';

import {
  BehaviorSubject,
  SubscriptionLike as ISubscription,
  Subject
} from 'rxjs';

// Services
import { OptionsService } from 'app/dashboard/app-services/options.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { SubsService } from 'app/dashboard/app-services/subs.service';
import { AgencyUserService } from 'app/shared/services/agency-user.service';
import { VehiclesService } from 'app/dashboard/app-services/vehicles.service';

// Models
import { Vehicle, VehicleCoverages } from 'app/app-model/vehicle';
import {
  QuoteAuto,
  QuotePlan,
  QuotePlanListAPIResponse
} from 'app/app-model/quote';
import {
  Coverage,
  PolicyCoveragesData,
  CoverageItem,
  CoverageItemParsed,
  PolicyItemDefaultDataAPIResponse,
  CoverageApiResponseData
} from 'app/app-model/coverage';

interface TriggerSourceI {
  triggerBy?: 'Vehicles' | 'SelectedPlans' | 'QuoteEffectiveDate';
  vehiclesResourceId?: string[]; // if defined, will be processed only for specific vehicles
}

interface PromiseDataAfterSettingDefaultValuesNew {
  policiesToUpdate: CoverageItemParsed[];
  policyItemsParsed: CoverageItemParsed[];
  vehicleCoverages: VehicleCoverages;
}

interface TriggerStatusUpdateI {
  triggerBy?: 'StandardCoverages' | 'AdditionalCoverages';
}

@Injectable()
export class AutoAdditionalCoveragesAutomanagerService {
  private serviceIsInitialized = false;

  private subscriptionQuote: ISubscription;
  private subscriptionQuoteIsNew: ISubscription;
  private subscriptionSelectedPlans: ISubscription;
  private subscriptionParserTrigger: ISubscription;
  private subscriptionVehicles: ISubscription;
  private subscriptionUserData: ISubscription;
  private subscriptionAutoCoveragesStandardForVehicles: ISubscription;
  private subscriptionAutoCoveragesAdditionalForVehicles: ISubscription;
  private subscriptionStatusUpdateTrigger: ISubscription;

  private _parserTrigger: BehaviorSubject<TriggerSourceI> = new BehaviorSubject(
    {}
  );
  private _statusUpdateTrigger: BehaviorSubject<
    TriggerStatusUpdateI
  > = new BehaviorSubject({});

  private quote: QuoteAuto;
  private quoteId = '';
  private quoteLob = '';
  private quoteIsNew = false;
  private selectedPlans: QuotePlan[] = [];
  private selectedPlansIds: string[] = [];
  private vehicles: Vehicle[] = [];
  private agencyId = '';
  private defaultCoverageValues: PolicyItemDefaultDataAPIResponse = new PolicyItemDefaultDataAPIResponse();
  private autoCoveragesStandardForVehicles: VehicleCoverages[] = [];
  private autoCoveragesAdditionalForVehicles: VehicleCoverages[] = [];
  private vehicleResourceIdsAlreadyWithDefaultValues: string[] = [];

  private quoteBeforeChange: QuoteAuto = null;

  constructor(
    private storageService: StorageService,
    private agencyUserService: AgencyUserService,
    private specsService: SpecsService,
    private optionsService: OptionsService,
    private subsService: SubsService,
    private vehiclesService: VehiclesService
  ) {}

  public initialize(): Promise<void> {
    if (this.serviceIsInitialized) {
      return Promise.resolve();
    }

    this.serviceIsInitialized = true;
    console.log('Initialize Additional Coverages');

    return Promise.all([
      this.subscribeQuote(),
      this.subscribeQuoteIsNew(),
      this.subscribeSelectedPlans(),
      this.subscribeVehicles(),
      this.subscribeUserData(),
      this.subscribeAutoCoveragesStandardForVehicles(),
      this.subscribeAutoCoveragesAdditionalForVehicles()
    ])
      .then(() => {
        return this.getDefaultCoveragesValues(this.agencyId);
      })
      .then(() => {
        return this.initPolicyItemsParser();
      })
      .then(() => {
        return this.subscribeForForErrorAndAvailabilityStatus();
      })
      .catch(err => {
        console.log(err);
      });
  }

  public destroy(): void {
    this.serviceIsInitialized = false;

    this.subscriptionQuote && this.subscriptionQuote.unsubscribe();
    this.subscriptionQuoteIsNew && this.subscriptionQuoteIsNew.unsubscribe();
    this.subscriptionSelectedPlans &&
      this.subscriptionSelectedPlans.unsubscribe();
    this.subscriptionParserTrigger &&
      this.subscriptionParserTrigger.unsubscribe();
    this.subscriptionVehicles && this.subscriptionVehicles.unsubscribe();
    this.subscriptionUserData && this.subscriptionUserData.unsubscribe();
    this.subscriptionAutoCoveragesStandardForVehicles &&
      this.subscriptionAutoCoveragesStandardForVehicles.unsubscribe();
    this.subscriptionAutoCoveragesAdditionalForVehicles &&
      this.subscriptionAutoCoveragesAdditionalForVehicles.unsubscribe();
    this.subscriptionStatusUpdateTrigger &&
      this.subscriptionStatusUpdateTrigger.unsubscribe();
  }

  private subscribeQuote(): Promise<QuoteAuto> {
    return new Promise((resolve, reject) => {
      this.subscriptionQuote = this.storageService
        .getStorageData('selectedQuote')
        .subscribe((quote: QuoteAuto) => {
          this.quote = JSON.parse(JSON.stringify(quote));
          this.quoteId = quote.resourceId;
          this.quoteLob = quote.lob;

          resolve(this.quote);

          if (!this.quoteBeforeChange) {
            this.quoteBeforeChange = JSON.parse(JSON.stringify(quote));
          } else if (
            this.quoteBeforeChange.effectiveDate !== this.quote.effectiveDate
          ) {
            this.quoteBeforeChange = JSON.parse(JSON.stringify(quote));

            // INIT POLICY ITEMS PARSING After Quote Effective date change
            // Emit Observable to Parse Items
            this._parserTrigger.next({ triggerBy: 'QuoteEffectiveDate' });
          }
        });
    });
  }

  private subscribeQuoteIsNew(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.subscriptionQuoteIsNew = this.storageService
        .getStorageData('isNewQuote')
        .subscribe((res: boolean) => {
          this.quoteIsNew = res;
          resolve(this.quoteIsNew);
        });
    });
  }

  private subscribeSelectedPlans(): Promise<QuotePlan[]> {
    return new Promise((resolve, reject) => {
      this.subscriptionSelectedPlans = this.storageService
        .getStorageData('selectedPlan')
        .subscribe((res: QuotePlanListAPIResponse) => {
          if (res && res.items && res.items.length) {
            this.selectedPlans = JSON.parse(JSON.stringify(res.items[0].items));
            this.selectedPlansIds = this.selectedPlans.map(
              plan => plan.ratingPlanId
            );
            resolve(this.selectedPlans);

            // INIT POLICY ITEMS PARSING
            // Emmit Observable to Parse Items
            this._parserTrigger.next({ triggerBy: 'SelectedPlans' });
            console.log(this._parserTrigger.getValue(), 'SelectedPlans');
          }
        });
    });
  }

  private subscribeUserData(): Promise<any> {
    return new Promise<void>((resolve, reject) => {
      this.subscriptionUserData = this.agencyUserService.userData$.subscribe(
        agent => {
          this.agencyId = agent.agencyId;
          resolve();
        }
      );
    });
  }

  private subscribeVehicles(): Promise<Vehicle[]> {
    let vehiclesCount = 0;
    let vehicles: Vehicle[] = [];
    let firstSubscribeDone = false;

    return new Promise((resolve, reject) => {
      this.subscriptionVehicles = this.storageService
        .getStorageData('vehiclesList')
        .subscribe((res: Vehicle[]) => {
          this.vehicles = JSON.parse(JSON.stringify(res));
          resolve(this.vehicles);

          // Set vehicleResourceIdsAlreadyWithDefaultValues to be able to set default values for newly created vehicles
          if (!firstSubscribeDone) {
            firstSubscribeDone = true;
            this.vehicleResourceIdsAlreadyWithDefaultValues = this.vehicles.map(
              (v: Vehicle) => v.resourceId
            );
          }

          if (vehiclesCount !== this.vehicles.length) {
            vehiclesCount = this.vehicles.length;

            // Keep only coverages data for the existing vehicles
            this.storageService
              .getStorageData('autoCoveragesAdditionalForVehicles')
              .pipe(take(1))
              .subscribe(res => {
                const toKeep = res.filter(el => {
                  const exists = this.vehicles.find(
                    veh => el.vehicleResourceId === veh.resourceId
                  );
                  return exists ? true : false;
                });

                this.storageService.setStorageData(
                  'autoCoveragesAdditionalForVehicles',
                  toKeep
                );
              });
          }

          // Update vehicle Data each time the vehicle has been updated
          this.storageService
            .getStorageData('autoCoveragesAdditionalForVehicles')
            .pipe(take(1))
            .subscribe((res: VehicleCoverages[]) => {
              this.vehicles.forEach((vehicle: Vehicle) => {
                const vehicleCoveragesToUpdate = res.find(
                  (item: VehicleCoverages) => {
                    return item.vehicleResourceId === vehicle.resourceId;
                  }
                );

                if (vehicleCoveragesToUpdate) {
                  vehicleCoveragesToUpdate.vehicle = vehicle;
                }
              });

              this.storageService.setStorageData(
                'autoCoveragesAdditionalForVehicles',
                res
              );
            });

          // Emmit Observable to Parse Items only on vehicles count change or vehicle type change
          const vehiclesCoveragesToRefresh = this.vehicles.filter(veh => {
            const vehicleToCheck = vehicles.find(
              item => item.resourceId === veh.resourceId
            );

            if (veh.resourceId && !vehicleToCheck) {
              return true; // New vehicle, require update for vehicle
              // } else if (vehicleToCheck.vehicleType == veh.vehicleType) {
            } else if (
              vehicleToCheck.vehicleType === veh.vehicleType ||
              !vehicleToCheck.vehicleType
            ) {
              // Vehicle type hasn't change || If previously vehicle had vehicleType set to null or undefined,
              // it means that this vehicle is newly added, so request was already emitted
              return false;
            } else {
              return true; // Vehicle type has changed, require update for vehicle
            }
          });

          // Save old data about vehicles
          vehicles = JSON.parse(JSON.stringify(this.vehicles));

          // If new vehicle or vehicleType has change for some vehicle, Emit Observable
          if (vehiclesCoveragesToRefresh && vehiclesCoveragesToRefresh.length) {
            const vehiclesIds = vehiclesCoveragesToRefresh.map(
              el => el.resourceId
            );
            this._parserTrigger.next({
              triggerBy: 'Vehicles',
              vehiclesResourceId: vehiclesIds
            });
          }
        });
    });
  }

  private initPolicyItemsParser(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.subscriptionParserTrigger = this._parserTrigger
        .asObservable()
        .subscribe((res: TriggerSourceI) => {
          let vehiclesToParseFor: Vehicle[] = [];

          if (res.vehiclesResourceId && res.vehiclesResourceId.length) {
            vehiclesToParseFor = this.vehicles.filter(
              veh => res.vehiclesResourceId.indexOf(veh.resourceId) !== -1
            );
          } else {
            vehiclesToParseFor = this.vehicles;
          }

          this.getOptionsPoliciesListAndParseDataForVehicles(vehiclesToParseFor)
            .then(() => resolve())
            .catch(err => reject(err));
        });
    });
  }

  private getOptionsPoliciesListAndParseDataForVehicles(
    vehicles: Vehicle[]
  ): Promise<void> {
    if (!vehicles || !vehicles.length) {
      return;
    }

    const selectedPlansIdsString = this.selectedPlansIds.join(',');
    const states = 'MA';
    const quoteEffectiveDate =
      this.quote && this.quote.effectiveDate ? this.quote.effectiveDate : '';

    let agencyId;
    this.agencyUserService.userData$
      .pipe(take(1))
      .subscribe(agent => (agencyId = agent.agencyId));

    const promises: Promise<void>[] = [];

    vehicles.forEach((vehicle: Vehicle) => {
      // Where from get rating coverages
      // let coverageGroup = this.getcoverageGroupForRequest(vehicle);
      const coverageGroup = 'vehicle';

      const tmpPromise: Promise<void> = new Promise((resolve, reject) => {
        this.specsService
          .getRatingCoverages(
            states,
            this.quoteLob,
            coverageGroup,
            selectedPlansIdsString,
            '',
            quoteEffectiveDate
          )
          .pipe(take(1))
          .subscribe(
            res => {
              let policiesItems: CoverageItem[] = [];
              if (res && res.items && res.items.length) {
                policiesItems = [...res.items];
              }

              if (policiesItems && this.quoteId) {
                this.processPoliciesItemsParsingForVehicle(
                  policiesItems,
                  this.selectedPlans,
                  this.quoteId,
                  vehicle
                )
                  .then((res: VehicleCoverages) =>
                    this.setDefaultValuesForVehicleCoveragesIfThisIsNewQuoteOrNewVehicleAdded(
                      res,
                      this.quoteIsNew
                    )
                  )
                  .then((res: PromiseDataAfterSettingDefaultValuesNew) => {
                    // Update coverages To API (if new values were set)
                    if (res.policiesToUpdate && res.policiesToUpdate.length) {
                      this.saveCoveragesForVehicleToAPI(
                        res.vehicleCoverages
                      ).then(() =>
                        this.storageService.updateAutoCoveragesAdditionalForVehiclesSingleItem(
                          res.vehicleCoverages
                        )
                      );
                    } else {
                      this.storageService.updateAutoCoveragesAdditionalForVehiclesSingleItem(
                        res.vehicleCoverages
                      );
                    }
                  })
                  .then(() => resolve())
                  .catch(err => {
                    console.log(err);
                    reject(err);
                  });
              }
            },
            err => {
              console.log(
                'Fetching Rating Coverages for: ' +
                  coverageGroup +
                  ' ERROR Occurred: ',
                err
              );
              reject(err);
            }
          );
      });

      promises.push(tmpPromise);
    });

    return Promise.all(promises).then(() => void 0);
  }

  public processPoliciesItemsParsingForVehicle(
    policies: CoverageItem[],
    selectedPlans: QuotePlan[],
    quoteId: string,
    vehicle: Vehicle,
    forceDefaultValues: boolean = false
  ): Promise<VehicleCoverages> {
    let arrPoliciesItemsParsed = this.optionsService.parsePoliciesItemsToPoliciesItemsParsed(
      policies,
      vehicle.resourceId
    );
    arrPoliciesItemsParsed = this.optionsService.orderObjectsArrayByProperty(
      arrPoliciesItemsParsed,
      'description',
      true,
      true
    );

    // Required for setting default values if new Quote - to check if options
    // has been alredy saved in storage or not
    let vehicleCoveragesFromStorage: VehicleCoverages[] = [];
    let policiesItemsFromStorage: CoverageItemParsed[] = [];

    this.storageService
      .getStorageData('autoCoveragesAdditionalForVehicles')
      .pipe(take(1))
      .subscribe(res => (vehicleCoveragesFromStorage = res));

    const tmpCurrentVehicleCoverages = vehicleCoveragesFromStorage.find(
      (el: VehicleCoverages) => el.vehicleResourceId === vehicle.resourceId
    );

    if (tmpCurrentVehicleCoverages && tmpCurrentVehicleCoverages.options) {
      policiesItemsFromStorage = JSON.parse(
        JSON.stringify(tmpCurrentVehicleCoverages.options)
      );
    }
    // --------------------------------------------------------------------------

    return this.loadAdditionalCoveragesForVehicle(vehicle).then(
      (data: PolicyCoveragesData) => {
        let tmpClonedArrPoliciesItemsParsed = JSON.parse(
          JSON.stringify(arrPoliciesItemsParsed)
        );

        tmpClonedArrPoliciesItemsParsed = tmpClonedArrPoliciesItemsParsed.map(
          item => {
            item = this.optionsService.sortPolicyItemParsedChildItems(item);
            item = this.optionsService.setPolicyItemStatusBasedOnPolicyCoverages(
              item,
              data.coverages
            );
            // item = this.setPolicyItemStatusBasedOnRequirements(item, quoteSelectedFormTypes);

            item.endpointUrl = data.endpointURL;
            item = this.optionsService.setPolicyItemParsedIsNewFromAPIValue(
              item,
              policiesItemsFromStorage
            );
            item.quoteResourceId = quoteId;

            if (item.inputType !== 'Dropdown') {
              item.values = this.optionsService.orderObjectsArrayByProperty(
                item.values,
                'value'
              );
            }

            return item;
          }
        );

        const tmpVehicleCoverages: VehicleCoverages = new VehicleCoverages();
        tmpVehicleCoverages.vehicle = vehicle;
        tmpVehicleCoverages.vehicleResourceId = vehicle.resourceId;
        tmpVehicleCoverages.options = tmpClonedArrPoliciesItemsParsed;

        // console.log('%c tmpVehicleCoverages', 'color:red;text-transform:uppercase;', tmpVehicleCoverages);

        return tmpVehicleCoverages;
      }
    );
  }

  private loadAdditionalCoveragesForVehicle(
    vehicle: Vehicle
  ): Promise<PolicyCoveragesData> {
    const policyCoverageData: PolicyCoveragesData = new PolicyCoveragesData();

    return new Promise((resolve, reject) => {
      if (
        vehicle &&
        vehicle.additionalCoverages &&
        vehicle.additionalCoverages.meta &&
        vehicle.additionalCoverages.meta.href
      ) {
        this.vehiclesService
          .getVehicleAdditionalCoverages(vehicle.additionalCoverages.meta.href)
          .subscribe((res: CoverageApiResponseData) => {
            if (res && res.items[0]) {
              policyCoverageData.coverages = res.items[0].coverages;

              if (res.items[0].meta && res.items[0].meta.href) {
                policyCoverageData.endpointURL = res.items[0].meta.href;
              } else if (res.items[0]) {
                policyCoverageData.endpointURL =
                  res.meta.href + '/' + res.items[0].resourceId;
              }
            } else {
              policyCoverageData.endpointURL =
                'api_does_not_returned_data_to_create_uri';
            }

            resolve(policyCoverageData);
          });
      } else {
        reject(
          'Error Load Policy Coverages, vehicle.additionalCoverages.meta.href Not defined'
        );
      }
    });
  }

  private getDefaultCoveragesValues(
    agencyId: string
  ): Promise<PolicyItemDefaultDataAPIResponse> {
    return new Promise((resolve, reject) => {
      this.subsService
        .getDefaultAutoAdditionalCoverages(agencyId)
        .pipe(take(1))
        .subscribe(
          (res: PolicyItemDefaultDataAPIResponse) => {
            this.defaultCoverageValues = res;
            resolve(res);
          },
          err => reject(err)
        );
    });
  }

  private setDefaultValuesForVehicleCoveragesIfThisIsNewQuoteOrNewVehicleAdded(
    vehicleCoverages: VehicleCoverages,
    isNewQuote: boolean,
    forVehicleTypes: string[] = ['Private Passenger']
  ): Promise<PromiseDataAfterSettingDefaultValuesNew> {
    return new Promise((resolve, reject) => {
      let allowForVehicleType = false;

      if (
        forVehicleTypes.indexOf(vehicleCoverages.vehicle.vehicleType) !== -1 ||
        vehicleCoverages.vehicle.vehicleType === null
      ) {
        allowForVehicleType = true;
      }

      // Set Default values for new vehicles
      const notANewVehicle: boolean =
        this.vehicleResourceIdsAlreadyWithDefaultValues.indexOf(
          vehicleCoverages.vehicleResourceId
        ) !== -1;

       if ((!isNewQuote && notANewVehicle) || !allowForVehicleType) {
         const promiseResponse: PromiseDataAfterSettingDefaultValuesNew = {
           policiesToUpdate: [],
           policyItemsParsed: vehicleCoverages.options,
           vehicleCoverages: vehicleCoverages
         };
         return resolve(promiseResponse);
       }

      const policiesParsedWithDefaultValues = this.optionsService.setDefaultValuesForOptions(
        vehicleCoverages.options,
        this.defaultCoverageValues.items
      );
      const policiesToUpdate = policiesParsedWithDefaultValues.filter(
        policyParsed =>
          this.defaultCoverageValues.items.some(
            item =>
              item.coverageCode === policyParsed.coverageCode &&
              policyParsed.isNewFromAPI
          )
      );

      vehicleCoverages.options = policiesParsedWithDefaultValues;

      const promiseResponse: PromiseDataAfterSettingDefaultValuesNew = {
        policiesToUpdate: policiesToUpdate,
        policyItemsParsed: policiesParsedWithDefaultValues,
        vehicleCoverages: vehicleCoverages
      };
      this.vehicleResourceIdsAlreadyWithDefaultValues.push(
        vehicleCoverages.vehicleResourceId
      ); // Update vehicles list with already default values
      resolve(promiseResponse);
    });
  }

  private saveCoveragesForVehicleToAPI(data: VehicleCoverages): Promise<void> {
    const endpointUrl = data.options[0].endpointUrl;
    const activeOptions: CoverageItemParsed[] = data.options.filter(
      item => item.isActive
    );
    const readyAllPoliciesToUpdate: Coverage[] = this.optionsService.convertPolicyItemParsedArrayToPolicyArray(
      activeOptions
    );

    // Update remote data
    const newCoverageData = {
      coverages: readyAllPoliciesToUpdate
    };

    return this.optionsService
      .updatePoliciesByUri(endpointUrl, newCoverageData)
      .pipe(take(1))
      .toPromise()
      .then(res => console.log('Updated Coverages', res))
      .catch(err => console.log('Update Error: ', err));
  }

  // Error Status and Availability Settings - set specific values for specific Options
  // ---------------------------------------------------------------------------
  private subscribeAutoCoveragesStandardForVehicles(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.subscriptionAutoCoveragesStandardForVehicles = this.storageService
        .getStorageData('autoCoveragesStandardForVehicles')
        .subscribe((res: VehicleCoverages[]) => {
          this.autoCoveragesStandardForVehicles = res;
          this._statusUpdateTrigger.next({ triggerBy: 'StandardCoverages' });
        });
      resolve();
    });
  }

  private subscribeAutoCoveragesAdditionalForVehicles(): Promise<void> {
    return new Promise(resolve => {
      this.subscriptionAutoCoveragesAdditionalForVehicles = this.storageService
        .getStorageData('autoCoveragesAdditionalForVehicles')
        .subscribe((data: VehicleCoverages[]) => {
          this.autoCoveragesAdditionalForVehicles = data;
          this._statusUpdateTrigger.next({ triggerBy: 'AdditionalCoverages' });
        });

      resolve();
    });
  }

  private subscribeForForErrorAndAvailabilityStatus(): Promise<void> {
    let allowUpdating = true; // To prevent infinity loop validation, when storage is updated by this method

    return new Promise(resolve => {
      this.subscriptionStatusUpdateTrigger = this._statusUpdateTrigger
        .asObservable()
        .subscribe((data: TriggerStatusUpdateI) => {
          // console.log('TriggerStatusUpdateI: ', data);

          // Needs to update storage to keep data in sync
          if (allowUpdating) {
            const additionalCoveragesUpdated = this.performAdditionalCoveragesErrorStatusAndAvailabilitySettings();

            allowUpdating = false;
            this.storageService.setStorageData(
              'autoCoveragesAdditionalForVehicles',
              additionalCoveragesUpdated
            );
            allowUpdating = true;
          }
        });
      resolve();
    });
  }

  private performAdditionalCoveragesErrorStatusAndAvailabilitySettings(): VehicleCoverages[] {
    return this.autoCoveragesAdditionalForVehicles.map(
      (item: VehicleCoverages) => {
        const standardCoverages: VehicleCoverages = this.autoCoveragesStandardForVehicles.find(
          (cov: VehicleCoverages) => {
            return cov.vehicleResourceId === item.vehicleResourceId;
          }
        );

        if (standardCoverages) {
          item.options.map(el =>
            this.setPolicyItemErrorStatusAndAvailability(
              el,
              item,
              standardCoverages
            )
          );
        }

        return item;
      }
    );
  }

  private setPolicyItemErrorStatusAndAvailability(
    item: CoverageItemParsed,
    additionalCoverages: VehicleCoverages,
    standardCoverages: VehicleCoverages
  ): CoverageItemParsed {
    switch (item.coverageCode) {
      // https://bostonsoftware.atlassian.net/browse/SPR-2770
      case 'BSC-AUTO-000037':
        const stdCollision: CoverageItemParsed = standardCoverages.options.find(
          (el: CoverageItemParsed) => el.coverageCode === 'COLL'
        );
        item.isDisabled =
          stdCollision && stdCollision.currentValue === 'Omit' ? true : false;
      break;

      case 'BSC-AUTO-000038':
        const nationwideCompDeductible: CoverageItemParsed = additionalCoverages.options.find(
          (el: CoverageItemParsed) => el.coverageCode === 'BSC-AUTO-002417'
        );
        const glassDeduc: CoverageItemParsed = additionalCoverages.options.find(
          (el: CoverageItemParsed) => el.definedCode === 'GLASSDED'
        );
        if (nationwideCompDeductible) {
          if (glassDeduc.isActive === false) {
            nationwideCompDeductible.isActive = false;
            nationwideCompDeductible.isDisabled = true;
          } else {
            nationwideCompDeductible.isDisabled = false;
          }
        }
      break;
    }

    return item;
  }
}
