import { Injectable } from '@angular/core';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
@Injectable()
export class LeadsService {
  constructor(
    // private http: HttpClient,
    // private apiService: ApiService,
    // private storageService: StorageService,
    private apiCommonService: ApiCommonService
  ) {
  }



public getLeads(
  offset: string = '0',
  limit: string = '10',
  name: string = '',
  source: string = '',
  status: string = '',
  startDate: string = '',
  endDate: string = ''
): Observable<any> {

    const params =
    'offset=' +
    offset +
    '&limit=' +
    limit +
    '&name=' +
    name +
    '&source=' +
    source +
    '&status=' +
    status +
    '&startDate=' +
    startDate +
    '&endDate=' +
    endDate;

    return this.apiCommonService.getByUri('/leads?' + params);
}

getLeadsCount() {
  return this.getLeads().pipe(
    map(res => {
      const newQuotes = res.items.filter(lead => lead.viewStatus !== 'Opened');
      return newQuotes.length;
    })
  );
}
public updateLeadStatus(lead?: any): any {
   return this.apiCommonService.putByUri('/leads/' + lead.leadId, lead);
}
public getLeadDetail(leadId?: any): any {
  return this.apiCommonService.getByUri('/leads/' + leadId);
}
public createNewQuoteSession(lead): Observable<any> {
  return this.apiCommonService.getByUri('/quotes/' + lead.leadId + '/lead');
}
public deleteLead(lead?: any): any {
  const data = {
    leadListToDelete: [] = [lead]
  };
  return this.apiCommonService.putByUri('/quotes/leads/', data);
}

public deleteSelectedLeads(leads?: any): any {

  if (!leads) {
    return;
  }

  const data = {
    leadListToDelete: [] = leads
  };

  return this.apiCommonService.putByUri('/quotes/leads/', data);
}

}
