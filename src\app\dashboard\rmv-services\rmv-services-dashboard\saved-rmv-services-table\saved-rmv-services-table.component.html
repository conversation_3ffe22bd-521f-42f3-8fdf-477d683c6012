<div class="section">
  <div class="row">
    <div class="col-xs-12">
      <div class="u-flex u-flex--to-middle">
        <div class="search">
          <input #searchInput [id]="'filter_NAME'" [(ngModel)]="searchQuery" (keydown.enter)="filterByName($event)"
            class="search__input search__input--md" type="text" placeholder="Search By {{searchType | titlecase}}"
            changeDetectionDelay />
          <button *ngIf="searchQuery && searchQuery.length > 0" (click)="searchQuery = '';resetSearch()" name="cancel"
            class="reset__button"></button>
          <button (click)="filterByName($event)" name="searchbtn" class="search__button"></button>
        </div>

        <div>
          <label class="o-checkable u-spacing--left-2" [appDetectSystem] [removeRadio]="true">
            <input type="radio" name="searchType" value="vin" [(ngModel)]="searchType">
            <i class="o-btn o-btn--radio"></i>
            <span>VIN</span>
          </label>
          <label class="o-checkable u-spacing--left-2" [appDetectSystem] [removeRadio]="true">
            <input type="radio" name="searchType" value="plate" [(ngModel)]="searchType">
            <i class="o-btn o-btn--radio"></i>
            <span>Plate</span>
          </label>
          <label class="o-checkable u-spacing--left-2" [appDetectSystem] [removeRadio]="true">
            <input type="radio" name="searchType" value="lastname" [(ngModel)]="searchType">
            <i class="o-btn o-btn--radio"></i>
            <span>Last Name</span>
          </label>
          <label class="o-checkable u-spacing--left-2" [appDetectSystem] [removeRadio]="true">
            <input type="radio" name="searchType" value="businessname" [(ngModel)]="searchType">
            <i class="o-btn o-btn--radio"></i>
            <span>Business Name</span>
          </label>
        </div>
      </div>
    </div>
  </div>
  <span style="color: red" *ngIf="searchQuery && searchQuery.length < 3">Minimum search length is 3</span>
</div>
<section class="section">
  <div class="row">
    <div class="u-float-right">
      <div class="trash trash--select u-float-left">
        <span class="u-t-upper u-color-slate-grey">
          <label class="o-checkable">
            <input type="checkbox" class="o-btn--checkbox u-show-inline" (click)="toggleAll()"
              [checked]="isChecked()" />
            <i class="
                tooltip__menu-icon tooltip__menu-icon_inner
                o-btn o-btn--checkbox
              "></i>
            Select All
          </label>
        </span>
      </div>
      <div class="trash trash--img u-float-right">
        <a class="u-t-upper u-color-slate-grey" (click)="deleteModalbox.open()">Delete</a>
      </div>
    </div>
    <app-modalbox #deleteModalbox>
      <h2 class="u-t-size--1-5rem u-color-sunset u-align-left">
        Delete Transaction?
      </h2>
      <p class="u-spacing--1-5 u-spacing--bottom-2 u-align-left">
        Are you sure you want to delete these Requests?
      </p>

      <hr />

      <div class="row u-spacing--2">
        <div class="col-xs-12 u-align-right">
          <button (click)="deleteSelectedRequest()" class="o-btn u-spacing--right-1">
            Yes, Delete Requests
          </button>
          <button (click)="deleteModalbox.close()" class="o-btn o-btn--idle">
            Cancel
          </button>
        </div>
      </div>
    </app-modalbox>
  </div>
</section>

<section class="section">

  <div class="col-xs-14" *ngIf="!IsDatesFilterViewEnabled()">Showing Last 7 Days</div>

  <div class="col-xs-12" *ngIf="!showHideDiv()">

    <app-filter-dates (getByFilter)="getTransactionsFromFilteredDates()"
      (getByFilter)="getTransactionsFromFilteredDates()"></app-filter-dates>

  </div>
</section>

<section class="section">
  <div class="row">
    <div class="col-xs-12">
      <table class="table table--compact table--hoverable-grey">
        <thead class="table__thead">
          <tr class="">
            <th class="table__th u-width-45px"></th>
            <th class="table__th">Owner</th>
            <th class="table__th">Agent Name</th>
            <th class="table__th">Transaction Type</th>
            <th class="table__th">Vin/Plate</th>
            <th class="table__th">Last Modified</th>
          </tr>
        </thead>
        <tbody class="table__tbody" *ngIf="list">
          <tr class="table__tr" *ngFor="let row of list">
            <td class="table__td">
              <label class="o-checkable u-float-right">
                <input type="checkbox" class="o-btn--checkbox" [checked]="isSelected(row.rmvServicesId)"
                  (click)="toggleSelected(row.rmvServicesId)" />
                <i class="o-btn o-btn--checkbox"></i>
              </label>
            </td>
            <td class="table__td">
              <a [routerLink]="['/dashboard/rmv-services/rta-prefill']" [queryParams]="{
                  id: row.rmvServicesId,
                  type: row.transactionType
                }" routerLinkActive="router-link-active">{{ row.ownerFirstName || 'No Owner' }} {{ row.ownerLastName ||
                '' }}</a>
            </td>
            <td class="table__td">
              {{ row.userFirstName }} {{ row.userLastName }}
            </td>
            <td class="table__td">{{ getDisplayName(row.transactionType) }}</td>
            <td class="table__td">
              <span *ngIf="row.vin; else plate">{{ row.vin }}</span>
              <ng-template #plate>
                <span>{{ row.plate }}</span>
              </ng-template>
            </td>
            <td class="table__td">
              {{ row.lastModifiedDate | date }}
            </td>
          </tr>
        </tbody>
        <tbody class="table__tbody" *ngIf="!list?.length">
          <tr class="table__tr">
            <td colspan="5">
              <p class="u-padd--bottom-1 u-padd--1">
                There are no results that match your search.
              </p>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</section>

<section class="section">
  <div class="u-flex u-flex--spread u-flex--to-middle">
    <div class="">
      <app-pagination [currentPage]="paginationCurrentPage" [totalRecords]="size" [recordsLimit]="limit"
        (onPageChange)="paginationPageChange($event)"></app-pagination>
    </div>
    <div class="">
      <app-results-limiter (onChange)="onResultLimitChange($event)"
        [description]="'Transactions to show'"></app-results-limiter>
    </div>
  </div>
  <div style="padding-top: 20px">
    <button class="o-btn" [routerLink]="'/dashboard/rmv-services'">
      RETURN TO RMV DASHBOARD
    </button>
  </div>
</section>