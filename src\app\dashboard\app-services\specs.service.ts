
import {map} from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { ApiService } from 'app/shared/services/api.service';
import { Observable } from 'rxjs';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { TownsListAPIResponse } from 'app/app-model/specs';
import { FireDistrictListAPIResponse } from 'app/app-model/specs';
import { FilterOption } from 'app/app-model/filter-option';
import { rest } from 'underscore';

const SPECS_URL = '/specs/';

@Injectable()
export class SpecsService {

  constructor(
    private apiService: ApiService,
    private apiCommonService: ApiCommonService
  ) { }

  public getIncidentsSpecsTypes(): Observable<any> {
    return this.apiCommonService.getByUri(SPECS_URL + 'driverIncidents/types');
  }

  // options and coverages
  public getRatingCoverages(state: string, lob: string, coverageGroups: string = '', ratingPlanIds: string = '', formType: string = '', quoteEffectiveDate: string = '', viewCategory = ''): Observable<any> {
    if (state !== 'MA' && state !== 'RI') { state = 'MA'; }
    let url = SPECS_URL + 'ratingCoverages/state/' + state + '/lob/' + lob + '?coverageGroups=' + coverageGroups + '&ratingPlans=' + ratingPlanIds;

    if (formType) {
      url += '&formType=' + formType;
    }

    if (quoteEffectiveDate) {
      url += '&effectiveDate=' + quoteEffectiveDate;
    }

    if (viewCategory) {
      url += '&viewCategory=' + viewCategory;
    }

    return this.apiCommonService.getByUri(url);
  }

  // List Of Towns
  // public getTownsList(state:string, lob:string=''):Observable<TownsListAPIResponse> {
  //   let uri = SPECS_URL + 'ratingStates/' + state + '/towns?lobs=' + lob;

  //   return this.apiCommonService.getByUri(uri);
  // }


  public getTownsList(state: string, lob: string= '', useCache: boolean = false): Observable<TownsListAPIResponse> {
    if (state !== 'MA' && state !== 'RI') { state = 'MA'; }
    const uri = SPECS_URL + 'ratingStates/' + state + '/towns?lobs=' + lob;

    return this.apiCommonService.getByUri(uri, {}, true).pipe(map(res => {
      return res;
    }));
  }

  public getFireDistrictList(state: string, lob: string= '', useCache: boolean = false): Observable<FireDistrictListAPIResponse> {
    const uri = SPECS_URL + 'ratingStates/' + state + '/firedistrict?lobs=' + lob;

    return this.apiCommonService.getByUri(uri, {}, true).pipe(map(res => {
      return res;
    }));
  }


  public getExportOptions(lob: string = 'autop') {
    const uri = SPECS_URL + 'integrationVendors?lob=' + lob + '&direction=export';

    return this.apiCommonService.getByUri(uri);
  }

  public getLossHistoryRequirements(ratingPlansIds: string = '') {
    const uri = '/specs/lossHistoryRequirements?ratingPlans=' + ratingPlansIds;

    return this.apiCommonService.getByUri(uri);
  }

  public getRatingPlans(state: string, lob: string, maipArcIds: string): Observable<any> {
    const url = SPECS_URL + 'ratingPlans/' + state + '/' + lob + '/' + maipArcIds;

    return this.apiCommonService.getByUri(url);
  }

  public getBodyTypes(lob: string): Observable<any> {
    const url = SPECS_URL + 'vehicleDetails/' + lob + '/bodyTypes'  ;
    return this.apiCommonService.getByUri(url);
  }

  public getPlateTypes(lob: string): Observable<any> {
    const url = SPECS_URL + 'vehicleDetails/' + lob + '/plateTypes'  ;
    return this.apiCommonService.getByUri(url);
  }

  public getVehicleUseTypes(lob: string): Observable<any> {
    const url = SPECS_URL + 'vehicleDetails/' + lob + '/usageTypes'  ;
    return this.apiCommonService.getByUri(url);
  }

  public getVehicleSecondaryTypes(lob: string): Observable<any> {
    const url = SPECS_URL + 'vehicleDetails/' + lob + '/secondaryTypes'  ;
    return this.apiCommonService.getByUri(url);
  }

  public getTransactionTypes() {
    const url = SPECS_URL + 'rmvservices/transactions';
    return this.apiCommonService.getByUri(url);
  }
  public getTransactionsAsOptions(): Observable<FilterOption[]> {
    return this.getTransactionTypes().pipe(map(res => {
      let options = [];
      options = res.items.map(item => ({id: item.type, text: item.desc}));
      return options;
    }));
  }

  public getOwnerships() {
    const url = SPECS_URL + 'rmvservices/ownerships';
    return this.apiCommonService.getByUri(url);
  }
  public getOwnershipsAsOptions(): Observable<FilterOption[]> {
    return this.getOwnerships().pipe(map(res => {
      let options = [];
      options = res.items.map(item => ({id: item.type, text: item.desc}));
      return options;
    }));
  }

  public getCities() {
    const url = SPECS_URL + 'rmvservices/cities';
    return this.apiCommonService.getByUri(url);
  }
  public getCitiesAsOptions(): Observable<FilterOption[]> {
    return this.getCities().pipe(map(res => {
      let options = [];
      options = res.items.map(item => ({id: item.type, text: item.desc}));
      return options;
    }));
  }

  getVehicleTitleTypes() {
    const url = SPECS_URL + 'rmvservices/vehicleTitleTypes';
    return this.apiCommonService.getByUri(url);
  }

  public getTitleTypesAsOptions(): Observable<FilterOption[]> {
    return this.getVehicleTitleTypes().pipe(map(res => {
      let options = [];
      options = res.items.map(item => ({id: item.type, text: item.desc}));
      return options;
    }));
  }

  getVehicleMakes() {
    const url = SPECS_URL + 'rmvservices/makes';
    return this.apiCommonService.getByUri(url);
  }

  public getMakesAsOptions(): Observable<FilterOption[]> {
    return this.getVehicleMakes().pipe(map(res => {
      let options = [];
      options = res.items.map(item => ({id: item.type, text: item.desc}));
      return options;
    }));
  }

  getVehicleBrandTypes() {
    const url = SPECS_URL + 'rmvservices/vehicleBrandTypes';
    return this.apiCommonService.getByUri(url);
  }

  public getCoverageViewCategories(lob) {
    const url = `${SPECS_URL}coverageViewCategories/${lob}`;
    return this.apiCommonService.getByUri(url);
  }

  public getCoverageViewCatergoriesAsOptions(lob) {

    return this.getCoverageViewCategories(lob).pipe(map( res => {
      const options = [];
    options.push({id: 'All', text: 'All Coverages'});
    res.forEach(item => {
      options.push({id: item.value, text: item.name});
    });
      return options;


    }));
  }
}


