import { Injectable } from '@angular/core';
import { Router, RouterStateSnapshot, ActivatedRouteSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';
import { RmvService } from '../app-services/rmv.service';

@Injectable({
  providedIn: 'root'
})
export class DocumentDestructionDetailsResolver  {
  constructor(private rmvService: RmvService) {}
  resolve(): Observable<boolean> {
    return this.rmvService.GetDocumentDestructionDetails();
  }
}
