
import {take} from 'rxjs/operators';
import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { StorageService } from 'app/shared/services/storage-new.service';
import { DwellingService } from 'app/dashboard/app-services/dwelling.service';
import { Coverage, CoverageItem, CoverageValue, CoveragesData, CoverageApiResponseData, PolicyItemDefaultDataAPIResponse } from 'app/app-model/coverage';
import { CoveragesService } from 'app/dashboard/app-services/coverages.service';
import { FilterOption } from 'app/app-model/filter-option';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { ClientsService } from 'app/dashboard/app-services/clients.service';
import { ClientDetails } from 'app/app-model/client';
import { MoneyService } from 'app/shared/services/money.service';
import createNumberMask from 'text-mask-addons/dist/createNumberMask';
import { SubscriptionLike as ISubscription } from 'rxjs';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { Quote, QuotePlanListAPIResponse, QuotePlan } from '../../../../app-model/quote';
import { ModalboxComponent } from 'app/shared/components/modalbox/modalbox.component';
import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { Validate } from 'app/hints-and-warnings/validators';



import { differenceInYears, format, parseISO } from 'date-fns';

const EXCLUDED = 'Excluded';

interface IModalLouMessages {
  setDwellingValue: string;
  percentageWrong: string;
  // minimalValue: string;
  minimalValueOfDwelling: string;
  minimalValueOfPersonalPropertyCondo: string;
  minimalValueOfPersonalPropertyTenant: string;
  setPersonalPropertyValue: string;
}

const MODAL_LOU_MESSAGES: IModalLouMessages = {
  setDwellingValue: 'Please first set correct value for Dwelling field.',
  percentageWrong: 'Loss of Use coverage limit cannot be reduced.',
  minimalValueOfDwelling: 'Loss of Use needs to be at least 20% of Dwelling value',
  minimalValueOfPersonalPropertyCondo: 'Loss of Use needs to be at least 40% of Personal Property value',
  minimalValueOfPersonalPropertyTenant: 'Loss of Use needs to be at least 20% of Personal Property value',
  setPersonalPropertyValue: 'Please first set correct value for Personal Property field.'
}

@Component({
    selector: 'app-coverage-limits-insured-discounts',
    templateUrl: './coverage-limits-insured-discounts.component.html',
    styleUrls: ['./coverage-limits-insured-discounts.component.scss'],
    standalone: false
})
export class CoverageLimitsInsuredDiscountsComponent implements OnInit {

  constructor(
    private storageService: StorageService,
    private dwellingService: DwellingService,
    private coveragesService: CoveragesService,
    private overlayLoaderService: OverlayLoaderService,
    private clientsService: ClientsService,
    public moneyService: MoneyService,
    private apiCommonService: ApiCommonService,
    private quotesService: QuotesService
  ) { }

  public get formType() {
    return this.quote.formType.replace(' ', '');
  }
  private subscriptionQuotePlans: ISubscription;

  private coveragesInitialized = false;
  public coverages: CoverageItem[] = [];

  public coverageDwelling:CoverageItem = new CoverageItem();
  public coverageOtherStructures:CoverageItem = new CoverageItem();
  public coveragePersonalProperty:CoverageItem = new CoverageItem();
  public coveragePersonalLiability:CoverageItem = new CoverageItem();
  public coverageMedicalPayments:CoverageItem = new CoverageItem();
  public coverageDwellingReplacementCost:CoverageItem = new CoverageItem();
  public coverageContentsReplacementCost:CoverageItem = new CoverageItem();
  public coverageAllPerils:CoverageItem = new CoverageItem();
  public coverageWindHail:CoverageItem = new CoverageItem();
  public coverageLossOfUse:CoverageItem = new CoverageItem();
  public coverageNonSmoke:CoverageItem = new CoverageItem();

  public coverageQuoteDwelling = '';
  public coverageQuoteOtherStructures = '';
  public coverageQuotePersonalProperty = '';
  public coverageQuoteLossOfUse = '';
  public coverageQuoteLossOfUseReset = false;

  public coverageQuotePersonalLiability: string|any = ''; // THE Type should be precised!!!
  public coverageQuoteMedicalPayments:string|any = ''; // THE Type should be precised!!!
  public coverageQuoteDwellingReplacementCost = '';
  public coverageQuoteContentsReplacementCost = '';
  // public coverageQuoteAllPerils: string = '';
  // public coverageQuoteWindHail: string = '';
  public coverageQuoteAllPerils: FilterOption = null;
  public coverageQuoteWindHail: FilterOption = null;
  public coverageQuoteNonSmoke = '';
  public coverageQuoteSpecialCoverage = '';

  public coveragePersonalLiabilityOptions: any[] = [];
  public coverageMedicalPaymentsOptions: any[] = [];
  public coverageDwellingReplacementCostOptions: any[] = [];
  public coverageAllPerilsOptions: any[] = [];
  public coverageWindHailOptions: any[] = [];

  private subscribtionGetCoverages;
  private subscriptionQuote;
  private subscriptionQuoteCoverages;
  private subscriptionDwellingData;
  public quote = new Quote();
  private quoteTypes: string[] = [];
  private quoteCoverages;
  private quoteCoveragesItems;
  private selectedPlans: QuotePlan[] = [];
  private selectedPlansIds: string[] = [];

  // insured discounts
  private subscriptionGetClients;
  public insurerIsSmoker = false;
  public insurerDOB: string = null;
  public insurerAge: number = null;

  private clientId: string;

  private clientDetails: ClientDetails =  new ClientDetails();
  private dwelling;
  public lockMEDPM = false;

  @ViewChild('refDwelling') refDwelling: ElementRef;
  @ViewChild('modalDefaultPercentages') modalDefaultPercentages;
  @ViewChild('refModalLossOfUse') refModalLossOfUse: ModalboxComponent;
  @ViewChild('refPersonalProperty') refPersonalProperty: ElementRef;

  public moneyMask = createNumberMask();

  public coveragesNotDefaultPercentages = [];
  public defaultCoverageValue = {};
  private quoteCoveragesItemsTmp;
  private updatingCoverageCode = {
    coverageCode: '',
    value: ''
  };

  private dwellFieldsToUpdate = ['DWELL', 'OS', 'PP', 'LOU'];

  private dropdownCoverages = ['PL', 'MEDPM', 'RCDWELL', 'APDED', 'WHDED'];

  // https://bostonsoftware.atlassian.net/browse/SPR-2083
  // ----------------------------------------------------------------------------

  // SPR-2083 - Loss of Use

  private lossOfUsePreviousValue = '';
  private lossOfUseAdjustedValue = '';
  public lossOfUseModalMessage = '';

  // HELPER - textMask Input change event issue workaround
  // ----------------------------------------------------------------------------
  private helperTextMaskChangeEventIssueWorkaroundInputValueDuringFocus = '';

  ngOnInit() {
    this.subscribeQuoteSelectedPlans();
    this.getCoverages();
    this.getQuote();
    this.getQuoteCoverages();
    this.getClientData();
    this.getQuoteDwelling();
  }

  ngOnDestroy() {
    this.subscribtionGetCoverages && this.subscribtionGetCoverages.unsubscribe();
    this.subscriptionQuote && this.subscriptionQuote.unsubscribe();
    this.subscriptionGetClients && this.subscriptionGetClients.unsubscribe();
    this.subscriptionQuoteCoverages && this.subscriptionQuoteCoverages.unsubscribe();
    this.subscriptionDwellingData && this.subscriptionDwellingData.unsubscribe();
    this.subscriptionQuotePlans && this.subscriptionQuotePlans.unsubscribe();
  }

  private getQuote() {
    this.subscriptionQuote = this.storageService.getStorageData('selectedQuote').subscribe( data => {
      if (data) {
        this.quote = JSON.parse(JSON.stringify(data));
        this.quoteTypes = this.quotesService.simplifyFormType(this.quote.formType);
      }
    });
  }

  private subscribeQuoteSelectedPlans(): void {
    this.subscriptionQuotePlans = this.storageService.getStorageData('selectedPlan')
      .subscribe((res: QuotePlanListAPIResponse) => {
        if (res && res.items && res.items.length) {
          this.selectedPlans = JSON.parse(JSON.stringify(res.items[0].items));
          this.selectedPlansIds = this.selectedPlans.map(plan => plan.ratingPlanId);
        }
      });
  }


  private getQuoteCoverages() {
    this.subscriptionQuoteCoverages = this.storageService.getStorageData('homeQuoteCoverages').subscribe( data => {
      if (data && data.meta && data.meta.href) {
        if (data['coverages'] && data['coverages'].length) {
          this.quoteCoverages = {items: [JSON.parse(JSON.stringify(data))]};
        } else {
          this.quoteCoverages = JSON.parse(JSON.stringify(data));
        }
        if (this.quoteCoverages && this.quoteCoverages.items && this.quoteCoverages.items.length) {
          this.quoteCoveragesItems = this.quoteCoverages.items[0];
          this.assignQuoteCoverages();
          if (this.quoteCoveragesItems.coverages) {
            this.checkPLvalue(this.quoteCoveragesItems.coverages);
          }
        } else {
          if (this.quote && this.quote.coveragesStandard && this.quote.coveragesStandard.href) {
            this.overlayLoaderService.showLoader();

            this.coveragesService.createHomeCoverageByUriWithDefaultValues(this.quote.coveragesStandard.href).pipe(
              take(1))
              .subscribe(
                (res: CoveragesData) => {
                  this.quoteCoverages.items = [res];
                  // Save to storage
                  const tmpRes = new CoverageApiResponseData();
                  tmpRes.items = [res];
                  this.storageService.setStorageData('homeQuoteCoverages', tmpRes);
                  this.overlayLoaderService.hideLoader();
                },
                err => {
                  console.log(err);
                  this.overlayLoaderService.hideLoader();
                });
          }
        }
      } else {
        if (this.quote && this.quote.coveragesStandard && this.quote.coveragesStandard.href) {
          this.coveragesService.getHomeQuoteCoverages$(this.quote.coveragesStandard.href).pipe(take(1)).subscribe();
        }
      }
    });
  }

  private checkPLvalue(coverages) {
    if (coverages && coverages.length) {
      let plCoverage = coverages.find(cov => cov.coverageCode === 'PL');
      if (plCoverage && plCoverage.values && plCoverage.values[0] && plCoverage.values[0].value && plCoverage.values[0].value === EXCLUDED) {
        let medpmCoverage = coverages.find(cov => cov.coverageCode === 'MEDPM');
        if (medpmCoverage && medpmCoverage.values && medpmCoverage.values[0] && medpmCoverage.values[0].value) {
          if (medpmCoverage.values[0].value !== EXCLUDED) {
            this.updateCoverageAPI('MEDPM', EXCLUDED);
          }
        } else {
          this.updateCoverageAPI('MEDPM', EXCLUDED);
        }
        this.lockMEDPM = true;
      } else {
        this.lockMEDPM = false;
      }
    }
  }

  private getQuoteDwelling() {
    this.subscriptionDwellingData = this.storageService.getStorageData('dwelling').subscribe(data => {
      if (data) {
        this.dwelling = JSON.parse(JSON.stringify(data));
        let hasExcludedPL = this.coveragePersonalLiabilityOptions.find( item => item.id === EXCLUDED);
        let hasExcludedMEDPM = this.coverageMedicalPaymentsOptions.find( item => item.id === EXCLUDED);
        if (this.dwelling && this.dwelling.utilizationTypeCode !== 'Primary') {
          if (!hasExcludedPL || hasExcludedPL === undefined) {
            this.coveragePersonalLiabilityOptions = [{id: EXCLUDED, text: EXCLUDED}, ...this.coverageMedicalPaymentsOptions];
          }
          if (!hasExcludedMEDPM || hasExcludedMEDPM === undefined) {
            this.coverageMedicalPaymentsOptions = [{id: EXCLUDED, text: EXCLUDED}, ...this.coverageMedicalPaymentsOptions];
          }
        } else {
          if (hasExcludedPL) {
            this.coveragePersonalLiabilityOptions = this.removeOption(this.coveragePersonalLiabilityOptions, EXCLUDED);
          }
          if (hasExcludedMEDPM) {
            this.coverageMedicalPaymentsOptions = this.removeOption(this.coverageMedicalPaymentsOptions, EXCLUDED);
          }
        }
      }
    });
  }

  public updateQuoteCoverage(coverageCode, value) {
    this.coveragesNotDefaultPercentages = [];
    this.defaultCoverageValue = {};
    if (value.indexOf('$') > -1) {
      value = value.replace('$', '').replace(/,/g, '');
    }
    if (coverageCode === 'DWELL' && this.quote.policyType === 'Homeowner') {
      let dwell = this.quoteCoveragesItems.coverages.find( item => {
        return item.coverageCode === 'DWELL';
      });
      if (dwell && dwell.values && dwell.values.length) {
        this.defaultCoverageValue['DWELL'] = dwell.values[0].value;
        this.defaultCoverageValue['OS'] = Math.round(+dwell.values[0].value / 10).toString();
        this.defaultCoverageValue['PP'] = Math.round(+dwell.values[0].value / 2).toString();
        this.defaultCoverageValue['LOU'] = Math.round(+dwell.values[0].value / 5).toString();
      }
      let updatedItemIndex;
      this.quoteCoveragesItems.coverages.forEach( (item, index) => {
        if (this.defaultCoverageValue[item.coverageCode] && item.values[0].value && item.values[0].value !== this.defaultCoverageValue[item.coverageCode] && value && value !== 0) {
          switch (item.coverageCode) {
            case 'DWELL':
              item.description = 'Dwelling';
              item.defaultValue = value.toString();
              break;
            case 'OS':
              item.description = 'Other Structures';
              item.defaultValue = Math.round(+value / 10).toString();
              break;
            case 'PP':
              item.description = 'Personal Property';
              item.defaultValue = Math.round(+value / 2).toString();
              break;
            case 'LOU':
              item.description = 'Loss of Use';
              item.defaultValue = Math.ceil(+value / 5).toString();
              break;
          }
          this.coveragesNotDefaultPercentages.push(item);
        }
        if (item.coverageCode === coverageCode) {
          updatedItemIndex = index;
        }
      });
      if (this.coveragesNotDefaultPercentages.length) {
        this.modalDefaultPercentages.openModalbox();
        this.quoteCoveragesItemsTmp = JSON.parse(JSON.stringify(this.quoteCoveragesItems));
        this.quoteCoveragesItemsTmp.coverages[updatedItemIndex].values[0].value = value;
        this.updatingCoverageCode.coverageCode = coverageCode;
        this.updatingCoverageCode.value = value;
      } else {
        this.updateCoverageAPI(coverageCode, value);
      }
    } else if (coverageCode === 'PP' && (this.quote.policyType === 'Condo' || this.quote.policyType === 'Tenant')) {
      let times = 0.4;
      if (this.quote.policyType === 'Tenant') {
        times = 0.2;
      }
      let pp = this.quoteCoveragesItems.coverages.find( item => {
        return item.coverageCode === 'PP';
      });
      if (pp && pp.values && pp.values.length) {
        this.defaultCoverageValue['PP'] = pp.values[0].value;
        this.defaultCoverageValue['LOU'] = Math.ceil(+pp.values[0].value * times).toString();
      }
      let updatedItemIndex;
      this.quoteCoveragesItems.coverages.forEach( (item, index) => {
        if (this.defaultCoverageValue[item.coverageCode] && item.values[0].value !== this.defaultCoverageValue[item.coverageCode] && value && value !== 0 && item.values[0].value !== '') {
          switch (item.coverageCode) {
            case 'PP':
              item.description = 'Personal Property';
              item.defaultValue = value;
              break;
            case 'LOU':
              item.description = 'Loss of Use';
              item.defaultValue = Math.ceil(+value * times).toString();
              break;
          }
          this.coveragesNotDefaultPercentages.push(item);
        }
        if (item.coverageCode === coverageCode) {
          updatedItemIndex = index;
        }
      });
      if (this.coveragesNotDefaultPercentages.length) {
        this.modalDefaultPercentages.openModalbox();
        this.quoteCoveragesItemsTmp = JSON.parse(JSON.stringify(this.quoteCoveragesItems));
        this.quoteCoveragesItemsTmp.coverages[updatedItemIndex].values[0].value = value;
        this.updatingCoverageCode.coverageCode = coverageCode;
        this.updatingCoverageCode.value = value;
      } else {
        this.updateCoverageAPI(coverageCode, value);
      }
    } else {
      this.updateCoverageAPI(coverageCode, value);
    }
    if (coverageCode === 'PL') {
      if (value === EXCLUDED) {
        this.lockMEDPM = true;
        let hasExcluded = this.coverageMedicalPaymentsOptions.find( item => item.id === EXCLUDED);
        if (!hasExcluded || undefined) {
          this.coverageMedicalPaymentsOptions = [{id: EXCLUDED, text: EXCLUDED}, ...this.coverageMedicalPaymentsOptions];
        }
        this.updateCoverageAPI('MEDPM', EXCLUDED);
      } else {
        this.lockMEDPM = false;
        setTimeout( () => {
          let medpm = this.quoteCoveragesItems.coverages.find( item => item.coverageCode === 'MEDPM' );
          if (medpm && medpm.values && medpm.values.length && medpm.values[0] && medpm.values[0].value === EXCLUDED) {
            this.updateCoverageAPI('MEDPM', '1000');
            if (this.coverageMedicalPaymentsOptions.length && this.coverageMedicalPaymentsOptions[0].id === EXCLUDED) {
              this.coverageMedicalPaymentsOptions = this.coverageMedicalPaymentsOptions.splice(1, this.coverageMedicalPaymentsOptions.length - 1);
            }
          }
        }, 100);
      }
    }
  }

  private updateCoverageAPI(coverageCode, value) {
    if (this.quoteCoveragesItems && this.quoteCoveragesItems.coverages && this.quoteCoveragesItems.coverages.length) {
      let inCoverages = false;
      this.quoteCoveragesItems.coverages = this.quoteCoveragesItems.coverages.map( (item) => {
        if (item.coverageCode === coverageCode) {
          let val = new CoverageValue();
          val.value = value;
          item.values = [val];
          inCoverages = true;
        }
        return item;
      });
      if (!inCoverages) {
        const newCoverage = new Coverage();
        newCoverage.values.push(new CoverageValue());
        newCoverage.coverageCode = coverageCode;
        newCoverage.values[0].value = value;
        this.quoteCoveragesItems.coverages.push(newCoverage);
      }
    } else {
      let newCoverage = new Coverage();
      newCoverage.values.push(new CoverageValue());
      newCoverage.coverageCode = coverageCode;
      newCoverage.values[0].value = value;
      this.quoteCoveragesItems.coverages.push(newCoverage);
    }
    this.coverageDwellingUpdate(coverageCode, value);
    this.updateCoveragesAPI(this.quoteCoveragesItems);
  }

  private updateCoveragesAPI(coveragesObj) {
    if (coveragesObj && coveragesObj.meta && coveragesObj.meta.href) {
      this.overlayLoaderService.showLoader();
      this.apiCommonService.putByUri(coveragesObj.meta.href, coveragesObj).pipe(take(1)).subscribe( res => {
        this.storageService.setStorageData('homeQuoteCoverages', res);
        this.assignQuoteCoverages();
        this.overlayLoaderService.hideLoader();
        this.modalDefaultPercentages.closeModalbox();
      }, err => this.overlayLoaderService.hideLoader());
    }
  }

  public decideOnDefaultCoverages(type: string): void {
    switch (type) {
      case 'leave':
        this.updateCoveragesAPI(this.quoteCoveragesItemsTmp);
        break;
      case 'all':
        if (this.quote.policyType === 'Homeowner') {
          this.quoteCoveragesItemsTmp.coverages.forEach( item => {
            if (this.updatingCoverageCode.coverageCode === 'DWELL') {                                 // 10
              if (item.coverageCode === 'OS') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value / 10).toString();  // 1
              }
              if (item.coverageCode === 'PP') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value / 2).toString();   // 5
              }
              if (item.coverageCode === 'LOU') {
                item.values[0].value = Math.ceil(+this.updatingCoverageCode.value / 5).toString();   // 2
              }
            }
            if (this.updatingCoverageCode.coverageCode === 'OS') {
              if (item.coverageCode === 'DWELL') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value * 10).toString();
              }
              if (item.coverageCode === 'PP') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value * 5).toString();
              }
              if (item.coverageCode === 'LOU') {
                item.values[0].value = Math.ceil(+this.updatingCoverageCode.value * 2).toString();
              }
            }
            if (this.updatingCoverageCode.coverageCode === 'PP') {
              if (item.coverageCode === 'DWELL') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value * 2).toString();
              }
              if (item.coverageCode === 'OS') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value / 5).toString();
              }
              if (item.coverageCode === 'LOU') {
                item.values[0].value = Math.ceil((+this.updatingCoverageCode.value / 5) * 2).toString();
              }
            }
            if (this.updatingCoverageCode.coverageCode === 'LOU') {
              if (item.coverageCode === 'DWELL') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value * 5).toString();
              }
              if (item.coverageCode === 'OS') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value / 2).toString();
              }
              if (item.coverageCode === 'PP') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value * 2.5).toString();
              }
            }
          });
        }
        if (this.quote.policyType === 'Condo' || this.quote.policyType === 'Tenant') {
          let times = 0.4;
          if (this.quote.policyType === 'Tenant') {
            times = 0.2;
          }
          this.quoteCoveragesItemsTmp.coverages.forEach( item => {
            if (this.updatingCoverageCode.coverageCode === 'PP') {
              if (item.coverageCode === 'LOU') {
                item.values[0].value = Math.ceil(+this.updatingCoverageCode.value * times).toString();
              }
            }
          });
        }
        this.updateCoveragesAPI(this.quoteCoveragesItemsTmp);
        break;
      case 'unmodified':
        this.quoteCoveragesItemsTmp.coverages.forEach( item => {
          let inNotDefault = false;
          this.coveragesNotDefaultPercentages.forEach( defaultItem => {
            if (defaultItem.coverageCode === item.coverageCode) {
              inNotDefault = true;
            }
          });
          if (!inNotDefault) {
            if (this.updatingCoverageCode.coverageCode === 'DWELL') {                                 // 10
              if (item.coverageCode === 'OS') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value / 10).toString();  // 1
              }
              if (item.coverageCode === 'PP') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value / 2).toString();   // 5
              }
              if (item.coverageCode === 'LOU') {
                item.values[0].value = Math.ceil(+this.updatingCoverageCode.value / 5).toString();   // 2
              }
            }
            if (this.updatingCoverageCode.coverageCode === 'OS') {
              if (item.coverageCode === 'DWELL') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value * 10).toString();
              }
              if (item.coverageCode === 'PP') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value * 5).toString();
              }
              if (item.coverageCode === 'LOU') {
                item.values[0].value = Math.ceil(+this.updatingCoverageCode.value * 2).toString();
              }
            }
            if (this.updatingCoverageCode.coverageCode === 'PP') {
              if (item.coverageCode === 'DWELL') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value * 2).toString();
              }
              if (item.coverageCode === 'OS') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value / 5).toString();
              }
              if (item.coverageCode === 'LOU') {
                item.values[0].value = Math.ceil((+this.updatingCoverageCode.value / 5) * 2).toString();
              }
            }
            if (this.updatingCoverageCode.coverageCode === 'LOU') {
              if (item.coverageCode === 'DWELL') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value * 5).toString();
              }
              if (item.coverageCode === 'OS') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value / 2).toString();
              }
              if (item.coverageCode === 'PP') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value * 2.5).toString();
              }
            }
          }
        });
        this.updateCoveragesAPI(this.quoteCoveragesItemsTmp);
        break;
    }
  }
  private coverageDwellingUpdate(coverageCode, dwellValue) {
    if (coverageCode === 'DWELL' && this.quote.policyType === 'Homeowner') {
      this.quoteCoveragesItems.coverages = this.quoteCoveragesItems.coverages.map( (item) => {
        if (item.coverageCode === 'OS') {
          item.values = [{value: Math.round(+dwellValue / 10).toString()}];
        }
        if (item.coverageCode === 'PP') {
          item.values = [{value: Math.round(+dwellValue / 2).toString()}];
        }
        if (item.coverageCode === 'LOU') {
          item.values = [{value: Math.ceil(+dwellValue / 5).toString()}];
        }
        return item;
      });
      if (this.quoteCoveragesItems.coverages.length) {
        let missingCoverages = ['DWELL', 'OS', 'PP', 'LOU'];
        this.dwellFieldsToUpdate.forEach( (dftu) => {
          this.quoteCoveragesItems.coverages.forEach( (item) => {
            const coverageInd = missingCoverages.indexOf(item.coverageCode);
            if (coverageInd > -1) {
              missingCoverages.splice(coverageInd, 1);
            }
          });
        });
        missingCoverages.forEach( item => {
          const newCoverage = new Coverage();
          newCoverage.values.push(new CoverageValue());
          newCoverage.coverageCode = item;
          if (item === 'OS') {
            newCoverage.values[0].value = Math.round(+dwellValue / 10).toString();
          }
          if (item === 'PP') {
            newCoverage.values[0].value = Math.round(+dwellValue / 2).toString();
          }
          if (item === 'LOU') {
            newCoverage.values[0].value = Math.ceil(+dwellValue / 5).toString();
          }
          this.quoteCoveragesItems.coverages.push(newCoverage);
        });
      }
    }
    if (coverageCode === 'PP' && (this.quote.policyType === 'Condo' || this.quote.policyType === 'Tenant')) {
      let times = 0.4;
      if (this.quote.policyType === 'Tenant') {
        times = 0.2;
      }
      this.quoteCoveragesItems.coverages = this.quoteCoveragesItems.coverages.map( (item) => {
        if (item.coverageCode === 'LOU') {
          item.values[0].value = Math.ceil(+dwellValue * times).toString();
        }
        return item;
      });
      if (this.quoteCoveragesItems.coverages.length) {
        const missingCoverages = ['LOU'];
        this.dwellFieldsToUpdate.forEach( (dftu) => {
          this.quoteCoveragesItems.coverages.forEach( (item) => {
            const coverageInd = missingCoverages.indexOf(item.coverageCode);
            if (coverageInd > -1) {
              missingCoverages.splice(coverageInd, 1);
            }
          });
        });
        missingCoverages.forEach( item => {
          const newCoverage = new Coverage();
          newCoverage.values.push(new CoverageValue());
          newCoverage.coverageCode = item;
          if (item === 'LOU') {
            newCoverage.values[0].value = Math.ceil(+dwellValue * times).toString();
          }
          this.quoteCoveragesItems.coverages.push(newCoverage);
        });
      }
    }
  }


  private getCoverages() {
    this.subscribtionGetCoverages = this.storageService.getStorageData('homeStandardCoveragesList').subscribe( response => {
      if (response.length) {
        this.coverages = response;
        this.initializeCoverages(this.coverages);
      }
    });
  }

  // private initializeCoverages(coverages: Coverage[]) {
  private initializeCoverages(coverages: CoverageItem[]) {
    // assign coverages
    this.coverageDwelling = this.findCoverageByCode(coverages, 'DWELL');
    this.coverageOtherStructures = this.findCoverageByCode(coverages, 'OS');
    this.coveragePersonalProperty = this.findCoverageByCode(coverages, 'PP');
    this.coverageLossOfUse = this.findCoverageByCode(coverages, 'LOU');
    this.coveragePersonalLiability = this.findCoverageByCode(coverages, 'PL');
    this.coverageMedicalPayments = this.findCoverageByCode(coverages, 'MEDPM');
    this.coverageDwellingReplacementCost = this.findCoverageByCode(coverages, 'RCDWELL');
    this.coverageContentsReplacementCost = this.findCoverageByCode(coverages, 'RCCONT');
    this.coverageAllPerils = this.findCoverageByCode(coverages, 'APDED');
    this.coverageWindHail = this.findCoverageByCode(coverages, 'WHDED');
    this.coverageNonSmoke = this.findCoverageByCode(coverages, 'NONSMOKE');

    // set select options
    this.coveragePersonalLiabilityOptions = this.formatCoverageValues(this.getCoverageValuesFirstValues(this.coveragePersonalLiability), true);
    this.coveragePersonalLiabilityOptions = this.removeOption(this.coveragePersonalLiabilityOptions);
    this.coverageMedicalPaymentsOptions = this.formatCoverageValues(this.getCoverageValuesFirstValues(this.coverageMedicalPayments), true);
    this.coverageMedicalPaymentsOptions = this.removeOption(this.coverageMedicalPaymentsOptions);
    this.coverageDwellingReplacementCostOptions = this.formatCoverageValues(this.getCoverageValuesFirstValues(this.coverageDwellingReplacementCost));
    this.coverageDwellingReplacementCostOptions = this.addOption(this.coverageDwellingReplacementCostOptions);
    this.coverageAllPerilsOptions = this.formatCoverageValues(this.getCoverageValuesFirstValues(this.coverageAllPerils));
    this.coverageWindHailOptions = this.formatCoverageValues(this.getCoverageValuesFirstValues(this.coverageWindHail));

    this.coveragesInitialized = true;

    if (this.quoteCoveragesItems && this.quoteCoveragesItems.coverages && this.quoteCoveragesItems.coverages.length) {
      this.assignQuoteCoverages();
    }
  }

  private getCoverageValuesFirstValues(coverage: CoverageItem) {
    let result = [];

    if (coverage['values'] && coverage['values']['0'] && coverage['values']['0'] && coverage['values']['0']['values']) {
      result = coverage['values']['0']['values'];
    } else {
      console.warn('Warning: There are no values for Coverage: ', coverage);
    }

    return result;
  }

  private removeOption(coverages, option: string = 'None') {
    if (coverages && coverages.length) {
      let ind: number = null;
      coverages.forEach( (item, index) => {
        if (item.id === option) {
          ind = index;
        }
      });
      if (ind !== null) {
        coverages.splice(ind, 1);
      }
    }
    return coverages;
  }

  private addOption(coverages, option: string = 'None') {
    if (coverages && coverages.length) {
      let ind: number = null;
      coverages.forEach( (item, index) => {
        if (item.id === option) {
          ind = index;
        }
      });
      if (ind === null) {
        coverages = [{id: '', text: ''}, ...coverages];
      }
    }
    return coverages;
  }

  private findCoverageByCode(coverages: CoverageItem[], code: string): CoverageItem {
    let result: CoverageItem = new CoverageItem();

    if (!coverages || !code) { return result; }

    coverages.map(coverage => {
      if (coverage.coverageCode === code) {
        return result = coverage;
      }
    });

    return result;
  }

  private assignQuoteCoverages() {
    this.coverageQuoteDwelling = this.prepareCoverageData(this.quoteCoveragesItems.coverages, 'DWELL');
   this.coverageQuoteOtherStructures = this.prepareCoverageData(this.quoteCoveragesItems.coverages, 'OS');
   this.coverageQuotePersonalProperty = this.prepareCoverageData(this.quoteCoveragesItems.coverages, 'PP');
   this.coverageQuoteLossOfUse = this.prepareCoverageData(this.quoteCoveragesItems.coverages, 'LOU');
    this.coverageQuotePersonalLiability = this.prepareCoverageData(this.quoteCoveragesItems.coverages, 'PL', true);
   this.coverageQuoteMedicalPayments = this.prepareCoverageData(this.quoteCoveragesItems.coverages, 'MEDPM', true);
   this.coverageQuoteDwellingReplacementCost = this.prepareCoverageData(this.quoteCoveragesItems.coverages, 'RCDWELL');
    this.coverageQuoteContentsReplacementCost = this.prepareCoverageData(this.quoteCoveragesItems.coverages, 'RCCONT');
    this.coverageQuoteAllPerils = this.prepareCoverageData(this.quoteCoveragesItems.coverages, 'APDED');
    this.coverageQuoteWindHail = this.prepareCoverageData(this.quoteCoveragesItems.coverages, 'WHDED');
    this.coverageQuoteNonSmoke = this.prepareCoverageData(this.quoteCoveragesItems.coverages, 'NONSMOKE');
    this.coverageQuoteSpecialCoverage = this.prepareCoverageData(this.quoteCoveragesItems.coverages, 'AASPC');


    // console.log('this.coverageQuoteContentsReplacementCost', this.coverageQuoteContentsReplacementCost);
    // https://bostonsoftware.atlassian.net/browse/SPR-2563
    if (!this.coverageQuoteContentsReplacementCost) {
      this.updateCoverageAPI('RCCONT', 'No');
    }
  }
  private prepareCoverageData(coverages, code: string, isMoney: boolean = false): string|any {
    let result: any = '';

    coverages.filter(coverage => {
      if (coverage.coverageCode === code) {
        if (coverage.values && coverage.values.length) {
          if (this.dropdownCoverages.indexOf(code) > -1) {
            result = {
              id: coverage.values[0].value,
              text: coverage.values[0].value
            };
            if (isMoney) {
              result = {
                id: coverage.values[0].value,
                text: this.moneyService.formatToMoney(coverage.values[0].value)
              };
            }
          } else {
            result = coverage.values[0].value;
          }
        }
      }
    });

    return result;
  }

  private formatCoverageValues(values: any[], isMoney: boolean = false): FilterOption[] {
    let tempArr: FilterOption[] = [];

    if (!values || !values.length) { return tempArr; }

    tempArr = values.map((item, index) => {
      let result = {
        text: item.value,
        id: item.value
      };
      if (isMoney) {
        result = {
          id: item.value,
          text: this.moneyService.formatToMoney(item.value)
        };
      }
      return result;
    });

    return tempArr;
  }

  // insured discounts
  private getClientData() {
    this.subscriptionGetClients = this.storageService.getStorageData('clients').subscribe(client => {
      if (client[0] !== undefined) {
        this.clientDetails = client[0];
        this.clientId = client[0].resourceId;
        if (client[0].dob !== undefined) {
          const newDOB = format(parseISO(client[0].dob), 'yyyy-MM-dd');
          const datesAreDifferent = this.datesAreDifferent(this.insurerDOB, newDOB);
          if (datesAreDifferent) {
            this.insurerDOB = newDOB;
          }
        }
      }
    });
  }

  private datesAreDifferent(dateA, dateB): boolean {
    return dateA !== dateB ? true : false;
  }

 public selectedDate($event) {
    // Handle date format consistently
    const date = $event.date ? new Date($event.date) : null;
    const newDOB = date ? format(date, 'yyyy-MM-dd') : '';

    const datesAreDifferent = this.datesAreDifferent(this.insurerDOB, newDOB);

    // Calculate age using the date object
    this.calcAge(date);

    if (datesAreDifferent) {
        this.insurerDOB = newDOB;
        this.updateClientDOB(newDOB);
    }
}

private calcAge(date: Date | null): void {
    this.insurerAge = date ? differenceInYears(new Date(), date) : null;
}

  private updateClientDOB(newDOB) {
    if (newDOB && newDOB !== 'Invalid date') {
      this.clientDetails.dob = this.insurerDOB;
      this.clientsService.updateClient(this.quote.resourceId, this.clientId, this.clientDetails).subscribe(response => {
        this.storageService.updateClientsSingleItem(response);
      });
    }
  }
  public lossOfUseValidationOnFocus(): void {
    this.lossOfUseModalMessage = '';
    this.lossOfUsePreviousValue = this.coverageQuoteLossOfUse ? this.coverageQuoteLossOfUse.replace('$', '').replace(/,/g, '') : this.coverageQuoteLossOfUse;
    this.lossOfUseAdjustedValue = this.lossOfUsePreviousValue;
  }

  public lossOfUseValidationOnBlur(event: Event, coverageCode: string, value: string): void {
    // if (this.coverageQuoteLossOfUse.indexOf('$') > -1) {
    // //  this.coverageQuoteLossOfUse = this.coverageQuoteLossOfUse.replace('$', '').replace(/,/g, '');
    // }

    // if (this.coverageQuoteDwelling.indexOf('$') > -1) {
    // //  this.coverageQuoteDwelling = this.coverageQuoteDwelling.replace('$', '').replace(/,/g, '');
    // }

    // if (this.coverageQuotePersonalProperty.indexOf('$') > -1) {
    // //  this.coverageQuotePersonalProperty = this.coverageQuotePersonalProperty.replace('$', '').replace(/,/g, '');
    // }

    const dwellingValue = parseInt(this.coverageQuoteDwelling, 10);
    const previousValue: number = parseInt(this.lossOfUsePreviousValue, 10);
    const currentValue: number = parseInt(this.coverageQuoteLossOfUse, 10);
    const personalPropertyValue: number = parseInt(this.coverageQuotePersonalProperty, 10);

    if (this.quoteTypes.indexOf('HO4') !== -1 || this.quoteTypes.indexOf('HO6') !== -1) {

      // https://bostonsoftware.atlassian.net/browse/SPR-2742
      if (!isNaN(personalPropertyValue)) {
        const multipleBy = (this.quote.policyType === 'Tenant') ? 0.2 : 0.4;
        const minPercentPersonalPropertyValue = personalPropertyValue * multipleBy;
        const minPercentPersonalPropertyValueCeiled = minPercentPersonalPropertyValue && minPercentPersonalPropertyValue > 0
          ? Math.ceil(minPercentPersonalPropertyValue)
          : minPercentPersonalPropertyValue;
        const minValueInfo = (this.quote.policyType === 'Tenant')
          ? MODAL_LOU_MESSAGES.minimalValueOfPersonalPropertyTenant
          : MODAL_LOU_MESSAGES.minimalValueOfPersonalPropertyCondo;

        this.lossOfUseValueAndWarningUpdater(currentValue, minPercentPersonalPropertyValue, minValueInfo);
      } else if (!personalPropertyValue || isNaN(personalPropertyValue)) {
        this.lossOfUseModalMessage = MODAL_LOU_MESSAGES.setPersonalPropertyValue;
      }

    } else {
      if (!isNaN(dwellingValue)) {
        const minPercentDwellingValue: number = dwellingValue * 0.2;
        const minPercentDwellingValueCeiled: number = minPercentDwellingValue && minPercentDwellingValue > 0 ? Math.ceil(minPercentDwellingValue) : minPercentDwellingValue;

        this.lossOfUseValueAndWarningUpdater(currentValue, minPercentDwellingValueCeiled, MODAL_LOU_MESSAGES.minimalValueOfDwelling);
      } else if (!dwellingValue || isNaN(dwellingValue)) {
        this.lossOfUseModalMessage = MODAL_LOU_MESSAGES.setDwellingValue;
      }
    }

    // If there is a message, show modal and do not update API
    if (this.lossOfUseModalMessage && this.refModalLossOfUse) {
      this.refModalLossOfUse.open();
    } else {
      this.helperOnInputBlurTextMaskChangeEventIssueWorkaroundUpdateQuoteCoverage(event, coverageCode, this.coverageQuoteLossOfUse);
    }
  }

  private lossOfUseValueAndWarningUpdater(currentValue: number, minPercentValue: number, minValueInfo: string): void {
    if (!currentValue || isNaN(currentValue)) {
      // Adjust value
      this.lossOfUseAdjustedValue = minPercentValue.toString();
      this.lossOfUseModalMessage = minValueInfo;
    } else if (!isNaN(currentValue) && currentValue < minPercentValue) {
      this.lossOfUseModalMessage = MODAL_LOU_MESSAGES.percentageWrong;
      this.lossOfUseAdjustedValue = minPercentValue.toString();
    }
  }

  public actionModalLossOfUseOk(ev: Event): void {
    ev.preventDefault();
    this.refModalLossOfUse && this.refModalLossOfUse.close();
  }

  public actionModalLossOfUseStateChange(event, refModalBtnOk: HTMLButtonElement, refLosOfUseInput: HTMLInputElement): void {

    if (event.isOpen === false) {

      if (this.coverageQuoteLossOfUse != this.lossOfUseAdjustedValue) {
         this.coverageQuoteLossOfUseReset = true;
      } else {
         this.coverageQuoteLossOfUseReset = false;
      }

         this.coverageQuoteLossOfUse = this.lossOfUseAdjustedValue;
      if (this.lossOfUseModalMessage === MODAL_LOU_MESSAGES.setDwellingValue) {
        this.refDwelling && this.refDwelling.nativeElement.focus();
      } else if (this.lossOfUseModalMessage === MODAL_LOU_MESSAGES.setPersonalPropertyValue) {
        this.refPersonalProperty && this.refPersonalProperty.nativeElement.focus();
      } else {
        refLosOfUseInput && refLosOfUseInput.focus();
      }

    } else if (event.isOpen === true) {
      refModalBtnOk && refModalBtnOk.focus();
    }
  }


  // SPR-2083 - Wind/Hail
  private windHailFieldIsInvalidStandard(): boolean {
    let isInvalid = false;
    const calculateForHO6orHO4FormType: boolean = ['HO4', 'HO6'].some(el => this.quoteTypes.indexOf(el) !== -1);

    let windHailCurrentValue: string = null;
    let windHailCurrentValueIsPercent = false;

    let windHailValue = 0;
    let windHailCalculatedValue = 0;
    let allPerilsValue = 0;
    let dwellingValue = 0;
    let personalPropertyValue = 0;

    // Set allPerilsValue
    if (this.coverageQuoteAllPerils && this.coverageQuoteAllPerils.id) {
      const tmpVal: any = parseInt(this.coverageQuoteAllPerils.id, 10);
      allPerilsValue = !isNaN(tmpVal) ? tmpVal : 0;
    }

    // Set dwellingValue
    if (this.coverageQuoteDwelling) {
     // let tmpVal: any = this.coverageQuoteDwelling.replace('$', '').replace(',', '');
     const tmpVal = parseInt(this.coverageQuoteDwelling, 10);
      dwellingValue = !isNaN(tmpVal) ? tmpVal : 0;
    }

    // Set personalPropertyValue
    if (this.coverageQuotePersonalProperty) {
     // let tmpVal: any = this.coverageQuotePersonalProperty.replace('$', '').replace(',', '');
     const tmpVal = parseInt(this.coverageQuotePersonalProperty, 10);
      personalPropertyValue = !isNaN(tmpVal) ? tmpVal : 0;
    }

    if (this.coverageQuoteWindHail && this.coverageQuoteWindHail.id) {
      windHailCurrentValue = this.coverageQuoteWindHail.id;
    }


    if (windHailCurrentValue === 'None') {
      isInvalid = false;
      return false;
    } else if (windHailCurrentValue && windHailCurrentValue.indexOf('%') !== -1) {
      windHailCurrentValueIsPercent = true;
      windHailValue = parseInt(windHailCurrentValue.replace('%', '').trim(), 10);
      // windHailCalculatedValue = !isNaN(windHailValue) ? dwellingValue * (windHailValue / 100) : 0;

      // https://bostonsoftware.atlassian.net/browse/SPRC-613
      if (calculateForHO6orHO4FormType) {
        // windHailCalculatedValue = !isNaN(windHailValue) ? personalLiabilityValue * (windHailValue / 100) : 0;
        windHailCalculatedValue = !isNaN(windHailValue) ? personalPropertyValue * (windHailValue / 100) : 0;
      } else {
        windHailCalculatedValue = !isNaN(windHailValue) ? dwellingValue * (windHailValue / 100) : 0;
      }

    } else {
      windHailValue = parseInt(windHailCurrentValue, 10);
      windHailCalculatedValue = !isNaN(windHailValue) ? windHailValue : 0;
    }

    if (windHailCalculatedValue <= allPerilsValue) {
      isInvalid = true;
    }

    // If value for Dwelling field is not set (and not for HO^ or HO4), mark Wind / Hail as valid
    // if (windHailCurrentValueIsPercent && !dwellingValue) {
    if (windHailCurrentValueIsPercent && !dwellingValue && !calculateForHO6orHO4FormType) {
      isInvalid = false;
    }

    return isInvalid;
  }


  private windHailFieldIsInvalidTravelers(): boolean {
    const checkForCarriers: string[] = ['283'];
    const validValues: string[] = ['None', '1%', '2%', '5%'];
    let isValid = true;
    let windHailCurrentValue: string = null;

    if (this.coverageQuoteWindHail && this.coverageQuoteWindHail.id) {
      windHailCurrentValue = this.coverageQuoteWindHail.id;
    }

    isValid = validValues.some(el => el === windHailCurrentValue);

    return !isValid && Validate.isRequiredForSelectedPlans(checkForCarriers, this.selectedPlansIds);
  }

  public windHailFieldIsInvalid(): boolean {
    return this.windHailFieldIsInvalidStandard() || this.windHailFieldIsInvalidTravelers();
  }
  // # END: SPR-2083

  public fieldNonSmokerIsRequired(): boolean {
    const requiredForPlans = ['304', '292', '310'];

    return (
      Validate.isRadioInputValueNotSet2(this.coverageQuoteNonSmoke) &&
      Validate.isRequiredForSelectedPlans(requiredForPlans, this.selectedPlansIds)
    );
  }
  public helperOnInputFocusTextMaskChangeEventIssueWorkaround(event: Event): void {
    const currentInput = <HTMLInputElement>this.getElFromEvent(event);
    if (event.type === 'focus') {
      this.helperTextMaskChangeEventIssueWorkaroundInputValueDuringFocus = currentInput.value;
    }
  }

  public helperOnInputBlurTextMaskChangeEventIssueWorkaroundUpdateQuoteCoverage(event: Event, coverageCode: string, value: string): void {
    const currentInput = <HTMLInputElement>this.getElFromEvent(event);
    if (event.type === 'blur' && currentInput.className.includes('ng-dirty')) {
      if (this.helperTextMaskChangeEventIssueWorkaroundInputValueDuringFocus !== currentInput.value || this.bothValuesEmpty(currentInput.value)) {
        // this.updateQuoteCoverage(coverageCode, value);
        this.updateQuoteCoverage(coverageCode, currentInput.value);
      }
    }
    if (this.coverageQuoteLossOfUseReset) {
      this.updateQuoteCoverage(coverageCode, currentInput.value);
    }
  }

  private bothValuesEmpty(value) {

    if (value === '' && this.helperTextMaskChangeEventIssueWorkaroundInputValueDuringFocus === '' && this.formType === 'HO6') { return true; }
  }

  protected getElFromEvent(ev): HTMLElement {
    return ev.srcElement || ev.target;
  }
}
