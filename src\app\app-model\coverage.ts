import { ApiResponse, Resource, MetaData } from './_common';
import { Driver } from './driver';
import { Vehicle } from './vehicle';
import { FilterOption } from './filter-option';

export class CoverageApiResponseData extends ApiResponse<CoveragesData> { }

export class Coverage {
  constructor(
    public coverageCode: string = '',
    public coverageDescription: string = '',
    public meta: any = null,
    public resourceName: string = 'Coverage',
    public values: CoverageValue[] = []// [ new CoverageValue() ]
  ) {}
}

export class CoverageValue {
  public valueId?: string;
  public value = '';
  public questionIndex?: string;
  public description = '';

  constructor() {
    this.valueId = '';
    this.questionIndex = '';
  }
}

export class CoveragesData extends Resource {
  public coverages: Coverage[] = [];
  public meta: MetaData = { href: '', rel: [] };
}

export class CoverageForVehicle extends Coverage {
  constructor (
    public value: string = '',
    public valueId: string = '',
    public vehicleResourceId: string = '',
    public quoteId: string = ''
  ) { super(); }
}

export class CoverageItemValue {
  public value: string;
}

export class CoverageItem {
  public carrierNotes: string = null;
  public coverageCode = '';
  public definedCode = '';
  public coverageGroup = '';
  public defaultValue: any = null;
  public description = '';
  public inputType = '';
  public meta: MetaData = new MetaData();
  public ratingPlanId = '';
  public ratingPlanName = '';
  public resourceName = '';
  public values: (CoverageItemChild|CoverageItemValue)[] = [];
  public ratingCarrierId: number = null;
  public start?: number;
  public max?: number;
  public step?: number;
  public extra?: number;
  public quoteResourceId?: string;
  public viewOrderId?: number;
  public viewCategory: string;

  constructor() {
    this.quoteResourceId = ''; // It's not returned By API, should be set during parsing item, used for H&W
  }
}

export class CoverageItemChild {
  constructor (
    public carrierNotes: any = null,
    public coverageCode: string = '',
    public coverageGroup: string = '',
    public defaultValue: any = null,
    public description: string = '',
    public inputType: string = '',
    public meta: MetaData = new MetaData(),
    public questionIndex: number = 0,
    public ratingPlanId: string = '',
    public ratingPlanName: string = '',
    public resourceName: string = '',
    public values: CoverageItemValue[] = [],
    public ratingCarrierId = null,
    public currentValue?: string, // It's not returned by API, but used for saving value for updating the coverages
    public start?: number,
    public max?: number,
    public step?: number,
    public extra?: number,
    public viewOrderId: number = 0
  ) {}
}

export class CoverageItemParsedCurrentValueData {
  constructor (
    public valuesCount: number = 0,
    public values: string[] = [],
    public keyValue: Array<string>[] = [] // [['key', 'value'], ['key', 'value']]
  ) {}
}

export class CoverageItemParsed extends CoverageItem {
  constructor (
    public isRequired: boolean = false,
    public isDisabled: boolean = false,
    public isActive: boolean = false,
    public currentValue: string = '',
    public alreadySelected: boolean = false,
    public endpointUrl: string = '',
    public isNewFromAPI: boolean = false,  // Define if the option was just received from API, or it was already in the storage
    public viewUqId: string = '',
    public viewHasModalbox: boolean = false,
    public currentValueData: CoverageItemParsedCurrentValueData = new CoverageItemParsedCurrentValueData(),
    // public additionalData?: any            // Any additional data that could be required for specific component
    public additionalData?: ICoverageItemParsedAdditionalData            // Any additional data that could be required for specific component
  ) { super(); }
}

export interface ICoverageItemParsedAdditionalData {
  // [key: string]: any,
  hasError?: boolean;
  errorMessage?: string;
  forVehicle?: Vehicle;
  availableOptions?: FilterOption[];
  agentCodeGroupPlanAddEmptyOption?: boolean;
  requiredForCarriers?: string[];
  vehicle?: any;
}

export class CoverageItemParsedForAutoDriverOptionsHintsAndWarnings extends CoverageItemParsed {
  constructor(
    public driver: Driver = new Driver()
  ) { super(); }
}

export class PolicyCoveragesData {
  constructor (
    public coverages: Coverage[] = [],
    public endpointURL: string = ''
  ) {}
}

export class PolicyItemDefaultDataAPIResponse extends ApiResponse<Coverage> { }

export class CoverageSummaryItem {
  constructor(
    public coverageCode: string = '',
    public description: string = '',
    public value: string = '',
    public value2: string = '',
    public options?: {valueType?: string, value?: string},
    public limits?: {value: string}[]
  ) {}
}

// ----
export class CoverageLevelDataAPIResponse extends ApiResponse<CoverageLevelData> { }

export class CoverageLevelData {
  constructor (
    public items: CoverageLevelDataItem[] = [],
    public meta: MetaData = new MetaData(),
    public name: string = '',
    public resourceName: string = 'QuoteCoverageLevel'
  ) {}
}

export class CoverageLevelDataItem {
  constructor (
    public coverageCode: string = '',
    public value: string = ''
  ) {}
}

export class CoveragesAutoDefaultStandardCoverages {
  constructor (
    public Coverages: CoveragesAutoDefaultStandardCoveragesItem[] = []
  ) {}
}

export class CoveragesAutoDefaultStandardCoveragesItem {
  constructor (
    public coverageCode: string = '',
    public values: Array<{value: string}> = []
  ) {}
}
