<div *ngIf="!proceedClicked;then RmvResults else emailSent"></div>
  <ng-template #RmvResults>
<h1 class="o-heading o-heading--red">RMV Results</h1>
  <br>
  <p-accordion [multiple]="true">
  <p-accordionTab header="The RMV returned the following information for the data you entered:" [selected]="true"  *ngIf="rmvResponse">
      <div class="box box--silver">
        <div *ngFor="let msg of rmvResponse.messages" style="padding-bottom:.4rem">
          <p *ngIf="rmvResponse.transaction.success">{{msg.name}}: {{msg.value}}</p>
          <p *ngIf="!rmvResponse.transaction.success && msg.name === 'Failure'">{{msg.value}}</p>
          </div>
         </div>
         </p-accordionTab>
           <p-accordionTab header="Notifications:" [selected]="true" *ngIf="rmvResponse && rmvResponse.notifications.length > 0 && rmvResponse.transaction.success">
            <div *ngFor="let notif of rmvResponse.notifications">
               <p class="box box--silver">
                 {{notif.value}}
               </p>
            </div>
           </p-accordionTab>
           <p-accordionTab
           *ngIf="rmvResponse?.transaction.success && rmvResponse?.transaction.eligible"
           header="Click Proceed to send a Payment Request to {{rmvResponse.userEmail}}." [selected]="true">
                <div class="box box--silver">
              <input [(ngModel)]="rmvResponse.agentEmail"/>
              <div style="padding-top:.7rem">
                <label class="o-checkable">
                 <input type="checkbox" name="copyemail" [(ngModel)]="copyEmail">
                 <i class="o-btn o-btn--checkbox u-spacing--right-0"></i>
                 </label>
             Click here to be copied on all email correspondence during this process
             </div>
                </div>

           </p-accordionTab>
<div class="col-md-12" *ngIf="!rmvResponse?.transaction.eligible && rmvResponse?.transaction.success">
  <div class="box box--warning">
    <p>This transaction is not eligible to be done via direct integration with the RMV.</p>
    <p>Click Go To RTA to prefill the RTA form.</p>
  </div>
</div>
  </p-accordion>


          <div class="row u-spacing--1-5">
              <div class="col-xs-12 u-align-right u-remove-letter-spacing" style="padding-top: 2rem;">
                  <button *ngIf="rmvResponse?.transaction.eligible && rmvResponse?.transaction.success" type="button" class="o-btn"  (click)="proceed()">PROCEED</button>
                  <button *ngIf="!rmvResponse?.transaction.eligible && rmvResponse?.transaction.success" type="button" class="o-btn"  (click)="rta()">Go To RTA</button>
                  <button type="button" (click)="onCancel()" class="o-btn o-btn--idle u-spacing--left-2" style="margin-left:1.8rem;">Go Back</button>
              </div>
            </div>
  </ng-template>
            <ng-template #emailSent>
                <h1 class="o-heading o-heading--red">PAYMENT REQUEST</h1>
                <br>
                <div *ngIf="proceedResponse" class="box box--silver u-spacing--1-5">
                  <p style="white-space: pre-wrap;">{{proceedResponse.messages[0]}}</p>
                </div>
                <div class="row u-spacing--1-5">
                  <div class="col-xs-12 u-align-right u-remove-letter-spacing">
                      <button type="button" class="o-btn"  (click)="close()">CLOSE</button>
                      </div>
                      </div>
            </ng-template>





