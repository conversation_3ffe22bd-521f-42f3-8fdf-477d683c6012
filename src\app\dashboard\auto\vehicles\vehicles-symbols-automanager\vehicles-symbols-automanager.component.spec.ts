import { async, ComponentFixture, fakeAsync, inject, TestBed, tick } from '@angular/core/testing';

import { VEHICLE_GENERAL_DETAILS } from 'testing/data/lookups/vin';
import { AUTO_QUOTE } from 'testing/data/quotes/quote-auto';
import { AUTO_QUOTE_PLAN_LIST } from 'testing/data/quotes/quote-plan-list/auto';
import { VEHICLES } from 'testing/data/quotes/vehicles';
import { StubLookupsService, StubLookupsServiceProvider } from 'testing/stubs/services/lookups.service.provider';

import { LookupsService } from 'app/dashboard/app-services/lookups.service';
import { PlansService } from 'app/dashboard/app-services/plans.service';
import { SymbolsService } from 'app/dashboard/app-services/symbols.service';
import { VehiclesService } from 'app/dashboard/app-services/vehicles.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Helpers } from 'app/utils/helpers';

import { VehiclesSymbolsAutomanagerComponent } from './vehicles-symbols-automanager.component';
import { StubVehiclesServiceProvider, StubVehiclesService } from 'testing/stubs/services/vehicles.service.provider';
import { PLAN_SYMBOLS } from 'testing/data/lookups/plan-symbols';

describe('Component: VehiclesSymbolsAutomanagerComponent', () => {
  let component: VehiclesSymbolsAutomanagerComponent;
  let fixture: ComponentFixture<VehiclesSymbolsAutomanagerComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ VehiclesSymbolsAutomanagerComponent ],
      providers: [
        StorageService,
        StubVehiclesServiceProvider,
        StubLookupsServiceProvider,
        SymbolsService,
        PlansService
      ]
    })
    .compileComponents();
  }));

  describe('when data is available in storage', () => {
    beforeEach(fakeAsync(inject([StorageService, LookupsService],
      (storageService: StorageService, lookupsService: StubLookupsService) => {
        lookupsService.vehicleGeneralDetails = Helpers.deepClone(VEHICLE_GENERAL_DETAILS);
        lookupsService.vehiclePlanSymbols = Helpers.deepClone(PLAN_SYMBOLS);

        storageService.setStorageData('vehiclesList', Helpers.deepClone(VEHICLES.items));
        storageService.setStorageData('selectedQuote', Helpers.deepClone(AUTO_QUOTE));
        storageService.setStorageData('selectedPlan', Helpers.deepClone(AUTO_QUOTE_PLAN_LIST));

        fixture = TestBed.createComponent(VehiclesSymbolsAutomanagerComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
        tick();
      })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });

    it('should destroy without errors', () => {
      expect(() => {
        fixture.destroy();
      }).not.toThrow();
    });

    it('should update symbols on vehicle deletetion', fakeAsync(inject([StorageService], (storageService: StorageService) => {
      storageService.setStorageData('vehiclesList', Helpers.deepClone(VEHICLES.items.slice(0, 1)));
      fixture.detectChanges();
      tick(500);

      storageService.getStorageData('vehiclesSelectedSymbolsForVehicle').subscribe(data => {
        expect(data.length).toEqual(1);
      });
    })));

    it('should update symbols when plans change', fakeAsync(inject([StorageService], (storageService: StorageService) => {
      spyOn(storageService, 'updateVehiclesSelectedSymbolsForVehicleSingleItem').and.callThrough();

      const modifiedPlans = Helpers.deepClone(AUTO_QUOTE_PLAN_LIST);
      modifiedPlans.items[0].items = modifiedPlans.items[0].items.slice(2, 5);

      storageService.setStorageData('selectedPlan', modifiedPlans);
      fixture.detectChanges();
      tick(500);

      expect(storageService.updateVehiclesSelectedSymbolsForVehicleSingleItem).toHaveBeenCalled();
    })));
  });

  describe('when no vehicles are available in storage', () => {
    beforeEach(fakeAsync(inject([StorageService, LookupsService, VehiclesService],
      (storageService: StorageService, lookupsService: StubLookupsService,
        vehiclesService: StubVehiclesService) => {
        lookupsService.vehicleGeneralDetails = Helpers.deepClone(VEHICLE_GENERAL_DETAILS);
        lookupsService.vehiclePlanSymbols = Helpers.deepClone(PLAN_SYMBOLS);
        vehiclesService.vehicles = Helpers.deepClone(VEHICLES);

        storageService.setStorageData('selectedQuote', Helpers.deepClone(AUTO_QUOTE));
        storageService.setStorageData('selectedPlan', Helpers.deepClone(AUTO_QUOTE_PLAN_LIST));

        fixture = TestBed.createComponent(VehiclesSymbolsAutomanagerComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
        tick();
      })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('when vin is invalid', () => {
    beforeEach(fakeAsync(inject([StorageService, LookupsService, VehiclesService],
      (storageService: StorageService, lookupsService: StubLookupsService,
        vehiclesService: StubVehiclesService) => {
        lookupsService.vehicleGeneralDetails = Helpers.deepClone(VEHICLE_GENERAL_DETAILS);
        lookupsService.vehiclePlanSymbols = Helpers.deepClone(PLAN_SYMBOLS);
        vehiclesService.vinIsValid = false;

        storageService.setStorageData('vehiclesList', Helpers.deepClone(VEHICLES.items));
        storageService.setStorageData('selectedQuote', Helpers.deepClone(AUTO_QUOTE));
        storageService.setStorageData('selectedPlan', Helpers.deepClone(AUTO_QUOTE_PLAN_LIST));

        fixture = TestBed.createComponent(VehiclesSymbolsAutomanagerComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
        tick();
      })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });
  });
});
