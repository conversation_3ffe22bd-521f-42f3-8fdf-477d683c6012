import {
    generateCarriers, generateLabels, generateViewFieldIds, generateViewURIs, runConditions
} from 'testing/helpers/warning-definitions';

import { QuoteAuto, QuoteHome } from 'app/app-model/quote';

import { WARNINGS_DEFINITIONS_INFO_QUOTE } from './definitions';

describe('Definitions: Quote', () => {
    describe('when quote info validator is used on Auto quote', () => {
        let definitions: any[];
        let quote: QuoteAuto;

        beforeEach(() => {
            definitions = WARNINGS_DEFINITIONS_INFO_QUOTE;

            quote = Object.assign(new QuoteAuto(), {
                meta: {
                    href: ''
                },
                state: '',
                lob: '',
                creationDate: '',
                lastModifiedDate: '',
                effectiveDate: '',
                expirationDate: '',
                policyPeriod: '',
                policyNumber: '',
                policyType: '',
                quoteIdentifier: '',
                description: '',
                agency: '',
                agent: '',
                writtenStatus: '',
                writtenCarrier: '',
                sourceBusiness: '',
                declineReason: '',
                client: {
                    city: '',
                    clientIdentifier: '',
                    firstName: '',
                    lastName: '',
                    meta: {
                        href: ''
                    },
                    quoteSessionId: '',
                    resourceId: '',
                    resourceName: '',
                    state: ''
                },
                clients: {
                    href: ''
                },
                coverages: {
                    href: ''
                },
                drivers: {
                    href: ''
                },
                locations: {
                    href: ''
                },
                quotePlanList: {
                    href: ''
                },
                vehicles: {
                    href: ''
                },
                policyHistory: {
                    href: ''
                },
                quoteSessionId: '',
                resourceId: '',
                resourceName: '',
                formType: '',
                quoteUUID: ''
            });
        });

        it('allows to check if required fields are provided', () => {
            const errors = runConditions(definitions, quote, {});

            expect(errors).toEqual([
                'lob', 'state', 'agent', 'policyPeriod', 'effectiveDate', 'expirationDate'
            ]);
        });

        it('generates viewFieldIds without errors', () => {
            expect(() => {
                generateViewFieldIds(definitions, quote);
            }).not.toThrow();
        });

        it('generates viewURIs without errors', () => {
            expect(() => {
                generateViewURIs(definitions, quote);
            }).not.toThrow();
        });

        it('generates labels without errors', () => {
            expect(() => {
                generateLabels(definitions, quote);
            }).not.toThrow();
        });

        it('generates carriers without errors', () => {
            expect(() => {
                generateCarriers(definitions, quote);
            }).not.toThrow();
        });
    });

    describe('when quote info validator is used on Home quote', () => {
        let definitions: any[];
        let quote: QuoteHome;

        beforeEach(() => {
            definitions = WARNINGS_DEFINITIONS_INFO_QUOTE;

            quote = Object.assign(new QuoteHome(), {
                meta: {
                    href: ''
                },
                state: '',
                lob: '',
                creationDate: '',
                lastModifiedDate: '',
                effectiveDate: '',
                expirationDate: '',
                quoteIdentifier: '',
                policyType: '',
                policyPeriod: '',
                policyNumber: '',
                description: '',
                agency: '',
                agent: '',
                writtenStatus: '',
                writtenCarrier: '',
                sourceBusiness: '',
                declineReason: '',
                client: {
                    city: '',
                    clientIdentifier: '',
                    firstName: '',
                    lastName: '',
                    meta: {
                        href: ''
                    },
                    quoteSessionId: '',
                    resourceId: '',
                    resourceName: '',
                    state: ''
                },
                clients: {
                    href: ''
                },
                coveragesStandard: {
                    href: ''
                },
                options: {
                    href: ''
                },
                locations: {
                    href: ''
                },
                quotePlanList: {
                    href: ''
                },
                dwellings: {
                    href: ''
                },
                lossHistory: {
                    href: ''
                },
                scheduledProperty: {
                    href: ''
                },
                quoteSessionId: '',
                resourceId: '',
                resourceName: '',
                formType: '',
                quoteUUID: ''
            });
        });

        it('allows to check if required fields are provided', () => {
            const errors = runConditions(definitions, quote, {});

            expect(errors).toEqual([
                'lob', 'state', 'agent', 'policyPeriod', 'effectiveDate', 'expirationDate'
            ]);
        });

        it('generates viewFieldIds without errors', () => {
            expect(() => {
                generateViewFieldIds(definitions, quote);
            }).not.toThrow();
        });

        it('generates viewURIs without errors', () => {
            expect(() => {
                generateViewURIs(definitions, quote);
            }).not.toThrow();
        });

        it('generates labels without errors', () => {
            expect(() => {
                generateLabels(definitions, quote);
            }).not.toThrow();
        });

        it('generates carriers without errors', () => {
            expect(() => {
                generateCarriers(definitions, quote);
            }).not.toThrow();
        });
    });
});
