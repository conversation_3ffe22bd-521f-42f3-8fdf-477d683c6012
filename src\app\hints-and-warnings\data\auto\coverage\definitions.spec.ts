import {
    generateCarriers,
    generateLabels,
    generateViewFieldIds,
    generateViewURIs,
    runConditions,
} from 'testing/helpers/warning-definitions';

import { WARNINGS_DEFINITIONS_AUTO_STANDARD_COVERAGES } from 'app/hints-and-warnings/data';

import { CoverageForVehicle } from 'app/app-model/coverage';


describe('Definitions: Coverage', () => {
    describe('when auto standard coverages validator is used', () => {
        let definitions: any[];
        let coverage: CoverageForVehicle;

        beforeEach(() => {
            definitions = WARNINGS_DEFINITIONS_AUTO_STANDARD_COVERAGES;
            coverage = {
                coverageCode: '',
                coverageDescription: '',
                meta: {
                    href: '',
                    rel: []
                },
                resourceName: '',
                value: '',
                valueId: '',
                values: [],
                vehicleResourceId: '',
                quoteId: ''
            };
        });

        it('allows to check if required fields are provided', () => {
            let errors = runConditions(definitions, coverage, {});

            expect(errors).toEqual(['value']);

            coverage.value = 'filled';

            errors = runConditions(definitions, coverage, {});

            expect(errors).toEqual([]);
        });

        it('generates viewFieldIds without errors', () => {
            expect(() => {
                generateViewFieldIds(definitions, coverage);
            }).not.toThrow();
        });

        it('generates viewURIs without errors', () => {
            expect(() => {
                generateViewURIs(definitions, coverage);
            }).not.toThrow();
        });

        it('generates labels without errors', () => {
            expect(() => {
                generateLabels(definitions, coverage);
            }).not.toThrow();
        });

        it('generates carriers without errors', () => {
            expect(() => {
                generateCarriers(definitions, coverage);
            }).not.toThrow();
        });
    });
});
