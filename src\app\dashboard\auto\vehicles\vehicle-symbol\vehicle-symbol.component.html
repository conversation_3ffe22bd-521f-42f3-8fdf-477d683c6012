<h1 class="o-heading">Symbols</h1>

  <div class="box box--silver u-spacing--1-5">

      <app-loader [loading]="symbolsUpdateInProgress" [cssClass]="'loader--with-opacity'" [loadingText]="'Updating Symbols, please wait...'"></app-loader>

    <div *ngIf="showInfoSymbolsSourceCodeContainer">
      <p class="u-color-tenne" *ngIf="showInfoSymbolsSourceCodeManually">Symbols in yellow were manually entered.</p>
      <p class="u-color-tenne" *ngIf="showInfoSymbolsSourceCodePrice" [class.u-spacing--1-5]="showInfoSymbolsSourceCodeManually">Symbols in green were set by price.</p>

      <hr>
    </div>

    <p class="u-spacing--bottom-1-5">The plans you've selected use the following symbol types:</p>

    <div *ngIf="showIso75">
      <div class="u-flex u-flex--row u-padd--left-2">
        <div class="u-width-80px">
          ISO 75
        </div>
        <div class="u-width-180px u-flex u-flex--row">
          <p class="u-padd--right-1">Collision:</p>
          <div class="u-width-65px">
            <sm-autocomplete
              [css]="checkDeterminationOfSymbolValue(selectedVehicleSymbols.iso75Coll.symbol)"
              [options]="optionsIso75Collision"
              [activeOption]="selectedVehicleSymbols.iso75Coll.symbol.symbolValue"
              [searchFromBegining]="true"
              (onSelect)="onSelectSymbolValueChange($event, 'iso75Coll')">
            </sm-autocomplete>
          </div>
        </div>
        <div class="u-flex u-flex--row">
          <p class="u-padd--right-1">Comprehensive:</p>
          <div class="u-width-65px">
            <sm-autocomplete
              [css]="checkDeterminationOfSymbolValue(selectedVehicleSymbols.iso75Comp.symbol)"
              [options]="optionsIso75Comprehensive"
              [activeOption]="selectedVehicleSymbols.iso75Comp.symbol.symbolValue"
              [searchFromBegining]="true"
              (onSelect)="onSelectSymbolValueChange($event, 'iso75Comp')">
            </sm-autocomplete>
          </div>
        </div>
      </div>

      <hr>
    </div> <!-- ngIf -->

    <div *ngIf="showVrg">
      <div class="u-flex u-flex--row u-padd--left-2">
        <div class="u-width-80px">
          VRG
        </div>
        <div class="u-width-180px u-flex u-flex--row">
          <p class="u-padd--right-1">Collision:</p>
          <div class="u-width-65px">
            <sm-autocomplete
              [css]="checkDeterminationOfSymbolValue(selectedVehicleSymbols.vrgColl.symbol)"
              [options]="optionsVrgCollision"
              [activeOption]="selectedVehicleSymbols.vrgColl.symbol.symbolValue"
              [searchFromBegining]="true"
              (onSelect)="onSelectSymbolValueChange($event, 'vrgColl')">
            </sm-autocomplete>
          </div>
        </div>
        <div class="u-flex u-flex--row">
          <p class="u-padd--right-1">Comprehensive:</p>
          <div class="u-width-65px">
            <sm-autocomplete
              [css]="checkDeterminationOfSymbolValue(selectedVehicleSymbols.vrgComp.symbol)"
              [options]="optionsVrgComprehensive"
              [activeOption]="selectedVehicleSymbols.vrgComp.symbol.symbolValue"
              [searchFromBegining]="true"
              (onSelect)="onSelectSymbolValueChange($event, 'vrgComp')">
            </sm-autocomplete>
          </div>
        </div>
      </div>

      <hr>
    </div>

    <div *ngIf="showIso27">
      <div class="u-flex u-flex--row u-padd--left-2">
        <!-- div class="u-width-100px" -->
        <div class="u-width-145px">
          ISO 1-27
        </div>
        <div class="u-flex u-flex--row">
          <div class="u-width-65px">
            <sm-autocomplete
              #refSymIso27
              [css]="checkDeterminationOfSymbolValue(selectedVehicleSymbols.iso27.symbol)"
              [options]="optionsIso1_27"
              [activeOption]="selectedVehicleSymbols.iso27.symbol.symbolValue"
              [searchFromBegining]="true"
              (onSelect)="onSelectSymbolValueChange($event, 'iso27')">
            </sm-autocomplete>
          </div>
          <p class="u-padd--left-2 u-t-size--1-1rem u-color-nobel" style="font-size:1.18rem">Applies to both collision and comprehensive</p>
        </div>
      </div>

      <hr>
    </div>

    <div *ngIf="showIso27Safety">
      <div class="u-flex u-flex--row u-padd--left-2">
        <div class="u-width-145px">
          ISO 1-27 (Safety)
        </div>
        <div class="u-flex u-flex--row">
          <div class="u-width-65px">
            <sm-autocomplete
              [css]="checkDeterminationOfSymbolValue(selectedVehicleSymbols.iso27Safety.symbol)"
              [options]="optionsIso1_27"
              [activeOption]="selectedVehicleSymbols.iso27Safety.symbol.symbolValue"
              [searchFromBegining]="true"
              (onSelect)="onSelectSymbolValueChange($event, 'iso27Safety')">
            </sm-autocomplete>
          </div>
        </div>
      </div>

      <hr>
    </div>

    <p>You can also recalculate the symbols by updating the value of the car:</p>
    <div class="u-flex u-flex--row u-padd--left-2 u-spacing--1-5" [class.is-required-field]="refPriceValue?.invalid">
      <div class="u-width-130px">
        Price New / Value
      </div>
      <div class="u-flex u-flex--row">
        <div class="u-width-120px">
          <input
            #refPriceValue="ngModel"
            type="text"
            id="priceNewValue"
            name="priceNewValue"
            pattern="\d*"
            (keydown)="numbersOnly($event)"
            (keyup)="checkIfValuOfPriceHasChanged()"
            [(ngModel)]="selectedVehiclePriceNewValue"
            [disabled]="!checkIfPriceValueFieldRequired()"
            [required]="checkIfPriceValueFieldRequired()">
        </div>

        <div class="u-padd--left-2">
          <button class="o-link o-link--blue" [disabled]="!enableRecalculateButton()" (click)="actionOnRecalculateButtonClick($event)">Calculate Symbols</button>
        </div>
      </div>
    </div>
  </div>