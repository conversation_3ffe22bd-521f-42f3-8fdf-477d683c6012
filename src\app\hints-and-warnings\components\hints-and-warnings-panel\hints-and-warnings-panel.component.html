<!-- section *ngIf="showWarningPanel" class="section warnings-panel" [ngClass]="{'is-open': panelIsOpen}" -->
<section class="section warnings-panel" [ngClass]="{'is-open': panelIsOpen}">

    <div class="warnings-panel__head">
      <button class="warnings-panel__btn-tab" (click)="toggleWarningPanel($event)">
        <i class="icon-two-arrows-up warnings-panel__btn-tab-icon"></i>
        {{buttonText}}
      </button>
    </div>

    <div class="row o-columns o-columns--separate warnings-panel__body">
      <div class="col-xs-6">
        <div class="warnings-panel__col-heading">
          <h2 class="o-heading u-color-sun-juan">General warnings:</h2>
        </div>

        <div class="u-t-size--1-2rem u-spacing--1-5">
          <div>
            <ul class="list list--nowrap u-color-pelorous">
              <li class="list__item" *ngFor="let item of generalWarnings | limitTo:initShowCount">
                <app-hints-and-warnings-link [css]="'warnings-panel__link'" [openPanel]="true" [warningItem]="item"></app-hints-and-warnings-link>
              </li>
            </ul>

            <div class="u-spacing u-spacing--1"></div>
            <button id="modalGeneralWarningsButton" *ngIf="showToggleViewAllGeneralWarnings" class="o-btn o-btn--action u-color-pelorous">View All</button>
          </div>

          <p *ngIf="!generalWarnings.length">No warnings.</p>
        </div>
      </div>


      <div class="col-xs-6">
        <div class="warnings-panel__col-heading">
          <h2 class="o-heading u-color-sun-juan u-show-inline u-spacing--right-0-5">Warnings: </h2>

          <app-filter
            [id]="'filter_carriers'"
            [name]="''"
            [popupPosition]="'my-center-at-top-right'"
            [label]="filterLabel"
            [positionToTop]="true"
            [hasSearch]="false"
            [options]="plans"
            (onChange)="onFilterCarrierChange($event)">
          </app-filter>
        </div>

        <div class="u-t-size--1-2rem u-spacing--1-5">
          <div *ngIf="filteredCarrierWarnings.length">
            <ul class="list list--nowrap u-color-pelorous">
              <li class="list__item" *ngFor="let item of filteredCarrierWarnings | limitTo:initShowCount">
                <!-- app-warning-link [css]="'warnings-panel__link'" [openPanel]="true" [warningItem]="item"></app-warning-link -->
                <app-hints-and-warnings-link [css]="'warnings-panel__link'" [openPanel]="true" [warningItem]="item"></app-hints-and-warnings-link>
              </li>
            </ul>

            <div class="u-spacing u-spacing--1"></div>
            <button id="modalWarningsButton" *ngIf="showToggleViewAllCarrierWarnings" class="o-btn o-btn--action u-color-pelorous js-modal-warnings">View All</button>
          </div>

          <p *ngIf="!filteredCarrierWarnings.length">No warnings</p>
        </div>
      </div>

    </div>

</section>

<!-- GENERAL WARNINGS MODAL -->
<app-modalbox #refModalGeneralWarnings [launcher]="'#modalGeneralWarningsButton'">
  <h2 class="o-heading o-heading--red u-show-iblock u-align-v-middle">General Warnings:</h2>

  <div class="box box--silver u-spacing--1-5">
      <ul class="list list--big-items-compressed u-spacing--left-3-5">
        <li (click)="closeModalbox(refModalGeneralWarnings)" class="list__item" *ngFor="let item of generalWarnings; let i = index">
          <app-hints-and-warnings-link [css]="'o-link u-color-pelorous o-link--hover-underline'" [openPanel]="true" [warningItem]="item"></app-hints-and-warnings-link>
        </li>
      </ul>
  </div>

  <div class="row u-spacing--2">
    <div class="col-xs-12 u-align-right">
      <button (click)="refModalGeneralWarnings.closeModalbox()" class="o-btn">Close</button>
    </div>
  </div>
</app-modalbox>

<!-- WARNINGS MODAL -->
<!-- [css]="'centered'" [backdropCss]="'not-visible'" -->
<app-modalbox #refModalWarnings [launcher]="'#modalWarningsButton'">
  <div class="u-flex u-flex--spread">
    <div class="">
      <h2 class="o-heading o-heading--red u-show-iblock u-align-v-middle u-spacing--right-1">Warnings:</h2>
    </div>
    <div class="">
      <app-filter
        [id]="'filter_carriers-modal'"
        [css]="'u-spacing--right-0'"
        [label]="filterLabel"
        [name]="''"
        [hasSearch]="false"
        [options]="plans"
        (onChange)="onFilterCarrierChange($event)"></app-filter>
    </div>
  </div>

  <div class="box box--silver u-spacing--1-5">

      <ul class="list list--big-items-compressed u-spacing--left-3-5">
        <li (click)="closeModalbox(refModalWarnings)" class="list__item" *ngFor="let item of filteredCarrierWarnings; let i = index">
          <app-hints-and-warnings-link [css]="'o-link u-color-pelorous o-link--hover-underline'" [openPanel]="true" [warningItem]="item"></app-hints-and-warnings-link>
        </li>
      </ul>
      <p *ngIf="filteredCarrierWarnings && filteredCarrierWarnings.length === 0">No warnings</p>

  </div>

  <div class="row u-spacing--2">
    <div class="col-xs-12 u-align-right">
      <button (click)="refModalWarnings.closeModalbox()" class="o-btn">Close</button>
    </div>
  </div>
</app-modalbox>
