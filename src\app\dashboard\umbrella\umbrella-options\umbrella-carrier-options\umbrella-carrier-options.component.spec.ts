import { async, ComponentFixture, inject, TestBed } from '@angular/core/testing';

import { StubOptionsViewDropdownComponent } from 'testing/stubs/components/options-view-dropdown.component';

import { CoverageItemParsed } from 'app/app-model/coverage';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Helpers } from 'app/utils/helpers';

import { UmbrellaCarrierOptionsComponent } from './umbrella-carrier-options.component';

describe('UmbrellaCarrierOptionsComponent', () => {
  let component: UmbrellaCarrierOptionsComponent;
  let fixture: ComponentFixture<UmbrellaCarrierOptionsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [
        UmbrellaCarrierOptionsComponent,
        StubOptionsViewDropdownComponent
      ],
      providers: [
        StorageService
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(UmbrellaCarrierOptionsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
