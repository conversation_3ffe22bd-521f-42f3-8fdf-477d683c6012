<app-aside-buttons-menu [isDashboard]=isDashboard></app-aside-buttons-menu>

<div class="sidebox__separator"></div>

<div class="sidebox">

  <ul class="list-menu">
    <li *ngIf="!formsOnlyUser" class="list-menu__item">
      <a class="list-menu__link list-menu__link-block" [crossAppRouterLink]="'{env::domain}/webapps/SinglePointApp/'"
        target="_blank" title="">Carrier Credentials</a>
    </li>
  </ul>

</div> <!-- / .sidebox -->
