import { async, ComponentFixture, fakeAsync, inject, TestBed, tick } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';

import { DWELLING_PROTECTION_CLASS } from 'testing/data/dwellings/protection-class';
import {
    DWELLING_PROTECTION_CLASS_OVERRIDES
} from 'testing/data/dwellings/protection-class-overrides';
import { DWELLINGS } from 'testing/data/quotes/dwellings';
import { DWELLING_LOCATION } from 'testing/data/quotes/locations/dwelling';
import { HOME_QUOTE_PLAN_LIST } from 'testing/data/quotes/quote-plan-list/home';
import { StubAutocompleteComponent } from 'testing/stubs/components/autocomplete.component';
import { StubModalboxComponent } from 'testing/stubs/components/modalbox.component';
import {
    StubPerfectScrollbarComponent
} from 'testing/stubs/components/perfect-scrollbar.component';
import { StubDwellingServiceProvider, StubDwellingService } from 'testing/stubs/services/dwelling.service.provider';
import {
    StubOverlayLoaderServiceProvider
} from 'testing/stubs/services/overlay-loader.service.provider';
import { StubQuotesServiceProvider } from 'testing/stubs/services/quotes.service.provider';
import { StubSpecsServiceProvider } from 'testing/stubs/services/specs.service.provider';

import { DwellingService } from 'app/dashboard/app-services/dwelling.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Helpers } from 'app/utils/helpers';

import { ProtectionClassComponent } from './protection-class.component';

describe('Component: ProtectionClass', () => {
  let component: ProtectionClassComponent;
  let fixture: ComponentFixture<ProtectionClassComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [ FormsModule ],
      declarations: [
        ProtectionClassComponent,
        StubAutocompleteComponent,
        StubPerfectScrollbarComponent,
        StubModalboxComponent
      ],
      providers: [
        StubOverlayLoaderServiceProvider,
        StorageService,
        StubDwellingServiceProvider,
        StubQuotesServiceProvider,
        StubSpecsServiceProvider
      ]
    })
    .compileComponents();
  }));

  describe('when all data is available in storage', () => {
    let dwellingService: DwellingService;

    beforeEach(fakeAsync(inject([StorageService, DwellingService], (storageService: StorageService, _dwellingService: DwellingService) => {
      storageService.setStorageData('dwelling', Helpers.deepClone(DWELLINGS.items[0]));
      storageService.setStorageData('dwellingProtectionClass', Helpers.deepClone(DWELLING_PROTECTION_CLASS.items[0]));
      storageService.setStorageData('dwellingProtectionClassOverrides', Helpers.deepClone(DWELLING_PROTECTION_CLASS_OVERRIDES.items[0]));
      storageService.setStorageData('selectedPlan', Helpers.deepClone(HOME_QUOTE_PLAN_LIST));
      storageService.setStorageData('dwellingLocation', Helpers.deepClone(DWELLING_LOCATION.items[0]));

      dwellingService = _dwellingService;
      spyOn(dwellingService, 'updateDwellingProtectionClass').and.callThrough();
      spyOn(dwellingService, 'updateDwellingProtectionClassOverrides').and.callThrough();

      fixture = TestBed.createComponent(ProtectionClassComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
      tick();
    })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });

    it('should destroy without errors', () => {
      expect(() => {
        fixture.destroy();
      }).not.toThrow();
    });

    it('should update protection class if distance to fire station is changed', fakeAsync(() => {
      const autocomplete: StubAutocompleteComponent = fixture.debugElement.query(By.directive(StubAutocompleteComponent)).componentInstance;

      autocomplete.activeOption = component.distanceToFireStation[1];
      fixture.detectChanges();
      tick();

      expect(dwellingService.updateDwellingProtectionClass).toHaveBeenCalled();
    }));

    it('it should allow to manually update protection class ISO', fakeAsync(() => {
      const modalboxDE = fixture.debugElement.query(By.directive(StubModalboxComponent));
      const saveBtn = modalboxDE.query(By.css('button')).nativeElement;
      const autocomplete: StubAutocompleteComponent = modalboxDE.query(By.directive(StubAutocompleteComponent)).componentInstance;

      autocomplete.activeOption = component.isoValues[1];
      fixture.detectChanges();
      tick();

      saveBtn.click();
      fixture.detectChanges();
      tick();

      expect(dwellingService.updateDwellingProtectionClassOverrides).toHaveBeenCalled();
    }));
  });

  describe('when only basic data is available in storage', () => {
    beforeEach(fakeAsync(inject([StorageService, DwellingService],
      (storageService: StorageService, dwellingService: StubDwellingService) => {
      storageService.setStorageData('dwelling', Helpers.deepClone(DWELLINGS.items[0]));
      storageService.setStorageData('dwellingProtectionClass', Helpers.deepClone(DWELLING_PROTECTION_CLASS.items[0]));
      storageService.setStorageData('selectedPlan', Helpers.deepClone(HOME_QUOTE_PLAN_LIST));
      storageService.setStorageData('dwellingLocation', Helpers.deepClone(DWELLING_LOCATION.items[0]));

      dwellingService.dwellingProtectionClassOverrides = Helpers.deepClone(DWELLING_PROTECTION_CLASS_OVERRIDES);

      fixture = TestBed.createComponent(ProtectionClassComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
      tick();
    })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });

    it('should destroy without errors', () => {
      expect(() => {
        fixture.destroy();
      }).not.toThrow();
    });
  });
});
