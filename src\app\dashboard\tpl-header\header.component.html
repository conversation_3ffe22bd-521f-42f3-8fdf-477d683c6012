<div class="main-header" [appDetectSystem]>

  <a routerLink="/dashboard" class="main-header__logo">
    <img src="assets/images/common/logo.svg" alt="Single Point" width="160" height="18">
  </a>

  <nav class="main-nav">
    <ul class="main-nav__list">
      <li class="main-nav__item" *ngIf="viewingQuote || viewingRmvService">
        <a [routerLink]="['/dashboard']" class="main-nav__link">Dashboard</a>
      </li>
      <li class="main-nav__item" *ngIf="viewingQuote || viewingRmvService ">
        <a [tooltip]="'tooltip-main-menu-open'" class="main-nav__link o-link o-link--expand" (click)="actionOnOpenLinkClick($event)">Open</a>

        <div id="tooltip-main-menu-open" [class.tooltip--has-subcontent]="submenuOpen" class="tooltip  tooltip--arrowed-small tooltip--mm-open u-color-sun-juan">
          <ul class="tooltip__menu u-align-center u-color-pelorous">
            <li *ngIf="!formsOnlyUser" class="tooltip__menu-item" (mouseenter)="openSubmenu('submenuOpenQuotesIsVisible')"
              (mouseleave)="closeSubmenu('submenuOpenQuotesIsVisible')">
              <a [routerLink]="['/dashboard']" class="tooltip__menu-link tooltip__menu-link_block" title="">Quotes</a>
            </li>
            <li class="tooltip__menu-item">
              <a routerLink="/dashboard/clients" class="tooltip__menu-link tooltip__menu-link_block">Clients</a>
            </li>
            <li class="tooltip__menu-item">
              <a routerLink="/dashboard/forms" class="tooltip__menu-link tooltip__menu-link_block">Forms</a>
            </li>
            <li *ngIf="!formsOnlyUser" class="tooltip__menu-item">
              <!-- a routerLink="/dashboard/quote-inbox" class="tooltip__menu-link tooltip__menu-link_block">Leads Inbox</a -->
              <a [routerLink]="['/dashboard/quote-inbox']" class="tooltip__menu-link tooltip__menu-link_block">Leads
                Inbox</a>
            </li>
            <li class="tooltip__menu-item">
              <a routerLink="/dashboard/eStamp-requests" class="tooltip__menu-link tooltip__menu-link_block">eStamp Requests</a>
            </li>
          </ul>

          <div class="tooltip__subcontent u-align-left u-width-min-300px" [class.is-open]="submenuOpenQuotesIsVisible"
            (mouseenter)="openSubmenu('submenuOpenQuotesIsVisible')" (mouseleave)="closeSubmenu('submenuOpenQuotesIsVisible')">
            <h3 class="o-heading u-color-sun-juan u-spacing--1">Recent Quotes</h3>

            <app-loader [loading]="loadingQuotes" [cssClass]="'loader--with-opacity'" [loadingText]="'Updating, please wait...'"></app-loader>

            <table class="table table--no-last-border table--compact table--hoverable-grey u-spacing--1-5">
              <tbody class="table__tbody">
                <tr class="table__tr" *ngFor="let row of arrQuotesAll">
                  <td class="table__td">
                    <i class="o-icon o-icon--md o-icon--i-{{row.lob}} table__td-icon"></i>
                  </td>
                  <td class="table__td u-color-pelorous">
                    <a (click)="goToLink('/dashboard/quotes/' + row.resourceId)">
                      <span *ngIf="!row.client.firstName && !row.client.lastName">--</span>
                      <span *ngIf="row.client.firstName">{{row.client.firstName}}</span>
                      <span *ngIf="row.client.lastName">{{row.client.lastName}}</span>
                    </a>
                  </td>
                  <td class="table__td">{{row.client.City}}
                    <span *ngIf="row.client.State">, {{row.client.State}}</span>
                  </td>
                  <td class="table__td">{{row.lastModifiedDate}}</td>
                  <td class="table__td">{{row.agent}}</td>
                  <td class="table__td">
                    <div class="u-t-nowrap">{{row.description}}</div>
                  </td>
                </tr>
              </tbody>
            </table>

          </div>
          <!-- / .tooltip__menu-subcontent -->
        </div>
        <!-- / .tooltip -->

      </li>
      <li class="main-nav__item">
        <a [tooltip]="'tooltip-main-menu-new'" class="main-nav__link o-link o-link--expand">New</a>

        <div class="tooltip tooltip--arrowed-small tooltip--mm-new u-color-sun-juan" id="tooltip-main-menu-new">
          <ul class="tooltip__menu">

            <ng-container *ngIf="!formsOnlyUser">
              <li class="tooltip__menu-item">
                <a id="newAutoModalboxOpener" class="tooltip__menu-link tooltip__menu-link_block tooltip__menu-link_with-icon"
                  [userSubscriptionRating]="{lob: 'AUTOP', warningPopup: refSubscriptionInfoPopup, createFormContainer: refModalNewAuto, workflow: 'copyquote'}"
                  (showPopup)="setMsg($event, refCopyInfoPopup)">
                  <i class="o-icon o-icon--i-AUTOP o-icon--md tooltip__menu-icon tooltip__menu-icon_inner"></i>
                  Auto
                </a>
              </li>

              <li class="tooltip__menu-item">
                <a id="newHomeModalboxOpener" class="tooltip__menu-link tooltip__menu-link_block tooltip__menu-link_with-icon"
                  [userSubscriptionRating]="{lob: 'HOME', warningPopup: refSubscriptionInfoPopup, createFormContainer: refModalNewHome, workflow: 'copyquote'}"
                  (showPopup)="setMsg($event,refCopyInfoPopup)">
                  <i class="o-icon o-icon--i-HOME o-icon--md tooltip__menu-icon tooltip__menu-icon_inner"></i>
                  Home
                </a>
              </li>
              <li class="tooltip__menu-item">
                <a id="newDwellingModalboxOpener" class="tooltip__menu-link tooltip__menu-link_block tooltip__menu-link_with-icon"
                  [userSubscriptionRating]="{lob: 'DFIRE', warningPopup: refSubscriptionInfoPopup, createFormContainer: refModalNewDwelling, workflow: 'copyquote'}"
                  (showPopup)="setMsg($event,refCopyInfoPopup)">
                  <i class="o-icon o-icon--i-DFIRE o-icon--md tooltip__menu-icon tooltip__menu-icon_inner"></i>
                  Dwelling
                </a>
              </li>
              <li class="tooltip__menu-item">
                <a id="newUmbrellaModalboxOpener" class="tooltip__menu-link tooltip__menu-link_block tooltip__menu-link_with-icon"
                  [userSubscriptionRating]="{lob: 'PUMBR', warningPopup: refSubscriptionInfoPopup, createFormContainer: refModalNewUmbrella, workflow: 'copyquote'}"
                  (showPopup)="setMsg($event, refCopyInfoPopup)">
                  <i class="o-icon o-icon--i-PUMBR o-icon--md tooltip__menu-icon tooltip__menu-icon_inner"></i>
                  Umbrella
                </a>
              </li>
              <li class="tooltip__menu-item">
                <a id="newCommercialModalboxOpener" class="tooltip__menu-link tooltip__menu-link_block tooltip__menu-link_with-icon"
                  [userSubscriptionRating]="{lob: 'AUTOB', warningPopup: refSubscriptionCommercialInfoPopup, createFormContainer: refTooltipNewCommercial}"
                  (showPopup)="setMsg($event, refCopyInfoPopup)">
                  <i class="o-icon o-icon--i-AUTOB o-icon--md tooltip__menu-icon tooltip__menu-icon_inner"></i>
                  Commercial Auto
                </a>
              </li>
            </ng-container>

            <li class="tooltip__menu-item">
              <a [routerLink]="['/dashboard/clients/new']" class="tooltip__menu-link tooltip__menu-link_block tooltip__menu-link_with-icon ">
                <i class="o-icon o-icon--i-CLIENT o-icon--md tooltip__menu-icon tooltip__menu-icon_inner "></i>
                Client
              </a>
            </li>
            <li class="tooltip__menu-item">
              <a id="newFormModalboxOpener" class="tooltip__menu-link tooltip__menu-link_block tooltip__menu-link_with-icon"
                [userSubscription]="{code: 'Forms', warningPopup: refSubscriptionInfoPopup, createFormContainer: refModalNewForm}"
                (showPopup)="setMsg($event,null)">
                <i class="o-icon o-icon--i-FILE o-icon--md tooltip__menu-icon tooltip__menu-icon_inner"></i>
                Form
              </a>
            </li>
            <!-- END -->
          </ul>
        </div>
        <!-- / .tooltip -->
      </li>
    </ul>
    <ul class="main-nav__list">
      <li class="main-nav__item ">
        <a [tooltip]="'tooltip-main-menu-tools'" class="main-nav__link o-link o-link--expand"><i class="fas fa-toolbox" style="font-size: 14px;"></i> Tools</a>

        <div class="tooltip tooltip--arrowed-small tooltip--mm-tools u-color-sun-juan" id="tooltip-main-menu-tools">
          <ul class="tooltip__menu">
            <li class="tooltip__menu-item">
              <a class="tooltip__menu-link tooltip__menu-link_block" (click)="toolsOpenCalculatorModal()">
                <!-- i class="o-icon o-icon--i-CLIENT o-icon--md tooltip__menu-icon tooltip__menu-icon_inner "></i -->
                Prorate / Short Rate Calculator
              </a>
            </li>
            <li class="tooltip__menu-item">
              <a class="tooltip__menu-link tooltip__menu-link_block" (click)="toolsOpenVinLookupModal()">
                <!-- i class="o-icon o-icon--i-FILE o-icon--md tooltip__menu-icon tooltip__menu-icon_inner"></i -->
                VIN Lookup
              </a>
            </li>
          </ul>
        </div>
        <!-- / .tooltip -->
      </li>


    </ul>

    <ul class="main-nav__list">
      <li class="main-nav__item main-nav__item--user">
        <a [tooltip]="'tooltip-main-menu-settings'" class="main-nav__link o-link o-link--expand">
          <i class="icon-gear main-nav__icon"></i> {{getUserName}}
        </a>

        <div class="tooltip tooltip--arrowed-small tooltip--mm-settings u-color-sun-juan" id="tooltip-main-menu-settings">
          <ul class="tooltip__menu">
            <li class="tooltip__menu-item">
              <a [crossAppRouterLink]="'{env::domain}/webapps/SinglePointApp/'" target="_blank" class="tooltip__menu-link tooltip__menu-link_block">SinglePoint
                Settings</a>
            </li>
            <li class="tooltip__menu-item">
              <a class="tooltip__menu-link tooltip__menu-link_block" (click)="openDialog()">ISO Copyright</a>

            </li>
            <li class="tooltip__menu-item">
              <a class="tooltip__menu-link tooltip__menu-link_block" (click)="userLogout()">Logout</a>
            </li>
          </ul>
        </div>
        <!-- / .tooltip -->
      </li>
    </ul>

    <ul class="main-nav__list main-nav__list--last">
      <li class="main-nav__item main-nav__item--question">
        <a [tooltip]="'tooltip-main-menu-help'" class="main-nav__link">
          Help
        </a>

        <div class="tooltip tooltip--arrowed-small tooltip--mm-help" id="tooltip-main-menu-help">
          <ul class="tooltip__menu">
            <!--
            <li class="tooltip__menu-item">
              <a href="http://bostonsoftware.com/sprating" target="_blank" class="tooltip__menu-link tooltip__menu-link_block">SP Rating Help Page</a>
            </li>
            -->
            <li class="tooltip__menu-item">
              <a href="http://bostonsoftware.com" target="_blank" class="tooltip__menu-link tooltip__menu-link_block">Boston
                Software Home Page</a>
            </li>
            <li class="tooltip__menu-item">
              <a href="https://bostonsoftware.com/carriers/" target="_blank" id="mm-remote-assistance" class="tooltip__menu-link tooltip__menu-link_block">Carrier Quoting Guidelines</a>
            </li>
            <!--
            <li class="tooltip__menu-item">
              <a href="http://bostonsoftware.com/resourcecenter.php" target="_blank" class="tooltip__menu-link tooltip__menu-link_block">Boston Software Resource Page</a>
            </li>
            <li class="tooltip__menu-item">
              <a href="http://bostonsoftware.com/autotips.php" target="_blank" class="tooltip__menu-link tooltip__menu-link_block">Auto Quoting Guidelines</a>
            </li>
            -->
            <li class="tooltip__menu-item">
              <a (click)="IntSetupHelpLink()"  class="tooltip__menu-link tooltip__menu-link_block">Management System Integration Setups</a>
            </li>
            <li class="tooltip__menu-item">
              <a href="https://helpdesk.me/end-user-support/" target="_blank" id="mm-remote-assistance" class="tooltip__menu-link tooltip__menu-link_block">Start
                Remote Assistance</a>
            </li>

            <!-- <li class="tooltip__menu-item">
              <button #refSendToBscBtn type="button" id="send-to-bsc-msg" class="tooltip__menu-link">Ask Support a
                Question</button>
            </li> -->
          </ul>
        </div>
        <!-- / .tooltip -->
      </li>
    </ul>
  </nav>
</div>
<app-modalbox #modalSendToBscMsg [launcher]="'#send-to-bsc-msg'" [preventCloseOnBackdropClick]="true">
  <app-send-to-bsc *ngIf="modalSendToBscMsg.isOpen" [isMessage]="!viewingQuote" (confirmationEmail)="onSendToBscConfirmation(modalSendToBscMsg)"
    (cancel)="onSendToBscCancel(modalSendToBscMsg)">
  </app-send-to-bsc>
</app-modalbox>

<app-modalbox #refModalNewAuto [css]="'u-width-430px'">
  <app-new-auto-create-form *ngIf="refModalNewAuto.isOpen" (onCreateManuallyClick)="createManuallyClick($event, refModalNewAuto)"
    (onCancelClick)="cancelClick($event, refModalNewAuto)">
  </app-new-auto-create-form>
</app-modalbox>

<app-modalbox #refModalNewHome [css]="'u-width-430px'">
  <app-new-home-create-form *ngIf="refModalNewHome.isOpen" (onCreateManuallyClick)="createManuallyClick($event, refModalNewHome)"
    (onCancelClick)="cancelClick($event, refModalNewHome)">
  </app-new-home-create-form>
</app-modalbox>

<app-modalbox #refModalNewDwelling [css]="'u-width-430px'">
  <app-new-dwelling-create-form *ngIf="refModalNewDwelling.isOpen" (onCreateManuallyClick)="createManuallyClick($event, refModalNewDwelling)"
    (onCancelClick)="cancelClick($event, refModalNewDwelling)">
  </app-new-dwelling-create-form>
</app-modalbox>

<app-modalbox #refModalNewUmbrella [css]="'u-width-430px'">
  <app-new-umbrella-create-form *ngIf="refModalNewUmbrella.isOpen" (onCreateManuallyClick)="createManuallyClick($event, refModalNewUmbrella)"
    (onCancelClick)="cancelClick($event, refModalNewUmbrella)">
  </app-new-umbrella-create-form>
</app-modalbox>

<app-modalbox #refTooltipNewCommercial [css]="'u-width-430px'">
  <app-new-commercial-auto-create-form *ngIf="refTooltipNewCommercial.isOpen" (onCreateManuallyClick)="createManuallyClick($event, refTooltipNewCommercial)"
    (onCancelClick)="cancelClick($event, refTooltipNewCommercial)">
  </app-new-commercial-auto-create-form>
</app-modalbox>

<app-modalbox #refModalNewForm (onStateChange)="modalNewFormOnStateChange($event)" [css]="cssWidthForNewFormModal">
  <app-new-form-create *ngIf="refModalNewForm.isOpen" (createForm)="createFormClick($event, refModalNewForm)" (cancel)="cancelClick($event, refModalNewForm)"
    (formsTypeSelected)="setModalNewFormCssClass($event)">
  </app-new-form-create>
</app-modalbox>

<app-modalbox #refSubscriptionInfoPopup>
  <div class="box box--silver u-spacing--1-5">
    <p *ngFor="let message of subscriptionMessages">{{message}}</p>
  </div>
  <div class="row u-spacing--2">
    <div class="col-xs-12 u-align-right">
      <button type="button" (click)="actionModalSubscriptionInfoOk()" class="o-btn">OK</button>
    </div>
  </div>
</app-modalbox>


<app-modalbox #refCopyInfoPopup>
  <h1 class="o-heading o-heading--red">Prefill New Quote?</h1>
  <div class="box box--silver u-spacing--1-5">
    Would you like to use data from this quote to populate the new quote?
  </div>
  <div class="row u-spacing--2">
    <div class="col-xs-12 u-align-right">
      <button type="button" (click)="actionModalCopyQuoteYes()" class="o-btn ">Yes</button>
      <button type="button" (click)="actionModalCopyQuoteNo()" class="o-btn u-spacing--left-1">No</button>
    </div>
  </div>
</app-modalbox>


<app-modalbox #refModalToolsCalculator [css]="'u-width-435px'">
  <app-rate-calculator *ngIf="refModalToolsCalculator.isOpen" [refModalParent]="refModalToolsCalculator"></app-rate-calculator>
</app-modalbox>

<app-modalbox #refModalToolsVinLookup [css]="'u-width-450px'">
  <app-vin-lookup *ngIf="refModalToolsVinLookup.isOpen" [refModalParent]="refModalToolsVinLookup"></app-vin-lookup>
</app-modalbox>

            <p-dialog header="ISO Copyright" [(visible)]="visible" [styleClass]="'iso-copyright'" closable="false" position="topright" >
    <p style="line-height: normal;text-align: start;">This product ("the Product") includes information which is proprietary to Insurance Services Office, Inc. Use of the Product is limited to ISO Participating Insurers and their insurance agents acting on the insurers' behalf.  ISO Participating Insurers' use is limited to those jurisdictions and for those lines of insurance, jurisdictions and services for which such insurer is licensed by ISO.  ISO does not guarantee the accuracy or timeliness of the ISO information provided.  ISO shall not be liable for any loss or damage of any kind and howsoever caused resulting from your use of the ISO information.</p>
    <div class="flex justify-end gap-2 u-align-right">
        <button class="o-btn" (click)="visible = false" >Close</button>

    </div>
</p-dialog>
