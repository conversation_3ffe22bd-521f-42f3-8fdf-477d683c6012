
import {take, filter} from 'rxjs/operators';
import { ForgotIdSentComponent } from './../../../login/forgot-id-sent/forgot-id-sent.component';
import { BroadcastService } from 'app/shared/services/broadcast.service';
import { SubscriptionLike as ISubscription } from 'rxjs';
import { ModalboxComponent } from 'app/shared/components/modalbox/modalbox.component';
import { FilterOption } from 'app/app-model/filter-option';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { Component, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { Driver, RmvDriverResult, DFLDriver, DriverOptionsAndCoverages } from 'app/app-model/driver';
import { DatepickerInputComponent } from 'app/shared/components/datepicker-input/datepicker-input.component';

import { DriversService } from 'app/dashboard/app-services/drivers.service';
import { RmvService } from 'app/dashboard/app-services/rmv.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { QuoteNewRMVResponseData, Quote } from 'app/app-model/quote';

import { Coverage } from '../../../app-model/coverage';

import { parseISO, format } from 'date-fns';
const mmddyFormat = 'MM/dd/yyyy';

@Component({
    selector: 'app-overlay-rmv-reports-driver',
    templateUrl: './overlay-rmv-reports-driver.component.html',
    styleUrls: ['./overlay-rmv-reports-driver.component.scss'],
    standalone: false
})
export class OverlayRmvReportsDriverComponent implements OnInit, OnDestroy {
  @Input() params: string;

  private subscriptionRouter;
  private subscriptionStorage;
  private subscriptionGetRmvDriversLookup;
  private subscriptionQueryParams;
  private storageDriversSubscription;
  public selectedDriver: RmvDriverResult = new RmvDriverResult();
  private rmvDrivers: RmvDriverResult[] = [];

  private subscriptionBroadcastOpen: ISubscription;
  private dateFirstLicenseData: DFLDriver[] = [];
  private currentQuote: Quote;
  private currentDrivers: Driver[];
  public currentlySelectedDriver: DFLDriver;
  @ViewChild('modalRmvResultsDFL') public modalRmvResultsDFL: ModalboxComponent;
  @ViewChild('modalCustomDFL') public modalCustomDFL: ModalboxComponent;
  @ViewChild('refPickerDateFirstLic')
  public refPickerDateFirstLic: DatepickerInputComponent;

  customDate: string;

  constructor(
    private driverService: DriversService, // unused
    private storageService: StorageService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private rmvService: RmvService,
    private broadcastService: BroadcastService
  ) {}

  ngOnInit() {
    this.getRMVdriversFromStorage();
    this.updateDFLDriversModal();
    this.registerBroadcastGetDFLInfo();
    // On route change setSelectedDriver
    this.subscriptionRouter = this.router.events.pipe(
      filter(event => event instanceof NavigationEnd))
      .subscribe(event => {
        this.setSelectedDriver();
      });

    this.getCurrentQuote();
  }
  getCurrentQuote() {
    const subscription = this.storageService
      .getStorageData('selectedQuote')
      .subscribe(quote => {
        if (quote) {
          this.currentQuote = quote;
        }
        subscription && subscription.unsubscribe();
      });
  }

  private subscribeDrivers() {
    this.storageDriversSubscription &&
      this.storageDriversSubscription.unsubscribe();
    this.storageDriversSubscription = this.storageService
      .getStorageData('driversList')
      .subscribe(data => {
        this.currentDrivers = data;
      });
  }

  ngOnDestroy() {
    this.subscriptionStorage && this.subscriptionStorage.unsubscribe();
    this.subscriptionRouter && this.subscriptionRouter.unsubscribe();
    this.subscriptionGetRmvDriversLookup &&
      this.subscriptionGetRmvDriversLookup.unsubscribe();
    this.subscriptionQueryParams && this.subscriptionQueryParams.unsubscribe();
    this.subscriptionBroadcastOpen &&
      this.subscriptionBroadcastOpen.unsubscribe();
  }

  private registerBroadcastGetDFLInfo(): void {
    this.subscriptionBroadcastOpen = this.broadcastService
      .on('getDriverDFLInfo')
      .subscribe(data => {
        this.modalRmvResultsDFL.open();
      });
  }

  private getRMVdriversFromStorage(): void {
    this.subscriptionGetRmvDriversLookup = this.storageService
      .getStorageData('rmvDriversLookup')
      .subscribe(res => {
        this.rmvDrivers = res;
        this.setSelectedDriver();
      });
  }

  updateCustomDate(driver: DFLDriver) {
    const currentDriver = this.dateFirstLicenseData.find(
      d => d.resourceId === this.currentlySelectedDriver.resourceId
    );

    const dflOptions = Object.assign([], currentDriver.options);

    const customOptionInd = dflOptions.findIndex(
      d => d.text.includes('Custom') || d.text.includes('custom')
    );
    const existingCustomOptionInd = dflOptions.findIndex(d =>
      d.text.includes('(Custom)')
    );

    if (existingCustomOptionInd > -1) {
      dflOptions[existingCustomOptionInd] = {
        id: this.customDate,
        text: this.helpDisplayLossDate(this.customDate) + ' (Custom)'
      };
    } else {
      dflOptions.splice(customOptionInd, 0, {
        id: this.customDate,
        text: this.helpDisplayLossDate(this.customDate) + ' (Custom)'
      });
    }
    currentDriver.selectedOption = {
      id: this.customDate,
      text: this.helpDisplayLossDate(this.customDate) + ' (Custom)'
    };

    currentDriver.options = dflOptions;

    this.modalCustomDFL.close();
  }

  updateDFLDriversModal() {
    if (this.rmvDrivers) {
      this.subscribeDrivers();
      this.rmvDrivers.forEach(d => {
        const dflDriver = new DFLDriver();
        dflDriver.resourceId = d.resourceId;
        dflDriver.firstname = d.firstName;
        dflDriver.lastname = d.lastName;
        dflDriver.licenseNumber = d.licenseNumber;
        dflDriver.options = this.getDriverFirstLicOptions(d);
        const foundLicense = this.currentDrivers.find(
          dr => dr.licenseNumber === dflDriver.licenseNumber
        );
        if (foundLicense) {
          dflDriver.customDate = foundLicense.firstLicensed;
        }
        if (d.firstLicenseOptions) {
          const defaultOpt = d.firstLicenseOptions.find(dr => dr.selected);
          if (defaultOpt) {
            dflDriver.selectedOption = {
              id: defaultOpt.date ? defaultOpt.date : defaultOpt.label,
              text:
                (defaultOpt.date
                  ? this.helpDisplayLossDate(defaultOpt.date)
                  : defaultOpt.label) +
                (this.helpDisplayLossDate(defaultOpt.date)
                  ? ' (' + defaultOpt.label + ')'
                  : '')
            };
          }
        }
        this.dateFirstLicenseData.push(dflDriver);
      });
    }
  }
  public getDriverFirstLicOptions(driver: RmvDriverResult): FilterOption[] {
    if (driver.firstLicenseOptions) {
            let options = [];
      options = driver.firstLicenseOptions
        .filter(function(rmvOpt) {
          if (!rmvOpt.label.includes('Custom') && !rmvOpt.date) {
            return false;
          }
          return true;
        })
        .map(item => ({
          id: item.date ? item.date : item.label,
          text:
            (item.date ? this.helpDisplayLossDate(item.date) : item.label) +
            (this.helpDisplayLossDate(item.date) ? ' (' + item.label + ')' : '')
        }));

      return options;
    } else {
      const options = [];
      options.push({ 'id': 'Custom', 'text': 'Custom' });
      return options;
    }
  }

 public helpDisplayLossDate(date: string): string {
  return date ? format(new Date(date), 'MM/dd/yyyy') : '';
}

  public selectDriverDFL($event, driver: DFLDriver) {
    const newDFL = $event.id;
    this.currentlySelectedDriver = driver;
    if (newDFL) {
      if (newDFL.includes('custom') || newDFL.includes('Custom')) {
        this.modalCustomDFL.open();
        this.refPickerDateFirstLic.focus(false);
      } else {
        driver.selectedOption = { id: $event.id, text: $event.text };
      }
    }
  }

  public saveDFLRouteToQuote() {
    // Update dfl data from
    let driverResources;
    const promises = [];

    this.driverService
      .getDriversList(this.currentQuote.quoteSessionId).pipe(
      take(1))
      .subscribe(res => {
        driverResources = res.items;
        if (driverResources && driverResources.length > 0) {
          const updatedDrivers: Driver[] = [];
          let updatedCoverage: DriverOptionsAndCoverages[];
          driverResources.forEach(item => {
            if (item.resourceId && item.firstName) {
              const dfldriver = this.dateFirstLicenseData.find(
                dfl =>
                  dfl.firstname + dfl.lastname + dfl.licenseNumber ===
                  item.firstName + item.lastName + item.licenseNumber
              );
              if (dfldriver) {
                item.firstLicensed = dfldriver.selectedOption
                && dfldriver.selectedOption.id ? dfldriver.selectedOption.id : '';
              }
              updatedDrivers.push(item);
              const prom = this.driverService
                .updateDriverByUri(item.meta.href, item)
                .toPromise();
              promises.push(prom);
            }
          });

          let driverOptionsAndCoverages: DriverOptionsAndCoverages[];
          this.storageService.getStorageData('autoDriversOptionsAndCoverages')
                .subscribe(coverageData => {
                  driverOptionsAndCoverages = coverageData;
                  updatedCoverage = driverOptionsAndCoverages;
                });

          if (updatedDrivers) {
            updatedDrivers.forEach((driver, index) => {
              const coverageProm = this.driverService.getDriverOptions(driver.coverages.meta.href).toPromise().then(data => {
                data.items.forEach((items) => {
                  if (items.coverages.length > 0) {
                    if (driverOptionsAndCoverages[index] && driverOptionsAndCoverages[index].options) {
                  driverOptionsAndCoverages[index].options.forEach((driverOptions, i) => {
                    const driversEd = items.coverages.find(x => x.coverageCode === 'DriversEd');
                    if (driversEd && driversEd.values.length > 0 && driverOptions.isActive.toString() !== driversEd.values[0]?.value
                      && driverOptions.coverageCode === driversEd.coverageCode) {
                      updatedCoverage[index].driverResourceId = driverOptionsAndCoverages[index].driverResourceId;
                      updatedCoverage[index].options[i].isActive = JSON.parse(driversEd.values[0]?.value);
                    }
                    });
                  }
                  }
                });
              });
              promises.push(coverageProm);
            });
          }

          Promise.all(promises).then(() => {
            if (updatedDrivers) {
              this.storageService.setStorageData(
                'driversHelperList',
                updatedDrivers
              );
            }

            if (updatedCoverage) {
              this.storageService.setAutoDriversOptionsAndCoverages(updatedCoverage);
            }


            // broadcast to apply changes
            this.broadcastService.broadcast('overlay.rmv.dfl.applychanges');
          });
        }
      });
  }

  public updateCustomDateChange(date) {
    if (date) {
      this.customDate = date;
    } else {
      this.customDate = null;
    }
  }

  closeModalboxCustomDate() {
    this.customDate = '';
    this.modalCustomDFL.close();
  }

  private setSelectedDriver(): void {
    this.subscriptionQueryParams = this.activatedRoute.queryParams.subscribe(
      params => {
        // console.log('Activated Route Params', params);

        if (params['id']) {
          const id = params['id'];

          const driverToSelect = this.rmvDrivers.find(driver => {
            // let tmpId = driver.firstName + '-' + driver.lastName + '-' + driver.licenseNumber;
            const tmpId = this.rmvService.generateRmvReportDriverId(driver);
            return tmpId === id;
          });

          this.selectedDriver = driverToSelect || new RmvDriverResult();
        } else if (this.rmvDrivers && this.rmvDrivers.length) {
          this.selectedDriver = this.rmvDrivers[0];
        }

        // this.currentDFlOptions = this.getDFLOptions(this.selectedDriver);

        if (
          this.router.url.indexOf('/rmv-results') !== -1 &&
          this.rmvDrivers &&
          this.rmvDrivers.length === 0
        ) {
          this.subscriptionStorage = this.storageService
            .getStorageData('responseNewRmvQuoteResult')
            .subscribe((res: QuoteNewRMVResponseData) => {
              console.log('1: >>>>', res);
              this.rmvDrivers = res ? res.drivers : []; // res.drivers;
              this.setSelectedDriver();
            });
        }
      }
    );
  }

  public get hasIncidents() {
    return (
      this.selectedDriver.incidents && this.selectedDriver.incidents.length > 0
    ); //  false;//this.selectedDriver && this.selectedDriver.incidents // && this.selectedDriver.incidents.length;
  }

  public get isOnlyWarrningInfo(): boolean {
    return this.rmvDrivers &&
      this.rmvDrivers.length === 1 &&
      !this.rmvDrivers[0].firstName &&
      !this.rmvDrivers[0].lastName &&
      !this.rmvDrivers[0].licenseNumber
      ? true
      : false;
  }

  public get selectedDriverName(): string {
    let name = '';

    if (this.selectedDriver.firstName) {
      name += this.selectedDriver.firstName;
    }

    if (this.selectedDriver.middleName) {
      name += ' ' + this.selectedDriver.middleName;
    }

    if (this.selectedDriver.lastName) {
      name += ' ' + this.selectedDriver.lastName;
    }

    return name ? name : '-';
  }

  public displayIfNotPresent(value: string): string {
    return value ? value : '-';
  }

  private displayEmptyValue(value: string): string {
    return !value ? '-' : '';
  }

  public doNotDisplayFirstLicenseSetMessage(value: string): boolean {
    if (value.startsWith('First License Date')) {
      return false;
    }

    return true;
  }

  public highlightData(value: string): boolean {
    if (Date.parse(value) < Date.parse('01/01/1976')) {
      return true;
    }

    return false;
  }
}
