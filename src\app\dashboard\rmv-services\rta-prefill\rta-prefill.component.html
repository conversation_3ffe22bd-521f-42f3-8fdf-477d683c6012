<br>
<i style="font-style: italic; padding-left:10px">Select the transaction and complete the fields to prefill data from the
  RMV</i>
<form #form="ngForm" (ngSubmit)="prefillFromRMV()">

<app-type-selection></app-type-selection>
<app-vehicle-selection [hidden]="!typeSelection.type" [type]="typeSelection.type"></app-vehicle-selection>
<app-owners-selection
  *ngIf=" vehicleSelection && vehicleSelection.vehicle.ownership && vehicleSelection.vehicle.ownership !== 'BusinessOwned'">
</app-owners-selection>
<app-business-owner-selection
  *ngIf=" vehicleSelection && vehicleSelection.vehicle.ownership && vehicleSelection.vehicle.ownership === 'BusinessOwned'">
</app-business-owner-selection>
<app-lienholder-selection
  *ngIf="vehicleSelection && vehicleSelection.vehicle.ownership && vehicleSelection.vehicle.ownership === 'Financed'">
</app-lienholder-selection>
<app-lessor-selection
  *ngIf="vehicleSelection && vehicleSelection.vehicle.ownership && vehicleSelection.vehicle.ownership === 'Leased'">
</app-lessor-selection>
<app-garaging-address [hidden]="!typeSelection.type" [type]="typeSelection.type"></app-garaging-address>
<app-purchase-and-sales [hidden]="!typeSelection.type" [type]="typeSelection.type"></app-purchase-and-sales>
<app-e-stamp-get-ready [hidden]="!typeSelection.type" [type]="typeSelection.type"></app-e-stamp-get-ready>
<div class="row u-spacing--1-5" [hidden]="hideButtonsUntilConditionMet()">
  <div class="col-xs-12 u-remove-letter-spacing" style="padding-left: 40px;">
    <button type="submit" [disabled]="form.invalid"  class="o-btn">PREFILL FROM RMV</button>
    <button type="button" (click)="SkpToForm()" class="o-btn o-btn--outlined u-spacing--left-2"
      style="margin-left:1.8rem;">SKIP TO FORM</button>
      <button type="button" (click)="resetModels()"  class="o-btn o-btn--outlined u-spacing--left-2"
      style="margin-left:1.8rem;">CLEAR FORM</button>
  </div>
</div>
</form>
<app-modalbox #rmvResponseModal>
  <app-rmv-response-modal #response  [messages]="messages" [notifications]="notifications" [formCreateModel]="createFormModel" [getReadyModel]="getReadyRequest"
    [modalbox]="rmvResponseModal"></app-rmv-response-modal>
    <div class="row u-spacing--1-5">
      <div class="col-xs-12 u-align-right u-remove-letter-spacing">
          <button type="button" class="o-btn" *ngIf="!IsGetReadyEligible" (click)="response.goToRTA()">GO TO RTA</button>
          <button type="button" *ngIf="IsGetReadyEligible" class="o-btn" (click)="submitToRmv()">SUBMIT TO RMV</button>
          <button type="button" *ngIf="IsGetReadyEligible" class="o-btn o-btn--idle u-spacing--left-2" (click)="response.goToRTA()">SKIP TO FORM</button>                  
        <button type="button" (click)="response.onCancel()" class="o-btn o-btn--idle u-spacing--left-2" style="margin-left:1.8rem;">Go Back</button>
      </div>
    </div>
</app-modalbox>
<app-modalbox #getReadyResponseModal [css]="'u-width-650px'">
  <app-get-ready-success [modalbox]="getReadyResponseModal" [responseData]="getReadyResponse"></app-get-ready-success>
</app-modalbox>
<app-modalbox #getReadyErrorModal>
  <img src="assets/images/common/rmv_getready_logo.png" style="height: 25px;">
<br>
    
            <div class="box box--silver" style="margin-bottom: 20px;">
                <p><b>Transaction:</b> {{getReadyResponse?.transactionType}}</p>
                <p><b>Status:</b>{{getReadyResponse?.resultStatus.status}}</p>
                <p><b>Please review the following error and try again:</b></p>
              <div *ngFor="let msg of getReadyResponse?.resultStatus?.message" style="padding-bottom:.4rem">
                <p [innerHTML]="msg" style="white-space:pre-wrap"></p>
                </div>
               </div> 
               <div class="col-xs-12 u-align-right u-remove-letter-spacing">
                <button type="button" class="o-btn" (click)="response.goToRTA()">GO TO RTA</button>               
              <button type="button" (click)="getReadyErrorModal.close()" class="o-btn o-btn--idle u-spacing--left-2" style="margin-left:1.8rem;">Go Back</button>
            </div>

</app-modalbox>


