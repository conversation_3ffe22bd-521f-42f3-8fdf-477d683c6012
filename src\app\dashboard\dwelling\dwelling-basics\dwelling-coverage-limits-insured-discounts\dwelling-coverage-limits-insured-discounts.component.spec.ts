import { async, ComponentFixture, fakeAsync, inject, TestBed, tick } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';

import { TextMaskModule } from 'angular2-text-mask/dist/angular2TextMask';

import { DWELLING_COVERAGES } from 'testing/data/quotes/coverages/dwelling';
import { DWELLINGS } from 'testing/data/quotes/dwellings';
import { DWELLING_QUOTE } from 'testing/data/quotes/quote-dwelling';
import {
    DWELLING_POLICY_COVERAGES_STANDARD
} from 'testing/data/specs/rating-coverages/DFIRE/policy-coverages-standard';
import { changeTextInputValue, getDebugEl } from 'testing/helpers/all';
import { StubAutocompleteComponent } from 'testing/stubs/components/autocomplete.component';
import { StubModalboxComponent } from 'testing/stubs/components/modalbox.component';
import { StubSelectComponent } from 'testing/stubs/components/select.component';
import { StubClientsServiceProvider } from 'testing/stubs/services/clients.service.provider';
import {
    StubCoveragesService, StubCoveragesServiceProvider
} from 'testing/stubs/services/coverages.service.provider';
import { StubDwellingServiceProvider } from 'testing/stubs/services/dwelling.service.provider';
import { StubMoneyServiceProvider } from 'testing/stubs/services/money.service.provider';
import { StubOptionsServiceProvider } from 'testing/stubs/services/options.service.provider';
import {
    StubOverlayLoaderServiceProvider
} from 'testing/stubs/services/overlay-loader.service.provider';

import { ApiResponse } from 'app/app-model/_common';
import { CoverageItem, CoveragesData } from 'app/app-model/coverage';
import { CoveragesService } from 'app/dashboard/app-services/coverages.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Helpers } from 'app/utils/helpers';

import {
    DwellingCoverageLimitsInsuredDiscountsComponent
} from './dwelling-coverage-limits-insured-discounts.component';
import { Observable } from 'rxjs';

describe('Component: DwellingCoverageLimitsInsuredDiscounts', () => {
  let component: DwellingCoverageLimitsInsuredDiscountsComponent;
  let fixture: ComponentFixture<DwellingCoverageLimitsInsuredDiscountsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [TextMaskModule, FormsModule],
      declarations: [
        DwellingCoverageLimitsInsuredDiscountsComponent,
        StubAutocompleteComponent,
        StubModalboxComponent,
        StubSelectComponent
      ],
      providers: [
        StorageService,
        StubDwellingServiceProvider,
        StubCoveragesServiceProvider,
        StubOverlayLoaderServiceProvider,
        StubClientsServiceProvider,
        StubMoneyServiceProvider,
        StubOptionsServiceProvider
      ]
    })
    .compileComponents();
  }));

  describe('when all data is available in storage', () => {
    let apiCommonService: ApiCommonService;

    beforeEach(fakeAsync(inject([StorageService, ApiCommonService],
      (storageService: StorageService, _apiCommonService: ApiCommonService) => {
        storageService.setStorageData('selectedQuote', Helpers.deepClone(DWELLING_QUOTE));
        storageService.setStorageData('homeStandardCoveragesList', Helpers.deepClone(DWELLING_POLICY_COVERAGES_STANDARD.items));
        storageService.setStorageData('homeQuoteCoverages', Helpers.deepClone(DWELLING_COVERAGES));
        storageService.setStorageData('dwellingQuoteOptions', Helpers.deepClone(DWELLING_COVERAGES));
        storageService.setStorageData('dwelling', Helpers.deepClone(DWELLINGS.items[0]));

        apiCommonService = _apiCommonService;
        spyOn(apiCommonService, 'putByUri').and.callFake((url, data) => {
          return Observable.of(data);
        });

        fixture = TestBed.createComponent(DwellingCoverageLimitsInsuredDiscountsComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
        tick();
    })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });

    it('should destroy without errors', () => {
      expect(() => {
        fixture.destroy();
      }).not.toThrow();
    });

    it('should update coverage limits when dwelling coverage changes', fakeAsync(() => {
      const inputDE = getDebugEl(fixture, '#field_DWELL');
      const input = inputDE.nativeElement;

      inputDE.triggerEventHandler('focus', {
        srcElement: input,
        target: input,
        type: 'focus'
      });
      fixture.detectChanges();
      tick();

      changeTextInputValue(input, '250000', fixture);

      inputDE.triggerEventHandler('blur', {
        srcElement: input,
        target: input,
        type: 'blur'
      });
      fixture.detectChanges();
      tick(300);

      expect(apiCommonService.putByUri).toHaveBeenCalledWith(
        '/quotes/2cb115ad-1d40-4415-8109-05b6e9be9963/coverages/03a4d547-3f02-43cd-8784-ecb749ece847',
        jasmine.objectContaining({
          coverages: jasmine.arrayContaining([
            jasmine.objectContaining({
              coverageCode: 'DWELL',
              values: [
                jasmine.objectContaining({
                  value: '250000'
                })
              ]
            })
          ])
        }));
    }));

    it('should update coverage limits when personal liability coverage changes', fakeAsync(() => {
      const autocomplete: StubAutocompleteComponent = getDebugEl(fixture, '#field_PL').componentInstance;

      autocomplete.activeOption = component.coveragePersonalLiabilityOptions[2];
      fixture.detectChanges();
      tick(300);

      expect(apiCommonService.putByUri).toHaveBeenCalledWith(
        '/quotes/2cb115ad-1d40-4415-8109-05b6e9be9963/coverages/03a4d547-3f02-43cd-8784-ecb749ece847',
        jasmine.objectContaining({
          coverages: jasmine.arrayContaining([
            jasmine.objectContaining({
              coverageCode: 'PL',
              values: [
                jasmine.objectContaining({
                  value: '200000'
                })
              ]
            })
          ])
        }));
    }));

    it('should update quote options when inflation percentage changes', fakeAsync(() => {
      const autocomplete: StubAutocompleteComponent = getDebugEl(fixture, '#inflation').componentInstance;

      autocomplete.activeOption = component.coverageInflationOptions[1];
      fixture.detectChanges();
      tick(300);

      expect(apiCommonService.putByUri).toHaveBeenCalledWith(
        '/quotes/2cb115ad-1d40-4415-8109-05b6e9be9963/coverages/03a4d547-3f02-43cd-8784-ecb749ece847',
        jasmine.objectContaining({
          coverages: jasmine.arrayContaining([
            jasmine.objectContaining({
              coverageCode: 'BSC-DFIRE-000243',
              values: [
                jasmine.objectContaining({
                  value: '2'
                })
              ]
            })
          ])
        }));
    }));
  });

  describe('when only basic data is available in storage', () => {
    beforeEach(fakeAsync(inject([StorageService, CoveragesService],
      (storageService: StorageService, coveragesService: StubCoveragesService) => {
        storageService.setStorageData('selectedQuote', Helpers.deepClone(DWELLING_QUOTE));
        storageService.setStorageData('dwelling', Helpers.deepClone(DWELLINGS.items[0]));
        storageService.setStorageData('homeStandardCoveragesList', Helpers.deepClone(DWELLING_POLICY_COVERAGES_STANDARD.items));

        coveragesService.homeQuoteCoverages = Helpers.deepClone(DWELLING_COVERAGES);

        fixture = TestBed.createComponent(DwellingCoverageLimitsInsuredDiscountsComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
        tick();
      })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('when empty homeQuoteCoverages is available in storage', () => {
    beforeEach(fakeAsync(inject([StorageService, CoveragesService],
      (storageService: StorageService, coveragesService: StubCoveragesService) => {
        const homeQuoteCoverages = new ApiResponse<CoveragesData>();
        homeQuoteCoverages.meta.href = 'anything';

        storageService.setStorageData('selectedQuote', Helpers.deepClone(DWELLING_QUOTE));
        storageService.setStorageData('homeQuoteCoverages', homeQuoteCoverages);
        storageService.setStorageData('dwelling', Helpers.deepClone(DWELLINGS.items[0]));
        storageService.setStorageData('homeStandardCoveragesList', Helpers.deepClone(DWELLING_POLICY_COVERAGES_STANDARD.items));

        coveragesService.homeQuoteCoverages = Helpers.deepClone(DWELLING_COVERAGES);

        fixture = TestBed.createComponent(DwellingCoverageLimitsInsuredDiscountsComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
        tick();
      })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });
  });

});
