<section class="section section--compact">
  <div class="row">
    <div class="col-xs-12 u-spacing--1">
      <div class="o-container o-container--scroll-horizontal">

        <table class="table table--row-border-less" style="width:auto">
            <thead>
                <tr>
                  <th><div class="u-width-min-190px"></div></th>
                  <th class="u-width-min-190px">Limits</th>
                  <th class="u-width-min-190px">Symbols</th>
                </tr>
            </thead>
          <tbody>

            <tr *ngFor="let c of coveragesToShow.coverageItemInfo; let i = index" >

                  <td>
                    <span *ngIf="c.tabbed">
                     &nbsp;&nbsp;&nbsp;
                  </span>

                    {{c.description}}</td>


              <td class="u-align-center" >
                <span *ngIf="c.onlyOneAvailableOption">
                    {{c.currentValue}}
                </span>

                <sm-autocomplete
                  *ngIf="!c.onlyOneAvailableOption"
                  [fieldAutofocus]="i === 1"
                  #selectCoverageInput
                  [options]="c.options"
                  [(activeOption)]="c.currentValue"
                  [id]="c.coverageCode"
                  [name]="c.coverageCode"
                  [required]="true"
                  [disabled]=""
                  [searchFromBegining]="true"
                  (onSelect)="onCoverageItemSelected($event, c)">
              </sm-autocomplete>
              </td>
              <td *ngIf="c.containSymbols === false" class="u-align-center"></td>
              <td *ngIf="c.containSymbols" class="u-align-center">
                  <span>
                    {{c.selectedSymbol.length? c.selectedSymbol + ' - ' : 'None -'}}
                  </span>
                  <a class="o-link o-link--blue" id="edit-symbol-{{c.coverageCode}}" (click)="symbolsModalOpen($event, c)">Edit</a>
              </td>
            </tr>

          </tbody>
        </table>

        <app-modalbox #refModalEditSymbols>
            <ng-container *ngIf="refModalEditSymbols.isOpen">
            <h1 class="o-heading o-heading--red">
              {{selectedSymbol.description}} Symbols
            </h1>
            <div class="box box--silver u-spacing--1-5">
              <div class="row o-container">
                <div class="col-xs-2">
                    <table class="form-table">
                      <tr *ngFor="let symbol of selectedSymbol.symbols | slice:0:4" class="form-table__row">
                        <td class="form-table__cell">
                            <span class="u-cursor-default" (click)="symbolActionToggle(symbol)">
                              <i class="o-btn o-btn--checkbox" [ngClass]="{'is-active': symbol.selected}"></i>
                              {{symbol.value}}
                            </span>
                          </td>
                      </tr>
                    </table>
                </div>
                <div class="col-xs-2">
                    <table class="form-table">
                      <tr *ngFor="let symbol of selectedSymbol.symbols | slice:4:10" class="form-table__row">
                        <td class="form-table__cell">
                            <span class="u-cursor-default" (click)="symbolActionToggle(symbol)">
                              <i class="o-btn o-btn--checkbox" [ngClass]="{'is-active': symbol.selected}"></i>
                              {{symbol.value}}
                            </span>
                          </td>
                      </tr>
                    </table>
                </div>
              </div>
            </div>
            <div class="row u-spacing--2">
                <div class="col-xs-12 u-align-right">
                  <button type="button" class="o-btn" (click)="modalActionSave()">Save</button>
                  <button type="button" class="o-btn o-btn--idle u-spacing--left-2"
                    (click)="refModalEditSymbols.close()">Cancel</button>
                </div>
              </div>
            </ng-container>
        </app-modalbox>

      </div>
    </div>
  </div>
</section>

<app-modalbox #modalCoveragesValidation>
  <h1 class="o-heading o-heading--red u-spacing--bottom-1-5">Coverages Information</h1>

      <div *ngFor="let msg of errorMsg; let i = index">
        <p>{{ msg }} </p>
      </div>
      <div class="row u-spacing--2">
        <div class="col-xs-12 u-align-right">
          <hr class="o-hr u-spacing--bottom-1-5"/>
          <button (click)="modalCoveragesValidation.close()" class="o-btn o-btn">OK</button>
        </div>
      </div>
  </app-modalbox>
