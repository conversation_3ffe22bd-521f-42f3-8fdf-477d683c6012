import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { StubOptionsViewComponent } from 'testing/stubs/components/options-view.component';

import { StorageService } from 'app/shared/services/storage-new.service';

import { HomeCarrierOptionsComponent } from './home-carrier-options.component';

describe('Component: HomeCarrierOptions', () => {
  let component: HomeCarrierOptionsComponent;
  let fixture: ComponentFixture<HomeCarrierOptionsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [
        HomeCarrierOptionsComponent,
        StubOptionsViewComponent
      ],
      providers: [
        StorageService
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(HomeCarrierOptionsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });
});
