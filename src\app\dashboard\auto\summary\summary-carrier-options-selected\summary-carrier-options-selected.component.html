<div class="row">
  <div class="col-xs-12">

    <div class="summarybox">
      <div class="summarybox__body">
        <div class="summarybox__body-col u-width-100">

          <div class="summarybox__item" *ngFor="let option of arrSelectedOptions">
            <div class="summarybox__item-label u-color-sun-juan u-width-170px">{{option.label}}:</div>
            <div class="summarybox__item-value u-color-sun-juan u-width-auto">
              <p class="sumarybox__subitem" *ngFor="let val of option.options">{{val}}</p>
            </div>
          </div>

        </div>
      </div>
    </div>

  </div>
</div>