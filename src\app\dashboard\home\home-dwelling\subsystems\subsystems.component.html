<section class="section section--compact u-spacing--2-5">
  <div class="row">
    <div class="col-xs-12">
      <h1 class="o-heading">Subsystems And Protection Information</h1>
    </div>
  </div>

  <div class="row u-spacing--1">
    <div class="col-xs-12">
      <div class="box box--silver">
        <div class="row o-columns">
          <div class="col-xs-7">
            <table class="form-table">
              <tr class="form-table__row">
                <td class="form-table__cell u-width-120px u-spacing--bottom-1 u-color-slate-grey">SUBSYSTEM</td>
                <td class="form-table__cell u-width-160px"></td>
                <td class="form-table__cell u-padd--left-1 u-spacing--bottom-1 u-color-slate-grey u-width-120px">YEAR UPDATED</td>
              </tr>

              <tr class="form-table__row">
                <td class="form-table__cell u-width-120px" [ngClass]="{'is-required-field': fieldPrimaryHeatIsRequired()}">
                  <label for="">Primary Heat:</label>
                </td>
                <td class="form-table__cell u-width-160px" [ngClass]="{'is-required-field': fieldPrimaryHeatIsRequired()}">
                  <sm-autocomplete
                    [id]="'heatingPrimaryTypeCode'"
                    [name]="'heatingPrimaryTypeCode'"
                    [options]="heatingType"
                    [activeOption]="subsystemItems.heatingPrimaryTypeCode"
                    [placeholder]="'Please select'"
                    (onSelect)="selected($event, subsystemItems, 'heatingPrimaryTypeCode')">
                  </sm-autocomplete>
                </td>
                <td class="form-table__cell">
                  <input
                    class="u-width-70px"
                    type="number"
                    id="heatingLastUpdatedYear"
                    name="heatingLastUpdatedYear"
                    #refHeatingLastUpdatedYear="ngModel"
                    [(ngModel)]="subsystemItems.heatingPrimaryLastUpdatedYear"
                    (change)="validateAndSaveSubsystems($event)">
                </td>
              </tr>

              <tr class="form-table__row">
                <td class="form-table__cell u-width-120px" [ngClass]="{'is-required-field': fieldSecondaryHeatIsRequired()}">
                  <label for="">Sec. Heat:</label>
                </td>
                <td class="form-table__cell u-width-160px" [ngClass]="{'is-required-field': fieldSecondaryHeatIsRequired()}">
                  <sm-autocomplete
                    [id]="'heatingSecondaryTypeCode'"
                    [name]="'heatingSecondaryTypeCode'"
                    [options]="heatingType"
                    [activeOption]="subsystemItems.heatingSecondaryTypeCode"
                    [placeholder]="'Please select'"
                    (onSelect)="selected($event, subsystemItems, 'heatingSecondaryTypeCode')">
                  </sm-autocomplete>
                </td>
                <td class="form-table__cell u-width-70px"></td>
              </tr>

              <tr class="form-table__row">
                <td class="form-table__cell u-width-120px" [ngClass]="{'is-required-field': fieldPlumbingIsRequired()}">
                  <label for="">Plumbing:</label>
                </td>
                <td class="form-table__cell u-width-160px" [ngClass]="{'is-required-field': fieldPlumbingIsRequired()}">
                  <sm-autocomplete
                    [id]="'plumbingMaterialTypeCode'"
                    [name]="'plumbingMaterialTypeCode'"
                    [options]="plumbingMaterialType"
                    [activeOption]="subsystemItems.plumbingMaterialTypeCode"
                    [placeholder]="'Please select'"
                    (onSelect)="selected($event, subsystemItems, 'plumbingMaterialTypeCode')">
                  </sm-autocomplete>
                </td>
                <td class="form-table__cell u-width-70px">
                  <input
                    class="u-width-70px"
                    type="number"
                    id="plumbingLastUpdatedYear"
                    name="plumbingLastUpdatedYear"
                    #refPlumbingLastUpdatedYear="ngModel"
                    [(ngModel)]="subsystemItems.plumbingLastUpdatedYear"
                    (change)="validateAndSaveSubsystems($event)">
                </td>
              </tr>

              <tr class="form-table__row">
                <td class="form-table__cell u-width-120px" [ngClass]="{'is-required-field': fieldWiringTypeIsRequired()}">
                  <label for="">Wiring Type:</label>
                </td>
                <td class="form-table__cell u-width-160px" [ngClass]="{'is-required-field': fieldWiringTypeIsRequired()}">
                  <sm-autocomplete
                    [id]="'electricalWiringTypeCode'"
                    [name]="'electricalWiringTypeCode'"
                    [options]="electricalWiringType"
                    [activeOption]="subsystemItems.electricalWiringTypeCode"
                    [placeholder]="'Please select'"
                    (onSelect)="selected($event, subsystemItems, 'electricalWiringTypeCode')">
                  </sm-autocomplete>
                </td>
                <td class="form-table__cell u-width-70px">
                  <input
                    class="u-width-70px"
                    type="number"
                    id="electricalLastUpdatedYear"
                    name="electricalLastUpdatedYear"
                    #refElectricalLastUpdatedYear="ngModel"
                    [(ngModel)]="subsystemItems.electricalLastUpdatedYear"
                    (change)="validateAndSaveSubsystems($event)">
                </td>
              </tr>

              <tr class="form-table__row">
                <td class="form-table__cell u-width-120px" [ngClass]="{'is-required-field': fieldRoofTypeIsRequired()}">
                  <label for="">Roof Type:</label>
                </td>
                <td class="form-table__cell u-width-160px" [ngClass]="{'is-required-field': fieldRoofTypeIsRequired()}">
                  <sm-autocomplete
                    [id]="'roofMaterialTypeCode'"
                    [name]="'roofMaterialTypeCode'"
                    [options]="roofMaterialType"
                    [activeOption]="subsystemItems.roofMaterialTypeCode"
                    [placeholder]="'Please select'"
                    (onSelect)="selected($event, subsystemItems, 'roofMaterialTypeCode')">
                  </sm-autocomplete>
                </td>
                <td class="form-table__cell u-width-70px">
                  <input
                    class="u-width-70px"
                    type="number"
                    id="roofLastUpdatedYear"
                    name="roofLastUpdatedYear"
                    #refRoofLastUpdatedYear="ngModel"
                    [(ngModel)]="subsystemItems.roofLastUpdatedYear"
                    (change)="validateAndSaveSubsystems($event); validateCarrierOptionForSafecoPlan(subsystemItems.roofLastUpdatedYear) ">
                </td>
              </tr>

              <tr class="form-table__row" [class.is-required-field]="fieldOilTankLocationIsRequired()">
                <td class="form-table__cell u-width-120px">
                  <label for="">Oil Tank Loc.:</label>
                </td>
                <td class="form-table__cell" colspan="2">
                  <div class="u-width-230px">
                    <sm-autocomplete
                      [id]="'oilStorageTankLocation'"
                      [name]="'oilStorageTankLocation'"
                      [options]="oilStorageTankLocation"
                      [activeOption]="subsystemItems.oilStorageTankLocation"
                      [placeholder]="'Please select'"
                      (onSelect)="selected($event, subsystemItems, 'oilStorageTankLocation')">
                    </sm-autocomplete>
                  </div>
                </td>
                <!-- <td class="form-table__cell u-width-20px"></td> -->
              </tr>

            </table>


            <app-modalbox
              [isOpen]="yearOfUpdateModalVisible"
              [css]="'u-width-380px tight'">

              <p class="u-spacing--1">
                Year of Update cannot be prior to Year built.
              </p>

              <div class="row u-spacing--2">
                <div class="col-xs-12 u-align-right">
                  <button class="o-btn" (click)="yearOfUpdateModalVisible = false;">
                    Close
                  </button>
                </div>
              </div>
            </app-modalbox>

            <app-modalbox
              [isOpen]="futureYearOfUpdateModalVisible"
              [css]="'u-width-380px tight'">

              <p class="u-spacing--1">
                Year of Update cannot be in the future.
              </p>

              <div class="row u-spacing--2">
                <div class="col-xs-12 u-align-right">
                  <button class="o-btn" (click)="futureYearOfUpdateModalVisible = false;">
                    Close
                  </button>
                </div>
              </div>
            </app-modalbox>

          </div>
          <div class="col-xs-5">
            <app-protection-class></app-protection-class>
          </div>
        </div>
      </div>
    </div>
  </div>

</section>
