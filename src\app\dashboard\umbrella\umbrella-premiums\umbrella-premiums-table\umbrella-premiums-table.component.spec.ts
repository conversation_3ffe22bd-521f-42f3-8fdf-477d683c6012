import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { UmbrellaPremiumsTableComponent } from './umbrella-premiums-table.component';
import { PositionFixedDirective } from 'app/shared/directives/position-fixed.directive';
import { StubFilterComponent } from 'testing/stubs/components/filter.component';
import { StubLoaderComponent } from 'testing/stubs/components/loader.component';
import { StubModalboxComponent } from 'testing/stubs/components/modalbox.component';
import { StubOrderByPipe } from 'testing/stubs/pipes/order-by.pipe';
import { StubHintsAndWarningsLinkComponent } from 'testing/stubs/components/hints-and-warnings-link.component';
import { StubPerfectScrollbarComponent } from 'testing/stubs/components/perfect-scrollbar.component';
import { HintsAndWarningsService } from 'app/hints-and-warnings/services/hints-and-warnings.service';
import { RouterTestingModule } from '@angular/router/testing';
import { StubOverlayLoaderServiceProvider } from 'testing/stubs/services/overlay-loader.service.provider';
import { StubRateServiceProvider } from 'testing/stubs/services/rate.service.provider';
import { StorageService } from 'app/shared/services/storage-new.service';
import { StubPremiumsServiceProvider } from 'testing/stubs/services/premiums.service.provider';

describe('UmbrellaPremiumsTableComponent', () => {
  let component: UmbrellaPremiumsTableComponent;
  let fixture: ComponentFixture<UmbrellaPremiumsTableComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      declarations: [
        UmbrellaPremiumsTableComponent,
        PositionFixedDirective,
        StubFilterComponent,
        StubLoaderComponent,
        StubModalboxComponent,
        StubOrderByPipe,
        StubHintsAndWarningsLinkComponent,
        StubPerfectScrollbarComponent
      ],
      providers: [
        HintsAndWarningsService,
        StubOverlayLoaderServiceProvider,
        StubRateServiceProvider,
        StorageService,
        StubPremiumsServiceProvider
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(UmbrellaPremiumsTableComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
