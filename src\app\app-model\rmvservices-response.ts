export interface MetaData {
  name: string;
  value: string;
}

export interface Transaction {
  type: string;
  success: boolean;
  eligible: boolean;
  metaData: MetaData[];
}

export interface Message {
  name: string;
  value: string;
}

export interface PaymentDefinition {
  category?: any;
  type: string;
  payment?: Payment;
}

export interface Payment {
  total: string;
  rmvFee: string;
  bankTransferTotal?: number;
  agencyBillTotal?:number;
  lineItems: LineItem[];
}

export interface LineItem {
  amount: string;
  description: string;
  type: string;
}

export interface Notification {
  name: string;
  value: string;
  type: string;
}

export interface AgentFeeOption {
  amount: string;
  description: string;
}

export interface RmvServicesResponse {
  transaction: Transaction;
  messages: Message[];
  paymentDefinition: PaymentDefinition;
  transactionRequestId: number;
  userEmail?: any;
  agentEmail?: any;
  requestAdditionalData?: MetaData[];
  notifications: Notification[];
  stampId?: any;
  ownerEmail?: any;
  ownerDob?: any;
  ownerFid?: any;
  agentFeeOptions?: AgentFeeOption[];
}
