import { AdditionalDataI, WARNING_GROUPS, WarningDefinitionI } from 'app/hints-and-warnings/model/warnings';
import { CoverageItem, CoverageItemParsed } from 'app/app-model/coverage';
import { Validate } from 'app/hints-and-warnings/validators';
import { StorageService } from 'app/shared/services/storage-new.service';
import { values } from 'underscore';


// Common
// ------------------------------------------------------------------------------
function validPolicyItemParsed(value, fullObj: CoverageItemParsed): boolean {
  return fullObj.isRequired && !fullObj.isActive && !fullObj.isDisabled;
}

function validPolicyItemParsedFairPlan(value, fullObj: CoverageItemParsed, additionalData): boolean {
 if (fullObj.coverageCode !== 'BSC-DFIRE-000481') {return false; }
  const storage = new StorageService();
  let pic;

  storage.getStorageData('homeQuoteCoverages').subscribe((v: any) => {
     pic = additionalData.observedData.find(c => c.coverageCode === 'BSC-DFIRE-010067');
console.log(pic);
    const coverages = v.coverages ? v.coverages : (v.items && v.items[0] && v.items[0].coverages ? v.items[0].coverages : []);
  const dwell = coverages.find(c => c.coverageCode === 'DWELL');
      const dwellValue = dwell && dwell.values && dwell.values[0] ? +dwell.values[0].value : 0;

      if (dwellValue > 1000000 && !pic?.isActive) {
        fullObj.isRequired = true;
      } else {
        fullObj.isRequired = false;
      }
      pic.isDisabled = fullObj.isActive;


   });
return fullObj.isRequired && !fullObj.isActive && !fullObj.isDisabled;
}

function validPolicyItemParsedFairPlan2(value, fullObj: CoverageItemParsed, additionalData): boolean {
 if (fullObj.coverageCode !== 'BSC-DFIRE-010067') {return false; }
  const storage = new StorageService();
  let loss;

  storage.getStorageData('homeQuoteCoverages').subscribe((v: any) => {
    storage.getStorageData('dwellingGeneralOptionsParsed').subscribe((x: any) => {
    loss = x.find(c => c.coverageCode === 'BSC-DFIRE-000481');

    const coverages = v.coverages ? v.coverages : (v.items && v.items[0] && v.items[0].coverages ? v.items[0].coverages : []);
  const dwell = coverages.find(c => c.coverageCode === 'DWELL');
      const dwellValue = dwell && dwell.values && dwell.values[0] ? +dwell.values[0].value : 0;
      console.log(dwellValue);
     if (loss && loss.isActive) {
      console.log(loss);

      fullObj.isActive = false;
      fullObj.isDisabled = true;
      fullObj.currentValue = '';
      fullObj.currentValueData.keyValue = [];
      fullObj.currentValueData.values = [];
     }

     if (dwellValue > 1000000 && !fullObj.isDisabled && !loss.isActive) {
      fullObj.isRequired = true;
     } else {
      fullObj.isRequired = false;
     }

  });
});
 return fullObj.isRequired && !fullObj.isActive && !fullObj.isDisabled;
}

function modifyPolicyItemPropertiesByCode(
    policies: CoverageItemParsed[],
    policyCode: string,
    propValues: { [key: string]: any }
  ): CoverageItemParsed[] {
    const policyItem: CoverageItem = policies.find(
      item => item.coverageCode === policyCode
    );

    if (policyItem) {
      for (const property in propValues) {
        if (property in policyItem) {
          policyItem[property] = propValues[property];
        }
      }
    }

    return policies;
  }

function generateUqOptionViewId(policy: CoverageItemParsed): string {
  return policy.viewUqId;
}

function generateUqOptionViewInteractId(policy: CoverageItemParsed): string {
  return policy.viewUqId + '_modal-launcher';
  // return policy.viewUqId + '_checkbox';
}


// Dwelling Options Tab, Carrier Options
// ------------------------------------------------------------------------------
/**
 * Validation for Dwelling Carrier Options Data
 * For Model: PolicyItemParsed
 */

function generateViewUrlCarrierOptions(fullObj: CoverageItemParsed): string {
  return '/dashboard/dwelling/quotes/' + fullObj.quoteResourceId + '/options';
}


const policyItemDwellingParsedCarrierOption: WarningDefinitionI = {
  id: 'currentValue',
  deepId: 'currentValue',
  viewUri: generateViewUrlCarrierOptions,
  viewFieldId: generateUqOptionViewId,
  viewFieldInteractId: generateUqOptionViewInteractId,
  warnings: [{
    label: (val, fullObj: CoverageItemParsed) => 'Required ' + fullObj.description  + '.',
    condition: validPolicyItemParsed,
    group: WARNING_GROUPS.carrier,
    carriers: (fullObj: CoverageItemParsed) => {
      if (fullObj.additionalData && fullObj.additionalData.requiredForCarriers && fullObj.additionalData.requiredForCarriers.length) {
        return fullObj.additionalData.requiredForCarriers;
      } else {
        return fullObj.ratingPlanId ? fullObj.ratingPlanId.split(',') : [];
      }
    }
  },
  {
    label: (val, fullObj: CoverageItemParsed) => 'Option: ' + fullObj.description  + ' is selected but no values are chosen for the option.', // https://bostonsoftware.atlassian.net/browse/SPR-2580
    condition: (val, fullObj: CoverageItemParsed) => {
    return Validate.coverageItemParsedErrorIfActiveAndNoSubOptionsSelected(fullObj);
    },
    group: WARNING_GROUPS.carrier,
    carriers: (fullObj: CoverageItemParsed) => {
      if (fullObj.additionalData && fullObj.additionalData.requiredForCarriers && fullObj.additionalData.requiredForCarriers.length) {
        return fullObj.additionalData.requiredForCarriers;
      } else {
        return fullObj.ratingPlanId ? fullObj.ratingPlanId.split(',') : [];
      }
    }
  }]
};

export const WARNINGS_DEFINITIONS_DWELLING_OPTIONS_CARRIER_OPTIONS: WarningDefinitionI[] = [
  policyItemDwellingParsedCarrierOption
];


// Dwelling Options Tab, General Options
// ------------------------------------------------------------------------------
/**
 * Validation for Dwelling General Options Data
 * For Model: PolicyItemParsed
 */

function generateViewUrlGeneralOptions(fullObj: CoverageItemParsed): string {
  return '/dashboard/dwelling/quotes/' + fullObj.quoteResourceId + '/options';
}

const policyItemDwellingParsedGeneralOption: WarningDefinitionI = {
  id: 'currentValue',
  deepId: 'currentValue',
  viewUri: generateViewUrlGeneralOptions,
  viewFieldId: generateUqOptionViewId,
  viewFieldInteractId: generateUqOptionViewInteractId,
  warnings: [{
    label: (val, fullObj: CoverageItemParsed) => 'Required ' + fullObj.description  + '.',
    condition: validPolicyItemParsed,
    group: WARNING_GROUPS.general,
    carriers: (fullObj: CoverageItemParsed) => {

      if (fullObj.additionalData && fullObj.additionalData.requiredForCarriers && fullObj.additionalData.requiredForCarriers.length) {
        return fullObj.additionalData.requiredForCarriers;
      } else {
        return fullObj.ratingPlanId ? fullObj.ratingPlanId.split(',') : [];
      }
    }
  },
  //  {
  //    label: (val, fullObj: CoverageItemParsed) => 'Required ' + fullObj.description  + '.',
  //    condition: validPolicyItemParsedFairPlan,
  //    group: WARNING_GROUPS.carrier,
  //    carriers: (fullObj: CoverageItemParsed) => ['201']
  //  },
  //  {
  //    label: (val, fullObj: CoverageItemParsed) => 'Required ' + fullObj.description  + '.',
  //    condition: validPolicyItemParsedFairPlan2,
  //    group: WARNING_GROUPS.carrier,
  //    carriers: ['201']
  //  },
  {
    label: (val, fullObj: CoverageItemParsed) => 'Option: ' + fullObj.description  + ' is selected but no values are chosen for the option.', // https://bostonsoftware.atlassian.net/browse/SPR-2580
    condition: (val, fullObj: CoverageItemParsed) => {
    return Validate.coverageItemParsedErrorIfActiveAndNoSubOptionsSelected(fullObj);
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

export const WARNINGS_DEFINITIONS_DWELLING_OPTIONS_GENERAL_OPTIONS: WarningDefinitionI[] = [
  policyItemDwellingParsedGeneralOption
];
