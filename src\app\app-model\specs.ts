import { ApiResponse } from './_common';

export class Town {
  constructor(
    public countyId: string = null,
    public countyName: string = null,
    public id: string = null,
    public lob: string = null,
    public state: string = null,
    public townCode: string = null,
    public townName: string = null
  ) {}
}

export class FireDistrict {
  constructor(
    public fireDistrictName: string = null,
    public fireDistrictId: string = null,
    public lob: string = null,
    public state: string = null,

  ) {}
}

export interface VehicleTypesLookup {
	id: number;
	type: string;
	description: string;
}

export interface VehicleBrandType {
	vehicleBrandTypes: VehicleTypesLookup[];
}

export interface VehicleTitleType {
  vehicleTitleTypes: VehicleTypesLookup[];
}



export class TownsListAPIResponse extends ApiResponse<Town> {}
export class FireDistrictListAPIResponse extends ApiResponse<FireDistrict> {}
