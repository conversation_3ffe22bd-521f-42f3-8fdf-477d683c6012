
import { Component, OnInit, ViewChild } from '@angular/core';

import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { SubsService } from 'app/dashboard/app-services/subs.service';
import { AgencyUserService, UserData } from 'app/shared/services/agency-user.service';
import { PremiumsService } from 'app/dashboard/auto/premiums/premiums.service';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { SubscriptionLike as ISubscription } from 'rxjs';
import { DatepickerModalComponent } from 'app/shared/components/datepicker-modal/datepicker-modal.component';
import { take, first } from 'rxjs/operators';


import { addYears, format } from 'date-fns';

@Component({
    selector: 'app-aside-commercial-auto-quote',
    templateUrl: './aside-commercial-auto-quote.component.html',
    styleUrls: ['./aside-commercial-auto-quote.component.scss'],
    standalone: false
})
export class AsideCommercialAutoQuoteComponent implements OnInit {
  private subscription: ISubscription;
  private resetPlansSubscribtion: ISubscription;
  public quote;
  public effectiveDate;
  public plansOnQuotes;
  public plansCount = 0;
  public selectedPlan: any = [];
  public selectedAllPlansMask: any;
  public selectedPlansOnQuote: any;
  private selectedPlanObject;
  private currentPlanUrl;
  private currentPlanSubscriptionGet: ISubscription;
  private selectedPlanSubscription: ISubscription;
  public isMaipArcQuote = false;

  @ViewChild('datepickerEffectiveDate') public datepickerEffectiveDate: DatepickerModalComponent;

  constructor(
    private storageService: StorageService,
    private quotesService: QuotesService,
    private overlayLoaderService: OverlayLoaderService,
    private agencyUserService: AgencyUserService,
    private subsService: SubsService,
    private premiumsService: PremiumsService,
    private storageGlobalService: StorageGlobalService
  ) { }

  ngOnInit() {
    this.checkIfMaipArcQuote();
    this.processPlans();
    // this.subscribeQuoteEffectiveDateInPastShowWarningAfterLoad();

    this.subscription = this.storageService.getStorageData('selectedQuote').subscribe(
      res => {
        this.quote = res;
        this.getCurrentPlan(res.quotePlanList.href);
        this.effectiveDate = res.effectiveDate;
      }
    );

    setTimeout(() => {
      this.showQuoteEffectiveDateInPastWarningModal();
    });
  }

  ngOnDestroy() {
    this.subscription && this.subscription.unsubscribe();
    this.currentPlanSubscriptionGet && this.currentPlanSubscriptionGet.unsubscribe();
    this.resetPlansSubscribtion && this.resetPlansSubscribtion.unsubscribe();
    this.selectedPlanSubscription && this.selectedPlanSubscription.unsubscribe();
  }

  public updateEffectiveDate(event) {
    this.quote.effectiveDate = format(new Date(event.date), 'yyyy-MM-dd');
   this.quote.expirationDate = format(
  addYears(new Date(event.date), 1),
  'yyyy-MM-dd'
);
    this.overlayLoaderService.showLoader();
    this.quotesService.updateQuoteInfo(this.quote.resourceId, this.quote).pipe(first()).subscribe(quote => {
      this.quote = quote;
      this.effectiveDate = quote.effectiveDate;
      this.storageService.setStorageData('selectedQuote', this.quote);
      this.premiumsService.rerateGeneral(window.location.href);
      this.overlayLoaderService.hideLoader();
    });
  }

  private checkIfMaipArcQuote(): void {
    // let tmpIsMaipArc = localStorage.getItem('maipArcQuote');
    const tmpIsMaipArc = sessionStorage.getItem('maipArcQuote');
    if (tmpIsMaipArc) {
      const isMaipArc = JSON.parse(tmpIsMaipArc);
      this.isMaipArcQuote = isMaipArc.isMaipArcQuote;
    }
  }

  private processPlans() {
    const plans = JSON.parse(JSON.stringify(this.storageGlobalService.takeSubs('plans')));
    const plansFilteredByLob = this.filterByLob(plans, 'AUTOB');

    if (plansFilteredByLob.length > 0) {
      this.setPlansOnQuotes(plansFilteredByLob);
    } else {
      this.resetPlans();
    }
  }

  private resetPlans() {
    this.overlayLoaderService.showLoader('Loading Plans...');
    this.agencyUserService.userData$.subscribe(agent => {

      if (agent) {
        this.resetPlansSubscribtion = this.subsService.getRatingPlans(agent.agencyId).subscribe(response => {
          this.overlayLoaderService.hideLoader();

          this.setPlansOnQuotes(this.filterByLob(response.items, 'AUTOB'));
        });
      }
    });
  }

  private filterByLob(plans, lob) {
    const filteredPlans = [];

    plans.forEach(plan => {
      if (plan.lob === lob) {
        filteredPlans.push(plan);
      }
    });

    return filteredPlans;
  }

  private setPlansOnQuotes(plans) {
    this.plansCount = plans.length;
    this.plansOnQuotes = [];
    plans.forEach(item => {
      this.plansOnQuotes.push({ text: item.name, id: item.ratingPlanId, data: item });
    });

    this.plansOnQuotes.sort((a, b) => {
      if (a.text < b.text) { return -1; } else if (a.text > b.text) { return 1; } else { return 0; }
    });

    if (this.plansCount && !this.selectedPlan.length) {
      this.selectedPlan = [];
      this.plansOnQuotes.forEach((plan, index) => {
        this.selectedPlan.push(plan);
      });
    }
  }

  private processCurrentPlans(res) {
    if (res.items[0].meta && res.items[0].meta.href) {
      this.currentPlanUrl = res.items[0].meta.href;
    } else {
      this.currentPlanUrl = res.meta.href;
    }
    this.selectedPlanObject = res;

    this.selectedPlan = [];
    if (res.items[0].items.length) {
      res.items[0].items.forEach(item => {
        this.selectedPlan.push({
          data: item,
          id: item.ratingPlanId,
          text: item.name
        });
      });
    } else {
      this.plansOnQuotes.forEach(plan => {
        this.selectedPlan.push(plan);
      });
    }
  }

  private getCurrentPlan(uri: string) {
    this.selectedPlanSubscription && this.selectedPlanSubscription.unsubscribe();
    this.selectedPlanSubscription = this.storageService.getStorageData('selectedPlan')
      .subscribe(res => {
        if (res && res.items && res.items.length) {
          this.processCurrentPlans(res);
        } else {
          this.getCurrentPlanFromAPI(uri);
        }
      });
  }

  private getCurrentPlanFromAPI(uri) {
    this.overlayLoaderService.showLoader();
    this.quotesService.getDataByUrl(uri).pipe(first()).subscribe(plan => {
      this.storageService.setStorageData('selectedPlan', plan);
      this.overlayLoaderService.hideLoader();
    });
  }

  public updateQuotePlan($event) {
    this.selectedPlansOnQuote = [];
    this.selectedAllPlansMask = '';

    console.log('AAAAAAAAAAAAA plansOnQuotes:', this.plansOnQuotes);
    console.log('AAAAAAAAAAAAA updateQuotePlan:', $event);

    $event.selectedOption.forEach((option, index) => {
      this.selectedPlansOnQuote.push(option.data);
      if (index > 0) {
        this.selectedAllPlansMask = this.selectedAllPlansMask + ', ' + option.data.name;
      } else {
        this.selectedAllPlansMask = this.selectedAllPlansMask + option.data.name;
      }
    });
    const data = { items: this.selectedPlansOnQuote };

    if (this.currentPlanUrl) {
      this.overlayLoaderService.showLoader();
      this.quotesService.updateQuoteByUrl(this.currentPlanUrl, data, false).subscribe(plans => {
        this.selectedPlan = $event.selectedOption;
        this.overlayLoaderService.hideLoader();
        this.getCurrentPlanFromAPI(this.selectedPlanObject.meta.href);
      });
    }
  }

  public get clientNameToDisplay(): string {
    let name = '';

    if (this.quote.client && this.quote.client.businessName) {
      name += ' ' + this.quote.client.businessName;
    } else if (this.quote.lob !== 'AUTOB') {
        if (this.quote.client && this.quote.client.firstName) {
          name += this.quote.client.firstName;
        }

        if (this.quote.client && this.quote.client.lastName) {
          name += ' ' + this.quote.client.lastName;
        }
      }

    return (name.trim()) ? name : 'No clients name';

  }

  private showQuoteEffectiveDateInPastWarningModal(): void {
    this.storageService.getStorageData('quoteEffectiveDateInPastShowWarningAfterLoad')
      .pipe(take(1))
      .subscribe((data: boolean) => {
        if (data) {
          this.datepickerEffectiveDate && this.datepickerEffectiveDate.open();
          this.storageService.setStorageData('quoteEffectiveDateInPastShowWarningAfterLoad', false);
        }
      });
  }
}
