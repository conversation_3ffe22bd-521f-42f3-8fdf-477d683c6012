import { Component, OnInit, Input, ViewChild } from '@angular/core';
import { LookupsService } from '../../app-services/lookups.service';
import { RmvService } from '../../app-services/rmv.service';
import { DriverRmvToSendWithResourceId, DriverRmvToSend } from 'app/app-model/driver';
import { DriverInquiryComponent } from './driver-inquiry/driver-inquiry.component';
import { VehicleInquiryComponent } from './vehicle-inquiry/vehicle-inquiry.component';

import { InquiryTypeComponent } from './inquiry-type/inquiry-type.component';
import { NgForm, ControlContainer } from '@angular/forms';
import { StorageService } from '../../../shared/services/storage-new.service';
import { OverlayRouteService } from '../../../overlay/services/overlay-route.service';
import { OverlayLoaderService } from '../../../shared/services/overlay-loader.service';
import { format } from 'date-fns';

@Component({
    selector: 'app-rmv-inquiry',
    templateUrl: './rmv-inquiry.component.html',
    viewProviders: [{ provide: ControlContainer, useExisting: NgForm }],
    styleUrls: ['./rmv-inquiry.component.scss'],
    standalone: false
})
export class RmvInquiryComponent implements OnInit {
@ViewChild(DriverInquiryComponent) driverInquiry: DriverInquiryComponent;
@ViewChild(VehicleInquiryComponent) vehicleInquiry: VehicleInquiryComponent;
@ViewChild(InquiryTypeComponent, {static: true}) inquiryType: InquiryTypeComponent;
fullData = {vehicles: [], drivers: []};
  constructor(private lookupService: LookupsService,
     private storageService: StorageService,
     private overlayRouteService: OverlayRouteService,
     private overlayLoader: OverlayLoaderService,
     private rmvService: RmvService) { }

  ngOnInit() {

}

clear() {
this.fullData = {vehicles: [], drivers: []};
this.storageService.setStorageData('rmvVehicleLookup', []);
this.storageService.setStorageData('rmvDriversLookup', []);
}

lookup(form: NgForm) {
  this.clear();
 this.inquiryType.type === 'vehicle' ? this.getVehicleInformation() : this.getDriverInformation();
}

 getVehicleInformation() {
 this.clear();
  const vinList = [];
  const plateList = [];
  this.vehicleInquiry.vehicleList.forEach( x => {
    const vehicle = {...x, resourceId: ''};
    if (vehicle.plateType==undefined) {
      vehicle.plateType="PAN";
    }

    switch (vehicle.lookupType) {
      case 'vin':
        if (vehicle.vin) {
          vinList.push(vehicle);
        }
        break;
      default:
        if (vehicle.plateNumber) {
          plateList.push(vehicle);
        }
        break;
    }
  });
  const dataPlate = {
    quoteId: '0',
    vehicles: plateList,
    policyEffectiveDate: '2020-07-15',
    lob: 'autop'
  };
  const dataVin = {
    quoteId: '0',
    vehicles: vinList,
    policyEffectiveDate: '2020-07-15',
    lob: 'autop'
  };
  this.overlayLoader.showLoader();
  if (plateList.length > 0 && vinList.length === 0) {
   this.lookupService.postVehiclesDataByPlate(dataPlate)
    .subscribe(x => {x.items.forEach(veh => {
      this.fullData.vehicles.push(veh);
      this.overlayLoader.hideLoader();
    });
    this.storageService.setStorageData('rmvVehicleLookup', this.fullData.vehicles);
    this.overlayRouteService.go('rmvreports', 'vehicle', this.fullData.vehicles[0].make
     + '-' + this.fullData.vehicles[0].model + '-' + this.fullData.vehicles[0].vin);
  });
  }
  if (vinList.length > 0 && plateList.length === 0) {
    this.lookupService.postVehiclesDataByVin(dataVin)
      .subscribe(x => {x.items.forEach(veh => {
        this.fullData.vehicles.push(veh);
      this.overlayLoader.hideLoader();
      });
      this.storageService.setStorageData('rmvVehicleLookup', this.fullData.vehicles);
      this.overlayRouteService.go('rmvreports', 'vehicle', this.fullData.vehicles[0].make
       + '-' + this.fullData.vehicles[0].model + '-' + this.fullData.vehicles[0].vin);
    });
  }

  if (vinList.length > 0 && plateList.length > 0) {
    this.lookupService.postVehiclesDataByPlate(dataPlate)
    .subscribe(plate => {
      plate.items.forEach(veh => {
        this.fullData.vehicles.push(veh);
      });
      this.lookupService.postVehiclesDataByVin(dataVin)
      .subscribe(vin => {
        vin.items.forEach(veh => {
          this.fullData.vehicles.push(veh);
      this.overlayLoader.hideLoader();

          this.storageService.setStorageData('rmvVehicleLookup', this.fullData.vehicles);
          this.overlayRouteService.go('rmvreports', 'vehicle', this.fullData.vehicles[0].make
           + '-' + this.fullData.vehicles[0].model + '-' + this.fullData.vehicles[0].vin);
        });
      });
    });
  }

}

getDriverInformation() {
  this.clear();

  const resourceList: DriverRmvToSend[] = [];
  this.driverInquiry.driverList.forEach(x => {
  if (x.firstName) {resourceList.push(x); }

});
const data = {
  drivers: resourceList,
  lob: 'autop',
  policyEffectiveDate:  format(new Date(), 'yyyy-MM-dd')
};

let driverId = '';
this.overlayLoader.showLoader();
if (resourceList.length > 0) {
  this.lookupService.newRmvQuote(data).subscribe(x => {
    this.fullData.drivers.push(x.drivers);
    this.overlayLoader.hideLoader();
    this.storageService.setStorageData('rmvDriversLookup', x.drivers);
    driverId = this.rmvService.generateRmvReportDriverId(x.drivers[0]);
    this.overlayRouteService.go('rmvreports', 'driver', driverId);
  if (this.inquiryType.type === 'driverAndVehicle') {this.storageService.setStorageData('rmvVehicleLookup', x.vehicles);  }
     });
}

}

hideButtonsUntilConditionMet() {
  if (this.inquiryType && !this.inquiryType.type) {
    return true;
  }
}
}

