import { SelectedVehicleSymbolsForVehicle } from 'app/app-model/symbols';
import { AdditionalDataI, WARNING_GROUPS, WarningDefinitionI, AdditionalDataAutoVehicles } from 'app/hints-and-warnings/model/warnings';
import { PlansService, REQUIRED_PLANS_SYMBOLS_VRG } from 'app/dashboard/app-services/plans.service';
import { Quote, QuotePlan } from 'app/app-model/quote';
import { Vehicle, VehicleLocationDataForVehicle, VehicleOptionsForVehicle, VehicleSymbol, VehicleGeneralDetailsHelper} from 'app/app-model/vehicle';

import { LookupsService } from 'app/dashboard/app-services/lookups.service';
import { SymbolsService } from 'app/dashboard/app-services/symbols.service';
import { Validate } from 'app/hints-and-warnings/validators';
import { VehiclesService } from 'app/dashboard/app-services/vehicles.service';
import { Town } from 'app/app-model/specs';
import { FilterOption } from 'app/app-model/filter-option';
import { Driver } from 'app/app-model/driver';

function requiredField(value): boolean {
  return Validate.isEmptyValue(value);
}

function isOldVehicle(vehicle: Vehicle, year: number = 1984): boolean {
  const vehicleYear: number = Number(vehicle.year);
  if (vehicle.year && !isNaN(vehicleYear) && vehicleYear <= year) {
    return true;
  } else {
    return false;
  }
}

/*
function requiredOrInvalidVIN(value: string, fullObj: Vehicle, additionalData: AdditionalDataAutoVehicles): boolean {
  if (fullObj.vehicleType == 'Trailer' || fullObj.vehicleType == 'Motorcycle') {
    return false;
  } else {
    const validVin = VehiclesService.prototype.vinValidate(value);
    const validPartialVin = (value && value.length === 10);
    const vehiclesOptionsModel = additionalData.vehiclesOptionsModel.find(el => el.vehicleResourceId === fullObj.resourceId);
    const optionsCount = vehiclesOptionsModel ? vehiclesOptionsModel.options.length : 0;
    let vinForOldCars: boolean  = false;

    // https://bostonsoftware.atlassian.net/browse/SPR-2669
    // const vehicleYear: number = Number(fullObj.year);
    // if (fullObj.year && !isNaN(vehicleYear) && vehicleYear <= 1984) {
    //   vinForOldCars = true;
    // }
    vinForOldCars = isOldVehicle(fullObj);

    // const vinIsRequired = !value && (optionsCount > 0 || vinForOldCars);
    const vinIsRequired = !value || !value && (optionsCount > 0 || vinForOldCars);
    const vinIsValid = vinForOldCars || (!vinForOldCars && (validVin || validPartialVin));

    // return !value || !(validVin || validPartialVin);
    // return (!value && optionsCount > 0 ) || !(validVin || validPartialVin);
    // console.log('VIN: ', vinIsRequired, vinIsValid, value, !!value);
    console.log('VIN: ', vinIsRequired, !!value, !(vinIsValid), (!!value && !(vinIsValid)));
    // return vinIsRequired || (!!value && !(vinIsValid));
    return vinIsRequired || (!!value && !(vinIsValid));
  }
}
*/


function requiredOrInvalidVIN(value: string, fullObj: Vehicle, additionalData: AdditionalDataAutoVehicles): boolean {
  if (fullObj.vehicleType === 'Trailer' || fullObj.vehicleType === 'Motorcycle') {
    return false;
  } else {
    const validVin = VehiclesService.prototype.vinValidate(value);
    const validPartialVin = (value && value.length === 10);
    const vehiclesOptionsModel = additionalData.vehiclesOptionsModel.find(el => el.vehicleResourceId === fullObj.resourceId);
    const optionsCount = vehiclesOptionsModel ? vehiclesOptionsModel.options.length : 0;
    let vinForOldCars  = false;
    let vehicleModelManuallyEntered = false;
    let vinRequiredBasedOnModelValue: boolean = optionsCount > 0;

    // https://bostonsoftware.atlassian.net/browse/SPR-2669
    vinForOldCars = isOldVehicle(fullObj);

    // https://bostonsoftware.atlassian.net/browse/SPR-2929
    if (vehiclesOptionsModel) {
      const valueInOptions = vehiclesOptionsModel.options.find((opt) => {
        const id = (opt && opt.id) ? opt.id.toUpperCase() : '';
        const modelValueUpper = (fullObj.model) ? fullObj.model.toUpperCase() : fullObj.model;
        return id === modelValueUpper;
      });

      if (optionsCount > 0 && !valueInOptions && fullObj.model) {
        vehicleModelManuallyEntered = true;
      }
    }

    vinRequiredBasedOnModelValue = (vehicleModelManuallyEntered) ? false : vinRequiredBasedOnModelValue;

    // const vinIsRequired = !value && (optionsCount > 0 || vinForOldCars);
    const vinIsRequired = (!value && !vehicleModelManuallyEntered) || !value && (vinRequiredBasedOnModelValue || vinForOldCars);
    const vinIsValid = vinForOldCars || (!vinForOldCars && (validVin || validPartialVin));

    return vinIsRequired || (!!value && !(vinIsValid));
  }
}


function requiredVehicleDisplacement(value: string, fullObj: Vehicle): boolean {
  if (fullObj.vehicleType === 'Motorcycle') {
    return Validate.isEmptyValue(value);
  } else {
    return false;
  }
}

function vehicleNameToDisplay(fullObj: Vehicle): string {
  return VehiclesService.prototype.vehicleNameToDisplay(fullObj);
}

function generateViewUrl(fullObj: Vehicle): string {
  return  '/dashboard/auto' + fullObj.meta.href;
}

function checkIfBodyStyleFieldRequiredBasedOnSelectedPlans(selectedPlans: QuotePlan[]): boolean {
  let required = false;
  const requireForPlans  = REQUIRED_PLANS_SYMBOLS_VRG;

  for (const plan of requireForPlans) {
    const exists = selectedPlans.findIndex(selectedPlan => selectedPlan.ratingPlanId === plan.ratingPlanId);
    if (exists > -1) {
      required = true;
      break;
    }
  }
  return required;
}


function getPriceNewValueRequiredPlans(symbolsForVehicle: SelectedVehicleSymbolsForVehicle): string[] {
  let tmpRequiredForPlans = [];

  // for (let symbolType in symbolsForVehicle.selectedVehicleSymbols) {
  //   if(symbolsForVehicle.selectedVehicleSymbols[symbolType].required && !symbolsForVehicle.selectedVehicleSymbols[symbolType].symbol.symbolValue) {
  //   // if(symbolsForVehicle.selectedVehicleSymbols[symbolType].required) {
  //     tmpRequiredForPlans = tmpRequiredForPlans.concat(symbolsForVehicle.selectedVehicleSymbols[symbolType].requiredForPlans);
  //   }
  // }

  for (const symbolType in symbolsForVehicle.selectedVehicleSymbols) {
    if (symbolsForVehicle.selectedVehicleSymbols[symbolType].required) {
      if (!symbolsForVehicle.selectedVehicleSymbols[symbolType].symbol.symbolValue
          || (symbolType === 'iso27' && symbolsForVehicle.selectedVehicleSymbols[symbolType].symbol.symbolValue === '27' )
          || (symbolType === 'iso75Coll' && symbolsForVehicle.selectedVehicleSymbols[symbolType].symbol.symbolValue === '98' )
          || (symbolType === 'iso75Comp' && symbolsForVehicle.selectedVehicleSymbols[symbolType].symbol.symbolValue === '98' )) {
            tmpRequiredForPlans = tmpRequiredForPlans.concat(symbolsForVehicle.selectedVehicleSymbols[symbolType].requiredForPlans);
          }
    }
  }

  // Get only unique values
  tmpRequiredForPlans = tmpRequiredForPlans.filter((val, i, self) => self.indexOf(val) === i);
  return tmpRequiredForPlans;
}


/**
 * Validation for Vehicle Data
 * For Model: Vehicle
 */
const vehicleType: WarningDefinitionI = {
  id: 'vehicleType',
  deepId: 'vehicleType',
  viewUri: generateViewUrl,
  viewFieldId: 'vehicleType',
  warnings: [{
    label: (value, fullObj) => 'Vehicle Type not selected for ' + vehicleNameToDisplay(fullObj) + '.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const vehicleUsage: WarningDefinitionI = {
  id: 'usage',
  deepId: 'usage',
  viewUri: generateViewUrl,
  viewFieldId: 'vehicleUsage',
  warnings: [{
    label: (value, fullObj) => 'Usage not selected for ' + vehicleNameToDisplay(fullObj) + '.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const vehicleYear: WarningDefinitionI = {
  id: 'year',
  deepId: 'year',
  viewUri: generateViewUrl,
  viewFieldId: 'vehicleYear',
  warnings: [{
    label: (value, fullObj) => 'Vehicle Year not selected for ' + vehicleNameToDisplay(fullObj) + '.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

// const vehicleMake:WarningDefinitionI = {
//   id: 'make',
//   deepId: 'make',
//   viewUri: generateViewUrl,
//   viewFieldId: 'vehicleMake',
//   warnings: [{
//     label: (value, fullObj) => 'Vehicle Make not selected for ' + vehicleNameToDisplay(fullObj) + '.',
//     condition: requiredField,
//     group: WARNING_GROUPS.general,
//     carriers: []
//   }]
// }

const vehicleMake: WarningDefinitionI = {
  id: 'make',
  deepId: 'make',
  viewUri: generateViewUrl,
  viewFieldId: 'vehicleMake',
  warnings: [{
    label: (value, fullObj) => 'Vehicle Make not selected for ' + vehicleNameToDisplay(fullObj) + '.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  },
  {
    label: (value, fullObj) => 'Vehicle Make incorrect value for ' + vehicleNameToDisplay(fullObj) + '.',
    condition: (value: any, fullObj: Vehicle, additionalData: AdditionalDataAutoVehicles) => {
      let hasError = false;

      if (additionalData.vehiclesOptionsMake && additionalData.vehiclesOptionsMake.length) {
        const vehicleOptions: VehicleOptionsForVehicle = additionalData.vehiclesOptionsMake
          .find((data: VehicleOptionsForVehicle) => data.vehicleResourceId === fullObj.resourceId);

        if (vehicleOptions && vehicleOptions.options.length) {
          const optionInOptions: FilterOption = vehicleOptions.options.find((o: FilterOption) => o.id === value);

          if (!optionInOptions) {
            hasError = true;
          }
        }
      }

      return value && hasError;
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const vehicleModel: WarningDefinitionI = {
  id: 'model',
  deepId: 'model',
  viewUri: generateViewUrl,
  viewFieldId: 'vehicleModel',
  warnings: [{
    label: (value, fullObj) => 'Vehicle Model not selected for ' + vehicleNameToDisplay(fullObj) + '.',
    condition: (value: any, fullObj: Vehicle) => {
      let isRequired = requiredField(value);

      if (fullObj.vehicleType && (fullObj.vehicleType === 'Trailer' || fullObj.vehicleType === 'Motorcycle')) {
        isRequired = false;
      }

      return isRequired;
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

// Required if model has options
const vehicleTrim: WarningDefinitionI = {
  id: 'trimLevel',
  deepId: 'trimLevel',
  viewUri: generateViewUrl,
  viewFieldId: 'vehicleTrim',
  warnings: [{
    label: (value, fullObj) => 'Vehicle Trim not selected for ' + vehicleNameToDisplay(fullObj) + '.',
    condition: (value: any, fullObj: Vehicle, additionalDataAutoVehicles: AdditionalDataAutoVehicles) => {
      let isRequired = false;

      if (fullObj.vehicleType && fullObj.vehicleType === 'Trailer' || fullObj.vehicleType === 'Motorcycle') {
        return false;
      }

      // https://bostonsoftware.atlassian.net/browse/SPR-2669
      // if (``fullObj.year && Number(fullObj.year) <= 1984``) {
      if (isOldVehicle(fullObj)) {
        return false;
      }

      // https://bostonsoftware.atlassian.net/browse/SPR-2929 (scenario 2)
      const vehicleGeneralDetailsHelperForVehicle: VehicleGeneralDetailsHelper = additionalDataAutoVehicles
        .vehicleGeneralDetailsHelper
        .find((item: VehicleGeneralDetailsHelper) => item.vehicleResourceId === fullObj.resourceId);

      if (vehicleGeneralDetailsHelperForVehicle) {
        if (vehicleGeneralDetailsHelperForVehicle.validVinNumber || vehicleGeneralDetailsHelperForVehicle.validPartialVinNumber) {
          if (vehicleGeneralDetailsHelperForVehicle.returnedEmptyVehicleGeneralDetails) {
            // console.log('%c Trim should not be required', 'color:green', fullObj);
            return false;
          }
        }
      }

      /*
      if (additionalDataAutoVehicles.vehiclesOptionsModel) {
        let findOptionsForVehicle = additionalDataAutoVehicles.vehiclesOptionsModel
          .find(item => item.vehicleResourceId == fullObj.resourceId);

        if (findOptionsForVehicle) {
          isRequired = (findOptionsForVehicle.options.length <= 0) ? false : true;
        }
      }*/

      if (additionalDataAutoVehicles.vehiclesOptionsModel) {
        const findOptionsForVehicle = additionalDataAutoVehicles.vehiclesOptionsModel
          .find(item => item.vehicleResourceId === fullObj.resourceId);

        if (findOptionsForVehicle) {
          const valueInOptions = findOptionsForVehicle.options.find((opt) => {
            const id = (opt && opt.id) ? opt.id.toUpperCase() : '';
            const modelValueUpper = (fullObj.model) ? fullObj.model.toUpperCase() : fullObj.model;
            return id === modelValueUpper;
          });

          // If there are options, trim needs to be required
          if (findOptionsForVehicle.options.length > 0) {
            isRequired = true;
          } else {
            isRequired = false;
          }

          // https://bostonsoftware.atlassian.net/browse/SPR-2929 (scenario 1)
          // If there are options, but selected value do not match any option,
          // it means that the value has been entered manually and the field
          // shouldn't be required
          if (findOptionsForVehicle.options.length > 0 && !valueInOptions && fullObj.model) {
            isRequired = false;
          }

        }
      }

      return requiredField(value) && isRequired;
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const vehicleDisplacement: WarningDefinitionI = {
  id: 'displacement',
  deepId: 'displacement',
  viewUri: generateViewUrl,
  viewFieldId: 'vehicleDisplacement',
  warnings: [{
    label: (value, fullObj) => 'Required CC\'s for ' + vehicleNameToDisplay(fullObj) + '.',
    condition: requiredVehicleDisplacement,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const vehicleVin: WarningDefinitionI = {
  id: 'vin',
  deepId: 'vin',
  viewUri: generateViewUrl,
  viewFieldId: 'vehicleVin',
  warnings: [{
    label: (value, fullObj) => 'Invalid VIN number for ' + vehicleNameToDisplay(fullObj) + '.',
    condition: requiredOrInvalidVIN,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const MotorcyleVin: WarningDefinitionI = {
  id: 'vin',
  deepId: 'vin',
  viewUri: generateViewUrl,
  viewFieldId: 'vehicleVin',
  warnings: [{
    label: (value, fullObj) => 'Vin is required for ' + vehicleNameToDisplay(fullObj) + '.',
    condition: (value, veh: Vehicle, data) => {
   if (veh.vehicleType === 'Motorcycle') {
    return !value;
   }

    },
    group: WARNING_GROUPS.carrier,
    carriers: ['291']
  }]
};


const vehicleBodyStyle: WarningDefinitionI = {
  id: 'bodyStyle',
  deepId: 'bodyStyle',
  viewUri: generateViewUrl,
  viewFieldId: 'vehicleBodyStyle',
  warnings: [{
    label: (value, fullObj: Vehicle) => 'Vehicle Body Style not selected for ' + vehicleNameToDisplay(fullObj) + '.',
    condition: (value, fullObj, data: AdditionalDataAutoVehicles) => {
      let isRequired = checkIfBodyStyleFieldRequiredBasedOnSelectedPlans(data.quoteSelectedPlans);

      if (fullObj.vehicleType && fullObj.vehicleType === 'Trailer' || fullObj.vehicleType === 'Motorcycle') {
        isRequired = false;
      }

      return isRequired && !value;
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

// Annual miles
const vehicleAnnualMiles: WarningDefinitionI = {
  id: 'annualMiles',
  deepId: 'annualMiles',
  viewUri: generateViewUrl,
  viewFieldId: 'vehicleAnnualMiles',
  warnings: [{
    label: (value, fullObj) => 'Required Annual Miles for ' + vehicleNameToDisplay(fullObj) + '.',
    condition: (value, fullObj) => {
      if (fullObj.vehicleType && fullObj.vehicleType === 'Trailer') {
        return false;
      }

      return requiredField(value) || isNaN(Number(value));
    },
    group: WARNING_GROUPS.general,
    carriers: []
  },
  {
    label: (value, fullObj) => 'Mileage of 99999+ is unsupported for ' + vehicleNameToDisplay(fullObj) + '.',
    condition: (value, fullObj) => {
      const mileage: number = Number(value);

      if (!isNaN(mileage)) {
        return mileage >= 99999;
      }

      return false; // requiredField(value) || isNaN(Number(value));
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};



// Price New / Value - Conditionally
// When a symbol value is not returned for a vehicle
// For a High Value Car (A symbol is returned as 27 for ISO 1-27 or 98 for ISO 75)
const vehiclePriceValue: WarningDefinitionI = {
  id: 'priceValue',
  deepId: 'priceValue',
  viewUri: generateViewUrl,
  viewFieldId: 'vehiclePriceValue',
  warnings: [{
    label: (value, fullObj) => 'Required Price New / Value for ' + vehicleNameToDisplay(fullObj) + '.',
    condition: (value, fullObj: Vehicle, additionalData: AdditionalDataAutoVehicles) => {

      if (fullObj.vehicleType === 'Trailer' || fullObj.vehicleType === 'Motorcycle') {
        return false;
      }

      if (additionalData && additionalData.selectedVehicleSymbolsForVehicle) {
        const getSymbolsForVehicle = additionalData.selectedVehicleSymbolsForVehicle.find(item => item.vehicle.resourceId === fullObj.resourceId);

        if (getSymbolsForVehicle) {
          let tmpRequiredForPlans = getPriceNewValueRequiredPlans(getSymbolsForVehicle);

          // Make sure that the warning will be displayed even if the plans haven't been returned by API
          if (tmpRequiredForPlans.length <= 0) {
            tmpRequiredForPlans = additionalData.quoteSelectedPlansIds;
          }

          // TODO:: checkup if the all required symbols has been set
          return getSymbolsForVehicle.priceNewValueIsRequired && Validate.isRequiredForSelectedPlansIfEmptyValue(value, tmpRequiredForPlans, additionalData.quoteSelectedPlansIds);
        }
      }

      return false;
    },
    group: WARNING_GROUPS.carrier,
    carriers: (fullObj: Vehicle, additionalData: AdditionalDataAutoVehicles) => {
      let forCarriers = [];

      if (additionalData && additionalData.selectedVehicleSymbolsForVehicle) {
        const getSymbolsForVehicle = additionalData.selectedVehicleSymbolsForVehicle.find(item => item.vehicle.resourceId === fullObj.resourceId);

        if (getSymbolsForVehicle) {
          forCarriers = getPriceNewValueRequiredPlans(getSymbolsForVehicle);
        }
      }

      // Make sure that the warning will be displayed even if the plans haven't been returned by API
      if (forCarriers.length <= 0) {
        forCarriers = additionalData.quoteSelectedPlansIds;
      }

      return forCarriers;
    }
  },
  {
    label: (value, fullObj) => 'Required Price New / Value for ' + vehicleNameToDisplay(fullObj) + '.',
    condition: (value, fullObj: Vehicle, additionalData: AdditionalDataAutoVehicles) => {
      let isRequired = false;
      if (fullObj.vehicleType && fullObj.vehicleType === 'Trailer' || fullObj.vehicleType === 'Motorcycle') {
        isRequired = Validate.isEmptyValue(value);
      }

      return isRequired;
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};


// Price New / Value Or Symbols - Conditionally
// When a symbol value is not returned for a vehicle
// For a High Value Car (A symbol is returned as 27 for ISO 1-27 or 98 for ISO 75)
const vehiclePriceValueOrSymbols: WarningDefinitionI = {
  id: 'symbols',
  deepId: 'symbols',
  viewUri: generateViewUrl,
  viewFieldId: 'vehicleSymbolsPriceValue',
  viewFieldInteractId: 'iso-vrg-modal',
  warnings: [{
    // label: (value, fullObj) => 'Required Price New / Value for ' + vehicleNameToDisplay(fullObj) + '.',
    label: (value, fullObj) => 'Price New / Value Or Symbols are required to be set for ' + vehicleNameToDisplay(fullObj) + '.',
    condition: (value, fullObj: Vehicle, additionalData: AdditionalDataAutoVehicles) => {

      if (fullObj.vehicleType === 'Trailer' || fullObj.vehicleType === 'Motorcycle') {
        return false;
      }

      if (additionalData && additionalData.selectedVehicleSymbolsForVehicle) {
        const getSymbolsForVehicle = additionalData.selectedVehicleSymbolsForVehicle.find(item => item.vehicle.resourceId === fullObj.resourceId);

        if (getSymbolsForVehicle) {
          let tmpRequiredForPlans = getPriceNewValueRequiredPlans(getSymbolsForVehicle);

          // Make sure that the warning will be displayed even if the plans haven't been returned by API
          if (tmpRequiredForPlans.length <= 0) {
            tmpRequiredForPlans = additionalData.quoteSelectedPlansIds;
          }

          // Checkup if Price or all the required symbols has been set
          // return getSymbolsForVehicle.priceNewValueIsRequired && Validate.isRequiredForSelectedPlansIfEmptyValue(value, tmpRequiredForPlans, additionalData.quoteSelectedPlansIds);
          // console.log('Required Price: ', SymbolsService.checkIfVehiclePriceNewValueOrSymbolsMissingSomeValue(getSymbolsForVehicle.selectedVehicleSymbols), Validate.isRequiredForSelectedPlans(tmpRequiredForPlans, additionalData.quoteSelectedPlansIds));
          // console.log('Required Price: ', getSymbolsForVehicle.selectedVehicleSymbols);
          // return SymbolsService.checkIfVehiclePriceNewValueOrSymbolsMissingSomeValue(getSymbolsForVehicle.selectedVehicleSymbols)
          //        && Validate.isRequiredForSelectedPlans(tmpRequiredForPlans, additionalData.quoteSelectedPlansIds)
          //        && Validate.isEmptyValue(fullObj.priceValue);
          return Validate.isRequiredForSelectedPlans(tmpRequiredForPlans, additionalData.quoteSelectedPlansIds)
                 && ( SymbolsService.checkIfVehicleSymbolsMissingSomeValue(getSymbolsForVehicle.selectedVehicleSymbols) ||
                      ( SymbolsService.checkIfVehiclePriceNewValueIsRequired(getSymbolsForVehicle.selectedVehicleSymbols) && Validate.isEmptyValue(fullObj.priceValue))
                    );
        }
      }

      return false;
    },
    group: WARNING_GROUPS.carrier,
    carriers: (fullObj: Vehicle, additionalData: AdditionalDataAutoVehicles) => {
      let forCarriers = [];

      if (additionalData && additionalData.selectedVehicleSymbolsForVehicle) {
        const getSymbolsForVehicle = additionalData.selectedVehicleSymbolsForVehicle.find(item => item.vehicle.resourceId === fullObj.resourceId);

        if (getSymbolsForVehicle) {
          forCarriers = getPriceNewValueRequiredPlans(getSymbolsForVehicle);
        }
      }

      // Make sure that the warning will be displayed even if the plans haven't been returned by API
      if (forCarriers.length <= 0) {
        forCarriers = additionalData.quoteSelectedPlansIds;
      }

      return forCarriers;
    }
  }]
};


const vehicleOwner: WarningDefinitionI = {
  id: 'owner',
  deepId: 'owner',
  viewUri: generateViewUrl,
  viewFieldId: 'vehicleOwner',
  warnings: [{
    label: (value, fullObj) => 'Required vehicle owner for ' + vehicleNameToDisplay(fullObj) + '.',
    condition: (value, fullObj, additionalData: AdditionalDataAutoVehicles) => {
      return requiredField(value);
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const vehicleOperator: WarningDefinitionI = {
  id: 'operator',
  deepId: 'operator',
  viewUri: generateViewUrl,
  viewFieldId: 'vehicleOperator',
  warnings: [{
    label: (value, fullObj) => 'Required vehicle operator for ' + vehicleNameToDisplay(fullObj) + '.',
    condition: (value, fullObj, additionalData: AdditionalDataAutoVehicles) => {
      return requiredField(value);
    },
    group: WARNING_GROUPS.general,
    carriers: []
  },
  {
    label: (value, fullObj) => 'Wrong vehicle\'s operator for ' + vehicleNameToDisplay(fullObj) + '.',
    condition: (value, fullObj: Vehicle, additionalData: AdditionalDataAutoVehicles) => {
      let wrongOperator = false; // if Driver excludes vehicle

      if (fullObj.operator && fullObj.operator.meta && fullObj.operator.meta.href) {
        const driver: Driver = additionalData.drivers.find((d: Driver) => {
          if (d && d.meta && d.meta.href) {
            return d.meta.href === fullObj.operator.meta.href;
          }
          return false;
        });

        if (fullObj.operator.meta.href.length && !driver) {
          // It means that there is operator set for the vehicle,
          // but this value shouldn't be set, because such driver doesn't exists
          return true;
        }


        if (driver) {
          if (driver.exclusions && driver.exclusions.length) {
            const selectedVehicleIsExcludedIndex: number = driver.exclusions
              .findIndex((i) => i.meta.href === fullObj.meta.href);

            if (selectedVehicleIsExcludedIndex !== -1) {
              // isExcluded = true;
              wrongOperator = true;
              // console.log('%c Vehicle Is Excluded for driver: ', 'color:red', driver);
              // console.log('Driver can not be set, show warning');
            }
          }
        }
      }

      return wrongOperator;
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};


export const WARNINGS_DEFINITIONS_AUTO_VEHICLE: WarningDefinitionI[] = [
  vehicleType,
  vehicleUsage,
  vehicleYear,
  vehicleMake,
  vehicleModel,
  vehicleTrim,
  vehicleDisplacement,
  vehicleVin,
  vehicleBodyStyle,
  vehicleAnnualMiles,
  vehiclePriceValue,
  vehiclePriceValueOrSymbols,
  vehicleOwner,
  vehicleOperator,
  MotorcyleVin
];

/**
 * Validation for locations data (garaging address, state, etc.)
 * For Model: VehicleLocationDataForVehicle
 */
function vehicleName(data: VehicleLocationDataForVehicle): string {
  let name = '';
  name += data.vehicleYear ? data.vehicleYear + ' ' : '';
  name += data.vehicleMake ? data.vehicleMake + ' ' : '';
  name += data.vehicleModel ? data.vehicleModel.split(' ')[0] + ' ' : '';

  return name.trim() || 'New Vehicle';
}

function generateVehicleViewUrl(fullObj: VehicleLocationDataForVehicle): string {
  return  '/dashboard/auto' + fullObj.vehicleMeta.href;
}

function validateZipCode(zip): boolean {
  if (zip === '' || zip === null) {
    return false;
  }

  const zipPattern = /^\d{5}(?:-?\d{4})?$/;
  return !zipPattern.test(zip);
}

// Required for plans/carriers:
// Hanover, Travelers, National General, Green Mountain, Preffered New, Safeco, Hanover New
const vehicleLocationAddress1Carriers = ['20', '27', '172', '21', '29', '25', '281', '291', '312'];
const vehicleLocationAddress1: WarningDefinitionI = {
  id: 'address1',
  deepId: 'location.address1',
  viewUri: generateVehicleViewUrl,
  viewFieldId: 'vehicleLocationAddress1',
  warnings: [{
    label: (value, fullObj) => 'Garaging Address not set for ' + vehicleName(fullObj) + '.',
    condition: (value, vehicle: Vehicle, data: AdditionalDataI) => {
      return Validate.isRequiredForSelectedPlansIfEmptyValue(value, vehicleLocationAddress1Carriers, data.quoteSelectedPlansIds);
    },
    group: WARNING_GROUPS.carrier,
    carriers: vehicleLocationAddress1Carriers
  }]
};

// const vehicleLocationCity:WarningDefinitionI = {
//   id: 'city',
//   deepId: 'location.city',
//   viewUri: generateVehicleViewUrl,
//   viewFieldId: 'vehicleLocationCity',
//   warnings: [{
//     label: (value, fullObj) => 'City not set for ' + vehicleName(fullObj) + '.',
//     condition: requiredField,
//     group: WARNING_GROUPS.general,
//     carriers: []
//   }]
// }

const vehicleLocationCity: WarningDefinitionI = {
  id: 'city',
  deepId: 'location.city',
  viewUri: generateVehicleViewUrl,
  viewFieldId: 'vehicleLocationCity',
  warnings: [{
    label: (value, fullObj) => 'City not set for ' + vehicleName(fullObj) + '.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  },
  // https://bostonsoftware.atlassian.net/browse/SPR-2649
  {
    label: (value, fullObj) => 'Invalid Garaging City for ' + vehicleName(fullObj) + '.',
    condition: (value, fullObj, additionalData: AdditionalDataI) => {
      let wrongValueSetForCity = false;
      if (value && additionalData && additionalData.specLobTowns) {
        const townsListNames: string[] = additionalData.specLobTowns.map((t: Town) => t.townName);
        townsListNames.unshift('Out of State');
        const townAvailableOnTheList = townsListNames.includes(value);
        wrongValueSetForCity = !townAvailableOnTheList;
      }

      return wrongValueSetForCity;
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const vehicleLocationState: WarningDefinitionI = {
  id: 'state',
  deepId: 'location.state',
  viewUri: generateVehicleViewUrl,
  viewFieldId: 'vehicleLocationState',
  warnings: [{
    label: (value, fullObj) => 'Required Vehicles Garaging State for ' + vehicleName(fullObj) + '.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: ['22', '312']
  }]
};

// Required for plans/carriers:
// Hanover, Travelers, National General, Harleysville, MetLife, Norfolk and Dedham, Hanover New
// Preferred New, Progressive, Safeco
const vehicleLocationZipCarriers = ['20', '27', '172', '16', '24', '14', '29', '26', '25', '281', '291', '312', '315', '318'];
const vehicleLocationZip: WarningDefinitionI = {
  id: 'zip',
  deepId: 'location.zip',
  viewUri: generateVehicleViewUrl,
  viewFieldId: 'vehicleLocationZip',
  warnings: [{
    label: (value, fullObj) => 'Zip Code not set for ' + vehicleName(fullObj) + '.',
    condition: (value, vehicle: Vehicle, data: AdditionalDataI) => {
      return Validate.isRequiredForSelectedPlansIfEmptyValue(value, vehicleLocationZipCarriers, data.quoteSelectedPlansIds);
    },
    group: WARNING_GROUPS.carrier,
    carriers: vehicleLocationZipCarriers
  },
  {
    label: (value, fullObj) => 'Zip Code is not Valid.',
    condition: validateZipCode,
    group: WARNING_GROUPS.general,
    carriers: []
  }
]
};

export const WARNINGS_DEFINITIONS_AUTO_VEHICLE_LOCATIONS: WarningDefinitionI[] = [
  vehicleLocationAddress1,
  vehicleLocationCity,
  vehicleLocationState,
  vehicleLocationZip
];

