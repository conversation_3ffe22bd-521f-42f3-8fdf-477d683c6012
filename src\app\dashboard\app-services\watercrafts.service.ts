import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from 'app/shared/services/api.service';

import { StorageService } from 'app/shared/services/storage-new.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { Watercraft } from 'app/app-model/watercraft';
import { catchError } from 'rxjs/operators';

@Injectable()
export class WatercraftsService {
  public watercraft: Watercraft;

  constructor(
    private apiService: ApiService,
    private storageService: StorageService,
    private apiCommonService: ApiCommonService
  ) { }

  // Watercrafts
  private watercraftsUri(id: string) {
    return '/quotes/' + id + '/watercrafts';
  }

  public addWatercraft(quoteId: string, data: any): Observable<any> {
    return this.apiCommonService.postByUri(this.watercraftsUri(quoteId), data);
  }

  public getWatercrafts(quoteId: string): Observable<any> {
    return this.apiCommonService.getByUri(this.watercraftsUri(quoteId));
  }

  public editWatercraft(quoteId: string, data: any): Observable<any> {
    return this.apiCommonService.putByUri(this.watercraftsUri(quoteId) + '/' + data.resourceId, data).pipe(catchError(
    err => {
      if (err.status === 400 && err.error.message === 'Validation Failed') {
        alert(err.error.errors[0].message);
        return null;
      }
    }));
  }

  public deleteWatercraft(uri: string): Observable<any> {
    return this.apiCommonService.deleteByUri(uri);
  }
}
