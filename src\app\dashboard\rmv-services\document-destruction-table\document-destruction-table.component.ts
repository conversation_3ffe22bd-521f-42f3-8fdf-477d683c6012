import { DocumentDestruction, DocumentDestructionRequest } from './../../../app-model/document-destruction';
import { Component, OnInit, ViewChild } from '@angular/core';
import { RmvService } from '../../app-services/rmv.service';
import {
  DocumentsToDestroyParameters,
} from '../../../app-model/document-destruction';
import { OverlayLoaderService } from '../../../shared/services/overlay-loader.service';
import { Observable } from 'rxjs';
import { take } from 'rxjs/operators';
import { DocumentDestructionResponse, DocumentDestructionDetail } from '../../../app-model/document-destruction';
import { ModalboxComponent } from '../../../shared/components/modalbox/modalbox.component';
import { ActivatedRoute } from '@angular/router';
import { DocumentDestructionDetailsResolver } from '../../resolvers/document-destruction-details.resolver';
import {format, isBefore, isEqual, addDays, isAfter, parseISO } from 'date-fns';
@Component({
    selector: 'app-document-destruction-table',
    templateUrl: './document-destruction-table.component.html',
    styleUrls: ['./document-destruction-table.component.scss'],
    standalone: false
})
export class DocumentDestructionTableComponent implements OnInit {
  list = [];
  criteria;
  searchQuery;
  dataToChange;
  size = 0;
  limit = 10;
  paginationResultShowFrom = 0;
  userId;
  agencyId;
  dataInitiated = false;
  paginationCurrentPage = 1;
  documentDestructionDetails: Partial<DocumentDestructionDetail> = {};
  params: Partial<DocumentsToDestroyParameters> = {};
  response: DocumentDestructionResponse;
  filteredDate;
  maxDate;
  minDate;
  destroyedFromDate;
  dateRange = [];
  @ViewChild('error') error: ModalboxComponent;
  constructor(private rmvService: RmvService, private loaderService: OverlayLoaderService, private activatedRoute: ActivatedRoute) {}

   ngOnInit() {
     this.getDocumentDestructionDetails();


  }


getDateRange() {
  this.dateRange = [];

  // Parse the start date
  let dt = typeof this.destroyedFromDate === 'string'
    ? parseISO(this.destroyedFromDate)
    : new Date(this.destroyedFromDate);

  // Parse the end date
  const end = typeof this.documentDestructionDetails.eligibleToDestroyThroughDate === 'string'
    ? parseISO(this.documentDestructionDetails.eligibleToDestroyThroughDate)
    : new Date(this.documentDestructionDetails.eligibleToDestroyThroughDate);

  // Loop through dates from start to end (inclusive)
  while (!isAfter(dt, end)) {
    this.dateRange = [
      ...this.dateRange,
      {
        id: format(dt, 'yyyy-MM-dd'),
        text: format(dt, 'MM/dd/yyyy'),
      },
    ];

    dt = addDays(dt, 1);
  }
}

  async getDocumentDestructionDetails() {
      this.activatedRoute.data.subscribe(x => {
      const {documentDestructionDetails : {eligibleToDestroyThroughDate, destroyedThroughDate, lastDestroyedDate}}
      = x.documentDetailsResolver;
      this.documentDestructionDetails.lastDestroyedDate = lastDestroyedDate;
      this.documentDestructionDetails.destroyedThroughDate = destroyedThroughDate;
     this.destroyedFromDate = destroyedThroughDate
  ? format(addDays(new Date(destroyedThroughDate), 1), 'yyyy-MM-dd')
  : format(new Date(), 'yyyy-MM-dd');
     this.documentDestructionDetails.eligibleToDestroyThroughDate = eligibleToDestroyThroughDate;
     this.minDate = this.documentDestructionDetails.lastDestroyedDate ;
   this.maxDate = this.documentDestructionDetails.eligibleToDestroyThroughDate;
     this.getDateRange();
    this.filteredDate = this.documentDestructionDetails.eligibleToDestroyThroughDate;
    if (this.dateRange.length === 1) { this.filteredDate = this.dateRange[0].id; }
     this.getDocumentList();


    });

  }

   getDocumentList() {
     this.loaderService.showLoader();
    this.params.startDate = this.destroyedFromDate ? this.destroyedFromDate : format(new Date(), 'yyyy-MM-dd');
    if (this.filteredDate) {this.params.endDate = this.filteredDate; }
    this.params.limit = this.limit ?? 10;
    this.params.offset = this.paginationResultShowFrom ?? 0;
    this.rmvService
      .GetDocumentsToDestroy(this.params)
      .subscribe((x) => {this.list = x.items; this.size = x.size; this.dataInitiated = true;
         this.loaderService.hideLoader(); });
  }

  public paginationPageChange(data) {
    if (data.startAt < 0 || Object.is(NaN, data.startAt)) {
      data.startAt = 0;
      data.pageNumber = 1;
    }
    if (this.dataInitiated && this.paginationResultShowFrom !== data.startAt) {
      this.paginationCurrentPage = data.pageNumber;
      this.paginationResultShowFrom = data.startAt;
      this.getDocumentList();
    }
  }

  splitWords(word) {
    return word
      .replace(/([A-Z])/g, ' $1')
      .replace('Prefill', '')
      .trim();
  }

  setDate($ev) {
    this.filteredDate = $ev ? $ev.id : '';
    console.log(this.filteredDate);
      this.getDocumentList();
  }

  destroyDocuments() {
    const data: DocumentDestructionRequest = {
      documentDestruction: {
        destroyedFromDate: this.destroyedFromDate,
        destroyedThroughDate: this.filteredDate
      }
    };
    this.rmvService.DestroyDocuments(data).subscribe(x => {
      this.response = x;
      if (this.response.success) {
      this.documentDestructionDetails.destroyedThroughDate = this.response.documentDestructionDetails.destroyedThroughDate;
      this.documentDestructionDetails.eligibleToDestroyThroughDate = this.response.documentDestructionDetails.eligibleToDestroyThroughDate;
      this.documentDestructionDetails.lastDestroyedDate = this.response.documentDestructionDetails.lastDestroyedDate;
      this.destroyedFromDate =
      format(addDays(new Date(this.documentDestructionDetails.destroyedThroughDate), 1 ), 'yyyy-MM-dd').toString();
      this.getDateRange();
    this.filteredDate = this.documentDestructionDetails.destroyedThroughDate;

      this.list = [];
      } else {
        this.error.open();
      }
    });
  }

  public onResultLimitChange($ev): void {
    setTimeout(() => {
      this.paginationSetResultLimit($ev.limit);
      this.getDocumentList();
    });
  }

  private paginationSetResultLimit(intLimit: any) {
    this.limit = parseInt(intLimit, 10);
  }


}
