export interface Transaction {
	transactionAccepted: string;
	transactionTimestamp: string;
	atlasTransactionKey: string;
}

export interface Header {
	requestID: string;
	transaction: Transaction;
}

export interface InventoryItem {
	inventoryType: string;
	inventoryTypeDescription: string;
	inventoryID: string;
	numberOfUnits: string;
	numberOfItems: string;
	inventoryStatus: string;
	inventoryStatusDescription: string;
	inventoryStatusTimestamp: string;
	deliveryItemID: string;
	deliveryInventoryItemSequence: string;
}

export interface AssignedPlate {
	header: Header;
	inventoryItem: InventoryItem;
}
