import { StorageService } from './../../../../shared/services/storage-new.service';
import { WARNING_GROUPS, WarningDefinitionI} from 'app/hints-and-warnings/model/warnings';
import { Coverage, CoverageForVehicle, CoverageItemParsed } from 'app/app-model/coverage';
import { Validate} from 'app/hints-and-warnings/validators'
import { yearValidator } from 'app/shared/directives/year-validator.directive';


function generateViewUrl(policy: CoverageItemParsed):string {
  return  '/dashboard/auto/quotes/' + policy.quoteResourceId + '/coverages'; // + '?overlay=info&type=client';
}

function generateAdditionalViewUrl(policy: CoverageItemParsed):string {
  return  '/dashboard/auto/quotes/' + policy.quoteResourceId + '/coverages/additional-options'; // + '?overlay=info&type=client';
}

function generateModalUrl(policy: CoverageItemParsed,full,add):string {
  return policy.viewUqId + '_modal-launcher'
}

function generateUqOptionViewId(policy:CoverageItemParsed):string {
  return policy.viewUqId;
}

function generateUqOptionViewInteractId(policy:CoverageItemParsed):string {
  return policy.viewUqId;
  // return policy.viewUqId + '_checkbox';
}

function requiredField(value:any,fullObj):boolean {
  let returnValue;

  if(value === 'BSC-AUTO-002304'){
  returnValue = Validate.isEmptyValue(fullObj.currentValue)}
  return returnValue;
}

const isTrailerRequiredField = (value,fullObj,add) => {
  let returnValue;
  if(value === 'BSC-AUTO-002337') {
    if(fullObj.additionalData.vehicle.vehicleType === 'Trailer')
    return Validate.isEmptyValue(fullObj.currentValue)

  }
  return returnValue;
}


function generateViewFieldId(policy: CoverageItemParsed):string {
  return policy.viewUqId;
}


/**
 * Validation for CoverageItemParsed Data
 * For Model: CoverageItemParsed
 */

const parsedStandardCoverage:WarningDefinitionI = {
  id: 'currentValue',
  deepId: 'currentValue',
  viewUri: generateViewUrl,
  viewFieldId: generateViewFieldId,
  // viewFieldInteractId: generateUqOptionViewInteractId,
  warnings: [{
    label: (val, fullObj: CoverageItemParsed) => {
      let label = 'Coverages tab:: ' + fullObj.description + ': ';
      if (fullObj && fullObj.additionalData) {

        if (fullObj.additionalData && fullObj.additionalData.errorMessage) {
          label += fullObj.additionalData.errorMessage;
        }

      }

      return label
    },
    // condition: requiredField,
    condition: (val, fullObj: CoverageItemParsed) => {
      let isRequired = false;

      if (fullObj && fullObj.additionalData && fullObj.additionalData.hasError) {
        isRequired = true;
      }

      return isRequired;
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}

const safecoYearsOwnedRequired:WarningDefinitionI = {
  id:'coverageCode',
  deepId: 'coverageCode',
  viewUri: generateAdditionalViewUrl,
  viewFieldId: generateUqOptionViewId,
  viewFieldInteractId: generateUqOptionViewInteractId,
  warnings: [{
    label: 'Required Years Vehicle Owned',
    condition: requiredField,

    group: WARNING_GROUPS.carrier,
    carriers:['25']
  }]

}

const vermontTrailerTypeRequired: WarningDefinitionI = {
  id:'coverageCode',
  deepId: 'coverageCode',
  viewUri: generateAdditionalViewUrl,
  viewFieldId: generateUqOptionViewId,
  viewFieldInteractId: generateUqOptionViewInteractId,
  warnings: [{
    label: (val,fullObj) =>
      `Required Trailer Type - ${fullObj.additionalData.vehicle.year} ${fullObj.additionalData.vehicle.make}`,
    condition: isTrailerRequiredField,

    group: WARNING_GROUPS.carrier,
    carriers:['15']
  }]
}


export const WARNINGS_DEFINITIONS_AUTO_STANDARD_COVERAGES:WarningDefinitionI[] = [
  parsedStandardCoverage
]

export const WARNINGS_DEFINITIONS_AUTO_ADDITIONAL_COVERAGES:WarningDefinitionI[] = [
safecoYearsOwnedRequired,
vermontTrailerTypeRequired

]
