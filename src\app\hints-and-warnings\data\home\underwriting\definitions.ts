import { AdditionalDataI, WARNING_GROUPS, WarningDefinitionI } from 'app/hints-and-warnings/model/warnings';
import { Validate } from 'app/hints-and-warnings/validators';

// Home Underwriting Tab
// ------------------------------------------------------------------------------
/**
 * Validation for Home Loss History
 */
function validLoss(value): boolean {
  if (value) {
    return false;
  }
  return true;
}

function generateViewUrlUnderwriting(fullObj): string {
  return '/dashboard/home/<USER>/' + fullObj.parentId + '/underwriting';
}

// Note: 07.19.2018 - SA - Adding more other plans and removing Green Mountain plan as it is not active anymore.
// Note: 07/11/2018 - AH - Adding Preferred Mutual
// Note: 03/12/2018 -    - Adding Bay State
// Note: 10/11/2019 - Adding Arbella Home PRODUCT-1985
const plansRequiringLossFreeYears = [
  '4', '38', '42', '46', '47', '48', '49', '52', '53', '54', '58', '59', '60', '61', '63', '64', '67',
  '83', '86', '87', '89', '90', '91', '92', '93', '95', '96', '97', '100', '101', '313',
  '102', '103', '104', '105', '109', '112', '113', '119', '173', '202', '204', '206', '282', '267', '283', '290',
  '292', '303', '304', '310', '306', '316', '317', '319', '332'];
const lossHistoryValue: WarningDefinitionI = {
  id: 'lossFreeYears',
  deepId: 'lossFreeYears',
  viewUri: generateViewUrlUnderwriting,
  viewFieldId: 'total-number-loss-free-years',
  warnings: [{
    label: (val, fullObj) => 'Required Total Num. of Loss Free Years.',
    condition: (val, fullObj, data) => {
      return Validate.isRequiredForSelectedPlansIfEmptyValue(val, plansRequiringLossFreeYears, data.quoteSelectedPlansIds);
    },
    group: WARNING_GROUPS.carrier,
    carriers: plansRequiringLossFreeYears
  }]
};

// LOSS HISTORY ITEMS


function lossHistoryItemHasValueAndIsRequired(fullObj, lossYear: string, lossHistoryRequirements): boolean {
  let requiredYears = 0;
  let lossFreeYears = 0;
  let lossYearNumber: number;
  lossYearNumber = +lossYear.substr(10, 11);

  if (fullObj.lossFreeYears) {
    lossFreeYears = +fullObj.lossFreeYears.substr(1, 2);
  }

  if (lossHistoryRequirements && lossHistoryRequirements.length) {
    lossHistoryRequirements.forEach(requirement => {
      if (requirement && requirement.requirementYears) {
        const numberYears: number = +requirement.requirementYears.substr(1, 2);
        if (requiredYears < numberYears) {
          requiredYears = numberYears;
        }
      }
    });
  } else {
    requiredYears = 0;
  }

  let overRequiredYears = false;
  if (lossYear && fullObj.lossFreeYears && lossYearNumber > requiredYears) {
    overRequiredYears = true;
  }

  let hasValue = false;
  // if (fullObj[lossYear] && fullObj[lossYear] !== '_0') {
  if (fullObj[lossYear]) {
    hasValue = true;
  }

  if (fullObj[lossYear] && lossYear === 'lossesYear' + (lossFreeYears + 1) && fullObj[lossYear] === '_0') {
    hasValue = false;
  }


  let underRequiredYears = false;
  if (fullObj.lossFreeYears !== null) {
    if (lossYearNumber <= lossFreeYears) {
      underRequiredYears = true;
    }
    return !hasValue && !overRequiredYears && !underRequiredYears;
  }
  return false;
}

// function lossHistoryItemHasValueAndIsRequired(fullObj, lossYear: string, lossHistoryRequirements): boolean {
//   let requiredYears = 0;
//   let lossFreeYears = 0;
//   let lossYearNumber: number;
//   lossYearNumber = +lossYear.substr(10, 11);

//   if (fullObj.lossFreeYears) {
//     lossFreeYears = +fullObj.lossFreeYears.substr(1, 2);
//   }

//   if (lossHistoryRequirements && lossHistoryRequirements.length && lossHistoryRequirements[1] && lossHistoryRequirements[1].length) {
//     lossHistoryRequirements[1].forEach(requirement => {
//       if (requirement && requirement.requirementYears) {
//         const numberYears: number = +requirement.requirementYears.substr(1, 2);
//         if (requiredYears < numberYears) {
//           requiredYears = numberYears;
//         }
//       }
//     });
//   } else {
//     requiredYears = 0;
//   }

//   let overRequiredYears = false;
//   if (lossYear && fullObj.lossFreeYears && lossYearNumber > requiredYears) {
//     overRequiredYears = true;
//   }

//   let hasValue = false;
//   // if (fullObj[lossYear] && fullObj[lossYear] !== '_0') {
//   if (fullObj[lossYear]) {
//     hasValue = true;
//   }

//   if (fullObj[lossYear] && lossYear === 'lossesYear' + (lossFreeYears + 1) && fullObj[lossYear] === '_0') {
//     hasValue = false;
//   }


//   let underRequiredYears = false;
//   if (fullObj.lossFreeYears !== null) {
//     if (lossYearNumber <= lossFreeYears) {
//       underRequiredYears = true;
//     }
//     return !hasValue && !overRequiredYears && !underRequiredYears;
//   }
//   return false;
// }

const lossHistoryItemsValue1: WarningDefinitionI = {
  id: 'lossesYear1',
  deepId: 'lossesYear1',
  viewUri: generateViewUrlUnderwriting,
  viewFieldId: 'lossesYear1',
  warnings: [{
    label: 'Required Num. of Losses in Year 1',
    condition: (val, fullObj, data) => {
      return lossHistoryItemHasValueAndIsRequired(fullObj, 'lossesYear1', data);
    },
    group: WARNING_GROUPS.carrier,
    carriers: plansRequiringLossFreeYears
  }]
};

const lossHistoryItemsValue2: WarningDefinitionI = {
  id: 'lossesYear2',
  deepId: 'lossesYear2',
  viewUri: generateViewUrlUnderwriting,
  viewFieldId: 'lossesYear2',
  warnings: [{
    label: 'Required Num. of Losses in Year 2',
    condition: (val, fullObj, data) => {
      return lossHistoryItemHasValueAndIsRequired(fullObj, 'lossesYear2', data);
    },
    group: WARNING_GROUPS.carrier,
    carriers: plansRequiringLossFreeYears
  }]
};

const lossHistoryItemsValue3: WarningDefinitionI = {
  id: 'lossesYear3',
  deepId: 'lossesYear3',
  viewUri: generateViewUrlUnderwriting,
  viewFieldId: 'lossesYear3',
  warnings: [{
    label: 'Required Num. of Losses in Year 3',
    condition: (val, fullObj, data) => {
      return lossHistoryItemHasValueAndIsRequired(fullObj, 'lossesYear3', data);
    },
    group: WARNING_GROUPS.carrier,
    carriers: plansRequiringLossFreeYears
  }]
};

const lossHistoryItemsValue4: WarningDefinitionI = {
  id: 'lossesYear4',
  deepId: 'lossesYear4',
  viewUri: generateViewUrlUnderwriting,
  viewFieldId: 'lossesYear4',
  warnings: [{
    label: 'Required Num. of Losses in Year 4',
    condition: (val, fullObj, data) => {
      return lossHistoryItemHasValueAndIsRequired(fullObj, 'lossesYear4', data);
    },
    group: WARNING_GROUPS.carrier,
    carriers: plansRequiringLossFreeYears
  }]
};

const lossHistoryItemsValue5: WarningDefinitionI = {
  id: 'lossesYear5',
  deepId: 'lossesYear5',
  viewUri: generateViewUrlUnderwriting,
  viewFieldId: 'lossesYear5',
  warnings: [{
    label: 'Required Num. of Losses in Year 5',
    condition: (val, fullObj, data) => {
      return lossHistoryItemHasValueAndIsRequired(fullObj, 'lossesYear5', data);
    },
    group: WARNING_GROUPS.carrier,
    carriers: plansRequiringLossFreeYears
  }]
};


// LOSS HISTORY DETAILS

function lossHistoryItemHasDetails(fullObj, lossYear, lossHistoryRequirements): boolean {
  if (fullObj[lossYear] && fullObj[lossYear] !== '_0' && fullObj[lossYear] !== '') {
    let detailsRequired = false;
    let requiredYears = 0;
    if (lossHistoryRequirements && lossHistoryRequirements.length) {
      lossHistoryRequirements.forEach(requirement => {
        if (requirement) {
          if (requirement.requirementType === 'LossDetail') {
            detailsRequired = true;
          }
          if (requirement.requirementYears) {
            const numberYears: number = +requirement.requirementYears.substr(1, 2);
            if (requiredYears < numberYears) {
              requiredYears = numberYears;
            }
          }
        }
      });
    }
    // tslint:disable-next-line:max-line-length
    if (fullObj.lossFreeYears && (+fullObj.lossFreeYears.substr(1, 2) < requiredYears) && detailsRequired && (+lossYear.substr(10, 11)) <= requiredYears) {
      if (fullObj && fullObj.items && fullObj.items.length) {
        if (fullObj.items.length === 3) {
          return false;
        }
        const lossDetails = [];
        fullObj.items.forEach(item => {
          if (item.lossesYear === lossYear) {
            lossDetails.push(item);
          }
        });
        if (lossDetails.length === +fullObj[lossYear].substr(1, 2)) {
          return false;
        }
      }
      return true;
    } else {
      return false;
    }
  }
  return false;
}

const lossHistoryItemsDetails1: WarningDefinitionI = {
  id: 'items',
  deepId: 'items',
  viewUri: generateViewUrlUnderwriting,
  viewFieldId: 'modal-details-lossesYear1',
  warnings: [{
    label: 'Required Losses Details in Year 1',
    condition: (val, fullObj, data) => {
      return lossHistoryItemHasDetails(fullObj, 'lossesYear1', data);
    },
    group: WARNING_GROUPS.carrier,
    carriers: plansRequiringLossFreeYears
  }]
};

const lossHistoryItemsDetails2: WarningDefinitionI = {
  id: 'items',
  deepId: 'items',
  viewUri: generateViewUrlUnderwriting,
  viewFieldId: 'modal-details-lossesYear2',
  warnings: [{
    label: 'Required Losses Details in Year 2',
    condition: (val, fullObj, data) => {
      return lossHistoryItemHasDetails(fullObj, 'lossesYear2', data);
    },
    group: WARNING_GROUPS.carrier,
    carriers: plansRequiringLossFreeYears
  }]
};

const lossHistoryItemsDetails3: WarningDefinitionI = {
  id: 'items',
  deepId: 'items',
  viewUri: generateViewUrlUnderwriting,
  viewFieldId: 'modal-details-lossesYear3',
  warnings: [{
    label: 'Required Losses Details in Year 3',
    condition: (val, fullObj, data) => {
      return lossHistoryItemHasDetails(fullObj, 'lossesYear3', data);
    },
    group: WARNING_GROUPS.carrier,
    carriers: plansRequiringLossFreeYears
  }]
};

const lossHistoryItemsDetails4: WarningDefinitionI = {
  id: 'items',
  deepId: 'items',
  viewUri: generateViewUrlUnderwriting,
  viewFieldId: 'modal-details-lossesYear4',
  warnings: [{
    label: 'Required Losses Details in Year 4',
    condition: (val, fullObj, data) => {
      return lossHistoryItemHasDetails(fullObj, 'lossesYear4', data);
    },
    group: WARNING_GROUPS.carrier,
    carriers: plansRequiringLossFreeYears
  }]
};

const lossHistoryItemsDetails5: WarningDefinitionI = {
  id: 'items',
  deepId: 'items',
  viewUri: generateViewUrlUnderwriting,
  viewFieldId: 'modal-details-lossesYear5',
  warnings: [{
    label: 'Required Losses Details in Year 5',
    condition: (val, fullObj, data) => {
      return lossHistoryItemHasDetails(fullObj, 'lossesYear5', data);
    },
    group: WARNING_GROUPS.carrier,
    carriers: plansRequiringLossFreeYears
  }]
};

export const WARNINGS_DEFINITIONS_HOME_LOSS_HISTORY: WarningDefinitionI[] = [
  lossHistoryValue
];

export const WARNINGS_DEFINITIONS_HOME_LOSS_HISTORY_ITEMS: WarningDefinitionI[] = [
  /** 09.12.18 - SA - Remove the Loss Value Requirements to allow for 0 values */
  /*lossHistoryItemsValue1,
  lossHistoryItemsValue2,
  lossHistoryItemsValue3,
  lossHistoryItemsValue4,
  lossHistoryItemsValue5,*/
  lossHistoryItemsDetails1,
  lossHistoryItemsDetails2,
  lossHistoryItemsDetails3,
  lossHistoryItemsDetails4,
  lossHistoryItemsDetails5
];
