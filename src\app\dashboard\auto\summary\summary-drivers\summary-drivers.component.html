<div class="row">
  <div class="col-xs-12">


    <div class="summarybox" *ngFor="let driver of arrDrivers;">
      <h3 class="summarybox__head">{{driver.fullName}}</h3>
      <div class="summarybox__body">
        <div class="summarybox__body-col u-width-60">
          <div class="summarybox__item">
            <div class="summarybox__item-label u-width-90px">D.O.B:</div>
            <div class="summarybox__item-value u-width-auto">{{driver.dob}}</div>
          </div>
          <div class="summarybox__item">
            <div class="summarybox__item-label u-width-90px">1st License:</div>
            <div class="summarybox__item-value u-width-auto">{{driver.firstLicense}}</div>
          </div>
          <div class="summarybox__item">
            <div class="summarybox__item-label u-width-90px">Incidents:</div>
            <div class="summarybox__item-value u-width-auto">{{driver.incidents}}</div>
          </div>
        </div>
        <div class="summarybox__body-col u-width-auto">
          <div class="summarybox__item">
            <div class="summarybox__item-label u-width-90px">SDIP:</div>
            <div class="summarybox__item-value u-width-auto">{{driver.sdip}}</div>
          </div>
          <div class="summarybox__item">
            <div class="summarybox__item-label u-width-90px">Exclusions:</div>
            <div class="summarybox__item-value u-width-auto">{{driver.exclusions}}</div>
          </div>
          <div class="summarybox__item">
            <div class="summarybox__item-label u-width-90px">Deffered:</div>
            <div class="summarybox__item-value u-width-auto">{{driver.deffered}}</div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>