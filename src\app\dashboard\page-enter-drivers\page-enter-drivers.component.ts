
import {take} from 'rxjs/operators';
import { FormData } from './../../app-model/form';
import { Component, OnInit, ViewChild } from '@angular/core';

import { ClientDetails } from 'app/app-model/client';
import { DriverRmvToSend } from 'app/app-model/driver';
import { DriversService } from '../app-services/drivers.service';
import { Location } from '@angular/common';
import { LookupsService } from 'app/dashboard/app-services/lookups.service';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { Router } from '@angular/router';
import { StorageService } from 'app/shared/services/storage-new.service';
import { QuoteNewRMVData, QuoteNewRMVResponseData } from 'app/app-model/quote';
import { SubscriptionLike as ISubscription } from 'rxjs';
import { NgForm } from '@angular/forms';
import { format, parse } from 'date-fns';


@Component({
    selector: 'app-page-enter-drivers',
    templateUrl: './page-enter-drivers.component.html',
    styleUrls: ['./page-enter-drivers.component.scss'],
    standalone: false
})
export class PageEnterDriversComponent implements OnInit {

  @ViewChild('refEnterNewDriver') public refEnterNewDriver: NgForm;

  private quoteData: QuoteNewRMVData;
  private quoteDataSubscription: ISubscription;

  public driverList: Array<DriverRmvToSend> = [new DriverRmvToSend()];

  private newRmvClientInfo: ClientDetails = new ClientDetails();

  public selectedDriverIndex: number;

  public selectedDriverToDelete: any;
  private formSubmitted = false;
  public invalidForm = false;
  public errorMessage: string;
  public isLookupSuccess = true;
  public duplicateLicense = false;

  constructor(
    private storageService: StorageService,
    private driverService: DriversService,
    private location: Location,
    private lookupsService: LookupsService,
    private overlayLoaderService: OverlayLoaderService,
    private router: Router
  ) {}

  ngOnInit() {
    this.getNewRmvDriverRmvToSend();
    this.setDriversList();
  }

  ngOnDestroy() {
    this.quoteDataSubscription && this.quoteDataSubscription.unsubscribe();
  }

  private getNewRmvDriverRmvToSend(): void {
    this.storageService.getStorageData('newRmvDriverRmvToSend').pipe(
      take(1))
      .subscribe((data: DriverRmvToSend[]) => {
        if (data && data.length) {
          this.driverList = data;
        }
      });
  }

  private setDriversList(): void {
    this.quoteDataSubscription = this.storageService.getStorageData('newRmvQuoteData').subscribe( rmvData => this.quoteData = rmvData);
  }

  public selectDriverForDelete(index: number): void {
    this.selectedDriverIndex = index;
  }

  public saveDate($ev, driver: DriverRmvToSend, index: number) {
    driver.dateOfBirth = $ev.formatedDate;
    this.updateClient(index, driver);
  }

  public addDriver(event: Event, refForm: any = null) {
    event.preventDefault();

    if (this.isValidForm(this.driverList)) {
      this.driverList.push(new DriverRmvToSend());
      this.isValidForm(this.driverList);
    } else {
      this.formSubmitted = true; // Set as submitted to show error
    }
  }

  // Validation
  // ----------------------------------------------------------------------------
  private isValidSingleDriverRmvToSend(driver: DriverRmvToSend): boolean {
    const datePattern = /^\d{2}\/\d{2}\/\d{4}$/i;

    if (this.isEmptyValue(driver.firstName)
        || this.isEmptyValue(driver.lastName)
        || this.isEmptyValue(driver.license)
        || (this.isEmptyValue(driver.dateOfBirth) || !datePattern.test(driver.dateOfBirth))) {
          return false;
        }

    return true;
  }

  private isValidForm(drivers: DriverRmvToSend[]): boolean {
    const rowsResults: boolean[] = drivers.map(driver => this.isValidSingleDriverRmvToSend(driver));
    const isInvalid = rowsResults.indexOf(false) !== -1;
    this.invalidForm = isInvalid;

    if (this.invalidForm) {
      this.errorMessage = 'First and Last Name, License #, and D.O.B. are required to perform a lookup.';
    }
    return !isInvalid;
  }

  // Confirm Box
  // ----------------------------------------------------------------------------
  public deleteDriver(index: number): void {
    if (index != null) {
      this.driverList.splice(index, 1);
      this.updateClient(0, this.driverList[0]);
      this.isValidForm(this.driverList);
    }
  }

  public confirmDelete() {
    console.log('Deleted driver with index: ', this.selectedDriverIndex);
    this.deleteDriver(this.selectedDriverIndex);
    this.duplicateLicense = this.checkForDuplicateDriverLicense();
    this.updateDriverRmvToSendInStorage();
  }

  public confirmCanceled($event) {
    console.log('Cancel: ', $event);
  }

  public get driversCount() {
    return this.driverList.length;
  }

  // ----------------------------------------------------------------------------

  public newRmvQuote(refEnterNewDriver) {
    this.formSubmitted = true;
    this.isLookupSuccess = true;

    if (this.isValidForm(this.driverList)) {
      this.overlayLoaderService.showLoader();
      const drivers = [];
      // drivers.push({'multiples are possible': ''});

      this.driverList.forEach( driver => {
        drivers.push(Object.assign({}, driver));
      });


      drivers.forEach(driver => {
        driver.dateOfBirth = format(parse(driver.dateOfBirth, 'MM/dd/yyyy', new Date()), 'yyyy-MM-dd');
      });
      console.log(this.quoteData.effectiveDate);
      if (!this.quoteData.effectiveDate) {
        this.quoteData.effectiveDate = format(new Date(), 'yyyy-MM-dd');
      }

      const data = {
        policyEffectiveDate: this.quoteData.effectiveDate,
        drivers: drivers,
        lob: 'autop'
      };

        this.lookupsService.newRmvQuote(data).subscribe((response: QuoteNewRMVResponseData) => {
          this.storageService.setStorageData('responseNewRmvQuoteResult', response);
          if (response.vehicles && response.vehicles.length) {
            this.storageService.setStorageData('rmvVehicleLookup', response.vehicles);
          }
          // this.router.navigate(['/dashboard/rmv-results']);
          this.router.navigate(['/dashboard/auto/rmv-results']);
          this.isLookupSuccess = true;
          this.overlayLoaderService.hideLoader();
          },
          err => {
            if (err.error.Message) {
              console.log('An exception is thrown: ', err.status, err.statusText, err.error.Message);
            }

              this.isLookupSuccess = false;
              this.errorMessage = 'There was a problem performing the lookup. Please contact Boston Software.';

            this.overlayLoaderService.hideLoader();
          }
        );

    } else {
      // Show INFO
    }
  }

  private updateClient(index: number, driver: DriverRmvToSend) {
    if (index === 0) {
      this.newRmvClientInfo.firstName = driver.firstName;
      this.newRmvClientInfo.lastName = driver.lastName;
      this.newRmvClientInfo.dob = driver.dateOfBirth;
      this.lookupsService.updateNewRmvClient(this.newRmvClientInfo);
      this.storageService.setStorageData('clients', [this.newRmvClientInfo]);
    }
  }

  public lettersOnly(event) {
    const letters = /^[A-Za-z]{1}$/ig;
    const multiKey = event.ctrlKey || event.metaKey;
    const keyNormalized = event.key?.toLocaleLowerCase();

    if (!(
      keyNormalized === 'backspace' ||
      keyNormalized === 'delete' ||
      keyNormalized === 'tab' ||
      keyNormalized === 'arrowleft' ||
      keyNormalized === 'left' ||
      keyNormalized === 'arrowright' ||
      keyNormalized === 'right' ||
      keyNormalized === 'a' && multiKey ||
      keyNormalized === 'z' && multiKey ||
      keyNormalized === 'c' && multiKey ||
      keyNormalized === 'v' && multiKey ||
      letters.test(event.key)
    )) { event.preventDefault(); }
  }

  public updateDriverRmvToSendInStorage(): void {
    this.storageService.setStorageData('newRmvDriverRmvToSend', this.driverList);
  }

  public showErrorInfo(): boolean {
    return (this.formSubmitted && this.invalidForm) || (this.formSubmitted && !this.isLookupSuccess);
  }

  public onInputDataChange(): void {
    this.duplicateLicense = this.checkForDuplicateDriverLicense();
    this.updateDriverRmvToSendInStorage();
    this.isValidForm(this.driverList); // Check form if is valid each time the data changes;
  }

  public checkForDuplicateDriverLicense() {
    const temp: any = {};
    let isdup = false;
        this.driverList.map((item) => {
          const licenseNum = item.license;
          if (licenseNum in temp) {
            isdup = true;
          } else {
            temp[licenseNum] = item;
          }
        });
        isdup ? this.errorMessage = 'You have entered the same License Number more than once. Please remove duplicate license requests.' : this.errorMessage = '';
        return isdup;
  }


  // Helpers
  // ----------------------------------------------------------------------------
  private isEmptyValue(value: any): boolean {
    if (value === undefined || value == null) {
      return true;
    }

    value = String(value);
    return value.trim().length <= 0 || !value;
  }
}
