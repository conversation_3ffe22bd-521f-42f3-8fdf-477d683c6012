<section class="section section--compact">
  <div class="row">
    <div class="col-xs-12 u-spacing--1">
      <div class="o-container o-container--scroll-horizontal">

        <table class="table table--row-border-less" style="width:auto">
          <thead>
            <tr>
              <th><div class="u-width-min-190px"></div></th>
              <th *ngFor="let vehicleCoveragesData of vehiclesStandardCoverages" class="u-width-190px">
                {{displayVehicleNameByResourceId(vehicleCoveragesData.vehicleResourceId)}}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr class="u-bg-dim-grey u-color-white">
              <td class="u-t-weight--bold">SET OVERALL COVERAGE:</td>
              <td class="u-align-center" *ngFor="let vehicleCoveragesData of vehiclesStandardCoverages, let first=first">
                @for(option of optionsOverallCoverages; track option; let index = $index)
                  {
                    @if(option.id !== 'Custom') {
                    <button class="o-btn o-btn--outlined" [disabled]="disabledSetOverallCoverageField(vehicleCoveragesData.vehicle)" (click)="actionOnSelectedOverallCoverage(option, vehicleCoveragesData)">{{index +1}}</button>
                    }
                  }
                <!-- <sm-autocomplete
                fieldAutofocus
                  [id]="vehicleCoveragesData.vehicleResourceId"
                  [options]="optionsOverallCoverages"
                  [(ngModel)]="vehicleCoveragesData.defaultOption"
                  name= "activeDefaultOptions_{{vehicleCoveragesData.vehicleResourceId}}"
                  [css]="disabledSetOverallCoverageField(vehicleCoveragesData.vehicle) ? 'u-bg-i-light-grey' : ''"
                  [activeOption]="activeOptionsOverallCoverages[vehicleCoveragesData.vehicleResourceId]"
                  [disabled]="disabledSetOverallCoverageField(vehicleCoveragesData.vehicle)"
                  (onSelect)="actionOnSelectedOverallCoverage($event, vehicleCoveragesData)"
                  [searchFromBegining]="true">
                </sm-autocomplete> -->
              </td>
            </tr>

            <tr *ngFor="let row of coveragesTableData.rows; trackBy: trackByRow">
              <td>{{row.label}}</td>
              <td *ngFor="let cell of row.cells; trackBy: trackByCell"  class="u-align-center" [class.is-required-field]="cell.policyParsedToDisplay?.additionalData?.hasError || cell.policyParsedToDisplay?.currentValue === null">
                <span *ngIf="cell.onlyOneAvailableOption" [id]="cell.policyParsedToDisplay?.viewUqId">
                  {{cell.policyParsedToDisplay?.currentValue}}
                </span>

                <sm-autocomplete
                  *ngIf="!cell.onlyOneAvailableOption"
                  #selectCoverageInput
                  [options]="cell.coverageOptions"
                  [activeOption]="cell.policyParsedToDisplay.currentValue"
                  [id]="cell.policyParsedToDisplay.viewUqId"
                  [name]="cell.policyParsedToDisplay.viewUqId"
                  [required]="true"
                  [disabled]=""
                  [searchFromBegining]="true"
                  (onSelect)="actionOnOptionSelect($event, cell.policyParsedToDisplay, cell.vehicleCoverages)">
                </sm-autocomplete>
              </td>
            </tr>

          </tbody>
        </table>


      </div>
    </div>
  </div>
</section>

<app-modalbox #modalCoveragesValidation>
<h1 class="o-heading o-heading--red u-spacing--bottom-1-5">Coverages Information</h1>

    <div *ngFor="let msg of errorMsg; let i = index">
      <p>{{ msg }} </p>
    </div>
    <div class="row u-spacing--2">
      <div class="col-xs-12 u-align-right">
        <hr class="o-hr u-spacing--bottom-1-5"/>
        <button (click)="modalCoveragesValidation.close()" class="o-btn o-btn">OK</button>
      </div>
    </div>
</app-modalbox>


<app-modalbox #modalQuoteEffectiveDate>
  <h1 class="o-heading o-heading--red u-spacing--bottom-1-5">Limits Information</h1>

      <div>
        <p>Prior Vehicle Coverage Limits that were on this quote were below the allowed minimums for this effective date.  SinglePoint has bumped the limits to the new minimums allowed.  Review and make changes if desired.</p>
      </div>
      <div class="row u-spacing--2">
        <div class="col-xs-12 u-align-right">
          <hr class="o-hr u-spacing--bottom-1-5"/>
          <button (click)="modalQuoteEffectiveDate.close()" class="o-btn o-btn">OK</button>
        </div>
      </div>
  </app-modalbox>

<app-modalbox #modalCoveragesAdditionalInformation>
  <h1 class="o-heading o-heading--red u-spacing--bottom-1-5">Information</h1>

  <div>
    <p>Would you like to add Waiver of Deductible to this vehicle?</p>
  </div>
  <div class="row u-spacing--2">
    <div class="col-xs-12 u-align-right">
      <hr class="o-hr u-spacing--bottom-1-5"/>
      <button class="o-btn u-spacing--right-1" (click)="actionYesHandleWaiverOfDeductibleAdjust($event)">Yes</button>
      <button class="o-btn o-btn--outlined" (click)="modalCoveragesAdditionalInformation.close()">No</button>
    </div>
  </div>
</app-modalbox>
