<section class="section section--compact u-spacing--2-5">
    <div class="row">
        <h1 class="o-heading" style="color: #0b71ac; padding-left: 20px;">Drivers</h1>
      </div>
      <div class="row u-spacing--1">
        <div class="col-xs-12">
          <div class="box box--silver">
      <div class="row" *ngFor="let driver of driverList;let index = index" style="padding-bottom:1rem">
          <div class="col-lg-1 col-md-2">Driver {{index + 1}}</div>
          <div class="col-lg-3 col-md-2">
              <input [(ngModel)]="driver.firstName" name="firstName{{driver.uuid}}" placeholder="First Name" (change)="onInputDataChange()">
          </div>
          <div class="col-lg-3 col-md-2">
            <input [(ngModel)]="driver.lastName" name="lastName{{driver.uuid}}" placeholder="Last Name" (change)="onInputDataChange()">
        </div>
        <div class="col-lg-2 col-md-2">
            <input required [(ngModel)]="driver.license" name="license{{driver.uuid}}" placeholder="License #" (change)="onInputDataChange()">
        </div>
        <div class="col-lg-2 col-md-3">
              <app-datepicker-input [required]="true"
              [(ngModel)]="driver.dateOfBirth"
              name="dob{{driver.uuid}}"
              #refPickerDateOfBirth ngDefaultControl [placeholder]="'MM/dd/yyyy'" [selectDate]="driver.dateOfBirth" [returnDateFormat]="'yyyy-MM-dd'"
          (onDateChange)="setDate($event,driver)">
        </app-datepicker-input>
        </div>
        <div class="col-lg-1">
          <button *ngIf="index>0 && driverList?.length > 1" type="button" (click)="deleteDriver(index)" class="o-btn o-btn--action o-btn--i_cancel u-t-size--1-7rem modal-delete-driver" tabindex="-1"></button>
        </div>
      </div>
      <div class="row">
          <div class="offset-md-3">
            <button (click)="addDriver($event);" type="button" class="o-btn o-btn--action o-btn--i_round-plus u-color-pelorous">Add Driver</button>
          </div>
      </div>
      </div>
      </div>
      </div>
</section>
