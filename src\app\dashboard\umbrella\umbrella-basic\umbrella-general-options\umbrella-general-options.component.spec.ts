import { async, ComponentFixture, inject, TestBed } from '@angular/core/testing';

import { StubOptionsViewDropdownComponent } from 'testing/stubs/components/options-view-dropdown.component';

import { CoverageItemParsed } from 'app/app-model/coverage';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Helpers } from 'app/utils/helpers';

import { UmbrellaGeneralOptionsComponent } from './umbrella-general-options.component';

describe('UmbrellaGeneralOptionsComponent', () => {
  let component: UmbrellaGeneralOptionsComponent;
  let fixture: ComponentFixture<UmbrellaGeneralOptionsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [
        UmbrellaGeneralOptionsComponent,
        StubOptionsViewDropdownComponent
      ],
      providers: [
        StorageService
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(UmbrellaGeneralOptionsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
