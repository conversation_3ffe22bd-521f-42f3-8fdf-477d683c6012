import { Component, OnInit, AfterViewInit, ViewChild } from '@angular/core';
import { NavigationExtras, Router } from '@angular/router';
import { ModalboxComponent } from 'app/shared/components/modalbox/modalbox.component';
import { take, first } from 'rxjs/operators';
import { RmvService } from '../../../app-services/rmv.service';
import { SpecsService } from '../../../app-services/specs.service';
import { DatesService } from '../../../app-services/dates.service';
import { FilterComponent } from 'app/shared/components/filter/filter.component';
import { FilterDatesComponent } from 'app/shared/components/filter-dates/filter-dates.component';
import { DateRange } from 'app/app-model/date';
import { format } from 'date-fns';


@Component({
    selector: 'app-saved-rmv-services-table',
    templateUrl: './saved-rmv-services-table.component.html',
    styleUrls: ['./saved-rmv-services-table.component.scss'],
    standalone: false
})

export class SavedRmvServicesTableComponent implements OnInit {

  @ViewChild('deleteModalbox') public deleteModalbox: ModalboxComponent;
  @ViewChild(FilterDatesComponent) filterDates;

  list = [];
  criteria;
  searchQuery;
  size = 0;
  limit = 10;
  paginationResultShowFrom = 0;
  dataInitiated = false;
  searchType = 'vin';
  typeOptions = [];
  isDatesFilterViewEnabled = false;

  public isAllSelected = false;
  public selectedItem: string[] = [];
  paginationCurrentPage = 1;

  public dateRange: DateRange = new DateRange();


  constructor(private rmvService: RmvService, private specsService: SpecsService, private router: Router) { }

  ngOnInit(): void {
    setTimeout(() => this.getTransactions(true));
    this.specsService.getTransactionsAsOptions().subscribe(x => this.typeOptions = x);
    this.isDatesFilterViewEnabled = JSON.parse(localStorage.getItem('features')).find(x => x.name === 'SprApp_DatesFilterView');
  }

  getDisplayName(transactionType) {
    const result = this.typeOptions.find(x => x.id === transactionType);
    if (result) { return result.text; } else { return ''; }
  }

  filterByName(event) {
    if (event.key === 'Enter' && this.searchQuery.length >= 3) {
      this.getTransactions();
    } else if (event.target.tagName === 'BUTTON' && this.searchQuery.length >= 3) {
      this.getTransactions();
    }
  }

  public paginationPageChange(data) {
    if (data.startAt < 0) {
      data.startAt = 0;
      data.pageNumber = 1;
    }
    if (this.dataInitiated && this.paginationResultShowFrom !== data.startAt) {
      this.paginationCurrentPage = data.pageNumber;
      this.paginationResultShowFrom = data.startAt;
      this.getTransactions();
    }
  }

  private paginationSetResultLimit(intLimit: any) {
    this.limit = parseInt(intLimit, 10);
  }

  public onResultLimitChange($ev): void {
    if (this.limit !== $ev.limit) {
      setTimeout(() => {
        this.paginationSetResultLimit($ev.limit);
        this.getTransactions();
      }
      );
    }
  }


  getTransactions(showResultsFromFirstPage = false) {
    if (showResultsFromFirstPage) {
      this.paginationResultShowFrom = 0;
      this.paginationCurrentPage = 1;
    }

    let startDate, endDate;

    let criteria = this.searchQuery !== '' && this.searchQuery !== undefined ? `?${this.searchType}=${this.searchQuery}` : '';
    if (criteria == '') {
      startDate = this.dateRange.start ? format(new Date(this.dateRange.start), 'yyyy-MM-dd') : '';
      endDate = this.dateRange.end ? format(new Date(this.dateRange.end), 'yyyy-MM-dd') : '';
    }

    criteria !== '' ? criteria += `&workflowType=RmvServices&excludeRRD=true` : criteria += `?workflowType=RmvServices&excludeRRD=true`;
    if (startDate != undefined && endDate != undefined) {
      criteria = criteria + `&startDate=` + startDate + '&endDate=' + endDate;
    }

    // criteria = `?offset=${this.paginationResultShowFrom}`;
    this.rmvService.getSavedRmvList(criteria, this.paginationResultShowFrom, this.limit).pipe(take(1)).subscribe(x => {
      this.dataInitiated = true;
      this.list = x.items; this.size = x.size;
    });

    if (this.searchQuery === '') {
      this.searchType = 'vin';
    }

  }

  resetSearch() {
    this.dateRange.start = null;
    this.dateRange.end = null;
    this.getTransactions();
  }

  public isSelected(Identifier: string): boolean {
    return (this.selectedItem.indexOf(Identifier) > -1 || this.isAllSelected);
  }

  public toggleSelected(Identifier: string): void {
    const itemIndex = this.selectedItem.indexOf(Identifier);
    const isPresent = (itemIndex > -1);

    if (isPresent) {
      this.selectedItem.splice(itemIndex, 1);
    } else {
      this.selectedItem.push(Identifier.toString());
    }
  }

  public isChecked(): boolean {
    return this.isAllSelected;
  }

  public deleteSelectedRequest() {
    const deleteArray = [];
    this.selectedItem.forEach(x => { deleteArray.push(x); console.log(deleteArray); });
    this.rmvService.deleteRmvTransactions(deleteArray).subscribe(x => {
      this.deleteModalbox.close();
      this.getTransactions();
    });
  }

  public toggleAll(): void {
    this.isAllSelected = !this.isAllSelected;

    if (this.isAllSelected) {
      this.list.forEach(item => {
        if (this.selectedItem.indexOf(item.rmvServicesId) === -1) {
          this.selectedItem.push(item.rmvServicesId.toString());
        }
      });
    } else {
      this.selectedItem = [];
    }
  }

  public showHideDiv(): boolean {
    let result = true;
    if (this.isDatesFilterViewEnabled) {
      result = this.searchQuery && this.searchQuery.length >= 3;
    }
    return result;
  }

  public IsDatesFilterViewEnabled(): boolean {
    return this.isDatesFilterViewEnabled;
  }

  private getTransactionsFromFilteredDates() {

    this.dateRange = this.filterDates.dateRange;

    let allowFiltering = this.filterDates.isFilteringAllowed();

    if (allowFiltering) {
      this.getTransactions();
    }
    this.filterDates.tooltip.close();
  }




}
