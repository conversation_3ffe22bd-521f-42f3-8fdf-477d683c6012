<div>
  <h2 class="o-heading" *ngIf="standAlone">New Form</h2>

  <div [ngClass]="{'box box--silver u-spacing--1-5': standAlone}">
      <div class="row">
        <div class="col-xs-12" *ngIf="selectedForm.number !== 'RTA'">
            <div class="u-padd--bottom-1">
                <p>Select a plan:</p>
            </div>
            <ul class="checklist" [class.is-required-field]="highlightInvalidField('Rating Plan')" >
                <li class="checklist__item">
                    <sm-autocomplete
                    #refDropdownPlans
                    [options]="plansOnQuotes"
                    [activeOption]=""
                    [readonly]="false"
                    (onSelect)="actionOnSelectPlanId($event)">
                  </sm-autocomplete>
                </li>
            </ul>
        </div>
      </div>
      <hr  *ngIf='quoteLob==="AUTOP" && selectedForm.number !== "RTA"'/>
      <div class="row" *ngIf='quoteLob==="AUTOP"'>
          <div class="col-xs-12">
              <div class="u-padd--bottom-1" *ngIf="selectedForm.vehicleLimit==1">
                  <p>You must select a vehicle:</p>
              </div>
              <div class="u-padd--bottom-1" *ngIf="selectedForm.vehicleLimit > 1">
                  <p>You cannot select more than {{selectedForm.vehicleLimit}} vehicles: <p>
              </div>

              <ul class="checklist" [class.is-required-field]="highlightInvalidField('Vehicle')">
                  <li class="checklist__item" *ngFor="let row of vehicles | orderBy: 'orderIndex';index as i;">
                    <div class="checklist__btns" *ngIf="selectedForm.vehicleLimit==1">
                      <label class="o-checkable" >
                        <input type="radio" name="vehicles" [checked]="this.quoteVehicles.length===1" (change)="setSelectedVehicle(row.resourceId, i)" />
                        <i class="o-btn o-btn--radio"></i>
                      </label>
                    </div>
                    <div class="checklist__btns" *ngIf="selectedForm.vehicleLimit>1">
                      <label class="o-checkable" >
                        <!--[checked]="this.quoteVehicles.length===1"-->
                        <input type="checkbox" name="vehicles"
                        (change)="setSelectedVehicle(row.resourceId, i)"
                        [checked]="quoteVehicles.length===1"
                        [disabled]= "!isVehicleSelected(i) && selectedVehicleIds.length >= selectedForm.vehicleLimit"/>
                        <i class="o-btn o-btn--checkbox"
                        [ngClass]="{'is-disabled': !isVehicleSelected(i) && selectedVehicleIds.length >= selectedForm.vehicleLimit }" >
                        </i>
                      </label>
                    </div>
                    <div class="checklist__label">
                      <p class="checklist__label-main">{{row.year}} {{row.make}} {{row.model}}</p>
                      <p class="checklist__label-comment"></p>
                    </div>
                  </li>
              </ul>
          </div>
      </div>
      <hr *ngIf='selectedForm?.driverLimit > 0'/>
      <div class="row" *ngIf='selectedForm?.driverLimit > 0'>
          <div class="col-xs-12"  *ngFor="let i of selectedForm.driverLimit|iter; let idx = index">
              <div class="u-padd--bottom-1" *ngIf="selectedForm?.driverLimit > 0">
                  <p>Owner {{i}}:</p>
              </div>
              <ul class="checklist u-padd--bottom-1" [class.is-required-field]="highlightInvalidField('Owner') && idx == 0">
                  <li class="checklist__item">
                      <sm-autocomplete
                      #refDropdownPlanTypes{{i}}
                      [options]="getDriverList(i-1)"
                      [activeOption]="selectedDrivers[i-1]"
                      [disabled]="i > selectedDrivers.length + 1"
                      (onSelect)="actionOnSelectOwner($event, i-1)">
                    </sm-autocomplete>
                  </li>
              </ul>
           </div>
      </div>
      <hr  *ngIf='selectedForm.number.toUpperCase()==="90MA"'/>
      <div class="row" *ngIf='selectedForm.number.toUpperCase()==="90MA"'>
          <div class="col-xs-12">
              <div class="u-padd--bottom-1">
                  <p>Do you want to mask Private Information on the form when viewing or printing?</p>
              </div>
              <ul class="checklist">
                <li class="checklist__item" >

               <div class="checklist__btns">
                <label class="o-checkable" >
                  <input type="checkbox" name="maskPrivateInformation" [(ngModel)]="maskPrivateInformation" />
                  <i class="o-btn o-btn--checkbox"></i>
                </label>
              </div>
              <div class="checklist__label">
                <p class="checklist__label-main">Mask Private Information</p>
                <p class="checklist__label-comment"></p>
              </div>
              </li>
              </ul>
          </div>
      </div>
  </div>

  <div *ngIf="standAlone" class="row u-spacing--1-5">
    <div class="col-xs-12 u-align-right u-remove-letter-spacing">
      <button type="button" [disabled]="createButtonDisabled" (click)="handleNewFormCreateForm($event)" class="o-btn">Create Form</button>
      <button (click)="handleCancelClick($event)" class="o-btn o-btn--idle u-spacing--left-2" style="margin-left:1.8rem;">Cancel</button>
    </div>
  </div>

  <div class="leave-quote--inside">
    <app-leave-quote #leaveQuote></app-leave-quote>
  </div>

</div>

<div class="modalPlansOnQuotes">
  <app-modalbox #modalMissingUserInput [css]="'u-width-600px horizontal-centered'" (click)="$event.stopPropagation()">
    <h1 class="o-heading o-heading--red u-spacing--bottom-1-5">{{missingUserInputTitle}}.</h1>
    <div class="box box--silver u-spacing--1-5 u-flex u-flex--spread u-flex--to-top">
        <div class="u-padd--bottom-1" >
            <p>Please provide the following form fields:</p>
        </div>
        <div class="col-xs-12"  *ngFor="let missingUserInput of missingUserInputs">
            <div class="u-padd--bottom-1" *ngIf="missingUserInput.length > 0">
                <p>{{missingUserInput}}</p>
            </div>
        </div>
      </div>
      <div class="row u-spacing--2">
      <div class="col-xs-12 u-align-right">
        <hr class="o-hr u-spacing--bottom-1-5" />
        <button (click)="modalMissingUserInput.closeModalbox()" class="o-btn o-btn--idle u-spacing--left-2">Close</button>
      </div>
    </div>
  </app-modalbox>
</div>
