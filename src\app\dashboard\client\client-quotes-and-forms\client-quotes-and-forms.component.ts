
import {take, first} from 'rxjs/operators';
import { UserData } from 'app/shared/services/agency-user.service';
import { AgencyUserService } from './../../../shared/services/agency-user.service';
import { ApiService } from 'app/shared/services/api.service';
import { ClientDetails } from 'app/app-model/client';
import { Component, OnInit, ViewChild, OnDestroy } from '@angular/core';
import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { FormsService } from 'app/dashboard/app-services/forms.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { LeaveQuoteComponent } from 'app/shared/components/leave-quote/leave-quote.component';
import { Observable } from 'rxjs';
import { format } from 'date-fns';

@Component({
    selector: 'app-client-quotes-and-forms',
    templateUrl: './client-quotes-and-forms.component.html',
    styleUrls: ['./client-quotes-and-forms.component.scss'],
    standalone: false
})
export class ClientQuotesAndFormsComponent implements OnInit, OnDestroy {
  public arrQuotesAll: any;
  public paginationQResultsCount = 0;
  public paginationFResultsCount = 0;
  public paginationQResultShowFrom = 0;
  public paginationFResultShowFrom = 0;
  public paginationFResultShowTo = 9;
  public paginationQResultLimit = 10;
  public paginationFResultLimit = 10;
  public arrFormsAll: any;
  public formsCount = 0;
  selectedClient: ClientDetails;
  storageClientSubscription: any;
  formCreated: boolean;
  subscriptionAgency: any;
  agencyInfo: any;
  userData: UserData;
  public paginationFCurrentPage = 1;
  private componentInitialized = false;
  constructor(
    private quotesService: QuotesService,
    private formsService: FormsService,
    private storageService: StorageService,
    private apiService: ApiService,
    private agencyUserService: AgencyUserService
  ) {}

  @ViewChild('leaveQuote') leaveQuote: LeaveQuoteComponent;
  canDeactivate(): Observable<boolean> | boolean {
    this.leaveQuote.detectConfirmation();
    return this.leaveQuote.detectConfirmationObservable.asObservable().pipe(first());
  }

  ngOnInit() {
    this.subscribeClient()
      .then(() => this.getAgentData())
      .then(() => this.getUser())
      .then(() => this.getFormsForClient(
        this.paginationFResultShowFrom.toString(),
        this.paginationFResultLimit.toString(),
        this.selectedClient.clientIdentifier))
      .then(() => {
        this.componentInitialized = true;
      })
      .catch(err => console.log('ERROR:', err));
    // this.paginationFCurrentPage = 1;
  }
  ngOnDestroy(): void {
    this.subscriptionAgency && this.subscriptionAgency.unsubscribe();
  }
  private getAgentData(): Promise<void> {
    return new Promise((resolve) => {
      this.subscriptionAgency = this.agencyUserService.userData$.subscribe(
        agency => {
          if (agency && agency.user) {
            this.agencyInfo = agency;
          }
          resolve();
        }
      );
    });
  }

  private getUser(): Promise<void> {
    return new Promise<void>((resolve) => {
      this.agencyUserService.userData$.pipe(
        first())
        .subscribe(data => {
          this.userData = data;
          resolve();
        });
    });
  }

  private subscribeClient(): Promise<void> {
    return new Promise<void>((resolve) => {
      this.storageClientSubscription = this.storageService
        .getStorageData('selectedClient')
        .subscribe(data => {
          this.selectedClient = data;
          resolve();
        });
    });
  }

  public loadingFormsList = false;

  private getFormsForClient(start, end, clientId): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      if (isNaN(start)) {
        start = 0;
      }
      if (isNaN(end)) {
        end = 9;
      }

      if (!this.loadingFormsList && clientId) {
        this.loadingFormsList = true;

        this.formsService
          .getAgencyForms(
            start,
            end,
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            '',
            clientId,
            ''
          ).pipe(
          take(1))
          .subscribe(
            formsObj => {
              this.arrFormsAll = formsObj.items;
              this.formsCount = formsObj.size;
              // this.filterFResults('');
              this.loadingFormsList = false;
              resolve();
            },
            error => {
              this.loadingFormsList = false;
              reject(error);
            }
          );
      }
    });
  }


  public loadingQuotesList = false;
  private getQuotes(
    offset: string = '0',
    limit: string = '10',
    name: string = '',
    agentId: string = '',
    lob: string = '',
    locationId: string = '',
    dateType: string = '',
    startDate: string = '',
    endDate: string = '',
    clientId: string = ''
  ): void {
    if (!this.loadingQuotesList && clientId) {
      this.loadingQuotesList = true;
      this.quotesService
        .getQuotes(
          offset,
          limit,
          name,
          agentId,
          lob,
          locationId,
          dateType,
          startDate,
          endDate,
          clientId
        ).pipe(
        take(1))
        .subscribe(
          quotesObj => {
            this.arrQuotesAll = quotesObj.items;
            this.paginationQResultsCount = quotesObj.size;
            this.filterQResults('');
            this.loadingQuotesList = false;
          },
          error => {
            this.loadingQuotesList = false;
          }
        );
    }
  }
  public filterQResults(search: string) {
    this.getQuotes(
      this.paginationQResultShowFrom.toString(),
      this.paginationQResultLimit.toString(),
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      this.selectedClient.clientIdentifier
    );
  }

  // private filterFResults(search: string) {
  //   this.getFormsForClient(
  //     this.paginationFResultShowFrom.toString(),
  //     this.paginationFResultLimit.toString(),
  //     this.selectedClient.clientIdentifier
  //   );
  // }
  public formatInitialDate(date): string {
    let formattedDate;
    if (date) {
      formattedDate = format(new Date(date), 'yyyy-MM-dd');
    } else {
      formattedDate = '-';
    }
    return formattedDate;
}
  public paginationQuotePageChange(data) {
    if (data.startAt !== '' && !Number.isNaN(data.startAt)) {
      this.paginationQResultShowFrom = data.startAt;
    } else {
      // this.paginationQResultShowFrom = '0';
      this.paginationQResultShowFrom = 0;
    }
    this.getQuotes(
      this.paginationQResultShowFrom.toString(),
      this.paginationQResultLimit.toString(),
      '',
      '',
      '',
      '',
      '',
      '',
      '',
      this.selectedClient.clientIdentifier
    );
  }

  public paginationFormPageChange(data) {
    if (!this.componentInitialized) {
      return;
    }

    if (
      this.paginationFCurrentPage === data.pageNumber
      && this.paginationFResultShowFrom === data.startAt
      && this.paginationFResultShowTo === data.endAt
    ) {
      return;
    }

    if (data && data.endAt) {
      this.paginationFResultShowTo = data.endAt;
    }

    if (data && data.startAt) {
    this.paginationFCurrentPage = (this.paginationFCurrentPage !== data.pageNumber) ? data.pageNumber : this.paginationFCurrentPage;
    this.paginationFResultShowFrom = data.startAt;
    } else {
    this.paginationFCurrentPage = 1;
    this.paginationFResultShowFrom = 0;
    }

    this.getFormsForClient(
      this.paginationFResultShowFrom.toString(),
      this.paginationFResultLimit.toString(),
      this.selectedClient.clientIdentifier
    );
  }
  private paginationSetFResultLimit(intLimit: any) {
    const tmpLimit = parseInt(intLimit, 10);

    if (!isNaN(tmpLimit) && this.paginationFResultLimit !== tmpLimit) {
      this.paginationFResultLimit = parseInt(intLimit, 10);
    }
  }
  public onFResultLimitChange($ev) {
    this.paginationSetFResultLimit($ev.limit);
  }

  private paginationSetQResultLimit(intLimit: any) {
    const tmpLimit = parseInt(intLimit, 10);

    if (!isNaN(tmpLimit) && this.paginationQResultLimit !== tmpLimit) {
      this.paginationQResultLimit = parseInt(intLimit, 10);
    }
  }
  public onQResultLimitChange($ev) {
    this.paginationSetQResultLimit($ev.limit);
  }
  private openAgencyForm(agencyForm): Promise<any> {
    let formWindow: Window;
    const baseUrl =
      document.location.protocol +
      '//' +
      document.location.hostname +
      ':' +
      document.location.port +
      '/' +
      document.location.pathname;
    const loadingPage = baseUrl + '/assets/html-templates/loading.html';

    // formWindow = window.open('', '_blank');
    formWindow = window.open(loadingPage, '_blank');
    return new Promise<void>((resolve, reject) => {
      this.formsService
        .getAgencyFormAuthorization(this.agencyInfo.agencyId).pipe(
        take(1))
        .subscribe(
          authorizationToken => {
            const createdFormHref =
              this.agencyInfo.agencyId +
              '/openform?agencyformid=' +
              agencyForm.identifier +
              '&agentId=' +
              this.userData.user.userId +
              '&ticket=' +
              encodeURIComponent(authorizationToken.ticket) +
              '&requestId=' +
              authorizationToken.requestId;
            formWindow.location.href = this.apiService.formAppUrl(
              createdFormHref
            );
            resolve();
            this.formCreated = true;
          },
          err => {
            formWindow.location.href =
              baseUrl + '/assets/html-templates/loading-error.html';
            reject(err);
          }
        );
    });
  }
}
