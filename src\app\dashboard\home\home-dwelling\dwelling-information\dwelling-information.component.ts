
import {filter, take, first} from 'rxjs/operators';
import { CoverageItemParsed } from './../../../../app-model/coverage';
import { QuotePlanListAPIResponse , QuotePlan} from 'app/app-model/quote';
import { Observable ,  SubscriptionLike as ISubscription } from 'rxjs';
import { Component, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { SubsService } from 'app/dashboard/app-services/subs.service';
import { DwellingService } from 'app/dashboard/app-services/dwelling.service';
import { FilterOption } from 'app/app-model/filter-option';
import { Town, Dwelling, DwellingProtectionClass, FireDistrict } from 'app/app-model/dwelling';
import { LocationData } from 'app/app-model/location';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { ClientAddress } from 'app/app-model/client';
import { Router, NavigationEnd, NavigationStart } from '@angular/router';
import { ClientsService } from 'app/dashboard/app-services/clients.service';
import { ModalboxComponent } from 'app/shared/components/modalbox/modalbox.component';
import { Validate } from 'app/hints-and-warnings/validators';
import { PolicyItemDefaultDataAPIResponse, Coverage, CoveragesData, CoverageApiResponseData, CoverageValue } from 'app/app-model/coverage';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { CoveragesService } from 'app/dashboard/app-services/coverages.service';
import { differenceInYears, getYear, parseISO } from 'date-fns';



@Component({
    selector: 'app-dwelling-information',
    templateUrl: './dwelling-information.component.html',
    styleUrls: ['./dwelling-information.component.scss'],
    standalone: false
})
export class DwellingInformationComponent implements OnInit {

  constructor(
    private storageService: StorageService,
    private subsService: SubsService,
    private dwellingService: DwellingService,
    private overlayLoaderService: OverlayLoaderService,
    private specsService: SpecsService,
    private router: Router,
    private clientsService: ClientsService,
    private apiCommonService: ApiCommonService,
    private coveragesService: CoveragesService
  ) { }

  get formType() {
    return this.quote.formType.replace(' ', '');
  }

  static readonly Unassigned ='Unassigned';

  @ViewChild('refWarningModalbox') public refWarningModalbox: ModalboxComponent;

  @ViewChild('refStateModel') public refStateModel;
  @ViewChild('refZipCodeModel') public refZipCodeModel;

  public quoteId = '';

  public quote;
  public pcResourceId = '';
  public dwelling: Dwelling = new Dwelling();
  public dwellingProtectionClass: DwellingProtectionClass = new DwellingProtectionClass();
  public construction: Object[] = [
   { id: 'BrickOrStone', text: 'Brick or Stone'},
   { id: 'BrickOrStoneVeneer', text: 'Brick or Stone Veneer'},
   { id: 'Frame', text: 'Frame'},
   { id: 'FrameWithAlumSiding', text: 'Frame With Alum Siding'},
   { id: 'FrameWithPlasticSiding', text: 'Frame With Plastic Siding'},
   { id: 'SuperiorFireResistance', text: 'Superior Fire Resistance'}
  ];
  public stories: Object[] = [
    {id: 'One', text: '1'},
    {id: 'OneAndHalf', text: '1.5'},
    {id: 'Two', text: '2'},
    {id: 'TwoAndHalf', text: '2.5'},
    {id: 'Three', text: '3'},
    {id: 'ThreeAndHalf', text: '3.5'},
    {id: 'Four', text: '4'},
    {id: 'BiLevel', text: 'Bi-Level'},
    {id: 'TriLevel', text: 'Tri-Level'}
  ];
  public states: Array<string> = [
    'AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DC', 'DE', 'FL',
    'GA', 'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME',
    'MD', 'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH',
    'NJ', 'NM', 'NY', 'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI',
    'SC', 'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY'];

  public occupancy: FilterOption[] = [
    {id: 'Primary', text: 'Primary'},
    {id: 'Secondary', text: 'Secondary'},
    {id: 'SeasonalOrVacation', text: 'Seasonal/Vacation'}
  ];
  public families: Object[] = this.generateOptions(5, 1, true);
  public mortgagees: Object[] = this.generateOptions(5, 0, true);
  public townhouseUnits: Object[] = this.generateOptions(51, 1, true, true);

  public hasPool = false;
  public squareFootage: number;
  public yearBuilt: number;
  public age: number;
  public cities: FilterOption[] = [];
  public fireDistricts: FilterOption[] = [];
  public isoProtectionClass = '';
  public town: Town = new Town();
  public fireDistrictsTown: Town = new Town();
  public dwellingLocation: LocationData = new LocationData();
  public loadingData = false;
  public protectionClassTown;
  public errorMessage = '';
  public constructionYearResetValue: number = null;
  public constructionAgeResetValue: number = null;
  private selectedPlans: QuotePlan[] = [];
  private selectedPlansIds: string[] = [];
  private selectedQuoteFormTypes: string[] = [];

  private subscriptionCities: any;
  private subscriptionFireDistricts: any;
  private subscriptionState: any;
  private subscriptionDwellingData: any;
  private subscriptionGetProtectionClass: any;
  private subscriptionGetProtectionClassOverrides: any;
  private subscriptionDwellingDataLocation: any;
  private subscriptionQuoteData: any;
  private subscriptionSubsystems: ISubscription;
  private subscriptionQuotePlans: ISubscription;
  private subscriptionQuoteFormTypes: ISubscription;

  private subscriptionHomeDefaultOptions: ISubscription;
  private subscriptionHomeCarrierOptions: ISubscription;
  private subscriptionQuoteIsNew: ISubscription;
  private lastValidConstructionYear: number;
  private allowDwellingDataUpdate = true;
  private subsystemProperties: string[] = [
    'electricalLastUpdatedYear',
    'heatingPrimaryLastUpdatedYear',
    'plumbingLastUpdatedYear',
    'roofLastUpdatedYear'
  ];

  private earliestSubsystemUpdateYear = 0;
  private homeDefaultOptions: Coverage[] = [];
  private homeCarrierOptions: CoverageItemParsed[] = [];
  private quoteIsNew = false;

  public showOccupancyConfirmation = false;
  private subscriptionQuoteCoverages;
  private quoteCoverages;
  private tmpData;

  private creatingLocation = false;

  // Updating Client Address
  // ----------------------------------------------------------------------------
  private subscriptionClientAddresses: ISubscription;
  private subscriptionRouter: ISubscription;
  private allowUpdateClientAddress = false;
  private clientCurrentAddress: ClientAddress = null;

  ngOnInit() {
    this.subscribeQuoteIsNew()
      .then(() => {
        this.subscribeHomeDefaultOptions();
      })
      .then(() => this.getQuote())
      .then(() => {
        this.subscribeHomeDefaultOptions();
        this.subscribeHomeCarrierOptions();
        this.subscribeQuoteSelectedPlans();
        this.subscribeQuoteFormTypes();
        this.initTown();
        this.subscribeSubsystems();
        this.setCities('HOME', this.town.state);
        this.setFireDistrict('HOME', this.town.state);
        this.getProtectionClass();
        this.getDwellingLocation();
        this.initClientAddressAutoUpdater();
        this.getQuoteCoverages();
        console.log(this.townhouseUnits)
      })
      .catch(err => console.log('ERR: ', err));
  }

  ngOnDestroy() {
    this.subscriptionCities && this.subscriptionCities.unsubscribe();
    this.subscriptionFireDistricts && this.subscriptionFireDistricts.unsubscribe();
    this.subscriptionDwellingData && this.subscriptionDwellingData.unsubscribe();
    this.subscriptionGetProtectionClass && this.subscriptionGetProtectionClass.unsubscribe();
    this.subscriptionGetProtectionClassOverrides && this.subscriptionGetProtectionClassOverrides.unsubscribe();
    this.subscriptionDwellingDataLocation && this.subscriptionDwellingDataLocation.unsubscribe();
    this.subscriptionQuoteData && this.subscriptionQuoteData.unsubscribe();
    this.subscriptionSubsystems && this.subscriptionSubsystems.unsubscribe();
    this.subscriptionQuotePlans && this.subscriptionQuotePlans.unsubscribe();
    this.subscriptionQuoteFormTypes && this.subscriptionQuoteFormTypes.unsubscribe();
    this.subscriptionHomeDefaultOptions && this.subscriptionHomeDefaultOptions.unsubscribe();
    this.subscriptionHomeCarrierOptions && this.subscriptionHomeCarrierOptions.unsubscribe();
    this.subscriptionState && this.subscriptionState.unsubscribe();
    this.subscriptionQuoteIsNew && this.subscriptionQuoteIsNew.unsubscribe();
    this.subscriptionQuoteCoverages && this.subscriptionQuoteCoverages.unsubscribe();
    this.destroyClientAddressesAutoUpdater();
  }

  private getSelectedState() {
    this.subscriptionState = this.storageService.getStorageData('selectedState').subscribe( data => {
      if (data) {
        this.town.state = data;
      } else {
        this.town.state = this.quote.state;
      }
    });
  }

  private subscribeHomeDefaultOptions(): void {
    this.subscriptionHomeDefaultOptions = this.storageService.getStorageData('homeDefaultOptions')
      .subscribe((res: PolicyItemDefaultDataAPIResponse) => {
        if (res?.items) {
          this.homeDefaultOptions = res.items;
        }
      });
  }
  private subscribeHomeCarrierOptions(): void {

    this.subscriptionHomeDefaultOptions = this.storageService.getStorageData('homeCarrierOptionsParsed')
      .subscribe((res: CoverageItemParsed[]) => {
        if (res) {
          this.homeCarrierOptions = res;
        }
      });
  }

  private subscribeQuoteIsNew(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.subscriptionQuoteIsNew = this.storageService.getStorageData('isNewQuote')
        .subscribe((res: boolean) => {
          this.quoteIsNew = res;
          resolve(res);
        });
    });
  }

  private subscribeQuoteSelectedPlans(): void {
    this.subscriptionQuotePlans = this.storageService.getStorageData('selectedPlan')
      .subscribe((res: QuotePlanListAPIResponse) => {
        if (res?.items?.length) {
          this.selectedPlans = JSON.parse(JSON.stringify(res.items[0].items));

          this.selectedPlansIds = this.selectedPlans.map(plan => plan.ratingPlanId);
        }
      });
  }

  private subscribeQuoteFormTypes(): void {
    this.subscriptionQuoteFormTypes = this.storageService.getStorageData('selectedQuoteFormTypes')
      .subscribe((res: string[]) => {
        this.selectedQuoteFormTypes = JSON.parse(JSON.stringify(res));
      });
  }

  private subscribeSubsystems(): void {
    this.subscriptionSubsystems = this.storageService.getStorageData('dwellingSubsystems').subscribe(data => {
      if ( data
        && data.items
        && data.items.length
      ) {
        const items = data.items[0];
        const years = this.subsystemProperties.map(property => {
          const propYear = parseInt(items[property], 10);
          return isNaN(propYear) ? Infinity : propYear;
        });
        this.earliestSubsystemUpdateYear = Math.min.apply(Math, years);
      }
    });
  }

  public updateDwellingLocation() {
    if ( this.dwellingLocation
      && this.dwellingLocation.meta
      && this.dwellingLocation.meta.href) {
      this.dwellingService
        .updateDwellingLocation(this.dwellingLocation)
        .subscribe( data => {  this.dwellingLocation = data;  });
    }
  }

  public actionOnConstructionYearChange(): void {
    if (this.dwelling) {
      if (this.dwelling.constructionYear) {
        this.dwelling.constructionAge = this.calcAge(this.dwelling.constructionYear);
      } else {
        this.dwelling.constructionAge = null;
      }

      const isValid = this.validateDwelling(this.dwelling);

      if (!isValid) {
        this.refWarningModalbox.openModalbox();
      } else {
        this.saveDwellingData(this.dwelling);
      }

    } else {
      console.log('No Dwelling Data');
    }
  }

  public actionOnConstructionAgeChange(): void {
    if (this.dwelling) {
      if (this.dwelling.constructionAge) {
        this.dwelling.constructionYear = this.calcYear(this.dwelling.constructionAge);
      } else {
        this.dwelling.constructionYear = null;
      }

      const isValid = this.validateDwelling(this.dwelling);

      if (!isValid) {
        this.refWarningModalbox.openModalbox();
      } else {
        this.saveDwellingData(this.dwelling);
      }
    } else {
      console.log('No Dwelling Data');
    }
  }

  public actionOnModalboxClose(event): void {
    if (event && event.isOpen !== false) {
      return;
    }

    if (this.constructionYearResetValue) {
      this.dwelling.constructionYear = this.constructionYearResetValue;
    }

    if (this.constructionAgeResetValue) {
      this.dwelling.constructionAge = this.constructionAgeResetValue;
    }

    // Reset Values
    this.errorMessage = '';
    this.constructionYearResetValue = null;
    this.constructionAgeResetValue = null;
    this.lastValidConstructionYear = null;

    this.saveDwellingData(this.dwelling);
  }

  public actionOnModalboxBtnClose(event): void {
    event.preventDefault();
    this.refWarningModalbox.closeModalbox();
  }

  public calcAge(constructionYear: number): number {
    const tmp = isNaN(constructionYear) ? 0 : constructionYear;
    return getYear(new Date()) - tmp;
  }

  public calcYear(constructionAge: number): number {
    const tmp = isNaN(constructionAge) ? 0 : constructionAge;
    return getYear(new Date()) - tmp;
  }


  public setLastValidConstructionYear(year) {
    this.lastValidConstructionYear = parseInt(year, 10);
  }

  public  updateDwellingDetails($event, prop) {
    if (!$event && !prop) {
      return;
    }

    console.log('Update TOWN');

    this.town.state = this.dwellingLocation.state;
    this.town.townName = this.dwellingLocation.city;

    switch (prop) {
      case 'city':
        this.town.countyId = $event.selectedOption.data.countyId;
        this.town.countyName = $event.selectedOption.data.countyName;
        this.town.id = $event.selectedOption.data.id;
        this.town.state = $event.selectedOption.data.state;
        this.fireDistrictsTown.townCode = this.town.townCode;
        this.town.townName = $event.selectedOption.data.townName;
        this.dwellingLocation.city = $event.selectedOption.data.townName;

        if (this.town.townName !== this.protectionClassTown) {
         setTimeout(() => this.UpdateProtectionClassCityFireDistrict(this.dwellingLocation.city), 1000);
          this.protectionClassTown = this.town.townName;
        }
        this.setFireDistrictSelection(this.dwellingLocation.city);


      break;
      case 'fireDistrict':
      if($event.id) {
        this.fireDistrictsTown.id = $event.selectedOption.data.fireDistrictId;
        this.fireDistrictsTown.state = $event.selectedOption.data.state;
        this.fireDistrictsTown.lob = $event.selectedOption.data.lob;
        this.fireDistrictsTown.townName = $event.selectedOption.data.fireDistrictName;
        this.fireDistrictsTown.townCode = $event.selectedOption.data.fireDistrictName;
        this.dwellingProtectionClass.fireDistrictsTown = this.fireDistrictsTown;
        this.dwellingProtectionClass.town = this.fireDistrictsTown;
        this.updateProtectionClass(this.dwellingProtectionClass, true).then(() => {
          this.dwellingLocation.fireDistrict = $event.selectedOption.data.fireDistrictName;
          this.updateDwellingLocation();
        });
      }
      break;

      case 'townCode':
        if (!this.dwellingLocation.city || !this.dwellingLocation.state) {
          return;
        }
        this.town.townCode = $event;
        this.dwellingProtectionClass.town.townCode = $event;
        this.updateProtectionClass(this.dwellingProtectionClass, true).then(() => {
          this.dwellingLocation.zip = $event.trim();
          this.updateDwellingLocation();
        });
      break;

      case 'state':
        this.town.state = $event.selectedOption.id;
        this.dwellingProtectionClass.town = this.town;
        this.dwellingLocation.state = $event.selectedOption.id;
        this.updateProtectionClass(this.dwellingProtectionClass).then(() => {
          this.dwellingLocation.state = $event.selectedOption.id;
          this.updateDwellingLocation();
        });
      break;
    }
  }

  public getSelectedCityOption(): FilterOption | undefined {
    let result;

    if (this.dwellingLocation.city) {
      result = this.cities.find(city => city.id.toLocaleLowerCase() === this.dwellingLocation.city.toLocaleLowerCase());
    } else {
      result = this.cities.find(city => city.id === this.dwellingLocation.city);
    }

    return result;
  }

  public getSelectedFireDistrictOption(): FilterOption | undefined {
    let result;


    if (this.dwellingLocation.fireDistrict) {
      result = this.fireDistricts.find(fireDistrict => fireDistrict.id.toLocaleLowerCase() === this.dwellingLocation.fireDistrict.toLocaleLowerCase());
    } else {

      result = this.fireDistricts.find(fireDistrict => fireDistrict.id === this.dwellingLocation.fireDistrict);

      }


    return result;
  }

  public selected($event, data: Dwelling, field, update: boolean = true, tryToConvertToNumber: boolean = false) {
    let dataToSet = $event.id;
    if (tryToConvertToNumber && dataToSet) {
      const tmpData = parseInt(dataToSet, 10);
      if (!isNaN(tmpData)) {
        dataToSet = tmpData;
      }
    }

    if ($event && field) {
      data[field] = $event.id;
    }

    if (update) {
      if (field === 'utilizationTypeCode' && $event.id !== 'Primary') {
        this.showOccupancyConfirmation = true;
        this.tmpData = JSON.parse(JSON.stringify(data));
      } else {
        this.saveDwellingData(data);
        if (this.quoteCoverages && this.quoteCoverages.items && this.quoteCoverages.items.length && this.quoteCoverages.items[0].coverages && this.quoteCoverages.items[0].coverages.length) {
          const pl = this.quoteCoverages.items[0].coverages.find( cov => cov.coverageCode === 'PL');
          const medpm = this.quoteCoverages.items[0].coverages.find( cov => cov.coverageCode === 'MEDPM');
          if (pl && pl.values && pl.values.length && pl.values[0].value === 'Excluded') {
            this.updateCoverageAPI('PL', '100000', true);
          }
          if (medpm && medpm.values && medpm.values.length && medpm.values[0].value === 'Excluded') {
            this.updateCoverageAPI('MEDPM', '1000');
          }
        }
      }
    }
  }

  private initTown() {
    this.getSelectedState();
  }

  private getQuote(): Promise<any> {
    return new Promise<void>((resolve, reject) => {
      this.subscriptionDwellingData = this.storageService.getStorageData('dwelling').subscribe(data => {
        if (data) {
          this.dwelling = JSON.parse(JSON.stringify(data));

          if (DwellingInformationComponent.Unassigned === this.dwelling.storiesCount) {
            this.dwelling.storiesCount = '';
          }


          if (DwellingInformationComponent.Unassigned === this.dwelling.constructionMaterialTypeCode) {
            this.dwelling.constructionMaterialTypeCode = '';
          }

          if (DwellingInformationComponent.Unassigned === this.dwelling.utilizationTypeCode) {
            this.dwelling.utilizationTypeCode = '';
          }

          // Set default options values if new Quote
          this.setDefaultDwellingValuesIfNewQuote(this.dwelling, this.homeDefaultOptions);
        }
      });

      this.subscriptionQuoteData = this.storageService.getStorageData('selectedQuote').subscribe(data => {
        if (data) {
          this.quote = JSON.parse(JSON.stringify(data));
          this.quoteId = this.quote.resourceId;
          if (this.formType === 'HO4' || this.formType === 'HO6') {
            this.families = this.generateOptions(2, 1, true, true);
            this.dwelling.familiesCount = 1;
          }
        }
        resolve();
      });
    });
  }



  private getDwellingLocation() {
    this.subscriptionDwellingDataLocation = this.storageService.getStorageData('dwellingLocation').subscribe(  data => {
      if (data && data.meta && data.meta.href) {
        this.dwellingLocation = JSON.parse(JSON.stringify(data));
        // this.setFireDistrictSelection(data.city);
        if (!this.dwellingLocation.state) {

          if (this.town && this.town.state && this.town.state.length !== 0) {
            this.dwellingLocation.state = this.town.state;
          } else {
            this.dwellingLocation.state = this.quote.state;
          }
         // this.updateDwellingLocation();
        }
      } else {
        this.getDwellingLocationFromAPI();
      }
    });
  }

  private UpdateProtectionClassFireDistrictSelection(selectedCity: string) {
    if (this.dwellingLocation.fireDistrict == null) {
    this.setFireDistrictSelection(selectedCity);
    } else {
      this.setFireDistrictSelection(this.dwellingLocation.fireDistrict);
    this.fireDistrictsTown.townName = this.dwellingLocation.fireDistrict;
    }


  }

  private UpdateProtectionClassCityFireDistrict(selectedValue) {

    this.dwellingProtectionClass.fireDistrictsTown = selectedValue;
    this.updateProtectionClass(this.dwellingProtectionClass, true);

  }


  private setFireDistrictSelection(selectedCity: string) {

           if (this.fireDistricts && this.fireDistricts.length > 0) {

          const  result = this.fireDistricts.find(fireDistrict => fireDistrict.id.toLocaleLowerCase() === selectedCity.toLocaleLowerCase());

            if ( result && result.data) {
            this.dwellingLocation.fireDistrict = selectedCity;
            this.fireDistrictsTown.townName = selectedCity;
            this.updateDwellingLocation();
            } else {
              this.fireDistrictsTown.townName = null;
              this.dwellingLocation.fireDistrict = null;
              this.updateDwellingLocation();
            }

          }

  }

  private getDwellingLocationFromAPI() {
    if (this.dwelling && this.dwelling.dwellingLocation && this.dwelling.dwellingLocation.href) {
      this.dwellingService.getDwellingLocation(this.dwelling.dwellingLocation.href).pipe(first()).subscribe( data => {
        if (data && data.items && !data.items.length) {
          this.createDwellingLocation();
        }
      });
    }
  }

  private generateOptions(count, index = 0, isNumber: boolean = false,addBlank = false) {
    const tempArr = [];
    if(addBlank) {
      tempArr.push({id:0,text:''})
    }
    while (index < count) {
      let customId: string | number = '' + index;
      if (isNumber) {
        customId = index;
      }
      tempArr.push({id: customId, text: index});
      index++;
    }
    return tempArr;
  }

  private setCities(lob: string, state: string): void {
    this.cities = [];
    const tmpOptions = [];

    this.subscriptionCities = this.specsService.getTownsList(state, 'HOME')
      .subscribe(
        res => {
          // Array.prototype.push.apply(this.cities, this.helperTownsToCitiesOptions(res.items));
          Array.prototype.push.apply(tmpOptions, this.helperTownsToCitiesOptions(res.items));
          this.cities = tmpOptions;
        },
        err => console.log(err)
      );
  }
  private setFireDistrict(lob: string, state: string): void {
    this.fireDistricts = [];

    this.subscriptionFireDistricts = this.specsService.getFireDistrictList(state, 'HOME')
      .subscribe(
        res => {
          this.fireDistricts = this.helperFireDistrictToCitiesOptions(res.items);
           this.UpdateProtectionClassFireDistrictSelection(this.town.townName) ;
        },
        err => console.log(err)
      );

  }

  private helperTownsToCitiesOptions(townsList: Town[]): FilterOption[] {
    let options: FilterOption[] = [];

    options = townsList.map(town => {
      const option = new FilterOption();
      option.id = town.townName;
      option.text = town.townName;
      option.data = town;

      return option;
    });

    return options;
  }
  private helperFireDistrictToCitiesOptions(FireDistrictsList: FireDistrict[]): FilterOption[] {
    let options: FilterOption[] = [];

    options = FireDistrictsList.map(fireDistrictsTown => {
      const option = new FilterOption();
      option.id = fireDistrictsTown.fireDistrictName;
      option.text = fireDistrictsTown.fireDistrictName;
      option.data = fireDistrictsTown;

      return option;
    });

    return options;
  }



  private getProtectionClass() {
    this.subscriptionGetProtectionClass = this.storageService.getStorageData('dwellingProtectionClass').subscribe( data => {
      if (data && data['items'] !== undefined) {
        this.town = data['items'][0]['town'];
        this.protectionClassTown = data['items'][0]['town'];

        this.pcResourceId = data['items'][0]['meta']['href'];

        return this.dwellingProtectionClass = data['items']['0'];
      } else if (data && data['meta'] && data['meta']['href']) {
        this.town = data['town'];
        this.protectionClassTown = data['town'].townName;
        this.pcResourceId = data['meta']['href'];

        return this.dwellingProtectionClass = data;
      }
    });
  }

  private updateProtectionClassIntialLoad(pc: DwellingProtectionClass): Promise<any> {
    if (this.pcResourceId && pc) {
      this.overlayLoaderService.showLoader();

      return this.dwellingService.updateDwellingProtectionClass(this.pcResourceId, pc).toPromise().then(response => {
        this.storageService.setStorageData('dwellingProtectionClass', this.dwellingProtectionClass);
        this.overlayLoaderService.hideLoader();
      }).catch(reject => {
        this.overlayLoaderService.hideLoader();
      });
    }
  }

  private updateProtectionClass(pc: DwellingProtectionClass, overrideSave = false): Promise<any> {
    if (this.pcResourceId && pc) {

      if (this.protectionClassTown !== this.town.townName || overrideSave) {
      this.overlayLoaderService.showLoader();
      return this.dwellingService.updateDwellingProtectionClass(this.pcResourceId, pc).toPromise().then(response => {
        this.dwellingProtectionClass['isoProtectionClass'] = response['isoProtectionClass'];
        this.storageService.setStorageData('dwellingProtectionClass', this.dwellingProtectionClass);
        this.protectionClassTown = this.dwellingProtectionClass.town.townName;
        this.overlayLoaderService.hideLoader();
      }).catch(reject => {
        this.overlayLoaderService.hideLoader();
      });
    }
    }
  }

  private saveDwellingData(data: Dwelling) {
    if (this.validateDwelling(data) || this.allowDwellingDataUpdate) {
      this.allowDwellingDataUpdate = false;
      setTimeout( () => {
        this.dwellingService
          .updateDwelling(data).pipe(
          take(1))
          .subscribe(
            res => {
              this.allowDwellingDataUpdate = true;
            },
            err => this.allowDwellingDataUpdate = true,
            () => this.allowDwellingDataUpdate = true,
          );
      }, 300);
    }
  }

  private validateDwelling(data) {
    const MAX_ALLOWED_CONSTRUCTION_AGE = 400;
    const constructionYear = parseInt(data.constructionYear, 10);
    const constructionAge = parseInt(data.constructionAge, 10);
        const currentYear = getYear(new Date());

    const earliestSubsystemUpdateYear = this.earliestSubsystemUpdateYear;

    // Reset Values
    this.errorMessage = '';
    this.constructionYearResetValue = null;
    this.constructionAgeResetValue = null;

    if (constructionAge > MAX_ALLOWED_CONSTRUCTION_AGE) {
      this.errorMessage = `The dwelling cannot be more than ${MAX_ALLOWED_CONSTRUCTION_AGE} years old.`;

      if (this.lastValidConstructionYear && !isNaN(this.lastValidConstructionYear)) {
        this.constructionYearResetValue = this.lastValidConstructionYear;
      } else {
        this.constructionYearResetValue = currentYear - MAX_ALLOWED_CONSTRUCTION_AGE;
      }

      this.constructionAgeResetValue = this.calcAge(this.constructionYearResetValue);

      return false;
    } else if (constructionAge < 0) {
      this.errorMessage = `The year the dwelling was built cannot be greater than the current year.`;

      if (this.lastValidConstructionYear && !isNaN(this.lastValidConstructionYear)) {
        this.constructionYearResetValue = this.lastValidConstructionYear;
      } else {
        this.constructionYearResetValue = currentYear;
      }

      this.constructionAgeResetValue = this.calcAge(this.constructionYearResetValue);
      return false;
    } else if (constructionYear > earliestSubsystemUpdateYear) {
      // this.errorMessage = `Year built cannot be greater than the year of the earliest subsystem update.`;
      this.errorMessage = `Year of Update cannot be prior to Year Built.`;

      if (this.lastValidConstructionYear && !isNaN(this.lastValidConstructionYear)) {
        this.constructionYearResetValue = this.lastValidConstructionYear;
      } else {
        this.constructionYearResetValue = earliestSubsystemUpdateYear;
      }

      this.constructionAgeResetValue = this.calcAge(this.constructionYearResetValue);

      return false;
    } else {
      this.errorMessage = '';
      this.constructionYearResetValue = null;
      this.constructionAgeResetValue = null;
      this.lastValidConstructionYear = null;
      return true;
    }
  }

  private setConstructionYearAndAge(year) {
        const currentYear = getYear(new Date());

    if (year === 0) {
      this.dwelling.constructionYear = 0;
      this.dwelling.constructionAge = 0;
    } else if (year) {
      this.dwelling.constructionYear = year;
      this.dwelling.constructionAge = currentYear - year;
    }
  }
  private createDwellingLocation() {
    if (this.dwelling && this.dwelling.dwellingLocation && this.dwelling.dwellingLocation.href && !this.creatingLocation) {
      this.creatingLocation = true;
      this.dwellingService.createDwellingLocation(this.dwelling.dwellingLocation.href).pipe(take(1)).subscribe( data => {
        this.dwellingLocation = data;
        this.creatingLocation = false;

        // Update Dwelling location with default values
        if (this.town && this.town.state && this.town.state.length !== 0) {
            this.dwellingLocation.state = this.town.state;
        } else {
          this.dwellingLocation.state = this.quote.state;
        }
        this.updateDwellingLocation();
      });
    }
  }

  private setDefaultDwellingValuesIfNewQuote(dwelling: Dwelling, defaultOptions: Coverage[]): void {
    if (!this.quoteIsNew) {
      return;
    }

    let saveData = false;

    defaultOptions.forEach((item: Coverage) => {
      switch (item.coverageCode) {
        case 'Occupancy':
          if (!this.dwelling.utilizationTypeCode && item.values && item.values[0] && item.values[0].value !== '') {
            this.dwelling.utilizationTypeCode = item.values[0].value;
            saveData = true;
          }
          break;
          case 'Construction':
          if (!this.dwelling.constructionMaterialTypeCode && item.values && item.values[0] && item.values[0].value !== '') {
            this.dwelling.constructionMaterialTypeCode = item.values[0].value;
            saveData = true;
          }
          break;
          case 'Families':
          if (!this.dwelling.familiesCount && item.values[0] && item.values[0].value !== '') {
            this.dwelling.familiesCount = parseInt(item.values[0].value);
            saveData = true;
          }
          break;
      }
    });

    if (saveData) {
      this.saveDwellingData(dwelling);
    }
  }

  // HELPERS
  // ----------------------------------------------------------------------------
  // filter - numbers only
  public numbersOnly(event) {
    const digits = /^[0-9]{1}$/;
    const multiKey = event.ctrlKey || event.metaKey;
    const keyNormalized = event.key.toLocaleLowerCase();

    if (!(
      keyNormalized === 'enter' ||
      keyNormalized === 'backspace' ||
      keyNormalized === 'delete' ||
      keyNormalized === 'tab' ||
      keyNormalized === 'arrowleft' ||
      keyNormalized === 'left' ||
      keyNormalized === 'arrowright' ||
      keyNormalized === 'right' ||
      keyNormalized === 'a' && multiKey ||
      keyNormalized === 'z' && multiKey ||
      keyNormalized === 'c' && multiKey ||
      keyNormalized === 'v' && multiKey ||
      digits.test(event.key)
    )) { event.preventDefault(); }
  }

  private initClientAddressAutoUpdater(): void {
    this.subscribeClientAddresses();
    this.routeSubscribe();
  }

  private destroyClientAddressesAutoUpdater(): void {
    this.subscriptionClientAddresses && this.subscriptionClientAddresses.unsubscribe();
    this.subscriptionRouter && this.subscriptionRouter.unsubscribe();
  }

  private subscribeClientAddresses(): void {
    this.storageService.getStorageData('clientAddresses')
      .subscribe(addresses => {
        const tmpAddresses = JSON.parse(JSON.stringify(addresses));
        this.clientCurrentAddress = tmpAddresses.find(addr => addr.addressType === 'StreetAddress');
      });
  }

  private routeSubscribe(): void {
    this.subscriptionRouter = this.router.events.pipe(
      filter(event => event instanceof NavigationStart))
      .subscribe((event: NavigationStart) => {
        const dwellingViewUrl = this.router.url.split('?')[0];

        if (event.url !== dwellingViewUrl) {
          this.updateClientAddressIfEmpty();
        }
      });
  }

  private updateClientAddressIfEmpty(): void {
    if (this.clientCurrentAddress && this.checkIfAddressIsNotSet(this.clientCurrentAddress)) {
      // console.log('%c Try To Update Client Address ', 'color:red');
    this.clientCurrentAddress.address1 =  this.dwellingLocation.address1 ? this.dwellingLocation.address1.replace(/[^a-zA-Z0-9 ]/g, '') : '';
      this.clientCurrentAddress.address2 = this.dwellingLocation.address2 ? this.dwellingLocation.address2.replace(/[^a-zA-Z0-9 ]/g, '') : '';
      // this.clientCurrentAddress.city = this.dwellingLocation.city ? this.dwellingLocation.city.replace(/[^a-zA-Z0-9 ]/g, '') : '';
      this.clientCurrentAddress.city = this.dwellingLocation.city ?  this.dwellingLocation.city.replace(/\s*\(.*?\)\s*/g, '') : '';
      console.log(this.clientCurrentAddress.city);
      this.clientCurrentAddress.residencyDate = (this.dwellingLocation.residencyDate) ? this.dwellingLocation.residencyDate : null;
      this.clientCurrentAddress.state = this.dwellingLocation.state;
      this.clientCurrentAddress.zip = this.dwellingLocation.zip.trim();

      // Update API
      if (this.clientCurrentAddress) {
        // overlay-client.component require refactor, to be able to update address in storage after it was updated in API
        this.storageService.updateClientAddressesSingleItem(this.clientCurrentAddress);

        if (this.clientCurrentAddress && this.clientCurrentAddress.meta && this.clientCurrentAddress.meta.href) {
          this.apiCommonService.putByUri(this.clientCurrentAddress.meta.href, this.clientCurrentAddress)
            .toPromise()
            .then((res: ClientAddress) => {
              // console.log('Updated Address', res);
              // Update Storage
              // this.storageService.updateSingleClientAddress(res);
            })
            .catch(err => console.log(err));
        }
      }
    }
  }

  private checkIfAddressIsNotSet(address: ClientAddress): boolean {
    if (!address.address1
        && !address.address2
        && !address.city
        && !address.residencyDate
        // && !address.state
        && !address.zip
      ) {
        return true;
      }

    return false;
  }

  public townhouseCheckckboxIsDisabled(): boolean {
    return this.quote && (this.formType === 'HO6' || this.formType === 'HO4');
  }

  public townhouseUnitsFieldIsDisabled(): boolean {
    if(this.townhouseCheckckboxIsDisabled()) {
      return true;
    }
    return !this.dwelling.designStyleTownhouseInd && !this.townhouseCheckckboxIsDisabled();
    // return !this.dwelling.designStyleTownhouseInd;
  }

  public townhouseUnitsFieldIsRequired(): boolean {
    if(this.townhouseUnitsFieldIsDisabled()) {
      return false;
    }
    return  !this.townhouseUnitsFieldIsDisabled() && this.dwelling.townhouseUnitsCount <= 0;
  }

  public fieldNumOfStoriesIsRequired(): boolean {
    // https://bostonsoftware.atlassian.net/browse/SPR-3003
    // Required for: ASI, National General, Safeco, Union Mutual of Vermont, Hanover
    const requiredForPlans = ['105', '119', '173', '93', '282'];
    const valueToCheck: string = (!this.dwelling.storiesCount || this.dwelling.storiesCount === 'Unassigned') ? '' : this.dwelling.storiesCount;

    const requirement1 = Validate.isRequiredForSelectedPlansIfEmptyValue(
      // this.dwelling.storiesCount,
      valueToCheck,
      requiredForPlans,
      this.selectedPlansIds
    );

    return requirement1;
  }

  public fieldMortgageesIsRequired(): boolean {
    // Required for: Quincy Mutual Group, Safety (Standard)
    const requiredForPlans = ['92', '91'];

    return Validate.isRequiredForSelectedPlansIfEmptyValue(
      this.dwelling.mortgageeCount,
      requiredForPlans,
      this.selectedPlansIds
    ) && this.isHO4();
  }

  public fieldPoolIsRequired(): boolean {
    // Required for: Quincy Mutual Group
    const requiredForPlans = ['92', '303', '316'];
    const requiredForSplPlans = ['31', '32', '75', '76'];

    const validation1 =  Validate.isRadioInputValueNotSet(this.dwelling.poolOnPremisesInd) && Validate.isRequiredForSelectedPlans(
      requiredForPlans,
      this.selectedPlansIds
    );

    const carrierOptions = this.homeCarrierOptions;

    let additionalOption = false;

    if (this.homeCarrierOptions != null && this.homeCarrierOptions.length > 0) {
      const  currentopts: CoverageItemParsed[] = this.homeCarrierOptions.filter(
        opt => opt.coverageCode === 'BSC-HOME-022808');

      if (currentopts && currentopts.length > 0) {
        if (currentopts[0].currentValue === 'Yes' || currentopts[0].currentValue === 'No') {
          additionalOption = true;
        }
      }
    }

    const isArbellaPlan = Validate.isRequiredForSelectedPlans(
      requiredForSplPlans,
      this.selectedPlansIds
    );

    const validation2 =  Validate.isRadioInputValueNotSet(this.dwelling.poolOnPremisesInd) && additionalOption && isArbellaPlan;

    return validation1 || validation2;
  }

  public fieldFireDistrictIsRequired(): boolean {
    // Required for: FireDistrict requied for only MFD plans
    const requirement1 = Validate.isRequiredForSelectedPlansIfEmptyValue(
      this.dwellingLocation.fireDistrict,
      this.selectedPlans.filter(x => (x.ratingFlow.toUpperCase() === 'MFD' || x.ratingPlanId === '319')).map(y => y.ratingPlanId),
      this.selectedPlansIds
    );

    return requirement1 ;
  }

  public fieldSquareFootageIsRequired(): boolean {
    // Required for: National General, Safeco, Safety (Standard), Union Mutual of Vermont
    const requiredForPlans1 = ['173', '91', '93', '305', '306'];
    const requirement1 = Validate.isRequiredForSelectedPlansIfEmptyValue(
      this.dwelling.livingSpaceArea,
      requiredForPlans1,
      this.selectedPlansIds
    );

    // Required for: Travelers New (Plan ID 283)
    const requiredForPlans2 = ['283', '119', '332'];
    const requiredForFormType2 = ['HO3', 'HO5'];
    const requirement2 = Validate.isRequiredForSelectedFormTypes(
      requiredForFormType2,
      this.selectedQuoteFormTypes
    ) && Validate.isRequiredForSelectedPlansIfEmptyValue(
      this.dwelling.livingSpaceArea,
      requiredForPlans2,
      this.selectedPlansIds
    );

    const requiredForPlans3 = ['314', '105'];
    const requiredForFormType3 = ['HO3', 'HO5', 'HO6'];
    const requirement3 = Validate.isRequiredForSelectedFormTypes(
      requiredForFormType3,
      this.selectedQuoteFormTypes
    ) && Validate.isRequiredForSelectedPlansIfEmptyValue(
      this.dwelling.livingSpaceArea,
      requiredForPlans3,
      this.selectedPlansIds
    );

    return requirement1 || requirement2 || requirement3;
  }

  public isHO4(): boolean {
    return this.selectedQuoteFormTypes.indexOf('HO4') < 0;
  }

  public fieldYearBuiltIsInvalid(): boolean {
    const isRequired = Validate.isEmptyValue(this.dwelling.constructionYear);

    const isInvalidRule1 = this.fieldYearBuiltValidationRule1(this.dwelling, this.selectedPlansIds, this.selectedQuoteFormTypes);
    const isInvalidRule2 = this.fieldYearBuiltValidationRule2(this.dwelling, this.selectedPlansIds, this.selectedQuoteFormTypes);
    const isInvalidRule3 = this.fieldYearBuiltValidationRule3(this.dwelling, this.selectedPlansIds, this.selectedQuoteFormTypes);
    const isInvalidRule4 = this.fieldYearBuiltValidationRule4(this.dwelling, this.selectedPlansIds, this.selectedQuoteFormTypes);
    const isInvalidRule5 = this.fieldYearBuiltValidationRule5(this.dwelling, this.selectedPlansIds, this.selectedQuoteFormTypes);

    return isRequired || isInvalidRule1 || isInvalidRule2 || isInvalidRule3 || isInvalidRule4 || isInvalidRule5;
  }

  private getQuoteCoverages() {
    this.subscriptionQuoteCoverages = this.storageService.getStorageData('homeQuoteCoverages').subscribe( data => {
      if (data && data.meta && data.meta.href) {
        if (data['coverages'] && data['coverages'].length) {
          this.quoteCoverages = {items: [JSON.parse(JSON.stringify(data))]};
        } else {
          this.quoteCoverages = JSON.parse(JSON.stringify(data));
        }
        if ((this.quoteCoverages && !this.quoteCoverages.items) || (this.quoteCoverages && this.quoteCoverages.items && !this.quoteCoverages.items.length)) {
          if (this.quote && this.quote.coveragesStandard && this.quote.coveragesStandard.href) {
            this.overlayLoaderService.showLoader();
            this.coveragesService.createHomeCoverageByUriWithDefaultValues(this.quote.coveragesStandard.href).pipe(
              take(1))
              .subscribe(
                (res: CoveragesData) => {
                  this.quoteCoverages.items = [res];
                  // Save to storage
                  const tmpRes = new CoverageApiResponseData();
                  tmpRes.items = [res];
                  this.storageService.setStorageData('homeQuoteCoverages', tmpRes);
                  this.overlayLoaderService.hideLoader();
                },
                err => {
                  console.log(err);
                  this.overlayLoaderService.hideLoader();
                });
          }
        }
      } else {
        if (this.quote && this.quote.coveragesStandard && this.quote.coveragesStandard.href) {
          this.coveragesService.getHomeQuoteCoverages$(this.quote.coveragesStandard.href).pipe(take(1)).subscribe();
        }
      }
    });
  }

  public occupancyDecline() {
    this.saveDwellingData(this.tmpData);
    this.showOccupancyConfirmation = false;
  }

  public occupancyAccept() {
    this.saveDwellingData(this.tmpData);
    this.updateCoverageAPI('PL', 'Excluded', true);
    this.updateCoverageAPI('MEDPM', 'Excluded');
    this.showOccupancyConfirmation = false;
  }

  private updateCoverageAPI(coverageCode, value, wait: boolean = false) {
    if (this.quoteCoverages && this.quoteCoverages.items && this.quoteCoverages.items.length && this.quoteCoverages.items[0].coverages && this.quoteCoverages.items[0].coverages.length) {
      let inCoverages = false;
      this.quoteCoverages.items[0].coverages = this.quoteCoverages.items[0].coverages.map( (item) => {
        if (item.coverageCode === coverageCode) {
          const val = new CoverageValue();
          val.value = value;
          item.values = [val];
          inCoverages = true;
        }
        return item;
      });
      if (!inCoverages) {
        const newCoverage = new Coverage();
        newCoverage.values.push(new CoverageValue());
        newCoverage.coverageCode = coverageCode;
        newCoverage.values[0].value = value;
        this.quoteCoverages.items[0].coverages.push(newCoverage);
      }
    } else {
      const newCoverage = new Coverage();
      newCoverage.values.push(new CoverageValue());
      newCoverage.coverageCode = coverageCode;
      newCoverage.values[0].value = value;
      this.quoteCoverages.items[0].coverages.push(newCoverage);
    }
    if (!wait) {
      this.updateCoveragesAPI(this.quoteCoverages.items[0]);
    }
  }

  private updateCoveragesAPI(coveragesObj) {
    this.overlayLoaderService.showLoader();
    this.apiCommonService.putByUri(coveragesObj.meta.href, coveragesObj).pipe(take(1)).subscribe( res => {
      this.storageService.setStorageData('homeQuoteCoverages', res);
      this.overlayLoaderService.hideLoader();
    }, err => this.overlayLoaderService.hideLoader());
  }

  // Rule 1:: (https://bostonsoftware.atlassian.net/browse/SPRC-365) (https://app.smartsheet.com/b/home?lx=wrx37F1hxf1XQrMBnVN7QA)
  // For Plans: Barnstable County Mutual  - Preferred [94], Barnstable County Mutual - Protector Plus [69]
  // Condition:
  // H03 form type - Minimum Year Built accepted >=1987
  // H05 form type - Minimum Year Built accepted >=1987
  private fieldYearBuiltValidationRule1(dwelling: Dwelling, selectedPlansIds: string[], selectedFormTypes: string []): boolean {
    const requiredForFormTypes = ['HO3', 'HO5'];
    const requiredForPlans = ['94', '69'];
    let isInvalid = false;

    const allowValidationForFormTypes = requiredForFormTypes.some(item => selectedFormTypes.indexOf(item) !== -1);
    const allowValidationForSelectedPlans = Validate.isRequiredForSelectedPlans(requiredForPlans, selectedPlansIds);

    if (allowValidationForFormTypes && allowValidationForSelectedPlans) {
      if (dwelling.constructionYear) {
        const tmpConstructionYear = Number(dwelling.constructionYear);
        isInvalid = (!isNaN(tmpConstructionYear) && (tmpConstructionYear < 1987));
        // console.log('The Year Built can not be lower than 1987');
      }
    }

    return isInvalid;
  }


  // Rule 2:: (https://bostonsoftware.atlassian.net/browse/SPRC-365) (https://app.smartsheet.com/b/home?lx=wrx37F1hxf1XQrMBnVN7QA)
  // For Plans: Barnstable County Mutual  - Preferred [94], Barnstable County Mutual - Protector Plus [69]
  // Condition:
  // Construction Year <=30 Years from Quote effective date
  private fieldYearBuiltValidationRule2(dwelling: Dwelling, selectedPlansIds: string[], selectedFormTypes: string []): boolean {
    const requiredForPlans = ['94', '69'];
    let isInvalid = false;

    const allowValidationForSelectedPlans = Validate.isRequiredForSelectedPlans(requiredForPlans, selectedPlansIds);
    if (allowValidationForSelectedPlans) {
      const tmpDifference = differenceInYears(
        parseISO(this.quote.effectiveDate),
        new Date(dwelling.constructionYear, 0, 1)
      );
      if (tmpDifference > 30) {
        isInvalid = true;
        // console.log('The Year Built difference between Quote Effective Date can not be more than 30 years');
      }
    }

    return isInvalid;
  }


  // Rule 3:: (https://bostonsoftware.atlassian.net/browse/SPRC-365) (https://app.smartsheet.com/b/home?lx=wrx37F1hxf1XQrMBnVN7QA)
  // For Plans: Tower National Insurance [77]
  // Condition:
  // Construction Year > 47 Yrs not accepted
  private fieldYearBuiltValidationRule3(dwelling: Dwelling, selectedPlansIds: string[], selectedFormTypes: string []): boolean {
    const requiredForPlans = ['77'];
    let isInvalid = false;

    const allowValidationForSelectedPlans = Validate.isRequiredForSelectedPlans(requiredForPlans, selectedPlansIds);
    if (allowValidationForSelectedPlans) {
      const tmpDifference = differenceInYears(
  new Date(),
  new Date(dwelling.constructionYear, 0, 1));
      if (tmpDifference > 47) {
        isInvalid = true;
        // console.log('The Year Built can not be older than 47 years from now.')
      }
    }

    return isInvalid;
  }


  // Rule 4:: (https://bostonsoftware.atlassian.net/browse/SPRC-365) (https://app.smartsheet.com/b/home?lx=wrx37F1hxf1XQrMBnVN7QA)
  // For Plans: Tower National Preferred [78]
  // Condition:
  // Construction Year > 47 Yrs not accepted
  private fieldYearBuiltValidationRule4(dwelling: Dwelling, selectedPlansIds: string[], selectedFormTypes: string []): boolean {
    const requiredForPlans = ['78'];
    let isInvalid = false;

    const allowValidationForSelectedPlans = Validate.isRequiredForSelectedPlans(requiredForPlans, selectedPlansIds);
    if (allowValidationForSelectedPlans) {
      const tmpDifference = differenceInYears(
  new Date(),
  new Date(dwelling.constructionYear, 0, 1))
      if (tmpDifference > 70) {
        isInvalid = true;
        // console.log('The Year Built can not be older than 70 years from now.')
      }
    }

    return isInvalid;
  }


  // Rule 5:: (https://bostonsoftware.atlassian.net/browse/SPRC-365) (https://app.smartsheet.com/b/home?lx=wrx37F1hxf1XQrMBnVN7QA)
  // For Plans: Yankee Risk solutions - Preferred [99]
  // Condition:
  // Dwell age >30 years not accepted
  private fieldYearBuiltValidationRule5(dwelling: Dwelling, selectedPlansIds: string[], selectedFormTypes: string []): boolean {
    const requiredForPlans = ['99'];
    let isInvalid = false;

    const allowValidationForSelectedPlans = Validate.isRequiredForSelectedPlans(requiredForPlans, selectedPlansIds);
    if (allowValidationForSelectedPlans) {
      const tmpDifference = differenceInYears(
  new Date(),
  new Date(dwelling.constructionYear, 0, 1))
      if (tmpDifference > 30) {
        isInvalid = true;
        // console.log('The Year Built can not be older than 30 years from now.')
      }
    }

    return isInvalid;
  }

  public isRequiredDwellingAddress(): boolean {
    return (this.dwellingLocation && this.dwellingLocation.address1 && this.dwellingLocation.address1.length > 0) ? false : true;
  }

  public isRequiredState(): boolean {
    return (this.dwellingLocation && 'state' in this.dwellingLocation && this.dwellingLocation.state && this.dwellingLocation.state.length > 0) ? false : true;
  }

  public isRequiredZipCode(): boolean {
    return (this.dwellingLocation && 'zip' in this.dwellingLocation && this.dwellingLocation.zip && this.dwellingLocation.zip.length > 0) ? false : true;
  }
}
