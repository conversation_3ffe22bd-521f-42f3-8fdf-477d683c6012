<section class="section section--compact u-spacing--2-5">
    <div class="row">
      <h1 class="o-heading" style="color: #0b71ac; padding-left: 20px;">Owner</h1>
    </div>
    <div class="row u-spacing--1">
      <div class="col-xs-12">
        <div class="box box--silver">
          <div class="row" style="padding-bottom: 10px;">
            <div class="col-md-2">
              <label class="pl-3" for="">Owner Type:</label>
           </div>
            <div class="col-md-2">
              <sm-autocomplete #refOwnerTypeSelector required [options]="ownerTypes" [activeOption]="'Indivdual'"
                               placeholder="Type" [searchFromBegining]="false" [allowSearchById]="true"
                                [(ngModel)]="ownerType" name="OwnerType" (ngModelChange)="clear()">
           </sm-autocomplete></div>

          </div>
            <div  class="row" style="padding-bottom:10px">
              <div *ngIf="ownerType === 'Individual'; else business">


              <div class="col-md-2">
                <label for="">Owner:</label>
             </div>
             <div class="col-md-2 col-lg-3">
                 <input required [(ngModel)]="owner.firstName" name="owner.firstName" placeholder="First Name">
             </div>
             <div class="col-lg-3 col-md-2">
                 <input required [(ngModel)]="owner.lastName" name="owner.lastName" placeholder="Last Name">
             </div>
             <div class="col-lg-2 col-md-2">
                 <input required [(ngModel)]="owner.license" name="owner.license" placeholder="License #">
             </div>
             <div class="col-lg-2 col-md-3">
                   <app-datepicker-input [required]="true" ngDefaultControl [(ngModel)]="owner.dob" name="owner.dob" #refPickerDateOfBirth [placeholder]="'MM/dd/yyyy'" [returnDateFormat]="'yyyy-MM-dd'"
               (onDateChange)="setDate($event,owner)">
             </app-datepicker-input>
             </div>
             </div>
              <ng-template #business>
                <div class="col-md-2">
                  <label for="">Owner:</label>
               </div>
               <div class="col-md-2 col-lg-3">
                   <input required [(ngModel)]="owner.fid" name="owner.fid" placeholder="Business FID">
               </div>
              </ng-template>
            </div>
          <div class="row">
            <div class="col-md-2">
               <label>Email:</label>
            </div>
            <div class="col-md-6">
            <input *ngIf="ownerType === 'Individual'" [(ngModel)]="owner.email" name="owner.email" placeholder="Email" type="email" required>
            <input *ngIf="ownerType === 'Business'"  [(ngModel)]="owner.email" name="owner.email" placeholder="Email" type="email" required>

            </div>
        </div>
        </div>
      </div>
    </div>
  </section>
