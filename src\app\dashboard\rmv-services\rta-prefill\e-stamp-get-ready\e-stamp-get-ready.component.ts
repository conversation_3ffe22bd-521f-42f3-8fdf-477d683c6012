import { Component, OnInit, Input } from '@angular/core';
import { LookupsService } from '../../../app-services/lookups.service';
import { EStampInfo } from '../get-ready.model';
import { ControlContainer, NgForm } from '@angular/forms';
import { AgencyUserService } from '../../../../shared/services/agency-user.service';

import { format } from 'date-fns';

@Component({
    selector: 'app-e-stamp-get-ready',
    templateUrl: './e-stamp-get-ready.component.html',
    viewProviders: [{ provide: ControlContainer, useExisting: NgForm }],
    styleUrls: ['./e-stamp-get-ready.component.scss'],
    standalone: false
})
export class EStampGetReadyComponent implements OnInit {
  @Input() type;
dateTypes = [{id: 'EffectiveDate', text: 'Effective Date'}, {id: 'PolicyChangeDate', text: 'Policy Change Date'}];
carriers;
carrierData = [];
estampInfo: EStampInfo =
{effectiveDate: '', writingCompanyName: '', companyCode: 0 , agencyName: '', signedBy: '', dateType: 'EffectiveDate'};
  constructor(private lookupService: LookupsService) { }

  ngOnInit() {
   this.lookupService.getEstampsAsOptions().subscribe(x => this.carriers = x);
   this.lookupService.getEstamps().subscribe( x => this.carrierData = x);
  }

  reset() {
    this.estampInfo = {effectiveDate: '', writingCompanyName: '', companyCode: 0 , agencyName: '', signedBy: '', dateType: 'EffectiveDate'};
  }

  setDate($ev) {
    this.estampInfo.effectiveDate = $ev.formatedDate ? format(new Date($ev.formatedDate), 'yyyyMMdd') : '';
  }

  setCompanyName() {
    const carrier = this.carriers.find(x => x.id === this.estampInfo.companyCode);
    if (this.carrierData && this.carrierData.length > 0) {
      this.estampInfo.agencyName =  this.carrierData.find(x => x.companyCode === this.estampInfo.companyCode)?.agencyDisplayName;
      }

    this.estampInfo.writingCompanyName = carrier ? carrier.text : '';
  }

  getReadyEligible() {
    return this.type === 'PrefillNewTitleAndRegistration' ||
    this.type === 'PrefillRegistrationOnly'
    || this.type === 'PrefillRegistrationTransfer';
  }

}
