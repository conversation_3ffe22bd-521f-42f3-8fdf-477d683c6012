import { async, ComponentFixture, fakeAsync, inject, TestBed, tick } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { RouterTestingModule } from '@angular/router/testing';

import { Observable } from 'rxjs';

import { CLIENTS } from 'testing/data/quotes/clients';
import { AUTO_QUOTE_PLAN_LIST as RATING_PLANS_DATA_AUTOP } from 'testing/data/quotes/quote-plan-list/auto';
import { data as RMV_QUOTE_DATA } from 'testing/data/rmv-quote-data';
import { expect } from 'testing/helpers/all';
import {
    StubAsideQuickLinksInfoComponent
} from 'testing/stubs/components/aside-quick-links.component';
import { StubDatepickerInputComponent } from 'testing/stubs/components/datepicker-input.component';
import { StubDatepickerModalComponent } from 'testing/stubs/components/datepicker-modal.component';
import { StubPlansSelectorComponent } from 'testing/stubs/components/plans-selector.component';
import { StubSelectComponent } from 'testing/stubs/components/select.component';
import { StubdateFormatPipe } from 'testing/stubs/pipes/am-date-format.pipe';
import { StubAgencyUserServiceProvider } from 'testing/stubs/services/agency-user.service.provider';
import {
    StubOverlayLoaderServiceProvider
} from 'testing/stubs/services/overlay-loader.service.provider';
import { StubQuotesServiceProvider } from 'testing/stubs/services/quotes.service.provider';
import { StubSubsServiceProvider } from 'testing/stubs/services/subs.service.provider';

import { ClientDetails } from 'app/app-model/client';
import { LookupsService } from 'app/dashboard/app-services/lookups.service';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Helpers } from 'app/utils/helpers';

import { AsideNewRmvComponent } from './aside-new-rmv.component';

class MockLookupsService {
  public newRmvClient: ClientDetails;

  public get getNewRmvClient$(): Observable<any> {
    return Observable.of(this.newRmvClient);
  }
}

describe('Component: AsideNewRmv', () => {
  let component: AsideNewRmvComponent;
  let fixture: ComponentFixture<AsideNewRmvComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      declarations: [
        AsideNewRmvComponent,
        StubAsideQuickLinksInfoComponent,
        StubSelectComponent,
        StubDatepickerInputComponent,
        StubdateFormatPipe,
        StubDatepickerModalComponent,
        StubPlansSelectorComponent
      ],
      providers: [
        StubSubsServiceProvider,
        StubAgencyUserServiceProvider,
        StorageService,
        StubQuotesServiceProvider,
        StubOverlayLoaderServiceProvider,
        { provide: LookupsService, useClass: MockLookupsService },
        StorageGlobalService
      ]
    })
    .compileComponents();
  }));

  beforeEach(inject([StorageService, LookupsService, StorageGlobalService],
    (storageService: StorageService, lookupsService: MockLookupsService, storageGlobalService: StorageGlobalService) => {
      storageService.setStorageData('newRmvQuoteData', Helpers.deepClone(RMV_QUOTE_DATA));
      storageGlobalService.setSubs('plans', Helpers.deepClone(RATING_PLANS_DATA_AUTOP.items[0].items));
      lookupsService.newRmvClient = Helpers.deepClone(CLIENTS.items[0]);

      fixture = TestBed.createComponent(AsideNewRmvComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should destroy without errors', () => {
    expect(() => {
      fixture.destroy();
    }).not.toThrow();
  });

  it('should react to effective date change', fakeAsync(() => {
      const datepicker = <StubDatepickerModalComponent>fixture.debugElement.query(
        By.directive(StubDatepickerModalComponent)).componentInstance;
      datepicker.onSave.emit({
        date: new Date(2017, 3, 27),
        dateIso: '2017-04-27'
      });

      fixture.detectChanges();
      tick();

      fixture.detectChanges();
      tick();

      expect(component.newRmvQuote.effectiveDate).toEqual('2017-04-27');
  }));

  it('should react to selected plans change', fakeAsync(() => {
    const plansSelector = <StubPlansSelectorComponent>fixture.debugElement.query(
      By.directive(StubPlansSelectorComponent)).componentInstance;

    plansSelector.onSelect.emit({
      selectedOption: [{
        text: 'name',
        id: 'id',
        data: {
          name: 'name'
        }
      }]
    });

    fixture.detectChanges();
    tick();

    fixture.detectChanges();
    tick();

    expect(component.newRmvQuote.plans).toEqual(jasmine.arrayContaining([
      {
        text: 'name',
        id: 'id',
        data: {
          name: 'name'
        }
      }
    ]));
  }));
});
