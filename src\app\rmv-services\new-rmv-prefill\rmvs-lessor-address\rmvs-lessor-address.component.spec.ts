import { ComponentFixture, TestBed } from '@angular/core/testing';

import { RmvsLessorAddressComponent } from './rmvs-lessor-address.component';

describe('RmvsLessorAddressComponent', () => {
  let component: RmvsLessorAddressComponent;
  let fixture: ComponentFixture<RmvsLessorAddressComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [RmvsLessorAddressComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(RmvsLessorAddressComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
