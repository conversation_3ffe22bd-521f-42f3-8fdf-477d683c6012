import { LocalSymbolData, SelectedVehicleSymbols, SelectedVehicleSymbolsForVehicle } from '../../../../app-model/symbols';
import {
    generateCarriers,
    generateLabels,
    generateViewFieldIds,
    generateViewURIs,
    runConditions,
} from 'testing/helpers/warning-definitions';

import { Vehicle, VehicleLocationDataForVehicle } from 'app/app-model/vehicle';
import { AdditionalDataAutoVehicles, AdditionalDataI } from '../../../model/warnings';
import { WARNINGS_DEFINITIONS_AUTO_VEHICLE, WARNINGS_DEFINITIONS_AUTO_VEHICLE_LOCATIONS } from './definitions';

describe('Definitions: Vehicle', () => {
    describe('when vehicle validator is used', () => {
        let definitions: any[];
        let vehicle: Vehicle;
        let additionalData: AdditionalDataAutoVehicles;

        beforeEach(() => {
            definitions = WARNINGS_DEFINITIONS_AUTO_VEHICLE;
            vehicle = Object.assign(new Vehicle(), {
                meta: {
                    href: '',
                    rel: []
                },
                vehicleType: '',
                usage: '',
                year: '',
                make: '',
                model: '',
                bodyStyle: '',
                vin: '',
                licensePlate: '',
                annualMiles: '',
                priceValue: '',
                ignoreCarrierCityAndMiles: false,
                options: {
                    highTheft: false,
                    leased: false,
                    antilockBrakes: false,
                    hybrid: false,
                    alarm: false,
                    activeDisabling: false,
                    passiveDisabling: false,
                    vehicleRecovery: false,
                    otherMACategoryI: false,
                    otherMACategoryII: false,
                    otherMACategoryIII: false,
                    otherMACategoryIV: false,
                    otherMACategoryV: false,
                    driverAirbag: false,
                    passengerAirbag: false,
                    driverAutomaticBelt: false,
                    passengerAutomaticBelt: false
                },
                symbols: [
                    {
                        symbolType: 'ISO27',
                        symbolValue: '',
                        codeType: 'NA',
                        conversionType: 'BSC',
                        codeSource: 'VinMaster',
                        description: 'ISO 1-27  (BSC Conversion)'
                    }
                ],
                garagingAddress: '',
                owner: {
                    meta: {
                        href: ''
                    }
                },
                operator: {
                    meta: {
                        href: ''
                    }
                },
                standardCoverages: {
                    meta: {
                        href: ''
                    }
                },
                additionalCoverages: {
                    meta: {
                        href: ''
                    }
                },
                quoteSessionId: '',
                resourceId: '',
                resourceName: '',
                parentId: '',
                trimLevel: ''
            });
            additionalData = {
                quoteSelectedPlans: [{
                    ratingPlanId: '4',
                    name: 'Commerce',
                    lob: '',
                    meta: {
                        href: '',
                        rel: []
                    },
                    naic: '',
                    resourceName: '',
                    state: '',
                    bscCompanyPolicyID: '',
                    bscCompanyQuoteNumber: '',
                    allowedFormTypesCode: '',
                    sharedRatingPlanId: null,
                    sppAgreedValueInd: null,
                    submissionAvailableInd: null
                }],
                specLobTowns: [],
                quoteSelectedPlansIds: ['4'],
                vehiclesOptionsMake: [{
                    vehicleResourceId: '',
                    options: [{
                        id: 'option-id',
                        text: 'option-text'
                    }]
                }],
                vehiclesOptionsModel: [{
                    vehicleResourceId: '',
                    options: [{
                        id: 'option-id',
                        text: 'option-text'
                    }]
                }],
                vehicleGeneralDetailsHelper: [],
                selectedVehicleSymbolsForVehicle: [
                    new SelectedVehicleSymbolsForVehicle(
                        vehicle,
                        new SelectedVehicleSymbols(
                            undefined, undefined, undefined, undefined,
                            new LocalSymbolData(
                                true,
                                {
                                    symbolType: 'ISO27',
                                    symbolValue: '',
                                    codeType: 'NA',
                                    conversionType: 'BSC',
                                    codeSource: 'VinMaster',
                                    description: 'ISO 1-27  (BSC Conversion)'
                                }, {
                                    symbolType: 'ISO27',
                                    symbolValue: '',
                                    codeType: 'NA',
                                    conversionType: 'BSC',
                                    codeSource: 'VinMaster',
                                    description: 'ISO 1-27  (BSC Conversion)'
                                },
                                [ '4' ]
                            )
                        )
                    )
              ],
                drivers: []
            };
        });

        it('allows to check if required fields are provided', () => {
            const errors = runConditions(definitions, vehicle, additionalData);

            expect(errors).toEqual([
                'vehicleType', 'usage', 'year', 'make', 'model', 'trimLevel',
                'vin', 'bodyStyle', 'annualMiles', 'priceValue'
            ]);
        });

        it('generates viewFieldIds without errors', () => {
            expect(() => {
                generateViewFieldIds(definitions, vehicle);
            }).not.toThrow();
        });

        it('generates viewURIs without errors', () => {
            expect(() => {
                generateViewURIs(definitions, vehicle);
            }).not.toThrow();
        });

        it('generates labels without errors', () => {
            expect(() => {
                generateLabels(definitions, vehicle);
            }).not.toThrow();
        });

        it('generates carriers without errors', () => {
            expect(() => {
                generateCarriers(definitions, vehicle);
            }).not.toThrow();
        });
    });

    describe('when vehicle location validator is used', () => {
        let definitions: any[];
        let additionalData: AdditionalDataI;
        let vehicleLocation: VehicleLocationDataForVehicle;

        beforeEach(() => {
            definitions = WARNINGS_DEFINITIONS_AUTO_VEHICLE_LOCATIONS;
            additionalData = {
                quoteSelectedPlans: [],
                quoteSelectedPlansIds: ['20'],
                specLobTowns: [],
            };
            vehicleLocation = {
                vehicleResourceId: '',
                vehicleQuoteSessionId: '',
                vehicleMeta: {
                    href: ''
                },
                vehicleYear: '',
                vehicleMake: '',
                vehicleModel: '',
                location: {
                    meta: {
                        href: '',
                        rel: []
                    },
                    addressType: '',
                    address1: '',
                    address2: '',
                    city: '',
                    state: '',
                    zip: '',
                    residencyDate: '',
                    resourceId: '',
                    resourceName: '',
                    parentId: '',
                    quoteSessionId: ''
                }
            };
        });

        it('allows to check if required fields are provided', () => {
            const errors = runConditions(definitions, vehicleLocation, additionalData);

            expect(errors).toEqual([
                'address1', 'city', 'state', 'zip'
            ]);
        });

        it('generates viewFieldIds without errors', () => {
            expect(() => {
                generateViewFieldIds(definitions, vehicleLocation);
            }).not.toThrow();
        });

        it('generates viewURIs without errors', () => {
            expect(() => {
                generateViewURIs(definitions, vehicleLocation);
            }).not.toThrow();
        });

        it('generates labels without errors', () => {
            expect(() => {
                generateLabels(definitions, vehicleLocation);
            }).not.toThrow();
        });

        it('generates carriers without errors', () => {
            expect(() => {
                generateCarriers(definitions, vehicleLocation);
            }).not.toThrow();
        });
    });
});
