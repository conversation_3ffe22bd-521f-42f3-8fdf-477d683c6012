import { first } from 'rxjs/operators';
import { Component, OnInit, ViewChild, On<PERSON><PERSON>roy } from '@angular/core';
import {
  ClientDetails,
  ClientAddress,
  ClientContactMethod,
  ClientBusinessDetails
} from 'app/app-model/client';
import { StorageService } from 'app/shared/services/storage-new.service';
import { SubscriptionLike as ISubscription, Observable } from 'rxjs';
import { LeaveQuoteComponent } from 'app/shared/components/leave-quote/leave-quote.component';
import {
  CONTACT_METHODS,
  ADDRESSES,
  ClientsService,
  CLIENT_TYPES
} from 'app/dashboard/app-services/clients.service';
import { Validate } from 'app/hints-and-warnings/validators';
import { FilterOption } from 'app/app-model/filter-option';
import { STATES } from 'app/dashboard/app-services/state.service';
import { format } from 'date-fns';



@Component({
    selector: 'app-client-info',
    templateUrl: './client-info.component.html',
    styleUrls: ['./client-info.component.scss'],
    standalone: false
})
export class ClientInfoComponent implements OnInit, OnDestroy {
  private subscriptionSelectedClient: ISubscription;
  private subscriptionClientAddresses: ISubscription;
  private subscriptionClientContactMethods: ISubscription;
  private subscriptionWarnings: ISubscription;
  private subscriptionClientDetailsHasError: ISubscription;

  public selectedClientDetails: ClientDetails = new ClientDetails();

  private clientAddresses: ClientAddress[] = [];
  public clientAddressCurrent: ClientAddress = new ClientAddress();
  public clientAddressPrior: ClientAddress = new ClientAddress();

  private clientContactMethods: ClientContactMethod[] = [];
  public clientContactHome: ClientContactMethod = new ClientContactMethod();
  public clientContactWork: ClientContactMethod = new ClientContactMethod();
  public clientContactEmail: ClientContactMethod = new ClientContactMethod();
  public clientContactMobile: ClientContactMethod = new ClientContactMethod();
  public clientContactFax: ClientContactMethod = new ClientContactMethod();

  public optionsStates = STATES;

  private inputMaskPhone = [
    '+',
    '1',
    '-',
    'd',
    'd',
    'd',
    '-',
    'd',
    'd',
    'd',
    'd',
    'd',
    'd',
    'd'
  ];
  private inputMaskPlaceholder = '+1-___-_______';
  public inputMaskFor = {
    home: {
      mask: this.inputMaskPhone,
      placeholder: this.inputMaskPlaceholder
    },
    work: {
      mask: this.inputMaskPhone,
      placeholder: this.inputMaskPlaceholder
    },
    mobile: {
      mask: this.inputMaskPhone,
      placeholder: this.inputMaskPlaceholder
    },
    fax: {
      mask: this.inputMaskPhone,
      placeholder: this.inputMaskPlaceholder
    }
  };

  public optionsContactMethodsPreference: FilterOption[] = [];
  public optionsContactMethodsPreferenceSelected: FilterOption = new FilterOption();

  public optionsLegalEntity: Object[] = [
    { id: 'AS', text: 'Association' },
    { id: 'CC', text: 'City Commission' },
    { id: 'CD', text: 'City Department' },
    { id: 'CE', text: 'Co Employers' },
    { id: 'CH', text: 'Church' },
    { id: 'CP', text: 'Corporation' },
    { id: 'CM', text: 'Corporations' },
    { id: 'CY', text: 'County' },
    { id: 'ES', text: 'Estate' },
    { id: 'FR', text: 'Fraternity' },
    { id: 'GA', text: 'Government Agency' },
    { id: 'HO', text: 'Hospital' },
    { id: 'IN', text: 'Individual' },
    { id: 'IS', text: 'Individuals' },
    { id: 'JV', text: 'Joint Venture' },
    { id: 'LC', text: 'Limited Corp' },
    { id: 'LP', text: 'Limited Partnership' },
    { id: 'MU', text: 'Municipality' },
    { id: 'NP', text: 'NonProfit' },
    { id: 'OT', text: 'Other' },
    { id: 'PR', text: 'Proprietorship' },
    { id: 'PT', text: 'Partnership' },
    { id: 'SB', text: 'School Board' },
    { id: 'SC', text: 'School' },
    { id: 'SO', text: 'Sorority' },
    { id: 'SolePrp', text: 'Sole Proprietor' },
    { id: 'SCORP', text: 'S Corp' },
    { id: 'TN', text: 'Township' },
    { id: 'TR', text: 'Trust' },
    { id: 'UA', text: 'Unincorporated Association' }
  ];

  public optionsTaxType: Object[] = [
    { id: 'FEIN', text: 'FEIN' },
    { id: 'SSN', text: 'SSN' },
    { id: 'MA Driver\'s License', text: 'MA Driver\'s License' }
  ];

  public readonly COMMERCIAL_CLIENT = CLIENT_TYPES.commercial;
  public readonly PERSONAL_CLIENT = CLIENT_TYPES.personal;

  public clientType: string = this.PERSONAL_CLIENT;

  public showErrors = false;

  constructor(
    private storageService: StorageService,
    private clientsService: ClientsService
  ) {}

  @ViewChild('leaveQuote') leaveQuote: LeaveQuoteComponent;
  canDeactivate(): Observable<boolean> | boolean {
    this.leaveQuote.detectConfirmation();
    return this.leaveQuote.detectConfirmationObservable
      .asObservable()
      .pipe(first());
  }

  ngOnInit(): void {
    this.subscribeSelectedClient()
      .then(() => this.subscribeClientAddresses())
      .then(() => this.subscribeClientContactMethods())
      // TODO:: make sure that all contact methods and addresses are created
      .then(() => {
        this.setOptionsContactPreference(this.clientContactMethods);
        this.clientType =
          this.selectedClientDetails.type === 'commercial'
            ? CLIENT_TYPES.commercial
            : CLIENT_TYPES.personal;
      });
    this.subscribeClientDetailsHasError();
  }

  ngOnDestroy(): void {
    this.subscriptionSelectedClient &&
      this.subscriptionSelectedClient.unsubscribe();
    this.subscriptionClientAddresses &&
      this.subscriptionClientAddresses.unsubscribe();
    this.subscriptionClientContactMethods &&
      this.subscriptionClientContactMethods.unsubscribe();
    this.subscriptionWarnings && this.subscriptionWarnings.unsubscribe();
    this.subscriptionClientDetailsHasError &&
      this.subscriptionClientDetailsHasError.unsubscribe();
  }

  private subscribeClientDetailsHasError(): void {
    this.subscriptionClientDetailsHasError = this.clientsService.clientDetailsHasError.subscribe(
      (hasError: boolean) => {
        this.showErrors = hasError;
      }
    );
  }

  private subscribeSelectedClient(): Promise<ClientDetails> {
    return new Promise(resolve => {
      this.subscriptionSelectedClient = this.storageService
        .getStorageData('selectedClient')
        .subscribe((data: ClientDetails) => {
          // this.selectedClientDetails = data;
          const tmpBeforeAdjust: string = JSON.stringify(data);

          this.clientsService
            .adjustClientDetails(data)
            .then((client: ClientDetails) => {
              return (this.selectedClientDetails = client);
            })
            .then((client: ClientDetails) => {
              if (tmpBeforeAdjust !== JSON.stringify(client)) {
                // console.log('There has been change to the data, update required');
                this.clientsService
                  .updateClientDetails(this.selectedClientDetails)
                  .then((c: ClientDetails) => {
                    this.storageService.setStorageData(
                      'selectedClient',
                      this.selectedClientDetails
                    );
                    resolve(this.selectedClientDetails);
                  });
              } else {
                resolve(this.selectedClientDetails);
              }
            });
        });
    });
  }

  private subscribeClientAddresses(): Promise<ClientAddress[]> {
    return new Promise(resolve => {
      this.subscriptionClientAddresses = this.storageService
        .getStorageData('clientAddresses')
        .subscribe((data: ClientAddress[]) => {
          this.clientAddresses = data;
          this.handleAddressesAssigningToProperObject(this.clientAddresses);
          resolve(this.clientAddresses);
        });
    });
  }

  private subscribeClientContactMethods(): Promise<ClientContactMethod[]> {
    return new Promise(resolve => {
      this.subscriptionClientContactMethods = this.storageService
        .getStorageData('clientContactMethods')
        .subscribe((data: ClientContactMethod[]) => {
          this.clientContactMethods = data;
          this.handleContactsAssigningToProperObject(this.clientContactMethods);
          resolve(this.clientContactMethods);
        });
    });
  }

  // Addresses Helpers
  // ---------------------------------------------------------------------------
  // private setAddressesToProperObject(addresses: ClientAddress[]): Promise<any> {
  //   this.handleAddressesAssigningToProperObject(addresses);
  //   return Promise.resolve();
  // }

  private handleAddressesAssigningToProperObject(
    addresses: ClientAddress[]
  ): void {
    addresses.forEach((address: ClientAddress) => {
      switch (address.addressType) {
        case ADDRESSES.current:
          this.clientAddressCurrent = address;
          break;
        case ADDRESSES.prior:
          this.clientAddressPrior = address;
          break;
      }
    });
  }

  public generateAddressFieldId(
    addr: ClientAddress,
    customName: string
  ): string {
    return addr && addr.addressType
      ? customName + '_' + addr.addressType
      : customName;
  }

  // Contacts Methods Helpers
  // ---------------------------------------------------------------------------
  // private setContactsToProperObject(contacts: ClientContactMethod[]): Promise<any> {
  //   this.handleContactsAssigningToProperObject(contacts);
  //   return Promise.resolve();
  // }

  private handleContactsAssigningToProperObject(
    contacts: ClientContactMethod[]
  ): void {
    contacts.forEach((contact: ClientContactMethod) => {
      switch (contact.type) {
        case CONTACT_METHODS.home:
          this.clientContactHome = contact;
          break;
        case CONTACT_METHODS.work:
          this.clientContactWork = contact;
          break;
        case CONTACT_METHODS.mobile:
          this.clientContactMobile = contact;
          break;
        case CONTACT_METHODS.fax:
          this.clientContactFax = contact;
          break;
        case CONTACT_METHODS.email:
          this.clientContactEmail = contact;
          break;
      }
    });
  }

  private setOptionsContactPreference(contacts: ClientContactMethod[]): void {
    this.optionsContactMethodsPreference = [];

    contacts.forEach(contact => {
      const option = new FilterOption();
      option.id = contact.type;
      option.text = this.helperGenereteContactMethodLabel(contact);
      this.optionsContactMethodsPreference.push(option);

      if (contact.preferredMethod === 'True') {
        this.setOptionContactMethodsPreferenceSelected(contact.type);
      }
    });
  }

  private helperGenereteContactMethodLabel(
    contact: ClientContactMethod
  ): string {
    let label = '';

    switch (contact.type) {
      case CONTACT_METHODS.home:
        label = 'Home';
        break;
      case CONTACT_METHODS.work:
        label = 'Work';
        break;
      case CONTACT_METHODS.mobile:
        label = 'Mobile';
        break;
      case CONTACT_METHODS.fax:
        label = 'Fax';
        break;
      case CONTACT_METHODS.email:
        label = 'Email';
        break;
    }

    return label;
  }

  private setOptionContactMethodsPreferenceSelected(
    contactTypOrOptionId: string
  ): void {
    const optionToSelect = this.optionsContactMethodsPreference.find(
      option => option.id === contactTypOrOptionId
    );
    if (optionToSelect) {
      this.optionsContactMethodsPreferenceSelected = optionToSelect;
    } else {
      this.optionsContactMethodsPreferenceSelected = new FilterOption();
    }
  }

  public onContactPreferenceChange($ev) {
    this.clientContactMethods.forEach(contact => {
      if (contact.type === $ev.id) {
        contact.preferredMethod = 'True';
        this.setOptionContactMethodsPreferenceSelected($ev.id);
      } else {
        contact.preferredMethod = 'False';
      }
    });

    this.actionUpdateClientContactMethod();
  }

  // Actions
  // ---------------------------------------------------------------------------

  public onSelect($ev, obj, property) {
    obj[property] = $ev.id;

    this.actionUpdateSelectedClientDetails();
  }

  onDateSelect($ev, obj, property) {
    obj[property] = $ev.formatedDate;
    this.actionUpdateSelectedClientDetails();
  }

  public actionUpdateSelectedClientDetails(): void {
    const businessName = this.selectedClientDetails.business.name;
    const firstName = this.selectedClientDetails.firstName;
    const lastName = this.selectedClientDetails.lastName;

    if (firstName !== null  && firstName?.trim() !== firstName) {
      this.selectedClientDetails.firstName = firstName.trim();
    }
    if (lastName !== null && lastName?.trim() !== lastName) {
      this.selectedClientDetails.lastName = lastName.trim();
    }
    if (businessName !== null && businessName?.trim() !== businessName) {
      this.selectedClientDetails.business.name = businessName.trim();
    }

    this.storageService.setStorageData(
      'selectedClient',
      this.selectedClientDetails
    );
  }

  public actionUpdateClientAddress(address: ClientAddress): void {
    this.storageService.updateClientAddressesSingleItem(address);
  }

  public actionOnSelectAddress(
    data: FilterOption,
    address: ClientAddress,
    property: string
  ): void {
    address[property] = data.id;
    this.storageService.updateClientAddressesSingleItem(address);
  }

  public actionUpdateDateClientAddress(
    data,
    address: ClientAddress,
    property: string
  ): void {
    const currentDate = address[property]
      ? format(new Date(address[property]), 'yyyy-MM-dd')
      : '';
    const updatedDate = data.date ? format(new Date(data.date),'yyyy-MM-dd') : '';

    if (currentDate !== updatedDate) {
      address[property] = updatedDate;
      this.storageService.updateClientAddressesSingleItem(address);
    }
  }

  public actionUpdateDateSelectedClientDetails(data, property: string) {
    const currentDate = this.selectedClientDetails[property]
  ? format(new Date(this.selectedClientDetails[property]), 'yyyy-MM-dd')
  : '';
const updatedDate = data.date ? format(new Date(data.date), 'yyyy-MM-dd') : '';

    if (currentDate !== updatedDate) {
      this.selectedClientDetails[property] = updatedDate;
      this.actionUpdateSelectedClientDetails();
    }
  }

  public actionUpdateClientContactMethod(): void {
    this.storageService.setStorageData(
      'clientContactMethods',
      this.clientContactMethods
    );
  }

  // Validation and Requirements
  // ---------------------------------------------------------------------------
  public fieldPrimaryFirstNameIsRequired(): boolean {
    return ClientsService.fieldPrimaryFirstNameIsRequired(
      this.selectedClientDetails
    );
  }

  public fieldPrimaryLastNameIsRequired(): boolean {
    return ClientsService.fieldPrimaryLastNameIsRequired(
      this.selectedClientDetails
    );
  }

  public fieldPrimaryDOBIsRequired(): boolean {
    return ClientsService.fieldPrimaryDOBIsRequired(this.selectedClientDetails);
  }

  public fieldSecondaryDOBIsRequired(): boolean {
    return ClientsService.fieldSecondaryDOBIsRequired(
      this.selectedClientDetails
    );
  }

  public fieldBusinessNameIsRequired(): boolean {
    return ClientsService.fieldBusinessNameIsRequired(
      this.selectedClientDetails
    );
  }

  public fieldLegalEntityIsRequired(): boolean {
    return ClientsService.fieldLegalEntityIsRequired(
      this.selectedClientDetails
    );
  }

  public isValidOrEmptyPhoneNumber(number: string): boolean {
    if (!number) {
      return true;
    }

    return Validate.isValidPhoneNumber(number);
  }

  public isValidOrEmptyEmail(email: string): boolean {
    if (!email) {
      return true;
    }

    return Validate.isValidEmail(email);
  }

  public setClientType(clientType): void {
    this.clientType = clientType;
    this.selectedClientDetails.type = this.clientType;
    this.storageService.setStorageData(
      'selectedClient',
      this.selectedClientDetails
    );
  }

  get showPersonalForm(): boolean {
    return this.clientType === this.PERSONAL_CLIENT;
  }

  get showCommercialForm(): boolean {
    return this.clientType === this.COMMERCIAL_CLIENT;
  }
}
