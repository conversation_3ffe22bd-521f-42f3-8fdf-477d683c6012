export interface Meta {
	href: string;
	rel: string[];
}

export interface First {
	href: string;
	rel: string[];
}

export interface Next {
	href: string;
	rel: string[];
}

export interface Last {
	href: string;
	rel: string[];
}

export interface Meta {
	href: string;
}

export interface Item {
	meta: Meta;
	rmvServicesId: number;
	userId: number;
	userFirstName: string;
	userLastName: string;
	companyOrgId: number;
	companyFacilityId: number;
	transactionId: string;
	transactionType: string;
	vin: string;
	plate: string;
	savedDate: string;
	lastModifiedDate: string;
}

export interface RmvServicesList {
	meta: Meta;
	offset: number;
	limit: number;
	size: number;
	first: First;
	previous?: any;
	next: Next;
	last: Last;
	items: Item[];
}
