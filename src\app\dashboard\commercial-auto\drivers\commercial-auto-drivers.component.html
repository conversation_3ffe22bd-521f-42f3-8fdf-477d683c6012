<section class="section section--compact u-spacing--0-5">


  <div class="row u-spacing--1-5">
    <div class="col-xs-12">
      <div class="box box--silver">
        <form name="driverDetailForm" novalidate>

          <table class="form-table">
            <tbody>
              <tr class="form-table__row" *ngFor="let driver of drivers; let index = index; trackBy: trackByFn">
                <td class="form-table__cell u-width-180px" [ngClass]="{'is-required-field': ValidateField(driver.firstName)}">
                  <input
                  fieldAutofocus
                  [id]="'driverFirstName_'+index"
                  [name]="'driverFirstName_'+index"
                  placeholder="First Name"
                  [(ngModel)]="driver.firstName"
                  (change)="onInputDataChange(driver)"
                  type="text">
                </td>
                <td class="form-table__cell u-width-180px" [ngClass]="{'is-required-field': <PERSON>ida<PERSON><PERSON>ield(driver.lastName)}">
                    <input
                    [id]="'driverLastName_'+index"
                    [name]="'driverLastName_'+index"
                    placeholder="Last Name"
                    [(ngModel)]="driver.lastName"
                    (change)="onInputDataChange(driver)"
                    type="text">
                  </td>
                  <td class="form-table__cell u-width-120px" [ngClass]="{'is-required-field': ValidateField(driver.dateOfBirth)}">
                      <app-datepicker-input
                      #refPickerDateOfBirth
                      [id]="'driverDob_'+index"
                      [name]="'driverDob_'+index"
                      [required]="false"
                      [placeholder]="'D.O.B.'"
                      [selectDate]="driver.dateOfBirth"
                      (onDateChange)="onInputDateChange(driver,$event)">
                    </app-datepicker-input>
                  </td>
                  <td class="form-table__cell u-width-180px" [ngClass]="{'is-required-field': ValidateField(driver.licenseNumber)}">
                    <input
                    #refDriverLicenseNumber
                    [id]="'driverLicenseNumber_'+index"
                    [name]="'driverLicenseNumber_'+index"
                    type="text"
                    placeholder="License #"
                    [(ngModel)]="driver.licenseNumber"
                    (change)="onInputDataChange(driver)"
                    changeDetectionDelay>
                  </td>
                  <td class="form-table__cell" [ngClass]="{'is-required-field': ValidateField(driver.licenseState)}">
                    <div class="u-width-180px">
                      <sm-autocomplete
                      [options]="licenseStates"
                      [activeOption]="driver.licenseState"
                      [id]="'driverLicenseState_'+index"
                      [searchFromBegining]="true"
                      [allowEmptyValue]="false"
                      placeholder="State"
                      (onSelect)="selected($event, driver, 'licenseState')">
                      </sm-autocomplete>
                    </div>
                  </td>
                  <td class="form-table__cell">
                      <div class="u-show-iblock">
                        <label class="o-checkable">
                          <input type="checkbox" [name]="'driverDoc_'+index" [id]="'driverDoc_'+index" [(ngModel)]="driver.driveOtherCar" (change)="onInputDataChange(driver)">
                          <i class="o-btn o-btn--checkbox"></i>
                          <span>DOC</span>
                        </label>
                      </div>
                    </td>
                  <td class="form-table__cell">
                    <button *ngIf="index>0 && drivers?.length > 1" (click)="selectDriverForDelete(index)" type="button" class="o-btn o-btn--action o-btn--i_cancel u-t-size--1-7rem modal-delete-driver" tabindex="-1"></button>
                  </td>
              </tr>
              <tr class="form-table__row">
                <td class="form-table__cell" colspan="6">
                    <button (click)="addDriver()" class="o-btn o-btn--action o-btn--i_round-plus" id="add-driver-modal">
                      Add Additional Driver
                    </button>
                </td>
                <td class="form-table__cell u-width-80px"></td>
              </tr>
            </tbody>
          </table>

        </form>
      </div>
    </div>
  </div>
  <div class="u-align-right" style="padding-top:5px;">
    <button class="o-btn" id="rmv-lookup-driver-modal">Rmv Lookup</button>
  </div>
  <app-modalbox #modalRmvLookupDriver [launcher]="'#rmv-lookup-driver-modal'" [css]="'u-width-620px'">
    <ng-template [ngIf]="modalRmvLookupDriver.isOpen">
      <h2 class="o-heading o-heading--red">RMV Driver Lookup</h2>

      <div class="box box--silver u-spacing--1-5">
        <app-rmv-driver-lookup [isCommercial]="true" #refDriverLookup [refModalbox]="modalRmvLookupDriver" (errorStatus)="checkForErrors($event)"></app-rmv-driver-lookup>
      </div>

      <div class="row u-spacing--2">
        <div class="col-xs-12 u-align-right">
          <button class="o-btn" [disabled]="disableButton" (click)="refDriverLookup.rmvLookup($event, modalRmvLookupDriver)">RMV
            Lookup</button>
          <button class="o-btn o-btn--idle u-spacing--left-2"
            (click)="modalRmvLookupDriver.closeModalbox()">Cancel</button>
        </div>
      </div>
    </ng-template>
  </app-modalbox>
</section>

<app-confirmbox
  [launcher]="'.modal-delete-driver'"
  [question]="'Are you sure you want to delete this driver?'"
  [askAbout]="drivers[selectedDriverIndex]?.licenseNumber"
  [confirmBtnText]="'Yes, delete driver'"
  (onAccept)="confirmDelete()"
  (onCancel)="confirmCanceled($event)">
</app-confirmbox>
