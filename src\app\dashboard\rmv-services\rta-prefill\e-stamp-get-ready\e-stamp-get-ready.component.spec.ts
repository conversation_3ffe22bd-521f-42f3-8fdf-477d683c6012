import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { EStampGetReadyComponent } from './e-stamp-get-ready.component';

describe('EStampGetReadyComponent', () => {
  let component: EStampGetReadyComponent;
  let fixture: ComponentFixture<EStampGetReadyComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ EStampGetReadyComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(EStampGetReadyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
