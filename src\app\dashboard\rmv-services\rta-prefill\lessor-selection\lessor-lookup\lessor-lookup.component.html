@if (false) { // if anyone ever decided to use this component with a name search again
<section class="section">
  <div class="row">
    <div class="col-xs-8">
      <div class="u-flex u-flex--to-middle">
        <h1 class="o-heading u-spacing--right-2-5">Select Lessor</h1>
        <div class="search">

          <input #searchInput [id]="'filter_criteria'" [(ngModel)]="searchQuery" (keydown.enter)="filter()"
            class="search__input search__input--md" type="text" placeholder="Search By Name or Code"
            changeDetectionDelay />
            <button *ngIf="searchQuery && searchQuery.length > 0"
              (click)="reset($event)"
              name="cancel"
              class="reset__button"
            ></button>
          <button (click)="filter()" type="button" name="searchbtn" class="search__button">
          </button>
        </div>
      </div>

    </div>
    <div class="col-xs-4">
      <div class="u-spacing--left-2 u-align-right">
        <app-pagination [totalRecords]="paginationResultsCount" [recordsLimit]="10"
          (onPageChange)="paginationPageChange($event)">
        </app-pagination>
      </div>
    </div>

  </div>
  <span style="color: red" *ngIf="searchQuery && searchQuery.length < 3">Minimum search length is 3</span>
</section>

} @else {
<p style="padding: 10px;">MULTIPLE RECORDS FOUND. PLEASE SELECT ONE.</p>
}


<div class="row">
  <app-loader [loading]="loadingLessors"></app-loader>
  <div class="col-xs-12">
    <table class="table table--compact table--hoverable form-table form-table--full">
      <thead class="table__thead">
        <tr class="">
          <th class="table__th u-width-40px">
            <!-- Checkbox -->
          </th>
          <th class="table__th">Lessor Name</th>
          <th class="table__th">Address</th>
          <th class="table__th">FID</th>
          <th class="table__th">Type</th>
        </tr>
      </thead>

      <tbody class="table__tbody" *ngIf="results">
        <tr class="table__tr" *ngFor="let row of results" (click)="setSelectedLessor(row)" (dblclick)="setSelectedLessor(row);saveSelectedLessor()"
        [title]="row.atlasEntityKey + ' - ' + row.atlasEntityLocationKey">
          <td class="table__td">
            <div class="checklist__btns">
              <label class="o-checkable">
                <input type="radio" name="lienholder-group" [checked]="isLessorSelected(row)" />
                <i class="o-btn o-btn--radio"></i>
              </label>
            </div>
          </td>
          <td class="table__td u-color-pelorous">
            {{ row.name }}

          </td>
          <td class="table__td">{{row.addressLine1}} {{row.city}} {{row.state}} {{row.zipCode}}
          </td>

          <td class="table__td">
            {{row.fid }}
          </td>
             <td class="table__td">
            {{row.locationTypeDescription }}
          </td>
        </tr>
      </tbody>
      <tbody class="table__tbody" *ngIf="!results?.length">
        <tr class="table__tr">
          <td colspan="6">
            <p class="u-padd--bottom-1 u-padd--1">There are no results that match your search.</p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
<div class="row u-spacing--2">
  <div class="col-xs-12 u-align-right">
    <button (click)="saveSelectedLessor()" class="o-btn u-spacing--left-2" [disabled]="!selectedLessor">Select Lessor</button>
    <button (click)="closeModal()" class="o-btn o-btn--idle u-spacing--left-2">Cancel</button>
  </div>
</div>
