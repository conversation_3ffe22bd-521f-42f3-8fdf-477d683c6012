export interface Transaction {
  transactionAccepted: string;
  transactionTimestamp: string;
  atlasTransactionKey: string;
}

export interface Header {
  transaction: Transaction;
}

export interface ResidentialAddress {
  addressStreet1: string;
  addressStreet2: string;
  addressUnitType: string;
  addressUnit: string;
  addressCity: string;
  addressState: string;
  addressZIP: string;
  addressCountry: string;
}

export interface MailAddress {
  addressStreet1: string;
  addressStreet2: string;
  addressUnitType: string;
  addressUnit: string;
  addressCity: string;
  addressState: string;
  addressZIP: string;
  addressCountry: string;
}

export interface Person {
  licenseNumber: string;
  licenseState: string;
  lastName: string;
  firstName: string;
  middleName: string;
  nameSuffix: string;
  dateOfBirth: string;
  residentialAddress: ResidentialAddress;
  mailAddress: MailAddress;
}

export interface Error {
  errorCode: string;
  errorCodeDescription: string;
  errorReference: string;
}

export interface Message {
  message: string;
}

export interface OwnerLookupResponse {
  isOwnerValidated: boolean;
  isOwnerFound: boolean;
  isOwnerDataExactlyMatched: boolean;
  isNewEntry: boolean;
  header: Header;
  person: Person;
  errors: Error[];
  messages: Message[];
  editResidentialAddress?: boolean;
  editMailingAddress?: boolean;
  editGaragingAddress?: boolean;
  lookupDone?: boolean;
  leasedVehicleIndicator?: boolean;
}

export interface PersonRequest {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  license: string;
  licenseState: string;
  fid?: string;
  entityType?: string;
  atlasEntityKey?: string;
  atlasEntityLocationKey?: string;
  ownershipRole?: string;
}

export interface OwnerLookupRequest {
  person: PersonRequest;
}
