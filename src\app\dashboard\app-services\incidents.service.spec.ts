import { inject, TestBed } from '@angular/core/testing';
import { MockBackend } from 'testing/setups/mock-backend';

import { data as DRIVER_INCIDENTS } from 'testing/data/drivers/incidents';
import {
    DataCustomMatchers, expectLastConnectionPayload, expectLastConnectionUrl
} from 'testing/helpers/all';
import { setupMockBackend } from 'testing/setups/mock-backend';
import { StorageService } from 'app/shared/services/storage-new.service';
import { IncidentsService } from './incidents.service';

describe('Service: Incidents', () => {
  let service: IncidentsService;
  let mockBackend: MockBackend;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        IncidentsService,
        { provide: StorageService, useValue: {} }  // unused
      ]
    });

    jasmine.addMatchers(DataCustomMatchers);
  });

  beforeEach(inject([IncidentsService], (_service: IncidentsService) => {
    service = _service;
  }));

  it('can instantiate service when inject service', () => {
    expect(service).toBeTruthy();
  });

  it('can retrieve incidents list by URI', () => {
    mockBackend = setupMockBackend(DRIVER_INCIDENTS);

    service.getIncidents('/drivers/driver-id/incidents').subscribe();

    expectLastConnectionUrl(mockBackend).toEndWith('/drivers/driver-id/incidents');
  });

  it('can create incident using driver ID', () => {
    mockBackend = setupMockBackend(DRIVER_INCIDENTS.items[0]);

    service.addIncident('driver-id', DRIVER_INCIDENTS.items[0]).subscribe();

    expectLastConnectionUrl(mockBackend).toEndWith('/drivers/driver-id/incidents');
    expectLastConnectionPayload(mockBackend).toEqual(DRIVER_INCIDENTS.items[0]);
  });

  it('can update incident using driver ID', () => {
    mockBackend = setupMockBackend(DRIVER_INCIDENTS.items[0]);

    // it gets resourceId from data
    service.editIncident('driver-id', DRIVER_INCIDENTS.items[0]).subscribe();

    expectLastConnectionUrl(mockBackend).toEndWith('/drivers/driver-id/incidents/c60b349e-6bb4-40d0-9df3-8c9e411e7755');
    expectLastConnectionPayload(mockBackend).toEqual(DRIVER_INCIDENTS.items[0]);
  });

  it('can delete incident by URI', () => {
    mockBackend = setupMockBackend({});

    service.deleteIncident('/drivers/driver-id/incidents/incident-id').subscribe();

    expectLastConnectionUrl(mockBackend).toEndWith('/drivers/driver-id/incidents/incident-id');
  });
});
