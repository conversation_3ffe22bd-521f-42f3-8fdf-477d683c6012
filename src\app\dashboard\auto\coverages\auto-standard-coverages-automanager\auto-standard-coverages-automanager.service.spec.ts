import { TestBed, inject } from '@angular/core/testing';

import { OptionsService } from 'app/dashboard/app-services/options.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { AgencyUserService } from 'app/shared/services/agency-user.service';
import { VehiclesService } from 'app/dashboard/app-services/vehicles.service';
import { CoveragesService } from 'app/dashboard/app-services/coverages.service';

import { HttpClient } from '@angular/common/http';
import { ApiService } from 'app/shared/services/api.service';
import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';

import { AutoStandardCoveragesAutomanagerService } from './auto-standard-coverages-automanager.service';
import { LocationsService } from 'app/dashboard/app-services/locations.service';

describe('Service: AutoStandardCoveragesAutomanagerService', () => {
  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        AutoStandardCoveragesAutomanagerService,
        OptionsService,
        SpecsService,
        StorageService,
        AgencyUserService,
        VehiclesService,
        CoveragesService,
        HttpClient,
        ApiService,
        QuotesService,
        ApiCommonService,
        StorageGlobalService,
        LocationsService
      ]
    });
  });

  it('Service: AutoStandardCoveragesAutomanagerService', inject([AutoStandardCoveragesAutomanagerService], (service: AutoStandardCoveragesAutomanagerService) => {
    expect(service).toBeTruthy();
  }));
});
