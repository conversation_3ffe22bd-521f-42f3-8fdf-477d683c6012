import {take, first, map} from 'rxjs/operators';
import { of } from 'rxjs';
import {
  AdditionalDataI,
  AdditionalDataAutoVehicles,
  AdditionalDataHomeDwelling,
  AdditionalDataHomeClientInfo,
  AdditionalDataAutoClientInfo,
  AdditionalDataUmbrellaClientInfo,
  AdditionalDataAutoDrivers,
  AdditionalDataQuotePlans,
  AdditionalDataHomeBasicsCoverages,
  AdditionalDataQuoteInfo,
  AdditionalDataScheduledProperty
} from 'app/hints-and-warnings/model/warnings';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { CoverageForVehicle,
         CoverageApiResponseData,
         CoverageItemParsed,
         Coverage,
         CoverageValue,
         CoverageItemChild,
         CoverageItemParsedForAutoDriverOptionsHintsAndWarnings } from 'app/app-model/coverage';
import { Quote, QuotePlan, QuotePlanListAPIResponse } from 'app/app-model/quote';
import { Vehicle,
         VehicleLocationDataForVehicle,
         VehicleOptionsForVehicle,
         VehicleCoverages,
         VehicleGeneralDetailsHelper } from 'app/app-model/vehicle';
import {
  WARNINGS_DEFINITIONS_INFO_QUOTE,
  WARNINGS_DEFINITIONS_INFO_CLIENT,
  WARNINGS_DEFINITIONS_INFO_CLIENT_FOR_AUTO,
  WARNINGS_DEFINITIONS_INFO_CLIENT_ADDRESSES_FOR_AUTO,
  WARNINGS_DEFINITIONS_INFO_CLIENT_CONTACT_METHODS_FOR_AUTO,
  WARNINGS_DEFINITIONS_AUTO_DRIVER,
  WARNINGS_DEFINITIONS_AUTO_DRIVERS_OPTIONS_AND_COVERAGES,
  WARNINGS_DEFINITIONS_AUTO_OPTIONS_CARRIER_OPTIONS,
  WARNINGS_DEFINITIONS_AUTO_OPTIONS_POLICY_HISTORY,
  WARNINGS_DEFINITIONS_AUTO_STANDARD_COVERAGES,
  WARNINGS_DEFINITIONS_AUTO_ADDITIONAL_COVERAGES,
  WARNINGS_DEFINITIONS_AUTO_VEHICLE,
  WARNINGS_DEFINITIONS_AUTO_VEHICLE_LOCATIONS,
  WARNINGS_DEFINITIONS_INFO_CLIENT_FOR_HOME,
  WARNINGS_DEFINITIONS_INFO_CLIENT_ADDRESSES_FOR_HOME,
  WARNINGS_DEFINITIONS_INFO_CLIENT_CONTACT_METHODS_FOR_HOME,
  WARNINGS_DEFINITIONS_HOME_DWELLINGS,
  WARNINGS_DEFINITIONS_HOME_DWELLING_LOCATION,
  WARNINGS_DEFINITIONS_HOME_DWELLING_FIRE_LOCATION,
  WARNINGS_DEFINITIONS_HOME_DWELLINGS_SUBSYSTEMS,
  WARNINGS_DEFINITIONS_HOME_BASICS,
  WARNINGS_DEFINITIONS_HOME_OPTIONS_CARRIER_OPTIONS,
  WARNINGS_DEFINITIONS_HOME_OPTIONS_CARRIER_OPTIONS_LOSS,

  WARNINGS_DEFINITIONS_HOME_OPTIONS_GENERAL_OPTIONS,
  WARNINGS_DEFINITIONS_HOME_LOSS_HISTORY,
  WARNINGS_DEFINITIONS_HOME_LOSS_HISTORY_ITEMS,
  WARNINGS_DEFINITIONS_HOME_AGREED_VALUE_SUPPLEMENT,
  WARNINGS_DEFINITIONS_HOME_DWELLINGS_PROTECTION_CLASS,
  WARNINGS_DEFINITIONS_DWELLING_DWELLING,
  WARNINGS_DEFINITIONS_DWELLING_DWELLING_LOCATION,
  WARNINGS_DEFINITIONS_DWELLING_DWELLING_FIRE_LOCATION,
  WARNINGS_DEFINITIONS_DWELLING_BASICS,
  WARNINGS_DEFINITIONS_DWELLING_OPTIONS_GENERAL_OPTIONS,
  WARNINGS_DEFINITIONS_DWELLING_OPTIONS_CARRIER_OPTIONS,
  WARNINGS_DEFINITIONS_INFO_CLIENT_FOR_DWELLING,
  WARNINGS_DEFINITIONS_INFO_CLIENT_FOR_UMBRELLA,
  WARNINGS_DEFINITIONS_INFO_CLIENT_ADDRESSES_FOR_UMBRELLA,
  WARNINGS_DEFINITIONS_UMBRELLA_OPTIONS_CARRIER_OPTIONS,
  WARNINGS_DEFINITIONS_UMBRELLA_OPTIONS_GENERAL_OPTIONS,
  WARNINGS_DEFINITIONS_UMBRELLA_PRIMARY_POLICY_INFORMATION,
  WARNINGS_DEFINITIONS_AUTO_DRIVERS_INCIDENTS,
  WARNINGS_DEFINITIONS_AUTO_SELECTED_PLANS,
  WARNINGS_DEFINITIONS_INFO_CLIENT_CONTACT_METHODS_FOR_DWELLING,
  WARNINGS_DEFINITIONS_INFO_CLIENT_CONTACT_METHODS_FOR_UMBRELLA,
  WARNINGS_DEFINITIONS_COM_AUTO_DRIVER,
  WARNINGS_DEFINITIONS_INFO_CLIENT_AUTOB,
  WARNINGS_DEFINITIONS_CLIENT_ADDRESS_AUTOB,
  WARNINGS_DEFINITIONS_CLIENT_PHONE_AUTOB,
  WARNINGS_DEFINITIONS_COM_AUTO_COVERAGES,
  WARNINGS_DEFINITIONS_COM_AUTO_SYMBOL_COVERAGES,
  WARNINGS_DEFINITIONS_COM_AUTO_VEHICLES
  // WARNINGS_DEFINITIONS_INFO_CLIENT_ADDRESSES_FOR_DWELLING
} from 'app/hints-and-warnings/data/';

import { BehaviorSubject ,  SubscriptionLike as ISubscription ,  Observable } from 'rxjs';
import { ClientsService } from 'app/dashboard/app-services/clients.service';
// data fetching services
import { CoveragesService } from 'app/dashboard/app-services/coverages.service';
import { CurrentPageService } from 'app/shared/services/current-page.service';
import { FilterOption } from 'app/app-model/filter-option';
import { HintsAndWarningsService,
        LOCATION_FRAGMENT_SEPARATOR,
        IHintsAndWarningsLocationFragmentElements } from 'app/hints-and-warnings/services/hints-and-warnings.service';
import { LocationData } from 'app/app-model/location';
import { LocationsService } from 'app/dashboard/app-services/locations.service';
import { LookupsService } from 'app/dashboard/app-services/lookups.service';
import { OptionsService } from 'app/dashboard/app-services/options.service';
import { PremiumsService } from 'app/dashboard/auto/premiums/premiums.service';
import { DwellingService } from 'app/dashboard/app-services/dwelling.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
//  models
import { QuotePolicyHistory } from 'app/app-model/quote';
import { ClientDetails, ClientAddress, ClientContactMethod, ClientBusinessDetails } from 'app/app-model/client';
import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { SelectedVehicleSymbolsForVehicle } from 'app/app-model/symbols';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { DwellingOptionsValidationService } from 'app/shared/services/dwelling-options-validation.service';
import { DwellingSubsystems, DwellingsSubsystemsAPIResponse } from 'app/app-model/dwelling';
import { Router, NavigationEnd } from '@angular/router';
import { Driver, DriverOptionsAndCoverages, ComDriver } from 'app/app-model/driver';
import { IncidentsResponse, Incident, IncidentForHintsAndWarnings, IncidentTypeResponse, IncidentType } from 'app/app-model/incident';
import { Town } from 'app/app-model/specs';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';

type LifeCycleType = null | 'ngOnInit' | 'ngOnDestroy';

@Component({
    selector: 'app-hints-and-warnings',
    templateUrl: './hints-and-warnings.component.html',
    styleUrls: ['./hints-and-warnings.component.scss'],
    standalone: false
})
export class HintsAndWarningsComponent implements OnInit, OnDestroy {
  private get getAdditionalData$(): Observable<any> {
    return this._additionalData.asObservable();
  }
  private get additionalDataQuoteInfo$(): Observable<AdditionalDataQuoteInfo> {
    return this._additionalDataQuoteInfo.asObservable();
  }
  private get additionalDataAutoDrivers$(): Observable<AdditionalDataAutoDrivers> {
    return this._additionalDataAutoDrivers.asObservable();
  }
  private get additionalDataQuotePlans$(): Observable<AdditionalDataQuotePlans> {
    return this._additionalDataQuotePlans.asObservable();
  }
  private get additionalDataAutoVehicles$(): Observable<AdditionalDataAutoVehicles> {
    return this._additionalDataAutoVehicles.asObservable();
  }
  private get additionalDataCommAutoClientInfo$(): Observable<AdditionalDataAutoClientInfo> {
    return this._additionalDataCommAutoClientInfo.asObservable();
  }
  private get additionalDataCommAutoDrivers$(): Observable<AdditionalDataAutoDrivers> {
    return this._additionalDataCommAutoDrivers.asObservable();
  }
  private get additionalDataCommQuotePlans$(): Observable<AdditionalDataQuotePlans> {
    return this._additionalDataCommQuotePlans.asObservable();
  }
  private get additionalDataCommAutoVehicles$(): Observable<AdditionalDataAutoVehicles> {
    return this._additionalDataCommAutoVehicles.asObservable();
  }
  private get additionalDataAutoClientInfo$(): Observable<AdditionalDataAutoClientInfo> {
    return this._additionalDataAutoClientInfo.asObservable();
  }
  private get additionalDataHomeDwelling$(): Observable<AdditionalDataHomeDwelling> {
    return this._additionalDataHomeDwelling.asObservable();
  }
  private get additionalDataHomeClientInfo$(): Observable<AdditionalDataHomeClientInfo> {
    return this._additionalDataHomeClientInfo.asObservable();
  }
  private get additionalDataUmbrellaClientInfo$(): Observable<AdditionalDataUmbrellaClientInfo> {
    return this._additionalDataUmbrellaClientInfo.asObservable();
  }
  private get additionalDataHomeBasicsCoverages$(): Observable<AdditionalDataHomeBasicsCoverages> {
    return this._additionalDataHomeBasicsCoverages.asObservable();
  }
  private get additionalDataHomeScheduledProperty$(): Observable<AdditionalDataScheduledProperty> {
    return this._additionalDataHomeScheduledProperty.asObservable();
  }


  constructor(
    private router: Router,
    private currentPageService: CurrentPageService,
    private storageService: StorageService,
    private hintsAndWarningsService: HintsAndWarningsService,
    private coveragesService: CoveragesService,
    private optionsService: OptionsService,
    private quotesService: QuotesService,
    private clientsService: ClientsService,
    private lookupsService: LookupsService,
    private locationsService: LocationsService,
    private premiumsService: PremiumsService,
    private specsService: SpecsService,
    private dwellingService: DwellingService,
    private apiCommonService: ApiCommonService,
    private dwellingOptionValidation: DwellingOptionsValidationService,
    private storageGlobalService: StorageGlobalService
  ) { }

  private _componentLifeCycleHook: BehaviorSubject<LifeCycleType> = new BehaviorSubject(null);

  private subscriptionMainQuoteDataAndWatchers: ISubscription;
  private subscriptionMainClientDataAndWatchers: ISubscription;

  private subscriptionRouter: ISubscription;
  private subscriptionQuote: ISubscription;
  private subscriptionQuotePlans: ISubscription;
  private subscriptionQuoteFormTypes: ISubscription;
  private subscriptionVehicleOptionsModel: ISubscription;
  private subscriptionCommonAutoCarrierOptions: ISubscription;
  private subscriptionCommonHomeCarrierOptions: ISubscription;
  private subscriptionCommonAutoDrivers: ISubscription;
  private subscriptionCommonAutoVehicles: ISubscription;
  private subscriptionCommonAutoPolicyHistory: ISubscription;
  private subscriptionLobTownsList: ISubscription;

  private subscriptionDriversArray: ISubscription[];
  private subscriptionVehiclesArray: ISubscription[];
  private subscriptionStandardCoveragesArr: ISubscription[];
  private subscriptionPolicyHistoryArr: ISubscription[];
  private subscriptionInfoClient: ISubscription;
  private subscriptionPhoneInfoClient: ISubscription[];
  private subscriptionInfoClientAddressesArray: ISubscription[];
  private subscriptionInfoContactMethodsArray: ISubscription[];
  private subscriptionVehiclesLocationsArray: ISubscription[];
  private subscriptionInfoQuoteArr: ISubscription[];
  private subscriptionAutoPolicyOptions: ISubscription;
  private subscriptionAutoDriversOptionsAndCarriers: ISubscription;
  private subscriptionAutoDriversIncidentsArray: ISubscription[];
  private subscriptionDwellingArr: ISubscription[];
  private subscriptionDwellingLocation: ISubscription;
  private subscriptionDwellingFireLocation: ISubscription[];
  private subscriptionDwellingSubsystemsArr: ISubscription[];
  private subscriptionDwellingScheduledProperty: ISubscription[];
  private subscriptionBasicsCoveragesStandardArr: ISubscription[];
  private subscriptionHomeGeneralOptions: ISubscription[];
  private subscriptionHomeCarrierOptions: ISubscription[];
  private subscriptionHomeQuoteCoverages: ISubscription;
  private subscriptionHomeLossHistory: ISubscription[];
  private subscriptionHomeLossHistoryItems: ISubscription[];
  private subscriptionHomeDwellingProtectionClass: ISubscription;
  private subscriptionUmbrellaCarrierOptions: ISubscription;
  private subscriptionUmbrellaGeneralOptions: ISubscription;
  private subscriptionUmbrellaBasicPolicyInfo: ISubscription;
  private subscriptionAutoQuotePlansArr: ISubscription[];
  private subscriptionCommercialAutoCoverage: ISubscription[];
  private subscriptionCommDriversArray: ISubscription[];

  private vehicles;
  private quoteId;
  private quote;
  private selectedQuotePlansList;
  private selectedQuotePlansListIds: string[] = [];
  public coverages: Coverage[] = [];
  private isNewQuote: boolean;

  private additionalData: AdditionalDataI = {
    quoteSelectedPlans: [],
    quoteSelectedPlansIds: [],
    specLobTowns: []
  };
  private _additionalData: BehaviorSubject<AdditionalDataI> = new BehaviorSubject(this.additionalData);

  // Additional data for Quote Info
  // ---------------------------------------------------------------------------
  private additionalDataQuoteInfo: AdditionalDataQuoteInfo = new AdditionalDataQuoteInfo();
  private _additionalDataQuoteInfo: BehaviorSubject<AdditionalDataQuoteInfo> = new BehaviorSubject(this.additionalDataQuoteInfo);

  // Additional data for Auto - drivers
  // ---------------------------------------------------------------------------
  private additionalDataAutoDrivers: AdditionalDataAutoDrivers = new AdditionalDataAutoDrivers();
  private _additionalDataAutoDrivers: BehaviorSubject<AdditionalDataAutoDrivers> = new BehaviorSubject(this.additionalDataAutoDrivers);

  // Additional data for Auto - quote plans
  // ---------------------------------------------------------------------------
  private additionalDataQuotePlans: AdditionalDataQuotePlans = new AdditionalDataQuotePlans();
  private _additionalDataQuotePlans: BehaviorSubject<AdditionalDataQuotePlans> = new BehaviorSubject(this.additionalDataQuotePlans);

  // Additional data for Auto - vehicle
  // ---------------------------------------------------------------------------
  private additionalDataAutoVehicles: AdditionalDataAutoVehicles = new AdditionalDataAutoVehicles();
  private _additionalDataAutoVehicles: BehaviorSubject<AdditionalDataAutoVehicles> = new BehaviorSubject(this.additionalDataAutoVehicles);

  // Additional data for Commercial Auto - ClientInfo
  // ---------------------------------------------------------------------------
  private additionalDataCommAutoClientInfo: AdditionalDataAutoClientInfo = new AdditionalDataAutoClientInfo();
  private _additionalDataCommAutoClientInfo: BehaviorSubject<AdditionalDataAutoClientInfo> = new BehaviorSubject(this.additionalDataCommAutoClientInfo);


   // Additional data for Auto - drivers
  // ---------------------------------------------------------------------------
  private additionalDataCommAutoDrivers: AdditionalDataAutoDrivers = new AdditionalDataAutoDrivers();
  private _additionalDataCommAutoDrivers: BehaviorSubject<AdditionalDataAutoDrivers> = new BehaviorSubject(this.additionalDataCommAutoDrivers);

  // Additional data for Auto - quote plans
  // ---------------------------------------------------------------------------
  private additionalDataCommQuotePlans: AdditionalDataQuotePlans = new AdditionalDataQuotePlans();
  private _additionalDataCommQuotePlans: BehaviorSubject<AdditionalDataQuotePlans> = new BehaviorSubject(this.additionalDataCommQuotePlans);

  // Additional data for Auto - vehicle
  // ---------------------------------------------------------------------------
  private additionalDataCommAutoVehicles: AdditionalDataAutoVehicles = new AdditionalDataAutoVehicles();
  private _additionalDataCommAutoVehicles: BehaviorSubject<AdditionalDataAutoVehicles> = new BehaviorSubject(this.additionalDataCommAutoVehicles);

  // Additional data for Auto - ClientInfo
  // ---------------------------------------------------------------------------
  private additionalDataAutoClientInfo: AdditionalDataAutoClientInfo = new AdditionalDataAutoClientInfo();
  private _additionalDataAutoClientInfo: BehaviorSubject<AdditionalDataAutoClientInfo> = new BehaviorSubject(this.additionalDataAutoClientInfo);


  // Additional data for Home - Dwelling
  // ---------------------------------------------------------------------------
  private additionalDataHomeDwelling: AdditionalDataHomeDwelling = new AdditionalDataHomeDwelling();
  private _additionalDataHomeDwelling: BehaviorSubject<AdditionalDataHomeDwelling> = new BehaviorSubject(this.additionalDataHomeDwelling);

  // Additional data for Home - ClientInfo
  // ---------------------------------------------------------------------------
  private additionalDataHomeClientInfo: AdditionalDataHomeClientInfo = new AdditionalDataHomeClientInfo();
  private _additionalDataHomeClientInfo: BehaviorSubject<AdditionalDataHomeClientInfo> = new BehaviorSubject(this.additionalDataHomeClientInfo);

  // Additional data for Umbrella - ClientInfo
  // ---------------------------------------------------------------------------
  private additionalDataUmbrellaClientInfo: AdditionalDataUmbrellaClientInfo = new AdditionalDataUmbrellaClientInfo();
  private _additionalDataUmbrellaClientInfo: BehaviorSubject<AdditionalDataUmbrellaClientInfo> = new BehaviorSubject(this.additionalDataUmbrellaClientInfo);

 // Additional data for Home - Dwelling-basics
  // ---------------------------------------------------------------------------
  private additionalDataHomeBasicsCoverages: AdditionalDataHomeBasicsCoverages = new AdditionalDataHomeBasicsCoverages();
  private _additionalDataHomeBasicsCoverages: BehaviorSubject<AdditionalDataHomeBasicsCoverages> = new BehaviorSubject(this.additionalDataHomeBasicsCoverages);

    // Additional data for Home - Schedules
  // ---------------------------------------------------------------------------
  private additionalDataHomeScheduledProperty: AdditionalDataScheduledProperty = new AdditionalDataScheduledProperty();
  private _additionalDataHomeScheduledProperty: BehaviorSubject<AdditionalDataScheduledProperty> = new BehaviorSubject(this.additionalDataHomeScheduledProperty);

  // vehiclesOptionsMake
  private subscriptionVehicleOptionsMake: ISubscription;

  private subscriptionGeneralDetailsHelper: ISubscription;

  private subscriptionAutoVehicleSelectedVehicleSymbolsForVehicle: ISubscription;

  // Auto data fetching
  // ----------------------------------------------------------------------------
  private subscriptionGetAllDataAutoVehicleList: ISubscription;


  private getSingleVehicleOptionsModelSubscriptions: { [key: string]: ISubscription } = {}; // Allow to cancel XHR Requests


  private getLocationInformationForSingleVehicleSubscriptions: { [key: string]: ISubscription } = {}; // Allow to cancel XHR Requests

  ngOnInit() {
    // Clear all Hints And Warnings
    this.hintsAndWarningsService.clearAllWarnings();

    this._componentLifeCycleHook.next('ngOnInit');

    this.subscribeRouter();
    this.subscribeSelectedQuote();
    this.subscribeSelectedQuotePlans();
    this.subscribeLobTownsList();
    this.subscribeSelectedQuoteFormTypes();
    this.mainSubscribeAndInitProperQuoteDataAndWatchers();
    // this.mainSubscribeAndInitProperClientDataAndWatchers();
    this.subscribeCommonAutoCarrierOptions();
    this.subscribeCommonHomeCarrierOptions();
    this.subscribeCommonAutoDrivers();
    this.subscribeCommonAutoVehicles();
    this.subscribeCommonAutoPolicyHistory();
    this.subscribeCommercialAutoDrivers();
    this.subscribeCommercialAutoVehicles();
  }


  ngOnDestroy() {
    this._componentLifeCycleHook.next('ngOnDestroy');

    this.subscriptionRouter && this.subscriptionRouter.unsubscribe();
    this.subscriptionQuote && this.subscriptionQuote.unsubscribe();
    this.subscriptionQuotePlans && this.subscriptionQuotePlans.unsubscribe();
    this.subscriptionLobTownsList && this.subscriptionLobTownsList.unsubscribe();
    this.subscriptionMainQuoteDataAndWatchers && this.subscriptionMainQuoteDataAndWatchers.unsubscribe();
    this.subscriptionMainClientDataAndWatchers && this.subscriptionMainClientDataAndWatchers.unsubscribe();
    this.subscriptionQuoteFormTypes && this.subscriptionQuoteFormTypes.unsubscribe();
    this.subscriptionCommonAutoCarrierOptions && this.subscriptionCommonAutoCarrierOptions.unsubscribe();
    this.subscriptionCommonHomeCarrierOptions && this.subscriptionCommonHomeCarrierOptions.unsubscribe();
    this.subscriptionCommonAutoDrivers && this.subscriptionCommonAutoDrivers.unsubscribe();
    this.subscriptionCommonAutoVehicles && this.subscriptionCommonAutoVehicles.unsubscribe();
    this.subscriptionCommonAutoPolicyHistory && this.subscriptionCommonAutoPolicyHistory.unsubscribe();

    this.destroyAllWatchersAndSubscriptionsForQuote();
  }

  // Scroll page to the right position of the highlighted field
  // ----------------------------------------------------------------------------
  private subscribeRouter(): void {
    this.subscriptionRouter = this.router.events && this.router.events.subscribe(val => {
      if (val instanceof NavigationEnd) {
        this.warningsModuleScrollHandle(val);
      }
    });
  }

  private warningsModuleScrollHandle(val: NavigationEnd): void {
    const fragment = this.router.parseUrl(val.url).fragment;
    this.hintsAndWarningsService.removeWarnFocusFromAllElements();

    if (fragment) {
      // Get element to focus and to interact (click)
      const fragmentData: IHintsAndWarningsLocationFragmentElements = this.hintsAndWarningsService.getLocationFragmentElements(fragment);

      this.hintsAndWarningsService
        .handleFindFocusAndInteractWithWarnElement(
          fragmentData.elementIdToFocus,
          'dashboardMainScrollContainer',
          fragmentData.elementIdToInteract
        );
    }
  }


  // Main initialization
  // ----------------------------------------------------------------------------
  private mainSubscribeAndInitProperQuoteDataAndWatchers(): void {
    this.subscriptionMainQuoteDataAndWatchers = this.storageService.getStorageData('selectedQuote')
      .subscribe((quote: Quote) => {

        if (this.quoteId !== quote.quoteSessionId) {
          this.quoteId = quote.resourceId;
          this.quote = quote;

          if (!this.quoteId) { return; }

          // Clear all Hints And Warnings
          this.hintsAndWarningsService.clearAllWarnings();
          // Destroy All Subscriptions
          this.destroyAllWatchersAndSubscriptionsForQuote();

          switch (this.quote.lob) {
            case 'AUTOP':
              this.getAllDataAuto();
              this.initInfoQuoteHintsAndWarnings();
              this.initAutoHintAndWarnings();
              break;
            case 'HOME':
              // Get All Data For Home
              // this.getAllDataHome(); // Issues with loss history
              this.initInfoQuoteHintsAndWarnings();
              this.initHomeHintAndWarnings();
              break;
            case 'DFIRE':
              // Get All Data For DFIRE
              this.getAllDataDwelling();
              this.initInfoQuoteHintsAndWarnings();
              this.initDwellingHintAndWarnings();
              break;
            case 'PUMBR':
              this.initInfoQuoteHintsAndWarnings();
              this.initUmbrellaHintAndWarnings();
              break;
            case 'AUTOB':
            this.initInfoQuoteHintsAndWarnings();
            this.initCommAutoHintAndWarnings();

          }
        }
      });
  }

  private mainSubscribeAndInitProperClientDataAndWatchers(): void {
    this.subscriptionMainClientDataAndWatchers = this.storageService.getStorageData('selectedClient')
    .subscribe((data: ClientDetails) => {
        if (data.resourceId) {
          // Clear all Hints And Warnings
          // this.hintsAndWarningsService.clearAllWarnings();
          // Destroy All Subscriptions
          this.destructorInfoClientHintsAndWarnings();

          this.initInfoClientHintsAndWarnings();
        }
    });
  }

  // Destroy all available subscriptions for the Quote to prevent errors if the Quote will change
  // or we move out of the quote
  private destroyAllWatchersAndSubscriptionsForQuote(): void {
    this.destructorInfoQuoteHintsAndWarnings();
    this.destructorAutoHintAndWarnings();
    this.destructorHomeHintAndWarnings();
    this.destructorDwellingHintAndWarnings();
    this.destructorUmbrellaHintAndWarnings();
  }


  // Quote Info
  // ----------------------------------------------------------------------------
  private initInfoQuoteHintsAndWarnings(): void {
    this.subscribeQuoteInfo();
  }

  private destructorInfoQuoteHintsAndWarnings(): void {
    this.unsubscribeAllSubscriptionsInArray(this.subscriptionInfoQuoteArr);
  }

  private subscribeQuoteInfo(): void {
    const uqInfoQuoteId = 'QuoteInfo';

    this.subscriptionInfoQuoteArr = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_INFO_QUOTE,
      this.storageService.getStorageData('selectedQuote'),
      uqInfoQuoteId,
      this.additionalDataQuoteInfo$
    );
  }

  // Client Info
  // -----------------------------------------------------------------------------
  private initInfoClientHintsAndWarnings(): void {
    this.subscribeClientInfo();
  }

  private destructorInfoClientHintsAndWarnings(): void {
    this.subscriptionInfoClient && this.subscriptionInfoClient.unsubscribe();
  }

  private subscribeClientInfo(): void {
    const uqInfoClientId = 'ClientInfo';

    this.subscriptionInfoClient = this.hintsAndWarningsService.watchObjectDataForHintsAndWarnings(
      WARNINGS_DEFINITIONS_INFO_CLIENT,
      this.storageService.getStorageData('selectedClient'),
      uqInfoClientId,
      this.additionalData
    );
  }

   // Comm Auto
  // ----------------------------------------------------------------------------
  private initCommAutoHintAndWarnings(): void {
    this.subscribeClientInfoForAUTOB();
    this.subscribeCommAutoDriver();
    this.subscribeClientAddressForAUTOB();
    this.subscribeClientPhoneForAUTOB();
    this.subscribeCoveragesForAUTOB();
    this.subscribeCommAutoVehicles();
    this.subscribeCoveragesSymbolsForAUTOB();

  }

  // Auto
  // ----------------------------------------------------------------------------
  private initAutoHintAndWarnings(): void {

    this.subscribeClientInfoForAUTO();
    this.subscribeClientInfoAddressesForAUTO();
    this.subscribeClientInfoContactMethodsForAUTO();
    this.subscribeAutoVehicleOptionsMake();
    this.subscribeAutoVehicleOptionsModel();
    this.subscribeAutoGeneralDetailsHelper();
    this.subscribeAutoVehicleSelectedVehicleSymbolsForVehicle();
    this.subscribeAutoDriver();

    this.subscribeAutoDriverIncidents();

    this.subscribeAutoDriversOptionsAndCarriers();
    this.subscribeAutoVehicles();
    this.subscribeAutoVehiclesLocations();
    this.subscribeAutoStandardCoveragesForVehicles();
    this.subscribeAutoAdditionalCoveragesForVehicles();
    this.subscribeAutoPolicyHistory();
    this.subscribeAutoPolicesList();
    this.subscribeAutoQuotePlans();
    // this.subscribeAutoDriversOptionsAndCarriers();
  }

  private destructorAutoHintAndWarnings(): void {
    this.subscriptionInfoClient && this.subscriptionInfoClient.unsubscribe();
    this.unsubscribeAllSubscriptionsInArray(this.subscriptionInfoClientAddressesArray);
    this.unsubscribeAllSubscriptionsInArray(this.subscriptionInfoContactMethodsArray);
    this.subscriptionVehicleOptionsModel && this.subscriptionVehicleOptionsModel.unsubscribe();
    this.subscriptionVehicleOptionsMake && this.subscriptionVehicleOptionsMake.unsubscribe();
    this.subscriptionAutoVehicleSelectedVehicleSymbolsForVehicle && this.subscriptionAutoVehicleSelectedVehicleSymbolsForVehicle.unsubscribe();
    this.subscriptionGeneralDetailsHelper && this.subscriptionGeneralDetailsHelper.unsubscribe();
    this.unsubscribeAllSubscriptionsInArray(this.subscriptionDriversArray);
    this.unsubscribeAllSubscriptionsInArray(this.subscriptionVehiclesArray);
    this.unsubscribeAllSubscriptionsInArray(this.subscriptionStandardCoveragesArr);
    this.unsubscribeAllSubscriptionsInArray(this.subscriptionPolicyHistoryArr);
    this.unsubscribeAllSubscriptionsInArray(this.subscriptionVehiclesLocationsArray);
    this.subscriptionAutoPolicyOptions && this.subscriptionAutoPolicyOptions.unsubscribe();
    this.subscriptionAutoDriversOptionsAndCarriers && this.subscriptionAutoDriversOptionsAndCarriers.unsubscribe();
    this.unsubscribeAllSubscriptionsInArray(this.subscriptionAutoDriversIncidentsArray);
    this.unsubscribeAllSubscriptionsInArray(this.subscriptionAutoQuotePlansArr);
  }

  // COMM AUTO MODULE
  // ---------------------------------------------------------------------------
  // ******************************* */

  // Driver Tab
  private subscribeCommAutoDriver(): void {
    const uqDriverGroupId = 'DriverModel';

    // It needs to be validate also after Selected Plans change
    this.subscriptionCommDriversArray = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_COM_AUTO_DRIVER,
      this.storageService.getStorageData('comDriverList'),
      uqDriverGroupId,
      this.additionalDataCommAutoDrivers$
    );
  }

  private subscribeCommAutoVehicles(): void {
    const uqDriverGroupId = 'ComVehicles';

    // It needs to be validate also after Selected Plans change
    this.subscriptionCommDriversArray = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_COM_AUTO_VEHICLES,
      this.storageService.getStorageData('comVehicles'),
      uqDriverGroupId,
      this.additionalDataAutoVehicles$
    );
  }


  // Client Info
  private subscribeClientInfoForAUTOB(): void {
    const uqInfoClientId = 'ClientInfo';

    this.subscriptionInfoClient = this.hintsAndWarningsService.watchObjectDataForHintsAndWarnings(
      WARNINGS_DEFINITIONS_INFO_CLIENT_AUTOB,
      this.storageService.getStorageData('clients'),
      uqInfoClientId,
      this.additionalData
    );
  }

  private subscribeClientAddressForAUTOB(): void {
    const uqInfoClientId = 'ClientAddress';

    this.subscriptionInfoClient = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsAUTOBAddress(
      WARNINGS_DEFINITIONS_CLIENT_ADDRESS_AUTOB,
      this.storageService.getStorageData('clientAddresses'),
      uqInfoClientId,
      this.additionalData
    );
  }

  private subscribeClientPhoneForAUTOB(): void {
    const uqInfoClientId = 'ClientPhone';

    this.subscriptionPhoneInfoClient = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_CLIENT_PHONE_AUTOB,
      this.storageService.getStorageData('clientContactMethods'),
      uqInfoClientId,
      this.additionalDataAutoVehicles$
    );
  }

  private subscribeCoveragesForAUTOB(): void {
    const uqInfoClientId = 'Coverages';

    this.subscriptionCommercialAutoCoverage = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_COM_AUTO_COVERAGES,
      this.storageService.getStorageData('commercialAutoQuoteCoverages'),
      uqInfoClientId,
      this.additionalDataAutoVehicles$
    );
  }

  private subscribeCoveragesSymbolsForAUTOB(): void {
    const uqInfoClientId = 'Symbols';

    this.subscriptionCommercialAutoCoverage = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_COM_AUTO_SYMBOL_COVERAGES,
      this.storageService.getStorageData('coveredAutoSymbols'),
      uqInfoClientId,
      this.storageService.getStorageData('commercialAutoQuoteCoverages')
    );
  }

  // AUTO MODULE
  // ----------------------------------------------------------------------------

  // Client Info
  private subscribeClientInfoForAUTO(): void {
    const uqInfoClientId = 'ClientInfo';

    this.subscriptionInfoClient = this.hintsAndWarningsService.watchObjectDataForHintsAndWarnings(
      WARNINGS_DEFINITIONS_INFO_CLIENT_FOR_AUTO,
      this.storageService.getStorageData('clients'),
      uqInfoClientId,
      this.additionalData
    );
  }

  private subscribeClientInfoAddressesForAUTO(): void {
    const uqInfoClientAddrId = 'ClientInfoAddr';

    // It needs to be validate also after Selected Plans change
    this.subscriptionInfoClientAddressesArray = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_INFO_CLIENT_ADDRESSES_FOR_AUTO,
      this.storageService.getStorageData('clientAddresses'),
      uqInfoClientAddrId,
      this.additionalDataAutoClientInfo$
    );
  }


  private subscribeClientInfoContactMethodsForAUTO(): void {
    const uqInfoClientContactMethodsId = 'ClientInfoContactMethods';

    // It needs to be validate also after Selected Plans change
    this.subscriptionInfoContactMethodsArray = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_INFO_CLIENT_CONTACT_METHODS_FOR_AUTO,
      this.storageService.getStorageData('clientContactMethods'),
      uqInfoClientContactMethodsId,
      this.additionalDataAutoClientInfo$
    );
  }

  // ******************************* */

  // Driver Tab
  private subscribeAutoDriver(): void {
    const uqDriverGroupId = 'DriverModel';

    // It needs to be validate also after Selected Plans change
    this.subscriptionDriversArray = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_AUTO_DRIVER,
      this.storageService.getStorageData('driversList'),
      uqDriverGroupId,
      this.additionalDataAutoDrivers$
    );
  }

  // Drivers Tab - Incidents
  private subscribeAutoDriverIncidents(): void {
    // let driversList: Driver[] = [];
    let incidentTypes: IncidentType[] = [];
    let allIncidents: Incident[] = [];
    let driversObj: {[key: string]: Driver} = {};
    let incidentsForHintsAndWarnings: IncidentForHintsAndWarnings[] = [];
    const _incidentsForHintsAndWarnings: BehaviorSubject<IncidentForHintsAndWarnings[]> = new BehaviorSubject(incidentsForHintsAndWarnings);

    function getDriversIncidentsAsArrayForHintsAndWarnings(data: IncidentsResponse[]): Incident[] {
      let result: Incident[] = [];

      if (data && data.length) {
        data.forEach((res: IncidentsResponse) => {
          if (res && res.items && res.items.length) {
            result = [...result, ...res.items];
          }
        });
      }

      return result;
    }

    function convertDriverIncidentsToDriverIncidentsForHintsAndWarnings(incidents: Incident[], drivers: {[key: string]: Driver}, incidentsTypes: IncidentType[]): IncidentForHintsAndWarnings[] {
      let result: IncidentForHintsAndWarnings[] = [];

      result = incidents.map((item: Incident) => {
        const tmpData: IncidentForHintsAndWarnings = Object.assign(new IncidentForHintsAndWarnings(), item);

        if ((drivers[item.parentId])) {
          const dr: any = drivers[item.parentId]; // Driver
          tmpData.driver = dr;
        }

        if (incidentsTypes && incidentsTypes.length) {
          tmpData.incidentTypes = incidentsTypes;
        }

        return tmpData;
        // return (drivers[item.parentId]) ? item.driver
      });

      return result;
    }

    const localIncidentsTypesAndDescSubscription = this.storageService.getStorageData('autoDriverIncidentsTypesAndDesc')
      .subscribe((res: IncidentTypeResponse) => {
        incidentTypes = (res && res.items) ? res.items : [];
        // console.log('Subscribed Incidents ', incidentTypes);
        incidentsForHintsAndWarnings = convertDriverIncidentsToDriverIncidentsForHintsAndWarnings(allIncidents, driversObj, incidentTypes);
        _incidentsForHintsAndWarnings.next(incidentsForHintsAndWarnings);
      });


    const localDriversSubscription: ISubscription = this.storageService.getStorageData('driversList')
      .subscribe((drivers: Driver[]) => {
        const driversList: Driver[] = JSON.parse(JSON.stringify(drivers));
        driversObj = {};

        if (driversList && driversList.length) {
          driversList.forEach((d: Driver) => driversObj[d.resourceId] = d);
        }

        incidentsForHintsAndWarnings = convertDriverIncidentsToDriverIncidentsForHintsAndWarnings(allIncidents, driversObj, incidentTypes);
        _incidentsForHintsAndWarnings.next(incidentsForHintsAndWarnings);
      });

    const localDriversIncidentsSubscription: ISubscription = this.storageService.getStorageData('driverIncidents')
      .subscribe((res: IncidentsResponse[]) => {
        allIncidents = JSON.parse(JSON.stringify(getDriversIncidentsAsArrayForHintsAndWarnings(res)));
        incidentsForHintsAndWarnings = convertDriverIncidentsToDriverIncidentsForHintsAndWarnings(allIncidents, driversObj, incidentTypes);
        _incidentsForHintsAndWarnings.next(incidentsForHintsAndWarnings);
      });

    this._componentLifeCycleHook.asObservable().subscribe(res => {
      if (res === 'ngOnDestroy') {
        localDriversSubscription && localDriversSubscription.unsubscribe();
        localDriversIncidentsSubscription && localDriversIncidentsSubscription.unsubscribe();
        localIncidentsTypesAndDescSubscription && localIncidentsTypesAndDescSubscription.unsubscribe();
      }
    });

    const uqGroupId = 'AutoDriversIncidents';

    this.subscriptionAutoDriversIncidentsArray = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_AUTO_DRIVERS_INCIDENTS,
      _incidentsForHintsAndWarnings.asObservable(),
      uqGroupId,
      this.getAdditionalData$
    );
  }


  // Drivers Tab - Drivers Options And Coverages
  private subscribeAutoDriversOptionsAndCarriers(): void {
    // Helper function
    function convertDriverOptionsAndCoveragesToCoverageItemParsedForAutoDriverOptionsHintsAndWarnings(driverOptionsAndCoverages: DriverOptionsAndCoverages[]): CoverageItemParsedForAutoDriverOptionsHintsAndWarnings[] {
      let result: CoverageItemParsedForAutoDriverOptionsHintsAndWarnings[] = [];

      driverOptionsAndCoverages.forEach((item: DriverOptionsAndCoverages) => {
        const tmpOptions: CoverageItemParsedForAutoDriverOptionsHintsAndWarnings[] = item.options.map((option: CoverageItemParsed) => {
          const readyOption: CoverageItemParsedForAutoDriverOptionsHintsAndWarnings = Object.assign(
            new CoverageItemParsedForAutoDriverOptionsHintsAndWarnings(),
            option
          );

          readyOption.driver = item.diver;
          return readyOption;
        });

        result = result.concat(tmpOptions);
      });

      return result;
    }

    const uqGroupId = 'AutoDriversOptionsAndCoverages';

    this.subscriptionAutoDriversOptionsAndCarriers = this.hintsAndWarningsService.watchObjectDataForHintsAndWarnings(
      WARNINGS_DEFINITIONS_AUTO_DRIVERS_OPTIONS_AND_COVERAGES,
      this.storageService.getStorageData('autoDriversOptionsAndCoverages').pipe(
        map((res: DriverOptionsAndCoverages[]) => convertDriverOptionsAndCoveragesToCoverageItemParsedForAutoDriverOptionsHintsAndWarnings(res))),
      uqGroupId,
      this.additionalData
    );
  }


  // Vehicles Tab
  private subscribeAutoVehicles(): void {
    const uqVehiclesGroupId = 'VehicleModel';

    // It needs to be validate also after Selected Plans change and Vehicle model options
    this.subscriptionVehiclesArray = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_AUTO_VEHICLE,
      this.storageService.getStorageData('vehiclesList'),
      uqVehiclesGroupId,
      this.additionalDataAutoVehicles$
    );
  }

  private subscribeAutoVehiclesLocations(): void {
    const uqGroupId = 'VehicleLocationsForVehicles';

    // It needs to be validate also after Selected Plans change and Vehicle model options
    this.subscriptionVehiclesLocationsArray = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_AUTO_VEHICLE_LOCATIONS,
      this.storageService.getStorageData('vehiclesLocationsForVehicles'),
      uqGroupId,
      this.getAdditionalData$
    );
  }

  private subscribeAutoVehicleOptionsModel(): void {
    this.subscriptionVehicleOptionsModel = this.storageService.getStorageData('vehiclesOptionsModel').subscribe((options: VehicleOptionsForVehicle[]) => {
      // Auto Vehicle
      this.additionalDataAutoVehicles.vehiclesOptionsModel = options;
      this._additionalDataAutoVehicles.next(this.additionalDataAutoVehicles);

    });
  }
  private subscribeAutoVehicleOptionsMake(): void {
    this.subscriptionVehicleOptionsMake = this.storageService.getStorageData('vehiclesOptionsMake').subscribe((options: VehicleOptionsForVehicle[]) => {
      this.additionalDataAutoVehicles.vehiclesOptionsMake = options;
      this._additionalDataAutoVehicles.next(this.additionalDataAutoVehicles);
    });
  }
  private subscribeAutoGeneralDetailsHelper(): void {
    this.subscriptionGeneralDetailsHelper = this.storageService.getStorageData('vehicleGeneralDetailsHelper').subscribe((data: VehicleGeneralDetailsHelper[]) => {
      this.additionalDataAutoVehicles.vehicleGeneralDetailsHelper = data;
      this._additionalDataAutoVehicles.next(this.additionalDataAutoVehicles);

    });
  }
  private subscribeAutoVehicleSelectedVehicleSymbolsForVehicle(): void {
    this.subscriptionAutoVehicleSelectedVehicleSymbolsForVehicle = this.storageService.getStorageData('vehiclesSelectedSymbolsForVehicle').subscribe((res: SelectedVehicleSymbolsForVehicle[]) => {
      this.additionalDataAutoVehicles.selectedVehicleSymbolsForVehicle = res;
      this._additionalDataAutoVehicles.next(this.additionalDataAutoVehicles);
    });
  }


  // Coverages Tab
  // TODO:: autoCoveragesAdditionalForVehicles if necessary

  private subscribeAutoStandardCoveragesForVehicles(): void {
    const uqStandardCoveragesId = 'StandardCoveragesAuto';

    // Helper - get proper data to observe
    let vehiclesStandardCoverages: CoverageItemParsed[] = [];
    const _vehiclesStandardCoverages: BehaviorSubject<CoverageItemParsed[]> = new BehaviorSubject(vehiclesStandardCoverages);

    const subscription: ISubscription = this.storageService.getStorageData('autoCoveragesStandardForVehicles').subscribe((data: VehicleCoverages[]) => {
      vehiclesStandardCoverages = [];
      if (data && data.length) {
        data.forEach((item: VehicleCoverages) => {
          vehiclesStandardCoverages = vehiclesStandardCoverages.concat(item.options);
        });
      }


      _vehiclesStandardCoverages.next(vehiclesStandardCoverages);
    });

    this._componentLifeCycleHook.asObservable().subscribe(res => {
      if (res === 'ngOnDestroy') {
        subscription && subscription.unsubscribe();
      }
    });

    this.subscriptionStandardCoveragesArr = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_AUTO_STANDARD_COVERAGES,
      _vehiclesStandardCoverages.asObservable(),
      uqStandardCoveragesId,
      this.getAdditionalData$
    );
  }

  private subscribeAutoAdditionalCoveragesForVehicles(): void {
    const uqStandardCoveragesId = 'AdditionalCoveragesAuto';

    // Helper - get proper data to observe
    let vehiclesStandardCoverages: CoverageItemParsed[] = [];
    const _vehiclesStandardCoverages: BehaviorSubject<CoverageItemParsed[]> = new BehaviorSubject(vehiclesStandardCoverages);

    const subscription: ISubscription = this.storageService.getStorageData('autoCoveragesAdditionalForVehicles').subscribe((data: VehicleCoverages[]) => {
      vehiclesStandardCoverages = [];
      if (data && data.length) {
        data.forEach((item: VehicleCoverages, index) => {
          item.options.forEach(o => {o.additionalData = {}; o.additionalData.vehicle = item.vehicle; });
          vehiclesStandardCoverages = vehiclesStandardCoverages.concat(item.options);

        });
      }
      _vehiclesStandardCoverages.next(vehiclesStandardCoverages);
    });

    this._componentLifeCycleHook.asObservable().subscribe(res => {
      if (res === 'ngOnDestroy') {
        subscription && subscription.unsubscribe();
      }
    });

    this.subscriptionStandardCoveragesArr = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_AUTO_ADDITIONAL_COVERAGES,
      _vehiclesStandardCoverages.asObservable(),
      uqStandardCoveragesId,
      this.getAdditionalData$
    );

  }


  // Options Tab
  private subscribeAutoPolicyHistory(): void {
    const uqOptionsPolicyHistoryGroupId = 'AutoOptionsPolicyHistory';

    // It needs to be validate also after Selected Plans change
    this.subscriptionPolicyHistoryArr = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_AUTO_OPTIONS_POLICY_HISTORY,
      this.storageService.getStorageData('policyHistory'),
      uqOptionsPolicyHistoryGroupId,
      this.getAdditionalData$
    );
  }


  private subscribeAutoPolicesList(): void {
    const uqGroupId = 'AutoOptionsPolicyCarrierOptions';

    this.subscriptionAutoPolicyOptions = this.hintsAndWarningsService.watchObjectDataForHintsAndWarnings(
      WARNINGS_DEFINITIONS_AUTO_OPTIONS_CARRIER_OPTIONS,
      this.storageService.getStorageData('autoPolicyOptions'),
      uqGroupId,
      this.additionalData
    );
  }

  // Premiums Tab
  private subscribeAutoQuotePlans(): void {
    const uqGroupId = 'AutoQuotePlans';

    this.subscriptionAutoQuotePlansArr = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_AUTO_SELECTED_PLANS,
      this.storageService.getStorageData('selectedPlan'),
      uqGroupId,
      this.storageService.getStorageData('vehiclesList')
    );
  }


  // HOME MODULE
  // ----------------------------------------------------------------------------
  private subscribeClientInfoForHOME(): void {
    const uqInfoClientId = 'ClientInfo';

    this.subscriptionInfoClient = this.hintsAndWarningsService.watchObjectDataForHintsAndWarnings(
      WARNINGS_DEFINITIONS_INFO_CLIENT_FOR_HOME,
      this.storageService.getStorageData('clients'),
      uqInfoClientId,
      this.additionalData
    );
  }

  private subscribeClientInfoAddressesForHOME(): void {
    const uqInfoClientAddrId = 'ClientInfoAddr';

    // It needs to be validate also after Selected Plans change
    this.subscriptionInfoClientAddressesArray = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_INFO_CLIENT_ADDRESSES_FOR_HOME,
      this.storageService.getStorageData('clientAddresses'),
      uqInfoClientAddrId,
      this.additionalDataHomeClientInfo$
    );
  }

  private subscribeClientInfoContactMethodsForHOME(): void {
    const uqInfoClientContactMethodsId = 'ClientInfoContactMethods';

    // It needs to be validate also after Selected Plans change
    this.subscriptionInfoContactMethodsArray = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_INFO_CLIENT_CONTACT_METHODS_FOR_HOME,
      this.storageService.getStorageData('clientContactMethods'),
      uqInfoClientContactMethodsId,
      this.additionalDataHomeClientInfo$
    );
  }



  // Dwelling Tab
  private subscribeDwelling(): void {
    const uqDwellingGroupId = 'HomeDwelling';

    // It needs to be validate also after Quote FormPlans changed
    this.subscriptionDwellingArr = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_HOME_DWELLINGS,
      this.storageService.getStorageData('dwelling'),
      uqDwellingGroupId,
      this.additionalDataHomeDwelling$
    );
  }

  private subscribeDwellingLocation(): void {
    const uqDwellingLocationGroupId = 'HomeDwellingLocation';

    this.subscriptionDwellingLocation = this.hintsAndWarningsService.watchObjectDataForHintsAndWarnings(
      WARNINGS_DEFINITIONS_HOME_DWELLING_LOCATION,
      this.storageService.getStorageData('dwellingLocation'),
      uqDwellingLocationGroupId
    );
  }


  private subscribeDwellingFireLocation(): void {
    const uqDwellingLocationGroupId = 'HomeDwellingFireLocation';

    this.subscriptionDwellingFireLocation = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_HOME_DWELLING_FIRE_LOCATION,
      this.storageService.getStorageData('dwellingLocation'),
      uqDwellingLocationGroupId,
      this.additionalDataHomeDwelling$
    );
  }

  private subscribeDwellingSubsystems(): void {
    const uqDwellingSubsystemsGroupId = 'HomeDwellingSubsystems';

    // Helper - get proper data to observe
    let homeDwellingSubSystems: DwellingSubsystems = new DwellingSubsystems();
    const _homeDwellingSubSystems: BehaviorSubject<DwellingSubsystems> = new BehaviorSubject(homeDwellingSubSystems);

    const subscription: ISubscription = this.storageService.getStorageData('dwellingSubsystems').subscribe((data: DwellingsSubsystemsAPIResponse) => {
      if (data && data.items && data.items[0]) {
        homeDwellingSubSystems = JSON.parse(JSON.stringify(data.items[0]));
        _homeDwellingSubSystems.next(homeDwellingSubSystems);
      }
    });

    this._componentLifeCycleHook.asObservable().subscribe(res => {
      if (res === 'ngOnDestroy') {
        subscription && subscription.unsubscribe();
      }
    });

    this.subscriptionDwellingSubsystemsArr = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_HOME_DWELLINGS_SUBSYSTEMS,
      _homeDwellingSubSystems.asObservable(),
      uqDwellingSubsystemsGroupId,
      this.additionalDataHomeDwelling$
    );
  }

  // // Basics Tab
  // private subscribeBasicsCoveragesStandard(definition = WARNINGS_DEFINITIONS_HOME_BASICS, quoteLob = 'HOME'): void {
  //   const uqBasicsCoveragesStandardGroupId = 'HomeBasicsCoveragesStandard';

  //   // getHomeQuoteCoveragesItems
  //   this.subscriptionBasicsCoveragesStandard = this.storageService.getStorageData('homeQuoteCoverages').subscribe(data => {
  //     let allParsedCoverages: CoverageForVehicle[] = [];

  //     if (data && data['coverages']) {
  //       const newData = new CoverageApiResponseData();
  //       newData.items = [JSON.parse(JSON.stringify(data))];
  //       data = newData;
  //     }
  //     allParsedCoverages = this.coveragesService.convertCoverageApiResponseDataArrayToCoverageParsedDataArray([data]);

  //     // Add all missing and required coverages - If the field  is not set, the coverage is not returned from API
  //     // so we need to add it to the array, to be able to validate field (set as required in H&W panel)
  //     const requiredForHome: string[] = ['APDED', 'DWELL', 'OS', 'PP', 'LOU', 'PL', 'MEDPM', 'RCDWELL', 'WHDED']; // 'RCCONT',
  //     const requiredForDwelling: string[] = ['DWELL', 'OS', 'PP', 'PL', 'MEDPM', 'BSC_LOSSFREEYRS', 'FRV', 'WHDED', 'APDED'];

  //     let requiredCoveragesToCheck: string[] = [];

  //     if (quoteLob === 'HOME') {
  //       requiredCoveragesToCheck = requiredForHome;
  //     } else if (quoteLob === 'DFIRE') {
  //       requiredCoveragesToCheck = requiredForDwelling;
  //     }

  //     let missingCoverages: CoverageForVehicle[] = [];
  //     requiredCoveragesToCheck.forEach((covId: string) => {
  //       let tmpCovFound = allParsedCoverages.find(item => item.coverageCode === covId);

  //       if (!tmpCovFound) {
  //         const tmpCoverageParsedData = new CoverageForVehicle();
  //         tmpCoverageParsedData.coverageCode = covId;
  //         tmpCoverageParsedData.vehicleResourceId = this.quoteId;
  //         missingCoverages.push(tmpCoverageParsedData);
  //       }
  //     });

  //     allParsedCoverages = allParsedCoverages.concat(missingCoverages);

  //     //-------

  //     // // If the field 'All Perils' is not set, the coverage is not returned from API
  //     // // so we need to add it to the array, to be able to validate field (set as required in H&W panel)
  //     // const allPerilsExists = allParsedCoverages.find(item => item.coverageCode === 'APDED');

  //     // if (!allPerilsExists) {
  //     //   const tmpCoverageParsedData = new CoverageForVehicle();
  //     //   tmpCoverageParsedData.coverageCode = 'APDED';
  //     //   tmpCoverageParsedData.vehicleResourceId = this.quoteId;
  //     //   allParsedCoverages.push(tmpCoverageParsedData);
  //     // }
  //     // // ------------------------------------------------------------------------

  //     // Watch Data
  //     this.hintsAndWarningsService.clearWarningsGroup(uqBasicsCoveragesStandardGroupId);
  //     if (allParsedCoverages && allParsedCoverages.length) {
  //       const additionalData: AdditionalDataHomeBasicsCoverages = {
  //         quoteSelectedPlans: this.selectedQuotePlansList,
  //         quoteSelectedPlansIds: this.selectedQuotePlansListIds,
  //         observedData: allParsedCoverages,
  //         quote: this.quote
  //       }

  //       allParsedCoverages.forEach(parsedCoverage => {
  //         this.hintsAndWarningsService.checkObjectDataForHintsAndWarningsByDeepId(
  //           definition,
  //           parsedCoverage,
  //           uqBasicsCoveragesStandardGroupId,
  //           additionalData
  //           //this.quote
  //         );
  //       });
  //     }
  //   });
  // }

  // Basics Tab
  private subscribeBasicsCoveragesStandard(definition = WARNINGS_DEFINITIONS_HOME_BASICS, quoteLob = 'HOME'): void {
    const uqBasicsCoveragesStandardGroupId = 'HomeBasicsCoveragesStandard';

    // Helper - get proper data to observe
    let allParsedCoverages: CoverageForVehicle[] = [];
    const _allParsedCoverages: BehaviorSubject<CoverageForVehicle[]> = new BehaviorSubject(allParsedCoverages);

    // getHomeQuoteCoveragesItems
    // this.subscriptionBasicsCoveragesStandard = this.storageService.getStorageData('homeQuoteCoverages').subscribe(data => {
    const subscription: ISubscription = this.storageService.getStorageData('homeQuoteCoverages').subscribe(data => {

      if (data && data['coverages']) {
        const newData = new CoverageApiResponseData();
        newData.items = [JSON.parse(JSON.stringify(data))];
        data = newData;
      }
      allParsedCoverages = this.coveragesService.convertCoverageApiResponseDataArrayToCoverageParsedDataArray([data]);

      // Add all missing and required coverages - If the field  is not set, the coverage is not returned from API
      // so we need to add it to the array, to be able to validate field (set as required in H&W panel)
      const requiredForHome: string[] = ['APDED', 'DWELL', 'OS', 'PP', 'LOU', 'PL', 'MEDPM', 'RCDWELL', 'WHDED', 'NONSMOKE']; // 'RCCONT',
      const requiredForDwelling: string[] = ['DWELL', 'OS', 'PP', 'PL', 'MEDPM', 'BSC_LOSSFREEYRS', 'FRV', 'WHDED', 'APDED'];

      let requiredCoveragesToCheck: string[] = [];

      if (quoteLob === 'HOME') {
        requiredCoveragesToCheck = requiredForHome;
      } else if (quoteLob === 'DFIRE') {
        requiredCoveragesToCheck = requiredForDwelling;
      }

      const missingCoverages: CoverageForVehicle[] = [];
      requiredCoveragesToCheck.forEach((covId: string) => {
        const tmpCovFound = allParsedCoverages.find(item => item.coverageCode === covId);

        if (!tmpCovFound) {
          const tmpCoverageParsedData = new CoverageForVehicle();
          tmpCoverageParsedData.coverageCode = covId;
          tmpCoverageParsedData.vehicleResourceId = this.quoteId;
          missingCoverages.push(tmpCoverageParsedData);
        }
      });

      allParsedCoverages = allParsedCoverages.concat(missingCoverages);
      _allParsedCoverages.next(allParsedCoverages);
    });

    this._componentLifeCycleHook.asObservable().subscribe(res => {
      if (res === 'ngOnDestroy') {
        subscription && subscription.unsubscribe();
      }
    });

    this.subscriptionBasicsCoveragesStandardArr = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      definition,
      _allParsedCoverages.asObservable(),
      uqBasicsCoveragesStandardGroupId,
      this.additionalDataHomeBasicsCoverages$
    );
  }


  // Underwriting tab
  private subscribeHomeLossHistory(): void {
    const uqHomeLossHistoryGroupId = 'HomeLossHistory';

    this.subscriptionHomeLossHistory = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_HOME_LOSS_HISTORY,
      this.storageService.getStorageData('dwellingLossHistory'),
      uqHomeLossHistoryGroupId,
      this.getAdditionalData$
    );
  }

  private subscribeHomeLossHistoryItems(): void {
    const uqHomeLossHistoryGroupId = 'HomeLossHistoryItems';

    this.subscriptionHomeLossHistoryItems = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_HOME_LOSS_HISTORY_ITEMS,
      this.storageService.getStorageData('dwellingLossHistory'),
      uqHomeLossHistoryGroupId,
      this.storageService.getStorageData('dwellingLossHistoryRequirementsNew')
    );
  }

  private subscribeHomeDwellingProtectionClass(): void {
    const uqGroupId = 'HomeDwellingProtectionClass';

    this.subscriptionHomeDwellingProtectionClass = this.hintsAndWarningsService.watchObjectDataForHintsAndWarnings(
      WARNINGS_DEFINITIONS_HOME_DWELLINGS_PROTECTION_CLASS,
      this.storageService.getStorageData('dwellingProtectionClass'),
      uqGroupId
    );
  }

  // Options Tab - Carrier Options
  private subscribeHomeCarrierOptions(definitions = WARNINGS_DEFINITIONS_HOME_OPTIONS_CARRIER_OPTIONS): void {
    const uqHomeCarrierOptionsGroupId = 'HomeCarrierOptions';
    this.subscriptionHomeCarrierOptions = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      definitions,
      this.storageService.getStorageData('homeCarrierOptionsParsed'),
      uqHomeCarrierOptionsGroupId,
      this.storageService.getStorageData('homeGeneralOptionsParsed')
    );
  }

 // Options Tab - Carrier Options
 private subscribeHomeCarrierOptionsWithLoss(definitions = WARNINGS_DEFINITIONS_HOME_OPTIONS_CARRIER_OPTIONS_LOSS): void {


  const uqHomeCarrierOptionsGroupId = 'HomeCarrierOptionsLoss';
  this.subscriptionHomeCarrierOptions = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
    definitions,
    this.storageService.getStorageData('homeCarrierOptionsParsed'),
    uqHomeCarrierOptionsGroupId,
    this.storageService.getStorageData('dwellingLossHistory')
  );
}

  // Options Tab - General Options
  private subscribeHomeGeneralOptions(definitions = WARNINGS_DEFINITIONS_HOME_OPTIONS_GENERAL_OPTIONS): void {
    const uqHomeGeneralOptionsGroupId = 'HomeGeneralOptions';

    this.subscriptionHomeGeneralOptions = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      definitions,
      this.storageService.getStorageData('homeGeneralOptionsParsed'),
      uqHomeGeneralOptionsGroupId,
      this.storageService.getStorageData('homeCarrierOptionsParsed')
    );
  }

    private subscribeDwellingGeneralOptions(definitions = WARNINGS_DEFINITIONS_DWELLING_OPTIONS_GENERAL_OPTIONS): void {
    const uqHomeGeneralOptionsGroupId = 'HomeGeneralOptions';

    this.subscriptionHomeGeneralOptions = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      definitions,
      this.storageService.getStorageData('dwellingGeneralOptionsParsed'),
      uqHomeGeneralOptionsGroupId,
      this.storageService.getStorageData('homeCarrierOptionsParsed')
    );
  }

  // Schedules Tab
  private subscribeDwellingScheduledProperty(): void {
    const uqDwellingScheduledPropertyId = 'HomeDwellingScheduledProperty';

    this.subscriptionDwellingScheduledProperty = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_HOME_AGREED_VALUE_SUPPLEMENT,
      this.storageService.getStorageData('dwellingScheduledProperty'),
      uqDwellingScheduledPropertyId,
      this.additionalDataHomeScheduledProperty$
    );
  }
  private getAllDataAuto() {
    this.premiumsService.loadingDataInProgress = true;

    this.subscriptionGetAllDataAutoVehicleList && this.subscriptionGetAllDataAutoVehicleList.unsubscribe();

    const tmpVehicles = undefined;  // tmpVehicles is never set
    this.subscriptionGetAllDataAutoVehicleList = this.storageService.getStorageData('vehiclesList').subscribe(vehicles => {
      if (tmpVehicles !== JSON.stringify(vehicles)) {
        this.vehicles = vehicles;
        this.fetchAutoData()
          .then(() => {
            this.premiumsService.loadingDataInProgress = false;
            this.premiumsService.rerateGeneral(window.location.href);
          })
          .catch(err => console.log(err));
      }
    });

    this._componentLifeCycleHook.asObservable().subscribe(res => {
      if (res === 'ngOnDestroy') {
        this.subscriptionGetAllDataAutoVehicleList && this.subscriptionGetAllDataAutoVehicleList.unsubscribe();
      }
    });
  }

  private fetchAutoData(): Promise<any> {
    return Promise.all([
      this.getVehiclesOptionsModel(this.vehicles),
      this.getLocationsInformationForVehicles(this.vehicles)
    ]);
  }

  private getVehiclesOptionsModel(vehicles: Vehicle[]): Promise<any> {
    if (vehicles && vehicles.length) {
      const promises = [];

      vehicles.forEach((vehicle: Vehicle) => {
        const tmpPromise = this.getSingleVehicleOptionsModel(vehicle);
        promises.push(tmpPromise);
      });

      return Promise.all(promises).then((values: VehicleOptionsForVehicle[]) => {
        this.storageService.setStorageData('vehiclesOptionsModel', values);
      });

    } else {
      return Promise.resolve();
    }
  }

  private getSingleVehicleOptionsModel(vehicle: Vehicle): Promise<VehicleOptionsForVehicle> {
    const vehicleOptions = new VehicleOptionsForVehicle();
    vehicleOptions.vehicleResourceId = vehicle.resourceId;

    this.getSingleVehicleOptionsModelSubscriptions[vehicle.resourceId] && this.getSingleVehicleOptionsModelSubscriptions[vehicle.resourceId].unsubscribe();

    return new Promise((resolve, reject) => {
      if (vehicle && vehicle.vehicleType && vehicle.vehicleType === 'Trailer') {
        vehicleOptions.options = [];
        resolve(vehicleOptions);
      } else if (!vehicle.resourceId || !vehicle.year || !vehicle.make) {
        // reject('Not year or make defined for the vehicle');
        vehicleOptions.options = [];
        resolve(vehicleOptions);
      } else {
        this.getSingleVehicleOptionsModelSubscriptions[vehicle.resourceId] = this.lookupsService.getVehicleModelsAsOptions(vehicle.year, vehicle.make).pipe(
          first())
          .subscribe(
            (options: FilterOption[]) => {
              vehicleOptions.options = options;
              resolve(vehicleOptions);
            },
            err => {
              vehicleOptions.options = [];
              resolve(vehicleOptions);
            }
          );
      }
    });
  }

  /**
   * Get Locations For Vehicles
   */
  private getLocationsInformationForVehicles(vehicles: Vehicle[]): Promise<any> {
    if (vehicles && vehicles.length) {
      const promises = [];

      vehicles.forEach((vehicle: Vehicle) => {
        const tmpPromise = this.getLocationInformationForSingleVehicle(vehicle);
        promises.push(tmpPromise);
      });

      return Promise.all(promises).then((values: VehicleLocationDataForVehicle[]) => {
        this.storageService.setStorageData('vehiclesLocationsForVehicles', values);
      });
    } else {
      return Promise.resolve();
    }
  }
  private getLocationInformationForSingleVehicle(vehicle: Vehicle): Promise<VehicleLocationDataForVehicle> {
    const tmpLocation: VehicleLocationDataForVehicle = new VehicleLocationDataForVehicle();
    tmpLocation.vehicleResourceId = vehicle.resourceId;
    tmpLocation.vehicleQuoteSessionId = vehicle.quoteSessionId;
    tmpLocation.vehicleMeta = Object.assign({}, vehicle.meta);
    tmpLocation.vehicleYear = vehicle.year;
    tmpLocation.vehicleMake = vehicle.make;
    tmpLocation.vehicleModel = vehicle.model;

    this.getLocationInformationForSingleVehicleSubscriptions[vehicle.resourceId] && this.getLocationInformationForSingleVehicleSubscriptions[vehicle.resourceId].unsubscribe();

    return new Promise((resolve, reject) => {
      if (vehicle.garagingAddress != null && vehicle.garagingAddress.meta.href) {
        this.getLocationInformationForSingleVehicleSubscriptions[vehicle.resourceId] = this.locationsService.getLocationByUri(vehicle.garagingAddress.meta.href).pipe(
          take(1))
          .subscribe((res: LocationData) => {
            tmpLocation.location = res;
            resolve(tmpLocation);
          });
      } else {
        // Create Fake Location data with empty properties
        tmpLocation.location = new LocationData();
        tmpLocation.location.resourceName = 'Location_Fake';
        resolve(tmpLocation);
      }
    });
  }


  // Subscriptions to update additional datas
  // ----------------------------------------------------------------------------
  private subscribeSelectedQuote(): void {
    this.subscriptionQuote = this.storageService.getStorageData('selectedQuote').subscribe((quote: Quote) => {
      // Auto ClientInfo
      this.additionalDataAutoClientInfo.quote = JSON.parse(JSON.stringify(quote));
      this._additionalDataAutoClientInfo.next(this.additionalDataAutoClientInfo);

      // Auto Quote Plans
      this.additionalDataQuotePlans.quoteId = quote.resourceId;
      this._additionalDataQuotePlans.next(this.additionalDataQuotePlans);

      // Home Dwelling
      this.additionalDataHomeDwelling.quote = quote;
      this._additionalDataHomeDwelling.next(this.additionalDataHomeDwelling);

      // Home ClientInfo
      this.additionalDataHomeClientInfo.quote = JSON.parse(JSON.stringify(quote));
      this._additionalDataHomeClientInfo.next(this.additionalDataHomeClientInfo);

      // Home Schedules Info
      this.additionalDataHomeScheduledProperty.quote = JSON.parse(JSON.stringify(quote));
      this._additionalDataHomeScheduledProperty.next(this.additionalDataHomeScheduledProperty);

      // Umbrella ClientInfo
      this.additionalDataUmbrellaClientInfo.quote = JSON.parse(JSON.stringify(quote));
      this._additionalDataUmbrellaClientInfo.next(this.additionalDataUmbrellaClientInfo);

      // Home basics
      this.additionalDataHomeBasicsCoverages.quote = JSON.parse(JSON.stringify(quote));
      this._additionalDataHomeBasicsCoverages.next(this.additionalDataHomeBasicsCoverages);

      this.storageService.getStorageData('dwelling').subscribe(x => {
        this.additionalDataHomeBasicsCoverages.moreData = x.nonOwnerOccupancyInd;
        this._additionalDataHomeBasicsCoverages.next(this.additionalDataHomeBasicsCoverages);
      });

      this.dwellingOptionValidation.onChange().subscribe(() => {
        this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
          WARNINGS_DEFINITIONS_HOME_OPTIONS_CARRIER_OPTIONS,
          this.dwellingOptionValidation.onChange(),
          'HomeCarrierOptions',
          of([])
        );
      });
    });
  }

  private subscribeSelectedQuotePlans(): void {
    this.subscriptionQuotePlans = this.storageService.getStorageData('selectedPlan').subscribe(plans => {
      if (plans && plans.items && plans.items.length) {
        this.selectedQuotePlansList = plans.items[0].items;
        const quoteSelectedPlansIds = this.selectedQuotePlansList.map(plan => plan.ratingPlanId);
        this.selectedQuotePlansListIds = quoteSelectedPlansIds;

        this.additionalData.quoteSelectedPlans = this.selectedQuotePlansList;
        this.additionalData.quoteSelectedPlansIds = quoteSelectedPlansIds;
        // The Client Addresses and Contact Methods needs to be checked after plans change
        this._additionalData.next(this.additionalData);

        // Quote Info
        this.additionalDataQuoteInfo.quoteSelectedPlans = this.selectedQuotePlansList;
        this.additionalDataQuoteInfo.quoteSelectedPlansIds = quoteSelectedPlansIds;
        this._additionalDataQuoteInfo.next(this.additionalDataQuoteInfo);

        // Auto Drivers
        this.additionalDataAutoDrivers.quoteSelectedPlans = this.selectedQuotePlansList;
        this.additionalDataAutoDrivers.quoteSelectedPlansIds = quoteSelectedPlansIds;
        this._additionalDataAutoDrivers.next(this.additionalDataAutoDrivers);

        // Auto Quote Plans
        this.additionalDataQuotePlans.quoteSelectedPlans = this.selectedQuotePlansList;
        this.additionalDataQuotePlans.quoteSelectedPlansIds = quoteSelectedPlansIds;
        this._additionalDataQuotePlans.next(this.additionalDataQuotePlans);

        // Auto Vehicle
        this.additionalDataAutoVehicles.quoteSelectedPlans = this.selectedQuotePlansList;
        this.additionalDataAutoVehicles.quoteSelectedPlansIds = quoteSelectedPlansIds;
        this._additionalDataAutoVehicles.next(this.additionalDataAutoVehicles);

        // Auto ClientInfo
        this.additionalDataAutoClientInfo.quoteSelectedPlans = this.selectedQuotePlansList;
        this.additionalDataAutoClientInfo.quoteSelectedPlansIds = quoteSelectedPlansIds;
        this._additionalDataAutoClientInfo.next(this.additionalDataAutoClientInfo);

        // Home ClientInfo
        this.additionalDataHomeClientInfo.quoteSelectedPlans = this.selectedQuotePlansList;
        this.additionalDataHomeClientInfo.quoteSelectedPlansIds = quoteSelectedPlansIds;
        this._additionalDataHomeClientInfo.next(this.additionalDataHomeClientInfo);

        // Home Dwelling
        this.additionalDataHomeDwelling.quoteSelectedPlans = this.selectedQuotePlansList;
        this.additionalDataHomeDwelling.quoteSelectedPlansIds = quoteSelectedPlansIds;
        this._additionalDataHomeDwelling.next(this.additionalDataHomeDwelling);

        // Umbrella ClientInfo
        this.additionalDataUmbrellaClientInfo.quoteSelectedPlans = this.selectedQuotePlansList;
        this.additionalDataUmbrellaClientInfo.quoteSelectedPlansIds = quoteSelectedPlansIds;
        this._additionalDataUmbrellaClientInfo.next(this.additionalDataUmbrellaClientInfo);

        // Home Dwelling Basics
        this.additionalDataHomeBasicsCoverages.quoteSelectedPlans = this.selectedQuotePlansList;
        this.additionalDataHomeBasicsCoverages.quoteSelectedPlansIds = quoteSelectedPlansIds;
        this._additionalDataHomeBasicsCoverages.next(this.additionalDataHomeBasicsCoverages);

        // Home Scheduled Property
        this.additionalDataHomeScheduledProperty.quoteSelectedPlans = this.selectedQuotePlansList;
        this.additionalDataHomeScheduledProperty.quoteSelectedPlansIds = quoteSelectedPlansIds;
        this.additionalDataHomeScheduledProperty.availablePlans = this.storageGlobalService.takeSubs('plans');
        this._additionalDataHomeScheduledProperty.next(this.additionalDataHomeScheduledProperty);
      }
    });
  }

  private subscribeLobTownsList(): void {
    this.subscriptionLobTownsList = this.storageService.getStorageData('lobTowns')
      .subscribe((data: Town[]) => {

        this.additionalData.specLobTowns = data;
        this._additionalData.next(this.additionalData);

        // Quote Info
        this.additionalDataQuoteInfo.specLobTowns = data;
        this._additionalDataQuoteInfo.next(this.additionalDataQuoteInfo);

        // Auto Drivers
        this.additionalDataAutoDrivers.specLobTowns = data;
        this._additionalDataAutoDrivers.next(this.additionalDataAutoDrivers);

        // Auto Quote Plans
        this.additionalDataQuotePlans.specLobTowns = data;
        this._additionalDataQuotePlans.next(this.additionalDataQuotePlans);

        // Auto Vehicle
        this.additionalDataAutoVehicles.specLobTowns = data;
        this._additionalDataAutoVehicles.next(this.additionalDataAutoVehicles);

        // Auto ClientInfo
        this.additionalDataAutoClientInfo.specLobTowns = data;
        this._additionalDataAutoClientInfo.next(this.additionalDataAutoClientInfo);

        // Home ClientInfo
        this.additionalDataHomeClientInfo.specLobTowns = data;
        this._additionalDataHomeClientInfo.next(this.additionalDataHomeClientInfo);

        // Home Dwelling
        this.additionalDataHomeDwelling.specLobTowns = data;
        this._additionalDataHomeDwelling.next(this.additionalDataHomeDwelling);

        // Umbrella ClientInfo
        this.additionalDataUmbrellaClientInfo.specLobTowns = data;
        this._additionalDataUmbrellaClientInfo.next(this.additionalDataUmbrellaClientInfo);

        // Home Dwelling Basics
        this.additionalDataHomeBasicsCoverages.specLobTowns = data;
        this._additionalDataHomeBasicsCoverages.next(this.additionalDataHomeBasicsCoverages);
      });
  }

  private subscribeCommonAutoPolicyHistory(): void {
    this.subscriptionCommonAutoPolicyHistory = this.storageService.getStorageData('policyHistory')
      .subscribe((res: QuotePolicyHistory) => {
        this.additionalDataQuoteInfo.policyHistory = res;
        this._additionalDataQuoteInfo.next(this.additionalDataQuoteInfo);
      });
  }

  private subscribeCommonAutoCarrierOptions(): void {
    this.subscriptionCommonAutoCarrierOptions = this.storageService.getStorageData('autoPolicyOptions')
      .subscribe((res: CoverageItemParsed[]) => {
        if (res) {
          this.additionalDataAutoClientInfo.carrierOptionsParsed = res;
          this._additionalDataAutoClientInfo.next(this.additionalDataAutoClientInfo);
        }
      });
  }

  private subscribeCommonAutoDrivers(): void {
    this.subscriptionCommonAutoDrivers = this.storageService.getStorageData('driversList')
      .subscribe((res: Driver[]) => {
        this.additionalDataAutoVehicles.drivers = res;
        this._additionalDataAutoVehicles.next(this.additionalDataAutoVehicles);
      });
  }

  private subscribeCommonAutoVehicles(): void {
    this.subscriptionCommonAutoVehicles = this.storageService.getStorageData('vehiclesList')
      .subscribe((res: Vehicle[]) => {
        this.additionalDataAutoDrivers.vehicles = res;
        this._additionalDataAutoDrivers.next(this.additionalDataAutoDrivers);

        this.additionalDataQuotePlans.vehicles = res;
        this._additionalDataQuotePlans.next(this.additionalDataQuotePlans);
      });
  }

  // COMMERCIAL AUTO
  // ---------------------------------------------------------------------------
  private subscribeCommercialAutoDrivers(): void {
    this.subscriptionCommonAutoDrivers = this.storageService.getStorageData('comDriverList')
      .subscribe((res: ComDriver[]) => {
        // this.additionalDataCommAutoVehicles.drivers = res;
      });
  }

  private subscribeCommercialAutoVehicles(): void {
    this.subscriptionCommonAutoVehicles = this.storageService.getStorageData('comVehicles')
      .subscribe((res: Vehicle[]) => {
        this.additionalDataAutoDrivers.vehicles = res;
        this._additionalDataAutoDrivers.next(this.additionalDataAutoDrivers);

        this.additionalDataQuotePlans.vehicles = res;
        this._additionalDataQuotePlans.next(this.additionalDataQuotePlans);
      });
  }

  // HOME
  // ----------------------------------------------------------------------------
  private subscribeSelectedQuoteFormTypes(): void {
    this.subscriptionQuoteFormTypes = this.storageService.getStorageData('selectedQuoteFormTypes').subscribe((formTypes: string[]) => {
      this.additionalDataHomeDwelling.quoteFormTypes = formTypes;
      this._additionalDataHomeDwelling.next(this.additionalDataHomeDwelling);

      this.additionalDataHomeClientInfo.quoteFormTypes = formTypes;
      this._additionalDataHomeClientInfo.next(this.additionalDataHomeClientInfo);
    });
  }

  private subscribeCommonHomeCarrierOptions(): void {
    this.subscriptionCommonHomeCarrierOptions = this.storageService.getStorageData('homeCarrierOptionsParsed')
      .subscribe((res: CoverageItemParsed[]) => {
        // if (res) {
        //   // this.additionalDataAutoClientInfo.carrierOptionsParsed = res;
        //   // this._additionalDataAutoClientInfo.next(this.additionalDataAutoClientInfo);
        // }

        //
        this.additionalDataHomeDwelling.quoteCarrierOptions = res;
        this._additionalDataHomeDwelling.next(this.additionalDataHomeDwelling);

        //
        this.additionalDataHomeClientInfo.carrierOptionsParsed = res;
        this._additionalDataHomeClientInfo.next(this.additionalDataHomeClientInfo);
      });
  }


  // Home data fetching
  // ----------------------------------------------------------------------------

  // Seems it's not in use
  private getHomeUnderwritingData(): Promise<any> {
    return new Promise((resolve, reject) => {
      this.dwellingService.getLossHistory(this.quoteId).pipe(first()).subscribe(response => {
        if (response && response.items && response.items[0]) {

          response.items[0] = this.dwellingService.helpUpdateLossHistoryForHintsAndWarnings(response.items[0], this.quote);

            this.storageService.setStorageData('dwellingLossHistory', response.items[0]);
            resolve(response.items[0]);
        }
      }, err => reject(err));
    });
  }

  private initHomeHintAndWarnings(): void {
    this.subscribeClientInfoForHOME();
    this.subscribeClientInfoAddressesForHOME();
    this.subscribeClientInfoContactMethodsForHOME();

    this.subscribeDwellingLocation();
    this.subscribeDwellingFireLocation();
    this.subscribeDwelling();
    this.subscribeDwellingSubsystems();
    this.subscribeHomeDwellingProtectionClass();
    // this.subscribeBasicsCoveragesStandard();
    this.subscribeBasicsCoveragesStandard();
    this.subscribeHomeGeneralOptions();
    this.subscribeHomeCarrierOptions();
    this.subscribeHomeCarrierOptionsWithLoss();
    this.subscribeHomeLossHistory();
    this.subscribeHomeLossHistoryItems();
    this.subscribeDwellingScheduledProperty();
  }

  private destructorHomeHintAndWarnings(): void {
    this.subscriptionInfoClient && this.subscriptionInfoClient.unsubscribe();
    this.unsubscribeAllSubscriptionsInArray(this.subscriptionInfoClientAddressesArray);
    this.unsubscribeAllSubscriptionsInArray(this.subscriptionInfoContactMethodsArray);

    this.unsubscribeAllSubscriptionsInArray(this.subscriptionDwellingArr);
    this.unsubscribeAllSubscriptionsInArray(this.subscriptionDwellingSubsystemsArr);
    this.unsubscribeAllSubscriptionsInArray(this.subscriptionHomeLossHistory);
    this.unsubscribeAllSubscriptionsInArray(this.subscriptionHomeLossHistoryItems);
    this.subscriptionDwellingLocation && this.subscriptionDwellingLocation.unsubscribe();
    this.unsubscribeAllSubscriptionsInArray(this.subscriptionDwellingFireLocation);
    // this.subscriptionBasicsCoveragesStandard && this.subscriptionBasicsCoveragesStandard.unsubscribe();
    this.unsubscribeAllSubscriptionsInArray(this.subscriptionBasicsCoveragesStandardArr);
    // this.subscriptionHomeGeneralOptions && this.subscriptionHomeGeneralOptions.unsubscribe();
    this.unsubscribeAllSubscriptionsInArray(this.subscriptionDwellingScheduledProperty);
    // this.subscriptionHomeCarrierOptions && this.subscriptionHomeCarrierOptions.unsubscribe();
    this.subscriptionHomeQuoteCoverages && this.subscriptionHomeQuoteCoverages.unsubscribe();
    this.subscriptionHomeDwellingProtectionClass && this.subscriptionHomeDwellingProtectionClass.unsubscribe();
  }

  // Dwelling data fetching

  private subscribeDwellingDwelling(): void {
    const uqDwellingGroupId = 'DwellingDwelling';

    // It needs to be validate also after Quote FormPlans changed
    this.subscriptionDwellingArr = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_DWELLING_DWELLING,
      this.storageService.getStorageData('dwelling'),
      uqDwellingGroupId,
      this.additionalDataHomeDwelling$
    );
  }

  private subscribeDwellingDwellingLocation(): void {
    const uqDwellingLocationGroupId = 'DwellingDwellingLocation';

    this.subscriptionDwellingLocation = this.hintsAndWarningsService.watchObjectDataForHintsAndWarnings(
      WARNINGS_DEFINITIONS_DWELLING_DWELLING_LOCATION,
      this.storageService.getStorageData('dwellingLocation'),
      uqDwellingLocationGroupId
    );
  }

  private subscribeDwellingDwellingFireLocation(): void {
    const uqDwellingLocationGroupId = 'DwellingDwellingFireLocation';

    this.subscriptionDwellingFireLocation = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_DWELLING_DWELLING_FIRE_LOCATION,
      this.storageService.getStorageData('dwellingLocation'),
      uqDwellingLocationGroupId,
      this.additionalDataHomeDwelling$
    );
  }

  private getAllDataDwelling(): Promise<any> {
    return Promise.all([
      this.getHomeUnderwritingData()
    ]);
  }

  private initDwellingHintAndWarnings(): void {
    this.subscribeClientInfoForDWELLING();
    // this.subscribeClientInfoAddressesForDWELLING();
    this.subscribeClientInfoContactMethodsForDWELLING();
    this.subscribeDwellingDwellingLocation();
    this.subscribeDwellingDwellingFireLocation();
    this.subscribeDwellingDwelling();
    this.subscribeBasicsCoveragesStandard(WARNINGS_DEFINITIONS_DWELLING_BASICS, 'DFIRE');
    this.subscribeDwellingGeneralOptions(WARNINGS_DEFINITIONS_DWELLING_OPTIONS_GENERAL_OPTIONS);
    this.subscribeHomeCarrierOptions(WARNINGS_DEFINITIONS_DWELLING_OPTIONS_CARRIER_OPTIONS);
  }

  private destructorDwellingHintAndWarnings(): void {
    this.subscriptionInfoClient && this.subscriptionInfoClient.unsubscribe();
    this.unsubscribeAllSubscriptionsInArray(this.subscriptionInfoClientAddressesArray);
    this.unsubscribeAllSubscriptionsInArray(this.subscriptionInfoContactMethodsArray);

    this.unsubscribeAllSubscriptionsInArray(this.subscriptionDwellingArr);
    this.unsubscribeAllSubscriptionsInArray(this.subscriptionDwellingSubsystemsArr);
    this.subscriptionDwellingLocation && this.subscriptionDwellingLocation.unsubscribe();
    this.unsubscribeAllSubscriptionsInArray(this.subscriptionBasicsCoveragesStandardArr);
   // this.subscriptionHomeGeneralOptions && this.subscriptionHomeGeneralOptions.unsubscribe();
    // this.subscriptionHomeCarrierOptions && this.subscriptionHomeCarrierOptions.unsubscribe();
    this.subscriptionHomeQuoteCoverages && this.subscriptionHomeQuoteCoverages.unsubscribe();
  }

  // CLIENT
  private subscribeClientInfoForDWELLING(): void {
    const uqInfoClientId = 'ClientInfo';

    this.subscriptionInfoClient = this.hintsAndWarningsService.watchObjectDataForHintsAndWarnings(
      WARNINGS_DEFINITIONS_INFO_CLIENT_FOR_DWELLING,
      this.storageService.getStorageData('clients'),
      uqInfoClientId,
      this.additionalData
    );
  }

  // private subscribeClientInfoAddressesForDWELLING(): void {
  //   const uqInfoClientAddrId = 'ClientInfoAddr';

  //   // It needs to be validate also after Selected Plans change
  //   this.subscriptionInfoClientAddressesArray = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
  //     WARNINGS_DEFINITIONS_INFO_CLIENT_ADDRESSES_FOR_DWELLING,
  //     this.storageService.getStorageData('clientAddresses'),
  //     uqInfoClientAddrId,
  //     this.additionalDataAutoClientInfo$
  //   );
  // }

  private subscribeClientInfoContactMethodsForDWELLING(): void {
    const uqInfoClientContactMethodsId = 'ClientInfoContactMethods';

    // It needs to be validate also after Selected Plans change
    this.subscriptionInfoContactMethodsArray = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_INFO_CLIENT_CONTACT_METHODS_FOR_DWELLING,
      this.storageService.getStorageData('clientContactMethods'),
      uqInfoClientContactMethodsId,
      this.additionalDataHomeDwelling$
    );
  }

  // UMBRELLA
  // ----------------------------------------------------------------------------
  private initUmbrellaHintAndWarnings(): void {
    this.subscribeClientInfoForUMBRELLA();
    this.subscribeClientInfoAddressesForUMBRELLA();
    this.subscribeClientInfoContactMethodsForUMBRELLA();
    this.subscribeBasicPolicyInfo();
    this.subscribeUmbrellaGeneralOptions();
    this.subscribeUmbrellaCarrierOptions();
  }

  private destructorUmbrellaHintAndWarnings(): void {
    this.subscriptionInfoClient && this.subscriptionInfoClient.unsubscribe();
    this.unsubscribeAllSubscriptionsInArray(this.subscriptionInfoClientAddressesArray);
    this.unsubscribeAllSubscriptionsInArray(this.subscriptionInfoContactMethodsArray);
    this.subscriptionUmbrellaBasicPolicyInfo && this.subscriptionUmbrellaBasicPolicyInfo.unsubscribe();
    this.subscriptionUmbrellaGeneralOptions && this.subscriptionUmbrellaGeneralOptions.unsubscribe();
    this.subscriptionUmbrellaCarrierOptions && this.subscriptionUmbrellaCarrierOptions.unsubscribe();
  }

  // CLIENT
  private subscribeClientInfoForUMBRELLA(): void {
    const uqInfoClientId = 'ClientInfo';

    this.subscriptionInfoClient = this.hintsAndWarningsService.watchObjectDataForHintsAndWarnings(
      WARNINGS_DEFINITIONS_INFO_CLIENT_FOR_UMBRELLA,
      this.storageService.getStorageData('clients'),
      uqInfoClientId,
      this.additionalData
    );
  }

  private subscribeClientInfoAddressesForUMBRELLA(): void {
    const uqInfoClientAddrId = 'ClientInfoAddr';

    // It needs to be validate also after Selected Plans change
    this.subscriptionInfoClientAddressesArray = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_INFO_CLIENT_ADDRESSES_FOR_UMBRELLA,
      this.storageService.getStorageData('clientAddresses'),
      uqInfoClientAddrId,
      this.additionalDataUmbrellaClientInfo$
    );
  }

  private subscribeClientInfoContactMethodsForUMBRELLA(): void {
    const uqInfoClientContactMethodsId = 'ClientInfoContactMethods';

    // It needs to be validate also after Selected Plans change
    this.subscriptionInfoContactMethodsArray = this.hintsAndWarningsService.watchObjectDataForHintsAndWarningsWithAdditionalData(
      WARNINGS_DEFINITIONS_INFO_CLIENT_CONTACT_METHODS_FOR_UMBRELLA,
      this.storageService.getStorageData('clientContactMethods'),
      uqInfoClientContactMethodsId,
      this.additionalDataUmbrellaClientInfo$
    );
  }

  // WARNINGS_DEFINITIONS_INFO_CLIENT_CONTACT_METHODS_FOR_UMBRELLA

  // Basics Tab - Primary Policy Information
  private subscribeBasicPolicyInfo(definitions = WARNINGS_DEFINITIONS_UMBRELLA_PRIMARY_POLICY_INFORMATION): void {

    const uqUmbrellaBasicPolicyInfoGroupId = 'UmbrellaBasicPolicyInfo';
    this.subscriptionUmbrellaBasicPolicyInfo = this.hintsAndWarningsService.watchObjectDataForHintsAndWarnings(
      definitions,
      this.storageService.getStorageData('basicPolicyInfo'),
      uqUmbrellaBasicPolicyInfoGroupId
    );
  }

  // Basics Tab - General Options
  private subscribeUmbrellaGeneralOptions(definitions = WARNINGS_DEFINITIONS_UMBRELLA_OPTIONS_GENERAL_OPTIONS): void {

    const uqUmbrellaGeneralOptionsGroupId = 'UmbrellaGeneralOptions';
    this.subscriptionUmbrellaGeneralOptions = this.hintsAndWarningsService.watchObjectDataForHintsAndWarnings(
      definitions,
      this.storageService.getStorageData('umbrellaGeneralOptionsParsed'),
      uqUmbrellaGeneralOptionsGroupId
    );
  }

  // Carrier Options Tab - Carrier Options
  private subscribeUmbrellaCarrierOptions(definitions = WARNINGS_DEFINITIONS_UMBRELLA_OPTIONS_CARRIER_OPTIONS): void {

    const uqUmbrellaCarrierOptionsGroupId = 'UmbrellaCarrierOptions';
    this.subscriptionUmbrellaCarrierOptions = this.hintsAndWarningsService.watchObjectDataForHintsAndWarnings(
      definitions,
      this.storageService.getStorageData('umbrellaCarrierOptionsParsed'),
      uqUmbrellaCarrierOptionsGroupId
    );
  }

  // Helpers
  // ----------------------------------------------------------------------------
  private unsubscribeAllSubscriptionsInArray(subscriptions: ISubscription[]): void {
    if (subscriptions && subscriptions.length) {
      subscriptions.forEach(sub => {
        sub && 'unsubscribe' in sub && sub.unsubscribe();
      });
    }
  }
}
