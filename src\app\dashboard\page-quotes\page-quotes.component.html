<app-lob-tabbed-nav [navigationList]="naviagationList" *ngIf="PersonalCommercialSeparate"></app-lob-tabbed-nav>
<section class="section">
  <app-search-by-name (filterByName)="filterByName($event)" (resetSearch)="resetSearch()" [lob]="lob"></app-search-by-name>
</section>
<section class="section" style="padding-top: 0;">
  <div class="row">
    <div class="col-xs-12">
      <app-filter *ngIf="filterLobOptions?.length" [id]="'filter_LOB'" [name]="'Line of Business'" [hasSearch]="false" [selectedOption]="filterLobSelectedOption"  [options]="filterLobOptions" [label]="filterLobLabel" (onChange)="onFilterDataChange($event)"></app-filter>

      <app-filter *ngIf="filterAgentsOptions?.length" [id]="'filter_agents'" [name]="'Agents'" [hasSearch]="true" [selectedOption]="filterAgentsSelectedOption" [options]="filterAgentsOptions" [label]="filterAgentsLabel" (onChange)="onFilterDataChange($event)"></app-filter>

      <app-filter #refFilterDates [id]="'filter_dates'" [name]="'Dates'" [label]="!refFilterDates.disabled ? filterDatesLabel : 'All'" [disabled]="searchQuery && searchQuery">
        <div class="box u-width-650px">
          <div class="row row-narrow">
            <div class="col-xs-3">
              <div class="u-padd--bottom-1">Date Type</div>

              <sm-autocomplete
                [options]="filterDatesTypesOptions"
                [activeOption]="filterDatesTypesSelectedOption"
                [searchFromBegining]="true"
                [disabled]="disableDateType()"
                (onSelect)="onDatesParamsChangeHandler($event, 'dateType')">
              </sm-autocomplete>

            </div>
            <div class="col-xs-3">
              <div class="u-padd--bottom-1">Date Range</div>

              <sm-autocomplete
                [options]="filterDatesRangeOptions"
                [activeOption]="filterDatesRangeSelectedOption"
                [searchFromBegining]="true"
                (onSelect)="onDatesParamsChangeHandler($event, 'dateRange')">
              </sm-autocomplete>

            </div>
            <div class="col-xs-3">
              <div class="u-padd--bottom-1">Starting</div>
              <app-datepicker-input #datepickerStartingView [id]="'dateStarting'" [dateRangeEnd]="dateRange.end" (onDateChange)="onDatesParamsChangeHandler($event, 'dateRangeStart')" [selectDate]="dateRange.start" [returnDateFormat]="'MMM d, yyyy'">
              </app-datepicker-input>
            </div>
            <div class="col-xs-3">
              <div class="u-padd--bottom-1">Ending</div>
              <app-datepicker-input #datepickerEndingView [id]="'dateEnding'" [dateRangeStart]="dateRange.start" class="'to-right'" (onDateChange)="onDatesParamsChangeHandler($event, 'dateRangeEnd')" [selectDate]="dateRange.end" [returnDateFormat]="'MMM d, yyyy'">
              </app-datepicker-input>
            </div>
          </div>
        </div>
        <button (click)="getQuotesFromFilteredDates()" name="searchbtn" class="o-btn u-float-right">Search</button>
      </app-filter>


      <app-filter *ngIf="filterLocationsOptions?.length" [selectedOption]="filterLocationsSelectedOption" [id]="'filter_locations'" [name]="'Locations'" [hasSearch]="true" [options]="filterLocationsOptions" (onChange)="onFilterDataChange($event)"></app-filter>
      <!-- a class="o-link u-color-pelorous u-float-right" id="modalFilterCustom">Custom Filter</a -->


    <div class="u-float-right" *ngIf="!isImported">
      <!-- div class="trash trash--select u-float-left"><span class="u-t-upper u-color-slate-grey" ><input type="checkbox" class="o-btn--checkbox u-show-inline" (click)="toggleAll()" [checked]="isChecked()"/>Select All</span></div -->
      <div class="trash trash--select u-float-left">
        <span class="u-t-upper u-color-slate-grey" >
          <label class="o-checkable">
            <input type="checkbox" class="o-btn--checkbox u-show-inline" (click)="toggleAll()" [checked]="isChecked()"/>
            <i class="o-btn o-btn--checkbox"></i>
            Select All
          </label>
        </span>
      </div>
      <div class="trash trash--img u-float-right"><a class="u-t-upper u-color-slate-grey" (click)="deleteQuotes()">Delete</a></div>
    </div>
    <app-delete-quote #deleteQuoteModal (onDeleteQuotesClick)="refreshPage($event)" *ngIf="!isImported"></app-delete-quote>
    <br>
    <label class="u-float-left u-padd--1">
      {{RecentDatesLabel}}
    </label>

  </div>
  </div>
</section>

<section class="section">

  <div class="row">
    <div class="col-xs-12">

      <table class="table table--compact table--fixed table--hoverable-grey">
        <thead class="table__thead">
          <tr class="">
            <th class="table__th u-width-50px">LOB</th>
            <th class="table__th u-width-150px">Name</th>
            <th class="table__th u-width-150px">City &amp; State</th>
            <th class="table__th u-width-120px">Effective Date</th>
            <th class="table__th u-width-110px">Saved</th>
            <th class="table__th u-width-115px">Agent</th>
            <th class="table__th">Notes</th>
            <th class="table__th" *ngIf="!isImported"></th>
          </tr>
        </thead>

        <tbody class="table__tbody" *ngIf="arrQuotesAll && !quotesLoading">
          <tr class="table__tr" *ngFor="let row of arrQuotesAll">
            <td class="table__td">
              <i class="o-icon o-icon--md o-icon--i-{{row.lob}} table__td-icon"></i>
            </td>
            <td class="table__td u-color-pelorous">
              <a [routerLink]="generateRouterLinkValue(row)">
                <span *ngIf="!row.client.firstName && !row.client.lastName && !row.client.name && !row.client.businessName">--</span>
                <span *ngIf="row.client.businessName">{{row.client.businessName}}</span>
                <span *ngIf="row.client.name">{{row.client.name}}</span>
                <span *ngIf="row.client.firstName">{{row.client.firstName}} </span>
                <span *ngIf="row.client.lastName">{{row.client.lastName}}</span>
              </a>
            </td>
            <td class="table__td">{{row?.client?.city}}<span *ngIf="row.client.city && row.client.state">, </span>{{row?.client?.state}}</td>
            <td class="table__td">{{parseLastModifiedDate(row.effectiveDate)}}</td>
            <td class="table__td">{{parseLastModifiedDate(row.lastModifiedDate)}}</td>
            <td class="table__td">{{row.agent}}</td>
            <td class="table__td">
              <div class="u-t-nowrap">{{row.description}}</div>
            </td>
            <td class="table__td" *ngIf="!isImported">
              <label class="o-checkable u-float-right">
                <input
                  type="checkbox"
                  class="o-btn--checkbox"
                  [checked]="isSelected(row.quoteIdentifier)"
                  (click)="toggleQuote(row.quoteIdentifier)"/>
                <i class="o-btn o-btn--checkbox"></i>
              </label>
            </td>
          </tr>
        </tbody>
        <tbody class="table__tbody" *ngIf="!arrQuotesAll?.length && !quotesLoading">
          <tr class="table__tr">
            <td [colSpan]="!arrQuotesAll?.length ? 7 : 6"><p class="u-padd--bottom-1 u-padd--1">There are no results that match your search.</p></td>
          </tr>
        </tbody>
        <tbody class="table__tbody" *ngIf="quotesLoading">
          <tr class="table__tr">
            <td [colSpan]="12">
    <div class="loader__icon"></div>

              <h5 style="text-align:center" class="u-padd--bottom-1 u-padd--1">Loading Recent Quotes...</h5></td>
          </tr>
        </tbody>
      </table>

    </div>
  </div>
</section>


<section class="section">
  <div class="u-flex u-flex--to-middle">
    <div class="" *ngIf="!RecentDatesLabel">
      <app-pagination [currentPage]="paginationCurrentPage" [totalRecords]="paginationResultsCount" [recordsLimit]="paginationResultLimit" (onPageChange)="paginationPageChange($event)"></app-pagination>
    </div>
    <label *ngIf="RecentDatesLabel" class="u-float-left u-padd--1 u-color-pelorous">
      1 - {{paginationResultsCount}} of {{paginationResultsCount}}
    </label>

    <div class="" style="padding-left: 10rem;" *ngIf="!RecentDatesLabel">
      <app-results-limiter (onChange)="onResultLimitChange($event)"></app-results-limiter>
    </div>
  </div>
</section>
