<section class="section section--compact u-spacing--2-5">
    <div class="row">
      <h1 class="o-heading" style="color: #0b71ac; padding-left: 20px;">Apply eStamp </h1>
    </div>
    <div class="row u-spacing--1">
      <div class="col-xs-12">
        <div class="box box--silver">
            <div class="row">
                <div class="col-md-2" [ngClass]="{'is-required-field': getReadyEligible() &&  estampInfo.writingCompanyName === ''}">
                    <label for="carrier">Date Type:</label>
                </div>
                <div class="col-md-3" [ngClass]="{'is-required-field': getReadyEligible() &&  estampInfo.writingCompanyName === ''}">
                    <sm-autocomplete [options]="dateTypes" [activeOption]="estampInfo.dateType" [(ngModel)]="estampInfo.dateType" name="dateType"></sm-autocomplete>
                </div>
            </div>
            <div class="row">
                <div class="col-md-2" [ngClass]="{'is-required-field': getReadyEligible() && estampInfo.effectiveDate === ''}">
                    <label for="effectiveDate" *ngIf="estampInfo.dateType=== 'EffectiveDate'">Effective Date:</label>
                    <label for="effectiveDate" *ngIf="estampInfo.dateType=== 'PolicyChangeDate'">Policy Change Date:</label>

                </div>
                <div class="col-md-3" [ngClass]="{'is-required-field': getReadyEligible() &&  estampInfo.effectiveDate === ''}">
                    <app-datepicker-input ngDefaultControl (onDateChange)="setDate($event)" [(ngModel)]="estampInfo.effectiveDate" name="effectiveDate" [placeholder]="'MM/dd/yyyy'" [returnDateFormat]="'yyyy-MM-dd'"></app-datepicker-input>
                 </div>
                 </div>
                 <div class="row">
                     <div class="col-md-2" [ngClass]="{'is-required-field': getReadyEligible() &&  estampInfo.writingCompanyName === ''}">
                         <label for="carrier">Carrier:</label>
                     </div>
                     <div class="col-md-3" [ngClass]="{'is-required-field': getReadyEligible() &&  estampInfo.writingCompanyName === ''}">
                         <sm-autocomplete [options]="carriers" [activeOption]="estampInfo.companyCode" [(ngModel)]="estampInfo.companyCode" name="carrier" (ngModelChange)="setCompanyName()"></sm-autocomplete>
                     </div>
                 </div>
                 <div class="row">
                     <div class="col-md-2" [ngClass]="{'is-required-field': getReadyEligible() &&  estampInfo.signedBy === ''}"><label for="signedBy">Signed By:</label></div>
                    <div class="col-md-3" [ngClass]="{'is-required-field': getReadyEligible() &&  estampInfo.signedBy === ''}"><input [(ngModel)]="estampInfo.signedBy"  name="signedBy"></div>
                    </div>
                 </div>
                 </div>
                 </div>
                 </section>
                 