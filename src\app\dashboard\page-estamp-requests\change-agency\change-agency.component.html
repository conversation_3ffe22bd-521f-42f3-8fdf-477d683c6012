<section class="section section--thick" #modal>
    <div class="row">
      <div class="col-xs-12">
        <div class="u-flex u-flex--to-middle">
          <app-search-by-name (filterByName)="filterByName($event)" [lob]="'Commercial'" (resetSearch)="resetSearch()">
    </app-search-by-name>
        </div>
      </div>
    </div>
    <span style="color: red" *ngIf="nameSearchQuery && nameSearchQuery.length < 3">Minimum search length is 3</span>
  </section>
  
  
  <section class="section">
    <div class="row">
      <div class="col-xs-12">
        <table class="table table--compact table--hoverable-grey">
          <thead class="table__thead">
            <tr class="">
              <th class="table__th ">Name</th>
              <th class="table__th ">Address</th>
              <th class="table__th ">Phone</th>
            </tr>
          </thead>
          <tbody class="table__tbody" *ngIf="agencyList">
            <tr class="table__tr" *ngFor="let row of agencyList">
              <td class="table__td u-color-pelorous">
                  <a (click)="changeAgency.open()">{{ row.name }}
                  </a>
                  <app-modalbox #changeAgency [css]="'u-width-420px'">
                    <h2 class="o-heading o-heading--red">Redirect Estamp Request?</h2>
                    <div class="box box--silver u-spacing--1-5">
                      <p>You are about to direct this Dealer Estamp Request to {{row.name}}. This will remove the request from your queue and you will no longer have access to it. Click Send to continue or cancel to go back.</p>
                   
                      </div>
              
                    <div class="row u-spacing--2">
                      <div class="col-xs-12 u-align-right">
                        <button class="o-btn" (click)="Send(row);changeAgency.closeModalbox()">Send</button>
              
                        <button class="o-btn o-btn--idle u-spacing--left-2"
                          (click)="changeAgency.closeModalbox();modalbox.close()">Cancel</button>
                      </div>
                    </div>
                  </app-modalbox>
                
                </td>
                  <td class="table__td">{{ row.address }}</td>
                  <td class="table__td">
                {{row.phone}}
                  </td>
            </tr>
          </tbody>
          <tbody class="table__tbody" *ngIf="!agencyList?.length">
            <tr class="table__tr">
              <td colspan="5">
                <p class="u-padd--bottom-1 u-padd--1">
                  There are no results that match your search.
                </p>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </section>
  
  <section class="section">
    <div class="u-flex u-flex--spread u-flex--to-middle">
      <div class="">
        <app-pagination
          [currentPage]="paginationCurrentPage"
          [totalRecords]="paginationResultsCount"
          [recordsLimit]="paginationResultLimit"
          (onPageChange)="paginationPageChange($event)"
        ></app-pagination>
      </div>
      <div class="">
        <app-results-limiter
          [description]="'Stamps to show'"
          (onChange)="onResultLimitChange($event)"
        ></app-results-limiter>
      </div>
    </div>

  
  
  
  
  