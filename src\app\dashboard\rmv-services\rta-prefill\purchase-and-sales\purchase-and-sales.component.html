<section class="section section--compact u-spacing--2-5">
    <div class="row">
      <h1 class="o-heading" style="color: #0b71ac; padding-left: 20px;">Purchase And Sales Information </h1>
    </div>
    <div class="row u-spacing--1">
      <div class="col-xs-12">
        <div class="box box--silver">
            <div class="row">
                <div class="col-md-2" [ngClass]="{'is-required-field': getReadyEligible() && purchaseAndSalesInfo.purchaseDate === ''}">
                    <label for="purchaseDate">Purchase Date:</label>
                </div>
                <div class="col-md-3" [ngClass]="{'is-required-field': getReadyEligible() && purchaseAndSalesInfo.purchaseDate === ''}">
        <app-datepicker-input ngDefaultControl [(ngModel)]="purchaseAndSalesInfo.purchaseDate" (onDateChange)="setDate($event)" name="purchaseDate" [placeholder]="'MM/dd/yyyy'" [returnDateFormat]="'yyyy-MM-dd'"></app-datepicker-input>
                </div>
                <div class="col-md-2 offset-md-3" [ngClass]="{'is-required-field': getReadyEligible() && purchaseAndSalesInfo.purchaseState === ''}">
                    <label for="PurchaseState">Purchase State:</label>
                </div>
                <div class="col-md-2" [ngClass]="{'is-required-field': getReadyEligible() && purchaseAndSalesInfo.purchaseState === ''}">
                    <sm-autocomplete style="width: 60px;" [options]="stateOptions" name="purchaseState" [(ngModel)]="purchaseAndSalesInfo.purchaseState" [activeOption]="purchaseAndSalesInfo.purchaseState" name="titleState"></sm-autocomplete>  
                </div>
            </div>
            <div class="row">
                <div class="col-md-2">
                    <label for="seller">Seller:</label>
                </div>
                <div class="col-md-6">
                    <input [(ngModel)]="seller.businessName" name="sellerBusinessName" placeholder="Business Name">
                </div>
                <div class="col-md-2"><input name="sellerFirstName" [(ngModel)]="seller.firstName" placeholder="First Name"></div>
                <div class="col-md-2"><input name="sellerLastName" [(ngModel)]="seller.lastName" placeholder="Last Name"></div>

            </div>
            <div class="row">
                <div class="col-md-2">
                    <label for="seller">Address:</label>
                </div>
                <div class="col-md-6">
                    <input [(ngModel)]="sellerAddress.street" name="sellerStreet" placeholder="Street">
                </div>
                <div class="col-md-4">
                    <input [(ngModel)]="sellerAddress.unitOrApt" name="sellerUnit" placeholder="Unit/Apt">
                </div>
            </div>
            <div class="row">
                <div class="col-md-6 offset-md-2">
                    <input name="sellerCity" [(ngModel)]="sellerAddress.city" placeholder="City">
                </div>
                    <div class="col-md-2">
                    <sm-autocomplete [options]="stateOptions" name="sellerState" [(ngModel)]="sellerAddress.state" [activeOption]="sellerAddress.state"></sm-autocomplete>  
                    </div>
                <div class="col-md-2"><input [(ngModel)]="sellerAddress.zip" name="sellerZip" placeholder="Zip"></div>
            </div>
            <div class="row">
                <div class="col-md-2">
                    <label>Tax Exempt:</label>
                </div>
                <div class="col-md-2">
                    <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
                        <input type="radio" name="taxExempt" value="Y"
                          [(ngModel)]="purchaseAndSalesInfo.taxExempt">
                        <i class="o-btn o-btn--radio"></i>
                        <span style="margin-right: 10px;">Yes</span>
                        </label>
                        <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
                          <input type="radio" name="taxExempt" value="N"
                            [(ngModel)]="purchaseAndSalesInfo.taxExempt">
                          <i class="o-btn o-btn--radio"></i>
                          <span>No</span>
                          </label>
                </div>
                <div class="col-md-2 offset-md-4"><label>Tax Exempt Type:</label></div>
                <div class="col-md-2"><sm-autocomplete [(ngModel)]="purchaseAndSalesInfo.taxExemptType" name="taxExemptType" [options]="taxEmemptOptions"></sm-autocomplete></div>
            </div>
            <div class="row">
                <div class="col-md-2" [ngClass]="{'is-required-field': getReadyEligible() && purchaseAndSalesInfo.purchaseType === ''}">
                    <label for="purchaseType">Purchase Type:</label>
                </div>
                <div class="col-md-3" [ngClass]="{'is-required-field': getReadyEligible() && purchaseAndSalesInfo.purchaseType === ''}">
                    <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
                        <input type="radio" name="purchaseType" value="CASUAL"
                          [(ngModel)]="purchaseAndSalesInfo.purchaseType">
                        <i class="o-btn o-btn--radio"></i>
                        <span style="margin-right: 10px;">Casual</span>
                        </label>
                        <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
                          <input type="radio" name="purchaseType" value="DEALER"
                            [(ngModel)]="purchaseAndSalesInfo.purchaseType">
                          <i class="o-btn o-btn--radio"></i>
                          <span>Dealer</span>
                          </label>
                </div>
                <div class="col-md-2 offset-md-3" [ngClass]="{'is-required-field': getReadyEligible() && purchaseAndSalesInfo.dealerFid === ''}" [hidden]="purchaseAndSalesInfo.purchaseType === 'CASUAL'">
                    <label for="dealerFid">Dealer FID:</label>
                </div>
                <div class="col-md-2" [hidden]="purchaseAndSalesInfo.purchaseType === 'CASUAL'" [ngClass]="{'is-required-field': getReadyEligible() && purchaseAndSalesInfo.dealerFid === ''}">
                    <input name="dealerFid" [(ngModel)]="purchaseAndSalesInfo.dealerFid">
                </div>
            </div>
            <div class="row">
                <div class="col-md-2" [ngClass]="{'is-required-field': getReadyEligible() && purchaseAndSalesInfo.totalSalePrice === ''}">
                    <label for="salePrice">
                        <span *ngIf="purchaseAndSalesInfo.purchaseType === 'CASUAL' || purchaseAndSalesInfo.purchaseType !== 'DEALER'">Gross Sale Price:</span>
                        <span *ngIf="purchaseAndSalesInfo.purchaseType === 'DEALER'">Total Sale Price:</span>
                    </label>
                </div>
                <div class="col-md-3" [ngClass]="{'is-required-field': getReadyEligible() && purchaseAndSalesInfo.totalSalePrice === ''}">
                    <input [(ngModel)]="purchaseAndSalesInfo.totalSalePrice" name="totalSales">
                </div>
                <div *ngIf="purchaseAndSalesInfo.purchaseType === 'DEALER'; else outOfState">
                    
                    <div class="col-md-2 offset-md-3" [ngClass]="{'is-required-field': getReadyEligible() && purchaseAndSalesInfo.auctionSale === ''}"><label>Auction Sale:</label></div>
                    <div class="col-md-2" [ngClass]="{'is-required-field': getReadyEligible() && purchaseAndSalesInfo.auctionSale === ''}">
                        <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
                            <input type="radio" name="auctionSale" value="Y"
                              [(ngModel)]="purchaseAndSalesInfo.auctionSale">
                            <i class="o-btn o-btn--radio"></i>
                            <span style="margin-right: 10px;">Yes</span>
                            </label>
                            <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
                              <input type="radio" name="auctionSale" value="N"
                                [(ngModel)]="purchaseAndSalesInfo.auctionSale">
                              <i class="o-btn o-btn--radio"></i>
                              <span>No</span>
                              </label>
                    
                   </div>
                    </div>
            </div>
                    <ng-template #outOfState>
                        <div class="col-md-2 offset-md-2"><label>Non MA Sales Tax Paid:</label></div>
                    <div class="col-md-2" style="margin-left:30px">
                        <input [(ngModel)]="purchaseAndSalesInfo.nonMASalesTaxPreviouslyPaid" name="nonMATaxPaid">
                    
                   </div>
                  
                    </ng-template>

                    <div class="row" *ngIf="purchaseAndSalesInfo.purchaseType === 'CASUAL' || purchaseAndSalesInfo.purchaseType !== 'DEALER'">
                        <div class="col-md-3 label-width">
                            <label for="MAResident">MA Resident At Time of Purchase:</label>
                        </div>
                        <div class="col-md-2" style="width:10%">
                         <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
                             <input type="radio" name="resident" value="Y"
                             [(ngModel)]="purchaseAndSalesInfo.maResidentAtTimeOfPurchase"
                               >
                             <i class="o-btn o-btn--radio"></i>
                             <span style="margin-right: 10px;">Yes</span>
                             </label>
                             <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
                               <input type="radio" name="resident" value="N"
                               [(ngModel)]="purchaseAndSalesInfo.maResidentAtTimeOfPurchase"
                                >
                               <i class="o-btn o-btn--radio"></i>
                               <span>No</span>
                               </label>
                     
                    </div>
                    <div class="col-md-2">
                     <label for="MAResident">Proof Of No Tax Required:</label>
                 </div>
                 <div class="col-md-2" style="width:10%">
                  <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
                      <input type="radio" name="proofOfNoTax" value="Y"
                      [(ngModel)]="purchaseAndSalesInfo.proofOfNoTaxRequired"
                        >
                      <i class="o-btn o-btn--radio"></i>
                      <span style="margin-right: 10px;">Yes</span>
                      </label>
                      <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
                        <input type="radio" name="proofOfNoTax" value="N"
                        [(ngModel)]="purchaseAndSalesInfo.proofOfNoTaxRequired"
                         >
                        <i class="o-btn o-btn--radio"></i>
                        <span>No</span>
                        </label>
              
             </div>
             <div class="col-md-3 label-width" style="margin-left:33px">
                <label for="MAResident">MA Sales Tax Previously Paid:</label>
            </div>
            <div class="col-md-2" style="width:10%">
             <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
                 <input type="radio" name="taxPreviously" value="Y" 
                 [(ngModel)]="purchaseAndSalesInfo.maSalesTaxPreviouslyPaid"  
                 >
                 <i class="o-btn o-btn--radio"></i>
                 <span style="margin-right: 10px;">Yes</span>
                 </label>
                 <label class="o-checkable" [appDetectSystem]  [removeRadio]="true">
                   <input type="radio" name="taxPreviously" value="N"
                   [(ngModel)]="purchaseAndSalesInfo.maSalesTaxPreviouslyPaid"
                   >
                   <i class="o-btn o-btn--radio"></i>
                   <span>No</span>
                   </label>
         
        </div>
                    </div>
                   
                </div>
            </div>
        </div>
    
           
</section>

