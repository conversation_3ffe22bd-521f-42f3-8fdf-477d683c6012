<section class="section section--compact u-bg-dark-blue">

  <div class="row">
    <div class="col-xs-12">
      <nav class="tabs">
        <ul class="tabs__list">
          <!-- li class="tabs__item">
            <a class="tabs__link" routerLink="/dashboard/summary" routerLinkActive="is-active">Summary</a>
          </li -->

          <li class="tabs__item">
            <a class="tabs__link" routerLink="/dashboard/quotes" [class.is-active]="activeTab === 'quotes'">Quotes</a>
          </li>
          <li class="tabs__item">
            <a class="tabs__link" routerLink="/dashboard/forms" [class.is-active]="activeTab === 'forms'">Forms</a>
          </li>

          <li class="tabs__item">
            <a class="tabs__link" routerLink="/dashboard/quote-inbox" [class.is-active]="activeTab === 'leads'">Leads Inbox <span *ngIf="leadCount$ | async as count"><span class="badge" *ngIf="count > 0">{{count}}</span></span></a>
          </li>
          <li class="tabs__item">
            <a class="tabs__link" routerLink="/dashboard/clients" [class.is-active]="activeTab === 'clients'">Clients</a>
          </li>
          <li class="tabs__item">
            <a class="tabs__link" routerLink="/dashboard/eStamp-requests" [class.is-active]="activeTab === 'eStamp-requests'">eStamp Requests <span *ngIf="estampCount$ | async as estampCount"><span class="badge" *ngIf="estampCount > 0">{{estampCount}}</span></span></a>
          </li>
        </ul>
        <!--
        <div class="tabs__right">
        </div>
        -->
      </nav><!-- / .tabs -->

    </div>
  </div>
</section>



