<section class="section section--compact u-spacing--2-5">
  <div class="row">
    <div class="col-xs-12">
      <h1 class="o-heading">Primary Policy Information</h1>
    </div>
  </div>

  <div class="box box--silver u-spacing--1">
    <div class="row o-columns">
      <div class="col-xs-6">
        <table class="form-table">
          <tr class="form-table__row" [ngClass]="{'is-required-field': refUmbrellaCity.ngModel?.invalid}">
            <td class="form-table__cell u-width-195px">
              <label for="">Primary Residence Location:</label>
            </td>
            <td class="form-table__cell u-width-210px">
              <sm-autocomplete
              fieldAutofocus
              #refUmbrellaCity
              [options]="cities"
              [name]="'primaryPolicyInformationLocation'"
              [id]="'primaryPolicyInformationLocation'"
              [caseSensitiveOptionsIdMatching]="false"
              [activeOption]="getSelectedCityOption()"
              [readonly]="cities.length ? false : true"
              [required]="true"
              [allowEmptyValue]="false"
              (onSelect)="updatePrimaryResidenceLocation($event)">
              </sm-autocomplete>
            </td>
          </tr>

          <tr class="form-table__row">
            <td class="form-table__cell u-width-155px">
              <label for="">Underlying Limits:</label>
            </td>
            <td class="form-table__cell u-width-185px">
              <sm-autocomplete
                [id]="'primaryPolicyInformationUnderlyingLimits'"
                [options]="primaryUnderlyingLimits"
                [activeOption]="basicPolicyInfo.underlyingLimits"
                (onSelect)="updatePrimaryUnderlyingLimits($event)">
              </sm-autocomplete>
            </td>
          </tr>
        </table>
      </div>
      <div class="col-xs-6">
        <table class="form-table">
          <tr class="form-table__row">
            <td class="form-table__cell u-width-155px">
              <label for="">Policy Limits:</label>
            </td>
            <td class="form-table__cell u-width-210px">
              <sm-autocomplete
                [id]="'primaryPolicyInformationPolicyLimits'"
                [options]="primaryPolicyLimits"
                [activeOption]="basicPolicyInfo.policyLimits"
                (onSelect)="updatePrimaryPolicyLimits($event)">
              </sm-autocomplete>
            </td>
          </tr>
        </table>
      </div>
    </div>
  </div>
</section>
