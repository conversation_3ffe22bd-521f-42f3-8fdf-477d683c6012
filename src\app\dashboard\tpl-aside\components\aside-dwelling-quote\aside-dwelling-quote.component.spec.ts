import { StorageGlobalService } from './../../../../shared/services/storage-global.service';
import { async, ComponentFixture, fakeAsync, inject, TestBed, tick } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';

import { DWELLING_COVERAGES } from 'testing/data/quotes/coverages/dwelling';
import { DWELLING_QUOTE } from 'testing/data/quotes/quote-dwelling';

import { StubDatepickerModalComponent } from 'testing/stubs/components/datepicker-modal.component';
import { StubPlansSelectorComponent } from 'testing/stubs/components/plans-selector.component';
import { StubSelectComponent } from 'testing/stubs/components/select.component';
import { StubdateFormatPipe } from 'testing/stubs/pipes/am-date-format.pipe';
import { StubCoveragesServiceProvider } from 'testing/stubs/services/coverages.service.provider';
import { StubOptionsServiceProvider } from 'testing/stubs/services/options.service.provider';
import {
    StubOverlayLoaderServiceProvider
} from 'testing/stubs/services/overlay-loader.service.provider';
import { StubPremiumsServiceProvider } from 'testing/stubs/services/premiums.service.provider';
import { StubQuotesServiceProvider } from 'testing/stubs/services/quotes.service.provider';

import { StorageService } from '../../../../shared/services/storage-new.service';
import { Helpers } from '../../../../utils/helpers';
import { AsideDwellingQuoteComponent } from './aside-dwelling-quote.component';

describe('AsideDwellingQuoteComponent', () => {
  let component: AsideDwellingQuoteComponent;
  let fixture: ComponentFixture<AsideDwellingQuoteComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [ RouterTestingModule ],
      declarations: [
        AsideDwellingQuoteComponent,
        StubSelectComponent,
        StubPlansSelectorComponent,
        StubdateFormatPipe,
        StubDatepickerModalComponent
      ],
      providers: [
        StorageService,
        StubQuotesServiceProvider,
        StubOverlayLoaderServiceProvider,
        StubPremiumsServiceProvider,
        StubCoveragesServiceProvider,
        StubOptionsServiceProvider,
        StorageGlobalService
      ]
    })
    .compileComponents();
  }));

  beforeEach(fakeAsync(inject([StorageService], (
    storageService: StorageService
  ) => {
    storageService.setStorageData('selectedQuote', Helpers.deepClone(DWELLING_QUOTE));
    storageService.setStorageData('dwellingQuoteOptions', Helpers.deepClone(DWELLING_COVERAGES));

    fixture = TestBed.createComponent(AsideDwellingQuoteComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
    tick();
  })));

  it('should be created', () => {
    expect(component).toBeTruthy();
  });
});
