
import { take } from 'rxjs/operators';
import * as _ from 'underscore';

import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { Component, OnInit, ViewChild, ChangeDetectorRef, Input, HostListener } from '@angular/core';

import { LeadDetail, LeadRecord } from 'app/app-model/lead';
import { AgencyUserService } from 'app/shared/services/agency-user.service';
import { ClientsService } from 'app/dashboard/app-services/clients.service';
import { DateRange } from 'app/app-model/date';
import { DatesService } from '../app-services/dates.service';
import { FilterOption } from 'app/app-model/filter-option';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { Quote } from 'app/app-model/quote';
import { LeadsService } from '../app-services/leads.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { SubsService } from '../app-services/subs.service';
import { SubscriptionLike as ISubscription } from 'rxjs';
import { PremiumsService } from 'app/dashboard/auto/premiums/premiums.service';
import { DeleteQuoteModalComponent } from 'app/shared/components/delete-quote/delete-quote.component';
import { MatTabGroup } from '@angular/material/tabs';
import { ModalboxComponent } from 'app/shared/components/modalbox/modalbox.component';
import { FilterComponent } from 'app/shared/components/filter/filter.component';
import { FilterDatesComponent } from 'app/shared/components/filter-dates/filter-dates.component';
import { format } from 'date-fns';

@Component({
    selector: 'app-page-quote-inbox',
    templateUrl: './page-quote-inbox.component.html',
    styleUrls: ['./page-quote-inbox.component.scss'],
    standalone: false
})
export class PageQuoteInboxComponent implements OnInit {

  constructor(
    private leadsService: LeadsService,
    private subsService: SubsService,
    private specsService: SpecsService,
    private storageService: StorageService,
    private datesService: DatesService,
    private agencyUserService: AgencyUserService,
    private overlayLoaderService: OverlayLoaderService,
    private activatedRoute: ActivatedRoute,
    private changeDetectorRef: ChangeDetectorRef,
    private storageGlobalService: StorageGlobalService,
    private premiumsService: PremiumsService,
    private router: Router,
  ) { }

  @ViewChild('deleteQuoteModal') public deleteQuoteModal: DeleteQuoteModalComponent;
  @ViewChild('refModalQuoteInbox') public leadPopupModal: ModalboxComponent;
  @ViewChild('tabGroup') tabGroup: MatTabGroup;
  @ViewChild('refFilterDates') public FilterDates: FilterComponent;
  @ViewChild(FilterDatesComponent) filterDates;

  newQuotesSize: any;
  leadQuoteSubscription: any;
  leadStatusSubscription: any;
  leadDetailSubscription: any;
  selectedLead: any;
  selectedLeadDetail = new LeadDetail();
  showLead = new LeadRecord();

  public modalboxLoadingData = false;
  public paginationResultsCount = 0;
  public paginationResultLimit = 10;
  public paginationResultShowFrom = 0;
  public isDeleteConfirmed = false;
  public arrLeadsAll: any[];
  public arrLeadsFiltered: any[];

  private dataInited = false;

  public searchQuery: string;

  public filterStatusOptions: FilterOption[] = [
    { id: '0', text: 'All' },
    { id: '1', text: 'Opened' },
    { id: '2', text: 'Viewed' },
    { id: '8', text: 'Received' },
    { id: '16', text: 'Quoted' },
    { id: '32', text: 'Incomplete' }
  ];

  private filterStatusSelectedOption: FilterOption = this.filterStatusOptions[0];
  public filterStatusLabel: string;

  public filterSourceOptions: FilterOption[] = [
    { id: '0', text: 'All' },
    { id: '1', text: 'Site' },
    { id: '2', text: 'Facebook' },
    { id: '3', text: 'Share' },
    { id: '4', text: 'Plymouth Rock' },
    { id: '5', text: 'MassDrive.com' },
    { id: '6', text: 'Trusted Choice' }
  ];

  private filterSourceSelectedOption: FilterOption = this.filterSourceOptions[0];
  public filterSourceLabel: string;

  public dateRange: DateRange = new DateRange();

  private agencyId: string;

  public selectedLeads: string[] = [];

  public isAllSelected = false;

  private isRefreshRequest = false;

  public leadsText;

  private getLeadsSubscription: ISubscription;
  private getLeadsRequestDataString = '';

  private dataToChange: any;

  ngOnInit() {
    this.Init();
  }

  ngOnDestroy() {

  }

  private Init() {
    this.getLeads();
    this.setFilterSourceOptions();
    this.setFilterStatusOptions();
    this.filterDates.setFilterDatesRangesOptions();
  }

  public filterByName(event) {
    this.filterDates.searchQuery = this.searchQuery;
    if ((event.key === 'Enter' && this.searchQuery.length) || (event.target.tagName === 'BUTTON') || (this.searchQuery.length >= 3)) {
      this.filterDates.filterDatesLabel = 'All Dates';
      this.isAllSelected = false;
      this.selectedLeads = [];
      this.getLeads();
    } else {
      this.Init();
    }
  }

  // Toggles invidual quote checkbox
  public toggleLead(quoteIdentifier: string): void {
    if (!quoteIdentifier) { return; }

    const itemIndex = this.selectedLeads.indexOf(quoteIdentifier);
    const isPresent = (itemIndex > -1);

    if (isPresent) {
      this.selectedLeads.splice(itemIndex, 1);
    } else {
      this.selectedLeads.push(quoteIdentifier);
    }

    this.storageService.setStorageData('leadListToDelete', this.selectedLeads);
  }

  // to check/uncheck based on the selected quote list
  public isSelected(quoteIdentifier: string): boolean {
    return (this.selectedLeads.indexOf(quoteIdentifier) > -1 || this.isAllSelected);
  }

  // Toggles all the Leads with a single select.
  public toggleAll(): void {
    this.isAllSelected = !this.isAllSelected;

    if (this.isAllSelected) {
      this.arrLeadsAll.forEach(quote => {
        if (this.selectedLeads.indexOf(quote.quoteIdentifier) === -1) {
          this.selectedLeads.push(quote.quoteIdentifier);
        }
      });
      this.storageService.setStorageData('leadListToDelete', this.selectedLeads);
    } else {
      this.selectedLeads = [];
    }
  }

  // to check/uncheck using a property
  public isChecked(): boolean {
    return this.isAllSelected;
  }

  // This method only updates the quote status to deleted
  // But will not physically remove the quote from the database.
  // Physical deletion is done at a later stage through automation
  public deleteLeads(): void {
    this.storageService.setStorageData('leadListToDelete', this.selectedLeads);
    this.deleteQuoteModal.modal.open();
  }

  // This is triggered once a delete Leads method is called from Delete-Quote.component
  public refreshPage() {
    // this property is set to indicate to the getLeads method to refresh the current page.
    this.isRefreshRequest = true;
    this.getLeads();
  }

  public getLeads(): void {

    let status, source, startDate, endDate, name;

    name = this.searchQuery ? this.searchQuery : '';

    status = this.filterStatusSelectedOption.id;
    if (status === 'all' || status === 'All') {
      status = '';
    }

    source = this.filterSourceSelectedOption.id;
    if (source === 'all' || source === 'All') {
      source = '';
    }

    startDate = this.dateRange.start ? format(new Date(this.dateRange.start), 'yyyy-MM-dd') : '';
    endDate = this.dateRange.end ? format(new Date(this.dateRange.end),'yyyy-MM-dd') : '';

    // If request data hasn't changed, do not send request
    const currentRequestDataString = this.paginationResultShowFrom.toString()
      + this.paginationResultLimit.toString()
      + name
      + source
      + status
      + startDate
      + endDate;

    if (this.getLeadsRequestDataString === currentRequestDataString && !(this.isRefreshRequest)) {
      return;
    }
    // --------------

    this.isRefreshRequest = false;
    this.getLeadsRequestDataString = currentRequestDataString;

    if (this.getLeadsSubscription) {
      this.overlayLoaderService.hideLoader();
      this.getLeadsSubscription.unsubscribe();
    }

    this.overlayLoaderService.showLoader('Loading Results...');
    this.getLeadsSubscription = this.leadsService.getLeads(
      this.paginationResultShowFrom.toString(),
      this.paginationResultLimit.toString(),
      name, // name
      source,
      status,
      startDate,
      endDate
    ).pipe(take(1)).subscribe(Leads => {
      this.dataInited = true;
      this.arrLeadsAll = Leads.items;
      this.arrLeadsFiltered = this.arrLeadsAll;
      this.paginationResultsCount = Leads.size;
      this.overlayLoaderService.hideLoader();
    }, reject => {
      this.overlayLoaderService.hideLoader();
    });
  }

  public getLeadsFromFilteredDates() {

    this.dateRange = this.filterDates.dateRange;
    this.selectedLeads = [];
    this.getLeads();

    var allowFiltering = this.filterDates.isFilteringAllowed();
    if (allowFiltering) {
      this.getLeads();
    }

    this.FilterDates.tooltip.close();

  }

  // Added to handle the case when used changes the dates but do not clicks "search" button  - preses escape or clicks outside of the dates filter
  @HostListener('document:keydown', ['$event'])
  public onKeyPress($ev: KeyboardEvent) {
    if ($ev.key == 'Escape' || $ev.key == 'Esc' || $ev.key == 'Esc') {
      this.getLeads();
    }
  }
  @HostListener('window:click', ['$event'])
  public onMouseClick($ev: MouseEvent) {
    this.getLeads();
  }

  public setShowLead(lead: any) {
    this.showLead = lead;
    lead.viewStatus = 'Viewed';
    this.leadStatusSubscription = this.leadsService.updateLeadStatus(lead).subscribe(status => {
      lead.viewStatus = status.viewStatus;
      this.updateLeadsText();
    });

    this.modalboxLoadingData = true;

    // Get leads details
    this.leadDetailSubscription = this.leadsService.getLeadDetail(lead.leadId).subscribe(detail => {
      this.selectedLeadDetail = detail;
      this.modalboxLoadingData = false;

    });

  }

  public rate(lead: any) {
    lead.viewStatus = 'Opened';
    this.overlayLoaderService.showLoader('Creating new session...');

    this.leadStatusSubscription = this.leadsService.updateLeadStatus(lead).subscribe(status => {
      lead.viewStatus = status.viewStatus;
      this.updateLeadsText();
    },
      err => { },
      () => {
        // this.overlayLoaderService.hideLoader();
      });

    this.leadQuoteSubscription = this.leadsService.createNewQuoteSession(lead)
      .subscribe(
        res => {
          const newQuoteSessionURL =
            '#/dashboard/quotes/' + res.quoteSessionId;
          // this.overlayLoaderService.hideLoader();
          // let leadRateWindow: Window;
          // leadRateWindow = window.open('', '_blank');
          // leadRateWindow.location.href = newQuoteSessionURL;
          this.router.navigate(['/dashboard/quotes/' + res.quoteSessionId]);
        },
        err => { },
        () => {
          this.overlayLoaderService.hideLoader();
        }
      );
  }
  public deleteSelectedQuote(lead: any, modalRef) {

    this.leadDetailSubscription = this.leadsService.deleteLead(lead.leadId).subscribe(result => {
      this.refreshPage();
    });

    modalRef.closeModalbox();
  }
  public confirmCanceled($event) {
    // console.log('Cancel: ', $event);
    // dummy function to close popup
  }


  public deleteSelectedQuotes() {

    this.leadDetailSubscription = this.leadsService.deleteSelectedLeads(this.selectedLeads).subscribe(result => {
      this.refreshPage();
    });

  }
  public updateLeadsText() {

    if (this.arrLeadsAll && this.arrLeadsAll.length > 0) {

      const newQuotes = this.arrLeadsAll.filter(lead => lead.viewStatus !== 'Opened');
      this.newQuotesSize = newQuotes.length;

      this.leadsText = (this.newQuotesSize <= 1) ? `${this.newQuotesSize} new lead` : `${this.newQuotesSize} new leads`;
    } else {
      return '';
    }
  }


  private parseLastModifiedDate(date: string): string {
    return format(new Date(date),'MMM d, yyyy');
  }

  private setFilterSourceOptions(): void {
    this.filterSourceSelectedOption = this.filterSourceOptions[0];
  }

  private setFilterStatusOptions(): void {
    this.filterStatusSelectedOption = this.filterStatusOptions[0];
  }

  // Pagination
  // ------------------------------------------------------------------------------

  public paginationPageChange(data) {
    // if (this.dataInited) {
    //   this.paginationResultShowFrom = data.startAt;
    //   /*if (this.filterLobSelectedOption.id === 'all') {
    //     this.filterLobSelectedOption.id = '';
    //   }*/
    //   this.getLeads();
    // }
    if (this.dataInited && this.paginationResultShowFrom !== data.startAt) {
      this.paginationResultShowFrom = data.startAt;
      this.getLeads();
    }
  }

  private paginationSetResultLimit(intLimit: any) {
    this.paginationResultLimit = parseInt(intLimit, 10);
  }

  public onResultLimitChange($ev): void {
    setTimeout(() => {
      this.paginationSetResultLimit($ev.limit);
      this.getLeads();
    });
  }

 


  // Filters on the Page
  // ------------------------------------------------------------------------------
  private onStatusChange(option: FilterOption): void {
    this.filterStatusSelectedOption = option;
  }

  private onSourceChange(option: FilterOption): void {
    this.filterSourceSelectedOption = option;
  }
  private filterDataChangeUpdate($ev): void {
    if (this.dataToChange && ($ev.selectedOption.id !== this.dataToChange.selectedOption.id || $ev.selectedOption.id === 'all')) {
      this.dataToChange = $ev;
      switch ($ev.filterId) {
        case 'filter_Status':
          this.onStatusChange($ev.selectedOption);
          break;
        case 'filter_Source':
          this.onSourceChange($ev.selectedOption);
          break;
      }
      this.selectedLeads = [];
      this.getLeads();
      this.getLeads();
    } else {
      this.dataToChange = $ev;
    }
  }


  public onFilterDataChange($ev): void {
    this.filterDataChangeUpdate($ev);
    // this.filterResults();
  }

  resetSearch() {
    this.filterDates.prepareResetSearch();
    this.searchQuery = null;
    this.dateRange.start = null;
    this.dateRange.end = null;
    this.isAllSelected = false;
    this.selectedLeads = [];
    this.filterStatusSelectedOption = this.filterStatusOptions[0];
    this.filterStatusLabel = this.filterStatusSelectedOption.text;
    this.filterSourceSelectedOption = this.filterSourceOptions[0];
    this.filterSourceLabel = this.filterSourceSelectedOption.text;
    this.getLeads();
  }

  // Custom filter Modal
  // ------------------------------------------------------------------------------
  /*
  private customFilterSelectedDateType:FilterOption = new FilterOption();
  private customFilterSelectedDateRange:FilterOption = new FilterOption();
  private customFilterSelectedAgent:FilterOption = new FilterOption();
  private customFilterSelectedLob:FilterOption = new FilterOption();
  private customFilterDateRange:DateRange = new DateRange();

  private initCustomFilterData():void {
    this.customFilterSelectedDateType = Object.assign({},this.filterDatesTypesSelectedOption);
    this.customFilterSelectedDateRange = Object.assign({},this.filterDatesRangeSelectedOption);
    this.customFilterSelectedAgent = Object.assign({},this.filterAgentsSelectedOption);
    this.customFilterSelectedLob = Object.assign({},this.filterLobSelectedOption);
    this.customFilterDateRange = Object.assign({},this.dateRange);
  }

  private onCustomFilterModalboxOpen($ev):void {
    $ev.isOpen && this.initCustomFilterData();
  }

  private onCustomFilterDateTypeChange($ev):void {
    this.customFilterSelectedDateType = $ev;
  }

  private onCustomFilterDateRangeChange($ev):void {
    this.customFilterDateRange = this.getDateRangeFromSelectedOption($ev, this.customFilterDateRange);
    this.customFilterSelectedDateRange = $ev;
  }

  private onCustomFilterAgentChange($ev):void {
    this.customFilterSelectedAgent = $ev;
  }

  private onCustomFilterLobChange($ev):void {
    this.customFilterSelectedLob = $ev;
  }

  private onCustomFilterDatepickerDateChange($ev, property:string):void {
    let tmpDate = $ev.date;
    switch(property) {
      case 'start':
        tmpDate = this.datesService.getDateStartRangeFromDate($ev.date);
      break;
      case 'end':
        tmpDate = this.datesService.getDateEndRangeFromDate($ev.date);
      break;
    }

    if ($ev.selectByClick) {
      this.customFilterSelectedDateRange = this.filterDatesRangeOptions.filter(opt=>opt.id==='custom')[0];
    }
    this.customFilterDateRange[property] = tmpDate;
  }

  private customFilterModalApply(refModal):void {
    this.filterLobSelectedOption = (this.customFilterSelectedLob) ? Object.assign({},this.customFilterSelectedLob) : this.filterLobSelectedOption;
    this.filterAgentsSelectedOption = (this.customFilterSelectedAgent) ? Object.assign({},this.customFilterSelectedAgent) : this.filterAgentsSelectedOption;
    this.filterDatesTypesSelectedOption = (this.customFilterSelectedDateType) ? Object.assign({},this.customFilterSelectedDateType) : this.filterDatesTypesSelectedOption;
    this.filterDatesRangeSelectedOption = (this.customFilterSelectedDateRange) ? Object.assign({},this.customFilterSelectedDateRange) : this.filterDatesRangeSelectedOption;
    this.dateRange = (this.customFilterDateRange) ? Object.assign({},this.customFilterDateRange) : this.dateRange;

    this.updateFiltersLabels();
    this.getLeads();
    //this.filterResults();
    refModal.closeModalbox();
  }

  private customFilterModalCancel(refModal) {
    refModal.closeModalbox();
  }
  */

  // Helpers
  // ------------------------------------------------------------------------------

  public closeModalBox() {
    this.tabGroup.selectedIndex = 0;
    this.leadPopupModal.close();
  }
}

