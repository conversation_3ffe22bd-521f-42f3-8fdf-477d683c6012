import { async, ComponentFixture, fakeAsync, TestBed } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';

import { changeTextInputValue } from 'testing/helpers/all';
import { StubDwellingServiceProvider } from 'testing/stubs/services/dwelling.service.provider';
import {
    StubOverlayLoaderServiceProvider
} from 'testing/stubs/services/overlay-loader.service.provider';

import { StorageService } from 'app/shared/services/storage-new.service';

import { AgreedValueJewelryComponent } from './agreed-value-jewelry.component';

describe('Component: AgreedValueJewelry', () => {
  let component: AgreedValueJewelryComponent;
  let fixture: ComponentFixture<AgreedValueJewelryComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [ FormsModule ],
      declarations: [AgreedValueJewelryComponent],
      providers: [
        StorageService,
        StubDwellingServiceProvider,
        StubOverlayLoaderServiceProvider
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AgreedValueJewelryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });

  it('should destroy without errors', () => {
    expect(() => {
      fixture.destroy();
    }).not.toThrow();
  });

  it('should be able to handle vaulted agreed jewelry value change', fakeAsync(() => {
    changeTextInputValue(
      fixture.debugElement.query(By.css('#agreedJewelryVaulted')).nativeElement,
      300,
      fixture
    );

    expect(component.propAgreedJewelryVaulted.itemValueAmount).toEqual(300);
  }));

  it('should be able to handle not vaulted agreed jewelry value change', fakeAsync(() => {
    changeTextInputValue(
      fixture.debugElement.query(By.css('#agreedJewelryNotVaulted')).nativeElement,
      100,
      fixture
    );

    expect(component.propAgreedJewelryNotVaulted.itemValueAmount).toEqual(100);
  }));
});
