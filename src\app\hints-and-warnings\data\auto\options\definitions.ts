import { AdditionalDataI, WARNING_GROUPS, WarningDefinitionI } from 'app/hints-and-warnings/model/warnings';

import { CoverageItemParsed } from 'app/app-model/coverage';
import { QuotePolicyHistory, QuotePlan } from 'app/app-model/quote';
import { Validate } from 'app/hints-and-warnings/validators';

function requiredField(value): boolean {
  return Validate.isEmptyValue(value);
}

function generateViewUrl(fullObj): string {
  let id = '';
  if (fullObj.quoteSessionId) {
    id = fullObj.quoteSessionId;
  } else {
    id = fullObj.endpointUrl;
    if (id) {
      id = id.split('/quotes/')[1];
      id = id.split('/coverages')[0];
    }
  }
  return  '/dashboard/auto/quotes/' + id + '/options';
}

function priorOrRenewingCarrierSetToNoPrior(fullObj: QuotePolicyHistory): boolean {
  return fullObj['priorOrRenewingCarrier'] === 'No Prior Coverage';
}

function disabledIfPriorOrRenewingCarrierNotSet(fullObj: QuotePolicyHistory): boolean {
  return !fullObj['priorOrRenewingCarrier'] || fullObj['priorOrRenewingCarrier'] === 'No Prior Coverage';
}

function fieldIsNotSet(fullObj: QuotePolicyHistory, fieldName: string): boolean {
  return !fullObj[fieldName] || fullObj[fieldName] === 'Unknown';
}

function fieldPolicyNumberIsAvailable(fullObj: QuotePolicyHistory): boolean {
  const availableForPriorOrRenewingCarrierValues = [
    'Arbella',
    'Harleysville',
    'Metropolitan', /* Equal to MetLife */
    'Norfolk and Dedham',
    'Vermont Mutual',
    'Quincy'
  ];
  let isInValues = false;

  isInValues = availableForPriorOrRenewingCarrierValues.indexOf(fullObj.priorOrRenewingCarrier) !== -1;
  return (fullObj.quoteThisCarrierAs === 'Renewal' && isInValues);
}

function fieldPolicyNumberIsRequiredForSelectedPlansBasedOnPriorOrRenewingCarrierValue(priorOrRenewingCarrierValue: string, selectedPlans: QuotePlan[]): boolean {
  let isRequiredValueMatchOneOfTheSelectedPlans = false;
  const forOneOfThePlans = [
    { ratingPlanId: '13', name: 'Arbella'},
    { ratingPlanId: '16', name: 'Harleysville'},
    { ratingPlanId: '24', name: 'MetLife'}, /* Equal to Metropolitan */
    { ratingPlanId: '14', name: 'Norfolk and Dedham'},
    { ratingPlanId: '15', name: 'Vermont Mutual'},
    { ratingPlanId: '18', name: 'Quincy Mutual Group'},


  ];
  let fieldValue;
  switch (priorOrRenewingCarrierValue) {
    case 'Metropolitan' :
      fieldValue = 'MetLife';
      break;
    case 'Quincy' :
      fieldValue = 'Quincy Mutual Group';
      break;
      default:
        fieldValue = priorOrRenewingCarrierValue;
        break;
  }

  isRequiredValueMatchOneOfTheSelectedPlans = selectedPlans.some(plan => plan.name === fieldValue);
  return isRequiredValueMatchOneOfTheSelectedPlans;
}

function getCarrierIdToRequireFor(priorOrRenewingCarrierValue: string): string[] {
  let requireFor: string[] = [];
  const forOneOfThePlans = [
    { ratingPlanId: '13', name: 'Arbella'},
    { ratingPlanId: '16', name: 'Harleysville'},
    { ratingPlanId: '24', name: 'MetLife'}, /* Equal to Metropolitan */
    { ratingPlanId: '14', name: 'Norfolk and Dedham'},
    { ratingPlanId: '15', name: 'Vermont Mutual'},
    { ratingPlanId: '18', name: 'Quincy Mutual Group'},
    { ratingPlanId: '312', name: 'Nationwide Mutual'}
  ];

  let fieldValue;
  switch (priorOrRenewingCarrierValue) {
    case 'Metropolitan' :
      fieldValue = 'MetLife';
      break;
    case 'Quincy' :
      fieldValue = 'Quincy Mutual Group';

      break;
      default:
        fieldValue = priorOrRenewingCarrierValue;
        break;
  }
  const carrierToRequireFor = forOneOfThePlans.find(plan => plan.name === fieldValue);

  if (carrierToRequireFor) {
    requireFor = [carrierToRequireFor.ratingPlanId];
  }
  return requireFor;
}


/**
 * Validation for QuotePolicyHistory Data
 * For Model: QuotePolicyHistory
 */

const priorOrRenewingCarrier: WarningDefinitionI = {
  id: 'priorOrRenewingCarrier',
  deepId: 'priorOrRenewingCarrier',
  viewUri: generateViewUrl,
  viewFieldId: 'priorOrRenewingCarrier',
  warnings: [{
    label: 'Required Policy History Prior or Renewing Carrier',
    condition: (val, fullObj: QuotePolicyHistory) => {
      return !val; // priorOrRenewingCarrierNotSet(fullObj);
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

// Required For:
// MetLife, Travelers, Safeco, Progressive, Bristol West
const priorPolicyExpirationDateCarriers = ['24', '27', '25', '26', '302', '312'];
const priorPolicyExpirationDate: WarningDefinitionI = {
  id: 'priorPolicyExpirationDate',
  deepId: 'priorPolicyExpirationDate',
  viewUri: generateViewUrl,
  viewFieldId: 'priorPolicyExpirationDate',
  warnings: [{
    label: 'Required Policy History Prior Policy Expiration Date',
    condition: (value, fullObj: QuotePolicyHistory, data: AdditionalDataI) => {
      return !disabledIfPriorOrRenewingCarrierNotSet(fullObj)
      && Validate.isRequiredForSelectedPlansIfEmptyValue(value, priorPolicyExpirationDateCarriers, data.quoteSelectedPlansIds);
    },
    group: WARNING_GROUPS.carrier,
    carriers: priorPolicyExpirationDateCarriers
  }]
};


// yearsWithThisCarrier
const yearsWithThisCarrier: WarningDefinitionI = {
  id: 'yearsWithThisCarrier',
  deepId: 'yearsWithThisCarrier',
  viewUri: generateViewUrl,
  viewFieldId: 'yearsWithThisCarrier',
  warnings: [{
    label: 'Required Policy History Years with this Carrier',
    condition: (val, fullObj: QuotePolicyHistory) => {
      return !disabledIfPriorOrRenewingCarrierNotSet(fullObj) && fieldIsNotSet(fullObj, 'yearsWithThisCarrier');
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

// quoteThisCarrierAs
const quoteThisCarrierAs: WarningDefinitionI = {
  id: 'quoteThisCarrierAs',
  deepId: 'quoteThisCarrierAs',
  viewUri: generateViewUrl,
  viewFieldId: 'quoteThisCarrierAs',
  warnings: [{
    label: 'Required Policy History Quote this Carrier as',
    condition: (val, fullObj: QuotePolicyHistory) => {
      return !disabledIfPriorOrRenewingCarrierNotSet(fullObj) && fieldIsNotSet(fullObj, 'quoteThisCarrierAs');
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

// const priorPolicyPolicyNumberCarriers = ['13', '14', '15', '16', '24'];
// https://bostonsoftware.atlassian.net/browse/SPRC-610
const priorPolicyPolicyNumber: WarningDefinitionI = {
  id: 'priorPolicyPolicyNumber',
  deepId: 'priorPolicyPolicyNumber',
  viewUri: generateViewUrl,
  viewFieldId: 'priorPolicyPolicyNumber',
  warnings: [{
    label: 'Required Policy History Policy #',
    condition: (value, fullObj: QuotePolicyHistory, data: AdditionalDataI) => {

      return fieldPolicyNumberIsAvailable(fullObj)
              && fieldPolicyNumberIsRequiredForSelectedPlansBasedOnPriorOrRenewingCarrierValue(fullObj.priorOrRenewingCarrier, data.quoteSelectedPlans)
              && Validate.isRequiredForSelectedPlansIfEmptyValue(
                value,
                // priorPolicyPolicyNumberCarriers,
                getCarrierIdToRequireFor(fullObj.priorOrRenewingCarrier),
                data.quoteSelectedPlansIds
              );
    },
    group: WARNING_GROUPS.carrier,
    // carriers: priorPolicyPolicyNumberCarriers
    carriers: (fullObj: QuotePolicyHistory, data: AdditionalDataI) => {
      return getCarrierIdToRequireFor(fullObj.priorOrRenewingCarrier);
    }
  }]
};


// yearsWithCurrentAgency
const yearsWithCurrentAgency: WarningDefinitionI = {
  id: 'yearsWithCurrentAgency',
  deepId: 'yearsWithCurrentAgency',
  viewUri: generateViewUrl,
  viewFieldId: 'yearsWithCurrentAgency',
  warnings: [{
    label: 'Required Policy History Years with Current Agency',
    condition: (val, fullObj: QuotePolicyHistory) => {
      return fieldIsNotSet(fullObj, 'yearsWithCurrentAgency');
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

// Required For:
// Arbella, Hanover, Plymouth Rock, Safeco, Safety, Travelers, Hanover New
const lapsedDaysLast12MonthsCarriers = ['13', '20', '28', '25', '11', '27', '281', '312'];
const lapsedDaysLast12Months: WarningDefinitionI = {
  id: 'lapsedDaysLast12Months',
  deepId: 'lapsedDaysLast12Months',
  viewUri: generateViewUrl,
  viewFieldId: 'lapsedDaysLast12Months',
  warnings: [{
    label: 'Required Policy History Lapsed Days Last 12 Months',
    condition: (value, fullObj: QuotePolicyHistory, data: AdditionalDataI) => {
      return !disabledIfPriorOrRenewingCarrierNotSet(fullObj)
             && Validate.isRequiredForSelectedPlansIfEmptyValue(value, lapsedDaysLast12MonthsCarriers, data.quoteSelectedPlansIds);
    },
    group: WARNING_GROUPS.carrier,
    carriers: lapsedDaysLast12MonthsCarriers
  }]
};


// Changes: 2018-08-19 :: https://bostonsoftware.atlassian.net/browse/SPR-3180
// Required For:
// Safeco, Progressive, Travelers, Cincinnati, Amica, Commerce, Hanover Tap Sales, National General, Plymouth Rock, MAPFRE
const priorBodilyInjurylimitsCarriers = ['25', '26', '27', '266', '3', '4', '281', '172', '28', '309', '302', '312', '291'];
const priorBodilyInjurylimits: WarningDefinitionI = {
  id: 'priorBodilyInjurylimits',
  deepId: 'priorBodilyInjurylimits',
  viewUri: generateViewUrl,
  viewFieldId: 'priorBodilyInjurylimits',
  warnings: [{
    label: 'Required Policy History Prior Bodily Injury Limits',
    // condition: false,//requiredField,
    condition: (value, quotePolicyHistory: QuotePolicyHistory, data: AdditionalDataI) => {
      return !disabledIfPriorOrRenewingCarrierNotSet(quotePolicyHistory) && Validate.isRequiredForSelectedPlansIfEmptyValue(value, priorBodilyInjurylimitsCarriers, data.quoteSelectedPlansIds);
    },
    group: WARNING_GROUPS.carrier,
    carriers: priorBodilyInjurylimitsCarriers
  }]
};

export const WARNINGS_DEFINITIONS_AUTO_OPTIONS_POLICY_HISTORY: WarningDefinitionI[] = [
  priorOrRenewingCarrier,
  priorPolicyExpirationDate,
  yearsWithThisCarrier,
  quoteThisCarrierAs,
  priorPolicyPolicyNumber,
  yearsWithCurrentAgency,
  lapsedDaysLast12Months,
  priorBodilyInjurylimits
];



// Options Tab, Carrier Options
// ------------------------------------------------------------------------------
/**
 * Validation for auto Carrier Options Data
 * For Model: PolicyItemParsed
 */


function validPolicyItemParsed(value, fullObj: CoverageItemParsed): boolean {
  // let isRequired = false;

  // switch (fullObj.inputType) {
  //   case 'Checkbox':
  //     isRequired = fullObj.isRequired && !fullObj.isActive;
  //   break;
  //   case 'Dropdown':
  //     isRequired = fullObj.isRequired && !fullObj.isActive && !fullObj.currentValue;
  //   break;
  //   case 'AgencyCode':
  //     isRequired = fullObj.isRequired && !fullObj.isActive;
  //   break;
  //   case 'Date':
  //     isRequired = fullObj.isRequired && !fullObj.isActive;
  //   break;
  //   case 'Text':
  //     isRequired = fullObj.isRequired && !fullObj.isActive;
  //   break;
  // }
  // return isRequired;
  return fullObj.isRequired && !fullObj.isActive && !fullObj.isDisabled;
}

function generateUqOptionViewId(policy: CoverageItemParsed): string {
  return policy.viewUqId;
}

function generateUqOptionViewInteractId(policy: CoverageItemParsed): string {
  return policy.viewUqId + '_modal-launcher';
  // return policy.viewUqId + '_checkbox';
}

const policyItemParsed: WarningDefinitionI = {
  id: 'currentValue',
  deepId: 'currentValue',
  viewUri: generateViewUrl,
  viewFieldId: generateUqOptionViewId,
  viewFieldInteractId: generateUqOptionViewInteractId,
  warnings: [{
    label: (val, fullObj: CoverageItemParsed) => 'Required ' + fullObj.description  + '.', // 'Required Policy History Prior Bodily Injury Limits',
    condition: validPolicyItemParsed,
    group: WARNING_GROUPS.carrier,
    carriers: (fullObj: CoverageItemParsed) => {
      if (fullObj.additionalData && fullObj.additionalData.requiredForCarriers && fullObj.additionalData.requiredForCarriers.length) {
        return fullObj.additionalData.requiredForCarriers;
      } else {
        return fullObj.ratingPlanId ? fullObj.ratingPlanId.split(',') : [];
      }
    }
  },
  {
    label: (val, fullObj: CoverageItemParsed) => 'Option: ' + fullObj.description  + ' is selected but no values are chosen for the option.', // https://bostonsoftware.atlassian.net/browse/SPR-2580
    condition: (val, fullObj: CoverageItemParsed) => {
    return Validate.coverageItemParsedErrorIfActiveAndNoSubOptionsSelected(fullObj);
    },
    group: WARNING_GROUPS.carrier,
    carriers: (fullObj: CoverageItemParsed) => {
      if (fullObj.additionalData && fullObj.additionalData.requiredForCarriers && fullObj.additionalData.requiredForCarriers.length) {
        return fullObj.additionalData.requiredForCarriers;
      } else {
        return fullObj.ratingPlanId ? fullObj.ratingPlanId.split(',') : [];
      }
    }
  },
  {
    label: (val, fullObj: CoverageItemParsed) => {
      if (fullObj.additionalData && fullObj.additionalData.errorMessage) {
        return fullObj.additionalData.errorMessage;
      } else {
        return 'Option: ' + fullObj.description + ' has some error.';
      }
    },
    condition: (val, fullObj: CoverageItemParsed) => {
      if (fullObj.additionalData && fullObj.additionalData.hasError) {
        return true;
      }

      return false;
    },
    group: WARNING_GROUPS.carrier,
    carriers: (fullObj: CoverageItemParsed) => {
      if (fullObj.additionalData && fullObj.additionalData.requiredForCarriers && fullObj.additionalData.requiredForCarriers.length) {
        return fullObj.additionalData.requiredForCarriers;
      } else {
        return fullObj.ratingPlanId ? fullObj.ratingPlanId.split(',') : [];
      }
    }
  },
  // Safety (planId - 11) -  https://bostonsoftware.atlassian.net/browse/SPR-2503
  {
    label: 'The Early Issuance discount, EBT and ALD discounts cannot be chosen on the same quote. Only two discounts are allowed on the same quote.', // 'Required Policy History Prior Bodily Injury Limits',
    condition: (val, fullObj: CoverageItemParsed, additionalData: AdditionalDataI) => {
      // Safety - Electronic Book Transfer BSC-AUTO-001757
      // Safety - Early Issuance Discount BSC-AUTO-002246
      // Safety - Agency Loyalty Discount BSC-AUTO-001710

      // Validate only for one option, to avoid duplicated warnings in warning panel
      if (additionalData.observedData && additionalData.observedData.length && fullObj.coverageCode === 'BSC-AUTO-001757') {
        const electronicBookTransfer: CoverageItemParsed = additionalData.observedData.find((opt: CoverageItemParsed) => opt.coverageCode === 'BSC-AUTO-001757');
        const earlyIssuanceDiscount: CoverageItemParsed = additionalData.observedData.find((opt: CoverageItemParsed) => opt.coverageCode === 'BSC-AUTO-002246');
        const agencyLoyaltyDiscount: CoverageItemParsed = additionalData.observedData.find((opt: CoverageItemParsed) => opt.coverageCode === 'BSC-AUTO-001710');
        // const agencyLoyaltyDiscount: CoverageItemParsed = additionalData.observedData.find((opt: CoverageItemParsed) => opt.coverageCode === 'BSC-AUTO-001736'); // TEST ONLY

        // console.log('Options: ', electronicBookTransfer, earlyIssuanceDiscount, agencyLoyaltyDiscount);

        if (electronicBookTransfer && electronicBookTransfer.isActive
            && earlyIssuanceDiscount && earlyIssuanceDiscount.isActive
            && agencyLoyaltyDiscount && agencyLoyaltyDiscount.isActive
          ) {
            return true;
        }
      }

      return false;
    },
    group: WARNING_GROUPS.carrier,
    carriers: ['11']
  }]
};

export const WARNINGS_DEFINITIONS_AUTO_OPTIONS_CARRIER_OPTIONS: WarningDefinitionI[] = [
  policyItemParsed
];
