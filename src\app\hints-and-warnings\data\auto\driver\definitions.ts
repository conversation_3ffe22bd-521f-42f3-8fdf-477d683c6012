import { WARNING_GROUPS, WarningDefinitionI, AdditionalDataI, AdditionalDataAutoDrivers} from 'app/hints-and-warnings/model/warnings';
import { Driver } from 'app/app-model/driver';
import { generateDriverName } from 'app/dashboard/app-services/drivers.service';
import { Validate} from 'app/hints-and-warnings/validators';
import { Vehicle } from 'app/app-model/vehicle';
import { CoverageItemParsedForAutoDriverOptionsHintsAndWarnings } from '../../../../app-model/coverage';
import { IncidentForHintsAndWarnings, IncidentTypeDesc, IncidentType } from 'app/app-model/incident';


function requiredField(value): boolean {
  return Validate.isEmptyValue(value);
}

function checkValidMALicense(value,fullObj) {
  const regex = /^[Ss](A|a)[0-9]{7}$|^[Ss][0-9]{8}$/;
    const isValid = regex.test(value);
    if(!isValid && fullObj.licenseState === 'MA') {
      return true
    }

    return false
}

function requiredRadioInputValue(value, fullObj: Driver): boolean {
  return Validate.isRadioInputValueNotSet(value);
}

function generateViewUrl(fullObj: Driver): string {
  return  '/dashboard/auto' + fullObj.meta.href; // + '?overlay=info&type=client';
}

/**
 * Validation for Driver Data
 * For Model: Driver
 */

const driverFirstName: WarningDefinitionI = {
  id: 'firstName',
  deepId: 'firstName',
  viewUri: generateViewUrl,
  viewFieldId: 'driverFirstName',
  warnings: [{
    label: (value, fullObj) => 'Required First Name for driver ' + generateDriverName(fullObj) + '.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const driverLastName: WarningDefinitionI = {
  id: 'lastName',
  deepId: 'lastName',
  viewUri: generateViewUrl,
  viewFieldId: 'driverLastName',
  warnings: [{
    label: (value, fullObj) => 'Required Last Name for driver ' + generateDriverName(fullObj) + '.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const driverDateOfBirth: WarningDefinitionI = {
  id: 'dateOfBirth',
  deepId: 'dateOfBirth',
  viewUri: generateViewUrl,
  viewFieldId: 'driverDateOfBirth',
  warnings: [{
    label: (value, fullObj) => 'Required Date of Birth for driver ' + generateDriverName(fullObj) + '.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const driverGender: WarningDefinitionI = {
  id: 'gender',
  deepId: 'gender',
  viewUri: generateViewUrl,
  viewFieldId: 'driverGender',
  warnings: [{
    label: (value, fullObj) => 'Required Gender for driver ' + generateDriverName(fullObj) + '.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

// Required for: Hanover New
const driverMaritalStatusCarriers = ['281', '312'];
const driverMaritalStatus: WarningDefinitionI = {
  id: 'maritalStatus',
  deepId: 'maritalStatus',
  viewUri: generateViewUrl,
  viewFieldId: 'driverMaritalStatus',
  warnings: [{
    label: (value, fullObj) => 'Required Marital Status for driver ' + generateDriverName(fullObj) + '.',
    condition: (value, driver: Driver, data: AdditionalDataI) => {
      return Validate.isRequiredForSelectedPlansIfEmptyValue(value, driverMaritalStatusCarriers, data.quoteSelectedPlansIds);
    },
    group: WARNING_GROUPS.carrier,
    carriers: driverMaritalStatusCarriers
  }]
};


// Required for plans/carriers: Hanover, Safeco, Progressive, Travelers, Hanover New, Bristol West, Nat Gen Value
// Safety
const driverRelationshipToInsuredCarriers = ['20', '25', '26', '27', '281', '11', '302', '309', '312', '321'];
const driverRelationshipToInsured: WarningDefinitionI = {
  id: 'relationshipToInsured',
  deepId: 'relationshipToInsured',
  viewUri: generateViewUrl,
  viewFieldId: 'driverRelationshipToInsured',
  warnings: [{
    label: (value, fullObj) => 'Required Relationship to insured for driver ' + generateDriverName(fullObj) + '.',
    condition: (value, driver: Driver, data: AdditionalDataI) => {
      return Validate.isRequiredForSelectedPlansIfEmptyValue(value, driverRelationshipToInsuredCarriers, data.quoteSelectedPlansIds);
    },
    group: WARNING_GROUPS.carrier,
    carriers: driverRelationshipToInsuredCarriers
  }]
};


const driverLicenseNumber: WarningDefinitionI = {
  id: 'licenseNumber',
  deepId: 'licenseNumber',
  viewUri: generateViewUrl,
  viewFieldId: 'driverLicenseNumber',
  warnings: [{
    label: (value, fullObj) => 'Driver License Number required for ' + generateDriverName(fullObj) + '.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const driverLicenseNumberFormat: WarningDefinitionI = {
  id: 'licenseNumber',
  deepId: 'licenseNumber',
  viewUri: generateViewUrl,
  viewFieldId: 'driverLicenseNumber',
  warnings: [{
    label: (value, fullObj) => 'Format of MA license number is invalid for ' + generateDriverName(fullObj) + '.',
    condition: checkValidMALicense,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const driverFirstLicensed: WarningDefinitionI = {
  id: 'firstLicensed',
  deepId: 'firstLicensed',
  viewUri: generateViewUrl,
  viewFieldId: 'driverFirstLicensed',
  warnings: [{
    label: (value, fullObj) => 'Driver First Licensed Date required for ' + generateDriverName(fullObj) + '.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

/*
const driverFirstLicensedState:WarningDefinitionI = {
  id: 'firstLicensedState',
  deepId: 'firstLicensedState',
  viewUri: generateViewUrl,
  viewFieldId: 'driverFirstLicensedState',
  warnings: [{
    label: (value, fullObj) => 'Driver First Licensed State required for ' + generateDriverName(fullObj) + '.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}
*/

// If 'First Licensed' is within the last 6 years, not required.
/*
const driverCurrentLicense:WarningDefinitionI = {
  id: 'licenseDate',
  deepId: 'licenseDate',
  viewUri: generateViewUrl,
  viewFieldId: 'driverCurrentLicense',
  warnings: [{
    label: (value, fullObj) => 'Driver Current License Date required for ' + generateDriverName(fullObj) + ' or needs to be updated.',
    condition: (value, fullObj:Driver):boolean => {

      if (fullObj.firstLicensed) {
        let currDate:any = new Date();
        currDate.setHours(0);
        currDate.setMinutes(0);
        currDate.setSeconds(0);
        currDate.setMilliseconds(0);
        let licenseDate = null;
        let difference = null;

        try {
          licenseDate = new Date(fullObj.firstLicensed);
          difference = moment.duration(currDate - licenseDate).years();
        } catch(err) {}

        if (difference != null && difference >= 6) {
          return true;
        }
      }

      return false;
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}
*/

// Current State
const driverCurrentLicenseState: WarningDefinitionI = {
  id: 'licenseState',
  deepId: 'licenseState',
  viewUri: generateViewUrl,
  viewFieldId: 'driverCurrentLicenseState',
  warnings: [{
    label: (value, fullObj) => 'Driver Current License State required for ' + generateDriverName(fullObj) + '.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};


const driverSdip: WarningDefinitionI = {
  id: 'sdip',
  deepId: 'sdip',
  viewUri: generateViewUrl,
  viewFieldId: 'driverSdip',
  warnings: [{
    label: (value, fullObj) => 'SDIP required for ' + generateDriverName(fullObj) + '.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

// Deffered Driver (radio)
const driverDefferedDriver: WarningDefinitionI = {
  id: 'deferredDriver',
  deepId: 'deferredDriver',
  viewUri: generateViewUrl,
  viewFieldId: 'driverDefferedDriver',
  warnings: [{
    label: (value, fullObj) => 'Deffered Driver required for ' + generateDriverName(fullObj) + '.',
    condition: requiredRadioInputValue,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

// Motorcycle Driver (radio)
const driverMotorcycleDriver: WarningDefinitionI = {
  id: 'motorcycle',
  deepId: 'motorcycle',
  viewUri: generateViewUrl,
  viewFieldId: 'driverMotorcycleDriver',
  warnings: [{
    label: (value, fullObj) => 'Motorcycle required for ' + generateDriverName(fullObj) + '.',
    condition: requiredRadioInputValue,
    group: WARNING_GROUPS.general,
    carriers: []
  },
  {
    label: (value, fullObj) => 'Motorcycle for ' + generateDriverName(fullObj) + ' needs to be set to "Yes".',
    // condition: requiredRadioInputValue,
    condition: (value, fullObj: Driver, additionalData: AdditionalDataAutoDrivers) => {
      if (additionalData && additionalData.vehicles) {
        const motorcycleVehiclesWithDriverAsOperator: Vehicle[] = additionalData.vehicles.filter((vehicle: Vehicle) => {
          return vehicle.vehicleType === 'Motorcycle' && vehicle.operator.meta.href === fullObj.meta.href && fullObj.motorcycle !== true;
        });

        return motorcycleVehiclesWithDriverAsOperator.length > 0;
      }

      return false;
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

// Motorcycle Driver Type - If 'Motorcyle Driver' is set to 'Yes', then selector appears and is required.
const driverMotorcycleLicenseType: WarningDefinitionI = {
  id: 'motorcycleLicenseType',
  deepId: 'motorcycleLicenseType',
  viewUri: generateViewUrl,
  viewFieldId: 'driverMotorcycleLicenseType',
  warnings: [{
    label: (value, fullObj) => 'Motorcycle License Type required for ' + generateDriverName(fullObj) + '.',
    condition: (value: any, fullObj: Driver) => {
      return fullObj.motorcycle && fullObj.motorcycle === true && (value !== undefined && value.trim().length <= 0 || !value);
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};



// Motorcycle Driver License Date - If 'Motorcyle Driver Type' is set to 'License', then date picker appears and is required.
const driverMotorcycleLicenseDate: WarningDefinitionI = {
  id: 'motorcycleLicenseDate',
  deepId: 'motorcycleLicenseDate',
  viewUri: generateViewUrl,
  viewFieldId: 'driverMotorcycleLicenseDate',
  warnings: [{
    label: (value, fullObj) => 'Motorcycle License Date required for ' + generateDriverName(fullObj) + '.',
    condition: (value: any, fullObj: Driver) => {
      return fullObj.motorcycleLicenseType && fullObj.motorcycleLicenseType === 'License' && (value !== undefined && value.trim().length <= 0 || !value);
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};


export const WARNINGS_DEFINITIONS_AUTO_DRIVER: WarningDefinitionI[] = [
  // TEST,
  driverFirstName,
  driverLastName,
  driverDateOfBirth,
  // driverGender,
  driverMaritalStatus,
  driverRelationshipToInsured,
  driverLicenseNumber,
  driverFirstLicensed,
  // driverFirstLicensedState,
  // driverCurrentLicense,
  driverCurrentLicenseState,
  driverSdip,
  // driverDefferedDriver,
  driverMotorcycleDriver,
  driverMotorcycleLicenseType,
  driverMotorcycleLicenseDate,
  driverLicenseNumberFormat
];


// Drivers Tab, Driver Options And Coverages
// ------------------------------------------------------------------------------
/**
 * Validation for auto Carrier Options Data
 * For Model: PolicyItemParsed
 */


function validPolicyItemParsed(value, fullObj: CoverageItemParsedForAutoDriverOptionsHintsAndWarnings): boolean {
  return fullObj.isRequired && !fullObj.isActive && !fullObj.isDisabled;
}

function generateUqOptionViewId(policy: CoverageItemParsedForAutoDriverOptionsHintsAndWarnings): string {
  return policy.viewUqId;
}

function generateUqOptionViewInteractId(policy: CoverageItemParsedForAutoDriverOptionsHintsAndWarnings): string {
  return policy.viewUqId + '_modal-launcher';
  // return policy.viewUqId + '_checkbox';
}

function generateOptionsViewUrl(policy: CoverageItemParsedForAutoDriverOptionsHintsAndWarnings): string {
  return  '/dashboard/auto' + policy.driver.meta.href; // + '?overlay=info&type=client';
}

const policyItemParsed: WarningDefinitionI = {
  id: 'currentValue',
  deepId: 'currentValue',
  viewUri: generateOptionsViewUrl,
  viewFieldId: generateUqOptionViewId,
  viewFieldInteractId: generateUqOptionViewInteractId,
  warnings: [{
    label: (val, fullObj: CoverageItemParsedForAutoDriverOptionsHintsAndWarnings) => 'Required ' + fullObj.description  + ' for driver ' + generateDriverName(fullObj.driver) + '.', // 'Required Policy History Prior Bodily Injury Limits',
    condition: validPolicyItemParsed,
    group: WARNING_GROUPS.carrier,
    carriers: (fullObj: CoverageItemParsedForAutoDriverOptionsHintsAndWarnings) => {
      if (fullObj.additionalData && fullObj.additionalData.requiredForCarriers && fullObj.additionalData.requiredForCarriers.length) {
        return fullObj.additionalData.requiredForCarriers;
      } else {
        return fullObj.ratingPlanId ? fullObj.ratingPlanId.split(',') : [];
      }
    }
  },
  {
    label: (val, fullObj: CoverageItemParsedForAutoDriverOptionsHintsAndWarnings) => 'Option: ' + fullObj.description  + ' for driver ' + generateDriverName(fullObj.driver) + ' is selected but no values are chosen for the option.', // https://bostonsoftware.atlassian.net/browse/SPR-2580
    condition: (val, fullObj: CoverageItemParsedForAutoDriverOptionsHintsAndWarnings) => {
    // return return fullObj.isRequired && !fullObj.isActive && !fullObj.isDisabled;
    return Validate.coverageItemParsedErrorIfActiveAndNoSubOptionsSelected(fullObj);
    },
    group: WARNING_GROUPS.carrier,
    carriers: (fullObj: CoverageItemParsedForAutoDriverOptionsHintsAndWarnings) => {
      if (fullObj.additionalData && fullObj.additionalData.requiredForCarriers && fullObj.additionalData.requiredForCarriers.length) {
        return fullObj.additionalData.requiredForCarriers;
      } else {
        return fullObj.ratingPlanId ? fullObj.ratingPlanId.split(',') : [];
      }
    }
  }, ]
};

export const WARNINGS_DEFINITIONS_AUTO_DRIVERS_OPTIONS_AND_COVERAGES: WarningDefinitionI[] = [
  policyItemParsed
];

// Drivers Tab, Driver Incidents
// ------------------------------------------------------------------------------

function generateIncidentsViewUrl(fullObj: IncidentForHintsAndWarnings): string {
  return  '/dashboard/auto/quotes/' + fullObj.quoteSessionId + '/drivers/' + fullObj.parentId; // + '?overlay=info&type=client';
}

function generateIncidentsViewId(fullObj: IncidentForHintsAndWarnings): string {
  return fullObj.resourceId + '_incident';
}

function generateIncidentsViewInteractId(fullObj: IncidentForHintsAndWarnings): string {
  return 'incident-btn_' + fullObj.resourceId;
}


// https://bostonsoftware.atlassian.net/browse/SPR-2812
// Safeco (ratingPlanId 25) or Progressive (ratingPlanId 26)
const driverIncidentDescriptionCarriers = ['25', '26', '309', '311', '302', '321', '281'];
const driverIncidentDescription: WarningDefinitionI = {
  id: 'descOfIncident',
  deepId: 'descOfIncident',
  viewUri: generateIncidentsViewUrl,
  viewFieldId: generateIncidentsViewId,
  viewFieldInteractId: generateIncidentsViewInteractId,
  warnings: [{
    label: (value, fullObj) => 'Incident description is empty or incorrect - for driver ' + generateDriverName(fullObj.driver),
    condition: (value: any, fullObj: IncidentForHintsAndWarnings, additionalData: AdditionalDataI) => {
      const selectedType = (fullObj.typeOfIncident) ? fullObj.typeOfIncident.trim().toLowerCase() : '';
      const selectedDesc = (fullObj.descOfIncident) ? fullObj.descOfIncident.trim().toLowerCase() : '';

      if (selectedType && selectedDesc) {
        let selectedDescValueAllowed: IncidentTypeDesc;
        const incidentTypeData: IncidentType = fullObj.incidentTypes.find((el: IncidentType) => el.name.trim().toLowerCase() === selectedType);

        if (incidentTypeData) {
          selectedDescValueAllowed = incidentTypeData.options.find((inc: IncidentTypeDesc) => inc.name.trim().toLowerCase() === selectedDesc);
        }

        if (selectedDescValueAllowed) {
          return false;
        }
      }

      return Validate.isRequiredForSelectedPlans(driverIncidentDescriptionCarriers, additionalData.quoteSelectedPlansIds);
      // return true;
    },
    group: WARNING_GROUPS.carrier,
    carriers: driverIncidentDescriptionCarriers
  }]
};

export const WARNINGS_DEFINITIONS_AUTO_DRIVERS_INCIDENTS: WarningDefinitionI[] = [
  driverIncidentDescription
];
