import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { QuoteImporterComponent } from './quote-importer.component';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { RouterTestingModule } from '@angular/router/testing';
import { StorageService } from 'app/shared/services/storage-new.service';

describe('QuoteImporterComponent', () => {
  let component: QuoteImporterComponent;
  let fixture: ComponentFixture<QuoteImporterComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      declarations: [ QuoteImporterComponent ],
      providers: [QuotesService, StorageService, OverlayLoaderService]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(QuoteImporterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
