<section class="section section-with-subnav">
  <nav class="section-with-subnav--nav">
      <span *ngFor="let item of navigationList; let last = last;">
        <a *ngIf="!item.type"
          routerLink="/dashboard/{{item.link}}"
          routerLinkActive="is-active"
          [queryParams]=""
          [ngClass]="{'is-active': checkRoute() }"
          [routerLinkActiveOptions]="{ exact: true }"
          (click)="isClicked('')"
          class="o-btn o-btn--pure u-spacing--left-2">
            {{ item.text}}
        </a>
        <a *ngIf="item.type === 'commercial'"
        #commercialrla="routerLinkActive"
        routerLink="/dashboard/{{item.link}}"
        routerLinkActive="is-active"
        (click)="isClicked('Commercial')"
        [queryParams]="{lob: 'commercial'}"
        [routerLinkActiveOptions]="{ exact: true }"
        class="o-btn o-btn--pure u-spacing--left-2"
        >
          {{ item.text}}
      </a>
        <span *ngIf="!last" class="o-separator o-separator--base"></span>
      </span>
  </nav>
</section>
