import { async, ComponentFixture, TestBed, inject, fakeAsync } from '@angular/core/testing';

import { StubAutocompleteComponent } from 'testing/stubs/components/autocomplete.component';
import { StubDwellingServiceProvider, StubDwellingService } from 'testing/stubs/services/dwelling.service.provider';
import {
    StubOverlayLoaderServiceProvider
} from 'testing/stubs/services/overlay-loader.service.provider';

import { StorageService } from 'app/shared/services/storage-new.service';

import {
    DwellingDwellingProtectionInformationComponent
} from './dwelling-dwelling-protection-information.component';
import { DWELLING_LOCATION } from 'testing/data/quotes/locations/dwelling';
import { Helpers } from 'app/utils/helpers';
import { DWELLING_QUOTE_PLAN_LIST } from 'testing/data/quotes/quote-plan-list/dwelling';
import { DWELLINGS } from 'testing/data/quotes/dwellings';
import { DWELLING_PROTECTION_CLASS } from 'testing/data/dwellings/protection-class';
import { DwellingService } from '../../../app-services/dwelling.service';

describe('Component: DwellingDwellingProtectionInformation', () => {
  let component: DwellingDwellingProtectionInformationComponent;
  let fixture: ComponentFixture<DwellingDwellingProtectionInformationComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [
        DwellingDwellingProtectionInformationComponent,
        StubAutocompleteComponent
      ],
      providers: [
        StubOverlayLoaderServiceProvider,
        StorageService,
        StubDwellingServiceProvider
      ]
    })
    .compileComponents();
  }));

  describe('when all data is available in storage', () => {
    beforeEach(fakeAsync(inject([StorageService], (storageService: StorageService) => {
      storageService.setStorageData('dwellingLocation', Helpers.deepClone(DWELLING_LOCATION.items[0]));
      storageService.setStorageData('selectedPlan', Helpers.deepClone(DWELLING_QUOTE_PLAN_LIST));
      storageService.setStorageData('dwelling', Helpers.deepClone(DWELLINGS.items[0]));
      storageService.setStorageData('dwellingProtectionClass', Helpers.deepClone(DWELLING_PROTECTION_CLASS.items[0]));

      fixture = TestBed.createComponent(DwellingDwellingProtectionInformationComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
    })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });

    it('should destroy without errors', () => {
      expect(() => {
        fixture.destroy();
      }).not.toThrow();
    });
  });

  describe('when some data is not available in storage', () => {
    beforeEach(fakeAsync(inject([StorageService, DwellingService],
      (storageService: StorageService, dwellingService: StubDwellingService) => {
        storageService.setStorageData('dwellingLocation', Helpers.deepClone(DWELLING_LOCATION.items[0]));
        storageService.setStorageData('selectedPlan', Helpers.deepClone(DWELLING_QUOTE_PLAN_LIST));
        storageService.setStorageData('dwelling', Helpers.deepClone(DWELLINGS.items[0]));

        dwellingService.dwellingProtectionClass = Helpers.deepClone(DWELLING_PROTECTION_CLASS);

        fixture = TestBed.createComponent(DwellingDwellingProtectionInformationComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
    })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });
  });
});
