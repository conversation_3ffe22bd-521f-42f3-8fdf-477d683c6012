import { async, ComponentFixture, inject, TestBed, tick } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { RouterTestingModule } from '@angular/router/testing';

import { StubDatepickerModalComponent } from 'testing/stubs/components/datepicker-modal.component';
import { StubLeaveQuoteComponent } from 'testing/stubs/components/leave-quote.component';
import { StubModalboxComponent } from 'testing/stubs/components/modalbox.component';
import { StubPlansSelectorComponent } from 'testing/stubs/components/plans-selector.component';
import { StubSelectComponent } from 'testing/stubs/components/select.component';
import { StubdateFormatPipe } from 'testing/stubs/pipes/am-date-format.pipe';
import { StubAgencyUserServiceProvider } from 'testing/stubs/services/agency-user.service.provider';
import {
    StubOverlayLoaderServiceProvider
} from 'testing/stubs/services/overlay-loader.service.provider';
import { StubPremiumsServiceProvider } from 'testing/stubs/services/premiums.service.provider';
import {
    StubQuotesService, StubQuotesServiceProvider
} from 'testing/stubs/services/quotes.service.provider';
import { StubSubsServiceProvider } from 'testing/stubs/services/subs.service.provider';

import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { StorageService } from 'app/shared/services/storage-new.service';

import { AsideUmbrellaQuoteComponent } from './aside-umbrella-quote.component';

describe('Component: AsideUmbrellaQuoteComponent', () => {
  let component: AsideUmbrellaQuoteComponent;
  let fixture: ComponentFixture<AsideUmbrellaQuoteComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [
        FormsModule,
        RouterTestingModule
      ],
      declarations: [
        AsideUmbrellaQuoteComponent,
        StubSelectComponent,
        StubdateFormatPipe,
        StubDatepickerModalComponent,
        StubModalboxComponent,
        StubLeaveQuoteComponent,
        StubPlansSelectorComponent
      ],
      providers: [
        StorageService,
        StubQuotesServiceProvider,
        StubOverlayLoaderServiceProvider,
        StubAgencyUserServiceProvider,
        StubSubsServiceProvider,
        StubPremiumsServiceProvider,
        StorageGlobalService
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AsideUmbrellaQuoteComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
