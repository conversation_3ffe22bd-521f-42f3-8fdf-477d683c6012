<section class="section section--compact u-spacing--2-5">
  <div class="row">
    <h1 class="o-heading" style="color: #0b71ac; padding-left: 20px;">Owners</h1>
  </div>
  <div class="row u-spacing--1">
    <div class="col-xs-12">
      <div class="box box--silver">
        <table class="form-table">
          <tr class="form-table__row" *ngFor="let owner of owners; let index = index">
            <td class="form-table__cell u-width-80px">
              <label for="">Owner {{index + 1}}:</label>
            </td>
            <td class="form-table__cell">
              <input [(ngModel)]="owner.firstName" name="firstName{{index}}" [required]="index === 0" placeholder="First Name">
            </td>
            <td class="form-table__cell">
              <input [(ngModel)]="owner.lastName" name="lastName{{index}}" [required]="index === 0" placeholder="Last Name">
            </td>
            <td class="form-table__cell">
              <input [(ngModel)]="owner.license" name="license{{index}}" [required]="index === 0" placeholder="License #">
            </td>
            <td class="form-table__cell">
      <sm-autocomplete style="width: 60px;" [options]="stateOptions" [(ngModel)]="owner.licenseState" [activeOption]="owner.licenseState" name="licenseState{{index}}"></sm-autocomplete>  
            </td>
            <td class="form-table__cell">
              <app-datepicker-input #refPickerDateOfBirth [placeholder]="'MM/dd/yyyy'"
              [required]="index===0" [(ngModel)]="owner.dateOfBirth" name="dateOfBirth{{index}}" ngDefaultControl
                (onDateChange)="setDate($event,owner)">
              </app-datepicker-input>
            </td>
          </tr>
        </table>
      </div>
    </div>
  </div>
</section>
