import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { AutoAdditionalCoveragesAutomanagerComponent } from './auto-additional-coverages-automanager.component';
import { AutoAdditionalCoveragesAutomanagerService } from './auto-additional-coverages-automanager.service';

class StubAutoAdditionalCoveragesAutomanagerService {
  public initialize(): void {
    return;
  }

  public destroy(): void {
    return;
  }
}

describe('AutoAdditionalCoveragesAutomanagerComponent', () => {
  let component: AutoAdditionalCoveragesAutomanagerComponent;
  let fixture: ComponentFixture<AutoAdditionalCoveragesAutomanagerComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      providers: [{
        provide: AutoAdditionalCoveragesAutomanagerService,
        useClass: StubAutoAdditionalCoveragesAutomanagerService
      }],
      declarations: [ AutoAdditionalCoveragesAutomanagerComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AutoAdditionalCoveragesAutomanagerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
