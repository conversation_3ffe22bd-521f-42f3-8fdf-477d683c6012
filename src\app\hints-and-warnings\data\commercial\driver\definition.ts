import { ComDriver } from './../../../../app-model/driver';
import { WARNING_GROUPS, WarningDefinitionI, AdditionalDataI, AdditionalDataAutoDrivers} from 'app/hints-and-warnings/model/warnings';
import { generateDriverName } from 'app/dashboard/app-services/drivers.service';
import { Validate} from 'app/hints-and-warnings/validators'
import { Vehicle } from 'app/app-model/vehicle';
import { CoverageItemParsedForAutoDriverOptionsHintsAndWarnings } from '../../../../app-model/coverage';
import { IncidentForHintsAndWarnings, IncidentTypeDesc, IncidentType } from 'app/app-model/incident';


let index;
function requiredField(value):boolean {
  return Validate.isEmptyValue(value);
}

function requiredRadioInputValue(value, fullObj:ComDriver):boolean {
  return Validate.isRadioInputValueNotSet(value);
}

function returnLastNameIndex(fullObj:ComDriver):string {
 return `driverLastName_${fullObj.orderIndex}`;
}

function returnFirstNameIndex(fullObj:ComDriver):string {
  return `driverFirstName_${fullObj.orderIndex}`;
 }

 function returnDobIndex(fullObj:ComDriver):string {
  return `driverDob_${fullObj.orderIndex}`;
 }

 function returnLicenseIndex(fullObj:ComDriver):string {
  return `driverLicenseNumber_${fullObj.orderIndex}`;
 }
 function returnStateIndex(fullObj:ComDriver):string {
  return `driverLicenseState_${fullObj.orderIndex}`;
 }

function generateViewUrl(fullObj:ComDriver):string {
  index = fullObj.orderIndex;
  return  `/dashboard/commercial-auto/quotes/${fullObj.quoteSessionId}/drivers`;// + '?overlay=info&type=client';
}

const driverFirstName:WarningDefinitionI = {
  id: 'firstName',
  deepId: 'firstName',
  viewUri: generateViewUrl,
  viewFieldId: returnFirstNameIndex,
  warnings: [{
    label: (value, fullObj) => 'Required First Name for driver ' + generateDriverName(fullObj) + '.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}

const driverLastName:WarningDefinitionI = {
  id: 'lastName',
  deepId: 'lastName',
  viewUri: generateViewUrl,
  viewFieldId: returnLastNameIndex,
  warnings: [{
    label: (value, fullObj) => 'Required Last Name for driver ' + generateDriverName(fullObj) + '.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}

const driverDateOfBirth:WarningDefinitionI = {
  id: 'dateOfBirth',
  deepId: 'dateOfBirth',
  viewUri: generateViewUrl,
  viewFieldId: returnDobIndex,
  warnings: [{
    label: (value, fullObj) => 'Required Date of Birth for driver ' + generateDriverName(fullObj) + '.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}

const driverLicense:WarningDefinitionI = {
  id: 'licenseNumber',
  deepId: 'licenseNumber',
  viewUri: generateViewUrl,
  viewFieldId: returnLicenseIndex,
  warnings: [{
    label: (value, fullObj) => 'Required License Number for driver ' + generateDriverName(fullObj) + '.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}

const driverLicenseState:WarningDefinitionI = {
  id: 'licenseState',
  deepId: 'licenseState',
  viewUri: generateViewUrl,
  viewFieldId: returnStateIndex,
  warnings: [{
    label: (value, fullObj) => 'Required License State for driver ' + generateDriverName(fullObj) + '.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}


export const WARNINGS_DEFINITIONS_COM_AUTO_DRIVER:WarningDefinitionI[] = [
  driverFirstName,
  driverLastName,
  driverDateOfBirth,
  driverLicense,
  driverLicenseState
]
