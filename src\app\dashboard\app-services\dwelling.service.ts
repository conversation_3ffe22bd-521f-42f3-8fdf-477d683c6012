
import {map} from 'rxjs/operators';
import { LocationData } from 'app/app-model/location';
import { Injectable } from '@angular/core';
import { ApiService } from 'app/shared/services/api.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import {
  Dwelling,
  DwellingProtectionClass,
  DwellingProtectionDevices,
  DwellingsListAPIResponse,
  DwellingsSubsystemsAPIResponse,
  DwellingSubsystems,
  DwellingProtectionClassApiResponse,
  LossHistory
} from 'app/app-model/dwelling';
import { Quote } from 'app/app-model/quote';
import {parseISO, differenceInDays} from 'date-fns'


@Injectable()
export class   DwellingService {

  // Dwelling
  // -------------------------------------------------------------------------------

  constructor(
    private http: HttpClient,
    private apiService: ApiService,
    private quotesService: QuotesService,
    private storageService: StorageService,
    private specsService: SpecsService,
    private apiCommonService: ApiCommonService
   ) { }

  public createDwellingByUri(uri: string, dwellingData: Dwelling): Observable<any> {
    return this.apiCommonService.postByUri(uri, dwellingData);
  }

  public updateDwelling(data): Observable<any> {
    return this.http.put(this.apiService.url(data.meta.href), data).pipe(map((res: any) => {
      this.storageService.setStorageData('dwelling', res);

      return res;
    }));
  }

  public updateDwellingByUri(uri: string, dwellingData: Dwelling): Observable<any> {
    return this.apiCommonService.putByUri(uri, dwellingData);
  }

  public getDwellingsListByUri(uri: string): Observable<DwellingsListAPIResponse> {
    return this.apiCommonService.getByUri(uri);
  }

  public createDwellingLocation(uri: string): Observable<any> {
    return this.http.post(this.apiService.url(uri), {}).pipe(map((res: any) => {
      this.storageService.setStorageData('dwellingLocation', res);
      return res;
    }));
  }

  public getDwellingLocation(uri: string): Observable<any> {
    return this.http.get(this.apiService.url(uri)).pipe(map((res: any) => {
      const toSave = (res.items && res.items[0]) ? res.items[0] : new LocationData();
      this.storageService.setStorageData('dwellingLocation', toSave);
      return res;
    }));
  }

  public updateDwellingLocation(data, updateStorage: boolean = true): Observable<any> {
    return this.http.put(this.apiService.url(data.meta.href), data).pipe(map((res: any) => {
      if (updateStorage) {
        this.storageService.setStorageData('dwellingLocation', res);
      }
      return res;
    }));
  }

  public createDwellingProtectionDevices(uri: string): Observable<any> {
    return this.apiCommonService.postByUri(uri, {});
  }

  public getDwellingProtectionDevices(uri: string): Observable<any> {
    return this.apiCommonService.getByUri(uri);
  }

  public updateDwellingProtectionDevices(data): Observable<any> {
    return this.apiCommonService.putByUri(data.meta.href, data);
  }

  // Dwelling Protection Class
  // -------------------------------------------------------------------------------

  public createDwellingProtectionClass(uri: string): Observable<DwellingProtectionClass> {
    const emptyObj: DwellingProtectionClass = new DwellingProtectionClass();
    return this.apiCommonService.postByUri(uri, emptyObj);
  }

  public getDwellingProtectionClass(uri: string): Observable<DwellingProtectionClassApiResponse> {
   return this.apiCommonService.getByUri(uri);
  }

  public updateDwellingProtectionClass(uri: string, data: DwellingProtectionClass): Observable<DwellingProtectionClass> {
    return this.apiCommonService.putByUri(uri, data);
  }

  public createDwellingProtectionClassOverrides(uri: string): Observable<any> {
    return this.apiCommonService.postByUri(uri, {items: []});
  }

  public getDwellingProtectionClassOverrides(uri: string): Observable<any> {
    return this.apiCommonService.getByUri(uri);
  }

  public updateDwellingProtectionClassOverrides(uri: string, data): Observable<any> {
    return this.apiCommonService.putByUri(uri, data);
  }

  // Scheduled Property
  // -------------------------------------------------------------------------------
  public createScheduledProperty(uri: string): Observable<any> {
    const emptyObj = {items: []};
    return this.apiCommonService.postByUri(uri, emptyObj);
  }

  public updateScheduledProperty(uri: string, data): Observable<any> {
    return this.apiCommonService.putByUri(uri, data);
  }

  public getScheduledPropertyList(quoteId: string): Observable<any> {
    const uri = '/quotes/' + quoteId + '/scheduledProperty';
    return this.apiCommonService.getByUri(uri);
  }

  public getScheduledProperty(quoteId: string, resourceId: string): Observable<any> {
    const uri = '/quotes/' + quoteId + '/scheduledProperty/' + resourceId;
    return this.apiCommonService.getByUri(uri);
  }

  // Dwelling subsystems
  public createDwellingSubsystem(uri: string): Observable<DwellingSubsystems> {
    return this.http.post(this.apiService.url(uri), {}).pipe(map((res: DwellingSubsystems) => {
      this.storageService.updateDwellingSubsystems(res);
      return res;
    }));
  }

  public getDwellingSubsystem(uri: string): Observable<DwellingsSubsystemsAPIResponse> {
    return this.http.get(this.apiService.url(uri)).pipe(map((res: DwellingsSubsystemsAPIResponse) => {
      this.storageService.setStorageData('dwellingSubsystems', res);
      return res;
    }));
  }

  public updateDwellingSubsystem(data): Observable<DwellingSubsystems> {
    let uri = '';
    if (data) {
      if (data.meta && data.meta.href) {
        uri = data.meta.href;
      } else {
        uri = data.href;
      }
    }
    return this.http.put(this.apiService.url(uri), data).pipe(map((res: DwellingSubsystems) => {
      this.storageService.updateDwellingSubsystems(res);
      return res;
    }));
  }

  // Dwelling Loss History (Underwriting)
  public createLossHistory(quoteId: string): Observable<any> {
    const uri: string =  '/quotes/' + quoteId + '/lossHistory';
    return this.apiCommonService.postByUri(uri, {});
  }

  public getLossHistory(quoteId: string): Observable<any> {
    const uri: string =  '/quotes/' + quoteId + '/lossHistory';
    return this.apiCommonService.getByUri(uri);
  }

  public updateLossHistory(uri: string, data: LossHistory): Observable<any> {
    return this.apiCommonService.putByUri(uri, data);
  }

public helpUpdateLossHistoryForHintsAndWarnings(lossHistory: LossHistory, quote: Quote): LossHistory {
  let effectiveDate: Date | undefined;
  if (quote && quote.effectiveDate) {
    effectiveDate = parseISO(quote.effectiveDate);
  }

  if (lossHistory && lossHistory.items && lossHistory.items.length && effectiveDate) {
    lossHistory.items.forEach(item => {
      const lossDetailDate = parseISO(item.lossDate);

      // Calculate the difference in months as a float
      const daysDiff = differenceInDays(effectiveDate, lossDetailDate);
      const floatMonths = daysDiff / 30.44;
      const lossYear = Math.ceil(floatMonths / 12);

      item.lossesYear = 'lossesYear' + lossYear;
    });
  }
  return lossHistory;
}

}
