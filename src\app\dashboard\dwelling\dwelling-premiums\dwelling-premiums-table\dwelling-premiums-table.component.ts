
import {map, first} from 'rxjs/operators';
import { Component, OnInit } from '@angular/core';
import { GENERAL_WARNINGS, WARNINGS } from 'app/app-mock/warnings';
import { NavigationExtras, Router, NavigationEnd } from '@angular/router';
import { WarningDefinitionI, WarningDataI, WarningItem, WARNING_GROUPS} from 'app/hints-and-warnings/model/warnings';

import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { PremiumsService } from 'app/dashboard/auto/premiums/premiums.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { RateService } from 'app/shared/services/rate.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { HintsAndWarningsService } from 'app/hints-and-warnings/services/hints-and-warnings.service';

import { Observable ,  forkJoin } from 'rxjs';
import { AgencyUserService, UserData } from 'app/shared/services/agency-user.service';

const BIG_FAKE_PREMIUM = 999999; // Deprecated

type TSortType = 'Carrier' | 'Rate';
type TSortOrder = 'ASC' | 'DESC';
const STORAGE_SORT_TYPE_NAME: string  = 'currentPremiumsSortType';
const STORAGE_SORT_ORDER_NAME: string  = 'currentPremiumsSortOrder';

@Component({
    selector: 'app-dwelling-premiums-table',
    templateUrl: './dwelling-premiums-table.component.html',
    styleUrls: ['./dwelling-premiums-table.component.scss'],
    standalone: false
})
export class DwellingPremiumsTableComponent implements OnInit {
  private quote;
  private subscription;
  private rateSubscription;
  private currentPlansubscription;
  private subscriptionChanges;
  public quotePlanObject;
  public loadingAll:boolean = false;

  public ratedPlans:any = [];
  public generalWarnings;
  public agentId;
  private carrierWarnings;
  private currentRatesSubscription;
  private routerSubscription;
  private currentPlan;

  private subscriptionRateAll;
  private planSummaryRequestSubscription;

  constructor(
    private hintsAndWarningsService:HintsAndWarningsService,
    private overlayLoaderService: OverlayLoaderService,
    private rateService: RateService,
    private storageService: StorageService,
    private router: Router,
    public premiumsService: PremiumsService,
    public apiCommonService: ApiCommonService,
    public agencyUserService: AgencyUserService
  ) { }

  ngOnInit() {
    this.rateService.getRates();
    this.premiumsService.getRates();

    this.processListSorting();

    this.getCurrentPlan();
    this.checkIfRequirementsMet();
    if (!this.premiumsService.loadingDataInProgress) {
      this.premiumsService.rerateGeneral();
    }
    this.routerCheck();

    this.getPlanSummaryRequest();
  }

  ngOnDestroy() {
    this.rateService.unsubscribeGetRates();
    this.subscription && this.subscription.unsubscribe();
    this.rateSubscription && this.rateSubscription.unsubscribe();
    this.currentPlansubscription && this.currentPlansubscription.unsubscribe();
    this.subscriptionChanges && this.subscriptionChanges.unsubscribe();
    this.subscriptionWarnings && this.subscriptionWarnings.unsubscribe();
    this.currentRatesSubscription && this.currentRatesSubscription.unsubscribe();
    this.routerSubscription && this.routerSubscription.unsubscribe();
    this.planSummaryRequestSubscription && this.planSummaryRequestSubscription.unsubscribe();
    this.subscriptionRateAll && this.subscriptionRateAll.unsubscribe();
    this.userSubscription && this.userSubscription.unsubscribe();
  }

  private userSubscription;

  private setUserAgent():void{

      let userData: UserData;

      this.agencyUserService.userData$.pipe(
        first())
        .subscribe(data => userData = data);

      // Quote agency contact
      if (userData) {
        this.agentId = userData.user.userId;
      }
  }

  private subscriptionWarnings;
  private delayTimer;
  private allWarnings:WarningItem[] = [];
  private checkIfRequirementsMet():void {
    this.subscriptionWarnings = this.hintsAndWarningsService.allWarnings$.subscribe(warnings => {
      this.delayTimer && clearTimeout(this.delayTimer);

      this.delayTimer = setTimeout(() => {
        this.allWarnings = warnings;//.filter(item => item.showWarning);
        this.generalWarnings = warnings.filter(warning => warning.group == WARNING_GROUPS.general && warning.showWarning);
        this.carrierWarnings = warnings.filter(warning => warning.group == WARNING_GROUPS.carrier && warning.showWarning);

        if (this.generalWarnings && this.generalWarnings.length) {
          if (this.quotePlanObject && this.quotePlanObject.items && this.quotePlanObject.items.length) {
            this.quotePlanObject.items.forEach( plan => {
              plan.review = true;
            })
          }
        } else {
          if (this.quotePlanObject && this.quotePlanObject.items && this.quotePlanObject.items.length) {
            this.quotePlanObject.items.forEach( plan => {
              plan.review = false;
            })
          }
        }
        if (this.carrierWarnings && this.carrierWarnings.length) {
          this.carrierWarnings.forEach(element => {
            // if (element.uqGroupId === 'HomeGeneralOptions' && element.label === 'Required Credit Check Authorization.') {
            //   element.carrier = ['95','96','105','173','204','267']
            // }
            element.carrier.forEach(carrierId => {
              if (this.quotePlanObject && this.quotePlanObject.items && this.quotePlanObject.items.length) {
                this.quotePlanObject.items.forEach(plan => {
                  if (plan.ratingPlanId == carrierId) {
                    plan.review = true;
                  }
                })
              }
            })
          })
        }
      })
    })
  }

  private getCurrentPlan():void {
    this.currentPlansubscription = this.storageService.getStorageData('selectedPlan').subscribe( res => {
      if (res && res.items && res.items.length) {
        this.currentPlan = JSON.parse(JSON.stringify(res));
        this.quotePlanObject = JSON.parse(JSON.stringify(res.items[0]));
        this.clearPlans();
        this.getCurrentRates()
          .then(() => {
            this.processListSorting();
          });
      }
    })
  }

  private clearPlans(force:boolean = false) {
    if (this.quotePlanObject && this.quotePlanObject.items && this.quotePlanObject.items.length) {
      this.quotePlanObject.items.forEach( plan => {
        if (!plan.rerate || force) {
          plan.error = null;
          plan.rate = null;
          plan.rerate = null;
          plan.premium = null;
        }
      })
    }
  }

  private getCurrentRates(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.currentRatesSubscription = this.storageService.getStorageData('rates').subscribe( res => {
        if (res && res.length) {
          this.ratedPlans = res;
          if (this.quotePlanObject && this.quotePlanObject.items && this.quotePlanObject.items.length) {
            this.quotePlanObject.items = this.quotePlanObject.items.map( (plan, index) => {
              this.ratedPlans.forEach(ratedPlan => {
                if (ratedPlan && ratedPlan.items && ratedPlan.items.length) {
                  if (!ratedPlan.items[0].rerate) {
                    if (ratedPlan.items[0].planName === plan.name) {
                      plan.rate = JSON.parse(JSON.stringify(ratedPlan));
                      if (!ratedPlan.loading) {
                        plan.loading = false
                      }
                      plan.premium = null;
                      if (plan.rate && plan.rate.items && plan.rate.items.length && plan.rate.items[0].premium !== '' && plan.rate.items[0].premium !== 0 && plan.rate.items[0].premium !== '0') {
                        plan.premium = plan.rate.items[0].premium;
                      } else {
                        // force for ordering
                        plan.premium = BIG_FAKE_PREMIUM // Deprecated
                      }
                      plan.rerate = null;
                      if (ratedPlan.items[0].msgStatusCd == 'Error' || ratedPlan.items[0].msgStatusCd == 'DataError' || ratedPlan.items[0].msgStatusCd == 'Unknown') {
                        plan.error = ratedPlan.items[0].msgStatusDesc
                        if (!plan.error && ratedPlan.items[0].systemMessages && ratedPlan.items[0].systemMessages.length) {
                          plan.error = ratedPlan.items[0].systemMessages[0].message
                        }
                      }

                      if (ratedPlan.items[0].msgStatusCd == 'Rejected' || ratedPlan.items[0].msgStatusCd == 'Declined') {
                        plan.declined = ratedPlan.items[0].msgStatusDesc
                        if (!plan.declined && ratedPlan.items[0].systemMessages && ratedPlan.items[0].systemMessages.length) {
                          plan.declined = ratedPlan.items[0].systemMessages[0].message
                        }
                      }
                    }
                  } else {
                    if (ratedPlan.items[0].planName === plan.name) {
                      plan.rerate = true;
                      plan.rate = null;
                      plan.error = null;
                      plan.premium = null;
                    }
                  }
                }
              });

              if (this.premiumsService.tmpQuoteData && this.premiumsService.tmpQuoteData.items && this.premiumsService.tmpQuoteData.items.length) {
                if (plan.items && plan.items.length) {
                  plan.items.forEach(planItem => {
                    if (this.premiumsService.tmpQuoteData.items[index] && this.premiumsService.tmpQuoteData.items[index].items && this.premiumsService.tmpQuoteData.items[index].items.length) {
                      this.premiumsService.tmpQuoteData.items[index].items.forEach(oldPlanItem => {
                        if (planItem.coverageCode === oldPlanItem.coverageCode) {
                          if (planItem.currentValue !== oldPlanItem.currentValue && !planItem.rate) {
                            this.quotePlanObject.items[index].rerate = true;
                            this.quotePlanObject.items[index].error = null;
                            this.quotePlanObject.items[index].rate = null;
                            this.quotePlanObject.items[index].premiums = null;
                          }
                        }
                      })
                    }
                  })
                }
              }
              return plan
            })
            this.premiumsService.tmpQuoteData = JSON.parse(JSON.stringify(this.quotePlanObject));
          }
        }

        resolve();
      });
    });
  }

  private removeRate(plan) {
    let plans = [];
    this.ratedPlans.forEach( ratedPlan => {
      if (ratedPlan && ratedPlan.items && ratedPlan.items.length && ratedPlan.items[0].carrier !== plan.name) {
        plans.push(ratedPlan)
      }
    })
    this.storageService.setStorageData('rates', plans);
  }

  private mapRatedPlans(newRate) {
    let inRatedPlans = false;
    this.ratedPlans = this.ratedPlans.map(ratedPlan => {
      if (ratedPlan && ratedPlan.items && ratedPlan.items.length) {
        // if ((newRate && newRate.items && newRate.items.length) && ratedPlan && ratedPlan.items && ratedPlan.items.length && ratedPlan.items[0].planName === newRate.items[0].planName) {
        if ((newRate && newRate.items && newRate.items.length) && ratedPlan && ratedPlan.items && ratedPlan.items.length && ratedPlan.items[0].ratingPlanId === newRate.items[0].ratingPlanId) {
          ratedPlan = newRate;
          inRatedPlans = true;
        }
      }
      return ratedPlan
    });
    if (!inRatedPlans) {
      this.ratedPlans.push(newRate)
    };
    this.storageService.setStorageData('rates', this.ratedPlans);
  }

  public rate(uri:string, plan):Promise<any> {
    return new Promise<void>( (resolve, reject) => {
      if (!plan.review) {
        plan.loading = true;
        plan.error = null;
        plan.declined = null;
        plan.rerate = null;
        plan.rate = null;
        plan.premium = null;
        return this.rateSubscription = this.apiCommonService.getByUri(uri).subscribe( response => {
          plan.loading = false;
          if (this.quotePlanObject && this.quotePlanObject.items && this.quotePlanObject.items.length) {
            this.quotePlanObject.items[this.quotePlanObject.items.indexOf(plan)].rate = response;
          };
          if (response.items[0].msgStatusCd == 'Error' || response.items[0].msgStatusCd == 'DataError' || response.items[0].msgStatusCd == 'Unknown') {
            plan.error = response.items[0].msgStatusDesc
            if (!plan.error && response.items[0].systemMessages && response.items[0].systemMessages.length) {
              plan.error = response.items[0].systemMessages[0].message
            }
            //response.items[0].rateRequestId = null
          }

          if (response.items[0].msgStatusCd == 'Rejected' || response.items[0].msgStatusCd == 'Declined') {
            plan.declined = response.items[0].msgStatusDesc;
            if (!plan.declined && response.items[0].systemMessages && response.items[0].systemMessages.length) {
              plan.declined = response.items[0].systemMessages[0].message;
            }
            if (!plan.declined) {
              plan.declined = true;
            }
            //response.items[0].rateRequestId = null;
          }

          this.mapRatedPlans(response);
          this.processListSorting();
          resolve();
        }, err => {
          plan.error = true;
          plan.loading = false;
          resolve()});
      } else {
        plan.premium = BIG_FAKE_PREMIUM; // Deprecated
        resolve();
      }
    });
  }

  public rateAll(continueRating: boolean = false): void {
    if (!continueRating) {
      this.ratedPlans = [];
      this.storageService.setStorageData('rates', []);
    }

    if (this.quotePlanObject && this.quotePlanObject.items && this.quotePlanObject.items.length) {
      this.loadingAll = true;
      let plansRating = [];
      this.quotePlanObject.items = this.quotePlanObject.items.map( (plan, index) => {
        if (!plan.review) {
          if (!continueRating) {
            plan.loading = true;
            let uri: string = this.quotePlanObject.meta.href + '/rateResponses/' + plan.ratingPlanId;
            plansRating.push(this.rateAllSingleFn(uri));
          } else {
            if (plan.loading) {
              let uri: string = this.quotePlanObject.meta.href + '/rateResponses/' + plan.ratingPlanId;
              plansRating.push(this.rateAllSingleFn(uri));
            }
          }
        }
        return plan;
      });

      if (plansRating.length) {
        this.subscriptionRateAll = forkJoin(plansRating).subscribe(results => {
          console.log('done rate all');
          this.loadingAll = false;
        }, err => console.log('err', err));
      } else {
        this.loadingAll = false;
      }
    }
  }

  private rateAllSingleFn(uri): Observable<any> {
    return this.rateService.getRate(uri).pipe(map(res => {
      this.processListSorting();
      return res;
    }));
  }

  private getPlanSummaryRequest() {
    this.planSummaryRequestSubscription = this.rateService.openSummary$.subscribe( data => {
      if (data) {
        this.stopAndRestartRateAll();
        this.rateService.openSummary(false);
      }
    });
  }

  private stopAndRestartRateAll() {
    this.subscriptionRateAll && this.subscriptionRateAll.unsubscribe();
    setTimeout( () => this.rateAll(true), 2500 );
  }

  // Sorting
  public filterOptions:Array<string> = ['Carrier', 'Rate'];
  public currentSortTypeOption: TSortType = 'Rate';
  public currentSortOrderOption: TSortOrder = 'ASC';

  public onFilterChange($ev) {
    if ($ev && $ev.filter) {
      this.currentSortTypeOption = $ev.filter;

      if (this.currentSortTypeOption === $ev.filter) {
        this.currentSortOrderOption = (this.currentSortOrderOption === 'ASC') ? 'DESC' : 'ASC';
      } else {
        this.currentSortOrderOption = 'ASC';
      }

      this.sortList(this.currentSortTypeOption, this.currentSortOrderOption);

      // Save Sort Order Option to local storage
      window.sessionStorage.setItem(STORAGE_SORT_TYPE_NAME, this.currentSortTypeOption);
      window.sessionStorage.setItem(STORAGE_SORT_ORDER_NAME, this.currentSortOrderOption);
    }
  }

  private processListSorting(): void {
    const sortTypeCurrOption: TSortType = <TSortType>window.sessionStorage.getItem(STORAGE_SORT_TYPE_NAME);
    const sortOrderCurrOption: TSortOrder = <TSortOrder>window.sessionStorage.getItem(STORAGE_SORT_ORDER_NAME);
    this.currentSortTypeOption = sortTypeCurrOption || 'Rate';
    this.currentSortOrderOption = sortOrderCurrOption || 'ASC';

    this.sortList(this.currentSortTypeOption, this.currentSortOrderOption);
  }

  // Sorting Premiums list
  // ---------------------------------------------------------------------------
  private sortList(sortType: TSortType, sortOrder?: TSortOrder): void {
    switch (sortType.toLowerCase()) {
      case 'rate': {
        // First sort by Carrier name - in case if there are no premiums yet;
        this.sortQuotePlanObjectItemsByCarrierName('ASC');
        this.sortQuotePlanObjectItemsByCarrierPremium(sortOrder);
        break;
      }
      case 'carrier': {
        this.sortQuotePlanObjectItemsByCarrierName(sortOrder);
        break;
      }
    }
  }

  private sortQuotePlanObjectItemsByCarrierPremium(sortOrder: TSortOrder): void {
    if (this.quotePlanObject && this.quotePlanObject.items && this.quotePlanObject.items.length) {
      this.quotePlanObject.items.sort((planA, planB) => {
        let valA: number;
        let valB: number;

        // Plans with status 'Declined' or 'Error' should be always in the end of the list.
        if (sortOrder === 'ASC') {
          valA = (this.planStatusIsError(planA) || this.planStatusIsDeclined(planA)) ? 9999999999 : (planA.premium && typeof planA.premium === 'number') ? planA.premium : -1;
          valB = (this.planStatusIsError(planB) || this.planStatusIsDeclined(planB)) ? 9999999999 : (planB.premium && typeof planB.premium === 'number') ? planB.premium : -1;
        } else {
          valA = (this.planStatusIsError(planA) || this.planStatusIsDeclined(planA)) ? -2 : (planA.premium && typeof planA.premium === 'number') ? planA.premium : -1;
          valB = (this.planStatusIsError(planB) || this.planStatusIsDeclined(planB)) ? -2 : (planB.premium && typeof planB.premium === 'number') ? planB.premium : -1;
        }

        if (planA.rate && planA.rate.items && planA.rate.items.length && planA.rate.items.length > 1) {
          let aTmpPremiums: number[] = planA.rate.items.map(p => p.premium);
          const minValue = Math.min.apply(null, aTmpPremiums);
          valA = minValue;
        }

        if (planB.rate && planB.rate.items && planB.rate.items.length && planB.rate.items.length > 1) {
          let aTmpPremiums: number[] = planB.rate.items.map(p => p.premium);
          const minValue = Math.min.apply(null, aTmpPremiums);
          valB = minValue;
        }

        if (sortOrder === 'ASC') {
          return (valA < valB) ? -1 : (valA > valB) ? 1 : 0;
        } else {
          return (valA < valB) ? 1 : (valA > valB) ? -1 : 0;
        }
      });
    }
  }

  private sortQuotePlanObjectItemsByCarrierName(sortOrder: TSortOrder): void {
    if (this.quotePlanObject && this.quotePlanObject.items && this.quotePlanObject.items.length) {

      this.quotePlanObject.items.sort((planA, planB) => {
        let valA: string = planA.name;
        let valB: string = planB.name;

        if (sortOrder === 'ASC') {
          return (valA < valB) ? -1 : (valA > valB) ? 1 : 0;
        } else {
          return (valA < valB) ? 1 : (valA > valB) ? -1 : 0;
        }
      });
    }
  }
  // Sorting Premiums list - END

  private goToSummary(plan) {
    let rateRequestId;
    if (plan.rate.items && plan.rate.items && plan.rate.items.length) {
      rateRequestId = plan.rate.items[0].rateRequestId;
    }
    let uri = '/' + plan.rate.meta.href.split('rateResponses')[0] + 'planSummaries/' + rateRequestId;

    let isError;
    isError = (this.planStatusIsError(plan) || this.planStatusIsDeclined(plan));

    this.overlayLoaderService.showLoader();
    if (!isError) {
      this.apiCommonService.getByUri(uri).toPromise().then( planSummary => {
        this.rateService.setPlanSummary(planSummary);
        this.storageService.setStorageData('planSummary', planSummary);

        console.log('%c SET PLAN SUMMARY 1: ', 'color:red', planSummary);

        let params: NavigationExtras = {
          queryParams: {overlay: 'plan-summary', type: 'home'}
        };
        this.router.navigate([], params);

        this.overlayLoaderService.hideLoader();
      }, err => {
        this.rateService.setPlanSummary({
          manualError: err
        });

        console.log('%c SET PLAN SUMMARY 2: ', 'color:red', this.rateService.getPlanSummary());
        this.storageService.setStorageData('planSummary', this.rateService.getPlanSummary());

        let params: NavigationExtras = {
          queryParams: {overlay: 'plan-summary', type: 'home'}
        };
        this.router.navigate([], params);
        this.overlayLoaderService.hideLoader();
      });
    } else {
      console.log('%c SET PLAN SUMMARY 3: ', 'color:red', plan);
      this.storageService.setStorageData('planSummary', plan);

      let params: NavigationExtras = {
        queryParams: {overlay: 'plan-summary', type: 'home'}
      };
      this.router.navigate([], params);

      this.overlayLoaderService.hideLoader();
    }

    this.stopAndRestartRateAll();
  }

  public reviewingPlan;
  public showWarnings(plan) {
    this.reviewingPlan = plan;
    this.reviewingPlan.carrierWarnings = [];
    this.carrierWarnings.forEach(element => {
      if (element.carrier.indexOf(plan.ratingPlanId) > -1) {
        this.reviewingPlan.carrierWarnings.push(element);
      }
    });
    this.checkIfRequirementsMet();
  }

  private closeModalbox(refModal): void {
    refModal.closeModalbox();
  }

  public focusWarnElement(elementID: string, refModal = null) {
    if (refModal) {
      this.closeModalbox(refModal);
    };
    setTimeout( () => {
      let warnElement = document.getElementById(elementID);
      // this.warningService.focusWarnElement(warnElement);
    });
  }

  public resolveWarnings(generalWarnings: WarningItem[], reviewingPlan, refModal = null) {
    if (refModal) {
      this.closeModalbox(refModal);
    };

    if (generalWarnings && generalWarnings.length) {
      this.hintsAndWarningsService.goTo(generalWarnings[0]);
    } else if (reviewingPlan && reviewingPlan.carrierWarnings && reviewingPlan.carrierWarnings.length) {
      this.hintsAndWarningsService.goTo(reviewingPlan.carrierWarnings[0]);
    }
  }

  private routerCheck() {
    if (this.router && this.router.events) {
      this.routerSubscription = this.router.events.subscribe( val => {
        if (val instanceof NavigationEnd) {
          this.premiumsService.rerateGeneral(val.url);
        }
      });
    }
  }

  public planStatusIsError(plan): boolean {
    return plan.error
        || (plan.rate && plan.rate.items && plan.rate.items.length && plan.rate.items[0].msgStatusCd === 'Error')
        || (plan.rate && plan.rate.items && plan.rate.items.length && plan.rate.items[0].msgStatusCd === 'DataError');
  }

  public planStatusIsDeclined(plan): boolean {
    return plan.declined
        || (plan.rate && plan.rate.items && plan.rate.items.length && (plan.rate.items[0].msgStatusCd === 'Declined'
        || plan.rate.items[0].msgStatusCd === 'Rejected'));
  }

  public planStatusIsAuthError(plan): boolean {
    return plan?.error && plan?.error.includes('Authentication Error');
  }

  public showBtnViewPlanSummary(plan): boolean {
    return plan.rate && plan.rate.items && plan.rate.items.length && !plan.loading;
  }
}
