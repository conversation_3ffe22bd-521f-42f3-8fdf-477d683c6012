import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ApiService } from 'app/shared/services/api.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';

import { Form } from 'app/app-model/form';


@Injectable()
export class FormsService {

  constructor(
    private http: HttpClient,
    private apiService: ApiService,
    private apiCommonService: ApiCommonService
  ) { }

  // Create Form
  public createAgencyForm(formData = {}): Observable<any> {
    const uri = '/agencyforms';
    return this.apiCommonService.postByUri(uri, formData);
  }

  // Update Form
  public updateForm(agencyId: string, formID: string, formData): Observable<any> {
    const uri = '/' + agencyId + '/forms/' + formID;
    return this.apiCommonService.postByUri(uri, formData);
  }

  // Get Form by ID
  public getForm(agencyId: string, formID: string): Observable<any> {
    const uri = '/' + agencyId + '/forms/' + formID;
    return this.apiCommonService.getByUri(uri);
  }

   // Get Form by ID
   public getAgencyFormAuthorization(agencyId: string): Observable<any> {
    return this.apiCommonService.getByUri('/formauthorization');
  }

  public getAgencyForms(offset: string = '0', limit: string = '10', name: string = '', agentId: string = '', lob: string = '', locationId: string = '', dateType: string = '', startDate: string = '', endDate: string = '',  formNumber: string = '', clientSessionId: string = '', quoteSessionId: string = '', nameType: string = '', clientType = ''): Observable<any> {
    const params = 'offset=' + offset + '&limit=' + limit + '&name=' + name + '&agentId=' + agentId + '&lob=' + lob + '&locationId=' + locationId + '&dateType=' + dateType + '&startDate=' + startDate + '&endDate=' + endDate + '&number=' + formNumber + '&clientSessionId=' + clientSessionId + '&quoteSessionId=' + quoteSessionId+ '&nameType=' + nameType + '&clientType=' + clientType;
    return this.apiCommonService.getByUri('/agencyforms?' + params);
  }

  public getForms(lob: string = ''): Observable<any> {
    const params = 'lob=' + lob;
    return this.apiCommonService.getByUri('/forms?' + params);
  }

  public deleteAgencyForms(agencyFormListToDelete: string[]): Observable<any>  {
    const data = {
      AgencyFormListToDelete: agencyFormListToDelete
    };
    return this.apiCommonService.putByUri('/agencyforms', data);
  }

  public deleteAgencyForm(agencyFormId: string): Observable<any> {
    return this.apiCommonService.deleteByUri('/agencyforms?' + agencyFormId);
  }
}
