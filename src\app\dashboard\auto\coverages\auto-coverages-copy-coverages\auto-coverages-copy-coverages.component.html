<h1 class="o-heading o-heading--red">Copy Coverages</h1>

<div class="box box--silver u-spacing--1-5">
  <div>
    <span class="o-fixed-width o-fixed-width--small">From:</span>
    <div class="o-fixed-width o-fixed-width--xlarge">
      <sm-autocomplete
        #refCopyCovFrom
        [name]="'cov-copy-from'"
        [id]="'cov-copy-from'"
        [options]="optionsCopyFrom"
        [activeOption]="activeOptionCopyFromId"
        [searchFromBegining]="true"
        (onSelect)="actionOnCopyOptionFromSelect($event)">
      </sm-autocomplete>

    </div>
  </div>
  <div class="u-spacing--1-5">
    <span class="o-fixed-width o-fixed-width--small">To:</span>
    <div  class="o-fixed-width o-fixed-width--xlarge">
      <sm-autocomplete
        #refCopyCovTo
        [name]="'cov-copy-to'"
        [id]="'cov-copy-to'"
        [options]="optionsCopyTo"
        [activeOption]="activeOptionCopyToId"
        [searchFromBegining]="true"
        (onSelect)="actionOnCopyOptionToSelect($event)">
      </sm-autocomplete>
    </div>
  </div>
</div>

<h1 class="o-heading o-heading--red u-spacing--3">Vehicle Endorsements</h1>
<div class="box box--silver u-spacing--1-5">
  <div class="u-flex u-flex--two-col">
    <div>
      <div [ngClass]="{'u-spacing--bottom-1': !last}" *ngFor="let coverage of availableCoveragesToCopy | slice:0:viewCoveragesListSeparatorPosition; let i = index; let last = last;">
        <label class="o-checkable" [htmlFor]="coverage.coverageCode">
          <input type="checkbox" [id]="coverage.coverageCode" [(ngModel)]="coverage.isSelected">
          <i class="o-btn o-btn--checkbox"></i>
          {{ coverage.coverageDescription }}
        </label>
      </div>
    </div>
    <div>
      <div [ngClass]="{'u-spacing--bottom-1': !last}" *ngFor="let coverage of availableCoveragesToCopy | slice:viewCoveragesListSeparatorPosition; let i = index; let last = last;">
        <label class="o-checkable" [htmlFor]="coverage.coverageCode">
          <input type="checkbox" [id]="coverage.coverageCode" [(ngModel)]="coverage.isSelected">
          <i class="o-btn o-btn--checkbox"></i>
          {{ coverage.coverageDescription }}
        </label>
      </div>
    </div>
  </div>

  <hr class="u-spacing--2-5 u-spacing--bottom-2">

  <div class="">
    <label class="o-checkable">
      <!-- <input type="checkbox" [checked]="includeOptionalCoverages" (change)="includeOptionalCoverages = !includeOptionalCoverages"> -->
      <input type="checkbox" id="allowToCopyAdditionalCoverages" [(ngModel)]="allowToCopyAdditionalCoverages">
      <i class="o-btn o-btn--checkbox"></i>
      Additional Coverages (i.e. Waiver of Deductible)
    </label>

  </div>
</div>

<div class="row u-spacing--2">
  <div class="col-xs-12 u-align-right">
    <button  class="o-btn u-spacing--right-2" (click)="actionOnBtnCopy($event)">Copy</button>
    <button  class="o-btn o-btn--idle" (click)="actionOnBtnCancel($event)">Cancel</button>
  </div>
</div>

<app-modalbox #refModalInfo smToOverlay [css]="'infobox size--550'">
  <div class="row">
    <div class="col-xs-12">
      <p *ngFor="let info of viewModalInfo" class="u-spacing--bottom-1-5">{{info}}</p>
    </div>
  </div>

  <div class="row u-spacing--2">
    <div class="col-xs-12 u-align-right">
      <button  class="o-btn u-spacing--right-2" (click)="actionOnModalBtnClose($event)">Close</button>
    </div>
  </div>
</app-modalbox>