import { StubSubsServiceProvider } from '../../../../../testing/stubs/services/subs.service.provider';
import { StubOptionsServiceProvider } from '../../../../../testing/stubs/services/options.service.provider';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';


import { StubAgencyUserServiceProvider } from '../../../../../testing/stubs/services/agency-user.service.provider';
import { StubSpecsServiceProvider } from '../../../../../testing/stubs/services/specs.service.provider';

import { StorageService } from '../../../../shared/services/storage-new.service';
import { UmbrellaGeneralOptionsAutomanagerComponent } from './umbrella-general-options-automanager.component';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { StubCoveragesServiceProvider } from 'testing/stubs/services/coverages.service.provider';

describe('UmbrellaGeneralOptionsAutomanagerComponent', () => {
  let component: UmbrellaGeneralOptionsAutomanagerComponent;
  let fixture: ComponentFixture<UmbrellaGeneralOptionsAutomanagerComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ UmbrellaGeneralOptionsAutomanagerComponent ],
      providers: [
        StorageService,
        StubAgencyUserServiceProvider,
        StubSpecsServiceProvider,
        StubOptionsServiceProvider,
        StubSubsServiceProvider,
        StorageGlobalService,
        StubCoveragesServiceProvider
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(UmbrellaGeneralOptionsAutomanagerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
