import { InventoryItem } from "app/inventory-confirm";

export interface InventoryOrderSummary {
	atlasInventoryOrderKey: string;
	inventoryOrderTrackingNumber: string;
	inventoryOrderStatus: string;
	inventoryOrderDisplayStatus: string;
	inventoryOrderStatusDescription: string;
	inventoryOrderStatusDate: string;
}

export interface InventoryOrderItem {
	inventoryType: string;
	inventoryTypeDescription: string;
	numberOfUnits: string;
}


export interface InventoryOrderDeliveryDetail {
	deliveryItemCount: string;
	deliveryItems: any[];
}

export interface InventoryOrderDetail {
	inventoryOrderItems: InventoryOrderItem[];
	inventoryOrderDeliveryDetails: InventoryOrderDeliveryDetail;
	inventoryOrderSummary: InventoryOrderSummary;
  inventoryItems: InventoryItem[]
}

export interface InventoryOrder {

	inventoryOrderDetails: InventoryOrderDetail;
}

export interface InventoryList {
	requestID: string;
	atlasTransactionKey: string;
	inventoryOrders: InventoryOrder[];
}

export interface InventoryStatus {
	inventoryType: string;
	inventoryID?: string;
	numberOfUnits: number;
	newInventoryStatus?: string;
  reasonCode?:string;
  transactionDesc?:string
}

