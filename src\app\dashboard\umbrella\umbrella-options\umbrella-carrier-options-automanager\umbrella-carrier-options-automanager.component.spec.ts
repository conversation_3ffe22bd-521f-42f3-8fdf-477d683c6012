import { StubSubsServiceProvider } from '../../../../../testing/stubs/services/subs.service.provider';
import { StubOptionsServiceProvider } from '../../../../../testing/stubs/services/options.service.provider';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';


import { StubAgencyUserServiceProvider } from '../../../../../testing/stubs/services/agency-user.service.provider';
import { StubSpecsServiceProvider } from '../../../../../testing/stubs/services/specs.service.provider';

import { StorageService } from '../../../../shared/services/storage-new.service';
import { UmbrellaCarrierOptionsAutomanagerComponent } from './umbrella-carrier-options-automanager.component';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';

describe('UmbrellaCarrierOptionsAutomanagerComponent', () => {
  let component: UmbrellaCarrierOptionsAutomanagerComponent;
  let fixture: ComponentFixture<UmbrellaCarrierOptionsAutomanagerComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ UmbrellaCarrierOptionsAutomanagerComponent ],
      providers: [
        StorageService,
        StubAgencyUserServiceProvider,
        StubSpecsServiceProvider,
        StubOptionsServiceProvider,
        StubSubsServiceProvider,
        StorageGlobalService
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(UmbrellaCarrierOptionsAutomanagerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
