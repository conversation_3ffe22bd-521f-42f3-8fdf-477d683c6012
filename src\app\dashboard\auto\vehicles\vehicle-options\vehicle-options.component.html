
<section class="section section--compact u-spacing--2-5">
  <div class="u-flex u-flex--spread">
    <div class="">
      <h1 class="o-heading">Vehicle options and equipment</h1>
    </div>
  </div>

  <div class="row u-spacing--1">
    <div class="col-xs-12">
      <div class="box box--silver">
        <div class="row o-columns">
          <div class="col-xs-6" *ngIf="!hideFirstColumn">

            <app-togglebox [headText]="'General'" [notToggable]="true">

              <ul class="checklist">
                <li class="checklist__item">
                  <div class="checklist__btns">
                    <label class="o-checkable">
                      <input type="checkbox" [(ngModel)]="selectedVehicle.options.highTheft" (change)="registerOptionsUpdate()">
                      <i class="o-btn o-btn--checkbox"></i>
                    </label>
                  </div>
                  <div class="checklist__label">
                    <p class="checklist__label-main">High Theft</p>
                    <p class="checklist__label-comment"></p>
                  </div>
                </li>

                <li class="checklist__item">
                  <div class="checklist__btns">
                    <label class="o-checkable">
                      <input type="checkbox" [(ngModel)]="selectedVehicle.options.leased" (change)="registerOptionsUpdate()">
                      <i class="o-btn o-btn--checkbox"></i>
                    </label>
                  </div>
                  <div class="checklist__label">
                    <p class="checklist__label-main">Leased</p>
                    <p class="checklist__label-comment"></p>
                  </div>
                </li>
              </ul>
            </app-togglebox>

            <div class="u-spacing u-spacing--0-5"></div>

            <app-togglebox [headText]="'Equipment'" [notToggable]="true">
              <ul class="checklist">
                <li class="checklist__item">
                  <div class="checklist__btns">
                    <label class="o-checkable">
                      <input type="checkbox" [(ngModel)]="selectedVehicle.options.antilockBrakes" (change)="registerOptionsUpdate()">
                      <i class="o-btn o-btn--checkbox"></i>
                    </label>
                  </div>
                  <div class="checklist__label">
                    <p class="checklist__label-main">Anti-lock Brakes</p>
                    <p class="checklist__label-comment"></p>
                  </div>
                </li>

                <li class="checklist__item">
                  <div class="checklist__btns">
                    <label class="o-checkable">
                      <input type="checkbox" [(ngModel)]="selectedVehicle.options.hybrid" (change)="registerOptionsUpdate()">
                      <i class="o-btn o-btn--checkbox"></i>
                    </label>
                  </div>
                  <div class="checklist__label">
                    <p class="checklist__label-main">Hybrid</p>
                    <p class="checklist__label-comment"></p>
                  </div>
                </li>
              </ul>
            </app-togglebox>

            <div class="u-spacing u-spacing--0-5"></div>

            <app-togglebox [headText]="'Passive Restraints'" [notToggable]="true">

              <ul class="checklist">
                <li class="checklist__item">
                  <div class="checklist__btns">
                    <label class="o-checkable">
                      <input type="checkbox" [(ngModel)]="selectedVehicle.options.driverAirbag" (change)="registerOptionsUpdate()">
                      <i class="o-btn o-btn--checkbox"></i>
                    </label>
                  </div>
                  <div class="checklist__label">
                    <p class="checklist__label-main">Driver airbag</p>
                    <p class="checklist__label-comment"></p>
                  </div>
                </li>

                <li class="checklist__item">
                  <div class="checklist__btns">
                    <label class="o-checkable">
                      <input type="checkbox" [(ngModel)]="selectedVehicle.options.passengerAirbag" (change)="registerOptionsUpdate()">
                      <i class="o-btn o-btn--checkbox"></i>
                    </label>
                  </div>
                  <div class="checklist__label">
                    <p class="checklist__label-main">Passenger airbag</p>
                    <p class="checklist__label-comment"></p>
                  </div>
                </li>

                <li class="checklist__item">
                  <div class="checklist__btns">
                    <label class="o-checkable">
                      <input type="checkbox" [(ngModel)]="selectedVehicle.options.driverAutomaticBelt" (change)="registerOptionsUpdate()">
                      <i class="o-btn o-btn--checkbox"></i>
                    </label>
                  </div>
                  <div class="checklist__label">
                    <p class="checklist__label-main">Driver's automatic seat belt</p>
                    <p class="checklist__label-comment"></p>
                  </div>
                </li>

                <li class="checklist__item">
                  <div class="checklist__btns">
                    <label class="o-checkable">
                      <input type="checkbox" [(ngModel)]="selectedVehicle.options.passengerAutomaticBelt" (change)="registerOptionsUpdate()">
                      <i class="o-btn o-btn--checkbox"></i>
                    </label>
                  </div>
                  <div class="checklist__label">
                    <p class="checklist__label-main">Passenger's automatic seat belt</p>
                    <p class="checklist__label-comment"></p>
                  </div>
                </li>
              </ul>
            </app-togglebox>
          </div>

          <div class="col-xs-6">

            <app-togglebox [headText]="'Anti-theft devices'" [notToggable]="true">
              <ul class="checklist">

                <li class="checklist__item">
                  <div class="checklist__btns">
                    <label class="o-checkable">
                      <input type="checkbox" [(ngModel)]="selectedVehicle.options.alarm" (change)="registerOptionsUpdate()">
                      <i class="o-btn o-btn--checkbox"></i>
                    </label>
                  </div>
                  <div class="checklist__label">
                    <p class="checklist__label-main">Alarm</p>
                    <p class="checklist__label-comment"></p>
                  </div>
                </li>

                <li class="checklist__item">
                  <div class="checklist__btns">
                    <label class="o-checkable">
                      <input type="checkbox" [(ngModel)]="selectedVehicle.options.activeDisabling" (change)="registerOptionsUpdate()">
                      <i class="o-btn o-btn--checkbox"></i>
                    </label>
                  </div>
                  <div class="checklist__label">
                    <p class="checklist__label-main">Active Disabling</p>
                    <p class="checklist__label-comment"></p>
                  </div>
                </li>

                <li class="checklist__item">
                  <div class="checklist__btns">
                    <label class="o-checkable">
                      <input type="checkbox" [(ngModel)]="selectedVehicle.options.passiveDisabling" (change)="registerOptionsUpdate()">
                      <i class="o-btn o-btn--checkbox"></i>
                    </label>
                  </div>
                  <div class="checklist__label">
                    <p class="checklist__label-main">Passive Disabling</p>
                    <p class="checklist__label-comment"></p>
                  </div>
                </li>

                <li class="checklist__item">
                  <div class="checklist__btns">
                    <label class="o-checkable">
                      <input type="checkbox" [(ngModel)]="selectedVehicle.options.vehicleRecovery" (change)="registerOptionsUpdate()">
                      <i class="o-btn o-btn--checkbox"></i>
                    </label>
                  </div>
                  <div class="checklist__label">
                    <p class="checklist__label-main">Vehicle Recovery</p>
                    <p class="checklist__label-comment"></p>
                  </div>
                </li>

                <li class="checklist__item">
                  <div class="checklist__btns">
                    <label class="o-checkable">
                      <input type="checkbox" [(ngModel)]="selectedVehicle.options.otherMACategoryI" (change)="registerOptionsUpdate()">
                      <i class="o-btn o-btn--checkbox"></i>
                    </label>
                  </div>
                  <div class="checklist__label">
                    <p class="checklist__label-main">Other MA Category I</p>
                    <p class="checklist__label-comment"></p>
                  </div>
                </li>

                <li class="checklist__item">
                  <div class="checklist__btns">
                    <label class="o-checkable">
                      <input type="checkbox" [(ngModel)]="selectedVehicle.options.otherMACategoryII" (change)="registerOptionsUpdate()">
                      <i class="o-btn o-btn--checkbox"></i>
                    </label>
                  </div>
                  <div class="checklist__label">
                    <p class="checklist__label-main">Other MA Category II</p>
                    <p class="checklist__label-comment"></p>
                  </div>
                </li>

                <li class="checklist__item">
                  <div class="checklist__btns">
                    <label class="o-checkable">
                      <input type="checkbox" [(ngModel)]="selectedVehicle.options.otherMACategoryIII" (change)="registerOptionsUpdate()">
                      <i class="o-btn o-btn--checkbox"></i>
                    </label>
                  </div>
                  <div class="checklist__label">
                    <p class="checklist__label-main">Other MA Category III</p>
                    <p class="checklist__label-comment"></p>
                  </div>
                </li>

                <li class="checklist__item">
                  <div class="checklist__btns">
                    <label class="o-checkable">
                      <input type="checkbox" [(ngModel)]="selectedVehicle.options.otherMACategoryIV" (change)="registerOptionsUpdate()">
                      <i class="o-btn o-btn--checkbox"></i>
                    </label>
                  </div>
                  <div class="checklist__label">
                    <p class="checklist__label-main">Other MA Category IV</p>
                    <p class="checklist__label-comment"></p>
                  </div>
                </li>

                <li class="checklist__item">
                  <div class="checklist__btns">
                    <label class="o-checkable">
                      <input type="checkbox" [(ngModel)]="selectedVehicle.options.otherMACategoryV" (change)="registerOptionsUpdate()">
                      <i class="o-btn o-btn--checkbox"></i>
                    </label>
                  </div>
                  <div class="checklist__label">
                    <p class="checklist__label-main">Other MA Category V</p>
                    <p class="checklist__label-comment"></p>
                  </div>
                </li>
              </ul>
            </app-togglebox>

          </div>
        </div>
      </div>
    </div>
  </div> <!-- / .row -->
</section>
