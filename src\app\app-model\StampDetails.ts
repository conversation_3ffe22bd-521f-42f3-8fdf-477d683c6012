export interface StampData {
	id: number;
	status: string;
	receiveDate: string;
	lastModifiedDate: string;
	requesterType: string;
	message?: any;
}

export interface Dealer {
	id: number;
	dealerUserId: number;
	dealerOrgId: number;
	name: string;
	contactPerson: string;
	phone: string;
	dealerUserEmail: string;
}

export interface BuyerInfo {
	id: number;
	requesterType: string;
	ownership?: any;
	firstName: string;
	lastName: string;
	dob?: any;
	companyName?: any;
	fid?: any;
	email: string;
	phone: string;
	license: string;
	plateType?: any;
	plateNumber?: any;
	vin: string;
	make?: any;
	model?: any;
	year?: any;
	transactionType?: any;
}

export interface Document {
	name: string;
	type?: any;
	data: string;
}

export interface StampAgency {
	agencyFacilityId: number;
	agencyOrgId: number;
	name: string;
	phoneNo: string;
}

export interface InsuranceDetail {
	agencyStampId: number;
	agencyId: number;
	writingCompanyId: number;
	signedBy?: any;
	writingCompany?: string;
	effectiveDate: any;
	policyChangeDate: any;
}

export interface StampDetails {
	stampData: StampData;
	dealer: Dealer;
	buyerInfo: BuyerInfo;
	thumbnails: any[];
	documents: Document[];
	stampAgency: StampAgency;
	insuranceDetails: InsuranceDetail;
}
