import { Injectable } from '@angular/core';
import { Router, RouterStateSnapshot, ActivatedRouteSnapshot } from '@angular/router';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { Observable, of, forkJoin } from 'rxjs';

import { RmvService } from '../app-services/rmv.service';
import { map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class InventoryResolver  {

  constructor(private rmvService: RmvService, private overlayLoader:OverlayLoaderService) {}
  resolve(): Observable<any> {
    this.overlayLoader.showLoader();
    return forkJoin([
      this.rmvService.getInventoryOrderList(),
      this.rmvService.getAvailableInventoryItems()
    ]).pipe(
      map(results => {
        this.overlayLoader.hideLoader();
        return {
          orderList: results[0],
          availableItems: results[1]
        };
      })
    );
  }
}
