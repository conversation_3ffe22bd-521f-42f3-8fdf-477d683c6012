import { Injectable } from '@angular/core';
import { Router, RouterStateSnapshot, ActivatedRouteSnapshot } from '@angular/router';
import { Observable, of, forkJoin, map } from 'rxjs';
import { EstampService } from '../app-services/estamp.service';
import { LeadsService } from '../app-services/leads.service';


@Injectable({
  providedIn: 'root'
})
export class DashboardTabsResolver  {

  constructor( private leadsService: LeadsService,
    private estampService: EstampService, ) {}
  resolve(): Observable<any> {
    return forkJoin([
      this.leadsService.getLeads(),
      this.estampService.getEstampRequests()
    ]).pipe(map(results => {
      return {
        leads: results[0],
        estamps: results[1]
      };
    }));
  }
}
