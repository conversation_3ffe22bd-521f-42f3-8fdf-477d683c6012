import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { GetReadySuccessComponent } from './get-ready-success.component';

describe('GetReadySuccessComponent', () => {
  let component: GetReadySuccessComponent;
  let fixture: ComponentFixture<GetReadySuccessComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ GetReadySuccessComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(GetReadySuccessComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
