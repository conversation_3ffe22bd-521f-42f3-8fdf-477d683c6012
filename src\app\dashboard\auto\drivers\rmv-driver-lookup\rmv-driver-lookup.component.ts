import { Component, OnInit, OnDestroy, ViewChild, Input, Output, EventEmitter, AfterViewInit } from '@angular/core';
import { Driver, DriverRmv, DriverRmvToSend, DriverRmvToSendWithResourceId, RmvDriverResult } from 'app/app-model/driver';
import { NgForm } from '@angular/forms';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Quote } from 'app/app-model/quote';
import { RmvService } from 'app/dashboard/app-services/rmv.service';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { OverlayRouteService } from 'app/overlay/services/overlay-route.service';
import { DriversService } from 'app/dashboard/app-services/drivers.service';

import { RMV_NEW_DRIVERS } from 'app/app-mock/rmv-new-driver';
import { Validate } from 'app/hints-and-warnings/validators';
import { ModalboxComponent } from 'app/shared/components/modalbox/modalbox.component';
import { Router } from '@angular/router';
import { TooltipComponent } from '../../../../shared/modules/sm-popups/components/tooltip/tooltip.component';
import { format } from 'date-fns';


const FORM_INFO = 'Please fill all the required fields';

@Component({
    selector: 'app-rmv-driver-lookup',
    templateUrl: './rmv-driver-lookup.component.html',
    styleUrls: ['./rmv-driver-lookup.component.scss'],
    standalone: false
})
export class RmvDriverLookupComponent implements OnInit, OnDestroy, AfterViewInit {
  @Input() isCommercial: boolean;
  private quoteSubscription;
  private driversSubscription;

  private selectedQuote: Quote;

  public driversList: DriverRmv[] = [];
  public tmpNewDriver: DriverRmv = new DriverRmv();

  // public infoFormShow: boolean = false;
  private formSubmittedShowInfo = false;
  public showFormErrorInfo = false;
  duplicateLicense = false;
  notUniqueLicense = false
  public showListErrorInfo = false;

  @ViewChild('refFormRmvDriver') public refFormRmvDriver: NgForm;
  @ViewChild('refPickerDateOfBirth') public refPickerDateOfBirth;

  @Input('refModalbox') public refModalbox: ModalboxComponent = undefined;
  @Output() errorStatus = new EventEmitter();
  constructor(
    private storageService: StorageService,
    private rmvService: RmvService,
    private overlayLoaderService: OverlayLoaderService,
    private overlayRouteService: OverlayRouteService,
    private driversService: DriversService,
    private router: Router
  ) { }

  ngOnInit() {
    this.subscribeSelectedQuote();
    this.subscribeDrivers();
    this.isCommercial ? this.subscribeComDrivers() : this.subscribeDrivers();
  }

  ngAfterViewInit(): void {
    this.setNewTmpDriver();
  }

  ngOnDestroy() {
    this.quoteSubscription && this.quoteSubscription.unsubscribe();
    this.driversSubscription && this.driversSubscription.unsubscribe();
  }

  private subscribeSelectedQuote(): void {
    this.quoteSubscription = this.storageService.getStorageData('selectedQuote').subscribe(res => {
      this.selectedQuote = res;
    });
  }

  private subscribeDrivers(): void {
    this.driversSubscription = this.storageService.getStorageData('driversList').subscribe((drivers: Driver[]) => {
      this.driversList = drivers.map(driver => {
        const tmpDriver = new DriverRmv();
        tmpDriver.checked = true;
        tmpDriver.firstName = driver.firstName;
        tmpDriver.lastName = driver.lastName;
        tmpDriver.license = driver.licenseNumber;
        tmpDriver.dob = driver.dateOfBirth;
        tmpDriver.resourceId = driver.resourceId;
        return tmpDriver;
      });
    });
  }

  private subscribeComDrivers(): void {
    this.driversSubscription = this.storageService.getStorageData('comDriverList').subscribe((drivers) => {
      this.driversList = drivers.map(driver => {
        const tmpDriver = new DriverRmv();
        tmpDriver.checked = true;
        tmpDriver.firstName = driver.firstName;
        tmpDriver.lastName = driver.lastName;
        tmpDriver.license = driver.licenseNumber;
        tmpDriver.dob = driver.dateOfBirth;
        tmpDriver.resourceId = driver.resourceId;
        return tmpDriver;
      });
    });
  }

  private setNewTmpDriver(): void {
    this.tmpNewDriver = new DriverRmv();
    this.tmpNewDriver.checked = false;
  }

  private toggleCheckStatus($ev: Event, driver: DriverRmv): void {
    $ev.preventDefault();
    driver.checked = !driver.checked;
    this.onListCheckboxChange();
  }

 public updateDateObjectProperty($event, obj, property) {
    const date = $event.date ? format(new Date($event.date), 'yyyy-MM-dd') : '';
    obj[property] = date;
}

  private filterDriversToLookup(drivers: DriverRmv[]): DriverRmv[] {
    const driversToLookup = drivers.filter(driver => driver.checked === true);
    return driversToLookup;
  }

  private parseRmvDriversToDriversRmvToSend(drivers: DriverRmv[]): DriverRmvToSendWithResourceId[] {
    return drivers.map(driver => {
      const tmpDriver = new DriverRmvToSendWithResourceId();
      tmpDriver.firstName = driver.firstName;
      tmpDriver.lastName = driver.lastName;
      tmpDriver.dateOfBirth = driver.dob;
      tmpDriver.license = driver.license;
      tmpDriver.resourceId = driver.resourceId || '';
      return tmpDriver;
    });
  }

  private checkIfFormIsValid(): boolean {
    return this.refFormRmvDriver.valid && this.refPickerDateOfBirth.ngModel && this.refPickerDateOfBirth.ngModel.valid;
  }

  private RMVlookupDrivers(drivers: DriverRmvToSendWithResourceId[]): Promise<any> {
    // Reset storage
    this.storageService.setStorageData('rmvDriversLookup', []);
    this.storageService.setStorageData('driversHelperList', []);

    this.overlayLoaderService.showLoader();
    const lob = this.isCommercial ? 'autob' : 'autop';
    if(this.checkForUniqueLicense(drivers)) {
      this.notUniqueLicense = false;
    return this.rmvService.updateDriversByLicense(this.selectedQuote.resourceId, this.selectedQuote.effectiveDate, drivers, lob)
      .toPromise()
      .then((res) => {
        this.overlayLoaderService.hideLoader();
        this.showRMVDriversReport(res.items);
        // this.showRMVDriversReport(RMV_NEW_DRIVERS); // MOCK DATA
        this.getQuoteDriversWithNewCreatedByRMVAndSetInSorage();
        return res;
      })
      .catch(err => {
        this.overlayLoaderService.hideLoader();
        console.log('RMV NEW DRIVER ERROR', err);

        const errorDriver = new RmvDriverResult();
        errorDriver.messages.push({
          message: 'An error occurred while performing the RMV Lookup, we are working to resolve it. Please try again later.'
        });
        this.showRMVDriversReport([errorDriver]);
      });}
      else {
        this.notUniqueLicense = true;
        this.overlayLoaderService.hideLoader()
      }
  }

  private checkForUniqueLicense(drivers: DriverRmvToSendWithResourceId[]) {
    const uniqueLicense = new Set()
    for(const d of drivers) {
      if(uniqueLicense.has(d.license)) {
        return false
      }
      uniqueLicense.add(d.license)
    }
    return true;
  }

  private showRMVDriversReport(driversRmvResult: RmvDriverResult[]) {
    this.storageService.setStorageData('rmvDriversLookup', driversRmvResult);
    const driver = driversRmvResult[0] || new RmvDriverResult();

    if (driver.firstName && driver.lastName && driver.licenseNumber) {
      const driverId = this.rmvService.generateRmvReportDriverId(driver);
      this.isCommercial ? this.overlayRouteService.go('rmvreports', 'comdriver', driverId)
       : this.overlayRouteService.go('rmvreports', 'driver', driverId);
    } else {
      this.isCommercial ? this.overlayRouteService.go('rmvreports', 'comdriver') : this.overlayRouteService.go('rmvreports', 'driver');

    }
  }

  private getQuoteDriversWithNewCreatedByRMVAndSetInSorage(): Promise<any> {
    if (this.isCommercial) {
      return this.driversService.getComDriversList(this.selectedQuote.resourceId)
      .toPromise()
      .then(res => {
        this.storageService.setStorageData('driversHelperList', res.items);
        return res;
      });
    } else {
    return this.driversService.getDriversList(this.selectedQuote.resourceId)
      .toPromise()
      .then(res => {
        this.storageService.setStorageData('driversHelperList', res.items);
        return res;
      });
  }
}


  // VALIDATION
  private checkIfShowTheFormError(): void {
    this.showFormErrorInfo = this.tmpNewDriver.checked && !this.checkIfFormIsValid() && this.formSubmittedShowInfo;
  }



  private isRequiredFormField(propertyName: string): boolean {
    let isRequired = false;
if (!this.isCommercial) {
    if (this.tmpNewDriver.checked) {
      if (propertyName in this.tmpNewDriver) {
        isRequired = Validate.isEmptyValue(this.tmpNewDriver[propertyName]);
      }
    }
  }

    return isRequired;
  }

  public checkForDuplicateDriverLicense(license) {
    let isdup = false;

    const dup = this.driversList.find(d => d.license.toLowerCase() === license.toLowerCase());
       dup ? isdup = true : isdup = false;
      //  isdup ? this.errorMessage = 'Duplicate License #': this.errorMessage = '';
      this.errorStatus.emit(isdup);
        return isdup;
  }


  // Public Methods
  // -------------------------------------------------------------------------------
  public validateFormDataAndCheckIfFieldIsRequired(propertyName: string): boolean {
    const isRequired = this.isRequiredFormField(propertyName);
    this.checkIfShowTheFormError();
    return isRequired;
  }

  public onFormCheckboxChange() {
    if (!this.tmpNewDriver.checked) {
      this.formSubmittedShowInfo = false;
    }
    this.checkIfShowTheFormError();
  }

  public onListCheckboxChange() {
    if (this.showListErrorInfo) {
      this.showListErrorInfo = this.validateUsersFromTheSelectedList(this.driversList);
    }
  }

  public addAnotherDriver($ev: Event): void {
    $ev.preventDefault();
   this.duplicateLicense = this.checkForDuplicateDriverLicense(this.tmpNewDriver.license);
    if (this.checkIfFormIsValid() && !this.duplicateLicense) {
      this.driversList.push(this.tmpNewDriver);
      this.setNewTmpDriver();
      this.formSubmittedShowInfo = false;
    } else {
      console.log('The form is not Valid');

      if (this.tmpNewDriver.checked) {
        // console.log('Form needs to be filled out');
        this.formSubmittedShowInfo = true;
      }
    }
  }

  public isInvalidDriver(driver: DriverRmv): boolean {
    if (driver.checked) {
      return !driver.firstName || !driver.lastName || !driver.dob || !driver.license;
    }

    return false;
  }

  private validateUsersFromTheSelectedList(drivers: DriverRmv[]): boolean {
    let invalidList = false;

    for (const driver of drivers) {
      if (this.isInvalidDriver(driver)) {
        invalidList = true;
        break;
      }
    }

    return invalidList;
  }

  public rmvLookup($ev: Event, refModal?): void {
    $ev.preventDefault();
    let invalidCustomFormDriver = false;
    let invalidSomeUserOfTheList = false;

    const drivers = this.filterDriversToLookup(this.driversList);

    // Validate drivers
    invalidSomeUserOfTheList = this.validateUsersFromTheSelectedList(drivers);
    this.showListErrorInfo = invalidSomeUserOfTheList;

    // Form Custom driver
    if (this.tmpNewDriver.checked && !this.isCommercial) {
      if (this.checkIfFormIsValid()) {
        drivers.push(this.tmpNewDriver);
        this.formSubmittedShowInfo = false;
      } else {
        // console.log('Form needs to be filled out');
        invalidCustomFormDriver = true;
        this.formSubmittedShowInfo = true;
      }
    }

    // If driver on the list or in the form are invalid and selected, prevent lookup
    if (invalidCustomFormDriver || invalidSomeUserOfTheList) {
      return;
    }

    if (drivers.length) {
      const parsedDrivers = this.parseRmvDriversToDriversRmvToSend(drivers);

      this.RMVlookupDrivers(parsedDrivers).then(() => {
        if (refModal && 'closeModalbox' in refModal) {
          refModal.closeModalbox();
        }
      });
    } else {
      console.log('There are no users to lookup.');
    }

  }

  public lettersOnly(event) {
    const letters = /^[A-Za-z]{1}$/ig;
    const multiKey = event.ctrlKey || event.metaKey;
    const keyNormalized = event.key.toLocaleLowerCase();

    if (!(
      keyNormalized === 'backspace' ||
      keyNormalized === 'delete' ||
      keyNormalized === 'tab' ||
      keyNormalized === 'arrowleft' ||
      keyNormalized === 'left' ||
      keyNormalized === 'arrowright' ||
      keyNormalized === 'right' ||
      keyNormalized === 'a' && multiKey ||
      keyNormalized === 'z' && multiKey ||
      keyNormalized === 'c' && multiKey ||
      keyNormalized === 'v' && multiKey ||
      letters.test(event.key)
    )) { event.preventDefault(); }
  }

  public generateDriverViewLink(driver: DriverRmv): string {
    const link = this.isCommercial ? `/dashboard/commercial-auto/quotes/${this.selectedQuote.resourceId}/drivers`
    : '/dashboard/auto/quotes/' + this.selectedQuote.resourceId + '/drivers/' + driver.resourceId;
    return link;
  }

  public goToDriverView(event: Event, driver: DriverRmv, refTooltip: TooltipComponent): void {
    event && event.preventDefault();
    if (refTooltip) {
      refTooltip.close();
    }

    if (this.refModalbox && 'close' in this.refModalbox) {
      this.refModalbox.close();
    }

    this.router.navigateByUrl(this.generateDriverViewLink(driver));
  }
}
