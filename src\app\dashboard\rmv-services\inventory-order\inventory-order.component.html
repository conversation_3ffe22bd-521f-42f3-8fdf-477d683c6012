<section class="section">
  <div class="row padding-row">
    <div class="col-xs-12">
        <h3>Order History</h3>
    </div>
</div>

  </section>

  <section class="section">
    <div class="row">
      <div class="col-xs-12">
        <table class="table table--compact table--hoverable-grey">
          <thead class="table__thead">
            <tr class="">
              <th class="table__th ">Order #</th>
              <th class="table__th ">Order Date</th>
              <th class="table__th ">Description</th>
              <th class="table__th">Order Status</th>
              <th class="table__th "></th>

            </tr>
          </thead>
          <tbody class="table__tbody" *ngIf="list">
            <tr class="table__tr" *ngFor="let row of list.inventoryOrders |orderBy: '-inventoryOrderDetails.inventoryOrderSummary.inventoryOrderStatusDate'|slice: (paginationCurrentPage * 10) - 10 :paginationCurrentPage * 10">
              <td class="table__td">
                <a (click)="orderDetails.open()">
                {{row.inventoryOrderDetails.inventoryOrderSummary.atlasInventoryOrderKey}}
                </a>
                <app-modalbox #orderDetails [css]="'u-width-650px'">
                  <app-order-details [order]="row.inventoryOrderDetails"></app-order-details>
                  <div class="col-xs-12 u-align-right u-remove-letter-spacing">
                    <button type="button" (click)="orderDetails.close()" class="o-btn u-spacing--left-2" style="margin-left:1.8rem;">Close</button>
                  </div>
                  </app-modalbox>

              </td>
              <td class="table__td">
                {{row.inventoryOrderDetails.inventoryOrderSummary.inventoryOrderStatusDate | date: "MM/dd/yyyy"}}
              </td>
                  <td class="table__td">
                    <div class="row" *ngFor="let item of row.inventoryOrderDetails.inventoryOrderItems">
                            {{item.inventoryTypeDescription}} @if(item.inventoryType.includes('Decal') && row.inventoryOrderDetails.inventoryOrderSummary.inventoryOrderDisplayStatus !== 'Ordered'){
                              ({{getInventoryCount(row.inventoryOrderDetails, item.inventoryType)}})
                            }
                    </div>
                    </td>
                  <td class="table__td">{{row.inventoryOrderDetails.inventoryOrderSummary.inventoryOrderDisplayStatus}}</td>
                  <td class="table__td">
                    <button class="o-btn" (click)="fullfilledConfirm.open()" *ngIf="row.inventoryOrderDetails.inventoryOrderSummary.inventoryOrderDisplayStatus==='Filled'">Received</button>
                    <app-modalbox #fullfilledConfirm>
                      <br>

                                  <div class="box box--silver" style="margin-bottom: 20px;line-height:2.5">
                                     <p>You are confirming that you received the following order:</p>

                                     <p *ngFor="let item of row.inventoryOrderDetails.inventoryOrderItems">
                {{row.inventoryOrderDetails.inventoryOrderSummary.inventoryOrderStatusDate | date: "MM/dd/yyyy"}} - {{item.inventoryTypeDescription}}
                                     </p>

                                     </div>
                                     <div class="col-xs-12 u-align-right u-remove-letter-spacing">
                                    <button type="button" (click)="confirmOrder(row.inventoryOrderDetails.inventoryOrderSummary.atlasInventoryOrderKey);fullfilledConfirm.close()" class="o-btn" style="margin-left:1.8rem;">Confirm</button>

                                    <button type="button" (click)="fullfilledConfirm.close()" class="o-btn o-btn--idle u-spacing--left-2" style="margin-left:1.8rem;">No</button>
                                  </div>

                      </app-modalbox>
                  </td>
            </tr>
          </tbody>
          <tbody class="table__tbody" *ngIf="!list?.inventoryOrders.length">
            <tr class="table__tr">
              <td colspan="5">
                <p class="u-padd--bottom-1 u-padd--1">
                  There are no results that match your search.
                </p>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  <div class="u-flex u-flex--spread u-flex--to-middle">
    <div class="" style="padding-top: 1rem;" *ngIf="list">
      <app-pagination
        [currentPage]="paginationCurrentPage"
        [totalRecords]="list.inventoryOrders.length"
        [recordsLimit]="10"
        (onPageChange)="paginationPageChange($event)"
      ></app-pagination>
    </div>  </div>
  </section>



  <section class="section box box--silver" style="width: 97%;left:25px">
    <div class="row padding-row" style="padding-left: 15px;">
      <div class="col-md-6">
          <h3>Order Items from RMV</h3>
      </div>
      <div class="col-md-4" *ngIf="isEvrFull">
        <h3>Update RMV Inventory Status</h3>
    </div>
  </div>

    <div class="row" style="padding-top:20px;">
      <div class="col-md-6">
        <div class="col-md-6">
          <sm-autocomplete [options]="orderOptions2" placeholder="Select item to order" [activeOption]="selectedItems[0]" (onSelect)="getSelectedItem()" [(ngModel)]="selectedItems[0]"></sm-autocomplete>

        </div>
        @if(detailedSelectedItem) {
        <div class="col-md-3">
          <sm-autocomplete [delayedOptions]="true" placeholder="Quantity" [options]="detailedSelectedItem?.maxAllowableOrder | maxQuantity" [(ngModel)]="quantity"></sm-autocomplete>

        </div>
      }
        <!-- <p-multiSelect [showHeader]="false" defaultLabel="Select items to order" [options]="orderOptions" [(ngModel)]="selectedItems" [filter]="false" optionLabel="inventoryItemDescription"></p-multiSelect> -->
        <!-- <p-dropdown placeholder="Select an Item" [options]="orderOptions"  optionLabel="inventoryItemDescription"></p-dropdown> -->
        <!-- <p-dropdown placeholder="" [options]="selectedItems[0]?.maxAllowableOrder | maxQuantity" [(ngModel)]="quantity"></p-dropdown> -->

        <button class="o-btn" style="margin-left:1.5rem" [disabled]="selectedItems.length < 1 || !quantity" (click)="orderConfirm.open()">Order</button>
</div>
<div class="col-md-6" *ngIf="isEvrFull">
    <div class="col-md-4"><sm-autocomplete  placeholder="Inventory Type" [activeOption]="status.inventoryType" (onSelect)="getSelectedTypeDescription()" [options]="inventoryTypeOptions" [(ngModel)]="status.inventoryType"> </sm-autocomplete></div>
    @if(status.inventoryType && !status.inventoryType.includes('Decals')) {
      <div class="col-md-2"><input placeholder="Plate Number" style="text-transform: uppercase;" (ngModelChange)="transformToUppercase()" [(ngModel)]="status.inventoryID" name="plateNumber"></div>
    }@else if(status.inventoryType.includes('Decals')) {
      <div class="col-md-2"><input type="text" appNumbersOnly [(ngModel)]="status.numberOfUnits" placeholder="No. Of Units" name="quantity"></div>
    }
    <div class="col-md-3">
      @if(!status.inventoryType.includes('Decals')) {
  <sm-autocomplete placeholder="Status" [options]="plateStatusOptions" [activeOption]="status.newInventoryStatus"  (onSelect)="getSelectedPlateStatusOption()" [disabled]="!status.inventoryType"  [(ngModel)]="status.newInventoryStatus"></sm-autocomplete>
      }@else {
  <sm-autocomplete placeholder="Status" [options]="inventoryReasonCodeOptions" [activeOption]="status.reasonCode" (onSelect)="getSelectedReasonOption()"  [(ngModel)]="status.reasonCode"></sm-autocomplete>

      }
  </div>
  <button class="o-btn" style="margin-left:1.5rem" [disabled]="disableUpdate()" (click)="updateConfirm.open()">Update</button>
</div>
</div>

  </section>
  <section style="display: flex;justify-content: space-between;padding-bottom: 100px;" >
    <div class="u-align-left" style="margin-top: 25px;margin-left:22px;">
      <button class="o-btn" [routerLink]="'/dashboard/rmv-services'">Return to RMV Dashboard</button>
    </div>
    <section style="display: flex;justify-content: space-between;" >
        <div class="u-align-right" style="margin-top: 25px; margin-right: 15px;">
          <button class="o-btn" *ngIf="isEvrFull" (click)="downloadInventoryReport()">Detail Inventory Report</button>
        </div>
        <div class="u-align-right" style="margin-top: 25px; margin-right: 15px;">
          <button class="o-btn" *ngIf="isEvrFull" (click)="downloadSummaryInventoryReport()">Summary Inventory Report</button>
        </div>
    </section>

  </section>

  <app-modalbox #orderConfirm>
    <br>

                <div class="box box--silver" style="margin-bottom: 20px;line-height:2.5">
                   <p>Are you sure you want to order the following:</p>
                   <p>
                    {{detailedSelectedItem?.inventoryItemDescription}} Quantity: {{quantity}}
                   </p>

                   </div>
                   <div class="col-xs-12 u-align-right u-remove-letter-spacing">
                  <button type="button" (click)="orderItems()" class="o-btn" style="margin-left:1.8rem;">Yes</button>

                  <button type="button" (click)="orderConfirm.close()" class="o-btn o-btn--idle u-spacing--left-2" style="margin-left:1.8rem;">No</button>
                </div>

    </app-modalbox>


    <app-modalbox #updateConfirm>
      <br>

                  <div class="box box--silver" style="margin-bottom: 20px;line-height:2.5">
                     <p>You are about to update your inventory</p>
                     <p>
                      @if(status.inventoryType && !status.inventoryType.includes('Decals')) {
                        {{selectedTypeDescription}}: {{status.inventoryID | uppercase}}, {{selectedPlateStatusOption}}
                      } @else if(status.inventoryType.includes('Decals')) {
                        {{selectedTypeDescription}}: {{status.numberOfUnits}}, {{selectedReasonOption}}
                      }
                     </p>
                    </div>
                  <div class="col-xs-12 u-align-right u-remove-letter-spacing">
                    <button type="button" (click)="updateStatus()" class="o-btn" style="margin-left:1.8rem;">Ok</button>

                    <button type="button" (click)="updateConfirm.close()" class="o-btn o-btn--idle u-spacing--left-2" style="margin-left:1.8rem;">Cancel</button>
                  </div>

      </app-modalbox>


  <app-modalbox #orderError>
    <br>

                <div class="box box--silver" style="margin-bottom: 20px;line-height:2.5">

                   <p *ngFor="let item of errorMessages">
                    {{item}}
                   </p>

                   </div>
                   <div class="col-xs-12 u-align-right u-remove-letter-spacing">

                  <button type="button" (click)="orderError.close()" class="o-btn o-btn--idle u-spacing--left-2" style="margin-left:1.8rem;">Close</button>
                </div>

    </app-modalbox>

    <app-modalbox #updateStatusBox>
      <br>

                  <div class="box box--silver" style="margin-bottom: 20px;line-height:2.5">
                    <p *ngIf="updateSuccess">Your Inventory Status Update was processed successfully</p>
                    <div *ngIf="!updateSuccess">
                      <p>Your Inventory Status Update was not successful for the following reasons:</p>
                      <p *ngFor="let item of updateStatusMessage">
                      {{item}}
                     </p>
                    </div>


                     </div>
                     <div class="col-xs-12 u-align-right u-remove-letter-spacing">

                    <button type="button" (click)="updateStatusBox.close()" class="o-btn o-btn--idle u-spacing--left-2" style="margin-left:1.8rem;">Close</button>
                  </div>

      </app-modalbox>




