<section class="section section--compact u-spacing--0-5">
  <div class="">
    <div class="row u-spacing--1">
      <div class="col-xs-12">
        <div class="box box--silver">

          <table class="form-table">
            <tr class=" form-table__row">
              <td class="form-table__cell u-width-150px u-t-weight--bold">
                Vehicle
              </td>
              <td class="form-table__cell u-width-150px u-t-weight--bold">
                VIN
              </td>
              <td class="form-table__cell u-width-150px u-t-weight--bold">
                Class Code
              </td>
              <td class="form-table__cell u-width-150px u-t-weight--bold">
                Garaged In
              </td>
              <td class="form-table__cell u-width-10px">
              </td>
              <td class="form-table__cell u-width-100px">
              </td>
            </tr>

            <ng-container *ngIf="vehicles && (vehicles.length >0)">
              <tr *ngFor="let veh of vehicles" class=" form-table__row">
                <td class="form-table__cell">
                  {{veh.year}} {{veh.make}}
                  {{veh.model}}</td>
                <td class="form-table__cell">{{veh.vin}}</td>
                <td class="form-table__cell">{{veh.classCode}}</td>
                <td class="form-table__cell"><span
                    *ngIf="veh.garagingAddress">{{veh.garagingAddress.city}},{{veh.garagingAddress.state}}</span></td>
                <td class="form-table__cell"><a class="o-action o-action--icon-less manage-list__btn"
                    (click)="formAddVehicleEdit(veh.resourceId)" id="edit-{{veh.resourceId}}">Edit</a></td>
                <td class="form-table__cell"><a class="o-action o-action--icon-less manage-list__btn"
                    (click)="formAddVehicleDelete(veh.resourceId)">Delete</a></td>
              </tr>
            </ng-container>
          </table>
          <button (click)="showAddVehicle(modalAddVehicle)" class="o-btn o-btn--action o-btn--i_round-plus"
            id="add-vehicle-modal">Add
            Vehicle</button>
        </div>
      </div>
    </div>
  </div>
  <div class="u-align-right" style="padding-top:5px;">
    <button class="o-btn" (click)="refVehicleLookup.checkVehicleList()" id="rmv-vehicle-lookup">Rmv Lookup</button>
  </div>
  <app-modalbox #modalRmvVehicle [launcher]="'#rmv-vehicle-lookup'">
    <h1 class="o-heading o-heading--red">RMV Lookup</h1>
    <div class="box box--silver u-spacing--1-5" >
      <app-commercial-vehicle-rmv-lookup #refVehicleLookup [refModalbox]=""></app-commercial-vehicle-rmv-lookup>
    </div>
    <div class="row u-spacing--2">
      <div class="col-xs-12 u-align-right">
        <!--button (click)="modalRmvVehicle.closeModalbox($event)" class="o-btn u-spacing--right-2">Lookup</button-->
        <button (click)="refVehicleLookup.rmvHandleLookupButtonAction($event, modalRmvVehicle)" class="o-btn"
          [disabled]="refVehicleLookup.checkForErrors()">Lookup</button>
          <button class="o-btn o-btn--outlined u-spacing--left-2"
                (click)="refVehicleLookup.addAnotherVehicle($event)">Add
                Another Vehicle</button>
        <button (click)="modalRmvVehicle.closeModalbox($event)" class="o-btn o-btn--idle u-spacing--left-2">Cancel</button>
      </div>
    </div>
    
  </app-modalbox>
  <app-modalbox #modalAddVehicle [css]="'u-width-800px u-margin-left-350px'">
    <h1 *ngIf="vehicleForm.formType == 'ADD'" class="o-heading o-heading--red">Add Vehicle</h1>
    <h1 *ngIf="vehicleForm.formType == 'UPDATE'" class="o-heading o-heading--red">Update Vehicle</h1>
    <div class="box box--silver u-spacing--1-5">
      <div class="row o-columns">
        <div class="col-xs-6">
          <table class="form-table form-table--fixed">
              <tr class="form-table__row" [ngClass]="{'is-required-field': !vinValidate(vehicleForm.vin)}">
                  <td class="form-table__cell u-width-80px">
                    VIN:
                  </td>
                  <td class="form-table__cell u-width-100px">
                    <input type="text" [(ngModel)]="vehicleForm.vin" id="vin" fieldAutofocus>
                  </td>

                </tr>
                <tr class="form-table__row">
                  <td class="form-table__cell u-width-80px">
                    Year:
                  </td>
                  <td class="form-table__cell u-width-100px">
                    <!-- <input type="text" pattern="\d*" (keydown)="numbersOnly($event)" [(ngModel)]="vehicleForm.year">-->
                    <sm-autocomplete #refYearOptions [options]="vehicleForm.yearOptions" [activeOption]="vehicleForm.year"
                      [name]="'com-veh-year'" [id]="'com-veh-year'" [searchFromBegining]="true" [required]="true"
                      (onSelect)="selectVehicleYear($event,false)">
                    </sm-autocomplete>
                  </td>

                </tr>
                <tr class="form-table__row">
                  <td class="form-table__cell u-width-80px">
                    Make:
                  </td>
                  <td class="form-table__cell u-width-100px">
                    <!-- <input type="text" [(ngModel)]="vehicleForm.make"> -->
                    <sm-autocomplete #refMakeOptions [options]="makeOptions" [activeOption]="vehicleForm.make"
                      [name]="'com-veh-make'" [id]="'com-veh-make'" [searchFromBegining]="true" [required]="true"
                      (onSelect)="selectVehicleMake($event,false)">
                    </sm-autocomplete>
                  </td>

                </tr>
                <tr class="form-table__row">
                  <td class="form-table__cell u-width-80px">
                    Model:
                  </td>
                  <td class="form-table__cell u-width-100px">
                    <input type="text" [(ngModel)]="vehicleForm.model">
                  </td>

                </tr>
                <tr class="form-table__row">
                  <td class="form-table__cell u-width-80px">
                    Body Type:
                  </td>
                  <td class="form-table__cell u-width-100px">
                    <sm-autocomplete #refBodyTypeOptions [options]="vehicleForm.bodyTypeOptions"
                      [activeOption]="vehicleForm.bodyType" [name]="'com-body-type'" [id]="'com-body-type'"
                      [required]="true" (onSelect)="selectBodyType($event,false)">
                    </sm-autocomplete>
                  </td>

                </tr>
                <tr class="form-table__row"  [ngClass]="{'is-required-field': !costValidate(vehicleForm.originalCostNew)}">
                    <td class="form-table__cell u-width-80px">
                      Original Cost New:
                    </td>
                    <td class="form-table__cell u-width-100px">
                      <input type="text" pattern="\d*" (keydown)="numbersOnly($event)"
                      id="originalCostNew_{{vehicleForm.vin}}" #originalCostNew
                        [(ngModel)]="vehicleForm.originalCostNew">
                    </td>

                  </tr>
                  <tr class="form-table__row">
                      <td class="form-table__cell u-width-80px">
                        Plate Number:
                      </td>
                      <td class="form-table__cell u-width-100px">
                        <input type="text" [(ngModel)]="vehicleForm.plateNumber">
                      </td>

                    </tr>
                    <tr class="form-table__row">
                      <td class="form-table__cell u-width-80px">
                        Plate Type:
                      </td>
                      <td class="form-table__cell u-width-100px">
                        <sm-autocomplete #refPlateTypeOptions [options]="vehicleForm.plateTypeOptions"
                          [activeOption]="vehicleForm.plateType" [name]="'com-plate-type'" [id]="'com-plate-type'"
                          [required]="true" (onSelect)="selectPlateType($event,false)">
                        </sm-autocomplete>
                      </td>

                    </tr>
                    <tr class="form-table__row" [ngClass]="{'is-required-field': !garageValidate()}">
                        <td class="form-table__cell u-width-80px">
                          Garaging:
                        </td>
                        <td class="form-table__cell u-width-100px">
                          <sm-autocomplete #refPlateTypeOptions [options]="vehicleForm.garagingOptions"
                            [activeOption]="vehicleForm.garaging" [name]="'com-gar_'+vehicleForm.vin" [id]="'com-gar'" [required]="true"
                            (onSelect)="selectGaragingOption($event,false)">
                          </sm-autocomplete>
                        </td>
                      </tr>

                          <tr class="form-table__row" *ngIf="newLocation">
                        <td class="form-table__cell u-width-80px">
                          Street:
                        </td>
                        <td class="form-table__cell u-width-100px">
                          <input type="text" [(ngModel)]="newLocationData.address1" (change)="onAddressDataChange(newLocationData,$event)">
                        </td>

                      </tr>
                      <tr class="form-table__row" *ngIf="newLocation">
                        <td class="form-table__cell u-width-80px">
                          City:
                        </td>
                        <td class="form-table__cell u-width-100px">
                          <sm-autocomplete #refVehicleCity [options]="optionsCity" [activeOption]="newLocationData.city"
                          [caseSensitiveOptionsIdMatching]="false" [(ngModel)]="newLocationData.city" [name]="'com-city'"
                          [id]="'com-city'" [searchFromBegining]="false" [allowEmptyValue]="false"
                          [allowCustomText]="false" [delayedOptions]="true"
                          (onSelect)="onCityDataChange(newLocationData,$event,'city')">
                        </sm-autocomplete>
                        </td>
                      </tr>
                      <table class="form-table form-table--fixed">
                      <tr class="form-table__row" *ngIf="newLocation">
                        <td class="form-table__cell u-width-145px">
                          State & Zip:
                        </td>
                        <td class="form-table__cell u-width-80px">
                          <sm-autocomplete #refVehicleState [options]="optionsState" [activeOption]="newLocationData.state"
                          [(ngModel)]="newLocationData.state"
                          [css]="(refVehicleState.ngModel?.invalid) ? 'error' : ''" [name]="'com-state'"
                          [id]="'com-state'" [searchFromBegining]="false" [allowEmptyValue]="false"
                          [allowCustomText]="false"
                          (onSelect)="onStateDataChange(newLocationData,$event)">
                        </sm-autocomplete>
                        </td>
                        <td class="form-table__cell u-width-80px"><input type="text" [(ngModel)]="newLocationData.zip"  number (change)="onZipDataChange(newLocationData,$event)"></td>
                      </tr>
                      </table>



          </table>
        </div>
          <div class="col-xs-6">
            <table class="form-table form-table--fixed">
                <tr class="form-table__row">
                  <td class="form-table__cell u-width-150px u-t-weight--bold"  colspan="2">CLASS ASSIGNMENT INFORMATION</td>
                </tr>
                <tr class="form-table__row">
                    <td class="form-table__cell u-width-80px">
                      Vehicle Weight:
                    </td>
                    <td class="form-table__cell u-width-100px">
                      <input type="text" pattern="\d*" (keydown)="numbersOnly($event)"
                        [(ngModel)]="vehicleForm.vehicleWeight">
                    </td>

                  </tr>
                  <tr class="form-table__row">
                      <td class="form-table__cell u-width-80px">
                        Radius:
                      </td>
                      <td class="form-table__cell u-width-100px">
                        <sm-autocomplete #refPlateTypeOptions [options]="vehicleForm.radiusOptions"
                          [activeOption]="vehicleForm.radius" [name]="'com-radius-opt'" [id]="'com-radius-opt'"
                          [required]="true" (onSelect)="selectRadiusOption($event,false)">
                        </sm-autocomplete>
                      </td>

                    </tr>
                    <tr class="form-table__row" [ngClass]="{'is-required-field': !vehicleUseValidate()}">
                        <td class="form-table__cell u-width-80px" >
                          Vehicle Use:
                        </td>
                        <td class="form-table__cell u-width-100px">
                          <sm-autocomplete #refPlateTypeOptions [options]="vehicleForm.vehicleUseOptions"
                            [activeOption]="vehicleForm.vehicleUse" [name]="'com-veh-use_'+vehicleForm.vin" [id]="'com-veh-use_'+vehicleForm.vin" [required]="true"
                            (onSelect)="selectVehicleUseOption($event,false)">
                          </sm-autocomplete>
                        </td>

                      </tr>
                      <tr class="form-table__row" [ngClass]="{'is-required-field': !vehicleTypeValidate()}">
                          <td class="form-table__cell u-width-80px">
                            Vehicle Type:
                          </td>
                          <td class="form-table__cell u-width-100px">
                            <sm-autocomplete #refVehicleTypeOptions [options]="vehicleForm.vehicleTypeOptions"
                            [activeOption]="vehicleForm.vehicleType"
                               [name]="'com-veh-type'" [id]="'com-veh-type_'+vehicleForm.vin"
                              [searchFromBegining]="true" [required]="true" (onSelect)="selectVehicleType($event,false)">
                            </sm-autocomplete>
                          </td>

                        </tr>
                        <tr class="form-table__row">
                            <td class="form-table__cell u-width-80px">
                              Size Class/Trailer Type:
                            </td>
                            <td class="form-table__cell u-width-100px">
                              <sm-autocomplete #refVehicleTypeOptions [options]="unitDescOptions"
                                [activeOption]="vehicleForm.unitDescription" [name]="'com-veh-size-class'" [id]="'com-veh-size-class'"
                                [searchFromBegining]="true" [required]="true" (onSelect)="selectUnitDesc($event,false)">
                              </sm-autocomplete>
                            </td>

                          </tr>

                          <tr class="form-table__row">
                              <td class="form-table__cell u-width-80px">
                                Secondary Class Type:
                              </td>
                              <td class="form-table__cell u-width-100px">
                                <sm-autocomplete #refVehicleSecondaryTypeOptions [options]="vehicleForm.vehicleSecondaryTypeOptions"
                                [activeOption]="vehicleForm.vehicleSecondaryType"
                                  [name]="'com-veh-type2'" [id]="'com-veh-type2'"
                                  [searchFromBegining]="true" [required]="true" (onSelect)="selectSecondaryType($event,false)">
                                </sm-autocomplete>
                              </td>

                            </tr>
                              <tr class="form-table__row">
                                  <td class="form-table__cell u-width-80px">
                                    Secondary Class Use:
                                  </td>
                                  <td class="form-table__cell u-width-100px">
                                    <sm-autocomplete #refVehicleSecondaryUseOptions [options]="vehicleForm.vehicleSecondaryUseOptions"
                                      [activeOption]="vehicleForm.vehicleSecondaryUse"
                                      [name]="'com-veh-2use'" [id]="'com-veh-2use'"
                                      [searchFromBegining]="true" [required]="true" (onSelect)="selectSecondaryUse($event,false)">
                                    </sm-autocomplete>
                                  </td>

                                </tr>
                            <tr class="form-table__row">
                                <td class="form-table__cell u-width-80px">
                                  Class Code:
                                </td>
                                <td class="form-table__cell u-width-100px">
                                  <input type="text" style="display: inline-block;" class="u-width-60px" readonly [(ngModel)]="vehicleForm.classCode">

                                </td>
                              </tr>
                              <tr class="form-table__row">
                                <td class="form-table__cell u-width-150px u-t-weight--bold o-link--red"  colspan="2">
        <h1 class="o-heading o-heading--red">{{vehicleForm.classCodeErrorMsg}}</h1>
        </td>
                              </tr>
            </table>

        </div>
      </div>
    </div>
    <div class="row u-spacing--2">
      <div class="col-xs-12 u-align-right">
        <button *ngIf="vehicleForm.formType == 'ADD'" [disabled]="formValid()" class="o-btn u-spacing--right-1" (click)="formAddVehicle(false)">
          ADD
        </button>
        <button *ngIf="vehicleForm.formType == 'ADD'" class="o-btn u-spacing--right-1" [disabled]="formValid()" (click)="formAddVehicle(true)">
          ADD AND NEW
        </button>
        <button *ngIf="vehicleForm.formType == 'UPDATE'" [disabled]="formValid()" class="o-btn u-spacing--right-1"  (click)="formUpdateVehicle()">
          UPDATE
        </button>
        <button class="o-btn o-btn--idle" (click)="modalAddVehicle.closeModalbox()">Cancel</button>
      </div>
    </div>
  </app-modalbox>
</section>
