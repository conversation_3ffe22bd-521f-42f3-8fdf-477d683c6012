import { StorageService } from 'app/shared/services/storage-new.service';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';


import { StubOptionsViewComponent } from 'testing/stubs/components/options-view.component';

import { DwellingCarrierOptionsComponent } from './dwelling-carrier-options.component';

describe('DwellingCarrierOptionsComponent', () => {
  let component: DwellingCarrierOptionsComponent;
  let fixture: ComponentFixture<DwellingCarrierOptionsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [
        DwellingCarrierOptionsComponent,
        StubOptionsViewComponent
      ],
      providers: [
        StorageService
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DwellingCarrierOptionsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });
});
