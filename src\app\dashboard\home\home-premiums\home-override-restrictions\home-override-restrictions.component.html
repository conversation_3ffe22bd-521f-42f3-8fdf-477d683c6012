<button id="overridesLauncher" (click)="openModalboxOverrides($event)" [disabled]="loading" class="o-btn o-btn--action o-btn--i_edit">
  Override Restrictions
</button>


<app-modalbox #refModalOverrides smToOverlay>
    <div class="u-flex u-flex--spread">
      <div class="">
        <h2 class="o-heading u-show-iblock u-align-v-middle u-spacing--right-1">Override Restrictions</h2>
      </div>
    </div>

    <div class="box box--silver u-spacing--1-5">
      <app-loader [loading]="updating" [cssClass]="'loader--content-top loader--with-opacity'" [loadingText]="'Updating, please wait...'"></app-loader>

      <p-scrollPanel styleClass="u-height-max-350px">

        <div *ngIf="guidelinesOverrides">
          <ul class="list list--nowrap">
            <li class="list__item">
              <label class="o-checkable o-checkable--block">
                <input type="checkbox" [(ngModel)]="guidelinesOverrides.overrideMinimumsAndMaximums">
                <i class="o-btn o-btn--checkbox"></i>
                Minimums and Maximums
              </label>
            </li>
            <li class="list__item u-spacing--1-5">
              <label class="o-checkable o-checkable--block">
                <input type="checkbox" [(ngModel)]="guidelinesOverrides.overridePlanYearBuilt">
                <i class="o-btn o-btn--checkbox"></i>
                Plan Year Built
              </label>
            </li>
            <li class="list__item u-spacing--1-5">
              <label class="o-checkable o-checkable--block">
                <input type="checkbox" [(ngModel)]="guidelinesOverrides.overrideEndorsementYearBuilt">
                <i class="o-btn o-btn--checkbox"></i>
                Endorsement Year Built
              </label>
            </li>
            <li class="list__item u-spacing--1-5">
              <label class="o-checkable o-checkable--block">
                <input type="checkbox" [(ngModel)]="guidelinesOverrides.overrideNumberOfFamiliesOrUnits">
                <i class="o-btn o-btn--checkbox"></i>
                Number of Families / Units
              </label>
            </li>
            <li class="list__item u-spacing--1-5">
              <label class="o-checkable o-checkable--block">
                <input type="checkbox" [(ngModel)]="guidelinesOverrides.overrideProtectionClass">
                <i class="o-btn o-btn--checkbox"></i>
                Protection Class
              </label>
            </li>
            <li class="list__item u-spacing--1-5">
              <label class="o-checkable o-checkable--block">
                <input type="checkbox" [(ngModel)]="guidelinesOverrides.overrideOccupancy">
                <i class="o-btn o-btn--checkbox"></i>
                Occupancy
              </label>
            </li>
            <li class="list__item u-spacing--1-5">
              <label class="o-checkable o-checkable--block">
                <input type="checkbox" [(ngModel)]="guidelinesOverrides.overrideCoastalDeductible">
                <i class="o-btn o-btn--checkbox"></i>
                Coastal Deductible
              </label>
            </li>
            <li class="list__item u-spacing--1-5">
              <label class="o-checkable o-checkable--block">
                <input type="checkbox" [(ngModel)]="guidelinesOverrides.overrideCoastalDistanceExclusions">
                <i class="o-btn o-btn--checkbox"></i>
                Coastal Distance Exclusions
              </label>
            </li>
          </ul>

          <hr *ngIf="isHome">

          <ul class="list list--nowrap" *ngIf="isHome">
            <li class="list__item">
              <label class="o-checkable o-checkable--block">
                <input type="checkbox" [(ngModel)]="guidelinesOverrides.applyNewHomeCredit">
                <i class="o-btn o-btn--checkbox"></i>
                Apply New Home Credit Automatically
              </label>
            </li>

            <li class="list__item u-spacing--1-5">
              <label class="o-checkable o-checkable--block">
                <input type="checkbox" [(ngModel)]="guidelinesOverrides.applyPreferredOrSuperiorHomeCredit">
                <i class="o-btn o-btn--checkbox"></i>
                Apply Preferred / Superior Home Automatically
              </label>
            </li>
          </ul>
        </div>

        <div *ngIf="!guidelinesOverrides">
          <p>There are no available Guideline Overrides</p>
        </div>
      </p-scrollPanel>
    </div>

    <div class="row u-spacing--2">
      <div class="col-xs-12 u-align-right">
        <button (click)="actionOnModalboxOverridesSave($event)" class="o-btn">Save</button>
        <button (click)="actionOnModalboxOverridesCancel($event)" class="o-btn o-btn--idle u-spacing--left-1-5">Cancel</button>
      </div>
    </div>

</app-modalbox>
