import { Injectable } from '@angular/core';
import * as signalR from '@microsoft/signalr';
import { ApiService } from 'app/shared/services/api.service';
import { MessageService } from 'primeng/api';

@Injectable({
  providedIn: 'root'
})
export class SignalRService {
  private hubConnection: signalR.HubConnection;

  constructor(private messageService: MessageService,private apiService:ApiService) {}

  startConnection() {
    this.hubConnection = new signalR.HubConnectionBuilder()
      .withUrl(this.apiService.url('/connectionHub'), {
        withCredentials: true
      })
      .withAutomaticReconnect()
      .build();

    this.hubConnection
      .start()
      .then(() => {
        console.log('SignalR connection started');
      })
      .catch(err => {
        console.error('Error while starting SignalR connection: ' + err);
      });

    // Monitor connection state
    this.hubConnection.onclose(() => {
    });

    this.hubConnection.onreconnected(() => {
      this.messageService.clear();
    });

    this.hubConnection.onreconnecting(() => {
      this.messageService.add({ severity: 'warn', summary: 'Network Error ', detail: ' Connection to the server lost.', closable: false,  });

    });
  }

}
