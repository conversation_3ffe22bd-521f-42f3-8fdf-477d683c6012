import { async, ComponentFixture, fakeAsync, inject, TestBed, tick } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { NavigationEnd, Router } from '@angular/router';

import { Observable } from 'rxjs';

import { AUTO_QUOTE_PLAN_LIST as RATING_PLANS_DATA } from 'testing/data/quotes/quote-plan-list/auto';
import { expect } from 'testing/helpers/all';
import { StubFilterComponent } from 'testing/stubs/components/filter.component';
import {
    StubHintsAndWarningsLinkComponent
} from 'testing/stubs/components/hints-and-warnings-link.component';
import { StubModalboxComponent } from 'testing/stubs/components/modalbox.component';
import {
    StubPerfectScrollbarComponent
} from 'testing/stubs/components/perfect-scrollbar.component';
import { StubLimitToPipe } from 'testing/stubs/pipes/limit-to.pipe';
import { MockRouter, MockRouterProvider } from 'testing/stubs/router.provider';

import {
    HintsAndWarningsService
} from 'app/hints-and-warnings/services/hints-and-warnings.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Helpers } from 'app/utils/helpers';

import { WarningItem } from '../../model/warnings';
import { HintsAndWarningsPanelComponent } from './hints-and-warnings-panel.component';

class MockHintsAndWarningsService {
  public allWarnings: WarningItem[] = [];
  public panelState = false;

  get allWarnings$() {
    return Observable.of(this.allWarnings);
  }

  get panelState$() {
    return Observable.of(this.panelState);
  }

  setPanelStateIsOpen() {}
}

describe('Component: HintsAndWarningsPanel', () => {
  let component: HintsAndWarningsPanelComponent;
  let fixture: ComponentFixture<HintsAndWarningsPanelComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [
        HintsAndWarningsPanelComponent,
        StubHintsAndWarningsLinkComponent,
        StubLimitToPipe,
        StubFilterComponent,
        StubModalboxComponent,
        StubPerfectScrollbarComponent
      ],
      providers: [
        {
          provide: HintsAndWarningsService, useClass: MockHintsAndWarningsService
        },
        StorageService,
        MockRouterProvider
      ]
    })
    .compileComponents();
  }));

  describe('when only default data is available', () => {
    beforeEach(fakeAsync(() => {
      fixture = TestBed.createComponent(HintsAndWarningsPanelComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
      tick();
    }));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });

    it('should destroy without errors', () => {
      expect(() => {
        fixture.destroy();
      }).not.toThrow();
    });

    it('should allow to toggle the panel', () => {
      component.toggleWarningPanel({ preventDefault: () => {} });

      expect(component.panelIsOpen).toBeTruthy();
    });
  });

  describe('when selected plan and warnings are available', () => {
    beforeEach(fakeAsync(inject([StorageService, HintsAndWarningsService],
      (storageService: StorageService, hintsService: MockHintsAndWarningsService) => {
        storageService.setStorageData('selectedPlan', Helpers.deepClone(RATING_PLANS_DATA));
        hintsService.allWarnings = [
          new WarningItem(true, 'first label', '', '', ['3', '12'], '', '', 'carrier'),
          new WarningItem(true, 'second label', '', '', ['10'], '', '', 'carrier'),
          new WarningItem(true, 'third label', '', '', [], '', '', 'general')
        ];

      fixture = TestBed.createComponent(HintsAndWarningsPanelComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
      tick();
    })));

    it('should create plans list', () => {
      expect(component.plans).toEqual([
        { id: 'all', text: 'All', data: null },
        { id: '1', text: 'ACE Bankers Standard', data: jasmine.anything() },
        { id: '10', text: 'AIG', data: jasmine.anything() },
        { id: '3', text: 'Amica', data: jasmine.anything() },
        { id: '6', text: 'Farm Family', data: jasmine.anything() },
        { id: '12', text: 'State Farm', data: jasmine.anything() }
      ]);
    });

    it('should initially set filter to All option', () => {
      expect(component.filterLabel).toEqual('All');
      expect(component.allWarnings.length).toEqual(3);
      expect(component.generalWarnings.length).toEqual(1);
      expect(component.carrierWarnings.length).toEqual(2);
      expect(component.filteredCarrierWarnings.length).toEqual(component.carrierWarnings.length);
    });

    it('should update filter label and items on filter change', fakeAsync(() => {
      const filterCmp: StubFilterComponent = fixture.debugElement.query(By.directive(StubFilterComponent)).componentInstance;

      filterCmp.onChange.emit({
        filter: '12',
        filterName: '',
        filterId: 'filter_carriers',
        options: [],
        selectedOption: {
          id: '12',
          text: 'State Farm'
        }
      });
      fixture.detectChanges();
      tick();

      const tmpData = new WarningItem();
      tmpData.label = 'first label';
      tmpData.carrier = ['3', '12'];

      expect(component.filterLabel).toEqual('State Farm');
      // expect(component.filteredCarrierWarnings).toEqual(jasmine.arrayContaining([
      //   tmpData
      // ]));

      expect(component.filteredCarrierWarnings).toEqual([
        jasmine.objectContaining({
          label: 'first label',
          carrier: ['3', '12']
        })
      ]);
    }));

    it('should react to navigation events', fakeAsync(inject([Router], (router: MockRouter) => {
      router.events.next(new NavigationEnd(0, '/dashboard/auto/premiums', null));

      fixture.detectChanges();
      tick();

      expect(component.panelIsOpen).toBeTruthy();
    })));

    it('should close panel if premums panel was open and toggle was requested', fakeAsync(inject([Router], (router: MockRouter) => {
      router.events.next(new NavigationEnd(0, '/dashboard/auto/premiums', null));

      fixture.detectChanges();
      tick();

      component.toggleWarningPanel({ preventDefault: () => { } });

      fixture.detectChanges();
      tick();

      expect(component.panelIsOpen).toBeFalsy();
    })));
  });

  describe('when panelState is open', () => {
    beforeEach(fakeAsync(inject([StorageService, HintsAndWarningsService],
      (storageService: StorageService, hintsService: MockHintsAndWarningsService) => {
        storageService.setStorageData('selectedPlan', Helpers.deepClone(RATING_PLANS_DATA));
        hintsService.allWarnings = [];
        hintsService.panelState = true;

        fixture = TestBed.createComponent(HintsAndWarningsPanelComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
        tick();
      })));

    it('should open warnings panel', fakeAsync(inject([HintsAndWarningsService], (hintsService: MockHintsAndWarningsService) => {
      expect(component.panelIsOpen).toBeTruthy();
    })));
  });
});
