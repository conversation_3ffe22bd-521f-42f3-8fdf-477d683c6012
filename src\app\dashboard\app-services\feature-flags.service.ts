import { Injectable } from '@angular/core';
import { FeatureService } from 'app/shared/services/feature.service';

//  FeatureFlagsService encapsulate Feature flags access here in case if we would need to access the same flag
//  from different places. It would be easy to maintain because they would be groupped here.
//  I have added just one flag "isRecentQuotesView", but we can refactor SprApp later and move the relevant code here. 

@Injectable()
export class FeatureFlagsService {

    public isRecentQuotesView = false;

    constructor(public featureService: FeatureService) {
      this.isRecentQuotesView = this.checkFeatureFlag("SprApp_RecentQuotesView");
    }

    private checkFeatureFlag( key: string) {
        const returnValue = JSON.parse(localStorage.getItem('features')).find(x => x.name === key);
        return returnValue && returnValue.enabled ;
    }

}
