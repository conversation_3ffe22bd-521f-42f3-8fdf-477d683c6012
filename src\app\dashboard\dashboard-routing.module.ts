import { SavedRmvServicesTableComponent } from './rmv-services/rmv-services-dashboard/saved-rmv-services-table/saved-rmv-services-table.component';
import { HistoryComponent } from './rmv-services/history/history.component';
import { InquiryComponent } from './rmv-services/inquiry/inquiry.component';
import { RtaPrefillComponent } from './rmv-services/rta-prefill/rta-prefill.component';
import { RmvServicesTabsNavComponent } from './rmv-services/rmv-services-tabs-nav/rmv-services-tabs-nav.component';
import { AsideRmvServicesComponent } from './tpl-aside/aside-rmv-services/aside-rmv-services.component';
import { DriveOtherCarComponent } from './commercial-auto/drive-other-car/drive-other-car.component';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { AsideAutoComponent } from './tpl-aside/aside-auto/aside-auto.component';
import { AsideDashboardComponent } from './tpl-aside/aside-dashboard/aside-dashboard.component';
import { AsideHomeComponent } from './tpl-aside/aside-home/aside-home.component';
import { AsideNewRmvComponent } from './tpl-aside/aside-new-rmv/aside-new-rmv.component';
import { AuthGuard } from 'app/shared/services/auth-guard.service';
import { AutoTabsNavComponent } from './auto/tabs-nav/tabs-nav.component';
import { HomeBasicsComponent } from './home/<USER>/home-basics.component';
import { CanDeactivateGuard } from 'app/shared/services/can-deactivate.service';
import { PageClientsComponent } from './page-clients/page-clients.component';
import { ContentHeadDefaultComponent } from './tpl-content-head/content-head-default/content-head-default.component';
import { ContentHeadGoToQuoteComponent } from './tpl-content-head/content-head-go-to-quote/content-head-go-to-quote.component';
import { ContentHeadWelcomeComponent } from './tpl-content-head/content-head-welcome/content-head-welcome.component';
import { CoveragesComponent } from './auto/coverages/coverages.component';
import { CoveragesNavComponent } from './auto/coverages/coverages-nav/coverages-nav.component';
import { DashboardComponent } from './dashboard.component';
import { DriverDetailsComponent } from './auto/drivers/driver-details/driver-details.component';
import { DriversComponent } from './auto/drivers/drivers.component';
import { DriversNavComponent } from './auto/drivers/drivers-nav/drivers-nav.component';
import { HomeDwellingComponent } from './home/<USER>/home-dwelling.component';
import { HomeOptionsComponent } from './home/<USER>/home-options.component';
import { HomePremiumsComponent } from './home/<USER>/home-premiums.component';
import { HomeTabsNavComponent } from './home/<USER>/tabs-nav.component';
import { QuoteImporterComponent } from 'app/dashboard/quote-importer/quote-importer.component';
import { OptionsComponent } from './auto/options/options.component';
import { PageDashboardMainComponent } from './page-dashboard-main/page-dashboard-main.component';
import { PageEnterDriversComponent } from './page-enter-drivers/page-enter-drivers.component';
import { PageQuoteInboxComponent } from './page-quote-inbox/page-quote-inbox.component';
import { PageQuotesComponent } from './page-quotes/page-quotes.component';
import { PageFormsComponent } from './page-forms/page-forms.component';
import { PageRmvResultsComponent } from './page-rmv-results/page-rmv-results.component';
import { PageEstampRequestsComponent } from './page-estamp-requests/page-estamp-requests.component';
import { PremiumsComponent } from './auto/premiums/premiums.component';
import { QuoteLoaderComponent } from './quote-loader/quote-loader.component';
import { HomeSchedulesComponent } from './home/<USER>/home-schedules.component';
import { AutoStandardCoveragesComponent } from './auto/coverages/auto-standard-coverages/auto-standard-coverages.component';
import { AutoCoveragesCopyCoveragesComponent } from './auto/coverages/auto-coverages-copy-coverages/auto-coverages-copy-coverages.component';
import { SummaryComponent } from './auto/summary/summary.component';
import { HomeUmbrellaComponent } from './home/<USER>/home-umbrella.component';
import { HomeUnderwritingComponent } from './home/<USER>/home-underwriting.component';
import { VehicleDetailsComponent } from './auto/vehicles/vehicle-details/vehicle-details.component';
import { VehiclesComponent } from './auto/vehicles/vehicles.component';
import { VehiclesNavComponent } from './auto/vehicles/vehicles-nav/vehicles-nav.component';
import { HomeOptionsNavComponent } from './home/<USER>/home-options-nav/home-options-nav.component';
import { HomeGeneralOptionsComponent } from './home/<USER>/home-general-options/home-general-options.component';
import { HomeCarrierOptionsComponent } from './home/<USER>/home-carrier-options/home-carrier-options.component';
import { HomeUserDefinedCoveragesComponent } from './home/<USER>/home-user-defined-coverages/home-user-defined-coverages.component';
import { DwellingTabsNavComponent } from './dwelling/tabs-nav/tabs-nav.component';
import { DwellingDwellingComponent } from './dwelling/dwelling-dwelling/dwelling-dwelling.component';
import { DwellingBasicsComponent } from './dwelling/dwelling-basics/dwelling-basics.component';
import { DwellingOptionsComponent } from './dwelling/dwelling-options/dwelling-options.component';
import { DwellingPremiumsComponent } from './dwelling/dwelling-premiums/dwelling-premiums.component';
import { AsideDwellingComponent } from './tpl-aside/aside-dwelling/aside-dwelling.component';
import { DwellingOptionsNavComponent } from './dwelling/dwelling-options/dwelling-options-nav/dwelling-options-nav.component';
import { DwellingCarrierOptionsComponent } from './dwelling/dwelling-options/dwelling-carrier-options/dwelling-carrier-options.component';
import { DwellingGeneralOptionsComponent } from './dwelling/dwelling-options/dwelling-general-options/dwelling-general-options.component';
import { UmbrellaTabsNavComponent } from './umbrella/umbrella-tabs-nav/umbrella-tabs-nav.component';
import { UmbrellaBasicComponent } from 'app/dashboard/umbrella/umbrella-basic/umbrella-basic.component';
import { AsideUmbrellaComponent } from './tpl-aside/aside-umbrella/aside-umbrella.component';
import { UmbrellaCarrierOptionsComponent } from './umbrella/umbrella-options/umbrella-carrier-options/umbrella-carrier-options.component';
import { UmbrellaPremiumsComponent } from './umbrella/umbrella-premiums/umbrella-premiums.component';
import { UmbrellaComponent } from './umbrella/umbrella.component';
import { AutoAdditionalCoveragesComponent } from './auto/coverages/auto-additional-coverages/auto-additional-coverages.component';

import { HintsAndWarningsComponent } from 'app/hints-and-warnings/hints-and-warnings.component';
import { CanLoadQuoteGuardService } from 'app/shared/services/can-load-quote-guard.service';
import { QuoteRecallStoredComponent } from 'app/dashboard/quote-recall-stored/quote-recall-stored.component';
import { ClientLoaderComponent } from './client/client-loader/client-loader.component';
import { ClientHeaderComponent } from './client/client-header/client-header.component';
import { ClientNavComponent } from './client/client-nav/client-nav.component';
import { ClientInfoComponent } from './client/client-info/client-info.component';
import { ClientQuotesAndFormsComponent } from './client/client-quotes-and-forms/client-quotes-and-forms.component';
import { CommercialAutoTabsNavComponent } from './commercial-auto/tabs-nav/commercial-auto-tabs-nav.component';
import { CommercialAutoLocationsComponent } from './commercial-auto/locations/commercial-auto-locations.component';
import { AsideCommercialAutoComponent } from './tpl-aside/aside-commercial-auto/aside-commercial-auto/aside-commercial-auto.component';
import { CommercialAutoDriversComponent } from './commercial-auto/drivers/commercial-auto-drivers.component';
import { CommercialAutoVehiclesComponent } from './commercial-auto/vehicles/commercial-auto-vehicles/commercial-auto-vehicles.component';
import { CommercialAutoPolicyInfoComponent } from './commercial-auto/policy-info/commercial-auto-policy-info.component';
import { CommericalAutoSubmitComponent } from './commercial-auto/submit/commerical-auto-submit.component';
import { CommercialAutoComponent } from './commercial-auto/commercial-auto.component';
import { DashboardTabsNavComponent } from './dashboard-tabs-nav/dashboard-tabs-nav.component';
import { RmvServicesComponent } from './rmv-services/rmv-services.component';
import { RegistrationComponent } from './rmv-services/reinstatement/registration.component';
import { RmvInquiryComponent } from './rmv-services/rmv-inquiry/rmv-inquiry.component';
import { RmvTransactionHistoryComponent } from './rmv-services/rmv-transaction-history/rmv-transaction-history.component';
import { RmvPrefillAsideComponent } from '../rmv-services/new-rmv-prefill/rmv-prefill-aside/rmv-prefill-aside.component';
import { PrefillServiceTypeComponent } from '../rmv-services/new-rmv-prefill/prefill-service-type/prefill-service-type.component';
import { PrefillVehicleComponent } from '../rmv-services/new-rmv-prefill/prefill-vehicle/prefill-vehicle.component';
import { PrefillOwnerComponent } from '../rmv-services/new-rmv-prefill/prefill-owner/prefill-owner.component';
import { NewRmvPrefillComponent } from './rmv-services/new-rmv-prefill/new-rmv-prefill.component';
import { PrefillPurchaseComponent } from '../rmv-services/new-rmv-prefill/prefill-purchase/prefill-purchase.component';
import { PrefillInsuranceComponent } from '../rmv-services/new-rmv-prefill/prefill-insurance/prefill-insurance.component';
import { RmvServicesDashboardComponent } from './rmv-services/rmv-services-dashboard/rmv-services-dashboard.component';
import { SavedEvrLiteTableComponent } from './rmv-services/rmv-services-dashboard/saved-evr-lite-table/saved-evr-lite-table.component';
import { DocumentDestructionTableComponent } from './rmv-services/document-destruction-table/document-destruction-table.component';
import { InventoryOrderComponent } from './rmv-services/inventory-order/inventory-order.component';
import { InventoryResolver } from './resolvers/inventory.resolver';
import { DocumentDestructionDetailsResolver } from './resolvers/document-destruction-details.resolver';
import { DashboardTabsResolver } from './resolvers/dashboard-tabs.resolver';
import {EvrliteComponent} from '../overlay/evrlite/evrlite.component';
import { DownloadCompletedDocsComponent } from './rmv-services/rmv-services-dashboard/download-completed-docs/download-completed-docs.component';
import { AdminRedirectComponent } from './admin-redirect/admin-redirect.component';

const routes: Routes = [
  {
    path: 'dashboard',
    component: DashboardComponent,
    data: { pageTitle: 'Dashboard' },
    canActivate: [AuthGuard],
    children: [
      {
        path: '',
        data: { pageTitle: 'Dashboard' },
        children: [
          { path: '', component: PageDashboardMainComponent },
          {
            path: '',
            component: DashboardTabsNavComponent,
            outlet: 'contentHead',
          },
          { path: '', component: AsideDashboardComponent, outlet: 'aside' },
          {
            path: '',
            component: ContentHeadWelcomeComponent,
            outlet: 'contentHead',
          },
        ],
      },
      {
        path: 'quotes',
        data: { pageTitle: 'Quotes' },
        children: [
          { path: '', component: PageQuotesComponent },
          { path: '', component: AsideDashboardComponent, outlet: 'aside' },
          {
            path: '',
            component: DashboardTabsNavComponent,
            outlet: 'contentHead',
          },
        ],
      },
      {
        path: 'quotes/new',
        data: { pageTitle: 'Creating Quote' },
        children: [
          { path: '', component: QuoteLoaderComponent },
          { path: '', component: AsideDashboardComponent, outlet: 'aside' },
          {
            path: '',
            component: ContentHeadDefaultComponent,
            outlet: 'contentHead',
          },
        ],
      },
      {
        path: 'quotes/:id',
        data: { pageTitle: 'Quote Data Loading...' },
        children: [
          { path: '', component: QuoteLoaderComponent },
          { path: '', component: AsideDashboardComponent, outlet: 'aside' },
          {
            path: '',
            component: ContentHeadDefaultComponent,
            outlet: 'contentHead',
          },
        ],
      },
      {
        path: 'quotes/importQuote/:id',
        data: { pageTitle: 'Quote import' },
        children: [
          { path: '', component: QuoteImporterComponent },
          { path: '', component: AsideDashboardComponent, outlet: 'aside' },
          {
            path: '',
            component: ContentHeadDefaultComponent,
            outlet: 'contentHead',
          },
        ],
      },
      {
        path: 'quotes/recallQuote/:id',
        data: { pageTitle: 'Quote import' },
        children: [
          { path: '', component: QuoteRecallStoredComponent },
          { path: '', component: AsideDashboardComponent, outlet: 'aside' },
          {
            path: '',
            component: ContentHeadDefaultComponent,
            outlet: 'contentHead',
          },
        ],
      },
      {
        path: 'quote-inbox',
        data: { pageTitle: 'Leads Inbox' },
        children: [
          { path: '', component: PageQuoteInboxComponent },
          { path: '', component: AsideDashboardComponent, outlet: 'aside' },
          {
            path: '',
            component: DashboardTabsNavComponent,
            outlet: 'contentHead',
          },
        ],
      },
      {
        path: 'forms',
        data: { pageTitle: 'Forms' },
        children: [
          { path: '', component: PageFormsComponent },
          { path: '', component: AsideDashboardComponent, outlet: 'aside' },
          {
            path: '',
            component: DashboardTabsNavComponent,
            outlet: 'contentHead',
          },
        ],
      },
      {
        path: 'eStamp-requests',
        data: { pageTitle: 'Estamp Requests' },
        children: [
          { path: '', component: PageEstampRequestsComponent },
          {
            path: '',
            component: AsideDashboardComponent,
            outlet: 'aside',
          },
          {
            path: '',
            component: DashboardTabsNavComponent,
            outlet: 'contentHead',
          },
        ],
      },
      {
        path: 'clients',
        data: { pageTitle: 'Clients' },
        children: [
          { path: '', component: PageClientsComponent },
          {
            path: '',
            component: AsideDashboardComponent,
            outlet: 'aside',
          },
          {
            path: '',
            component: DashboardTabsNavComponent,
            outlet: 'contentHead',
          },
        ],
      },

      // --------
      // CLIENTS ROUTING
      {
        path: 'clients',
        data: { pageTitle: 'Client' },
        children: [
          { path: '', component: ClientNavComponent, outlet: 'aside' },
          {
            path: '',
            component: HintsAndWarningsComponent,
            outlet: 'warnings',
          },
          {
            path: ':id',
            data: { pageTitle: 'Client' },
            children: [
              {
                path: '',
                component: ClientLoaderComponent,
                data: { pageTitle: 'Client Data Loading...' },
              },
              {
                path: '',
                component: ClientHeaderComponent,
                outlet: 'contentHead',
              },
              {
                path: 'clientInfo',
                data: { pageTitle: 'Client Info' },
                component: ClientInfoComponent,
                canDeactivate: [CanDeactivateGuard],
              },
              {
                path: 'clientQuotesAndForms',
                data: { pageTitle: 'Client Quotes and Forms' },
                component: ClientQuotesAndFormsComponent,
                canDeactivate: [CanDeactivateGuard],
              },
            ],
          },
        ],
      },

      // --------
      // AUTO ROUTING
      {
        path: 'auto',
        data: { pageTitle: 'Auto' },
        children: [
          { path: '', component: AsideAutoComponent, outlet: 'aside' },
          {
            path: '',
            component: HintsAndWarningsComponent,
            outlet: 'warnings',
          },
          { path: '', component: AutoTabsNavComponent, outlet: 'contentHead' },
          {
            path: 'quotes',
            data: { pageTitle: 'Quotes' },
            children: [
              {
                path: ':id',
                data: { pageTitle: 'Quote' },
                canActivate: [CanLoadQuoteGuardService],
                children: [
                  { path: '', component: QuoteLoaderComponent },
                  {
                    path: 'drivers',
                    data: { pageTitle: 'Auto Drivers', carrier: 'auto' },
                    children: [
                      {
                        path: '',
                        component: DriversComponent,
                        children: [
                          {
                            path: ':id',
                            component: DriverDetailsComponent,
                            data: {
                              pageTitle: 'Auto Drivers',
                              carrier: 'auto',
                            },
                            canDeactivate: [CanDeactivateGuard],
                          },
                          {
                            path: '',
                            component: DriverDetailsComponent,
                            data: {
                              pageTitle: 'Auto Add Driver',
                              carrier: 'auto',
                            },
                          },
                        ],
                      },
                      {
                        path: '',
                        component: DriversNavComponent,
                        outlet: 'contentBox1',
                      },
                    ],
                  },
                  {
                    path: 'vehicles',
                    data: { pageTitle: 'Auto Vehicles', carrier: 'auto' },
                    children: [
                      {
                        path: '',
                        component: VehiclesComponent,
                        children: [
                          {
                            path: ':vehicleId',
                            component: VehicleDetailsComponent,
                            data: {
                              pageTitle: 'Auto Vehicles',
                              carrier: 'auto',
                            },
                            canDeactivate: [CanDeactivateGuard],
                          },
                          {
                            path: '',
                            component: VehicleDetailsComponent,
                            data: {
                              pageTitle: 'Auto Add Vehicle',
                              carrier: 'auto',
                            },
                          },
                        ],
                      },
                      {
                        path: '',
                        component: VehiclesNavComponent,
                        outlet: 'contentBox1',
                      },
                    ],
                  },
                  {
                    path: 'coverages',
                    data: { pageTitle: 'Auto Coverages', carrier: 'auto' },
                    children: [
                      {
                        path: '',
                        component: CoveragesComponent,
                        children: [
                          {
                            path: '',
                            component: AutoStandardCoveragesComponent,
                          },
                          {
                            path: 'additional-options',
                            component: AutoAdditionalCoveragesComponent,
                            data: {
                              pageTitle: 'Auto Coverages, Additional Options',
                              carrier: 'auto',
                              additionalParam: 'addToggleButton',
                            },
                          },
                        ],
                        canDeactivate: [CanDeactivateGuard],
                      },
                      {
                        path: '',
                        component: CoveragesNavComponent,
                        outlet: 'contentBox1',
                      },
                    ],
                  },
                  {
                    path: 'options',
                    data: { pageTitle: 'Auto Options', carrier: 'auto' },
                    component: OptionsComponent,
                    canDeactivate: [CanDeactivateGuard],
                  },
                  {
                    path: 'premiums',
                    data: { pageTitle: 'Auto Premiums', carrier: 'auto' },
                    component: PremiumsComponent,
                    canDeactivate: [CanDeactivateGuard],
                  },
                  {
                    path: 'forms',
                    data: { pageTitle: 'Auto Forms', additionalParam: 'AUTOP' },
                    component: PageFormsComponent,
                    canDeactivate: [CanDeactivateGuard],
                  },
                  {
                    path: 'summary',
                    data: { pageTitle: 'Auto Summary', carrier: 'auto' },
                    component: SummaryComponent,
                  },
                ],
              },
            ],
          },
        ],
      },
      {
        path: 'auto/rmv-results',
        data: { pageTitle: 'RMV Results' },
        children: [
          { path: '', component: PageRmvResultsComponent },
          { path: '', component: AsideNewRmvComponent, outlet: 'aside' },
          {
            path: '',
            component: ContentHeadGoToQuoteComponent,
            outlet: 'contentHead',
          },
        ],
      },
      {
        path: 'auto/new-rmv-quote',
        data: { pageTitle: 'Enter Drivers' },
        children: [
          { path: '', component: PageEnterDriversComponent },
          { path: '', component: AsideNewRmvComponent, outlet: 'aside' },
          {
            path: '',
            component: ContentHeadDefaultComponent,
            outlet: 'contentHead',
          },
        ],
      },
      {
        path: 'auto/rmv-load/quotes/:id',
        data: { pageTitle: 'Enter Drivers' },
        children: [
          {
            path: '',
            component: QuoteLoaderComponent,
            data: { additionalParam: 'preventRmvQuoteDataClear' },
          },
          { path: '', component: AsideNewRmvComponent, outlet: 'aside' },
          {
            path: '',
            component: ContentHeadDefaultComponent,
            outlet: 'contentHead',
          },
        ],
      },
      // HOME ROUTING
      {
        path: 'home',
        data: { pageTitle: 'Home' },
        children: [
          { path: '', component: AsideHomeComponent, outlet: 'aside' },
          {
            path: '',
            component: HintsAndWarningsComponent,
            outlet: 'warnings',
          },
          { path: '', component: HomeTabsNavComponent, outlet: 'contentHead' },
          {
            path: 'quotes',
            data: { pageTitle: 'Quotes' },
            children: [
              {
                path: ':id',
                data: { pageTitle: 'Quote' },
                canActivate: [CanLoadQuoteGuardService],
                children: [
                  { path: '', component: QuoteLoaderComponent },
                  {
                    path: 'dwelling',
                    data: { pageTitle: 'Home Dwelling', carrier: 'home' },
                    component: HomeDwellingComponent,
                    canDeactivate: [CanDeactivateGuard],
                  },
                  {
                    path: 'basics',
                    data: { pageTitle: 'Basics', carrier: 'home' },
                    component: HomeBasicsComponent,
                    canDeactivate: [CanDeactivateGuard],
                  },
                  {
                    path: 'options',
                    data: { pageTitle: 'Options', carrier: 'home' },
                    children: [
                      {
                        path: '',
                        component: HomeOptionsComponent,
                        children: [
                          {
                            path: '',
                            component: HomeCarrierOptionsComponent,
                            data: {
                              pageTitle: 'Home Carrier Options',
                              carrier: 'home',
                            },
                          },
                          {
                            path: 'general',
                            component: HomeGeneralOptionsComponent,
                            data: {
                              pageTitle: 'Home General Options',
                              carrier: 'home',
                            },
                          },
                          {
                            path: 'user-defined',
                            component: HomeUserDefinedCoveragesComponent,
                            data: {
                              pageTitle: 'Home Carrier Options',
                              carrier: 'home',
                            },
                          },
                        ],
                        canDeactivate: [CanDeactivateGuard],
                      },
                      {
                        path: '',
                        component: HomeOptionsNavComponent,
                        outlet: 'contentBox1',
                      },
                    ],
                  },

                  {
                    path: 'underwriting',
                    data: { pageTitle: 'Underwriting', carrier: 'home' },
                    component: HomeUnderwritingComponent,
                    canDeactivate: [CanDeactivateGuard],
                  },

                  {
                    path: 'schedules',
                    data: { pageTitle: 'Schedules', carrier: 'home' },
                    component: HomeSchedulesComponent,
                    canDeactivate: [CanDeactivateGuard],
                  },
                  {
                    path: 'umbrella',
                    data: { pageTitle: 'Umbrella', carrier: 'home' },
                    component: HomeUmbrellaComponent,
                    canDeactivate: [CanDeactivateGuard],
                  },
                  {
                    path: 'premiums',
                    data: { pageTitle: 'Premiums', carrier: 'home' },
                    component: HomePremiumsComponent,
                    canDeactivate: [CanDeactivateGuard],
                  },
                  {
                    path: 'forms',
                    data: { pageTitle: 'Home Forms', additionalParam: 'HOME' },
                    component: PageFormsComponent,
                    canDeactivate: [CanDeactivateGuard],
                  },
                ],
              },
            ],
          },
        ],
      },

      // DWELLING ROUTING
      {
        path: 'dwelling',
        data: { pageTitle: 'Dwelling' },
        children: [
          { path: '', component: AsideDwellingComponent, outlet: 'aside' },
          {
            path: '',
            component: HintsAndWarningsComponent,
            outlet: 'warnings',
          },
          {
            path: '',
            component: DwellingTabsNavComponent,
            outlet: 'contentHead',
          },
          {
            path: 'quotes',
            data: { pageTitle: 'Quotes' },
            children: [
              {
                path: ':id',
                data: { pageTitle: 'Quote' },
                canActivate: [CanLoadQuoteGuardService],
                children: [
                  { path: '', component: QuoteLoaderComponent },
                  {
                    path: 'dwelling',
                    data: { pageTitle: 'Dwelling', carrier: 'dwelling' },
                    component: DwellingDwellingComponent,
                    canDeactivate: [CanDeactivateGuard],
                  },
                  {
                    path: 'basics',
                    data: { pageTitle: 'Basics', carrier: 'dwelling' },
                    component: DwellingBasicsComponent,
                    canDeactivate: [CanDeactivateGuard],
                  },
                  {
                    path: 'options',
                    data: { pageTitle: 'Options', carrier: 'dwelling' },
                    children: [
                      {
                        path: '',
                        component: DwellingOptionsComponent,
                        children: [
                          {
                            path: '',
                            component: DwellingGeneralOptionsComponent,
                            data: {
                              pageTitle: 'Dwelling General Options',
                              carrier: 'dwelling',
                            },
                          },
                          {
                            path: 'carrier',
                            component: DwellingCarrierOptionsComponent,
                            data: {
                              pageTitle: 'Dwelling Carrier Options',
                              carrier: 'dwelling',
                            },
                          },
                        ],
                        canDeactivate: [CanDeactivateGuard],
                      },
                      {
                        path: '',
                        component: DwellingOptionsNavComponent,
                        outlet: 'contentBox1',
                      },
                    ],
                  },
                  {
                    path: 'premiums',
                    data: { pageTitle: 'Premiums', carrier: 'dwelling' },
                    component: DwellingPremiumsComponent,
                    canDeactivate: [CanDeactivateGuard],
                  },
                  {
                    path: 'forms',
                    data: {
                      pageTitle: 'Dwelling Forms',
                      additionalParam: 'DFIRE',
                    },
                    component: PageFormsComponent,
                    canDeactivate: [CanDeactivateGuard],
                  },
                ],
              },
            ],
          },
        ],
      },

      // UMBRELLA ROUTING
      {
        path: 'umbrella',
        data: { pageTitle: 'umbrella' },
        children: [
          { path: '', component: AsideUmbrellaComponent, outlet: 'aside' },
          {
            path: '',
            component: HintsAndWarningsComponent,
            outlet: 'warnings',
          },
          {
            path: '',
            component: UmbrellaTabsNavComponent,
            outlet: 'contentHead',
          },
          {
            path: '',
            component: UmbrellaComponent,
            canDeactivate: [CanDeactivateGuard],
            children: [
              {
                path: 'quotes',
                data: { pageTitle: 'Quotes' },
                children: [
                  {
                    path: ':id',
                    data: { pageTitle: 'Quote' },
                    canActivate: [CanLoadQuoteGuardService],
                    children: [
                      { path: '', component: QuoteLoaderComponent },
                      {
                        path: 'basics',
                        data: { pageTitle: 'Basics', carrier: 'umbrella' },
                        component: UmbrellaBasicComponent,
                      },
                      {
                        path: 'options',
                        data: {
                          pageTitle: 'Carrier Options',
                          carrier: 'umbrella',
                        },
                        component: UmbrellaCarrierOptionsComponent,
                      },
                      {
                        path: 'premiums',
                        data: { pageTitle: 'Premiums', carrier: 'umbrella' },
                        component: UmbrellaPremiumsComponent,
                      },
                      {
                        path: 'forms',
                        data: {
                          pageTitle: 'Umbrella Forms',
                          additionalParam: 'PUMBR',
                        },
                        component: PageFormsComponent,
                        // canDeactivate: [CanDeactivateGuard]
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
      // COMMERCIAL AUTO ROUTING
      {
        path: 'commercial-auto',
        data: { pageTitle: 'Commercial Auto' },
        children: [
          {
            path: '',
            component: AsideCommercialAutoComponent,
            outlet: 'aside',
          },
          {
            path: '',
            component: HintsAndWarningsComponent,
            outlet: 'warnings',
          },
          {
            path: '',
            component: CommercialAutoTabsNavComponent,
            outlet: 'contentHead',
          },
          {
            path: '',
            component: CommercialAutoComponent,
            canDeactivate: [CanDeactivateGuard],
            children: [
              {
                path: 'quotes',
                data: { pageTitle: 'Quotes' },
                children: [
                  {
                    path: ':id',
                    data: { pageTitle: 'Quote' },
                    canActivate: [CanLoadQuoteGuardService],
                    children: [
                      { path: '', component: QuoteLoaderComponent },
                      {
                        path: 'locations',
                        data: {
                          pageTitle: 'Locations',
                          carrier: 'commercialauto',
                        },
                        component: CommercialAutoLocationsComponent,
                      },
                      {
                        path: 'drivers',
                        data: {
                          pageTitle: 'Auto Drivers',
                          carrier: 'commercialauto',
                        },
                        component: CommercialAutoDriversComponent,
                      },
                      {
                        path: 'vehicles',
                        data: {
                          pageTitle: 'Auto Vehicles',
                          carrier: 'commercialauto',
                        },
                        component: CommercialAutoVehiclesComponent,
                      },
                      {
                        path: 'coverages',
                        data: {
                          pageTitle: 'Policy Info',
                          carrier: 'commercialauto',
                        },
                        component: CommercialAutoPolicyInfoComponent,
                      },
                      {
                        path: 'doc',
                        data: {
                          pageTitle: 'Drive Other Car',
                          carrier: 'commercialauto',
                        },
                        component: DriveOtherCarComponent,
                      },
                      {
                        path: 'submit',
                        data: {
                          pageTitle: 'Submit',
                          carrier: 'commercialauto',
                        },
                        component: CommericalAutoSubmitComponent,
                      },
                    ],
                  },
                ],
              },
            ],
          },
        ],
      },
      // RMV Services
      {
        path: 'rmv-services',
        canActivate: [AuthGuard],
        data: { pageTitle: 'RMV Services' },
        children: [
          { path: '', component: AsideRmvServicesComponent, outlet: 'aside' },

          {
            path: '',
            component: RmvServicesComponent,
            canDeactivate: [CanDeactivateGuard],
            children: [
              { path: '', component: RmvServicesDashboardComponent },
              { path: 'saved-evr-lite', component: SavedEvrLiteTableComponent },
              { path: 'download-documents', component: DownloadCompletedDocsComponent },

              {
                path: 'saved-rmv-services',
                component: SavedRmvServicesTableComponent,
              },
              {
                path: 'evr-document-destruction',
                component: DocumentDestructionTableComponent,
                resolve: {
                  documentDetailsResolver: DocumentDestructionDetailsResolver
                }
              },
              {
                path: 'inventory-order',
                component: InventoryOrderComponent,
                resolve: {
                  inventoryResolver: InventoryResolver
                }
              },

              {
                path: 'rta-prefill',
                component: NewRmvPrefillComponent,
                canDeactivate: [CanDeactivateGuard],
                children: [
                  {
                    path: 'service-type',
                    component: PrefillServiceTypeComponent,
                  },
                  { path: 'vehicle', component: PrefillVehicleComponent },
                  { path: 'owner', component: PrefillOwnerComponent },
                  { path: 'purchase', component: PrefillPurchaseComponent },
                  { path: 'insurance', component: PrefillInsuranceComponent },
                ],
              },
              {path: 'evr-lite', component: EvrliteComponent},
              {
                path: 'inquiry',
                data: { pageTitle: 'Inquiry' },
                component: RmvInquiryComponent,
              },
              {
                path: 'reinstatement',
                data: {
                  pageTitle: 'Registration Reinstatement',
                  transactionType: 'RegistrationReinstatementDataValidation',
                  proceedType: 'RegistrationReinstatement',
                },
                component: RegistrationComponent,
              },
              {
                path: 'renewal',
                data: {
                  pageTitle: 'Registration Renewal',
                  transactionType: 'RegistrationRenewalDataValidation',
                  proceedType: 'RegistrationRenewal',
                },
                component: RegistrationComponent,
              },
              {
                path: 'history',
                data: { pageTitle: 'History' },
                component: RmvTransactionHistoryComponent,
              },
              { path: '', redirectTo: 'rta-prefill', pathMatch: 'full' },
            ],
          },
        ],
      },
      {
        path: 'admin-redirect/:wildcard',
        data: { pageTitle: 'Admin Redirect' },
        component: AdminRedirectComponent
      }
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
  providers: [InventoryResolver, DashboardTabsResolver]
})
export class DashboardRoutingModule {}
