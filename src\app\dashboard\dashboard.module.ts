import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from 'app/shared/shared.module';
import { MatTabsModule } from '@angular/material/tabs';
import { TooltipModule } from 'primeng/tooltip';


import { FormsService } from './app-services/forms.service';
import { EstampService } from './app-services/estamp.service';
import { LeadsService } from './app-services/leads.service';
import { QuotesService } from './app-services/quotes.service';
import { StubQuotesService } from '../../testing/stubs/services/quotes.service.provider';
import { SubsService } from './app-services/subs.service';
import { SpecsService } from './app-services/specs.service';
import { DatesService } from './app-services/dates.service';

import { DriversService } from './app-services/drivers.service';
import { VehiclesService } from './app-services/vehicles.service';
import { LocationsService } from './app-services/locations.service';
import { IncidentsService } from './app-services/incidents.service';
import { LookupsService } from './app-services/lookups.service';
import { ClientsService } from './app-services/clients.service';
import { CoveragesService } from './app-services/coverages.service';
import { OptionsService } from './app-services/options.service';
import { RmvService } from './app-services/rmv.service';
import { PlansService } from './app-services/plans.service';
import { SymbolsService } from './app-services/symbols.service';
import { LoginService } from 'app/login/login.service';
import { DwellingService } from './app-services/dwelling.service';
import { UmbrellaService } from './app-services/umbrella.service';

import { DashboardRoutingModule } from './dashboard-routing.module';
import { DashboardComponent } from './dashboard.component';
import { HeaderComponent } from './tpl-header/header.component';
import { AutoTabsNavComponent } from './auto/tabs-nav/tabs-nav.component';
import { HomeTabsNavComponent } from './home/<USER>/tabs-nav.component';

import { PageDashboardMainComponent } from './page-dashboard-main/page-dashboard-main.component';
import { QuoteInboxComponent } from './page-dashboard-main/quote-inbox/quote-inbox.component';
import { PageQuoteInboxComponent } from './page-quote-inbox/page-quote-inbox.component';
import { PageQuotesComponent } from './page-quotes/page-quotes.component';
import { PageFormsComponent } from './page-forms/page-forms.component';
import { PageClientsComponent } from './page-clients/page-clients.component';
import { PageEstampRequestsComponent } from './page-estamp-requests/page-estamp-requests.component';


import { SummaryComponent } from './auto/summary/summary.component';
import { DriversComponent } from './auto/drivers/drivers.component';
import { DriverDetailsComponent } from './auto/drivers/driver-details/driver-details.component';
import { DriversNavComponent } from './auto/drivers/drivers-nav/drivers-nav.component';
import { PolicyComponent } from './auto/options/policy/policy.component';
import { OptionsComponent } from './auto/options/options.component';
import { CoveragesComponent } from './auto/coverages/coverages.component';
import { CoveragesNavComponent } from './auto/coverages/coverages-nav/coverages-nav.component';
import { PremiumsComponent } from './auto/premiums/premiums.component';
import { PremiumsTableComponent } from './auto/premiums/premiums-table/premiums-table.component';
import { VehiclesComponent } from './auto/vehicles/vehicles.component';
import { CarrierOptionsAutomanagerComponent } from './auto/options/carrier-options-automanager/carrier-options-automanager.component';
import { CarrierOptionsComponent } from './auto/options/carrier-options/carrier-options.component';

import { VehicleDetailsComponent } from './auto/vehicles/vehicle-details/vehicle-details.component';

import { VehiclesNavComponent } from './auto/vehicles/vehicles-nav/vehicles-nav.component';
import { SummaryDriversComponent } from './auto/summary/summary-drivers/summary-drivers.component';
import { SummaryVehiclesComponent } from './auto/summary/summary-vehicles/summary-vehicles.component';
import { SummaryRmvDetailsComponent } from './auto/summary/summary-rmv-details/summary-rmv-details.component';
import { SummaryHistoryComponent } from './auto/summary/summary-history/summary-history.component';
import { SummaryCarrierOptionsSelectedComponent } from './auto/summary/summary-carrier-options-selected/summary-carrier-options-selected.component';
import { NewAutoCreateFormComponent } from './auto/new-auto-create-form/new-auto-create-form.component';

import { PageRmvResultsComponent } from './page-rmv-results/page-rmv-results.component';
import { PageEnterDriversComponent } from './page-enter-drivers/page-enter-drivers.component';

import { AsideDashboardComponent } from './tpl-aside/aside-dashboard/aside-dashboard.component';
import { AsideAutoComponent } from './tpl-aside/aside-auto/aside-auto.component';
import { AsideNoSavePanelComponent } from './tpl-aside/aside-no-save-panel/aside-no-save-panel.component';
import { AsideButtonsMenuComponent } from './tpl-aside/components/aside-buttons-menu/aside-buttons-menu.component';
import { AsideAutoQuoteComponent } from './tpl-aside/components/aside-auto-quote/aside-auto-quote.component';
import { AsideQuickLinksInfoComponent } from './tpl-aside/components/aside-quick-links-info/aside-quick-links-info.component';
import { AsideSavePanelComponent } from './tpl-aside/components/aside-save-panel/aside-save-panel.component';

import { ContentHeadDefaultComponent } from './tpl-content-head/content-head-default/content-head-default.component';
import { ContentHeadWelcomeComponent } from './tpl-content-head/content-head-welcome/content-head-welcome.component';
import { ContentHeadGoToQuoteComponent } from './tpl-content-head/content-head-go-to-quote/content-head-go-to-quote.component';

import { OverlayModule } from 'app/overlay/overlay.module';
import { HintsAndWarningsModule } from 'app/hints-and-warnings/hints-and-warnings.module';
import { VehicleOptionsComponent } from './auto/vehicles/vehicle-options/vehicle-options.component';
import { VehicleSymbolComponent } from './auto/vehicles/vehicle-symbol/vehicle-symbol.component';
import { RmvDriverLookupComponent } from './auto/drivers/rmv-driver-lookup/rmv-driver-lookup.component';
import { AsideNewRmvComponent } from './tpl-aside/aside-new-rmv/aside-new-rmv.component';
import { VehiclesSymbolsAutomanagerComponent } from './auto/vehicles/vehicles-symbols-automanager/vehicles-symbols-automanager.component';
import { QuoteImporterComponent } from 'app/dashboard/quote-importer/quote-importer.component';
import { QuoteRecallStoredComponent } from 'app/dashboard/quote-recall-stored/quote-recall-stored.component';
import { ImportedQuoteInfoComponent } from 'app/shared/components/imported-quote-info/imported-quote-info.component';
import { NewHomeCreateFormComponent } from './home/<USER>/new-home-create-form.component';
import { AsideHomeQuoteComponent } from './tpl-aside/components/aside-home-quote/aside-home-quote.component';
import { HomeBasicsComponent } from './home/<USER>/home-basics.component';
import { HomeDwellingComponent } from './home/<USER>/home-dwelling.component';
import { AsideHomeComponent } from './tpl-aside/aside-home/aside-home.component';
import { DwellingInformationComponent } from './home/<USER>/dwelling-information/dwelling-information.component';
import { CoverageLimitsInsuredDiscountsComponent } from './home/<USER>/coverage-limits-insured-discounts/coverage-limits-insured-discounts.component';
import { AlarmsComponent } from './home/<USER>/alarms/alarms.component';
import { HomeSchedulesComponent } from './home/<USER>/home-schedules.component';
import { GeneralItemsComponent } from './home/<USER>/general-items/general-items.component';
import { AgreedValueJewelryComponent } from './home/<USER>/agreed-value-jewelry/agreed-value-jewelry.component';
import { JewelryComponent } from './home/<USER>/jewelry/jewelry.component';
import { ProtectionClassComponent } from './home/<USER>/protection-class/protection-class.component';
import { SubsystemsComponent } from './home/<USER>/subsystems/subsystems.component';
import { HomeOptionsComponent } from './home/<USER>/home-options.component';
import { HomeUnderwritingComponent } from './home/<USER>/home-underwriting.component';
import { LossHistoryComponent } from './home/<USER>/loss-history/loss-history.component';
import { HomeUmbrellaComponent } from './home/<USER>/home-umbrella.component';
import { HomePremiumsComponent } from './home/<USER>/home-premiums.component';
import { HomePremiumsTableComponent } from './home/<USER>/home-premiums-table/home-premiums-table.component';
import { QuoteLoaderComponent } from './quote-loader/quote-loader.component';
import { HomeOptionsNavComponent } from './home/<USER>/home-options-nav/home-options-nav.component';
import { HomeGeneralOptionsComponent } from './home/<USER>/home-general-options/home-general-options.component';
import { HomeCarrierOptionsComponent } from './home/<USER>/home-carrier-options/home-carrier-options.component';
import { HomeUserDefinedCoveragesComponent } from './home/<USER>/home-user-defined-coverages/home-user-defined-coverages.component';
import { HomeCarrierOptionsAutomanagerComponent } from './home/<USER>/home-carrier-options-automanager/home-carrier-options-automanager.component';
import { HomeGeneralOptionsAutomanagerComponent } from './home/<USER>/home-general-options-automanager/home-general-options-automanager.component';
import { HomeOverrideRestrictionsComponent } from './home/<USER>/home-override-restrictions/home-override-restrictions.component';

import { DwellingDwellingComponent } from './dwelling/dwelling-dwelling/dwelling-dwelling.component';
import { NewDwellingCreateFormComponent } from './dwelling/new-dwelling-create-form/new-dwelling-create-form.component';
import { DwellingTabsNavComponent } from './dwelling/tabs-nav/tabs-nav.component';
import { DwellingBasicsComponent } from './dwelling/dwelling-basics/dwelling-basics.component';
import { DwellingOptionsComponent } from './dwelling/dwelling-options/dwelling-options.component';
import { DwellingPremiumsComponent } from './dwelling/dwelling-premiums/dwelling-premiums.component';
import { AsideDwellingComponent } from './tpl-aside/aside-dwelling/aside-dwelling.component';
import { AsideDwellingQuoteComponent } from './tpl-aside/components/aside-dwelling-quote/aside-dwelling-quote.component';
import { DwellingDwellingInformationComponent } from './dwelling/dwelling-dwelling/dwelling-dwelling-information/dwelling-dwelling-information.component';
import { DwellingOptionsNavComponent } from './dwelling/dwelling-options/dwelling-options-nav/dwelling-options-nav.component';
import { DwellingCarrierOptionsComponent } from './dwelling/dwelling-options/dwelling-carrier-options/dwelling-carrier-options.component';
import { DwellingGeneralOptionsComponent } from './dwelling/dwelling-options/dwelling-general-options/dwelling-general-options.component';
import { DwellingGeneralOptionsAutomanagerComponent } from './dwelling/dwelling-options/dwelling-general-options-automanager/dwelling-general-options-automanager.component';
import { DwellingDwellingProtectionInformationComponent } from './dwelling/dwelling-dwelling/dwelling-dwelling-protection-information/dwelling-dwelling-protection-information.component';
import { DwellingCoverageLimitsInsuredDiscountsComponent } from './dwelling/dwelling-basics/dwelling-coverage-limits-insured-discounts/dwelling-coverage-limits-insured-discounts.component';
import { DwellingCarrierOptionsAutomanagerComponent } from './dwelling/dwelling-options/dwelling-carrier-options-automanager/dwelling-carrier-options-automanager.component';
import { DriversOptionsAutomanagerComponent } from './auto/drivers/drivers-options-automanager/drivers-options-automanager.component';
import { DriversOptionsComponent } from './auto/drivers/drivers-options/drivers-options.component';
import { DwellingPremiumsTableComponent } from './dwelling/dwelling-premiums/dwelling-premiums-table/dwelling-premiums-table.component';
import { DwellingAlarmsComponent } from './dwelling/dwelling-basics/dwelling-alarms/dwelling-alarms.component';
import { UmbrellaBasicComponent } from './umbrella/umbrella-basic/umbrella-basic.component';
import { UmbrellaPrimaryPolicyComponent } from './umbrella/umbrella-basic/umbrella-primary-policy/umbrella-primary-policy.component';
import { UmbrellaWatercraftComponent } from './umbrella/umbrella-basic/umbrella-watercraft/umbrella-watercraft.component';
import { UmbrellaTabsNavComponent } from './umbrella/umbrella-tabs-nav/umbrella-tabs-nav.component';
import { NewUmbrellaCreateFormComponent } from './umbrella/new-umbrella-create-form/new-umbrella-create-form.component';
import { PlanSelectorComponent } from './form/plan-selector/plan-selector.component';
import { AsideUmbrellaComponent } from './tpl-aside/aside-umbrella/aside-umbrella.component';
import { AsideUmbrellaQuoteComponent } from './tpl-aside/components/aside-umbrella-quote/aside-umbrella-quote.component';
import { WatercraftsService } from './app-services/watercrafts.service';
import { UmbrellaCarrierOptionsComponent } from './umbrella/umbrella-options/umbrella-carrier-options/umbrella-carrier-options.component';
import { UmbrellaCarrierOptionsAutomanagerComponent } from './umbrella/umbrella-options/umbrella-carrier-options-automanager/umbrella-carrier-options-automanager.component';
import { UmbrellaGeneralOptionsAutomanagerComponent } from './umbrella/umbrella-basic/umbrella-general-options-automanager/umbrella-general-options-automanager.component';
import { UmbrellaGeneralOptionsComponent } from './umbrella/umbrella-basic/umbrella-general-options/umbrella-general-options.component';
import { UmbrellaPremiumsComponent } from './umbrella/umbrella-premiums/umbrella-premiums.component'; // TODO : remove

import { UmbrellaComponent } from './umbrella/umbrella.component';
import { UmbrellaPremiumsTableComponent } from './umbrella/umbrella-premiums/umbrella-premiums-table/umbrella-premiums-table.component';
import { AutoStandardCoveragesComponent } from './auto/coverages/auto-standard-coverages/auto-standard-coverages.component';
import { AutoStandardCoveragesAutomanagerComponent } from './auto/coverages/auto-standard-coverages-automanager/auto-standard-coverages-automanager.component';
import { MaipArcComponent } from '../shared/components/maip-arc/maip-arc.component';
import { MaipArcLearnMoreComponent } from '../shared/components/maip-arc-learn-more/maip-arc-learn-more.component';
import { MaipArcBannerComponent } from '../shared/components/maip-arc-banner/maip-arc-banner.component';
import { DeleteQuoteModalComponent } from '../shared/components/delete-quote/delete-quote.component';
import { DeleteFormModalComponent } from '../shared/components/delete-form/delete-form.component';
import { AutoCoveragesCopyCoveragesComponent } from './auto/coverages/auto-coverages-copy-coverages/auto-coverages-copy-coverages.component';
import { AutoAdditionalCoveragesAutomanagerComponent } from './auto/coverages/auto-additional-coverages-automanager/auto-additional-coverages-automanager.component';
import { AutoAdditionalCoveragesComponent } from './auto/coverages/auto-additional-coverages/auto-additional-coverages.component';

// Services
import { DriverOptionsAutomanagerService } from './auto/drivers/drivers-options-automanager/driver-options-automanager.service';
import { AutoStandardCoveragesAutomanagerService } from './auto/coverages/auto-standard-coverages-automanager/auto-standard-coverages-automanager.service';
import { AutoAdditionalCoveragesAutomanagerService } from './auto/coverages/auto-additional-coverages-automanager/auto-additional-coverages-automanager.service';
import { CarrierOptionsAutomanagerService } from './auto/options/carrier-options-automanager/carrier-options-automanager.service';
import { VehiclesSymbolsAutomanagerService } from './auto/vehicles/vehicles-symbols-automanager/vehicles-symbols-automanager.service';
import { HomeCarrierOptionsAutomanagerService } from './home/<USER>/home-carrier-options-automanager/home-carrier-options-automanager.service';
import { HomeGeneralOptionsAutomanagerService } from './home/<USER>/home-general-options-automanager/home-general-options-automanager.service';
import { DwellingCarrierOptionsAutomanagerService } from './dwelling/dwelling-options/dwelling-carrier-options-automanager/dwelling-carrier-options-automanager.service';
import { DwellingGeneralOptionsAutomanagerService } from './dwelling/dwelling-options/dwelling-general-options-automanager/dwelling-general-options-automanager.service';
import { UmbrellaGeneralOptionsAutomanagerService } from './umbrella/umbrella-basic/umbrella-general-options-automanager/umbrella-general-options-automanager.service';
import { UmbrellaCarrierOptionsAutomanagerService } from './umbrella/umbrella-options/umbrella-carrier-options-automanager/umbrella-carrier-options-automanager.service';

import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { StubSpecsService } from 'testing/stubs/services/specs.service.provider';
import { NewFormCreateComponent } from './form/new-form-create/new-form-create.component';
import { NewFormCreateForQuoteComponent } from './form/new-form-create/new-form-create-for-quote/new-form-create-for-quote.component';
import { NewFormCreateNewComponent } from './form/new-form-create/new-form-create-new/new-form-create-new.component';

import { ClientHeaderComponent } from './client/client-header/client-header.component';
import { ClientInfoComponent } from './client/client-info/client-info.component';
import { ClientNavComponent } from './client/client-nav/client-nav.component';
import { ClientQuotesAndFormsComponent } from './client/client-quotes-and-forms/client-quotes-and-forms.component';
import { ClientLoaderComponent } from './client/client-loader/client-loader.component';
import { NotificationComponent } from '../shared/components/notification/notification.component';
import { RateCalculatorComponent } from './common/rate-calculator/rate-calculator.component';
import { CalculatorService } from './app-services/calculator.service';
import { VinLookupComponent } from './common/vin-lookup/vin-lookup.component';
import { NewFormCreateForQuoteWithFormSelectDropdownComponent } from './form/new-form-create/new-form-create-for-quote-with-form-select-dropdown/new-form-create-for-quote-with-form-select-dropdown.component';
import { ProtectionClassOverridesComponent } from './common/protection-class-overrides/protection-class-overrides.component';
import { NewCommercialAutoCreateFormComponent } from './commercial-auto/new-commercial-auto-create-form/new-commercial-auto-create-form.component';
import { AsideCommercialAutoQuoteComponent } from './tpl-aside/components/aside-commercial-auto-quote/aside-commercial-auto-quote/aside-commercial-auto-quote.component';
import { CommercialAutoTabsNavComponent } from './commercial-auto/tabs-nav/commercial-auto-tabs-nav.component';
import { CommercialAutoLocationsComponent } from './commercial-auto/locations/commercial-auto-locations.component';
import { AsideCommercialAutoComponent } from './tpl-aside/aside-commercial-auto/aside-commercial-auto/aside-commercial-auto.component';
import { CommercialAutoVehiclesComponent } from './commercial-auto/vehicles/commercial-auto-vehicles/commercial-auto-vehicles.component';
import { CommercialAutoDriversComponent } from './commercial-auto/drivers/commercial-auto-drivers.component';
import { CommercialAutoPolicyInfoComponent } from './commercial-auto/policy-info/commercial-auto-policy-info.component';
import { CommericalAutoSubmitComponent } from './commercial-auto/submit/commerical-auto-submit.component';
import { ComVehicleService } from './app-services/com-vehicle.service';
import { ComAutoPolicyCoverageService } from './app-services/com-auto-policy-coverage.service';
import { ComCoveredAutoSymbolsService } from './app-services/com-covered-auto-symbols.service';
import { CommercialAutoComponent } from './commercial-auto/commercial-auto.component';
import { DriveOtherCarComponent } from './commercial-auto/drive-other-car/drive-other-car.component';
import { DashboardTabsNavComponent } from './dashboard-tabs-nav/dashboard-tabs-nav.component';
import { RmvVehicleLookupComponent } from './auto/vehicles/rmv-vehicle-lookup/rmv-vehicle-lookup.component';
import { RmvsVehicleDetailsComponent } from '../rmv-services/new-rmv-prefill/rmvs-vehicle-details/rmvs-vehicle-details.component';

import { OrderListModule } from 'primeng/orderlist';
import { AccordionModule } from 'primeng/accordion';
import { TableModule } from 'primeng/table';
import { StepsModule } from 'primeng/steps';
import { CalendarModule } from 'primeng/calendar';
import { MatStepperModule } from '@angular/material/stepper';
import { MatIconModule } from '@angular/material/icon';
import { MultiSelectModule } from 'primeng/multiselect';
import { Menu } from 'primeng/menu';
import { NgxMaskDirective, NgxMaskPipe, provideEnvironmentNgxMask, provideNgxMask } from 'ngx-mask';
import { DropdownModule } from 'primeng/dropdown';
import { NgArrayPipesModule } from 'ngx-pipes';
import { SearchByNameComponent } from './search-by-name/search-by-name.component';
import { LobTabbedNavComponent } from './lob-tabbed-nav/lob-tabbed-nav.component';
import { RmvServicesComponent } from './rmv-services/rmv-services.component';
import { AsideRmvServicesComponent } from './tpl-aside/aside-rmv-services/aside-rmv-services.component';
import { RmvServicesTabsNavComponent } from './rmv-services/rmv-services-tabs-nav/rmv-services-tabs-nav.component';
import { RtaPrefillComponent } from './rmv-services/rta-prefill/rta-prefill.component';
import { InquiryComponent } from './rmv-services/inquiry/inquiry.component';
import { HistoryComponent } from './rmv-services/history/history.component';
import { TypeSelectionComponent } from './rmv-services/rta-prefill/type-selection/type-selection.component';
import { VehicleSelectionComponent } from './rmv-services/rta-prefill/vehicle-selection/vehicle-selection.component';
import { OwnersSelectionComponent } from './rmv-services/rta-prefill/owners-selection/owners-selection.component';
import { LienholderSelectionComponent } from './rmv-services/rta-prefill/lienholder-selection/lienholder-selection.component';
import { BusinessOwnerSelectionComponent } from './rmv-services/rta-prefill/business-owner-selection/business-owner-selection.component';
import { LessorSelectionComponent } from './rmv-services/rta-prefill/lessor-selection/lessor-selection.component';
import { LienholderLookupComponent } from './rmv-services/rta-prefill/lienholder-selection/lienholder-lookup/lienholder-lookup.component';
import { LessorLookupComponent } from './rmv-services/rta-prefill/lessor-selection/lessor-lookup/lessor-lookup.component';
import { RmvResponseModalComponent } from './rmv-services/rta-prefill/rmv-response-modal/rmv-response-modal.component';
import { CommercialVehicleRmvLookupComponent } from './commercial-auto/vehicles/commercial-vehicle-rmv-lookup/commercial-vehicle-rmv-lookup.component';
import { RegistrationComponent } from './rmv-services/reinstatement/registration.component';
import { EstampComponent } from './rmv-services/reinstatement/estamp/estamp.component';
import { RegistrationOwnerComponent } from './rmv-services/reinstatement/registration-owner/registration-owner.component';
import { RegistrationVehicleComponent } from './rmv-services/reinstatement/registration-vehicle/registration-vehicle.component';
import { RegistrationResponseModalComponent } from './rmv-services/reinstatement/registration-response-modal/registration-response-modal.component';
import { RmvTransactionHistoryComponent } from './rmv-services/rmv-transaction-history/rmv-transaction-history.component';
import { RmvInquiryComponent } from './rmv-services/rmv-inquiry/rmv-inquiry.component';
import { DriverInquiryComponent } from './rmv-services/rmv-inquiry/driver-inquiry/driver-inquiry.component';
import { VehicleInquiryComponent } from './rmv-services/rmv-inquiry/vehicle-inquiry/vehicle-inquiry.component';
import { InquiryTypeComponent } from './rmv-services/rmv-inquiry/inquiry-type/inquiry-type.component';
import { GetReadySuccessComponent } from './rmv-services/rta-prefill/get-ready-success/get-ready-success.component';
import { GaragingAddressComponent } from './rmv-services/rta-prefill/garaging-address/garaging-address.component';
import { PurchaseAndSalesComponent } from './rmv-services/rta-prefill/purchase-and-sales/purchase-and-sales.component';
import { EStampGetReadyComponent } from './rmv-services/rta-prefill/e-stamp-get-ready/e-stamp-get-ready.component';
import { AsideRegistrationResponseComponent } from './tpl-aside/components/aside-buttons-menu/aside-registration-response/aside-registration-response.component';
import { NewRmvPrefillComponent } from './rmv-services/new-rmv-prefill/new-rmv-prefill.component';
import { ReactiveFormsModule } from '@angular/forms';
import { PrefillDataService } from 'app/rmv-services/new-rmv-prefill/prefill.service';
import { PrefillServiceTypeComponent } from '../rmv-services/new-rmv-prefill/prefill-service-type/prefill-service-type.component';
import { PrefillVehicleComponent } from '../rmv-services/new-rmv-prefill/prefill-vehicle/prefill-vehicle.component';
import { PrefillOwnerComponent } from '../rmv-services/new-rmv-prefill/prefill-owner/prefill-owner.component';
import { RmvsOwnerInformationComponent } from '../rmv-services/new-rmv-prefill/rmvs-owner-information/rmvs-owner-information.component';
import { PrefillPurchaseComponent } from '../rmv-services/new-rmv-prefill/prefill-purchase/prefill-purchase.component';
import { PrefillInsuranceComponent } from '../rmv-services/new-rmv-prefill/prefill-insurance/prefill-insurance.component';
import { RmvServicesDashboardComponent } from './rmv-services/rmv-services-dashboard/rmv-services-dashboard.component';
import { RmvsAddressComponent } from '../rmv-services/new-rmv-prefill/rmvs-address/rmvs-address.component';
import { RmvsLessorAddressComponent } from '../rmv-services/new-rmv-prefill/rmvs-lessor-address/rmvs-lessor-address.component';
import { SavedRmvServicesTableComponent } from './rmv-services/rmv-services-dashboard/saved-rmv-services-table/saved-rmv-services-table.component';
import { EvrLiteResponseComponent } from '../rmv-services/evr-lite-response/evr-lite-response.component';
import { SavedEvrLiteTableComponent } from './rmv-services/rmv-services-dashboard/saved-evr-lite-table/saved-evr-lite-table.component';
import { DocumentDestructionTableComponent } from './rmv-services/document-destruction-table/document-destruction-table.component';
import { InventoryOrderComponent } from './rmv-services/inventory-order/inventory-order.component';
import { OrderDetailsComponent } from './rmv-services/inventory-order/order-details/order-details.component';
import { LeaveTransactionComponent } from './rmv-services/new-rmv-prefill/leave-transaction/leave-transaction.component';
import { ClipboardModule } from '@angular/cdk/clipboard';
import { DialogService, DynamicDialogModule } from 'primeng/dynamicdialog';
import { DownloadCompletedDocsComponent } from './rmv-services/rmv-services-dashboard/download-completed-docs/download-completed-docs.component';
import { AdminRedirectComponent } from './admin-redirect/admin-redirect.component';
import { providePrimeNG } from 'primeng/config';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import Aura from '@primeng/themes/aura';
import { definePreset } from '@primeng/themes';
import { DialogModule} from 'primeng/dialog';
const preset = definePreset(Aura, {
  components: {
    accordion: {
      header: {
        background: 'none',
        hover: {
          background: 'none'
        },
        active: {
          background: 'none',
          hover: {
            background: 'none'
          }
        }
      },
      content: {
        background: 'none'
      }
    }

  }
});
@NgModule({
  imports: [
    NgxMaskDirective, NgxMaskPipe,
    CommonModule,
    MatTabsModule,
    SharedModule,
    DashboardRoutingModule,
    OverlayModule,
    HintsAndWarningsModule,
    BrowserAnimationsModule,
    OrderListModule,
    AccordionModule,
    TooltipModule,
    StepsModule,
    MatStepperModule,
    MatIconModule,
    ReactiveFormsModule,
    MultiSelectModule,
    DropdownModule,
    NgArrayPipesModule,
    TableModule,
    CalendarModule,
    ClipboardModule,
    Menu,
    DynamicDialogModule,
    DialogModule
  ],
  declarations: [
    DownloadCompletedDocsComponent,
    DashboardComponent,
    HeaderComponent,
    PolicyComponent,
    AutoTabsNavComponent,
    HomeTabsNavComponent,
    CoveragesComponent,
    OptionsComponent,
    PremiumsComponent,
    PremiumsTableComponent,
    VehiclesComponent,
    VehicleDetailsComponent,
    DriversComponent,
    DriverDetailsComponent,
    SummaryComponent,
    PageDashboardMainComponent,
    QuoteInboxComponent,
    PageQuoteInboxComponent,
    SummaryDriversComponent,
    SummaryVehiclesComponent,
    SummaryRmvDetailsComponent,
    SummaryHistoryComponent,
    SummaryCarrierOptionsSelectedComponent,
    PageQuotesComponent,
    PageFormsComponent,
    PageClientsComponent,
    PageRmvResultsComponent,
    PageEnterDriversComponent,
    PageEstampRequestsComponent,
    AsideDashboardComponent,
    AsideAutoComponent,
    AsideButtonsMenuComponent,
    AsideAutoQuoteComponent,
    AsideQuickLinksInfoComponent,
    AsideSavePanelComponent,
    AsideNoSavePanelComponent,
    ContentHeadDefaultComponent,
    ContentHeadWelcomeComponent,
    ContentHeadGoToQuoteComponent,
    DriversNavComponent,
    VehiclesNavComponent,
    CoveragesNavComponent,
    NewAutoCreateFormComponent,
    VehicleOptionsComponent,
    VehicleSymbolComponent,
    RmvDriverLookupComponent,
    AsideNewRmvComponent,
    VehiclesSymbolsAutomanagerComponent,
    QuoteImporterComponent,
    QuoteRecallStoredComponent,
    ImportedQuoteInfoComponent,
    NewHomeCreateFormComponent,
    AsideHomeQuoteComponent,
    HomeBasicsComponent,
    AsideHomeComponent,
    HomeDwellingComponent,
    DwellingInformationComponent,
    CoverageLimitsInsuredDiscountsComponent,
    AlarmsComponent,
    HomeSchedulesComponent,
    GeneralItemsComponent,
    AgreedValueJewelryComponent,
    JewelryComponent,
    ProtectionClassComponent,
    SubsystemsComponent,
    HomeOptionsComponent,
    HomeUnderwritingComponent,
    LossHistoryComponent,
    HomeUmbrellaComponent,
    HomePremiumsComponent,
    QuoteLoaderComponent,
    CarrierOptionsAutomanagerComponent,
    CarrierOptionsComponent,
    HomeOptionsNavComponent,
    HomeGeneralOptionsComponent,
    HomeCarrierOptionsComponent,
    HomeUserDefinedCoveragesComponent,
    HomePremiumsTableComponent,
    HomeCarrierOptionsAutomanagerComponent,
    HomeGeneralOptionsAutomanagerComponent,
    HomeOverrideRestrictionsComponent,
    DwellingDwellingComponent,
    NewDwellingCreateFormComponent,
    DwellingTabsNavComponent,
    DwellingBasicsComponent,
    DwellingOptionsComponent,
    DwellingPremiumsComponent,
    AsideDwellingComponent,
    AsideDwellingQuoteComponent,
    DwellingDwellingInformationComponent,
    DwellingOptionsNavComponent,
    DwellingCarrierOptionsComponent,
    DwellingGeneralOptionsComponent,
    DwellingGeneralOptionsAutomanagerComponent,
    DwellingDwellingProtectionInformationComponent,
    DwellingCoverageLimitsInsuredDiscountsComponent,
    DwellingCarrierOptionsAutomanagerComponent,
    DriversOptionsAutomanagerComponent,
    DriversOptionsComponent,
    DwellingPremiumsTableComponent,
    PlanSelectorComponent,
    DwellingAlarmsComponent,
    UmbrellaBasicComponent,
    UmbrellaPrimaryPolicyComponent,
    UmbrellaWatercraftComponent,
    UmbrellaTabsNavComponent,
    NewUmbrellaCreateFormComponent,
    AsideUmbrellaComponent,
    AsideUmbrellaQuoteComponent,
    UmbrellaCarrierOptionsComponent,
    UmbrellaCarrierOptionsAutomanagerComponent,
    UmbrellaGeneralOptionsAutomanagerComponent,
    UmbrellaGeneralOptionsComponent,
    UmbrellaPremiumsComponent,
    UmbrellaComponent,
    UmbrellaPremiumsTableComponent,
    AutoStandardCoveragesComponent,
    AutoCoveragesCopyCoveragesComponent,
    AutoStandardCoveragesAutomanagerComponent,
    MaipArcComponent,
    MaipArcLearnMoreComponent,
    MaipArcBannerComponent,
    NotificationComponent,
    DeleteQuoteModalComponent,
    DeleteFormModalComponent,
    AutoAdditionalCoveragesAutomanagerComponent,
    AutoAdditionalCoveragesComponent,
    NewFormCreateComponent,
    NewFormCreateForQuoteComponent,
    NewFormCreateNewComponent,
    ClientHeaderComponent,
    ClientInfoComponent,
    ClientNavComponent,
    ClientQuotesAndFormsComponent,
    ClientLoaderComponent,
    RateCalculatorComponent,
    VinLookupComponent,
    NewFormCreateForQuoteWithFormSelectDropdownComponent,
    ProtectionClassOverridesComponent,
    NewCommercialAutoCreateFormComponent,
    AsideCommercialAutoQuoteComponent,
    CommercialAutoTabsNavComponent,
    CommercialAutoLocationsComponent,
    AsideCommercialAutoComponent,
    CommercialAutoVehiclesComponent,
    CommercialAutoDriversComponent,
    CommercialAutoPolicyInfoComponent,
    CommericalAutoSubmitComponent,
    CommercialAutoComponent,
    DriveOtherCarComponent,
    DashboardTabsNavComponent,
    RmvVehicleLookupComponent,
    RmvsVehicleDetailsComponent,
    LobTabbedNavComponent,
    RmvServicesComponent,
    AsideRmvServicesComponent,
    RmvServicesTabsNavComponent,
    RtaPrefillComponent,
    InquiryComponent,
    HistoryComponent,
    TypeSelectionComponent,
    VehicleSelectionComponent,
    OwnersSelectionComponent,
    LienholderSelectionComponent,
    BusinessOwnerSelectionComponent,
    LessorSelectionComponent,
    LienholderLookupComponent,
    LessorLookupComponent,
    RmvResponseModalComponent,
    CommercialVehicleRmvLookupComponent,
    RegistrationComponent,
    EstampComponent,
    RegistrationOwnerComponent,
    RegistrationVehicleComponent,
    RegistrationResponseModalComponent,
    RmvTransactionHistoryComponent,
    RmvInquiryComponent,
    DriverInquiryComponent,
    VehicleInquiryComponent,
    InquiryTypeComponent,
    GetReadySuccessComponent,
    GaragingAddressComponent,
    PurchaseAndSalesComponent,
    EStampGetReadyComponent,
    AsideRegistrationResponseComponent,
    NewRmvPrefillComponent,
    PrefillServiceTypeComponent,
    PrefillVehicleComponent,
    PrefillOwnerComponent,
    PrefillPurchaseComponent,
    PrefillInsuranceComponent,
    RmvsAddressComponent,
    RmvsLessorAddressComponent,
    RmvsOwnerInformationComponent,
    RmvServicesDashboardComponent,
    SavedRmvServicesTableComponent,
    EvrLiteResponseComponent,
    SavedEvrLiteTableComponent,
    DocumentDestructionTableComponent,
    InventoryOrderComponent,
    OrderDetailsComponent,
    LeaveTransactionComponent,
    AdminRedirectComponent
  ],
  exports: [],
  providers: [
    provideAnimationsAsync(),
    providePrimeNG({
      theme: {
        preset,
        options: {
          darkModeSelector: false || 'none'
        }

      }
    }),
    provideNgxMask(),
    PrefillDataService,
    FormsService,
    EstampService,
    LeadsService,
    QuotesService,
    StubQuotesService,
    SubsService,
    SpecsService,
    StubSpecsService,
    DatesService,
    DriversService,
    VehiclesService,
    LocationsService,
    IncidentsService,
    LookupsService,
    ClientsService,
    CoveragesService,
    OptionsService,
    RmvService,
    PlansService,
    SymbolsService,
    LoginService,
    DwellingService,
    WatercraftsService,
    UmbrellaService,
    DriverOptionsAutomanagerService,
    AutoStandardCoveragesAutomanagerService,
    AutoAdditionalCoveragesAutomanagerService,
    CarrierOptionsAutomanagerService,
    VehiclesSymbolsAutomanagerService,
    HomeCarrierOptionsAutomanagerService,
    HomeGeneralOptionsAutomanagerService,
    DwellingCarrierOptionsAutomanagerService,
    DwellingGeneralOptionsAutomanagerService,
    UmbrellaGeneralOptionsAutomanagerService,
    UmbrellaCarrierOptionsAutomanagerService,
    CalculatorService,
    ComVehicleService,
    ComAutoPolicyCoverageService,
    DialogService,
    ComCoveredAutoSymbolsService
  ]
})
export class DashboardModule { }
