
import {first, map} from 'rxjs/operators';
import { CommercialQuoteGuideLineOverrides } from './../../app-model/quote';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { HttpClient } from '@angular/common/http';

import {
  Quote,
  QuoteHome,
  QuoteDwelling,
  QuoteGuidelineOverrides,
  QuoteGuidelineOverridesApiResponse,
  RatedCarrier,
  RatedCarrierApiResponse,
  SelectedCarriersAPIResponse,
  Notification,
  NotificationResponse,
  IntegrationNote,
  ModalIntegrationNote
} from 'app/app-model/quote';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { ApiService } from 'app/shared/services/api.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { OverlayRouteService } from 'app/overlay/services/overlay-route.service';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { LeaveQuoteService } from 'app/shared/services/leave-quote.service';

import { addYears, format } from 'date-fns';

export const IMPORTED_QUOTE = 'importedQuote';
export const INTEGRATION_NOTES = 'integrationNotes';

interface importedQuote {
  isImported: boolean;
}

const isImportedQuote: importedQuote = {
  isImported: false
};

export const MAIPARC_QUOTE = 'maipArcQuote';

interface maipArcQuote {
  isMaipArcQuote: boolean;
}

const isMaipArcQuote: maipArcQuote = {
  isMaipArcQuote: false
};

export const MAIPARCINFO_QUOTE = 'maipArcInfo';

interface maipArcLearnMore {
  showMaipArcLearnMore: boolean;
}

const maipArcLearnMore: maipArcLearnMore = {
  showMaipArcLearnMore: false
};

export enum SessionDataSource {
  NewQuote,
  QuoteCopy,
  Database,
  Integration,
  MaipArcQuote,
  Leads
}

@Injectable()
export class QuotesService {

  constructor(
    private http: HttpClient,
    private apiService: ApiService,
    private storageService: StorageService,
    private apiCommonService: ApiCommonService,
    private overlayRouteService: OverlayRouteService,
    private overlayLoaderService: OverlayLoaderService,
    private leaveQuoteService: LeaveQuoteService
  ) {
    this.init();
  }

  public get isImportedQuote$(): Observable<any> {
    if (this._isQuoteImported && this._isQuoteImported.observers.length === 0) {
      this._isQuoteImported.next(this.isQuoteImported);
    }
    return this._isQuoteImported.asObservable();
  }

  public get isMaipArcQuote$(): Observable<any> {
    if (this._isMaipArcQuote && this._isMaipArcQuote.observers.length === 0) {
      this._isMaipArcQuote.next(this.isMaipArcQuote);
    }
    return this._isMaipArcQuote.asObservable();
  }

  public get showMaipArcLearnMore$(): Observable<any> {
    if (
      this._showMaipArcLearnMore &&
      this._showMaipArcLearnMore.observers.length === 0
    ) {
      this._showMaipArcLearnMore.next(this.maipArcLearnMore);
    }
    return this._showMaipArcLearnMore.asObservable();
  }
  public newQuote;

  public maipArcQuote = false;
  public showMaipArcLearnMore = false;

  // Moved from StorageService
  private isQuoteImported: importedQuote = isImportedQuote;
  private _isQuoteImported: BehaviorSubject<any> = new BehaviorSubject(
    this.isQuoteImported
  );

  private integrationNotes: ModalIntegrationNote[] = [];
  private _integrationNotes: BehaviorSubject<any> = new BehaviorSubject(
    this.integrationNotes
  );

  private isMaipArcQuote: maipArcQuote = isMaipArcQuote;
  private _isMaipArcQuote: BehaviorSubject<any> = new BehaviorSubject(
    this.isMaipArcQuote
  );

  private maipArcLearnMore: maipArcLearnMore = maipArcLearnMore;
  private _showMaipArcLearnMore: BehaviorSubject<any> = new BehaviorSubject(
    this.maipArcLearnMore
  );
  /************** MAIP ARC LEARN MORE INFO **************/

  public static simplifyQuoteFormType(formType: string): string[] {
    const quoteFormTypes: string[] = [];

    const mappedFormType = formType.toUpperCase().replace(/\s*/g, '');

    if (
      mappedFormType.indexOf('HO3') !== -1 &&
      mappedFormType.indexOf('HO5') !== -1
    ) {
      quoteFormTypes.push('HO5');
    } else if (mappedFormType.indexOf('HO2') !== -1) {
      quoteFormTypes.push('HO2');
    } else if (mappedFormType.indexOf('HO3') !== -1) {
      quoteFormTypes.push('HO3');
    } else if (mappedFormType.indexOf('HO4') !== -1) {
      quoteFormTypes.push('HO4');
    } else if (mappedFormType.indexOf('HO5') !== -1) {
      quoteFormTypes.push('HO5');
    } else if (mappedFormType.indexOf('HO6') !== -1) {
      quoteFormTypes.push('HO6');
    } else if (mappedFormType.indexOf('DP1') !== -1) {
      quoteFormTypes.push('DP1');
    } else if (mappedFormType.indexOf('DP2') !== -1) {
      quoteFormTypes.push('DP2');
    } else if (mappedFormType.indexOf('DP3') !== -1) {
      quoteFormTypes.push('DP3');
    } else {
      console.warn(
        'The input formType does not match any specified simplified formType ID, adding formType: ',
        formType
      );
      quoteFormTypes.push(formType);
    }

    if (quoteFormTypes && quoteFormTypes.length) {
      quoteFormTypes.sort();
    }

    return quoteFormTypes;
  }

  private init() {
    this.loadIsImportedFromStorage();

    this.loadIsMaipArcFromStorage();
    this.loadIsMaipArcInfoFromStorage();
    const subscription = this.storageService
      .getStorageData('selectedQuote')
      .subscribe(quote => {
        if (quote) {
          this.newQuote = quote;
        }
      });
  }

  public getQuotes(
    offset: string = '0',
    limit: string = '10',
    name: string = '',
    agentId: string = '',
    lob: string = '',
    locationId: string = '',
    dateType: string = '',
    startDate: string = '',
    endDate: string = '',
    clientIdentifier: string = '',
    nameType: string = '',
    mostRecent = false
  ): Observable<any> {
    const params =
      'offset=' +
      offset +
      '&limit=' +
      limit +
      '&name=' +
      name +
      '&agentId=' +
      agentId +
      '&lob=' +
      lob +
      '&locationId=' +
      locationId +
      '&dateType=' +
      dateType +
      '&startDate=' +
      startDate +
      '&endDate=' +
      endDate +
      '&clientIdentifier=' +
      clientIdentifier +
      '&nameType=' +
      nameType;

    if (mostRecent)
      return this.apiCommonService.getByUri('/quotes/recent');
    else
      return this.apiCommonService.getByUri('/quotes?' + params);
  }

  public createNewQuote(data): Observable<any> {
    if (
      !data.expirationDate &&
      !data.effectiveDate &&
      data.sessionDataSource !==
        SessionDataSource[SessionDataSource.MaipArcQuote]
    ) {
   data.effectiveDate = format(new Date(), 'yyyy-MM-dd');
data.expirationDate = format(addYears(new Date(data.effectiveDate), 1), 'yyyy-MM-dd');
    }

    let srcQuoteSessionId = null;
    this.storageService
      .getStorageData('srcQuoteSessionId')
      .subscribe(sessionId => {
        if (sessionId) {
          srcQuoteSessionId = sessionId;
        }
      });
    if (srcQuoteSessionId && srcQuoteSessionId != null) {
      data.srcQuoteSessionId = srcQuoteSessionId;
    }

    return this.http
      .post(this.apiService.url('/quotes'), data).pipe(
      map((res: any) => {
        this.newQuote = res;
        return res;
      }));
  }

  public updateQuoteInfo(quoteId: string, data): Observable<any> {
    return this.apiCommonService.putByUri('/quotes/' + quoteId + '/info', data);
  }

  public updateRatedCarrierInfoByCarrierId(quoteId: string, carrierId: string): void {
     this.apiCommonService.putByUri('/quotes/' + quoteId + '/updateRatedCarrier?carrierid=' + carrierId, null);
  }
  public updateRatedCarrierInfoByCarrierName(quoteId: string, carrierName: string): void {
    try {
      this.apiCommonService.putByUri('/quotes/' + quoteId + '/updateRatedCarrier?carriername=' + carrierName, null);
    } catch ( e) {

    }
  }
  public updateRatedCarrierInfoAll(quoteId: string):  void {
    try {
       this.apiCommonService.putByUri('/quotes/' + quoteId + '/updateRatedCarrier?carriername=all', null);
    }    catch (e) {

    }
  }

  public copyQuoteInfo(quoteSessionId: string, data): Observable<any> {
    return this.apiCommonService.putByUri(
      '/quotes/' + quoteSessionId + '/copyfrom',
      data
    );
  }

  public updateQuoteByUrl(
    uri: string,
    data,
    firstTime = true
  ): Observable<any> {
    if (firstTime) {
      return this.apiCommonService.postByUri(uri, data);
    } else {
      return this.apiCommonService.putByUri(uri, data);
    }
  }

  public deleteQuotes(quoteListToDelete: string[]): Observable<any> {
    const data = {
      QuoteListToDelete: quoteListToDelete
    };
    return this.apiCommonService.putByUri('/quotes', data);
  }

  public saveQuote(uri: string, quote): Observable<any> {
    return this.apiCommonService.putByUri(uri, quote);
  }

  public saveAsQuote(uri: string, quote): Observable<any> {
    quote.quoteIdentifier = '';
    return this.apiCommonService.putByUri(uri + '/saveas', quote);
  }


  public checkIfQuoteClientNameSet(quote: Quote): boolean {
    return quote &&
    quote.client &&
    !!(quote.client.firstName && quote.client.lastName);
  }

  public saveQuoteCompleteProcess(quote: Quote): Promise<boolean> {
    return new Promise((resolve, reject) => {
      if (
        quote &&
        quote.client &&
        ((!quote.client.businessName || !quote.client.legalEntity) && quote.lob === 'AUTOB')
      ) {
        // data:'error-quote-save' required to show error on overlay client info screen
        this.overlayRouteService.go('info', 'client', null, 'error-quote-save');
        resolve(false);
        return;
      }

      if (
        quote &&
        quote.client &&
        ((!quote.client.firstName || !quote.client.lastName) && quote.lob !== 'AUTOB')) {
        // data:'error-quote-save' required to show error on overlay client info screen
        this.overlayRouteService.go('info', 'client', null, 'error-quote-save');
        resolve(false);
        return;
      }

      this.overlayLoaderService.showLoader('Saving...');

      this.saveQuote(quote.meta.href, quote).pipe(
        first())
        .subscribe(
          res => {
            this.storageService.setStorageData('selectedQuote', res);
            this.leaveQuoteService.saveStorageQuoteDataSnapshot();
            resolve(true);
          },
          err => {
            this.overlayLoaderService.hideLoader();
            resolve(false);
          },
          () => this.overlayLoaderService.hideLoader()
        );
    });
  }

  public createNewQuoteSession(quote): Observable<any> {
    const data: any = {};
    data.QuoteSessionId = quote.quoteSessionId;
    return this.apiCommonService.postByUri('/quotes', data);
  }

  public getDataByUrl(uri: string): Observable<any> {
    return this.apiCommonService.getByUri(uri);
  }

  public getRatedCarriers(uri: string): Observable<RatedCarrierApiResponse> {
    return this.apiCommonService.getByUri(uri);
  }

  public getNotifications(plans): Observable<NotificationResponse> {
    let quotePlansIds: string[];
    let quotePlansIdsString: string;

    if (plans && plans.length) {
      quotePlansIds = plans.map(plan => plan.ratingPlanId);
      quotePlansIdsString = quotePlansIds.join(',');
      if (quotePlansIdsString && quotePlansIdsString !== '') {
        quotePlansIdsString = '?PlanIds=' + quotePlansIdsString;
      }
    }
    return this.apiCommonService.getByUri(
      '/notifications' + quotePlansIdsString
    );
  }

  public savePrintCarriers(
    quoteSessionId: string,
    printCarriers: RatedCarrier[]
  ): Observable<any> {
    const data = { selectedCarriers: printCarriers };

    // return Observable.of(SelectedCarriersAPIResponse);
    return this.apiCommonService
      .postByUri('/quotes/' + quoteSessionId + '/printCarriers', data).pipe(
      map(res => {
        return res;
      }));
  }

  public getPrintCarriers(quoteSessionId: string): Observable<any> {
    // return Observable.of(SelectedCarriersAPIResponse);
    return this.apiCommonService
      .getByUri('/quotes/' + quoteSessionId + '/printCarriers').pipe(
      map(res => {
        return res;
      }));
  }

  public updatePrintCarriers(
    quoteSessionId: string,
    resourceId: string,
    printCarriers: RatedCarrier[]
  ): Observable<any> {
    const data = { selectedCarriers: printCarriers };

    // return Observable.of(SelectedCarriersAPIResponse);
    return this.apiCommonService
      .putByUri(
        '/quotes/' + quoteSessionId + '/printCarriers/' + resourceId,
        data
      ).pipe(
      map(res => {
        return res;
      }));
  }

  // Policy History
  // -------------------------------------------------------------------------------

  public policyHistoryCreateByUri(
    uri: string,
    policyHistoryData: any = {}
  ): Observable<any> {
    return this.apiCommonService.postByUri(uri, policyHistoryData);
  }

  public policyHistoryGetListByUri(uri: string): Observable<any> {
    return this.apiCommonService.getByUri(uri);
  }

  // Update policy history
  public policyHistoryUpdateByUri(
    uri: string,
    policyHistoryData
  ): Observable<any> {
    return this.apiCommonService.putByUri(uri, policyHistoryData);
  }

  // Import quotes
  private inboundUrl(id: string): string {
    return '/quotes/' + id + '/inbound';
  }

  public getImportQuoteUrl(id: string): Observable<any> {
    return this.apiCommonService.getByUri(this.inboundUrl(id));
  }
  public getIntegrationNotes(quoteIdentifier: string): Observable<any> {
    return this.apiCommonService.getByUri('/quotes/' + quoteIdentifier + '/integrationNotes');
  }

  public getRecallQuoteInboundUrl(id: string): Observable<any> {
    return this.apiCommonService.getByUri(
      '/quotes/' + id + '/inboundRecallQuote'
    );
  }

  // Export quote
  private outboundUrl(
    quoteId: string,
    vendorId: string,
    rateResponseId: string,
    responseIndex: string
  ) {
    return (
      // '/quotes/' + quoteId + '/outbound?vendorId=' + vendorId + '&requestId='
      '/quotes/' +
      quoteId +
      '/outbound' +
      '?vendorId=' +
      vendorId +
      '&rateresponseId=' +
      rateResponseId +
      '&responseIndex=' +
      responseIndex +
      '&requestId='
    );
  }

  public exportQuote(
    quoteId: string,
    vendorId: string,
    rateResponseId: string,
    responseIndex: string
  ): Observable<any> {
    return this.apiCommonService.getByUri(
      this.outboundUrl(quoteId, vendorId, rateResponseId, responseIndex)
    );
  }

  /************** IMPORTED QUOTE **************/
  // Storage handling
  // Moved from StorageService;
  // 1) it doesn't make any sense there - this data is stored in a separate object anyway
  // 2) making service responsible for its own stored data makes it easier for everyone I believe
  public setIsImportedQuote(is: boolean = true): void {
    this.isQuoteImported = { isImported: is };
    this._isQuoteImported.next(this.isQuoteImported);


    sessionStorage.setItem(
      IMPORTED_QUOTE,
      JSON.stringify(this.isQuoteImported)
    );
  }

  private loadIsImportedFromStorage() {
    const tmpIsImported = sessionStorage.getItem(IMPORTED_QUOTE);
    if (tmpIsImported) {
      this.isQuoteImported = JSON.parse(tmpIsImported);
    }
    return this.isQuoteImported;
  }
  public setIntegrationNotesStorage(Notes: ModalIntegrationNote[]): void {
    this.integrationNotes = Notes;
    this._integrationNotes.next(this.integrationNotes);


    sessionStorage.setItem(
      INTEGRATION_NOTES,
      JSON.stringify(this.integrationNotes)
    );
  }

  public getIntegrationNotesStorage(): Observable<ModalIntegrationNote[]> {
    if (this._integrationNotes && this._integrationNotes.observers.length === 0) {
      this._integrationNotes.next(this.integrationNotes);
    }
    return this._integrationNotes.asObservable();
  }

  /************** IMPORTED QUOTE **************/

  /************** MAIP ARC QUOTE **************/
  public setIsMaipArcQuote(is: boolean = true): void {
    this.isMaipArcQuote = { isMaipArcQuote: is };

    this._isMaipArcQuote.next(this.isMaipArcQuote);

    sessionStorage.setItem(MAIPARC_QUOTE, JSON.stringify(this.isMaipArcQuote));
  }

  private loadIsMaipArcFromStorage() {
    const tmpIsMaipArc = sessionStorage.getItem(MAIPARC_QUOTE);
    if (tmpIsMaipArc) {
      this.isMaipArcQuote = JSON.parse(tmpIsMaipArc);
    }
    return this.isMaipArcQuote;
  }
  /************** MAIP ARC QUOTE **************/

  /************** MAIP ARC LEARN MORE INFO **************/
  public setShowMaipLearnMore(is: boolean = true): void {
    this.maipArcLearnMore = { showMaipArcLearnMore: is };

    this._showMaipArcLearnMore.next(this.maipArcLearnMore);

    sessionStorage.setItem(
      MAIPARCINFO_QUOTE,
      JSON.stringify(this.maipArcLearnMore)
    );
  }

  private loadIsMaipArcInfoFromStorage() {
    const tmpIsMaipArcInfo = sessionStorage.getItem(MAIPARCINFO_QUOTE);
    if (tmpIsMaipArcInfo) {
      this.maipArcLearnMore = JSON.parse(tmpIsMaipArcInfo);
    }
    return this.maipArcLearnMore;
  }

  public simplifyFormType(formType: string): string[] {
    return QuotesService.simplifyQuoteFormType(formType);
  }

  public getQuoteSelectedFormTypes(quote: QuoteHome | QuoteDwelling): string[] {
    let quoteFormTypes: string[] = [];
    if (!quote.formType) {
      return quoteFormTypes;
    }

    quoteFormTypes = this.simplifyFormType(quote.formType);
    return quoteFormTypes;
  }

  public createGuidelineOverrides(
    quoteId: string,
    data: QuoteGuidelineOverrides[]
  ): Observable<QuoteGuidelineOverrides> {
    const uri = 'quotes/' + quoteId + '/overrides';
    const dataToSend = { items: data };

    return this.apiCommonService.postByUri(uri, dataToSend);
  }

  public getGuidelineOverrides(
    quoteId: string
  ): Observable<QuoteGuidelineOverridesApiResponse> {
    const uri = 'quotes/' + quoteId + '/overrides';

    return this.apiCommonService.getByUri(uri);
  }

  public updateGuidelineOverrides(
    quoteId: string,
    data: QuoteGuidelineOverrides
  ): Observable<QuoteGuidelineOverrides> {
    const uri = 'quotes/' + quoteId + '/overrides/' + data.resourceId;

    return this.apiCommonService.putByUri(uri, data);
  }

  public quoteSubmit(uri: string): Observable<QuoteGuidelineOverrides> {
    return this.apiCommonService.getByUri(uri);
  }

  public commQuoteSubmit(uri: string): Observable<CommercialQuoteGuideLineOverrides> {
    return this.apiCommonService.getByUri(uri);
  }

  getQuoteAndClientAddresses(quoteId) {
     const uri = `quotes/${quoteId}/quoteAndClientAddresses`;
     return this.apiCommonService.getByUri(uri)
    .pipe(
      map((res) => {
        const list = [];
        res.items.forEach(item => {
          const i = {
            label: item.address1,
            state: item,
            command: (event) => 'getMenuList(event)'
          };
          list.push(i);
        });
        return list;
      })
    );

  }

  logSummary(data) {
    const uri = "/summary/log"
    return this.apiCommonService.postByUri(uri, data)
  }

}
