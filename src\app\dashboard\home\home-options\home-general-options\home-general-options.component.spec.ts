import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { StubOptionsViewComponent } from 'testing/stubs/components/options-view.component';

import { StorageService } from 'app/shared/services/storage-new.service';

import { HomeGeneralOptionsComponent } from './home-general-options.component';

describe('Component: HomeGeneralOptions', () => {
  let component: HomeGeneralOptionsComponent;
  let fixture: ComponentFixture<HomeGeneralOptionsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [
        HomeGeneralOptionsComponent,
        StubOptionsViewComponent
      ],
      providers: [
        StorageService
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(HomeGeneralOptionsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });
});
