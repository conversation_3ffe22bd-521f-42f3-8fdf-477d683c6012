
<table  class="form-table form-table--fixed u-spacing--2">
  <tr class="form-table__row" *ngFor="let veh of vehicleList; let i = index">

    <td class="form-table__cell u-width-20px">
      <button class="o-btn o-btn--checkbox u-spacing--right-0" [class.is-active]="veh.checked" (click)="toggleCheckStatus($event, veh)"></button>
    </td>
    <td class="form-table__cell u-width-120px">
      Vehicle {{i +1}}
    </td>
    <td class="form-table__cell u-width-80px">
      <sm-autocomplete
      [(ngModel)]="veh.lookupType"
      [options]="lookupOptions"
      [activeOption]="'Plate'"
      >
      </sm-autocomplete>
    </td>
<div *ngIf="veh.lookupType === 'plate'; then plateTemplate else vinTemplate"></div>
    <ng-template #plateTemplate>
      <td class="form-table__cell u-width-120px">

        <input type="text" id="plateNum_{{i}}" name="rmvLookupPlate"
         [(ngModel)]="veh.plateNumber"
         (focus)="rmvResetPlateValidationStatus()">
         </td>
         <td class="form-table__cell u-width-120px">
<sm-autocomplete
      [options]="plateTypeOptions"
      [activeOption]="'PAN'"
      [searchFromBegining]="true"
      (onSelect)="selectRMVlookupPlateType($event)"
      [(ngModel)]="veh.plateType"
      >
    </sm-autocomplete>
</td>
    </ng-template>

    <ng-template #vinTemplate>

      <td class="form-table__cell u-width-240px" colspan="2">
      <input type="text" id="vin_{{i}}" name="rmvLookUpVin"
        #refInputRmvVin
        [(ngModel)]="veh.vin"
        (ngModelChange)="rmvLookupVinNumberOnChange($event, refInputRmvVin)"
        (focus)="rmvResetVinValidationStatus()">
    </td>
    </ng-template>

    <td class="form-table__cell">
      <!-- span class="u-color-sunset" *ngIf="rmvLookupPlateNumber && rmvLookupPlateNumber.length && !plateValidate(rmvLookupPlateNumber)">Invalid plate number</span -->
    </td>
  </tr>


