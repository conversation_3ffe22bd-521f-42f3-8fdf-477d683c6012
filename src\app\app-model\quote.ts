import { Collection, Resource, MetaData } from 'app/app-model/_common';
import { ApiResponse } from './_common';
import { Client } from 'app/app-model/client';
import { RmvDriverResult } from 'app/app-model/driver';
import { VehicleRMVLookupData } from 'app/app-model/vehicle';

export type QuoteLob = 'AUTOP' | 'HOME' | 'DFIRE' | 'PUMBR';
// export type TQuotePlanRatingType = 'None' | 'Mono' | 'All';

export enum EQuotePlanRatingType {
  None = 'None',
  Mono = 'Mono',
  All = 'All'
}
export enum RatingFlowType {
  None = 'None',
  RTR = 'RTR',
  MFD = 'MFD'
}

export class QuoteMetaData {
  constructor(public href: string = '') {}
}

export class QuoteCore {
  constructor(
    public meta = { href: <string>'' },
    public quoteIdentifier: string = null,
    public state: string = null,
    public lob: string = null,
    public policyType: string = null,
    public formType: string = null,
    public creationDate: string = null,
    public lastModifiedDate: string = null,
    public effectiveDate: string = null,
    public expirationDate: string = null,
    public policyPeriod: number | string = 12,
    public policyNumber: string = null,
    public description: string = null,
    public agent: string = null,
    public agency: string = null,
    public writtenStatus: string = null,
    public writtenCarrier: string = null,
    public sourceBusiness: string = null,
    public declineReason: string = null,
    public client: Client = new Client(),
    public clients: QuoteMetaData = new QuoteMetaData(),
    public locations: QuoteMetaData = new QuoteMetaData(),
    public quotePlanList: QuoteMetaData = new QuoteMetaData(),
    public quoteSessionId: string = null,
    public resourceId: string = null,
    public resourceName: string = null,
    public quoteUUID: string = null,
    public maipCertificateNumber: string = null,
    public sessionDataSource: string = null
  ) {}
}

export class QuoteAuto extends QuoteCore {
  constructor(
    public drivers: QuoteMetaData = new QuoteMetaData(),
    public vehicles: QuoteMetaData = new QuoteMetaData(),
    public policyHistory: QuoteMetaData = new QuoteMetaData(),
    public coverages: QuoteMetaData = new QuoteMetaData()
  ) {
    super();
    this.lob = 'AUTOP';
  }
}

export class QuoteHome extends QuoteCore {
  constructor(
    public dwellings: QuoteMetaData = new QuoteMetaData(),
    public lossHistory: QuoteMetaData = new QuoteMetaData(),
    public scheduledProperty: QuoteMetaData = new QuoteMetaData(),
    public coveragesStandard: QuoteMetaData = new QuoteMetaData(),
    public options: QuoteMetaData = new QuoteMetaData()
  ) {
    super();
    this.lob = 'HOME';
  }
}

export class QuoteDwelling extends QuoteCore {
  constructor(
    public coveragesStandard: QuoteMetaData = new QuoteMetaData(),
    public options: QuoteMetaData = new QuoteMetaData(),
    public dwellings: QuoteMetaData = new QuoteMetaData()
  ) {
    super();
    this.lob = 'DFIRE';
  }
}

export class QuoteUmbrella extends QuoteCore {
  constructor(
    public basicPolicyInfo: QuoteMetaData = new QuoteMetaData(),
    public quoteCoverages: QuoteMetaData = new QuoteMetaData(),
    public watercrafts: QuoteMetaData = new QuoteMetaData()
  ) {
    super();
    this.lob = 'PUMBR';
  }
}

export class QuoteCommAuto extends QuoteCore {
  constructor(
    public comDrivers: QuoteMetaData = new QuoteMetaData(),
    public comVehicles: QuoteMetaData = new QuoteMetaData(),
    public coverages: QuoteMetaData = new QuoteMetaData()
  ) {
    super();
    this.lob = 'AUTOB';
  }
}

export class Quote extends QuoteCore {
  constructor(
    public drivers?: QuoteMetaData,
    public vehicles?: QuoteMetaData,
    public policyHistory?: QuoteMetaData,
    public dwellings?: QuoteMetaData,
    public lossHistory?: QuoteMetaData,
    public scheduledProperty?: QuoteMetaData,
    public coveragesStandard?: QuoteMetaData,
    public options?: QuoteMetaData,
    public basicPolicyInfo?: QuoteMetaData,
    public quoteCoverages?: QuoteMetaData,
    public watercrafts?: QuoteMetaData,
    public comDrivers?: QuoteMetaData,
    public comVehicles?: QuoteMetaData
  ) {
    super();
  }
}

export class QuotePolicyHistory {
  constructor(
    public meta = {
      href: <string>'',
      rel: <any[]>['PolicyHistory']
    },
    public priorPolicyPolicyNumber: string = '',
    public priorOrRenewingCarrier: string = '',
    public priorPolicyExpirationDate: string = '',
    public yearsWithThisCarrier: string = '',
    public quoteThisCarrierAs: string = '',
    public yearsWithCurrentAgency: string = '',
    public lapsedDaysLast12Months: string = '',
    public priorBodilyInjurylimits: string = '',
    public quoteSessionId: string = '',
    public resourceId: string = '',
    public parentId: string = '',
    public resourceName: string = ''
  ) {}
}

export class QuotePlan {
  public lob: string = null;
  public meta: MetaData = {
    href: <string>'',
    rel: <Array<any>>[]
  };
  public naic: string = null;
  public name: string = null;
  public productCode: string = null;
  public ratingPlanId: string = null;
  public resourceName: string = null;
  public state: string = null;
  public bscCompanyPolicyID: string = null;
  public bscCompanyQuoteNumber: string = null;
  public allowedFormTypesCode: string = null;
  public sharedRatingPlanId: number = null;
  public sppAgreedValueInd: boolean = null;
  public submissionAvailableInd?: boolean; // Dwelling quote plans do not have this
  public carrier?: any;
  public ratingFlow?: RatingFlowType; // ['None' | 'RTR' | 'MFD'] - new field
  public trailerRatingType?: EQuotePlanRatingType; // ['None' | 'Mono' | 'All'] - new field
  public motorcycleRatingType?: EQuotePlanRatingType; // ['None' | 'Mono' | 'All'] - new field
}

export class QuoteTemporaryClientData {
  public firstName = '';
  public lastName = '';
  public dob = '';
}

// 'submissionAvailableInd': null, 'sharedRatingPlanId': null

export class QuotePlanRequired {
  constructor(public ratingPlanId: string = null, public name: string = null) {}
}

export class QuotePlanListItem extends Collection<QuotePlan> {
  public meta = {
    href: '',
    rel: <string[]>['quotePlanList']
  };
  public resourceName = 'RatingPlan';
}

export class QuotePlanListAPIResponse extends ApiResponse<QuotePlanListItem> {}

export class QuoteGuidelineOverrides {
  constructor(
    public overrideMinimumsAndMaximums: boolean = false,
    public overridePlanYearBuilt: boolean = false,
    public overrideEndorsementYearBuilt: boolean = false,
    public overrideNumberOfFamiliesOrUnits: boolean = false,
    public overrideProtectionClass: boolean = false,
    public overrideOccupancy: boolean = false,
    public overrideCoastalDeductible: boolean = false,
    public overrideCoastalDistanceExclusions: boolean = false,
    public applyNewHomeCredit: boolean = true,
    public applyPreferredOrSuperiorHomeCredit: boolean = true,
    public meta: { href: string; rel: string[] } = { href: '', rel: [] },
    public quoteSessionId: string = '',
    public resourceId: string = '',
    public parentId: string = '',
    public resourceName: string = 'GuidelineOverrides'
  ) {}
}

export class CommercialQuoteGuideLineOverrides extends QuoteGuidelineOverrides {
  submissionResponse: any[];
}

export class QuoteGuidelineOverridesApiResponse extends ApiResponse<
  QuoteGuidelineOverrides
> {}

// Rate Response
// ------------------------------------------------------------------------------

export class RateResponseMessage {
  message: string = null;
}

export class RateResponse extends Resource {
  ratingPlanId: number = null;
  planName = '';
  state = '';
  lob: string = null;
  carrier = '';
  naic = '';
  rateRequestId = '';
  carrierQuoteId = '';
  transactionResponseDt = '';
  premium: number = null;
  msgStatusCd: string = null;
  msgStatusDesc: string = null;
  carrierGeneralMessages: Array<RateResponseMessage> = [];
  carrierQuoteMessages: Array<RateResponseMessage> = [];
  notificationMessages: Array<RateResponseMessage> = [];
  systemMessages: Array<RateResponseMessage> = [];
}

// Plan Summary
// ------------------------------------------------------------------------------

export class QuotePlanSummary {
  constructor(
    public meta: any = null,
    public rateRequestId: string = '',
    public carrierName: string = '',
    public quoteIndexMax: string = '',
    public quoteResponses: QuotePlanSummaryResponse[] = [],
    public quoteSessionId: string = '',
    public resourceId: string = '',
    public resourceName: string = null,
    public rate?: any, // The field is optionaly added on UI site (it's not returned by API),
    public error?: any, // The field is optionaly added on UI site (it's not returned by API),
    public ratingPlanId?: string, // The field is optionaly added on UI site (it's not returned by API),
    public name?: string // The field is optionaly added on UI site (it's not returned by API),
  ) {}
}

export class QuotePlanSummaryResponse {
  constructor(
    public quoteSessionId: string = '',
    public resourceId: string = '',
    public resourceName: string = null,
    public quoteIndex: number = null,
    public ratingPlanId: string = '',
    public ratingPlanName: string = '',
    public naic: string = '',
    public productCode: any = null,
    public quoteInfo: QuotePlanSummaryResponseQuoteInfo = new QuotePlanSummaryResponseQuoteInfo(),
    public carrierGeneralMessages: { message: string }[] = [],
    public carrierQuoteMessages: { message: string }[] = [],
    public notificationMessages: { message: string }[] = [],
    public systemMessages: { message: string }[] = [],
    public planCoverages: QuotePlanSummaryPlanCoverage[] = [],
    public vehicles: QuotePlanSummaryResponseVehicle[] = [],
    public drivers: QuotePlanSummaryResponseDriver[] = [],
    public dwellings?: QuotePlanSummaryResponseDwellings,
    public dwellingStandardCoverages?: QuotePlanSummaryPlanCoverage[],
    public msgStatusCd?: string,
    public planName?: string
  ) {}
}

class QuotePlanSummaryResponseQuoteInfo {
  constructor(
    public effectiveDt: string = '',
    public expirationDt: string = '',
    public premium: string = '',
    public rateEffectiveDate: string = null,
    public companysQuoteNumber: string = '',
    public quoteDeclinedInd: any = null,
    public quoteDeclinationReason: any = null,
    public initialQuoteRequestDt: any = null,
    public quotePreparedDt: string = '',
    public lastSavedDt: string = '',
    public policyStatusCd: string = '',
    public quoteTitle: string = null
  ) {}
}

export class QuotePlanSummaryPlanCoverage {
  constructor(
    public coverageCode: string = '',
    public description: string = '',
    public value: string = '',
    public value2: string = null,
    public premium?: string,
    public requestValue?: string,
    public options?: Array<any>
  ) {}
}

class QuotePlanSummaryResponseVehicle {
  constructor(
    public vehicleIndex: number = null,
    public year: string = '',
    public make: string = '',
    public model: string = '',
    public trimLevel: string = '',
    public vin: string = '',
    public symbol: string = '',
    public ratingTerritory: any = null,
    public classificationCode: any = '',
    public assignedDriver: string = '',
    public bodyStyle: string = '',
    public cost: any = null,
    public usage: string = '',
    public operatorsDescription: string = '',
    public odometer: string = '',
    public premium: string = '',
    public standardCoverages: QuotePlanSummaryPlanCoverage[] = [],
    public additionalCoverages: QuotePlanSummaryPlanCoverage[] = []
  ) {}
}

class QuotePlanSummaryResponseDriver {
  constructor(
    public firstName: string = '',
    public lastName: string = '',
    public age: string = '',
    public dateOfBirth: string = '',
    public licenseDate: string = '',
    public yearsDriving: string = '',
    public deferred: string = '',
    public goodStudent: string = '',
    public driverTraining: string = '',
    public awayAtSchool: string = '',
    public advancedDriverTraining: string = '',
    public sdip: string = '',
    public accidentVoilations: any[] = []
  ) {}
}

class QuotePlanSummaryResponseDwellings {
  constructor(
    public basePremium: string = '',
    public liabilityPremium: string = '',
    public medPayPremium: string = null,
    public basicPolicyPremium: string = '',
    public adjustmentsToPremium: any = null,
    public territory: string = '',
    public ratedProtectionClass: string = '',
    public premiumGroup: string = ''
  ) {}
}

export class RatedCarrierApiResponse extends ApiResponse<RatedCarrier> {}

export class RatedCarrier {
  constructor(
    public specificationId: string = null,
    public carrierName: string = null,
    public status: string = null,
    public premium: string = null,
    public planName: string = null,
    public naicCode: string = null,
    public lob: string = null,
    public writingCompany: string = null,
    public multipleResponse: boolean = false,
    public resourceName: string = null,
    public meta = { href: <string>'' }
  ) {}
}
export class NotificationResponse extends ApiResponse<Notification> {}

export class Notification {
  constructor(
    public resourceName: string = null,
    public Id: string = null,
    public specificationId: string = null,
    public type: string = null,
    public value: string = null,
    public link: string = null,
    public notificationToDisplay: string = null,
    public startDate: string = null,
    public expirationDate: string = null,
    public show: string = null,
    public meta = { href: <string>'' },
    public status: string,
    public linkText: string,
    public priority: number
  ) {}
}

export class SelectedCarriersData extends Resource {
  public selectedCarriers: RatedCarrier[] = [];
  public meta: MetaData = { href: '', rel: [] };
}

export class SelectedCarriersAPIResponse extends ApiResponse<
  SelectedCarriersData
> {}

// RMV
// ------------------------------------------------------------------------------

export class QuoteNewRMVData {
  constructor(
    public effectiveDate: string,
    public plans: { text: string; id: string; data?: QuotePlan }[] = []
  ) {}
}

export class QuoteNewRMVResponseData {
  constructor(
    public drivers: RmvDriverResult[] = [],
    public messages: { message: string }[] = [],
    public meta: { href: string } = null,
    public quoteId: string = '',
    public vehicles: VehicleRMVLookupData[] = []
  ) {}
}

export class DownPaymentData {
  constructor(
    public maipPremium: number,
    public arcPremium: number,
    public maipArcId: string
  ) {}
}

export class DownPaymentResponseData {
  constructor(public amount: number = 0, public percentage: string = null) {}
}

export class IntegrationNotes {
  constructor(public notes: IntegrationNote[]) {}
}

export class IntegrationNote {
  constructor(public displayLabel: string, public message: string) {}
}

export class ModalIntegrationNote {
  constructor(public label: string, public messages: string[] = []) {}
}
