import { first, take } from 'rxjs/operators';
import { WatercraftsResponse } from 'app/app-model/watercraft';
import {
  QuoteUmbrella,
  QuotePlan,
  EQuotePlanRatingType,
} from 'app/app-model/quote';
import {
  CoverageApiResponseData,
  CoveragesData,
  CoverageItem,
} from 'app/app-model/coverage';
import { ActivatedRoute, Router } from '@angular/router';
import {
  AgencyUserService,
  UserData,
} from 'app/shared/services/agency-user.service';
import { Component, OnDestroy, OnInit } from '@angular/core';
import {
  Dwelling,
  DwellingsListAPIResponse,
  DwellingProtectionDevices,
  DwellingProtectionClass,
  DwellingProtectionClassApiResponse,
  DwellingProtectionClassOverridesApiResponse,
  DwellingProtectionClassOverrides,
  DwellingsSubsystemsAPIResponse,
} from 'app/app-model/dwelling';
import {
  Quote,
  QuoteAuto,
  QuoteHome,
  QuoteCommAuto,
  QuoteDwelling,
  QuotePlanListAPIResponse,
  QuotePlanListItem,
  QuoteGuidelineOverridesApiResponse,
  QuoteGuidelineOverrides,
  QuotePolicyHistory,
} from 'app/app-model/quote';
import {
  StorageService,
  appStorageInterface,
} from 'app/shared/services/storage-new.service';

import {
  ClientsService,
  CLIENT_TYPES,
} from 'app/dashboard/app-services/clients.service';
import {
  Driver,
  ComDriver,
  ComDriverApiResponseData,
} from 'app/app-model/driver';
import {
  Vehicle,
  VehicleOptionsForVehicle,
  VehicleLocationDataForVehicle,
  VehicleGeneralDetails,
  VehicleGeneralDetailsHelper,
} from 'app/app-model/vehicle';
import { DwellingService } from 'app/dashboard/app-services/dwelling.service';
import { SubscriptionLike as ISubscription } from 'rxjs';
import { OverlayClientQuoteInfoService } from 'app/overlay/services/overlay-client-quote-info.service';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import {
  QuotesService,
  SessionDataSource,
} from 'app/dashboard/app-services/quotes.service';
import { PremiumsService } from 'app/dashboard/auto/premiums/premiums.service';
import {
  VehiclesService,
  VEHICLE_TYPES,
  VEHICLE_OPTIONS_MAKE_TRAILER,
  VEHICLE_OPTIONS_MAKE_MOTORCYCLE,
} from 'app/dashboard/app-services/vehicles.service';
import {
  ClientDetails,
  ClientAddress,
  ClientContactMethod,
} from 'app/app-model/client';
import {
  LocationData,
  LocationDataCollectionApiResponseData,
} from 'app/app-model/location';
import { LocationsService } from 'app/dashboard/app-services/locations.service';
import { CoveragesService } from 'app/dashboard/app-services/coverages.service';
import { LookupsService } from 'app/dashboard/app-services/lookups.service';
import { FilterOption } from 'app/app-model/filter-option';
import { SubsService } from 'app/dashboard/app-services/subs.service';
import { PolicyItemDefaultDataAPIResponse } from 'app/app-model/coverage';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { OptionsService } from 'app/dashboard/app-services/options.service';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { ModalboxComponent } from 'app/shared/components/modalbox/modalbox.component';
import { CurrentPageService } from 'app/shared/services/current-page.service';
import { UserSubscriptionEmitDataI } from 'app/shared/directives/user-subscription.directive';
import { ApiService } from 'app/shared/services/api.service';
import { IncidentsService } from 'app/dashboard/app-services/incidents.service';
import {
  IncidentsResponse,
  IncidentTypeResponse,
} from 'app/app-model/incident';
import { LeaveQuoteService } from 'app/shared/services/leave-quote.service';

// Auto Automanagers
// import { DriversOptionsAutomanagerComponent } from '../auto/drivers/drivers-options-automanager/drivers-options-automanager.component';
import { DriverOptionsAutomanagerService } from '../auto/drivers/drivers-options-automanager/driver-options-automanager.service';
import { AutoStandardCoveragesAutomanagerService } from '../auto/coverages/auto-standard-coverages-automanager/auto-standard-coverages-automanager.service';
import { AutoAdditionalCoveragesAutomanagerService } from '../auto/coverages/auto-additional-coverages-automanager/auto-additional-coverages-automanager.service';
import { CarrierOptionsAutomanagerService } from '../auto/options/carrier-options-automanager/carrier-options-automanager.service';
import { VehiclesSymbolsAutomanagerService } from '../auto/vehicles/vehicles-symbols-automanager/vehicles-symbols-automanager.service';

import { HomeCarrierOptionsAutomanagerService } from '../home/<USER>/home-carrier-options-automanager/home-carrier-options-automanager.service';
import { HomeGeneralOptionsAutomanagerService } from '../home/<USER>/home-general-options-automanager/home-general-options-automanager.service';

import { DwellingCarrierOptionsAutomanagerService } from '../dwelling/dwelling-options/dwelling-carrier-options-automanager/dwelling-carrier-options-automanager.service';
import { DwellingGeneralOptionsAutomanagerService } from '../dwelling/dwelling-options/dwelling-general-options-automanager/dwelling-general-options-automanager.service';

import { UmbrellaGeneralOptionsAutomanagerService } from '../umbrella/umbrella-basic/umbrella-general-options-automanager/umbrella-general-options-automanager.service';
import { UmbrellaCarrierOptionsAutomanagerService } from '../umbrella/umbrella-options/umbrella-carrier-options-automanager/umbrella-carrier-options-automanager.service';

import { resolve } from '../../../../node_modules/@types/q';
import { TownsListAPIResponse } from 'app/app-model/specs';
import { DriversService } from '../app-services/drivers.service';
import { CoveredAutoSymbols } from 'app/app-model/com-covered-auto-symbols';
import { ComCoveredAutoSymbolsService } from '../app-services/com-covered-auto-symbols.service';
import { ComAutoPolicyCoverageService } from '../app-services/com-auto-policy-coverage.service';
import { QuoteCoverageApiResponseData } from 'app/app-model/com-coverage';

import { isAfter, isBefore, parseISO, startOfDay } from 'date-fns';

class LoadingStatus {
  constructor(public progress: number = 0, public totalSteps: number = 100) { }
}

@Component({
  selector: 'app-quote-loader',
  templateUrl: './quote-loader.component.html',
  styleUrls: ['./quote-loader.component.scss'],
  standalone: false
})
export class QuoteLoaderComponent implements OnInit, OnDestroy {
  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private storageService: StorageService,
    private quotesService: QuotesService,
    private overlayLoaderService: OverlayLoaderService,
    private clientsService: ClientsService,
    private overlayClientQuoteInfoService: OverlayClientQuoteInfoService,
    private agencyUserService: AgencyUserService,
    private dwellingService: DwellingService,
    private premiumsService: PremiumsService,
    private vehiclesService: VehiclesService,
    private locationsService: LocationsService,
    private coveragesService: CoveragesService,
    private lookupsService: LookupsService,
    private subsService: SubsService,
    private specsService: SpecsService,
    private optionsService: OptionsService,
    private storageGlobalService: StorageGlobalService,
    private apiCommonService: ApiCommonService,
    private currentPageService: CurrentPageService,
    private apiService: ApiService,
    private incidentsService: IncidentsService,
    private leaveQuoteService: LeaveQuoteService,
    private driverOptionsAutomanagerService: DriverOptionsAutomanagerService,
    private autoStandardCoveragesAutomanagerService: AutoStandardCoveragesAutomanagerService,
    private autoAdditionalCoveragesAutomanagerService: AutoAdditionalCoveragesAutomanagerService,
    private carrierOptionsAutomanagerService: CarrierOptionsAutomanagerService,
    private vehiclesSymbolsAutomanagerService: VehiclesSymbolsAutomanagerService,
    private homeCarrierOptionsAutomanagerService: HomeCarrierOptionsAutomanagerService,
    private homeGeneralOptionsAutomanagerService: HomeGeneralOptionsAutomanagerService,
    private dwellingCarrierOptionsAutomanagerService: DwellingCarrierOptionsAutomanagerService,
    private dwellingGeneralOptionsAutomanagerService: DwellingGeneralOptionsAutomanagerService,
    private umbrellaGeneralOptionsAutomanagerService: UmbrellaGeneralOptionsAutomanagerService,
    private umbrellaCarrierOptionsAutomanagerService: UmbrellaCarrierOptionsAutomanagerService,
    private vehicleService: VehiclesService,
    private driversService: DriversService,
    private comAutoPolicyCoverageService: ComAutoPolicyCoverageService,
    private comCoveredAutoSymbolsService: ComCoveredAutoSymbolsService
  ) { }

  private subscriptionRoute: ISubscription;

  private subscriptionRoute2: ISubscription;

  private storageSubscription: ISubscription;

  public quoteLoadingError = false;
  public quoteLoadingErrorMessage = '';
  public quoteId = '';
  private agencyInfo: UserData;
  public loadingStatus: LoadingStatus = new LoadingStatus();
  public isMaipArcQuote = false;
  public subscriptionMessages: string[] = [];
  public quote: Quote;
  private customerNumber: string = null;
  formType;

  private tmpFullStorageData: appStorageInterface;

  private getSingleVehicleOptionsModelSubscriptions: {
    [key: string]: ISubscription;
  } = {}; // Allow to cancel XHR Requests

  private getLocationInformationForSingleVehicleSubscriptions: {
    [key: string]: ISubscription;
  } = {}; // Allow to cancel XHR Requests

  // User Subscriptions
  // ----------------------------------------------------------------------------
  private userSubscriptionStatusPermissionToContinueQuoteLoad = true;
  ngOnInit() {
    this.premiumsService.rates = [];

    this.checkIfMaipArcQuote();
    this.overlayClientQuoteInfoService.resetData();
    this.setCustomerNumber();

    this.subscriptionRoute = this.activatedRoute.params.subscribe((params) => {
      if (params && params['id'] && params['id'].length > 0) {
        this.quoteId = params['id'];
        this.initQuoteData(params['id']);
      }

      if (params && params['imported']) {
        this.quotesService.setIsImportedQuote();
      } else {
        this.quotesService.setIsImportedQuote(false);
      }
    });
  }

  ngOnDestroy() {
    this.subscriptionRoute && this.subscriptionRoute.unsubscribe();

    this.subscriptionRoute2 && this.subscriptionRoute2.unsubscribe();

    this.storageSubscription && this.storageSubscription.unsubscribe();
  }

  private checkIfMaipArcQuote() {
    const datasource = SessionDataSource[SessionDataSource.MaipArcQuote];
    if (this.quote != null && this.quote.sessionDataSource === datasource) {
      this.quotesService.maipArcQuote = true;
      this.quotesService.setIsMaipArcQuote();
      this.quotesService.showMaipArcLearnMore = true;
      this.quotesService.setShowMaipLearnMore();
      this.isMaipArcQuote = true;
    } else {
      this.quotesService.setIsMaipArcQuote(false);
      this.isMaipArcQuote = false;
    }

    // this.quotesService.maipArcQuote = false;
    // this.quotesService.showMaipArcLearnMore = false;
  }

  private setCustomerNumber() {
    this.storageSubscription = this.storageService
      .getStorageData('recallQuoteInfo')
      .subscribe((data) => {
        if (data && data.customerNumber) {
          this.customerNumber = data.customerNumber;
        }
      });
  }
  private initQuoteData(quoteId: string): void {
    this.loadingStatus = new LoadingStatus();
    this.loadingStatus.progress = 1;
    let clearRmvData = true;

    if (
      this.currentPageService.routeData &&
      this.currentPageService.routeData.additionalParam &&
      this.currentPageService.routeData.additionalParam ===
      'preventRmvQuoteDataClear'
    ) {
      clearRmvData = false;
    }

    this.tmpFullStorageData = JSON.parse(
      JSON.stringify(this.storageService.loadDataFromStorage())
    );
    this.storageService.clearQuoteStoredData(clearRmvData);

    this.premiumsService.closeStorageSubscriptionAndRerate(true);

    this.agencyUserService.userData$.pipe(take(1)).subscribe((agent) => {
      this.agencyInfo = agent;
    });

    this.quoteLoadingError = false;
    this.quoteLoadingErrorMessage = '';
    this.overlayLoaderService.showLoader();
    this.loadQuoteBaseData(quoteId)
      .then((quote: Quote) => {
        this.loadingStatus.progress++;

        const tmpQuote: Quote = quote;
        quote = this.setQuoteInfo(tmpQuote);
        this.quote = quote;
        this.formType = quote.formType;
        this.checkIfMaipArcQuote();
        // this.continueQuoteLoading();
        // Instead of using directly this.continueQuoteLoading() method the 'userSubscription'
        // directive is invoke to check up user permissions and then decide if the quote should be loaded
        // INFO:: userSubscription directive is fired -> function userSubscriptionStatus
      })
      .catch((err) => {
        console.log('LOAD QUOTE ERROR:: ', err);

        if ('_body' in err) {
          try {
            const tmpData = JSON.parse(err['_body']);
            // this.quoteLoadingErrorMessage = ('Message' in tmpData) ? tmpData.Message : '';
          } catch (e) {
            this.quoteLoadingErrorMessage = err['_body'];
          }
        }

        this.quoteLoadingError = true;
        this.overlayLoaderService.hideLoader();
      });
  }

  async loadQuoteBaseData(quoteId: string): Promise<Quote> {
    const requestUrl = this.customerNumber
      ? 'quotes/' + quoteId + '?customerNumber=' + this.customerNumber
      : 'quotes/' + quoteId;

    try {
      const data = await this.quotesService
        .getDataByUrl(requestUrl)
        .toPromise();
      if (data && data.resourceName === 'Quote') {
        return data;
      } else {
        throw new Error(
          'ERROR: It is not a Quote data model >>' + JSON.stringify(data)
        );
      }
    } catch (err) {
      throw err;
    }
  }

  private async continueQuoteLoading(): Promise<void> {
    try {
      this.storageService.setStorageData('selectedQuote', this.quote);

      // Check if this is new quote created by RMV
      // if (
      //   this.tmpFullStorageData.quoteData.newRmvQuoteData &&
      //   this.tmpFullStorageData.quoteData.newRmvQuoteData.effectiveDate &&
      //   this.tmpFullStorageData.quoteData.newRmvQuoteData.plans
      // ) {
      //   console.log("NEW RMV QUOTE", this.tmpFullStorageData);
      //   this.storageService.setStorageData("isNewQuote", true); // Required for setting default values for Options (auto carrier options)
      // }


      // 26-06-2019 :: https://bostonsoftware.atlassian.net/browse/SPR-3195
      // - Check if new quote base on the queryParams
      const isNewQuoteFromQueryParams =
        this.activatedRoute.snapshot.queryParamMap.get('newQuote');

      if (isNewQuoteFromQueryParams) {
        this.storageService.setStorageData('isNewQuote', true); // Required for setting default values for Options (auto carrier options)
      }

      console.log('LOADED QUOTE', this.quote);
      let specPlans: QuotePlan[] = [];
      let quoteSelectedPlans: QuotePlan[] = [];

      // TODO:: get SPEC PLans and adjust current quote plans
      specPlans = await this.getSpecPlans();
      console.log('specPlans', specPlans);
      const plansApiResponse = await this.getQuoteCurrentPlans(
        this.quote,
        this.tmpFullStorageData.quoteData.newRmvQuoteData
      );
      await this.adjustQuotePlansBySpecPlans(
        this.quote,
        plansApiResponse,
        specPlans
      );
      quoteSelectedPlans =
        this.extractPlanFromQuotePlanListAPIResponse(plansApiResponse);
      const clients = await this.getQuoteClients(this.quote);
      await this.getAndUpdateQuoteClientData(clients[0]);
      await this.getSpecTownsList(this.quote);
      await this.getQuoteLocations(this.quote);
      // Check if QuoteEffectiveDate warning should be shown
      // https://bostonsoftware.atlassian.net/browse/SPR-3046
      if(!isNewQuoteFromQueryParams) {
      await this.setEffectiveDateInPastShowWarning(this.quote);
      }
      this.loadingStatus.progress++;
      await this.loadProperQuoteData(this.quote, quoteSelectedPlans);
      this.loadingStatus.progress++;
      this.leaveQuoteService.saveStorageQuoteDataSnapshot();
      this.overlayLoaderService.hideLoader();
    } catch (error) {
      console.error(error);
    }
  }

  private setEffectiveDateInPastShowWarning(quote: Quote): Promise<void> {

    return new Promise((resolve) => {
      if (
        quote.effectiveDate &&
        isAfter(
        startOfDay(new Date()),
        startOfDay(typeof quote.effectiveDate === 'string' ? parseISO(quote.effectiveDate) : quote.effectiveDate)
      )
      ) {
        this.storageService.setStorageData(
          'quoteEffectiveDateInPastShowWarningAfterLoad',
          true
        );
      } else {
        this.storageService.setStorageData(
          'quoteEffectiveDateInPastShowWarningAfterLoad',
          false
        );
      }
      resolve();
    });
  }

  private loadProperQuoteData(quote: Quote, quoteSelectedPlans: QuotePlan[]) {
    switch (quote.lob) {
      case 'AUTOP':
        return this.loadAutoQuoteData(<QuoteAuto>quote, quoteSelectedPlans);
      case 'AUTOB':
        return this.loadCommercialAutoQuoteData(
          <QuoteCommAuto>quote,
          quoteSelectedPlans
        );
      case 'HOME':
        return this.loadHomeQuoteData(<QuoteHome>quote, quoteSelectedPlans);
      case 'DFIRE':
        return this.loadDwellingQuoteData(
          <QuoteDwelling>quote,
          quoteSelectedPlans
        );
      case 'PUMBR':
        return this.loadPumbrQuoteData(
          <QuoteUmbrella>quote,
          quoteSelectedPlans
        );
      // TODO: Need to load data for existing Umbrella Quote here
    }
  }

  // Get Vehicles
  // Get Drivers
  // Get Vehicle Coverages
  // Get Policy history
  // getVehiclesOptionsModel
  // getLocationsInformationForVehicles

  // public loadingCompleteSteps: number = 0;
  // public loadingProgress: number

  async loadAutoQuoteData(quote: QuoteAuto, quoteSelectedPlans: QuotePlan[]) {
    this.loadingStatus.totalSteps = this.loadingStatus.progress + 15;

    try {
      console.log('AUTO QUOTE', quote);

      let drivers: Driver[] = [];
      let vehicles: Vehicle[] = [];

      drivers = await this.getQuoteDrivers(quote);
      this.loadingStatus.progress++;
      vehicles = await this.getQuoteVehicles(quote, this.agencyInfo.agencyId);
      this.loadingStatus.progress++;
      await this.getPolicyHistoryOrCreateIfNotExists(quote);
      this.loadingStatus.progress++;
      await this.getVehiclesOptionsMake(vehicles);
      this.loadingStatus.progress++;
      await this.getVehiclesOptionsModel(vehicles);
      this.loadingStatus.progress++;
      await this.getVehiclesGeneralDetails(vehicles, quote);
      this.loadingStatus.progress++;
      await this.getLocationsInformationForVehicles(vehicles);
      this.loadingStatus.progress++;
      await this.getAutoIncidentsTypesAndDescs();
      this.loadingStatus.progress++;
      await this.getDriversIncidentsFromAPI(drivers);
      this.loadingStatus.progress++;
      // Load all data handled by automanagers
      await this.vehiclesSymbolsAutomanagerService.initialize();
      this.loadingStatus.progress++;
      await this.driverOptionsAutomanagerService.initialize();
      this.loadingStatus.progress++;
      await this.autoStandardCoveragesAutomanagerService.initialize();
      this.loadingStatus.progress++;
      await this.autoAdditionalCoveragesAutomanagerService.initialize();
      this.loadingStatus.progress++;
      await this.carrierOptionsAutomanagerService.initialize();
      this.loadingStatus.progress++;
      if (
        quote != null &&
        quote.sessionDataSource ===
        SessionDataSource[SessionDataSource.MaipArcQuote]
      ) {
        this.router.navigate([
          'dashboard/auto/quotes/' + quote.resourceId + '/premiums',
        ]);
      } else if (drivers && drivers.length) {
        this.router.navigate(['dashboard/auto/' + drivers[0].meta.href]);
      } else {
        this.router.navigate([
          'dashboard/auto/quotes/' + quote.resourceId + '/drivers',
        ]);
      }
    } catch (err) {
      this.overlayLoaderService.hideLoader();
      this.quoteLoadingError = true;
    }
  }

  private async loadCommercialAutoQuoteData(
    quote: QuoteCommAuto,
    quoteSelectedPlans: QuotePlan[]
  ) {
    this.loadingStatus.totalSteps = this.loadingStatus.progress + 6;

    try {
      // TODO:: Load If There Are No Locations, Create One
      await this.checkIfQuoteLocationsExistsIfNotCreateOne(quote);
      this.loadingStatus.progress++;
      await this.getCommercialAutoDriversOrCreateifNotExist(quote);
      this.loadingStatus.progress++;
      const data = await this.getSpecQuoteCoveragesCommercial();
      this.loadingStatus.progress++;
      await this.getQuoteCoveragesCommercialFromApi(quote, data);
      this.loadingStatus.progress++;
      const symbolsData = await this.getSpecSymbolsCommercial();
      this.loadingStatus.progress++;
      await this.getSymbolsCommercial(quote, symbolsData);
      this.loadingStatus.progress++;
      this.router.navigateByUrl(
        '/dashboard/commercial-auto/quotes/' +
        quote.resourceId +
        '/locations?overlay=info&type=client'
      );
      return quote;
    } catch (err) {
      this.overlayLoaderService.hideLoader();
      this.quoteLoadingError = true;
      throw err;
    }
  }



  private async loadHomeQuoteData(
    quote: QuoteHome,
    quoteSelectedPlans: QuotePlan[]
  ) {
    // this.loadingStatus.totalSteps = 20;
    // this.loadingStatus.progress = 1;
    this.loadingStatus.totalSteps = this.loadingStatus.progress + 17;

    console.log('HOME QUOTE', quote);

    // Update storage QuoteSelectedFormTypes
    const quoteSelectedFormTypes =
      this.quotesService.getQuoteSelectedFormTypes(quote);
    this.storageService.setStorageData(
      'selectedQuoteFormTypes',
      quoteSelectedFormTypes
    );

    let dwellings: Dwelling[] = [];

    try {
      const resDwellings = await this.getQuoteDwellings(quote);
      this.loadingStatus.progress++;

      const promises = [];

      dwellings = resDwellings;

      resDwellings.forEach((dwelling) => {
        promises.push(this.getAndUpdateQuoteDwellingLocation(dwelling));
      });

      const data = await Promise.all(promises);
      dwellings = data;

      await this.getHomeDefaultOptions(this.agencyInfo.agencyId);
      this.loadingStatus.progress++;

      await this.getQuoteLossHistory(quote.resourceId, quote);
      this.loadingStatus.progress++;

      await this.getQuoteLossHistoryRequirements(quoteSelectedPlans);
      this.loadingStatus.progress++;

      await this.getQuoteGuidelineOverridesAndSetDefaultValues(
        quote.resourceId
      );
      this.loadingStatus.progress++;

      await this.getQuoteProtectionDevices(
        dwellings[0].protectionDevices.href
      );
      this.loadingStatus.progress++;

      // await this.getOrCreateProtectionClass(quote.resourceId, dwellings[0]);
      await this.processGettingProtectionClass(dwellings[0]);
      this.loadingStatus.progress++;

      console.log('getProtectionClassOverridesFromAPI');
      // return this.getProtectionClassOverridesFromAPI(dwellings[0])
      await this.getProtectionClassOverridesFromAPI(dwellings[0]);
      this.loadingStatus.progress++;

      console.log('getSubsystemsFromAPI');
      // return this.getSubsystemsFromAPI(dwellings[0])
      await this.getSubsystemsFromAPIAndCreateIfItemsNotExists(dwellings[0]);
      this.loadingStatus.progress++;

      await this.getOrCreateHomeQuoteCoverages(quote);
      this.loadingStatus.progress++;

      await this.getHomeDwellingDefaultCoverages(quote);
      this.loadingStatus.progress++;

      await this.getScheduledPropertyList(quote);
      this.loadingStatus.progress++;

      await this.homeCarrierOptionsAutomanagerService.initialize();
      this.loadingStatus.progress++;

      await this.homeGeneralOptionsAutomanagerService.initialize();
      this.loadingStatus.progress++;

      await this.router.navigate([
        'dashboard/home/<USER>/' + quote.resourceId + '/dwelling',
      ]);
      this.loadingStatus.progress++;
    } catch (error) {
      this.overlayLoaderService.hideLoader();
      this.quoteLoadingError = true;
      throw error;
    }

  }

  private async loadDwellingQuoteData(quote: QuoteDwelling, quoteSelectedPlans: QuotePlan[]): Promise<Quote> {
    this.loadingStatus.totalSteps = this.loadingStatus.progress + 11;

    console.log('DWELLING QUOTE', quote);

    // Update storage QuoteSelectedFormTypes
    const quoteSelectedFormTypes = this.quotesService.getQuoteSelectedFormTypes(quote);
    this.storageService.setStorageData('selectedQuoteFormTypes', quoteSelectedFormTypes);

    let dwellings: Dwelling[] = [];

    try {
      dwellings = await this.getQuoteDwellings(quote);
      this.loadingStatus.progress++;

      const promises = [];
      dwellings.forEach((dwelling) => {
        promises.push(this.getAndUpdateQuoteDwellingLocation(dwelling));
      });

      await Promise.all(promises);
      this.loadingStatus.progress++;

      await this.getQuoteProtectionDevices(dwellings[0].protectionDevices.href);
      this.loadingStatus.progress++;

      // return this.getOrCreateProtectionClass(quote.resourceId, dwellings[0]);
      await this.processGettingProtectionClass(dwellings[0]);
      this.loadingStatus.progress++;

      console.log('getProtectionClassOverridesFromAPI');
      await this.getProtectionClassOverridesFromAPI(dwellings[0]);
      this.loadingStatus.progress++;

      await this.getOrCreateHomeQuoteCoverages(quote);
      this.loadingStatus.progress++;

      await this.getOrCreateDwellingOptions(quote);
      this.loadingStatus.progress++;

      await this.getHomeDwellingDefaultCoverages(quote);
      this.loadingStatus.progress++;

      // Automanagers
      await this.dwellingCarrierOptionsAutomanagerService.initialize();
      this.loadingStatus.progress++;

      // general options
      await this.dwellingGeneralOptionsAutomanagerService.initialize();
      this.loadingStatus.progress++;

      this.router.navigate([
        'dashboard/dwelling/quotes/' + quote.resourceId + '/dwelling',
      ]);

      return quote;
    } catch (err) {
      this.overlayLoaderService.hideLoader();
      this.quoteLoadingError = true;
      throw err;
    }
  }

  // ----------------------------------------------------------------------------
  // Common
  // ----------------------------------------------------------------------------
  private setQuoteInfo(quote: Quote): Quote {
    let userData: UserData;

    this.agencyUserService.userData$
      .pipe(first())
      .subscribe((data) => (userData = data));

    // Quote agency contact
    if (!quote.agent && userData) {
      quote.agent = userData.user.userId;
    }

    return quote;
  }

  async getQuoteCurrentPlans(quote: Quote, newRmvQuoteData): Promise<QuotePlanListAPIResponse> {
    try {
      const plan = await this.quotesService.getDataByUrl(quote.quotePlanList.href).pipe(first()).toPromise();
      if (plan && plan.items.length) {
        console.log('SET SELECTED PLANS >> 1:', plan);

        this.storageService.setStorageData('selectedPlan', plan);
        return plan;
      } else if (newRmvQuoteData && newRmvQuoteData.plans && newRmvQuoteData.plans.length) {
        // RMV Quote Create
        const plans = newRmvQuoteData.plans.map((item) => item.data);
        const res = await this.quotesService
          .updateQuoteByUrl(quote.quotePlanList.href, { items: plans }, true)
          .pipe(first())
          .toPromise();
        plan.items[0] = res;
        console.log('SET SELECTED PLANS >> 2:', plan);
        this.storageService.setStorageData('selectedPlan', plan);
        return plan;
      } else {
        const plans = JSON.parse(JSON.stringify(this.storageGlobalService.takeSubs('plans')));

        const quotePlansResponse = new QuotePlanListAPIResponse();
        const quotePlanListItems = new QuotePlanListItem();
        quotePlanListItems.items = plans.filter((el) => el.lob === quote.lob);
        quotePlansResponse.items[0] = quotePlanListItems;
        console.log('SET SELECTED PLANS >> 3:', quotePlanListItems);
        this.storageService.setStorageData('selectedPlan', quotePlansResponse);

        // this.storageService.setStorageData('selectedPlan', plans);
        const res = await this.quotesService
          .updateQuoteByUrl(quote.quotePlanList.href, { items: quotePlanListItems.items }, true)
          .pipe(first())
          .toPromise();
        plan.items[0] = res;
        console.log('SET SELECTED PLANS >> 4:', plan);
        this.storageService.setStorageData('selectedPlan', plan);
        return plan;
      }
    } catch (err) {
      console.log('PLAN LIST ERR:', err);
      throw err;
    }
  }

  async getQuoteClients(quote: Quote): Promise<void | ClientDetails[]> {
    try {
      const res = await this.clientsService.getClientsList(quote.resourceId).pipe(take(1)).toPromise();
      if (quote.lob === 'AUTOB' && res.items && res.items.length) {
        res.items.forEach((details: ClientDetails) => {
          details.type = CLIENT_TYPES.commercial;
        });
      }

      this.storageService.setStorageData('clients', res.items);
      return res.items || [];
    } catch (err) {
      console.log('CLIENTS ERR:', err);
    }
  }

  async getAndUpdateQuoteClientData(client: ClientDetails): Promise<any> {
    this.clientsService.setClientDetailsHasError(false);
    try {
      const promiseAddresses = this.clientsService
        .getClientAddressesAndCreateNotExisting(client)
        .then((res: ClientAddress[]) => {
          this.storageService.setStorageData(
            'clientAddresses',
            JSON.parse(JSON.stringify(res))
          );
        });
      const promiseContactMethods = this.clientsService
        .getClientContactMethodsAndCreateNotExisting(client)
        .then((res: ClientContactMethod[]) => {
          this.storageService.setStorageData(
            'clientContactMethods',
            JSON.parse(JSON.stringify(res))
          );
        });

      await Promise.all([promiseAddresses, promiseContactMethods]);
      this.storageService.setStorageData('selectedClient', client);
    } catch (err) {
      console.log(err);
    }
  }

  async getQuoteLocations(quote: Quote): Promise<any> {
    try {
      const res = await this.locationsService.getLocationsList(quote.resourceId).toPromise();
      this.storageService.setStorageData('quoteLocations', res.items);
    } catch (err) {
      console.log('QUTOE LOCATIONS ERR:', err);
    }
  }

  // COMMERCIAL
  // ---------------------------------------------------------------------------
  private checkIfQuoteLocationsExistsIfNotCreateOne(
    quote: Quote
  ): Promise<LocationData[]> {
    return new Promise((resolve) => {
      this.storageService
        .getStorageData('quoteLocations')
        .pipe(take(1))
        .subscribe((data: LocationData[]) => {
          if (data && data.length) {
            resolve(data);
          } else {
            this.locationsService
              .createLocation(quote.quoteSessionId)
              .subscribe((data: LocationData) => {
                console.log('Created Location for Quote');
                this.storageService.setStorageData('quoteLocations', [data]);
                resolve([data]);
              });
          }
        });
    });
  }

  // ----------------------------------------------------------------------------
  // AUTO
  // ----------------------------------------------------------------------------
  private async getQuoteDrivers(quote: QuoteAuto): Promise<Driver[]> {
    try {
      const drivers = await this.quotesService.getDataByUrl(quote.drivers.href).toPromise();
      if (drivers.items && drivers.items.length) {
        drivers.items.forEach((d: Driver) => {
          d.licenseDate = null;
        });
      }

      this.storageService.setStorageData('driversList', drivers.items);
      return drivers.items;
    } catch (err) {
      console.log('DRIVERS ERR:', err);
    }
  }

  private async getQuoteVehicles(quote: QuoteAuto, agencyId: string): Promise<Vehicle[]> {
    try {
      const res = await this.vehiclesService.getVehiclesList(quote.resourceId).toPromise();
      this.storageService.setStorageData('vehiclesList', res.items);
      return this.createNewVehicleIfThereAreNoVehicles(quote.resourceId, res.items, agencyId);
    } catch (err) {
      console.log('VEHICLES ERR:', err);
    }
  }

  // Create Vehicles
  private async createNewVehicleIfThereAreNoVehicles(
    quoteId: string,
    vehicles: Vehicle[],
    agencyId: string
  ): Promise<Vehicle[]> {
    try {
      if (vehicles && vehicles.length && vehicles.length > 0) {
        return vehicles;
      } else {
        const res: Vehicle = await this.vehiclesService.createVehicle(quoteId).toPromise();
        await this.coveragesService.createAutoStandardAndAdditionalCoveragesForVehicle(res);
        return [res];
      }
    } catch (err) {
      console.log('VEHICLES ERR:', err);
    }
  }
  // End - Create Vehicle

  private async getPolicyHistoryOrCreateIfNotExists(
    quote: QuoteAuto
  ): Promise<QuotePolicyHistory | boolean> {
    try {
      if (quote && quote.policyHistory && quote.policyHistory.href) {
        const policies = await this.quotesService
          .policyHistoryGetListByUri(quote.policyHistory.href)
          .toPromise();
        if (policies && policies.items && policies.items.length) {
          const policyHistoryData = this.formatPolicyHistory(policies.items[0]);
          this.storageService.setStorageData('policyHistory', policyHistoryData);
          return policyHistoryData;
        } else {
          // Create policy history
          const data = await this.quotesService
            .policyHistoryCreateByUri(quote.policyHistory.href, {})
            .toPromise();
          const policyHistoryData = this.formatPolicyHistory(data);
          this.storageService.setStorageData('policyHistory', policyHistoryData);
          return policyHistoryData;
        }
      } else {
        console.log('Quote policyHistory.href missing');
        return false;
      }
    } catch (err) {
      console.log('POLICY HISTORY ERR:', err);
    }
  }

  private async getVehiclesGeneralDetails(
    vehicles: Vehicle[],
    quote: Quote
  ): Promise<void> {
    if (vehicles && vehicles.length) {
      const data: VehicleGeneralDetailsHelper[] = [];

      for (const vehicle of vehicles) {
        const dataToReturn: VehicleGeneralDetailsHelper = {
          vehicleResourceId: vehicle.resourceId,
          vehicleGeneralDetails: null,
          returnedEmptyVehicleGeneralDetails: false,
          validVinNumber: vehicle.vin
            ? this.vehicleService.vinValidate(vehicle.vin)
            : false,
          validPartialVinNumber:
            vehicle.vin &&
              typeof vehicle.vin === 'string' &&
              vehicle.vin.length === 10
              ? true
              : false,
        };

        try {
          const data = await this.lookupsService.getVehicleDataByVin(
            vehicle.vin,
            quote.effectiveDate
          ).toPromise();
          dataToReturn.vehicleGeneralDetails = data;

          if (!data) {
            dataToReturn.returnedEmptyVehicleGeneralDetails = true;
          }

          data.push(dataToReturn);
        } catch (err) {
          console.log('VEHICLE GENERAL DETAILS ERR:', err);
        }
      }

      this.storageService.setStorageData('vehicleGeneralDetailsHelper', data);
    }
  }

  // Get Vehicles Make Options
  private async getVehiclesOptionsMake(vehicles: Vehicle[]): Promise<void> {
    if (vehicles && vehicles.length) {
      const promises = [];

      for (const vehicle of vehicles) {
        const dataToReturn: VehicleOptionsForVehicle = {
          vehicleResourceId: vehicle.resourceId,
          options: [],
        };

        let options;
        if (vehicle.vehicleType === VEHICLE_TYPES.trailer) {
          options = VEHICLE_OPTIONS_MAKE_TRAILER;
        } else if (vehicle.vehicleType === VEHICLE_TYPES.motorcycle) {
          options = VEHICLE_OPTIONS_MAKE_MOTORCYCLE;
        } else if (vehicle.year) {
          options = await this.lookupsService.getVehicleMakesAsOptions(vehicle.year).toPromise();
        }

        dataToReturn.options = options;
        promises.push(dataToReturn);
      }

      this.storageService.setStorageData('vehiclesOptionsMake', promises);
    }
  }

  // getVehiclesOptionsModel - start
  private async getVehiclesOptionsModel(vehicles: Vehicle[]): Promise<void> {
    if (vehicles && vehicles.length) {
      const promises = [];

      for (const vehicle of vehicles) {
        const tmpPromise = this.getSingleVehicleOptionsModel(vehicle);
        promises.push(tmpPromise);
      }

      const values = await Promise.all(promises);
      this.storageService.setStorageData('vehiclesOptionsModel', values);
    }
  }

  private async getSingleVehicleOptionsModel(
    vehicle: Vehicle
  ): Promise<VehicleOptionsForVehicle> {
    const vehicleOptions = new VehicleOptionsForVehicle();
    vehicleOptions.vehicleResourceId = vehicle.resourceId;

    this.getSingleVehicleOptionsModelSubscriptions[vehicle.resourceId] &&
      this.getSingleVehicleOptionsModelSubscriptions[vehicle.resourceId].unsubscribe();

    if (!vehicle.resourceId || !vehicle.year || !vehicle.make) {
      // reject('Not year or make defined for the vehicle');
      vehicleOptions.options = [];
      return vehicleOptions;
    }

    try {
      const options = await this.lookupsService
        .getVehicleModelsAsOptions(vehicle.year, vehicle.make)
        .toPromise();
      vehicleOptions.options = options;
      return vehicleOptions;
    } catch (err) {
      vehicleOptions.options = [];
      return vehicleOptions;
    }
  }
  // getVehiclesOptionsModel - end

  // start - getLocationsInformationsForVehicles
  private async getLocationsInformationForVehicles(
    vehicles: Vehicle[]
  ): Promise<void> {
    if (vehicles && vehicles.length) {
      const promises = [];

      for (const vehicle of vehicles) {
        const tmpPromise = this.getLocationInformationForSingleVehicle(vehicle);
        promises.push(tmpPromise);
      }

      const values = await Promise.all(promises);
      this.storageService.setStorageData('vehiclesLocationsForVehicles', values);
    }
  }
  async getLocationInformationForSingleVehicle(vehicle: Vehicle): Promise<VehicleLocationDataForVehicle> {
    const tmpLocation: VehicleLocationDataForVehicle = new VehicleLocationDataForVehicle();
    tmpLocation.vehicleResourceId = vehicle.resourceId;
    tmpLocation.vehicleQuoteSessionId = vehicle.quoteSessionId;
    tmpLocation.vehicleMeta = Object.assign({}, vehicle.meta);
    tmpLocation.vehicleYear = vehicle.year;
    tmpLocation.vehicleMake = vehicle.make;
    tmpLocation.vehicleModel = vehicle.model;

    this.getLocationInformationForSingleVehicleSubscriptions[vehicle.resourceId] && this.getLocationInformationForSingleVehicleSubscriptions[vehicle.resourceId].unsubscribe();

    try {
      if (vehicle.garagingAddress != null && vehicle.garagingAddress.meta.href) {
        const res: LocationData = await this.locationsService.getLocationByUri(vehicle.garagingAddress.meta.href).pipe(take(1)).toPromise();
        tmpLocation.location = res;
      } else {
        // Create Fake Location data with empty properties
        tmpLocation.location = new LocationData();
        tmpLocation.location.resourceName = 'Location_Fake';
      }
    } catch (error) {
      console.error(error);
    }
    return tmpLocation;
  }
  // end - getLocationsInformationsForVehicles

  private async getDriversIncidentsFromAPI(drivers: Driver[]): Promise<void> {
    console.log('Get Drivers Incidents');
    if (drivers && drivers.length) {
      for (const driver of drivers) {
        await this.incidentsService.getIncidents(driver.incidents.meta.href).toPromise().then(
          (res: IncidentsResponse) => {
            this.storageService.updateDriverIncidentsList(res);
          }
        );
      }
    }
  }

  private async getAutoIncidentsTypesAndDescs(): Promise<void> {
    try {
      const res: IncidentTypeResponse = await this.specsService.getIncidentsSpecsTypes().toPromise();
      this.storageService.setStorageData('autoDriverIncidentsTypesAndDesc', res);
    } catch (error) {
      console.error(error);
    }
  }

  // ----------------------------------------------------------------------------
  // HOME
  // ----------------------------------------------------------------------------
  private async getQuoteDwellings(quote: QuoteHome | QuoteDwelling): Promise<Dwelling[]> {
    try {
      const res: DwellingsListAPIResponse = await this.dwellingService.getDwellingsListByUri(quote.dwellings.href).toPromise();
      this.storageService.setStorageData('dwelling', res.items[0]);
      return res.items;
    } catch (error) {
      console.error(error);
    }
  }

  private async getAndUpdateQuoteDwellingLocation(dwelling: Dwelling): Promise<Dwelling> {
    try {
      let dwellingLocation: LocationData;
      const res = await this.dwellingService.getDwellingLocation(dwelling.dwellingLocation.href).toPromise();
      if (!res.items || !res.items.length) {
        dwellingLocation = await this.dwellingService.createDwellingLocation(dwelling.dwellingLocation.href).toPromise();
      } else {
        dwellingLocation = res.items[0];
      }

      let requireUpdate = false;
      if (!dwellingLocation.state) {
        dwellingLocation.state = 'MA';
        requireUpdate = true;
      }

      if (requireUpdate) {
        dwellingLocation = await this.dwellingService.updateDwellingLocation(dwellingLocation).toPromise();
      }
      return dwelling;
    } catch (error) {
      console.error(error);
    }
  }

  // Home Scheduled Property List -----
  private async getScheduledPropertyList(quote: Quote): Promise<void> {
    try {
      const response = await this.dwellingService.getScheduledPropertyList(quote.resourceId).pipe(take(1)).toPromise();
      if (response.items.length) {
        const resourceId = response.items[0].resourceId;
        await this.getHomeScheduledProperty(quote.resourceId, resourceId);
      } else {
        await this.createScheduledProperty(quote);
      }
    } catch (error) {
      console.error(error);
    }
  }

  private async createScheduledProperty(quote: Quote): Promise<void> {
    try {
      const uri = '/quotes/' + quote.resourceId + '/scheduledProperty';
      const response = await this.dwellingService.createScheduledProperty(uri).pipe(first()).toPromise();
      const resourceId = response.resourceId;
      await this.getHomeScheduledProperty(quote.resourceId, resourceId);
    } catch (error) {
      console.error(error);
    }
  }

  private async getHomeScheduledProperty(quoteId, resourceId): Promise<void> {
    try {
      const response = await this.dwellingService.getScheduledProperty(quoteId, resourceId).pipe(take(1)).toPromise();
      await this.storageService.setStorageData('dwellingScheduledPropertyAPIResponse', response);
      if (response.items.length) {
        await this.storageService.setDwellingScheduledProperty(response.items);
      }
    } catch (error) {
      console.error(error);
    }
  }

  private async getQuoteProtectionDevices(url: string): Promise<DwellingProtectionDevices> {
    try {
      const res = await this.dwellingService.getDwellingProtectionDevices(url).toPromise();
      this.storageService.setStorageData('dwellingProtectionDevices', res.items[0]);
      return res.items[0];
    } catch (error) {
      console.error(error);
    }
  }

  private async getQuoteLossHistory(quoteId: string, quote: Quote): Promise<any> {
    try {
      const res = await this.dwellingService.getLossHistory(quoteId).toPromise();
      if (res && res.items && res.items.length) {
        res.items[0] = this.dwellingService.helpUpdateLossHistoryForHintsAndWarnings(res.items[0], quote);
        this.storageService.setStorageData('dwellingLossHistory', res.items[0]);
      }
    } catch (error) {
      console.error(error);
    }
  }

  async getQuoteLossHistoryRequirements(selectedPlans: QuotePlan[]): Promise<any> {
    const quotePlansIds: string[] = selectedPlans.map((plan) => plan.ratingPlanId);
    const quotePlansIdsString: string = quotePlansIds.join(',');

    try {
      const res = await this.specsService.getLossHistoryRequirements(quotePlansIdsString).toPromise();
      this.storageService.setStorageData('dwellingLossHistoryRequirementsNew', res.items);
    } catch (error) {
      // handle error
    }
  }

  async getQuoteGuidelineOverridesAndSetDefaultValues(quoteId: string): Promise<any> {
    try {
      const res = await this.quotesService.getGuidelineOverrides(quoteId).pipe(take(1)).toPromise();
      const overrides: QuoteGuidelineOverrides[] = [];
      // Set Default Values
      if (res.items && res.items[0]) {
        res.items[0].applyNewHomeCredit = true;
        res.items[0].applyPreferredOrSuperiorHomeCredit = true;

        const response = await this.quotesService
          .updateGuidelineOverrides(quoteId, res.items[0])
          .pipe(take(1))
          .toPromise();
        return response;
      } else {
        // Create Guideline Overrides
        const tmpOverrides = new QuoteGuidelineOverrides();
        const dataToSend = [tmpOverrides];

        const response = await this.quotesService
          .createGuidelineOverrides(quoteId, dataToSend)
          .pipe(take(1))
          .toPromise();
        // Needs to be updated with default values
        response.applyNewHomeCredit = true;
        response.applyPreferredOrSuperiorHomeCredit = true;

        const updatedResponse = await this.quotesService
          .updateGuidelineOverrides(quoteId, response)
          .pipe(take(1))
          .toPromise();
        return updatedResponse;
      }
    } catch (error) {
      // handle error
    }
  }

  async getHomeDefaultOptions(agencyId): Promise<any> {
    try {
      const res = await this.subsService
        .getDefaultHomeCarrierOptions(agencyId, this.formType)
        .pipe(take(1))
        .toPromise();
      console.log('Default Options :: ', res);
      this.storageService.setStorageData('homeDefaultOptions', res);
    } catch (error) {
      console.log('ERR: ', error);
    }
  }

  async getHomeDwellingDefaultCoverages(
    quote: Quote,
    quoteSelectedPlans: QuotePlan[] = [],
    coveragesGroup: string = 'policyCoveragesStandard'
  ): Promise<void> {
    try {
      const quoteFormTypes: string[] = this.quotesService.simplifyFormType(quote.formType);
      const formType: string =
        quoteFormTypes && quoteFormTypes.length && quoteFormTypes[0] ? quoteFormTypes[0] : '';
      const ratingPlansIds: string[] = quoteSelectedPlans.map((plan: QuotePlan) => plan.ratingPlanId);
      const ratingPlansIdsString: string = ratingPlansIds.join(',');
      const url = `/specs/ratingCoverages/state/${quote.state}/lob/${quote.lob}?coverageGroups=${coveragesGroup}&ratingPlans=${ratingPlansIdsString}&formType=${formType}&effectiveDate=${quote.effectiveDate}`;

      const res = await this.apiCommonService
        .getByUri(url)
        .pipe(take(1))
        .toPromise();
      if (res.items && res.items.length) {
        const dataToSave = res.items.map((item) => {
          item.quoteResourceId = quote.resourceId;
          return item;
        });
        this.storageService.setStorageData('dwellDefaultOptions', res);
        this.storageService.setStorageData('homeStandardCoveragesList', dataToSave);
      }
    } catch (error) {
      // handle error
    }
  }

  // ProtectionClassOverrides
  async createProtectionClassOverrides(uri): Promise<void> {
    try {
      const data = await this.dwellingService
        .createDwellingProtectionClassOverrides(uri)
        .pipe(first())
        .toPromise();
      this.storageService.setStorageData('dwellingProtectionClassOverrides', data);
    } catch (error) {
      // handle error
    }
  }

  async getProtectionClassOverridesFromAPI(dwelling: Dwelling): Promise<void> {
    try {
      const data: DwellingProtectionClassOverridesApiResponse = await this.dwellingService
        .getDwellingProtectionClassOverrides(dwelling.protectionClassOverrides.href)
        .pipe(first())
        .toPromise();
      const protectionClassOverrides: DwellingProtectionClassOverrides =
        data.items && data.items[0] ? data.items[0] : new DwellingProtectionClassOverrides();
      this.storageService.setStorageData('dwellingProtectionClassOverrides', protectionClassOverrides);

      if (data && data.items && data.items.length === 0) {
        await this.createProtectionClassOverrides(dwelling.protectionClassOverrides.href);
      }
    } catch (error) {
      // handle error
    }
  }

  async getSubsystemsFromAPI(dwelling: Dwelling): Promise<void> {
    try {
      if (dwelling && dwelling.subsystems && dwelling.subsystems.href) {
        const data: DwellingsSubsystemsAPIResponse = await this.dwellingService
          .getDwellingSubsystem(dwelling.subsystems.href)
          .pipe(take(1))
          .toPromise();
      }
    } catch (error) {
      // handle error
    }
  }

  async getSubsystemsFromAPIAndCreateIfItemsNotExists(dwelling: Dwelling): Promise<void> {
    try {
      if (dwelling && dwelling.subsystems && dwelling.subsystems.href) {
        const data: DwellingsSubsystemsAPIResponse = await this.dwellingService
          .getDwellingSubsystem(dwelling.subsystems.href)
          .pipe(take(1))
          .toPromise();
        if (data.items && data.items.length) {
          return;
        } else {
          const res = await this.dwellingService
            .createDwellingSubsystem(data.meta.href)
            .pipe(take(1))
            .toPromise();
          console.log('CREATED SUBSYSTEMS', res);
        }
      }
    } catch (error) {
      console.log('Error during creating subsystems', error);
      // resolve - component itself should be able to handle creating subsystems
    }
  }


  // Dwelling protection Class
  async getDwellingLocationForProtectionClass(dwelling: Dwelling): Promise<LocationData> {
    try {
      const res: LocationDataCollectionApiResponseData = await this.dwellingService
        .getDwellingLocation(dwelling.dwellingLocation.href)
        .toPromise();

      let locationForProtectionClass: LocationData = new LocationData();
      locationForProtectionClass.state = 'MA';

      if (res && res.items && res.items.length) {
        const tmpLocation: LocationData = res.items.find(
          (item: LocationData) => item.addressType === 'PhysicalRisk'
        );

        if (tmpLocation) {
          locationForProtectionClass = tmpLocation;
        }
      }

      return locationForProtectionClass;
    } catch (error) {
      // handle error
    }
  }


  async getOrCreateProtectionClass(dwelling: Dwelling): Promise<DwellingProtectionClass> {
    try {
      const res = await this.dwellingService.getDwellingProtectionClass(dwelling.protectionClass.href).toPromise();
      if (res && res.items && res.items[0]) {
        return res.items[0];
      } else {
        const response = await this.dwellingService.createDwellingProtectionClass(res.meta.href).toPromise();
        return response;
      }
    } catch (err) {
      throw err;
    }
  }

  async updateDwellingProtectionClass(pc: DwellingProtectionClass): Promise<DwellingProtectionClass> {
    try {
      const res = await this.dwellingService.updateDwellingProtectionClass(pc.meta.href, pc).toPromise();
      return res;
    } catch (err) {
      throw err;
    }
  }

  async processGettingProtectionClass(dwelling: Dwelling): Promise<DwellingProtectionClass> {
    try {
      let dwellingLocationData: LocationData;

      const res = await this.getDwellingLocationForProtectionClass(dwelling);
      dwellingLocationData = res;
      const pc = await this.getOrCreateProtectionClass(dwelling);
      pc.town.townName = dwellingLocationData.fireDistrict
        ? dwellingLocationData.fireDistrict
        : dwellingLocationData.city;
      pc.town.state = dwellingLocationData.state;
      pc.town.townCode = dwellingLocationData.zip;
      let updatedPc: DwellingProtectionClass = pc;
      if (pc.town.townName) {
        updatedPc = await this.updateDwellingProtectionClass(pc);
        this.storageService.setStorageData('dwellingProtectionClass', updatedPc);
      }
      return updatedPc;
    } catch (err) {
      throw err;
    }
  }

  async getOrCreateHomeQuoteCoverages(quote: QuoteHome | QuoteDwelling): Promise<CoverageApiResponseData> {
    try {
      const res = await this.coveragesService.getHomeQuoteCoverages$(quote.coveragesStandard.href).toPromise();
      // storage is updated directly in getHomeQuoteCoverages$ method
      if (res.items && res.items[0] && res.items[0].meta && res.items[0].meta.href) {
        return res;
      } else {
        // Create Home Quote Coverages
        const res = await this.coveragesService.createHomeCoverageByUriWithDefaultValues(quote.coveragesStandard.href).toPromise();
        // Save to storage
        const tmpRes = new CoverageApiResponseData();
        tmpRes.items = [res];
        console.log('CREATED COVERAGES:', tmpRes);
        this.storageService.setStorageData('homeQuoteCoverages', tmpRes);
        return tmpRes;
      }
    } catch (err) {
      throw err;
    }
  }

  async getOrCreateDwellingOptions(quote: QuoteHome | QuoteDwelling): Promise<CoverageApiResponseData> {
    try {
      let uri = '';
      if (quote && quote.options && quote.options.href) {
        uri = quote.options.href;
      } else {
        uri = quote['coverages'].href; // quote.options.href may be changed to quote.coverages.href
      }
      const res = await this.optionsService.getDwellingOptions$(uri).toPromise();
      if (res.items && res.items[0] && res.items[0].meta && res.items[0].meta.href) {
        return res;
      } else {
        const res = await this.optionsService.createDwellingOptionsByUriWithDefaultValues(uri).toPromise();
        const tmpRes = new CoverageApiResponseData();
        tmpRes.items = [res];
        this.storageService.setStorageDataDwellingQuoteOptions(tmpRes);
        return tmpRes;
      }
    } catch (err) {
      throw err;
    }
  }

  // ------

  // -----------------------------------

  // Helpers
  // ----------------------------------------------------------------------------
  private formatPolicyHistory(data): QuotePolicyHistory {
    const formattedData = new QuotePolicyHistory();

    for (const prop in data) {
      if (data[prop] === 'Unknown') {
        data[prop] = null;
      }
      formattedData[prop] = data[prop];
    }

    return formattedData;
  }

  public getProgressPercentage(data: LoadingStatus): number {
    const percent = Math.floor((data.progress / data.totalSteps) * 100);
    const result = percent > 100 ? 100 : percent;
    return result;
  }

  // check user subscription
  public getSubscriptionName(quote: Quote): string | Array<string> {
    let subscriptionCode: string | Array<string> = '';
    if (quote && quote.lob) {
      switch (quote.lob) {
        case 'AUTOP':
          subscriptionCode = 'MAAuto';
          break;
        case 'AUTOB':
          subscriptionCode = 'MACommercialAuto';
          break;
        case 'HOME':
          subscriptionCode =
            quote.state && quote.state === 'MA' ? 'MAHome' : 'RIHome';
          break;
        case 'DFIRE':
          subscriptionCode =
            quote.state && quote.state === 'MA' ? 'Dwelling' : 'RIDwelling';
          break;
        case 'PUMBR':
          subscriptionCode = 'Umbrella';
          break;
      }
    }
    return subscriptionCode;
  }

  public userSubscriptionStatus(eventData: UserSubscriptionEmitDataI): void {
    this.subscriptionMessages = eventData.messages;
    this.userSubscriptionStatusPermissionToContinueQuoteLoad =
      eventData.proceed;
    if (eventData.createQuote) {
      this.continueQuoteLoading();
    }
  }

  public actionOnUserSubscriptionModalClose(modalRef: ModalboxComponent): void {
    modalRef.closeModalbox();

    if (this.userSubscriptionStatusPermissionToContinueQuoteLoad) {
      this.continueQuoteLoading();
    } else {
      this.overlayLoaderService.hideLoader();
      this.router.navigate(['/dashboard']);
    }
  }

  private extractPlanFromQuotePlanListAPIResponse(
    data: QuotePlanListAPIResponse
  ): QuotePlan[] {
    let plans: QuotePlan[] = [];

    if (data && data.items && data.items.length) {
      plans = JSON.parse(JSON.stringify(data.items[0].items));
    }
    return plans;
  }

  //#region Load Umbrella (PUMBR) specific data
  async loadPumbrQuoteData(quote: QuoteUmbrella, quoteSelectedPlans: QuotePlan[]): Promise<Quote> {
    this.loadingStatus.totalSteps = this.loadingStatus.progress + 6;

    try {
      console.log('PUMBR QUOTE', quote);
      await this.getQuoteCoverages(quote);
      this.loadingStatus.progress++;

      await this.getBasicPolicyInfo(quote);
      this.loadingStatus.progress++;

      await this.getWatercraftList(quote);
      this.loadingStatus.progress++;

      await this.umbrellaGeneralOptionsAutomanagerService.initialize();
      this.loadingStatus.progress++;

      await this.umbrellaCarrierOptionsAutomanagerService.initialize();
      this.loadingStatus.progress++;

      this.router.navigate([
        'dashboard/umbrella/quotes/' + quote.resourceId + '/basics',
      ]);

      return quote;
    } catch (err) {
      throw err;
    }
  }

  async getQuoteCoverages(quote: QuoteUmbrella): Promise<CoverageApiResponseData> {
    const quoteCoverageUrl = '/quotes/' + this.quote.quoteSessionId + '/coverages';
    try {
      const coverages = await this.quotesService.getDataByUrl(quoteCoverageUrl).toPromise();
      this.storageService.setStorageData('umbrellaQuoteCoverages', coverages);
      return coverages;
    } catch (err) {
      console.log('Quote Coverages ERR:', err);
    }
  }

  async getBasicPolicyInfo(quote: QuoteUmbrella): Promise<CoverageApiResponseData> {
    const basicPolicyInfoUrl = quote.basicPolicyInfo.href;
    try {
      const bpi = await this.quotesService.getDataByUrl(basicPolicyInfoUrl).toPromise();
      if (bpi && bpi.items && bpi.items.length) {
        this.storageService.setStorageData('basicPolicyInfo', bpi.items[0]);
      }
      return bpi;
    } catch (err) {
      console.log('Quote basic policy info ERR:', err);
    }
  }

  async getWatercraftList(quote: QuoteUmbrella): Promise<WatercraftsResponse> {
    const basicWatercraftsUrl = quote.watercrafts.href;
    try {
      const wcData = await this.quotesService.getDataByUrl(basicWatercraftsUrl).toPromise();
      if (wcData && wcData.items) {
        this.storageService.setStorageData('watercraftList', wcData);
      }
      return wcData;
    } catch (err) {
      console.log('Error loading watercraft data ERR:', err);
    }
  }
  //#endregion

  private async getSpecPlans(): Promise<QuotePlan[]> {
    try {
      const plans: QuotePlan[] = this.storageGlobalService.takeSubs('plans');

      if (plans && plans.length) {
        return plans;
      } else {
        const response = await this.subsService.getRatingPlans(
          this.agencyInfo.agencyId
        ).toPromise();
        return response;
      }
    } catch (err) {
      throw err;
    }
  }

  private async getSpecTownsList(quote: Quote): Promise<TownsListAPIResponse> {
    try {
      const data: TownsListAPIResponse = await this.specsService
        .getTownsList(quote.state, quote.lob)
        .toPromise();
      console.log('Loaded LOB Towns:', data);
      this.storageService.setStorageData('lobTowns', data.items);
      return data;
    } catch (err) {
      console.log('getSpecTownsList ERR: ', err);
      throw err;
    }
  }

  async adjustQuotePlansBySpecPlans(
    quote: Quote,
    quotePlansApiResponse: QuotePlanListAPIResponse,
    specPlans: QuotePlan[]
  ): Promise<QuotePlanListAPIResponse> {
    const result: QuotePlanListAPIResponse = JSON.parse(
      JSON.stringify(quotePlansApiResponse)
    );

    if (
      result &&
      result.items &&
      result.items.length &&
      result.items[0].items &&
      result.items[0].items.length &&
      specPlans &&
      specPlans.length
    ) {
      const specPlansForLob: QuotePlan[] = specPlans.filter(
        (sp: QuotePlan) => sp.lob === quote.lob
      );

      const plansAdjusted: QuotePlan[] = result.items[0].items.map(
        (p: QuotePlan) => {
          const tmpSpecPlan = specPlansForLob.find(
            (sp: QuotePlan) => sp.ratingPlanId === p.ratingPlanId
          );
          if (tmpSpecPlan) {
            return { ...tmpSpecPlan, name: p.name, bscCompanyQuoteNumber: p.bscCompanyQuoteNumber };
          }
          return p;
        }
      );

      result.items[0].items = plansAdjusted;

      try {
        await this.quotesService.updateQuoteByUrl(
          quote.quotePlanList.href,
          { items: result.items[0].items },
          true
        ).pipe(first()).toPromise();
        this.storageService.setStorageData('selectedPlan', result);
        return result;
      } catch (err) {
        throw err;
      }
    } else {
      console.log('Returning not adjusted selected plans');
      return quotePlansApiResponse;
    }
  }
  // Commercial Auto
  // ---------------------------------------------------------------------------
  async getCommercialAutoDriversOrCreateifNotExist(quote: Quote): Promise<ComDriver[]> {
    try {
      const res = await this.driversService.getComDriversListByUri('/quotes/' + quote.resourceId + '/comdrivers').pipe(take(1)).toPromise();
      if (res.items && res.items.length === 0) {
        const drivers = await this.createCommercialAutoDriver(quote);
        return drivers;
      } else {
        this.storageService.setStorageData('comDriverList', res.items);
        return res.items;
      }
    } catch (err) {
      throw err;
    }
  }

  async createCommercialAutoDriver(quote: Quote): Promise<ComDriver[]> {
    try {
      const res = await this.driversService.createComDriver(quote.resourceId).toPromise();
      this.storageService.setStorageData('comDriverList', [res]);
      return [res];
    } catch (err) {
      throw err;
    }
  }

  async getSpecQuoteCoveragesCommercial(): Promise<CoverageItem[]> {
    try {
      const specData = await this.comAutoPolicyCoverageService.GetQuoteCoverageSpecByUri().toPromise();
      const specQuoteCoverages: CoverageItem[] = specData.items;
      this.storageService.setStorageData('commercialAutoSpecCoverages', specQuoteCoverages);
      return specQuoteCoverages;
    } catch (err) {
      throw err;
    }
  }

  async getSpecSymbolsCommercial(): Promise<CoveredAutoSymbols> {
    try {
      const specData = await this.comCoveredAutoSymbolsService.GetSymbolsSpecByUri().toPromise();
      const specSymbols: CoveredAutoSymbols = new CoveredAutoSymbols();
      specSymbols.symbols = specData.items;
      this.storageService.setStorageData('coveredAutoSpecSymbols', specSymbols);
      return specSymbols;
    } catch (err) {
      throw err;
    }
  }

  async getQuoteCoveragesCommercialFromApi(quote: Quote, specCoverages: CoverageItem[]): Promise<void> {
    try {
      const res = await this.comAutoPolicyCoverageService.GetQuoteCoverages(quote.resourceId).toPromise();
      if (res && res.items && res.items.length > 0) {
        this.storageService.setStorageData('commercialAutoQuoteCoverages', res.items[0]);
      } else {
        const newRes = await this.comAutoPolicyCoverageService.createPolicyInfoCoveragesWithDefaults(quote, specCoverages);
        this.storageService.setStorageData('commercialAutoQuoteCoverages', newRes);
      }
    } catch (err) {
      throw err;
    }
  }

  async getSymbolsCommercial(quote: Quote, specCoveredAutoSymbols: CoveredAutoSymbols): Promise<void> {
    try {
      const data = await this.comCoveredAutoSymbolsService.GetSymbols(quote.resourceId).toPromise();
      if (data && data.items && data.items.length > 0) {
        this.storageService.setStorageData('coveredAutoSymbols', data.items[0]);
      } else {
        const newRes = await this.comCoveredAutoSymbolsService.createSymbolsWithDefaults(quote, specCoveredAutoSymbols);
        this.storageService.setStorageData('coveredAutoSymbols', newRes);
      }
    } catch (err) {
      throw err;
    }
  }
}
