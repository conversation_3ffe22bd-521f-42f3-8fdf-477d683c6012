
import {take, first, switchMap, catchError, defaultIfEmpty, finalize, tap} from 'rxjs/operators';
import { Component, OnInit, ViewChild } from '@angular/core';

import { FilterOption } from 'app/app-model/filter-option';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { PremiumsService } from 'app/dashboard/auto/premiums/premiums.service';
import { PlansSelectorComponent } from 'app/shared/components/plans-selector/plans-selector.component';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { OptionsService } from 'app/dashboard/app-services/options.service';
import { CoverageItemParsed, Coverage } from 'app/app-model/coverage';

import { SharedStaticHomeDataFormTypeOptions } from 'app/dashboard/home/<USER>';
import { forkJoin, SubscriptionLike as ISubscription, of } from 'rxjs';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { QuotePlanListAPIResponse, QuotePlan } from 'app/app-model/quote';
import { DatepickerModalComponent } from 'app/shared/components/datepicker-modal/datepicker-modal.component';
import { addYears, format, parseISO } from 'date-fns';



@Component({
    selector: 'app-aside-home-quote',
    templateUrl: './aside-home-quote.component.html',
    styleUrls: ['./aside-home-quote.component.scss'],
    standalone: false
})
export class AsideHomeQuoteComponent implements OnInit {
  private subscription: ISubscription;
  public quote;
  public effectiveDate;
  public plansOnQuotes: FilterOption[] = [];
  public ratedPlans: any;
  public plansOnQuotesFiltered: FilterOption[] = [];
  public plansCount = 0;
  public selectedPlan: any;
  public selectedPlansOnQuote: any;
  public formTypes: FilterOption[] = []; // Array<string> = [];
  private selectedPlanObject;
  private currentPlanUrl;
  private currentPlanSubscriptionGet: ISubscription;
  private plansSubscription: ISubscription;
  private currentRatesSubscription: ISubscription;
  private selectedPlans: QuotePlan[] = [];
  private selectedPlansIds: string[] = [];

  @ViewChild('refPlansSelector', {static: true})
  private refPlansSelector: PlansSelectorComponent;
  @ViewChild('datepickerEffectiveDate') public datepickerEffectiveDate: DatepickerModalComponent;

  constructor(
    private storageService: StorageService,
    private quotesService: QuotesService,
    private overlayLoaderService: OverlayLoaderService,
    private premiumsService: PremiumsService,
    private storageGlobalService: StorageGlobalService,
    private specsService: SpecsService,
    private optionsService: OptionsService
  ) {}

  ngOnInit() {
    this.setPlansOnQuotes(
      this.storageGlobalService.takeSubs('plans'),
      this.ratedPlans
    );

    this.subscription = this.storageService
      .getStorageData('selectedQuote')
      .subscribe(res => {
        this.quote = res;
        this.getCurrentPlan(res.quotePlanList.href);
        this.effectiveDate = res.effectiveDate;
        this.setFormTypes(res);
      });

      setTimeout(() => {
        this.showQuoteEffectiveDateInPastWarningModal();
      });
    // this.subscribtionCurrentPlans();
  }

  ngOnDestroy() {
    this.subscription && this.subscription.unsubscribe();
    this.currentPlanSubscriptionGet &&
      this.currentPlanSubscriptionGet.unsubscribe();
    this.plansSubscription && this.plansSubscription.unsubscribe();
    this.currentRatesSubscription &&
      this.currentRatesSubscription.unsubscribe();
  }

  public updateEffectiveDate(event) {
  const dateValue = event.date instanceof Date ? event.date : parseISO(event.date);

  this.quote.effectiveDate = format(dateValue, 'yyyy-MM-dd');
  this.quote.expirationDate = format(addYears(dateValue, 1), 'yyyy-MM-dd');

    this.overlayLoaderService.showLoader();
    const quoteEffectiveDate = this.quote?.effectiveDate ?? '';

    // Use forkJoin to run multiple observables in parallel
    this.storageService.getStorageData('selectedPlan').pipe(
      first(),
      switchMap((res: QuotePlanListAPIResponse) => {
        if (res?.items?.length) {
          this.selectedPlans = JSON.parse(JSON.stringify(res.items[0].items));
          this.selectedPlansIds = this.selectedPlans.map(plan => plan.ratingPlanId);
          const selectedPlansIdsString = this.selectedPlansIds.join(',');

          // Using forkJoin to run General Options and Carrier Options in parallel
          return forkJoin({
            generalOptions: this.specsService.getRatingCoverages('MA', this.quote.lob, 'PolicyOptionsGeneral', selectedPlansIdsString, '', quoteEffectiveDate),
            carrierOptions: this.specsService.getRatingCoverages('MA', this.quote.lob, 'PolicyOptionsCarrier', selectedPlansIdsString, '', quoteEffectiveDate)
          });
        } else {
          return of(null);
        }
      }),
      switchMap((result) => {
        console.log(result);
        if (result) {
          const generalOptionsList = result.generalOptions.items;
          const carrierOptionsList = result.carrierOptions.items;

          // Grab both homeGeneralOptionsParsed and homeCarrierOptionsParsed in parallel
          return forkJoin({
            homeGeneralOptionsParsed: this.storageService.getStorageData('homeGeneralOptionsParsed').pipe(
              first(), // Ensure completion
           //   tap(homeGeneralOptions => console.log('Home General Options Parsed:', homeGeneralOptions)), // Debugging
              defaultIfEmpty([]) // Fallback in case of no data
            ),
            homeCarrierOptionsParsed: this.storageService.getStorageData('homeCarrierOptionsParsed').pipe(
              first(), // Ensure completion
           //   tap(homeCarrierOptions => console.log('Home Carrier Options Parsed:', homeCarrierOptions)), // Debugging
              defaultIfEmpty([]) // Fallback in case of no data
            )
          }).pipe(
            switchMap((storageResult) => {

              const allPoliciesToUpdateGeneral = storageResult.homeGeneralOptionsParsed.filter((x) => {
                return generalOptionsList.some((y) => y.coverageCode === x.coverageCode && x.isActive === true);
              });

              const allPoliciesToUpdateCarrier = storageResult.homeCarrierOptionsParsed.filter((x) => {
                return carrierOptionsList.some((y) => y.coverageCode === x.coverageCode && x.isActive === true);
              });

              const readyGeneralOptions = this.optionsService.convertPolicyItemParsedArrayToPolicyArray(allPoliciesToUpdateGeneral);
              const readyCarrierOptions = this.optionsService.convertPolicyItemParsedArrayToPolicyArray(allPoliciesToUpdateCarrier);

              const mergedCoverages = [...readyGeneralOptions, ...readyCarrierOptions];


              return this.optionsService.updatePoliciesByUri(storageResult.homeCarrierOptionsParsed[0]?.endpointUrl, { coverages: mergedCoverages }).pipe(
                first(), // Ensure completion
                tap(() => console.log('Update Policies Call Success')), // Debugging
                catchError(err => {
                  console.log('Update Policies Error:', err); // Debugging
                  return of(null);
                })
              );
            })
          );
        } else {
          console.log('No result from first forkJoin'); // Debugging
          return of(null);
        }
      }),
      first(),
      finalize(() => {
        this.overlayLoaderService.hideLoader(); // Hide loader after everything is done
      })
    ).subscribe(
      () => {
        console.log('Updated Coverages'); // Debugging
      },
      err => {
        console.log('Update Error:', err); // Debugging
      }
    );

    // Update quote information
    this.quotesService
      .updateQuoteInfo(this.quote.resourceId, this.quote).pipe(
      first()
    ).subscribe(quote => {
      this.quote = quote;
      this.effectiveDate = quote.effectiveDate;
      this.overlayLoaderService.hideLoader();
      this.storageService.setStorageData('selectedQuote', this.quote);
      this.premiumsService.rerateGeneral(window.location.href);
    });
  }

  private setPlansOnQuotes(plans, ratedPlans) {
    this.plansOnQuotes = [];
    plans.forEach(item => {
      if (item.lob === 'HOME') {
        this.plansOnQuotes.push({
          text: item.name,
          id: item.ratingPlanId,
          data: item
        });
      }
    });
    this.plansOnQuotes.sort((a, b) => {
      if (a.text < b.text) {
        return -1;
      } else if (a.text > b.text) {
        return 1;
      } else {
        return 0;
      }
    });

    this.plansCount = this.plansOnQuotes.length;

    if (this.plansCount) {
      this.selectedPlan = [];
      this.plansOnQuotes.forEach((plan, index) => {
        this.selectedPlan.push(plan);
      });
    }
  }

  private setFormTypes(quote) {
    if (quote && quote.policyType) {
      this.formTypes = SharedStaticHomeDataFormTypeOptions.filter(
        opt => opt.data.forPolicyType === quote.policyType
      );
    } else {
      this.formTypes = SharedStaticHomeDataFormTypeOptions.filter(
        opt => opt.data.forPolicyType === 'Homeowner'
      );
      this.quote.policyType = 'Homeowner';
    }
  }

  private processCurrentPlans(res) {
    if (res.items[0].meta && res.items[0].meta.href) {
      this.currentPlanUrl = res.items[0].meta.href;
    } else {
      this.currentPlanUrl = res.meta.href;
    }
    this.selectedPlanObject = res;
    this.selectedPlan = [];
    if (res.items[0].items.length) {
      res.items[0].items.forEach(item => {
        this.selectedPlan.push({
          data: item,
          id: item.ratingPlanId,
          text: item.name
        });
      });
    } else {
      this.plansOnQuotes.forEach(plan => {
        this.selectedPlan.push(plan);
      });
    }
    this.filterPlans(this.quote.formType, false);
    // this.filterPlans(this.quote.formType);
    this.refPlansSelector.init();
  }

  private subscribtionCurrentPlans(): void {
    this.currentPlanSubscriptionGet = this.storageService
      .getStorageData('selectedPlan')
      .subscribe(res => {
        if (res && res.items && res.items.length) {
          this.processCurrentPlans(res);
        }
      });
  }

  private getCurrentPlan(uri: string) {
    this.plansSubscription && this.plansSubscription.unsubscribe();
    this.plansSubscription = this.storageService
      .getStorageData('selectedPlan')
      .subscribe(res => {
        if (res && res.items && res.items.length) {
          this.processCurrentPlans(res);
        } else {
          this.getCurrentPlanFromAPI(uri);
        }
      });
  }

  private getCurrentPlanFromAPI(uri): void {
    let getCurrentPlanSubscription;

    this.overlayLoaderService.showLoader();
    this.quotesService
      .getDataByUrl(uri).pipe(
      first())
      .subscribe(plan => {
        this.storageService.setStorageData('selectedPlan', plan);
        this.refreshLossHistory(plan);
        this.overlayLoaderService.hideLoader();
      });
  }

  private refreshLossHistory(data: QuotePlanListAPIResponse) {
    const plansList: QuotePlan[] = this.extractPlanFromQuotePlanListAPIResponse(
      data
    );
    const plansListIds: string[] = plansList.map(plan => plan.ratingPlanId);
    const plansListIdsString: string = plansListIds.join(',');

    this.specsService
      .getLossHistoryRequirements(plansListIdsString).pipe(
      take(1))
      .subscribe(res => {
        this.storageService.setStorageData(
          'dwellingLossHistoryRequirementsNew',
          res.items
        );
      });
  }

  private extractPlanFromQuotePlanListAPIResponse(
    data: QuotePlanListAPIResponse
  ): QuotePlan[] {
    let plans: QuotePlan[] = [];

    if (data && data.items && data.items.length) {
      plans = JSON.parse(JSON.stringify(data.items[0].items));
    }
    return plans;
  }

  public updateQuotePlan($event): void {
    this.selectedPlansOnQuote = [];

    $event.selectedOption.forEach((option, index) => {
      this.selectedPlansOnQuote.push(option.data);
    });
    const data = { items: this.selectedPlansOnQuote };

    if (this.currentPlanUrl) {
      this.overlayLoaderService.showLoader();
      this.quotesService
        .updateQuoteByUrl(this.currentPlanUrl, data, false)
        .subscribe(plans => {
          this.selectedPlan = $event.selectedOption;
          this.overlayLoaderService.hideLoader();
          this.getCurrentPlanFromAPI(this.selectedPlanObject.meta.href);
        });
    }
  }

  public updateQuoteFormType($event): void {
    this.overlayLoaderService.showLoader();
    this.quote.formType = $event.selectedOption.id;

    this.currentRatesSubscription = this.storageService
      .getStorageData('rates')
      .subscribe(res => {
        if (res && res.length) {
          this.ratedPlans = res;
        }
      });

    this.quotesService
      .updateQuoteInfo(this.quote.resourceId, this.quote)
      .subscribe(quote => {
        this.quote = quote;
        this.storageService.setStorageData('selectedQuote', this.quote);

        // Update storage QuoteSelectedFormTypes
        const quoteSelectedFormTypes = this.quotesService.getQuoteSelectedFormTypes(
          this.quote
        );
        this.storageService.setStorageData(
          'selectedQuoteFormTypes',
          quoteSelectedFormTypes
        );

        this.filterPlans($event.text, false);
        this.adjustSelectedPlansAfterFormTypeChange();
        this.refPlansSelector.init();
        this.premiumsService.rerateAll();
        this.overlayLoaderService.hideLoader();
      });
  }

  private filterPlans(formType: string, changeSelectedPlans: boolean = true) {
    if (this.plansOnQuotes && this.plansOnQuotes.length) {
      const formTypes = this.quotesService.simplifyFormType(formType);
      this.plansOnQuotesFiltered = [];
      this.plansOnQuotes.forEach(plan => {
        this.plansOnQuotesFiltered.push(JSON.parse(JSON.stringify(plan)));
      });
      const filteredPlans = [];
      // this.selectedPlan = [];
      this.plansOnQuotesFiltered.forEach(plan => {
        // if (plan && plan.data && plan.data.allowedFormTypesCode) {
        if (
          plan &&
          plan.data &&
          plan.data.allowedFormTypesCode &&
          plan.data.state === this.quote.state
        ) {
          let allowType = false;
          formTypes.forEach(fType => {
            if (plan.data.allowedFormTypesCode.indexOf(fType) > -1) {
              allowType = true;
            }
          });
          if (allowType) {
            filteredPlans.push(plan);
            // this.selectedPlan.push(plan);
          }
        }
      });
      this.plansOnQuotesFiltered = filteredPlans;

      if (changeSelectedPlans) {
        this.selectedPlan = JSON.parse(JSON.stringify(filteredPlans));
        this.storageService.setStorageData('selectedPlan', this.selectedPlan);
      }
    }
  }

  private adjustSelectedPlansAfterFormTypeChange(): void {
    const correctSelectedPlans = [];

    this.selectedPlan.forEach(el => {
      let planInAvailablePlans;

      if (this.plansOnQuotesFiltered) {
        planInAvailablePlans = this.plansOnQuotesFiltered.find(plan => {
          return el.id === plan.id;
        });

        if (this.ratedPlans) {
          const ratedPlan = this.ratedPlans.find(plan => {
            return Number(el.id) === plan.items[0].ratingPlanId;
          });

          if (ratedPlan && planInAvailablePlans) {
            planInAvailablePlans.data.bscCompanyQuoteNumber =
              ratedPlan.items[0].carrierQuoteId;
          }
        }
      }

      if (planInAvailablePlans) {
        correctSelectedPlans.push(planInAvailablePlans);
      }
    });

    if (correctSelectedPlans.length !== this.selectedPlan) {
      const evData = {
        id: 'multiselect_mode',
        text: 'multiselect_mode',
        selectedOption: correctSelectedPlans
      };
      this.updateQuotePlan(evData);
    }
  }

  public get clientNameToDisplay(): string {
    let name = '';

    if (this.quote.client && this.quote.client.firstName) {
      name += this.quote.client.firstName;
    }

    if (this.quote.client && this.quote.client.lastName) {
      name += ' ' + this.quote.client.lastName;
    }

    return name.trim() ? name : 'No clients name';
  }

  private showQuoteEffectiveDateInPastWarningModal(): void {
    this.storageService.getStorageData('quoteEffectiveDateInPastShowWarningAfterLoad').pipe(
      take(1))
      .subscribe((data: boolean) => {
        if (data) {
          this.datepickerEffectiveDate && this.datepickerEffectiveDate.open();
          this.storageService.setStorageData('quoteEffectiveDateInPastShowWarningAfterLoad', false);
        }
      });
  }
}
