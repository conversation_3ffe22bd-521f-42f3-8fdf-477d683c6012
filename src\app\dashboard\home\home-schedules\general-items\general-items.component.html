<section class="section section--compact u-spacing--2-5">
  <div class="row">
    <div class="col-xs-12">
      <h1 class="o-heading">General Items</h1>
    </div>
  </div>

  <div class="row u-spacing--1">
    <div class="col-xs-12">
      <div class="box box--silver">
        <div class="row o-columns">
          <div class="col-xs-6">
            <table class="form-table">
              <tr class="form-table__row">

                <tr class="form-table__row" [ngClass]="{'is-required-field': false}">
                  <td class="form-table__cell u-width-140px">
                    <label for="">Cameras:</label>
                  </td>
                  <td class="form-table__cell u-width-160px">
                    <input fieldAutofocus #refCameras type="number" id="cameras" name="Cameras" [(ngModel)]="propCameras.itemValueAmount" (change)="handleChange($event)">
                  </td>
                  <td class="form-table__cell u-width-70px"></td>
                </tr>

                <tr class="form-table__row" [ngClass]="{'is-required-field': false}">
                  <td class="form-table__cell u-width-140px">
                    <label for="">Fine Arts:</label>
                  </td>
                  <td class="form-table__cell u-width-160px">
                    <input #refFineArts type="number" id="FineArts" name="FineArts" [(ngModel)]="propFineArts.itemValueAmount" (change)="handleChange($event)">
                  </td>
                  <td class="form-table__cell u-width-70px"></td>
                </tr>

                <tr class="form-table__row" [ngClass]="{'is-required-field': false}">
                  <td class="form-table__cell u-width-140px">
                    <label for="">Furs:</label>
                  </td>
                  <td class="form-table__cell u-width-160px">
                    <input #refFurs type="number" id="furs" name="Furs" [(ngModel)]="propFurs.itemValueAmount" (change)="handleChange($event)">
                  </td>
                  <td class="form-table__cell u-width-70px"></td>
                </tr>

                <tr class="form-table__row" [ngClass]="{'is-required-field': false}">
                  <td class="form-table__cell u-width-140px">
                    <label for="">Golf Equipment:</label>
                  </td>
                  <td class="form-table__cell u-width-160px">
                    <input #refGolfEquipment type="number" id="golfEquipment" name="GolfEquipment" [(ngModel)]="propGolfEquipment.itemValueAmount" (change)="handleChange($event)">
                  </td>
                  <td class="form-table__cell u-width-70px"></td>
                </tr>

            </table>
          </div>
          <div class="col-xs-6">
            <table class="form-table">

              <tr class="form-table__row" [ngClass]="{'is-required-field': false}">
                  <td class="form-table__cell u-width-140px">
                    <label for="">Guns:</label>
                  </td>
                  <td class="form-table__cell u-width-160px">
                    <input #refGuns type="number" id="guns" name="Guns" [(ngModel)]="propGuns.itemValueAmount" (change)="handleChange($event)">
                  </td>
                  <td class="form-table__cell u-width-70px"></td>
                </tr>

                <tr class="form-table__row" [ngClass]="{'is-required-field': false}">
                  <td class="form-table__cell u-width-140px">
                    <label for="">Musical Equipment:</label>
                  </td>
                  <td class="form-table__cell u-width-160px">
                    <input #refMusicalInstruments type="number" id="musicalInstruments" name="MusicalInstruments" [(ngModel)]="propMusicalInstruments.itemValueAmount" (change)="handleChange($event)">
                  </td>
                  <td class="form-table__cell u-width-70px"></td>
                </tr>

                <tr class="form-table__row" [ngClass]="{'is-required-field': false}">
                  <td class="form-table__cell u-width-140px">
                    <label for="">Silver:</label>
                  </td>
                  <td class="form-table__cell u-width-160px">
                    <input #refSilver type="number" id="silverware" name="Silverware" [(ngModel)]="propSilverware.itemValueAmount" (change)="handleChange($event)">
                  </td>
                  <td class="form-table__cell u-width-70px"></td>
                </tr>

                <tr class="form-table__row" [ngClass]="{'is-required-field': false}">
                  <td class="form-table__cell u-width-140px">
                    <label for="">Stamps:</label>
                  </td>
                  <td class="form-table__cell u-width-160px">
                    <input #refStamps type="number" id="stamps" name="Stamps" [(ngModel)]="propStamps.itemValueAmount" (change)="handleChange($event)">
                  </td>
                  <td class="form-table__cell u-width-70px"></td>
                </tr>

            </table>

          </div>
        </div>
      </div>
    </div>
  </div>

</section>
