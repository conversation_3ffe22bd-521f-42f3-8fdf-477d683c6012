import { first } from 'rxjs/operators';
import { forEach } from 'underscore';
import { OverlayLoaderService } from './../../../shared/services/overlay-loader.service';
import { ModalboxComponent } from 'app/shared/components/modalbox/modalbox.component';
import { AvailableInventoryResponse, InventoryOrderRequest } from './../../../app-model/inventory';
import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { InventoryList, InventoryStatus } from 'app/app-model/Inventory-list';
import { RmvService } from 'app/dashboard/app-services/rmv.service';
import { ActivatedRoute } from '@angular/router';
import { PLATE_STATUS } from 'app/dashboard/app-services/get-ready-dropdown';
import { FileDownloaderService } from 'app/shared/services/file-downloader.service';
import { ApiService } from 'app/shared/services/api.service';
import { saveAs } from 'file-saver';
import { InventoryOrderDetail } from 'app/inventory-confirm';

@Component({
    selector: 'app-inventory-order',
    templateUrl: './inventory-order.component.html',
    styleUrls: ['./inventory-order.component.scss'],
    encapsulation: ViewEncapsulation.None,
    standalone: false
})
export class InventoryOrderComponent implements OnInit {
  updateSuccess: any;
  updateStatusMessage: any;
  isQuantityInventory: any;
  detailedSelectedItem: any;
  selectedTypeDescription: any;
  selectedReasonOption: any;
  selectedPlateStatusOption: any;

constructor(private rmvService: RmvService, private activatedRoute: ActivatedRoute,
            private loader: OverlayLoaderService, private fileDownloadService: FileDownloaderService,
            private apiService: ApiService) { }
list: InventoryList;
orderOptions = [];
orderOptions2 = [];
selectedItems = [];
quantity;
paginationCurrentPage = 1;
size;
limit;
paginationResultShowFrom = 0;
errorMessages;
isEvrFull = false;
year = new Date().getFullYear();
status: InventoryStatus = {
    inventoryID: '',
    inventoryType: '',
    numberOfUnits: undefined,
    newInventoryStatus: ''

};
plateStatusOptions = [
  {id: 'DAMGED', text: 'Damaged'},
  {id: 'MISSNG', text: 'Missing'},
  {id: 'RETURN', text: 'EVR Returned'}
];
inventoryReasonCodeOptions = [
  {id: 'DAMAGED', text: 'Damaged'},
  {id: 'MISSING', text: 'Missing'},
  {id: 'RETURN_DAMAGED', text: 'Return Damaged'},
  {id: 'RETURN_EXPIRED', text: 'Return Expired'}
];
inventoryTypeOptions = [
  {id: 'CMCOMM', text: 'Commercial Plate'},
  {id: 'LVNMPL', text: 'Livery Normal Plate'},
  {id: 'MCMCYL', text: 'Motorcycle Normal Plate'},
  {id: 'PANPL', text: 'Passenger Normal Red Plate'},
  {id: 'SPETPL', text: 'Electric Vehicle Plate'},
  {id: 'TRTRLR', text: 'Trailer Plate'},

];

@ViewChild('orderConfirm') orderBox: ModalboxComponent;
@ViewChild('updateConfirm') updateBox: ModalboxComponent;
@ViewChild('orderError') error: ModalboxComponent;
@ViewChild('updateStatusBox') statusBox: ModalboxComponent;

err;
  ngOnInit(): void {
    this.getAvailableInventoryItems();
    this.getInventoryList();

    this.rmvService.checkEvrLiteEligibility('PANPL').subscribe(x => this.isEvrFull = x.isEvrFullEligible);
    this.isQuantityInventory = JSON.parse(localStorage.getItem('features')).find(x => x.name === 'SprApp_QuantityInventory');
    if (this.isQuantityInventory) {
      this.inventoryTypeOptions.push( {id: `Decals${this.year - 1}`, text: `Decals (${this.year - 1})`},
      {id: `Decals${this.year}`, text: `Decals (${this.year})`},
      {id: `Decals${this.year + 1}`, text: `Decals (${this.year + 1})`},
      {id: `Decals${this.year + 2}`, text: `Decals (${this.year + 2})`});
    }
  }

  orderItems() {
    const order =  [];

      order.push({inventoryType: this.detailedSelectedItem.rmvIdentifier, numberOfUnits: this.quantity});

    this.loader.showLoader();
    this.rmvService.orderInventory(order).subscribe(x => {
      this.selectedItems = [];
      if (x.success) {
        this.rmvService.getInventoryOrderList().subscribe(x => {this.list = x; this.selectedItems[0] = {}; this.quantity = null; this.detailedSelectedItem = null;  });
      } else {
          this.errorMessages = x.messages;
          this.error.open();
        }
        this.loader.hideLoader();
          this.orderBox.close();
      },

  err => {this.loader.hideLoader(); alert('There was a problem processing the order. Please try again'); });

  }

  resetStatus() {
   this.status = {
      inventoryID: '',
      inventoryType: '',
      numberOfUnits: undefined,
      newInventoryStatus: '',
      transactionDesc: '',
      reasonCode: ''


  };
  }

  getSelectedItem() {
    const selectedItem = this.orderOptions.find(x => x.inventoryItemDescription === this.selectedItems[0]);
    this.detailedSelectedItem = selectedItem;
  }


  getSelectedTypeDescription() {
    this.selectedTypeDescription = this.inventoryTypeOptions.find(x => x.id === this.status.inventoryType).text;
  }

  getSelectedReasonOption() {
    this.selectedReasonOption = this.inventoryReasonCodeOptions.find(x => x.id === this.status.reasonCode).text;
  }

  getSelectedPlateStatusOption() {
    this.selectedPlateStatusOption = this.plateStatusOptions.find(x => x.id === this.status.newInventoryStatus).text;
  }

  getInventoryList() {
    this.activatedRoute.data.subscribe((x) => {this.list = x.inventoryResolver.orderList; });
  }

  getAvailableInventoryItems() {
    this.activatedRoute.data.subscribe((x) => {
      this.orderOptions =
      x.inventoryResolver.availableItems.availableInventoryItems;
    x.inventoryResolver.availableItems.availableInventoryItems.forEach(x => this.orderOptions2.push(x.inventoryItemDescription));
    });


  }

  getInventoryCount(row: InventoryOrderDetail, type) {
   const invItem = row.inventoryItems.find(x => x.inventoryType === type);
   return invItem ? +invItem.numberOfItems * +invItem.numberOfUnits : '';
  }

  confirmOrder(key) {
    const request = {
      atlasInventoryOrderKey : key
    };
    this.loader.showLoader();
    this.rmvService.confirmInventoryOrder(request).subscribe(x => {
      this.loader.hideLoader();
      if (x.success) {
        const order = this.list.inventoryOrders.findIndex(o => o.inventoryOrderDetails.inventoryOrderSummary.atlasInventoryOrderKey === key);
          this.list.inventoryOrders[order].inventoryOrderDetails = x.inventoryOrderDetails;
    } else {
        this.errorMessages = x.messages;
          this.error.open();
    }

        }, err => {alert('there was a problem processing the order, please try again'); this.loader.hideLoader(); });
  }

  public paginationPageChange(data) {
    if (data.startAt < 0 || Object.is(NaN, data.startAt)) {
      data.startAt = 0;
      data.pageNumber = 1;
    }
    if (this.paginationResultShowFrom !== data.startAt) {
      this.paginationCurrentPage = data.pageNumber;
      this.paginationResultShowFrom = data.startAt;

    }
  }

  getMaxQuantity() {
    const max = this.selectedItems[0]?.maxAllowableOrder;
    return Array.from({length: max}, (value, index) => index + 1);


  }

  getInventoryStatus() {
    switch (this.status.reasonCode) {
      case 'DAMAGED':
        return 'DAMGED';
      case 'MISSING':
        return 'MISSNG';
      case 'RETURN_DAMAGED':
        return 'DAMGEDEVRRTN';
      case 'RETURN_EXPIRED':
        return 'EVRRTN';
    }
  }

  updateStatus() {
    if (this.status.inventoryType.includes('Decals')) {
      this.status.inventoryID = '',
      this.status.newInventoryStatus = this.getInventoryStatus();
      this.status.transactionDesc = `Adjustment - ${this.status.reasonCode}`;
      this.status.numberOfUnits = Math.abs(this.status.numberOfUnits);
      this.loader.showLoader();
      this.rmvService.updateQuantityInventoryStatus(this.status).subscribe(x => {
           this.updateSuccess = x.success;
           this.loader.hideLoader();
           this.updateStatusMessage = x.messages;
           this.status = null;
           this.statusBox.open();

          this.resetStatus();
      });
    } else {
      const order = this.list.inventoryOrders.find(order =>
        order.inventoryOrderDetails.inventoryItems.some(item => item?.inventoryID === this.status.inventoryID?.toUpperCase())
      );

      const item = order?.inventoryOrderDetails.inventoryItems.find(item => item.inventoryID === this.status.inventoryID.toUpperCase());
      this.status.transactionDesc = '';
      this.status.reasonCode = '';
      this.status.numberOfUnits = 1;
      this.status.inventoryID = this.status.inventoryID?.toUpperCase();

      if (item && this.status.newInventoryStatus === 'RETURN' && item?.inventoryStatus !== 'DAMGED' ) {
        this.updateStatusMessage = [];
        this.updateStatusMessage.push(`Cannot update ${item.inventoryID}. Status must be marked as Damaged first`);
        this.statusBox.open();
      } else {
       this.loader.showLoader();
       this.rmvService.updateInventoryStatus(this.status).subscribe(x => {
            this.updateSuccess = x.success;
            const updatedData = x.inventoryItem;
            this.updateItem(updatedData);
            this.loader.hideLoader();
            this.updateStatusMessage = x.messages;
            this.statusBox.open();



           this.resetStatus();
       });
    }
    }
    this.updateBox.close();

  }


  updateItem(data) {
    const orderIndex = this.list.inventoryOrders.findIndex(order =>
      order.inventoryOrderDetails.inventoryItems.some(item => item?.inventoryID === this.status?.inventoryID)
    );
    if (orderIndex > -1) {
    const itemIndex = this.list.inventoryOrders[orderIndex].inventoryOrderDetails.inventoryItems.findIndex(
      item => item.inventoryID === this.status.inventoryID
    );

    if (itemIndex > -1 && data != null) {
      this.list.inventoryOrders[orderIndex].inventoryOrderDetails.inventoryItems[itemIndex] = data;
    }
  }


  }

  transformToUppercase() {
    if (this.status && this.status.inventoryID) {
      this.status.inventoryID = this.status.inventoryID.toUpperCase();
    }
  }


  downloadInventoryReport() {
    this.loader.showLoader();
    this.rmvService.getInventoryReport('').subscribe( x => {
      this.loader.hideLoader();
      const url = `/rmv/printdocument`;
      this.fileDownloadService.downloadFileFromPost(this.apiService.url(url), x.attachmentData).subscribe(res => {
        saveAs(res, `InventoryReport.pdf`);
    });
  });
}

downloadSummaryInventoryReport() {
  this.loader.showLoader();
  this.rmvService.getSummaryInventoryReport('').subscribe( x => {
    this.loader.hideLoader();
    const url = `/rmv/printdocument`;
    this.fileDownloadService.downloadFileFromPost(this.apiService.url(url), x.attachmentData).subscribe(res => {
      saveAs(res, `SummaryInventoryReport.pdf`);
  });
});
}

disableUpdate() {
  if (!this.status.inventoryType) {
    return true;
  }

  if (this.status.inventoryType.includes('Decals')) {
    return !this.status.numberOfUnits || !this.status.reasonCode;
  } else {
    return !this.status.inventoryID || !this.status.newInventoryStatus;
  }


}




}
