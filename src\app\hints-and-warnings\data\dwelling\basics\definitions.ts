import { StorageService } from './../../../../shared/services/storage-new.service';
import { WARNING_GROUPS, WarningDefinitionI, AdditionalDataI, AdditionalDataHomeBasicsCoverages} from 'app/hints-and-warnings/model/warnings';
import { CoverageStandard } from 'app/app-model/basics';
import { CoverageForVehicle } from 'app/app-model/coverage';
import { Validate } from 'app/hints-and-warnings/validators';

function requiredField(value: any): boolean {
  return Validate.isEmptyValue(value);
}
function generateViewUrl(fullObj: CoverageStandard): string {
  return  '/dashboard/dwelling/quotes/' + fullObj['vehicleResourceId'] + '/basics';
}
function generateViewFieldId(fullObj: CoverageForVehicle): string {
  let fieldId = '';
  fieldId = 'field_' + fullObj.coverageCode;
  return fieldId;
}
function generateLabel(fullObj: CoverageForVehicle): string {
  let fieldName: string = '';
  switch (fullObj.coverageCode) {
    case 'DWELL':
      fieldName = 'Dwelling';
      break;
  }
  return 'Required Basics ' + fieldName + '.';
}

function generateViewUrlPL(fullObj):string {
  return  '/dashboard/dwelling/quotes/' + fullObj['vehicleResourceId'] + '/dwelling';
}

const validatePL = (value,fullobj,data) => {
  const storageService = new StorageService()
  let returnValue
  storageService.getStorageData('dwelling').subscribe(x => {
    let nonOwner = x.nonOwnerOccupancyInd
    returnValue = value.toLowerCase() !== 'none' && nonOwner === false ? true : false
  })
  return returnValue
}


function validateWindHail(value: string, fullObj, data: AdditionalDataHomeBasicsCoverages): boolean {
  let isInvalid: boolean = false;
  let windHailCurrentValueIsPercent: boolean = false;
  let windHailValue: number = 0;
  let windHailCalculatedValue: number = 0;
  let allPerilsValue: number = 0;
  let dwellingValue: number = 0;

  let dwellingCoverage = data.observedData.find(el => el.coverageCode === 'DWELL');
  let allPerilsCoverage = data.observedData.find(el => el.coverageCode === 'APDED');

  if (dwellingCoverage && dwellingCoverage.value) {
    let tmpVal = parseInt(dwellingCoverage.value, 10);
    dwellingValue = !isNaN(tmpVal) ? tmpVal : 0;
  }

  if (allPerilsCoverage && allPerilsCoverage.value) {
    let tmpVal = parseInt(allPerilsCoverage.value, 10);
    allPerilsValue = !isNaN(tmpVal) ? tmpVal : 0;
  }

  if (value === 'None') {
    isInvalid = false;
    return false;
  } else if (value && value.indexOf('%') !== -1) {
    windHailCurrentValueIsPercent = true;
    windHailValue = parseInt(value.replace('%', '').trim(), 10);
    windHailCalculatedValue = !isNaN(windHailValue) ? dwellingValue * (windHailValue / 100) : 0;
  } else {
    windHailValue = parseInt(value, 10);
    windHailCalculatedValue = !isNaN(windHailValue) ? windHailValue : 0;
  }

  if (windHailCalculatedValue <= allPerilsValue) {
    isInvalid = true;
  }

  // If value for Dwelling field is not set, mark Wind / Hail as valid
  if (windHailCurrentValueIsPercent && !dwellingValue) {
    isInvalid = false;
  }

  return isInvalid;
}

/**
 * Validation for basics Data
 * For Model: Coverage Standard
 */

const parsedBasicsCoverageDwelling: WarningDefinitionI = {
  id: 'value',
  deepId: 'value',
  viewUri: generateViewUrl,
  viewFieldId: generateViewFieldId,
  warnings: [{
    label: (value, fullObj) => {
      return generateLabel(fullObj);
    },
    condition: (value, fullObj, data: AdditionalDataHomeBasicsCoverages) => {
      return (fullObj.coverageCode === 'DWELL') && requiredField(fullObj.value);
    },
    group: WARNING_GROUPS.general,
    carriers: []
  },
  {
    label: 'Wind / Hail value can not be less or equal to All Perils value.',
    condition: (value, fullObj, data: AdditionalDataHomeBasicsCoverages) => {
      if (fullObj.coverageCode === 'WHDED') {
        return validateWindHail(value, fullObj, data);
      }

      return false;
    },
    group: WARNING_GROUPS.general,
    carriers: []
  },

]
};

const parsedBasicsCoverageDwellingPL: WarningDefinitionI = {
  id: 'value',
  deepId: 'value',
  viewUri: generateViewUrlPL,
  viewFieldId: generateViewFieldId,
  warnings: [
  {
    label: 'Non Owner Occupancy must be selected when adding Personal Liability.',
    condition: (value, fullObj, data: AdditionalDataHomeBasicsCoverages) => {
      if (fullObj.coverageCode === 'PL') {
        return validatePL(value, fullObj, data);
      }

      return false;
    },
    group: WARNING_GROUPS.carrier,
    carriers: ['201']
  },

]
};

export const WARNINGS_DEFINITIONS_DWELLING_BASICS: WarningDefinitionI[] = [
  parsedBasicsCoverageDwelling,
  parsedBasicsCoverageDwellingPL
];
