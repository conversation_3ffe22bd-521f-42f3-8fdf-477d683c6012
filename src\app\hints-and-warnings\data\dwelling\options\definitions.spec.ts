import {
    generateCarriers,
    generateLabels,
    generateViewFieldIds,
    generateViewURIs,
    runConditions,
} from 'testing/helpers/warning-definitions';

import { CoverageItemParsed } from '../../../../app-model/coverage';
import { WARNINGS_DEFINITIONS_DWELLING_OPTIONS_CARRIER_OPTIONS } from './definitions';
import { WARNINGS_DEFINITIONS_DWELLING_OPTIONS_GENERAL_OPTIONS } from './definitions';

describe('Definitions: Dwelling Options', () => {
    describe('when dwelling options general validator is used', () => {
        let definitions: any[];
        let options: CoverageItemParsed;

        beforeEach(() => {
          definitions = WARNINGS_DEFINITIONS_DWELLING_OPTIONS_GENERAL_OPTIONS;
            options = Object.assign(new CoverageItemParsed(), {
                isRequired: true,
                isDisabled: false,
                isActive: false,
                currentValue: '',
                alreadySelected: false,
                endpointUrl: '',
                isNewFromAPI: false,
                carrierNotes: '',
                coverageCode: '',
                coverageGroup: '',
                defaultValue: '',
                description: '',
                inputType: '',
                meta: {
                    href: ''
                },
                ratingPlanId: '',
                ratingPlanName: '',
                resourceName: '',
                values: [],
                quoteResourceId: '',
                ratingCarrierId: 0
            });
        });

        it('allows to check if required fields are provided', () => {
            const errors = runConditions(definitions, options, {});

            expect(errors).toEqual(['currentValue']);
        });

        it('generates viewFieldIds without errors', () => {
            expect(() => {
                generateViewFieldIds(definitions, options);
            }).not.toThrow();
        });

        it('generates viewURIs without errors', () => {
            expect(() => {
                generateViewURIs(definitions, options);
            }).not.toThrow();
        });

        it('generates labels without errors', () => {
            expect(() => {
                generateLabels(definitions, options);
            }).not.toThrow();
        });

        it('generates carriers without errors', () => {
            expect(() => {
                generateCarriers(definitions, options);
            }).not.toThrow();
        });
    });

    describe('when dwelling options carrier validator is used', () => {
        let definitions: any[];
        let options: CoverageItemParsed;

        beforeEach(() => {
          definitions = WARNINGS_DEFINITIONS_DWELLING_OPTIONS_CARRIER_OPTIONS;
            options = Object.assign(new CoverageItemParsed(), {
                isRequired: true,
                isDisabled: false,
                isActive: false,
                currentValue: '',
                alreadySelected: false,
                endpointUrl: '',
                isNewFromAPI: false,
                carrierNotes: '',
                coverageCode: '',
                coverageGroup: '',
                defaultValue: '',
                description: '',
                inputType: '',
                meta: {
                    href: ''
                },
                ratingPlanId: '',
                ratingPlanName: '',
                resourceName: '',
                values: [],
                quoteResourceId: '',
                ratingCarrierId: 0
            });
        });

        it('allows to check if required fields are provided', () => {
            const errors = runConditions(definitions, options, {});

            expect(errors).toEqual(['currentValue']);
        });

        it('generates viewFieldIds without errors', () => {
            expect(() => {
                generateViewFieldIds(definitions, options);
            }).not.toThrow();
        });

        it('generates viewURIs without errors', () => {
            expect(() => {
                generateViewURIs(definitions, options);
            }).not.toThrow();
        });

        it('generates labels without errors', () => {
            expect(() => {
                generateLabels(definitions, options);
            }).not.toThrow();
        });

        it('generates carriers without errors', () => {
            expect(() => {
                generateCarriers(definitions, options);
            }).not.toThrow();
        });
    });
});
