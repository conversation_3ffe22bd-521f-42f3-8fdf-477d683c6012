<section class="section section--thick" [positionFixed]="'absolute'">
  <div class="u-flex u-flex--spread">
    <div class="u-spacing--left-2-5">
      <app-filter [name]="'Sort By'" [options]="filterOptions" [selectedOption]="currentSortTypeOption" (onChange)="onFilterChange($event)" ></app-filter>
    </div>
    <div class="u-position--relative u-flex u-flex--row">
      <div class="u-padd--right-4">
        <!--
        <button class="o-btn o-btn--action o-btn--i_edit">
          Override Restrictions
        </button>
      -->
        <app-home-override-restrictions [isHome]=true></app-home-override-restrictions>
      </div>
      <div class="u-position--relative">
        <a class="o-btn" (click)="rateAll()">Rate All Plans</a>
        <app-loader [loading]="loadingAll"></app-loader>
      </div>
    </div>
  </div>
</section>

<section class="section section--compact">
  <div class="row">
    <div class="col-xs-12">
      <table class="table table--fixed table--wide-right table--hoverable-grey u-color-raven table--td-height-md">
        <tbody class="table_tbody">
          <tr [id]="'quotePlanId_' + plan?.ratingPlanId"  class="table__tr table__tr--premium" *ngFor="let plan of quotePlanObject?.items">

            <td class="table__td u-align-center u-width-180px"
              ngClass="(plan.error && plan.rate && plan.rate.items &&  plan.rate.items.length && !plan.rate.items[0].rateRequestId) ? 'u-align-v-middle' : 'u-align-v-top'">

              <div *ngIf="!plan.review && quotePlanObject && quotePlanObject.meta && quotePlanObject.meta.href">
                <button (click)="rate(quotePlanObject.meta.href + '/rateResponses/' + plan.ratingPlanId, plan)"
                  class="o-btn o-btn--small-fixed u-width-60px u-t-weight--bold"
                  *ngIf="!plan.rate && !plan.error && !plan.rerate">Rate</button>

                <button (click)="rate(quotePlanObject.meta.href + '/rateResponses/' + plan.ratingPlanId, plan)"
                  class="o-btn o-btn--small-fixed u-width-60px u-t-weight--bold"
                  *ngIf="plan.rerate" title="{{ quotePlanObject.meta.href + '/rateResponses/' + plan.ratingPlanId }}">Re rate</button>

                <div *ngIf="planStatusIsError(plan)" class="u-t-upper u-color-app-auto">
                  <button (click)="rate(quotePlanObject.meta.href + '/rateResponses/' + plan.ratingPlanId, plan)"
                    class="o-btn o-btn--small-fixed o-btn--error u-width-60px u-t-weight--bold">Error</button>
                </div>

                <div *ngIf="planStatusIsDeclined(plan)" class="u-t-upper u-color-orange">
                  <button (click)="rate(quotePlanObject.meta.href + '/rateResponses/' + plan.ratingPlanId, plan)"
                  class="o-btn o-btn--small-fixed o-btn--declined u-width-70px u-t-weight--bold">Declined</button>
                </div>

                <div class="o-tag o-tag--price" *ngIf="plan.rate && plan.rate.items && plan.rate.items.length && !planStatusIsError(plan) && !planStatusIsDeclined(plan)">
                  {{ premiumsService.displayRates(plan.rate.items) }}
                </div>
              </div>

              <button (click)="showWarnings(plan)"
                class="o-btn o-btn--small-fixed u-width-60px u-t-weight--bold o-btn--orange js-modal-review"
                *ngIf="plan.review">Review</button>

              <app-loader [loading]="plan.loading"></app-loader>
            </td>
            <td class="table__td">
              <div class="u-t-size--1-4rem u-color-black-pearl">{{plan.name}}</div>
              <div class="u-t-size--1-1rem u-spacing--0-5 u-color-pelorous" *ngIf="plan.error || (plan.rate && plan.rate.items && plan.rate.items.length && plan.rate?.items[0].msgStatusDesc)">

                <span *ngIf="planStatusIsAuthError(plan); then linkText else msgText"></span>

                <ng-template #linkText>

                  <div class="row">
                    <div class="col-xs-8">
                      {{plan.error || plan.rate?.items[0].msgStatusDesc}}
                    </div>
                    <div class="col-xs-2 text-center" style="margin-top:-2rem;">
                      <a class="o-btn o-btn--i_info" (click)="PlanAuthErrorHelpLink(plan)"  *ngIf="showBtnPlanAuthError(plan)">Fix this Error</a>
                    </div>
                    <div class="col-xs-2 text-center" style="margin-top:-2rem;  float: right;">
                      <button class="o-btn" [crossAppRouterLink]="'/User/CarrierCredentials/' + agentId" target="_blank">Edit Credentials</button>
                    </div>
                  </div>

                </ng-template>

                <ng-template #msgText>
                  <span [innerHTML]="plan.error || plan.rate?.items[0].msgStatusDesc"></span>
                </ng-template>

              </div>
            </td>
            <td class="table__td u-width-100px">
              <div class="table__show-when-tr-hover">
                <button class="o-btn o-btn--action o-btn--i_info js-modal-tip-btn" *ngIf="plan.tips">Tip</button>
              </div>
            </td>
            <td class="table__td u-align-right u-width-205px">
                <button (click)="goToSummary(plan)"
                *ngIf="showBtnViewPlanSummary(plan)"
                class="o-btn o-btn--outlined o-btn--i_search-plus">

                <span *ngIf="(planStatusIsError(plan) || planStatusIsDeclined(plan)); then review else view"></span>

                  <ng-template #review>
                    Review Error
                  </ng-template>
                  <ng-template #view>
                    View plan summary
                  </ng-template>

                </button>
            </td>
          </tr>
        </tbody>
      </table>

    </div>
  </div>
</section>

<!-- PREMIUMS WARNINGS MODAL -->
<!-- [css]="'centered'" [backdropCss]="'not-visible'" -->
<app-modalbox #refModalWarningsPremiums [launcher]="'.js-modal-review'">
  <div class="u-flex u-flex--spread">
    <div class="">
      <h2 class="o-heading o-heading--red u-show-iblock u-align-v-middle u-spacing--right-1">Review warnings: <span class="o-heading--notransform">{{ reviewingPlan?.name }}</span></h2>
    </div>
  </div>

  <div class="box box--silver u-spacing--1-5">
    <!-- <p-scrollPanel styleClass="u-height-max-215px"> -->
      <div *ngIf="generalWarnings && generalWarnings.length">
        <p class="u-spacing--bottom-1">This plan cannot be rated because:</p>
        <ul class="list list--nowrap u-color-pelorous u-spacing--left-3-5">
          <li (click)="focusWarnElement(item.elementId, refModalWarningsPremiums)" class="list__item" *ngFor="let item of generalWarnings; let i = index">
            <!-- <app-warning-link [css]="'warnings-panel__link'" [openPanel]="true" [warningItem]="item"></app-warning-link> -->
            <app-hints-and-warnings-link [css]="'warnings-panel__link'" [openPanel]="true" [warningItem]="item"></app-hints-and-warnings-link>
          </li>
        </ul>
      </div>
      <div *ngIf="reviewingPlan && reviewingPlan.carrierWarnings && reviewingPlan.carrierWarnings.length">
        <p class="u-spacing--1 u-spacing--bottom-1">{{ reviewingPlan?.name }} requires:</p>
        <ul class="list list--nowrap u-color-pelorous u-spacing--left-3-5">
          <li (click)="focusWarnElement(item.elementId, refModalWarningsPremiums)" class="list__item" *ngFor="let item of reviewingPlan.carrierWarnings; let i = index">
            <!-- <app-warning-link [css]="'warnings-panel__link'" [openPanel]="true" [warningItem]="item"></app-warning-link> -->
            <app-hints-and-warnings-link [css]="'warnings-panel__link'" [openPanel]="true" [warningItem]="item"></app-hints-and-warnings-link>
          </li>
        </ul>
      </div>
    <!-- </p-scrollPanel> -->
  </div>

  <div class="row u-spacing--2">
    <div class="col-xs-12 u-align-right">
      <button (click)="resolveWarnings(generalWarnings, reviewingPlan, refModalWarningsPremiums)" class="o-btn">Resolve Warnings</button>
      <button (click)="refModalWarningsPremiums.closeModalbox()" class="o-btn o-btn--idle u-spacing--left-1-5">Close</button>
    </div>
  </div>
</app-modalbox>

