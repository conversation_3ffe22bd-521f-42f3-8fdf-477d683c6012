
import {take} from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { Observable, BehaviorSubject } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ApiService } from 'app/shared/services/api.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import {
  ClientDetails,
  ClientAddress,
  ClientContactMethod,
  ClientDetailsApiResponse,
  ClientListItemApiResponse,
  ClientBusinessDetails
} from 'app/app-model/client';
import { StorageService } from 'app/shared/services/storage-new.service';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { LeaveQuoteService } from 'app/shared/services/leave-quote.service';
import { Validate } from 'app/hints-and-warnings/validators';

interface IContactMethods {
  home: string;
  work: string;
  mobile: string;
  email: string;
  fax: string;
}

interface IAddresses {
  current: string;
  prior: string;
}

interface IClientTypes {
  commercial: string;
  personal: string;
}

export const ADDRESS_CURRENT = 'StreetAddress';
export const ADDRESS_PRIOR = 'PreviousAddress';
export const ADDRESSES_TO_CREATE: string[] = [ADDRESS_CURRENT, ADDRESS_PRIOR];

export const ADDRESSES: IAddresses = {
  current: ADDRESS_CURRENT,
  prior: ADDRESS_PRIOR
};

export const CONTACT_HOME = 'HomePhone';
export const CONTACT_WORK = 'BusinessPhone';
export const CONTACT_MOBILE = 'MobilePhone';
export const CONTACT_EMAIL = 'Email';
export const CONTACT_FAX = 'Fax';
export const CONTACTS_TO_CREATE: string[] = [
  CONTACT_HOME,
  CONTACT_WORK,
  CONTACT_MOBILE,
  CONTACT_EMAIL,
  CONTACT_FAX
];

export const CONTACT_METHODS: IContactMethods = {
  home: CONTACT_HOME,
  work: CONTACT_WORK,
  mobile: CONTACT_MOBILE,
  email: CONTACT_EMAIL,
  fax: CONTACT_FAX
};

export const CLIENT_TYPES: IClientTypes = {
  commercial: 'commercial',
  personal: 'personal'
};


@Injectable()
export class ClientsService {
  constructor(
    private http: HttpClient,
    private apiService: ApiService,
    private apiCommonService: ApiCommonService,
    private storageService: StorageService,
    private overlayLoaderService: OverlayLoaderService,
    private leaveQuoteService: LeaveQuoteService
  ) { }

  // Create Client
  public createQuoteClient(quoteID: string, clientData = {}): Observable<any> {
    const uri = '/quotes/' + quoteID + '/clients';
    return this.apiCommonService.postByUri(uri, clientData);
  }

  public AttachClient(
    quoteID: string,
    clientId: number,
    clientData = {}
  ): Observable<any> {
    const uri = '/quotes/' + quoteID + '/attachclient/' + clientId;
    return this.apiCommonService.putByUri(uri, clientData);
  }

  public createClient(clientData = {}): Observable<any> {
    const uri = '/clients';
    return this.apiCommonService.postByUri(uri, clientData);
  }

  // Update Client
  public updateClient(
    quoteID: string,
    clientID: string,
    clientData
  ): Observable<any> {
    const uri = '/quotes/' + quoteID + '/clients/' + clientID;
    return this.apiCommonService.putByUri(uri, clientData);
  }

  // Get Client by ID
  public getClient(quoteID: string, clientID: string): Observable<any> {
    let uri;

    if (quoteID) {
      uri = '/quotes/' + quoteID + '/clients/' + clientID;
    } else if (clientID) {
      uri = '/clients/' + clientID;
    }
    return this.apiCommonService.getByUri(uri);
  }

  // Get Clients List
  public getClientsList(quoteID: string): Observable<ClientDetailsApiResponse> {
    const uri = '/quotes/' + quoteID + '/clients';
    console.log('GET QUOTE CLIENTS');
    return this.apiCommonService.getByUri(uri);
  }

  public getClients(params): Observable<ClientListItemApiResponse> {
    let uri = '/clients';
      Object.keys(params).forEach((key, index) => {
        if (index === 0) {
          uri += `?${key}=${params[key]}`;
        } else {
          uri += `&${key}=${params[key]}`;
        }
      });
       return this.apiCommonService.getByUri(uri);
  }

  // Client Contact Methods
  // -----------------------------------------------------------------------

  // Create contact methods
  public createContactMethod(
    clientId: string,
    contactData: any = {}
  ): Observable<any> {
    const uri = '/quoteclients/' + clientId + '/contactMethods';
    return this.apiCommonService.postByUri(uri, contactData);
  }

  // Get contact methods COLLECTION
  public getContactMethods(clientId: string): Observable<any> {
    const uri = '/quoteclients/' + clientId + '/contactMethods/';
    return this.apiCommonService.getByUri(uri);
  }

  // Get contact method SINGLE
  public getContactMethod(
    clientId: string,
    contactMethodsId: string
  ): Observable<any> {
    const uri =
      '/quoteclients/' + clientId + '/contactMethods/' + contactMethodsId;
    return this.apiCommonService.getByUri(uri);
  }

  // Update contact method
  public updateContactMethod(
    clientId: string,
    contactMethodsId: string,
    contactData: ClientContactMethod
  ): Observable<any> {
    const uri =
      '/quoteclients/' + clientId + '/contactMethods/' + contactMethodsId;
    return this.apiCommonService.putByUri(uri, contactData);
  }

  // Delete contact method
  public deleteContactMethod(
    clientId: string,
    contactMethodsId: string
  ): Observable<any> {
    const uri =
      '/quoteclients/' + clientId + '/contactMethods/' + contactMethodsId;
    return this.apiCommonService.deleteByUri(uri);
  }

  // Client Address
  // -----------------------------------------------------------------------

  // Create address
  public createAddress(
    clientId: string,
    addressData: any = {}
  ): Observable<any> {
    const uri = '/quoteclients/' + clientId + '/addresses';
    return this.apiCommonService.postByUri(uri, addressData);
  }

  // Get addresses
  public getAddresses(clientId: string): Observable<any> {
    const uri = '/quoteclients/' + clientId + '/addresses';
    return this.apiCommonService.getByUri(uri);
  }

  public getAddress(clientId: string, addressId: string): Observable<any> {
    const uri = '/quoteclients/' + clientId + '/addresses/' + addressId;
    return this.apiCommonService.getByUri(uri);
  }

  // Update address
  public updateAddress(
    clientId: string,
    addressId: string,
    addressData: ClientAddress
  ): Observable<any> {
    const uri = '/quoteclients/' + clientId + '/addresses/' + addressId;
    return this.apiCommonService.putByUri(uri, addressData);
  }

  // Delete address
  public deleteAddress(clientId: string, addressId: string): Observable<any> {
    const uri = '/quoteclients/' + clientId + '/addresses/' + addressId;
    return this.apiCommonService.deleteByUri(uri);
  }

  // HELPERS
  // ----------------------------------------------------------------------------

  // Get Client Addresses
  // *********************
  public getClientAddressesAndCreateNotExisting(
    clientDetails: ClientDetails
  ): Promise<ClientAddress[]> {
    return this.apiCommonService
      .getByUri(clientDetails.addresses.href)
      .toPromise()
      .then(res => {
        return this.createNotExistingAddresses(clientDetails, res.items);
      });
  }

  private createNotExistingAddresses(
    clientDetails: ClientDetails,
    addresses: ClientAddress[]
  ): Promise<ClientAddress[]> {
    const addressesPromises: Promise<any>[] = [];
    const tmpAddresses = [...addresses];

    ADDRESSES_TO_CREATE.forEach(addrType => {
      const existsIndex = addresses.findIndex(
        addr => addr.addressType === addrType
      );

      if (existsIndex === -1) {
        const tmpPromise = this.handleCreatingNewAddress(
          clientDetails,
          addrType
        );
        addressesPromises.push(tmpPromise);
        tmpPromise
          .then(addr => {
            addr && tmpAddresses.push(addr);
            console.log(
              '%c Created Client Address: ',
              'background: #46eac4;color:#fff',
              addr
            );
          })
          .catch(err => console.log(err));
      }
    });

    return Promise.all(addressesPromises).then(() => tmpAddresses);
  }

  private handleCreatingNewAddress(
    clientDetails: ClientDetails,
    addressType: string
  ): Promise<void | ClientAddress> {
    const newAddress = new ClientAddress();
    newAddress.addressType = addressType;

    return this.apiCommonService
      .postByUri(clientDetails.addresses.href, newAddress)
      .toPromise()
      .then((address: ClientAddress) => {
        return address;
      })
      .catch(err => console.log('CREATING ADDRESS ERROR:', err, newAddress));
  }

  // Get Client Contact Methods
  // ***************************
  public getClientContactMethodsAndCreateNotExisting(
    clientDetails: ClientDetails
  ): Promise<ClientContactMethod[]> {
    return this.apiCommonService
      .getByUri(clientDetails.contactMethods.href)
      .toPromise()
      .then(res => {
        return this.createNotExistingContacts(clientDetails, res.items);
      });
    // .catch(err => console.log('GET (index):: CONTACT METHODS ERR:', err));
  }

  private createNotExistingContacts(
    clientDetails: ClientDetails,
    contacts: ClientContactMethod[]
  ): Promise<ClientContactMethod[]> {
    const contactPromises: Promise<any>[] = [];
    const tmpContactMethods = [...contacts];

    CONTACTS_TO_CREATE.forEach(contactType => {
      const existsIndex = contacts.findIndex(
        contact => contact.type === contactType
      );

      if (existsIndex === -1) {
        const tmpPromise = this.handleCreatingNewContactMethod(
          clientDetails,
          contactType
        );
        contactPromises.push(tmpPromise);
        tmpPromise
          .then(contact => {
            contact && tmpContactMethods.push(contact);
            console.log(
              '%c Created Contact Method: ',
              'background:#00b2ec;color:#fff',
              contact
            );
          })
          .catch(err => console.log(err));
      }
    });

    return Promise.all(contactPromises).then(() => tmpContactMethods);
  }

  private handleCreatingNewContactMethod(
    clientDetails: ClientDetails,
    contactType: string
  ): Promise<void | ClientContactMethod> {
    const newContact = new ClientContactMethod();
    newContact.type = contactType;
    newContact.preferredMethod = 'False';
    newContact.value = '';

    return this.apiCommonService
      .postByUri(clientDetails.contactMethods.href, newContact)
      .toPromise()
      .then((contactMethod: ClientContactMethod) => {
        return contactMethod;
      })
      .catch(err =>
        console.log('CREATE CONTACT METHOD ERROR:', err, newContact)
      );
  }

  // Common method for Client Data saving
  public saveClient(
    showLoaderOverlay: boolean = false,
    updateStorage: boolean = false,
    updateStorageSnapshot: boolean = false
  ): Promise<[ClientDetails, ClientAddress[], ClientContactMethod[]]> {
    let clientDetails: ClientDetails;
    let clientAddresses: ClientAddress[];
    let clientContactMethods: ClientContactMethod[];

    let updatedClientDetails: ClientDetails;
    let updatedClientAddresses: ClientAddress[];
    let updatedClientContactMethods: ClientContactMethod[];

    this.storageService
      .getStorageData('selectedClient').pipe(
      take(1))
      .subscribe((data: ClientDetails) => (clientDetails = data));
      this.storageService.getStorageData('clients').pipe(take(1)).subscribe(c => {
        if (clientDetails.firstName == '') {
          clientDetails.firstName = c[0].firstName;
          clientDetails.lastName = c[0].lastName;

        }
      })
    this.storageService
      .getStorageData('clientAddresses').pipe(
      take(1))
      .subscribe((data: ClientAddress[]) => (clientAddresses = data));

    this.storageService
      .getStorageData('clientContactMethods').pipe(
      take(1))
      .subscribe(
        (data: ClientContactMethod[]) => (clientContactMethods = data)
      );

    if (showLoaderOverlay) {
      this.overlayLoaderService.showLoader('Saving Client...');
    }

    return this.updateClientAddresses(clientAddresses)
      .then((data: ClientAddress[]) => {
        updatedClientAddresses = data;
        return this.updateClientContactMethods(clientContactMethods);
      })
      .then((data: ClientContactMethod[]) => {
        updatedClientContactMethods = data;
        return this.updateClientDetails(clientDetails);
      })
      .then((data: ClientDetails) => {
        updatedClientDetails = data;
        if (updateStorage) {
          this.storageService.setStorageData(
            'selectedClient',
            updatedClientDetails
          );
          this.storageService.setStorageData(
            'clientAddresses',
            updatedClientAddresses
          );
          this.storageService.setStorageData(
            'clientContactMethods',
            updatedClientContactMethods
          );
        }

        if (updateStorageSnapshot) {
          this.leaveQuoteService.saveStorageQuoteDataSnapshot();
        }

        if (showLoaderOverlay) {
          this.overlayLoaderService.hideLoader();
        }
        console.log(
          'Client With Addresses and Contact Methods has been updated (saved)'
        );
      })
      .then(() => {
        return <[ClientDetails, ClientAddress[], ClientContactMethod[]]>[
          updatedClientDetails,
          updatedClientAddresses,
          updatedClientContactMethods
        ];
      })
      .catch(err => {
        if (showLoaderOverlay) {
          this.overlayLoaderService.hideLoader();
        }
        return Promise.reject(err);
      });
  }

  public updateClientDetails(
    selectedClient: ClientDetails
  ): Promise<ClientDetails> {
    return this.apiCommonService
      .putByUri(selectedClient.meta.href, selectedClient)
      .toPromise()
      .then((res: ClientDetails) => {
        return res;
      }).catch(err => {
        if (err.status === 400 && err.error.message === 'Validation Failed') {
           alert(err.error.errors[0].message);
           return null;
        }
      });
  }

  private updateClientAddresses(
    addresses: ClientAddress[]
  ): Promise<ClientAddress[]> {
    const promises = [];

    addresses.forEach(addr => {
      if (addr.meta.href) {
        const tmpPromise = this.apiCommonService
          .putByUri(addr.meta.href, addr)
          .toPromise()
          .then(res => {
            return res;
          })
          .catch(err => {
            if (err.status === 400) {
              window.alert("Zip Code " +err.error.errors.Zip[0]);
            }
            return addr;
          });

        promises.push(tmpPromise);
      }
    });

    return Promise.all(promises);
  }

  private updateClientContactMethods(
    contacts: ClientContactMethod[]
  ): Promise<ClientContactMethod[]> {
    const promises = [];
    contacts.forEach(contact => {
      if (contact.meta.href) {
        const tmpPromise = this.apiCommonService
          .putByUri(contact.meta.href, contact)
          .toPromise()
          .then(res => {
            return res;
          })
          .catch(err => {
            if (err.status === 400) {
              window.alert(err.error.errors.Value);
            }
            return contact;
          });

        promises.push(tmpPromise);
      }
    });

    return Promise.all(promises);
  }

  // Validation Rules
  // ---------------------------------------------------------------------------
  // ClientDetails
  public static fieldPrimaryFirstNameIsRequired(client: ClientDetails): boolean {
    if (client.type !== CLIENT_TYPES.commercial) {
      return Validate.isEmptyValue(client.firstName);
    }

    return false;
  }

  public static fieldPrimaryLastNameIsRequired(client: ClientDetails): boolean {
    if (client.type !== CLIENT_TYPES.commercial) {
      return Validate.isEmptyValue(client.lastName);
    }

    return false;
  }

  public static fieldPrimaryDOBIsRequired(client: ClientDetails): boolean {
    return Validate.isEmptyValue(client.dob);
  }

  public static fieldSecondaryDOBIsRequired(client: ClientDetails): boolean {
    return client.secondaryFirstName
      && client.secondaryLastName
      && !client.secondaryDOB;
  }

  public static fieldBusinessNameIsRequired(client: ClientDetails): boolean {
    if (client.type === CLIENT_TYPES.commercial) {
      return Validate.isEmptyValue(client.business.name);
    }

    return false;
  }

  public static fieldLegalEntityIsRequired(client: ClientDetails): boolean {
    if (client.type === CLIENT_TYPES.commercial) {
      return Validate.isEmptyValue(client.business.legalEntity);
    }

    return false;
  }

  // ---------------------------------------------------------------------------
  private _clientDetailsHasError = false;
  public clientDetailsHasError: BehaviorSubject<boolean> = new BehaviorSubject(this._clientDetailsHasError);
  // public clientDetailsHasError: BehaviorSubject<boolean> = new BehaviorSubject(false);
  public setClientDetailsHasError(val: boolean): void {
    this._clientDetailsHasError = val;
    this.clientDetailsHasError.next(this._clientDetailsHasError);
  }


  public verifyIfClientInfoDetailsAreValid(): Promise<boolean> {
    return new Promise((resolve) => {
      let clientDetails: ClientDetails;
      let commercialClientIsValid = false;
      let personalClientIsValid = false;

      this.storageService
        .getStorageData('selectedClient').pipe(
        take(1))
        .subscribe((data: ClientDetails) => (clientDetails = data));

      if (clientDetails.type === CLIENT_TYPES.commercial) {
        commercialClientIsValid = !ClientsService.fieldBusinessNameIsRequired(clientDetails)
          && !ClientsService.fieldLegalEntityIsRequired(clientDetails);
      }

      if (clientDetails.type === CLIENT_TYPES.personal) {
        personalClientIsValid = !ClientsService.fieldPrimaryFirstNameIsRequired(clientDetails)
          && !ClientsService.fieldPrimaryLastNameIsRequired(clientDetails);
      }


      const result: boolean = (commercialClientIsValid || personalClientIsValid);
      this.setClientDetailsHasError(!result);

      resolve(result);
    });
  }

  public adjustClientDetails(client: ClientDetails): Promise<ClientDetails> {
    return new Promise((resolve, reject) => {
      let tmpClientType: string = CLIENT_TYPES.personal;
      // client = data;
      if (!client.business) {
        client.business = new ClientBusinessDetails();
      }

      if (client.type) {
        tmpClientType = client.type;
      } else if (client.business && client.business.legalEntity && client.business.legalEntity.toLowerCase() !== 'none') {
        tmpClientType = CLIENT_TYPES.commercial;
           }

      if (!client.type) {
        client.type = tmpClientType;
      }

      // https://bostonsoftware.atlassian.net/browse/SPR-2887 - if 'null' as string, set it as null
      if (client && client.customerNumber && client.customerNumber.toLowerCase() === 'null') {
        client.customerNumber = null;
      }

      resolve(client);
    });
  }
}
