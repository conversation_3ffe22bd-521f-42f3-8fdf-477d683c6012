<section class="section section--compact u-spacing--2-5">
  <div class="row">
    <h1 class="o-heading" style="color:#0b71ac; padding-left: 20px;">Lessor</h1>
  </div>
  <div class="row u-spacing--1">
    <div class="col-xs-12">
      <div class="box box--silver">
        <table class="form-table">
          <tr class="form-table__row">
            <td class="form-table__cell u-width-80px">
              <label for="">Lessor:</label>
            </td>
            <td class="form-table__cell u-width-200px">
              <input [(ngModel)]="name" name="name" placeholder="Name" required disabled (keyup.enter)="onChangeName()"
                (keydown.tab)="onChangeName()">
            </td>
            <td class="form-table__cell u-width-10px">or</td>
            <td class="form-table__cell u-width-160px">
              <input [(ngModel)]="fid" name="fid" placeholder="Lessor FID" required disabled (keyup.enter)="onChangeFid()"
                (keydown.tab)="onChangeFid()">
            </td>
            <td class="form-table__cell u-width-160px" style="color: #1989C9;"><a id="lessorLookup-modal"> Look up
                Lessor</a></td>
          </tr>
        </table>
      </div>
    </div>
  </div>
</section>
<app-modalbox #modalLessorLookup [launcher]="'#lessorLookup-modal'" [css]="'u-width-850px modal-margin'">
  <app-lessor-lookup [modalBox]="modalLessorLookup" (onSelectedLessorClick)="setSelectedLessor($event)">
  </app-lessor-lookup>
</app-modalbox>
