import { LookupsService } from '../../app-services/lookups.service';
import { PlansService } from '../../app-services/plans.service';
import { SymbolsService } from '../../app-services/symbols.service';
import { MockRouterProvider } from '../../../../testing/stubs/router.provider';
import { StorageService } from '../../../shared/services/storage-new.service';
import { TestBed, inject } from '@angular/core/testing';

import { PremiumsService } from './premiums.service';

describe('PremiumsService', () => {
  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        PremiumsService,
        StorageService,
        MockRouterProvider,
        SymbolsService,
        PlansService,
        LookupsService
      ]
    });
  });

  it('should be created', inject([PremiumsService], (service: PremiumsService) => {
    expect(service).toBeTruthy();
  }));
});
