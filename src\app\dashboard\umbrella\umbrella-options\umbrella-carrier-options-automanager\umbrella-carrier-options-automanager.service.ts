
import {take} from 'rxjs/operators';
import { Injectable } from "@angular/core";
import { BehaviorSubject ,  SubscriptionLike as ISubscription } from "rxjs";

// Services
import { OptionsService } from "app/dashboard/app-services/options.service";
import { SpecsService } from "app/dashboard/app-services/specs.service";
import { StorageService } from "app/shared/services/storage-new.service";
import { SubsService } from "app/dashboard/app-services/subs.service";
import { AgencyUserService } from "app/shared/services/agency-user.service";
import { StorageGlobalService } from "app/shared/services/storage-global.service";

// Models
import {
  QuoteUmbrella,
  QuotePlan,
  QuotePlanListAPIResponse
} from "app/app-model/quote";
import {
  Coverage,
  PolicyCoveragesData,
  CoverageItem,
  CoverageItemParsed,
} from "app/app-model/coverage";
import { Dwelling } from "app/app-model/dwelling";

interface TriggerSourceI {
  triggerBy?: "SelectedPlans" | "QuoteEffectiveDate";
}

interface PromiseDataAfterSettingDefaultValuesNew {
  policiesToUpdate: CoverageItemParsed[];
  policyItemsParsed: CoverageItemParsed[];
}

interface PromiseDataAfterSavingToApi {
  status: string;
  policyItemsParsed: CoverageItemParsed[];
}

@Injectable()
export class UmbrellaCarrierOptionsAutomanagerService {
  private serviceIsInitialized: boolean = false;

  private subscriptionQuote: ISubscription;
  private subscriptionQuoteIsNew: ISubscription;
  private subscriptionSelectedPlans: ISubscription;
  // private subscriptionSelectedQuoteTypes: ISubscription;
  private subscriptionParserTrigger: ISubscription;
  private subscriptionGeneralOptionsPolicyItemHomeParsed: ISubscription;
  // private subscriptionDwellingData: ISubscription;

  private _parserTrigger: BehaviorSubject<TriggerSourceI> = new BehaviorSubject(
    {}
  );

  private quote: QuoteUmbrella;
  private quoteId: string = "";
  private quoteLob: string = "";
  // private quoteFormTypes: string[] = []; // Probably to remove
  private quoteIsNew: boolean = false;
  private selectedPlans: QuotePlan[] = [];
  private selectedPlansIds: string[] = [];
  private quoteBeforeChange: QuoteUmbrella = null;
  public dwelling: Dwelling = new Dwelling();

  // Required for updating coverages - we need all options from every view
  private generalOptionsPolicies: CoverageItemParsed[] = [];

  private preventParsingPoliciesUntilDataSavedToAPIServer = false;

  constructor(
    private storageService: StorageService,
    private agencyUserService: AgencyUserService,
    private specsService: SpecsService,
    private optionsService: OptionsService,
    private subsService: SubsService,
    private storageGlobalService: StorageGlobalService
  ) {}

  public initialize(): Promise<void> {
    if (this.serviceIsInitialized) {
      return Promise.resolve();
    }

    this.serviceIsInitialized = true;
    console.log("Initialize UmbrellaCarrierOptionsAutomanagerService");

    return Promise.all([
      this.subscribeQuote(),
      this.subscribeQuoteIsNew(),
      this.subscribeSelectedPlans(),
      this.subscribeGeneralOptionsPolicyItemHomeParsed()
    ]).then(() => {
      return this.initPolicyItemsParser();
    }).catch(err => {
      console.log(err);
    });
  }

  public destroy() {
    this.serviceIsInitialized = false;

    this.subscriptionQuote && this.subscriptionQuote.unsubscribe();
    this.subscriptionQuoteIsNew && this.subscriptionQuoteIsNew.unsubscribe();
    this.subscriptionSelectedPlans && this.subscriptionSelectedPlans.unsubscribe();
    this.subscriptionParserTrigger && this.subscriptionParserTrigger.unsubscribe();
    this.subscriptionGeneralOptionsPolicyItemHomeParsed && this.subscriptionGeneralOptionsPolicyItemHomeParsed.unsubscribe();
  }

  private subscribeQuote(): Promise<QuoteUmbrella> {
    return new Promise((resolve, reject) => {
      this.subscriptionQuote = this.storageService
        .getStorageData("selectedQuote")
        .subscribe((quote: QuoteUmbrella) => {
          this.quote = JSON.parse(JSON.stringify(quote));
          this.quoteId = quote.resourceId;
          this.quoteLob = quote.lob;

          resolve(this.quote);

          if (!this.quoteBeforeChange) {
            this.quoteBeforeChange = JSON.parse(JSON.stringify(quote));
          } else if (
            this.quoteBeforeChange.effectiveDate !== this.quote.effectiveDate
          ) {
            this.quoteBeforeChange = JSON.parse(JSON.stringify(quote));

            // INIT POLICY ITEMS PARSING After Quote Effective date change
            // Emit Observable to Parse Items
            this._parserTrigger.next({ triggerBy: "QuoteEffectiveDate" });
          }
        });
    });
  }

  private subscribeQuoteIsNew(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.subscriptionQuoteIsNew = this.storageService
        .getStorageData("isNewQuote")
        .subscribe((res: boolean) => {
          this.quoteIsNew = res;
          resolve(this.quoteIsNew);
        });
    });
  }

  private subscribeSelectedPlans(): Promise<QuotePlan[]> {
    return new Promise((resolve, reject) => {
      this.subscriptionSelectedPlans = this.storageService
        .getStorageData("selectedPlan")
        .subscribe((res: QuotePlanListAPIResponse) => {
          if (res && res.items && res.items.length) {
            this.selectedPlans = JSON.parse(JSON.stringify(res.items[0].items));
            this.selectedPlansIds = this.selectedPlans.map(
              plan => plan.ratingPlanId
            );
            resolve(this.selectedPlans);

            // INIT POLICY ITEMS PARSING
            // Emmit Observable to Parse Items
            this._parserTrigger.next({ triggerBy: "SelectedPlans" });
          }
        });
    });
  }

  private subscribeGeneralOptionsPolicyItemHomeParsed(): Promise<CoverageItemParsed[]> {
    let counter = 0;
    let timer;

    return new Promise((resolve, reject) => {
      this.subscriptionGeneralOptionsPolicyItemHomeParsed = this.storageService
        .getStorageData("umbrellaGeneralOptionsParsed")
        .subscribe((items: CoverageItemParsed[]) => {
          this.generalOptionsPolicies = JSON.parse(JSON.stringify(items));
          counter++;


          // For the first time we are receiving empty array, so to be sure that all options are in place
          // resolve Promise after second update;
          if (counter > 1 || this.generalOptionsPolicies.length > 0) {
            timer && clearTimeout(timer);
            resolve(this.generalOptionsPolicies);
          } else {
            timer = setTimeout(() => resolve(this.generalOptionsPolicies), 3000); // Required for the quote-loader
          }
        });
    });
  }

  private initPolicyItemsParser(): Promise<void> {
    let delay = 500;
    let timer;

    return new Promise((resolve, reject) => {
      this.subscriptionParserTrigger = this._parserTrigger
        .asObservable()
        .subscribe(res => {
          timer && clearTimeout(timer);

          // Prevent getting Policies and parse data until data is saved to API with default values - if necessary
          if (this.preventParsingPoliciesUntilDataSavedToAPIServer) return;

          timer = setTimeout(() => {
            this.getOptionsPoliciesListAndParseData()
              .then(() => resolve())
              .catch(err => reject(err));
          }, delay);
        });
    });
  }

  private getOptionsPoliciesListAndParseData(): Promise<void> {
    let selectedPlansIdsString = this.selectedPlansIds.join(",");
    let states = this.quote.state;
    let quoteFormTypesString = ""; //this.quoteFormTypes.join(','); // TODO: Remove
    let quoteEffectiveDate =
      this.quote && this.quote.effectiveDate ? this.quote.effectiveDate : "";

    let agencyId;
    this.agencyUserService.userData$.pipe(
      take(1))
      .subscribe(agent => (agencyId = agent.agencyId));

    return new Promise((resolve, reject) => {
      this.specsService
        .getRatingCoverages(
          states,
          this.quoteLob,
          "policyOptionsCarrier",
          selectedPlansIdsString,
          quoteFormTypesString,
          quoteEffectiveDate
        ).pipe(
        take(1))
        .subscribe(res => {
          let policiesItemsHome: CoverageItem[] = [];
          if (res && res.items && res.items.length) {
            policiesItemsHome = [...res.items];
          }

          if (policiesItemsHome && this.quoteId) {
            let policyItemsHomeParsed: CoverageItemParsed[] = [];
            this.processPoliciesItemsParsing(
              policiesItemsHome,
              this.selectedPlans,
              this.quoteId
              // this.quoteFormTypes // TODO: Remove
            )
              .then((res: CoverageItemParsed[]) => {
                policyItemsHomeParsed = JSON.parse(JSON.stringify(res));
                // ---------------
                // if there are no default values setting options, update storage service
                // this.storageService.setStorageData('umbrellaCarrierOptionsParsed', policyItemsHomeParsed);
                //----------------
                return policyItemsHomeParsed;
              })
              .then((res: CoverageItemParsed[]) => {
                policyItemsHomeParsed = res;
                return this.setDefaultValues(policyItemsHomeParsed);
              })
              .then((res: PromiseDataAfterSettingDefaultValuesNew) => {
                // Save To Storage
                policyItemsHomeParsed = res.policyItemsParsed;
                this.storageService.setStorageData(
                  "umbrellaCarrierOptionsParsed",
                  policyItemsHomeParsed
                );

                // Update API with default Values
                return this.savePoliciesToApiIfNewPoliciesToUpdate(
                  policyItemsHomeParsed,
                  res.policiesToUpdate
                );
              })
              .then(() => resolve())
              .catch(err => {
                console.log(err);
                reject(err);
              });
          }
        },
        err => reject(err)
      );
    });
  }

  // public processPoliciesItemsParsing(policies: CoverageItem[], selectedPlans: QuotePlan[], quoteId: string, quoteSelectedFormTypes: string[]): Promise<CoverageItemParsed[]> {
  public processPoliciesItemsParsing(
    policies: CoverageItem[],
    selectedPlans: QuotePlan[],
    quoteId: string
  ): Promise<CoverageItemParsed[]> {
    let arrPoliciesItemsParsed = this.optionsService.parsePoliciesItemsToPoliciesItemsParsed(
      policies
    );
    arrPoliciesItemsParsed = this.optionsService.orderObjectsArrayByProperty(
      arrPoliciesItemsParsed,
      "description"
    );

    // Required for setting default values if new Quote - to check if options
    // has been already saved in storage or not
    let policiesItemsFromStorage: CoverageItemParsed[] = [];

    this.storageService
      .getStorageData("umbrellaCarrierOptionsParsed").pipe(
      take(1))
      .subscribe(res => (policiesItemsFromStorage = res));
    // --------------------------------------------------------------------------

    return this.loadPolicyCoverages(quoteId).then(
      (data: PolicyCoveragesData) => {
        // console.log('%c DATA:', 'color:red', data)

        arrPoliciesItemsParsed = arrPoliciesItemsParsed.map(item => {
          item = this.setAdditionalDataForCoverageItemParsed(item);
          item = this.optionsService.sortPolicyItemParsedChildItems(item);
          item = this.optionsService.setPolicyItemStatusBasedOnPolicyCoverages(
            item,
            data.coverages
          );
          // item = this.setPolicyItemStatusBasedOnRequirements(item);
          item.isRequired = true; // All the options are required;

          item = this.setPolicyItemStatusBasedOnRequirements(item);

          item.endpointUrl = data.endpointURL;
          item = this.optionsService.setPolicyItemParsedIsNewFromAPIValue(
            item,
            policiesItemsFromStorage
          );
          item.quoteResourceId = quoteId;

          if (item.inputType !== "Dropdown") {
            item.values = this.optionsService.orderObjectsArrayByProperty(
              item.values,
              "value"
            );
          }

          return item;
        });
        return arrPoliciesItemsParsed;
      }
    );
  }

  private loadPolicyCoverages(quoteId: string): Promise<PolicyCoveragesData> {
    let policyCoverageData: PolicyCoveragesData = new PolicyCoveragesData();

    return new Promise((resolve, reject) => {
      if (quoteId) {
        this.optionsService
          .getPolicies(quoteId, false).pipe(
          take(1))
          .subscribe(res => {
            // console.log('%c getPolicies', 'color:red', res)
            // For Carrier options It should be '/quotes/{quoteid}/options' URL
            // res.meta.href = res.meta.href.replace('/coverages', '/options');

            if (res.items.length === 0) {
              this.optionsService
                .createCoverageByUri(res.meta.href).pipe(
                take(1))
                .subscribe(res => {
                  policyCoverageData.coverages = res.coverages;
                  policyCoverageData.endpointURL = res.meta.href;

                  // For Carrier options It should be '/quotes/{quoteid}/options' URL
                  // policyCoverageData.endpointURL = policyCoverageData.endpointURL.replace('/coverages/', '/options/');

                  resolve(policyCoverageData);
                });
            } else {
              policyCoverageData.coverages = res.items[0].coverages;

              if (res.items[0].meta && res.items[0].meta.href) {
                policyCoverageData.endpointURL = res.items[0].meta.href;
              } else if (res.items[0]) {
                policyCoverageData.endpointURL =
                  res.meta.href + "/" + res.items[0].resourceId;
              } else {
                policyCoverageData.endpointURL =
                  "api_does_not_returned_data_to_create_uri";
              }

              // For Carrier options It should be '/quotes/{quoteid}/options' URL
              // policyCoverageData.endpointURL = policyCoverageData.endpointURL.replace('/coverages/', '/options/');

              resolve(policyCoverageData);
            }
          });
      } else {
        reject("Error Load Policy Coverages, quoteId Not defined");
      }
    });
  }

  // Set first available option as default if there is not set any value
  private setDefaultValues(
    policiesParsed: CoverageItemParsed[]
  ): Promise<PromiseDataAfterSettingDefaultValuesNew> {
    let policiesToUpdate: CoverageItemParsed[] = [];

    return new Promise((resolve, reject) => {
      policiesParsed.forEach((policy: CoverageItemParsed) => {
        if (
          policy.currentValue == "" ||
          policy.currentValue == null ||
          policy.currentValue == undefined
        ) {
          if (
            policy.values &&
            policy.values.length &&
            policy.values[0] &&
            "value" in policy.values[0]
          ) {
            policy.currentValue = policy.values[0]["value"];

            policiesToUpdate.push(policy);
          }
          // policy with inputType == 'Checkbox' does not return available options/values
          else if (policy.inputType === "Checkbox") {
            policy.currentValue = "No";
            policiesToUpdate.push(policy);
          }
        }
      });

      const promiseResponse: PromiseDataAfterSettingDefaultValuesNew = {
        policiesToUpdate: policiesToUpdate,
        policyItemsParsed: policiesParsed
      };

      resolve(promiseResponse);
    });
  }

  // private savePoliciesWithDefaultValuesToAPI(policiesParsed:PolicyItemParsed[])
  private savePoliciesToApiIfNewPoliciesToUpdate(
    policiesParsed: CoverageItemParsed[],
    policiesToUpdate: CoverageItemParsed[]
  ): Promise<PromiseDataAfterSavingToApi> {
    return new Promise((resolve, reject) => {
      let promiseResponse: PromiseDataAfterSavingToApi = {
        status: "",
        policyItemsParsed: policiesParsed
      };

      // Do not send request to update if no new options with default values
      if (!policiesToUpdate || !policiesToUpdate.length) {
        promiseResponse.status =
          "No new options to Update default values, Do not send API Request";
        // console.log(promiseResponse.status);
        return resolve(promiseResponse);
      }

      // We need to also update policies General Options from the Options General View
      // The mechanism of updating policies (coverages) is strange - only options sent to API will be set as selected
      // (if the option was selected before and not sent to API during update, next time data is load, this option will wiped out )
      let allPoliciesToSearchActiveOptions: CoverageItemParsed[] = [];
      allPoliciesToSearchActiveOptions = [
        ...policiesParsed,
        ...this.generalOptionsPolicies
      ];

      let endpointUrl = policiesParsed[0].endpointUrl;
      // let activePoliciesToUpdate = allPoliciesToSearchActiveOptions.filter(item => item.isActive);
      // Treat all options as active
      let activePoliciesToUpdate = allPoliciesToSearchActiveOptions;
      let readyAllPoliciesToUpdate: Coverage[] = this.optionsService.convertPolicyItemParsedArrayToPolicyArray(
        activePoliciesToUpdate
      );

      // Update remote data
      let newCoverageData = {
        coverages: readyAllPoliciesToUpdate
      };

      this.optionsService
        .updatePoliciesByUri(endpointUrl, newCoverageData).pipe(
        take(1))
        .subscribe(
          res => {
            promiseResponse.status =
              "Set and saved Policies default values for new options";
            // console.log(promiseResponse.status);
            resolve(promiseResponse);
          },
          err => {
            promiseResponse.status =
              "Error occurred During updating, not set default values";
            // console.log(promiseResponse.status);
            resolve(promiseResponse);
          }
        );
    });
  }

  private setAdditionalDataForCoverageItemParsed(item: CoverageItemParsed): CoverageItemParsed {
    if (!item.additionalData) {
      item.additionalData = {};
    }

    return item;
  }

  // Check if any element of an array (anyElementArr) is in another array (arrayToCheckIfIsIn)
  private anyElementIsInArray(
    anyElementArr: string[],
    arrayToCheckIfIsIn: string[]
  ): boolean {
    return anyElementArr.some(el => arrayToCheckIfIsIn.indexOf(el) !== -1);
  }

  // Requirements
  // ----------------------------------------------------------------------------
  // public setPolicyItemStatusBasedOnRequirements(policyItemHomeParsed: CoverageItemParsed, selectedQuoteFormTypes: string[]): CoverageItemParsed {
  public setPolicyItemStatusBasedOnRequirements(
    policyItemHomeParsed: CoverageItemParsed
  ): CoverageItemParsed {
    // switch (policyItemHomeParsed.coverageCode) {
    // }

    return policyItemHomeParsed;
  }
}
