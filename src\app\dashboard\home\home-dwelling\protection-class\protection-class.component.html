<table class="form-table">

  <tr class="form-table__row">
    <td class="form-table__cell u-width-120px u-spacing--bottom-1 u-color-slate-grey">DISTANCE TO</td>
    <td class="form-table__cell u-width-185px"></td>
  </tr>

  <tr class="form-table__row" [class.is-required-field]="isInvalidFieldDistanceToFireStation()">
    <td class="form-table__cell u-width-120px">
      <label for="">Fire Station:</label>
    </td>
    <td class="form-table__cell u-width-185px">
      <sm-autocomplete
        #refDistanceToFireStation
        [id]="'distanceToFireStation'"
        [options]="distanceToFireStation"
        [activeOption]="selectedDistanceToFireStation"
        [required]="true"
        (onSelect)="updateProtectionClassDetails($event, 'distanceToFireStation')">
      </sm-autocomplete>
    </td>
    <td class="form-table__cell u-width-70px"></td>
  </tr>

  <tr class="form-table__row" [class.is-required-field]="isInvalidFieldDistanceToFireHydrant()">
    <td class="form-table__cell u-width-120px">
      <label for="">Hydrant:</label>
    </td>
    <td class="form-table__cell u-width-185px">
      <sm-autocomplete
        #refDistanceToFireHydrant
        [id]="'distanceToFireHydrant'"
        [options]="distanceToFireHydrant"
        [activeOption]="selectedDistanceToFireHydrant"
        [required]="true"
        (onSelect)="updateProtectionClassDetails($event, 'distanceToFireHydrant')">
      </sm-autocomplete>
    </td>
    <td class="form-table__cell u-width-70px"></td>
  </tr>

  <tr class="form-table__row" [class.is-required-field]="refDistanceToBodyWater.ngModel?.invalid">
    <td class="form-table__cell u-width-120px">
      <label for="">Coast:</label>
    </td>
    <td class="form-table__cell u-width-185px">
      <sm-autocomplete
        #refDistanceToBodyWater
        [id]="'distanceToBodyWater'"
        [options]="distanceToBodyWater"
        [activeOption]="selectedDistanceToBodyWater"
        [required]="true"
        (onSelect)="updateProtectionClassDetails($event, 'distanceToBodyWater')">
      </sm-autocomplete>
    </td>
    <td class="form-table__cell u-width-70px"></td>
  </tr>

</table>

<hr/>

<table class="form-table">
  <tr class="form-table__row">
    <td class="form-table__cell u-width-120px">
      <label for="">Suburban Rating:</label>
    </td>
    <td class="form-table__cell u-width-95px">
      <sm-autocomplete
        [options]="suburbanRatingOption"
        [activeOption]="selectedSuburbanRatingOption"
        (onSelect)="updateProtectionClassDetails($event, 'suburbanRatingOption')">
      </sm-autocomplete>
    </td>
    <td class="form-table__cell u-width-120px"></td>
  </tr>

  <tr class="form-table__row">
    <td class="form-table__cell u-width-120px">
      <label for="">ISO PC:</label>
    </td>
    <td class="form-table__cell u-width-95px">
      <input #refISO="ngModel" [disabled]="true" class="u-t-align--center" type="text" id="ISO" name="ISO" [(ngModel)]="isoProtectionClass">
    </td>
    <td class="form-table__cell u-width-120px">
      <app-protection-class-overrides></app-protection-class-overrides>
      <!-- <button (click)="openModalUpdateIso(modalUpdateISO);" class="o-link o-link--blue">Update</button> -->
    </td>
  </tr>
</table>

<!--
<app-modalbox #modalUpdateISO>
  <h1 class="o-heading">User defined ISO protection class</h1>

  <div class="box box--silver u-spacing--1-5">
    <p *ngIf="ISOmanuallyEntered" class="u-color-app-dwelling">Protection classes in yellow were manually entered.</p>
    p-scrollPanel class="u-height-max-340px">
      <table class="form-table">
        <thead class="table__thead">
          <tr class="form-table__row">
            <th class="table__th table__th--no-borders table__th--no-left-padding u-width-80px">ISO PC</th>
            <th class="table__th table__th--no-borders">PLAN</th>
          </tr>
        </thead>
        <tbody>
          <tr class="form-table__row" *ngFor="let plan of plansAndDwellingProtectionClassOverrides">
            <td class="form-table__cell u-width-80px">
              <sm-autocomplete
                [css]="plan.manuallyEdited ? 'u-warning-object' : ''"
                [name]="'iso-value' + plan.ratingPlanId"
                [id]="'iso-value' + plan.ratingPlanId"
                [options]="isoValues"
                [activeOption]="{id: plan.protectionClassCode, text: removeLeadingUnderscore(isoProtectionClass)}"
                [searchFromBegining]="true"
                (onSelect)="selectedCustomISO($event, plan)">
              </sm-autocomplete>
            </td>
            <td class="form-table__cell">{{ plan.ratingPlanName }}</td>
          </tr>
        </tbody>
      </table>

    </p-scrollPanel>
  </div>

  <div class="row u-spacing--2">
    <div class="col-xs-12 u-align-right">
      <button (click)="saveSelectedCustomISO(modalUpdateISO);" class="o-btn u-spacing--right-2">Save</button>
      <button (click)="modalUpdateISO.closeModalbox();" class="o-btn o-btn--idle">Cancel</button>
    </div>
  </div>
</app-modalbox>
-->
