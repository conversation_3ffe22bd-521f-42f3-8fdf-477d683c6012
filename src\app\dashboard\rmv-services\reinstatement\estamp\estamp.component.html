<section class="section section--compact u-spacing--2-5">
    <div class="row">
      <h1 class="o-heading" style="color: #0b71ac; padding-left: 20px;">eStamp</h1>
    </div>
    <div class="row u-spacing--1">
      <div class="col-xs-12">
        <div class="box box--silver">
          <div class="row" [ngStyle]="{'padding-bottom': !isRenewal ? '10px' : ''}">
            <div class="col-md-2"><label for="">Carrier:</label></div>
            <div class="col-md-5">
              <sm-autocomplete #refProvider placeholder="Carrier" [options]="providers" [required]="true" [searchFromBegining]="true" [(ngModel)]="carrier"
              name="carrier">
            </sm-autocomplete>
            </div>
          </div>
          <div class="row" *ngIf="!isRenewal">
            <div class="col-md-2"><label for="">Effective Date:</label></div>
            <div class="col-md-3">
              <app-datepicker-input #refPickerEffectiveDate ngDefaultControl [(ngModel)]="effectiveDate" name="effectiveDate" [required]="true" [maxDate]="maxDate" [minDate]="minDate" [dateRangeStart]="minDate" [dateRangeEnd]="maxDate" [selectDate]="effectiveDate" [placeholder]="'MM/dd/yyyy'" [returnDateFormat]="'yyyy-MM-dd'"
              (onDateChange)="setDate($event)">
            </app-datepicker-input>
            </div>
            <div class="col-md-4" *ngIf="effectiveDate && effectiveDate > maxDate"><p style="padding-top: 5px;color: red;">Effective Date cannot be in the future</p></div>
            <div class="col-md-5" *ngIf="effectiveDate && effectiveDate < minDate"><p style="padding-top: 5px;color: red;">Effective Date cannot be more than {{setMinDate}} days back</p></div>


           </div>
         </div>
       </div>
          </div>
  </section>
