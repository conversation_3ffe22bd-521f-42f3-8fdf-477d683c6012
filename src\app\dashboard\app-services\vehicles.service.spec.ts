import { fakeAsync, inject, TestBed, tick } from '@angular/core/testing';

import { VEHICLE_GENERAL_DETAILS } from 'testing/data/lookups/vin';
import { data as VEHICLE_DATA } from 'testing/data/quotes/vehicle';
import { VEHICLES as VEHICLE_LIST_DATA } from 'testing/data/quotes/vehicles';
import { data as RMV_VEHICLE_DATA } from 'testing/data/rmv-vehicle-plates';
import {
    DataCustomMatchers, expect, expectLastCallArgs, expectLastConnectionPayload,
    expectLastConnectionUrl
} from 'testing/helpers/all';
import { setupMockBackend } from 'testing/setups/mock-backend';

import { StorageService } from 'app/shared/services/storage-new.service';

import { LocationData } from '../../app-model/location';
import { Vehicle } from '../../app-model/vehicle';
import { Helpers } from '../../utils/helpers';
import { LocationsService } from './locations.service';
import { QuotesService } from './quotes.service';
import { VehiclesService } from './vehicles.service';

describe('Service: Vehicles', () => {
  const QUOTE_ID = 'fe3d5969-8a96-40eb-97a7-7c96e74eeec3';
  const VEHICLE_ID = 'f68fafc0-bb08-4fd5-8987-c3cde5f9c9be';
  let vehiclesService: VehiclesService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        VehiclesService,
        { provide: LocationsService, useValue: {} },  // unused
        { provide: QuotesService, useValue: {} },  // unused
        StorageService
      ]
    });

    jasmine.addMatchers(DataCustomMatchers);
  });

  beforeEach(inject([VehiclesService], (_vehiclesService: VehiclesService) => {
    vehiclesService = _vehiclesService;
  }));

  it('can instantiate service when inject service', () => {
    expect(vehiclesService).toBeTruthy();
  });

  describe('when getting single vehicle', () => {
    it('converts ID request to URI request', fakeAsync(() => {
      spyOn(vehiclesService, 'getVehicleByUri');

      // should be getVehicleById for naming consistency...
      vehiclesService.getVehicle(QUOTE_ID, VEHICLE_ID);
      tick();

      expectLastCallArgs(vehiclesService.getVehicleByUri).toEqual([
        '/quotes/' + QUOTE_ID + '/vehicles/' + VEHICLE_ID
      ]);
    }));

    it('resolves correctly', () => {
      const mockBackend = setupMockBackend(VEHICLE_DATA);

      vehiclesService.getVehicleByUri('/quotes/' + QUOTE_ID + '/vehicles/' + VEHICLE_ID).subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/' + QUOTE_ID + '/vehicles/' + VEHICLE_ID);
    });
  });

  describe('when creating single vehicle', () => {
    it('converts ID request to URI request', fakeAsync(() => {
      spyOn(vehiclesService, 'createVehicleByUri');

      // should be createVehicleById for naming consistency...
      vehiclesService.createVehicle(QUOTE_ID, VEHICLE_DATA);
      tick();

      expectLastCallArgs(vehiclesService.createVehicleByUri).toEqual([
        '/quotes/' + QUOTE_ID + '/vehicles',
        VEHICLE_DATA
      ]);
    }));

    it('resolves correctly', () => {
      const mockBackend = setupMockBackend(VEHICLE_DATA);

      vehiclesService.createVehicleByUri('/quotes/' + QUOTE_ID + '/vehicles').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/' + QUOTE_ID + '/vehicles');
    });
  });

  describe('when updating single vehicle', () => {
    it('converts ID request to URI request', fakeAsync(() => {
      spyOn(vehiclesService, 'updateVehicleByUri');

      // should be updateVehicleById for naming consistency...
      vehiclesService.updateVehicle(QUOTE_ID, VEHICLE_ID, VEHICLE_DATA);
      tick();

      expectLastCallArgs(vehiclesService.updateVehicleByUri).toEqual([
        '/quotes/' + QUOTE_ID + '/vehicles/' + VEHICLE_ID,
        VEHICLE_DATA,
        jasmine.any(Boolean)
      ]);
    }));

    it('resolves correctly', () => {
      const mockBackend = setupMockBackend(VEHICLE_DATA);

      vehiclesService.updateVehicleByUri('/quotes/' + QUOTE_ID + '/vehicles/' + VEHICLE_ID, VEHICLE_DATA).subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/' + QUOTE_ID + '/vehicles/' + VEHICLE_ID);
      expectLastConnectionPayload(mockBackend).toEqual(VEHICLE_DATA);
    });
  });

  describe('when deleting single vehicle', () => {
    it('converts ID request to URI request', fakeAsync(() => {
      spyOn(vehiclesService, 'deleteVehicleByUri');

      vehiclesService.deleteVehicleById(QUOTE_ID, VEHICLE_ID);
      tick();

      expectLastCallArgs(vehiclesService.deleteVehicleByUri).toEqual([
        '/quotes/' + QUOTE_ID + '/vehicles/' + VEHICLE_ID
      ]);
    }));

    it('resolves with success when API returns something', () => {
      const mockBackend = setupMockBackend({});

      vehiclesService.deleteVehicleByUri('/quotes/' + QUOTE_ID + '/vehicles/' + VEHICLE_ID).subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/' + QUOTE_ID + '/vehicles/' + VEHICLE_ID);
    });
  });

  describe('when getting vehicle list', () => {
    it('converts ID request to URI request', fakeAsync(() => {
      spyOn(vehiclesService, 'getVehiclesListByUri');

      // should be getVehiclesListById for naming consistency...
      vehiclesService.getVehiclesList(QUOTE_ID);
      tick();

      expectLastCallArgs(vehiclesService.getVehiclesListByUri).toEqual([
        '/quotes/' + QUOTE_ID + '/vehicles'
      ]);
    }));

    it('resolves correctly', () => {
      const mockBackend = setupMockBackend(VEHICLE_LIST_DATA);

      vehiclesService.getVehiclesListByUri('/quotes/' + QUOTE_ID + '/vehicles').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/' + QUOTE_ID + '/vehicles');
    });
  });

  describe('when using vehicleNameToDisplay helper', () => {
    let vehicle: Vehicle;

    beforeEach(() => {
      vehicle = Helpers.deepClone(VEHICLE_DATA);
    });

    it('works when year, make and model are specified', () => {
      const actual = vehiclesService.vehicleNameToDisplay(vehicle);
      expect(actual).toEqual('2017 Hyundai GENESIS');
    });

    it('works when year and make are specified', () => {
      vehicle.model = '';

      const actual = vehiclesService.vehicleNameToDisplay(vehicle);
      expect(actual).toEqual('2017 Hyundai');
    });

    it('works when year and model are specified', () => {
      vehicle.make = '';

      const actual = vehiclesService.vehicleNameToDisplay(vehicle);
      expect(actual).toEqual('2017 GENESIS');
    });

    it('works when make and model are specified', () => {
      vehicle.year = '';

      const actual = vehiclesService.vehicleNameToDisplay(vehicle);
      expect(actual).toEqual('Hyundai GENESIS');
    });

    it('works when only year is specified', () => {
      vehicle.make = '';
      vehicle.model = '';

      const actual = vehiclesService.vehicleNameToDisplay(vehicle);
      expect(actual).toEqual('2017');
    });

    it('works when only make is specified', () => {
      vehicle.year = '';
      vehicle.model = '';

      const actual = vehiclesService.vehicleNameToDisplay(vehicle);
      expect(actual).toEqual('Hyundai');
    });

    it('works when only model is specified', () => {
      vehicle.year = '';
      vehicle.make = '';

      const actual = vehiclesService.vehicleNameToDisplay(vehicle);
      expect(actual).toEqual('GENESIS');
    });

    it('works when nothing is specified', () => {
      vehicle.year = '';
      vehicle.make = '';
      vehicle.model = '';

      const actual = vehiclesService.vehicleNameToDisplay(vehicle);
      expect(actual).toEqual('New Vehicle');
    });

    it('shortens the model name', () => {
      vehicle.model = 'GENESIS 5.0 Ultimate';

      const actual = vehiclesService.vehicleNameToDisplay(vehicle);
      expect(actual).toEqual('2017 Hyundai GENESIS');
    });
  });

  describe('when using vinValidate helper', () => {
    it('checks if the string validation number is correct', () => {
      expect(vehiclesService.vinValidate('')).toBeFalsy();
      expect(vehiclesService.vinValidate('1234567890ABCDEF')).toBeFalsy();
      expect(vehiclesService.vinValidate('1234567890ABCDEF12')).toBeFalsy();
      expect(vehiclesService.vinValidate('1234567890ABCDEF1')).toBeFalsy();

      expect(vehiclesService.vinValidate('1N4AL2AP0BC145923')).toBeTruthy();
      expect(vehiclesService.vinValidate('JW6BHC1S43L095104')).toBeTruthy();
      expect(vehiclesService.vinValidate('2GCGK24J3E1266924')).toBeTruthy();
    });
  });

  describe('when using plateValidate helper', () => {
    it('check if the string plate number is correct', () => {
      expect(vehiclesService.plateValidate('')).toBeFalsy();
      expect(vehiclesService.plateValidate('6RG')).toBeFalsy();

      expect(vehiclesService.plateValidate('6RG 879')).toBeTruthy();
      expect(vehiclesService.plateValidate('2GR 60Y')).toBeTruthy();
      expect(vehiclesService.plateValidate('4TSR 22')).toBeTruthy();
      expect(vehiclesService.plateValidate('3XDR 7Y')).toBeTruthy();
      expect(vehiclesService.plateValidate('6BTJ 2X')).toBeTruthy();
      expect(vehiclesService.plateValidate('316-697')).toBeTruthy();
    });
  });

  describe('when using concatVehicleDetailDescriptionFields helper', () => {
    it('correctly generates vehicle description string', () => {
      const expected = '2-Door Coupe 3.6L 6 -Cylinder (2G1FA1E3&E)';

      const vehicleDetails = Helpers.deepClone(VEHICLE_GENERAL_DETAILS);
      const actual = vehiclesService.concatVehicleDetailDescriptionFields(vehicleDetails);

      expect(actual).toEqual(expected);
    });
  });

  describe('when using convertLocationToLocationForVehicle helper', () => {
    it('correctly converts LocationData objects to VehicleLocationDataForVehicle', () => {
      const vehicle = Helpers.deepClone(VEHICLE_DATA);
      const location = new LocationData();

      const vehicleLocation = vehiclesService.convertLocationToLocationForVehicle(location, vehicle);
      expect(vehicleLocation.location).toEqualIgnoringTypes(location);
      expect(vehicleLocation.vehicleMake).toEqual(vehicle.make);
      expect(vehicleLocation.vehicleResourceId).toEqual(vehicle.resourceId);
      expect(vehicleLocation.vehicleQuoteSessionId).toEqual(vehicle.quoteSessionId);
      expect(vehicleLocation.vehicleMeta).toEqual(vehicle.meta);
      expect(vehicleLocation.vehicleYear).toEqual(vehicle.year);
      expect(vehicleLocation.vehicleModel).toEqual(vehicle.model);
    });
  });

  describe('when selectedRMVvehicle is used', () => {
    it('can be set', () => {
      expect(() => {
        vehiclesService.selectedRMVvehicle = Helpers.deepClone(RMV_VEHICLE_DATA);
      }).not.toThrow();
    });

    it('ensures the data is immutable', () => {
      vehiclesService.selectedRMVvehicle = Helpers.deepClone(RMV_VEHICLE_DATA);

      expect(() => {
        vehiclesService.selectedRMVvehicle.make = 'Something';
      }).toThrow();
    });
  });
});
