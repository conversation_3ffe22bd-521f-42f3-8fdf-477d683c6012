<section class="section section--thick">
  <div class="row">
    <div class="col-xs-12">
      <div class="u-flex u-flex--to-middle">
        <div class="search">
          <input #searchInput [id]="'filter_NAME'" [(ngModel)]="searchQuery" (keydown)="filterByName($event)" class="search__input search__input--md"
            type="text" placeholder="Search By Name">
          <button *ngIf="searchQuery && searchQuery.length > 0" (click)="searchQuery = ''" name="cancel" class="cancel__button"></button>
        </div>
        <button (click)="filterByName($event)" name="searchbtn" class="o-btn u-spacing--left-2">Search</button>
      </div>
    </div>
  </div>
</section>

<section class="section">
  <div class="row">
    <div class="col-xs-12">
    </div>
  </div>
</section>

<section class="section">
  <div class="row">
    <div class="col-xs-12">

      <table class="table table--compact table--fixed table--hoverable-grey">
        <thead class="table__thead">
          <tr class="">
            <th class="table__th u-width-150px">Name</th>
            <th class="table__th u-width-150px">City &amp; State</th>
            <th class="table__th u-width-110px">Secondary Insured</th>
            <th class="table__th u-width-110px">Created</th>
            <th class="table__th u-width-115px">Primary Contact</th>
          </tr>
        </thead>

        <tbody class="table__tbody" *ngIf="arrClientsAll">
          <tr class="table__tr" *ngFor="let row of arrClientsAll">
            <td class="table__td u-color-pelorous">
              <!--[RouterLink]="'{env::formAppUrl}'"-->
              <!--href="{{formAppUrl(row.meta.href)}}" target="_blank"-->
              <a [routerLink]="isDashboard ? ['clients', row.resourceId] : [row.resourceId]"> <span *ngIf="!row.lastName">No
                  clients name</span><span *ngIf="row.lastName">{{row.firstName}} {{row.lastName}}</span></a>
            </td>
            <td class="table__td">{{row.city}}<span *ngIf="row.state">, {{row.state}}</span></td>
            <td class="table__td"><span *ngIf="!row.secondaryLastName">--</span><span *ngIf="row.lastName">{{row.secondaryFirstName}}
                {{row.secondaryLastName}}</span></td>
            <td class="table__td">{{row.creationDate}}</td>
            <td class="table__td">{{row.contactMethods.href}}</td>
          </tr>
        </tbody>
        <tbody class="table__tbody" *ngIf="!arrClientsAll?.length">
          <tr class="table__tr">
            <td colspan="5">
              <p class="u-padd--bottom-1 u-padd--1">There are no results that match your search.</p>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</section>

<section class="section">
  <div class="u-flex u-flex--spread u-flex--to-middle">
    <div class="">
      <app-pagination [totalRecords]="paginationResultsCount" [recordsLimit]="paginationResultLimit" (onPageChange)="paginationPageChange($event)"></app-pagination>
    </div>
    <div class="">
      <app-results-limiter (onChange)="onResultLimitChange($event)"></app-results-limiter>
    </div>
  </div>

  <!-- OPEN CLIENT WARNINGS MODAL -->
  <!-- [css]="'centered'" [backdropCss]="'not-visible'" -->
  <!-- <app-modalbox #refModalWarningsForms [launcher]="'.js-modal-review'">
    <div class="u-flex u-flex--spread">
        <div class="">
            <h2 class="o-heading o-heading--red u-show-iblock u-align-v-middle u-spacing--right-1">OPEN CLIENT: <span class="o-heading--notransform"></span></h2>
        </div>
    </div>

    <div class="box box--silver u-spacing--1-5">
        <p-scrollPanel class="u-height-max-215px">
            <div *ngIf="generalWarnings && generalWarnings.length">
                <p class="u-spacing--bottom-1">Do you want to update this saved client?</p>
                <ul class="list list--nowrap u-color-pelorous u-spacing--left-3-5">
                </ul>
            </div>
       </p-scrollPanel>
    </div>

    <div class="row u-spacing--2">
        <div class="col-xs-12 u-align-right">
            <button (click)="resolveWarnings(generalWarnings, reviewingPlan, refModalWarningsForms)" class="o-btn">Open Form</button>
            <button (click)="refModalWarningsForms.closeModalbox()" class="o-btn o-btn--idle u-spacing--left-1-5">Cancel</button>
        </div>
    </div>
</app-modalbox> -->
