import { WarningItem } from 'app/app-model/warning-item';

export const GENERAL_WARNINGS :WarningItem[] = [
  new WarningItem('Driver 3 has SDIP of 0 with no indicates listed.', '/dashboard/drivers/3', '', 'sdip'),
  new WarningItem('Driver License Number required for Driver 3.', '/dashboard/drivers/3', '', 'licenseNo'),
  new WarningItem('VIN required for Vehicle 2.', '/dashboard/vehicles/2', '', 'vehicleVin'),
  new WarningItem('Dolor sit amet.', '/dashboard/drivers/3', '','defferedRadioDriver'),
  new WarningItem('Consectetur adipiscing elit.'),
  new WarningItem('Proin nibh augue.', '/dashboard/drivers/3', '', 'datepickDriverDOB'),
];

export const  WARNINGS:WarningItem[] = [
  new WarningItem('Comp and either Collision or Limited Collision must be selected.', '', 'AIG'),
  new WarningItem('Gender is required', '/dashboard/drivers/3', 'AIG', 'dr-gender'),
  new WarningItem('Agent Code is Missing', '', 'MAIP'),
  new WarningItem('Lorem ipsum dolor sit amet...', '', 'IC'),
  new WarningItem('Consectetur adipiscing elit', '', ''),
  new WarningItem('Consectetur adipiscing elit some...', '', 'ACE'),
  new WarningItem('Dolor sit amet elit', '', 'SAF'),
  new WarningItem('2 Comp and either Collision or Limited Collision must be selected.', '', 'AIG'),
  new WarningItem('2 Prior Policy Expiration Date Required', '', 'AIG'),
];