import { data as LOSS_HISTORY_REQUIREMENTS } from 'testing/data/specs/loss-history-requirements';
import {
    generateCarriers, generateLabels, generateViewFieldIds, generateViewURIs, runConditions
} from 'testing/helpers/warning-definitions';

import { LossHistory } from 'app/app-model/dwelling';

import {
    WARNINGS_DEFINITIONS_HOME_LOSS_HISTORY, WARNINGS_DEFINITIONS_HOME_LOSS_HISTORY_ITEMS
} from './definitions';

describe('Definitions: Underwriting', () => {
  describe('when home loss history validator is used', () => {
    let definitions: any[];
    let lossHistory: LossHistory;

    beforeEach(() => {
      definitions = WARNINGS_DEFINITIONS_HOME_LOSS_HISTORY;
      lossHistory = {
        meta: {
          href: '',
          rel: []
        },
        lossFreeYears: '',
        lossesYear1: '',
        lossesYear2: '',
        lossesYear3: '',
        lossesYear4: '',
        lossesYear5: '',
        items: [],
        quoteSessionId: '',
        resourceId: '',
        parentId: '',
        resourceName: ''
      };
    });

    it('allows to check if required fields are provided', () => {
      const errors = runConditions(definitions, lossHistory, {
        quoteSelectedPlansIds: '42'
      });

      expect(errors).toEqual(['lossFreeYears']);
    });

    it('generates viewFieldIds without errors', () => {
      expect(() => {
        generateViewFieldIds(definitions, lossHistory);
      }).not.toThrow();
    });

    it('generates viewURIs without errors', () => {
      expect(() => {
        generateViewURIs(definitions, lossHistory);
      }).not.toThrow();
    });

    it('generates labels without errors', () => {
      expect(() => {
        generateLabels(definitions, lossHistory);
      }).not.toThrow();
    });

    it('generates carriers without errors', () => {
      expect(() => {
        generateCarriers(definitions, lossHistory);
      }).not.toThrow();
    });
  });

  describe('when home loss history items validator is used', () => {
    let definitions: any[];
    let lossHistory: LossHistory;

    beforeEach(() => {
      definitions = WARNINGS_DEFINITIONS_HOME_LOSS_HISTORY_ITEMS;
      lossHistory = {
        meta: {
          href: '',
          rel: []
        },
        lossFreeYears: '',
        lossesYear1: '',
        lossesYear2: '',
        lossesYear3: '',
        lossesYear4: '',
        lossesYear5: '',
        items: [],
        quoteSessionId: '',
        resourceId: '',
        parentId: '',
        resourceName: ''
      };
    });

    it('allows to check if required fields are provided', () => {
      let errors = runConditions(definitions, lossHistory, [ null, LOSS_HISTORY_REQUIREMENTS.items ]);
      expect(errors).toEqual(['lossesYear1', 'lossesYear2', 'lossesYear3', 'lossesYear4', 'lossesYear5']);

      lossHistory.lossFreeYears = '_1';
      lossHistory.lossesYear1 = '_0';
      lossHistory.lossesYear2 = '_2';
      lossHistory.lossesYear3 = '_0';
      lossHistory.lossesYear4 = '_0';
      lossHistory.lossesYear5 = '_0';
      errors = runConditions(definitions, lossHistory, [ null, LOSS_HISTORY_REQUIREMENTS.items ]);

      expect(errors).toEqual(['items']);
    });

    it('generates viewFieldIds without errors', () => {
      expect(() => {
        generateViewFieldIds(definitions, lossHistory);
      }).not.toThrow();
    });

    it('generates viewURIs without errors', () => {
      expect(() => {
        generateViewURIs(definitions, lossHistory);
      }).not.toThrow();
    });

    it('generates labels without errors', () => {
      expect(() => {
        generateLabels(definitions, lossHistory);
      }).not.toThrow();
    });

    it('generates carriers without errors', () => {
      expect(() => {
        generateCarriers(definitions, lossHistory);
      }).not.toThrow();
    });
  });
});
