import { WARNING_GROUPS, WarningDefinitionI, AdditionalDataI, AdditionalDataHomeClientInfo} from 'app/hints-and-warnings/model/warnings';
import { ClientAddress, ClientContactMethod, ClientDetails } from 'app/app-model/client';
import { Validate } from 'app/hints-and-warnings/validators';
import { Quote } from 'app/app-model/quote';
import { CoverageItemParsed } from '../../../../app-model/coverage';

import { differenceInYears } from 'date-fns';

function requiredField(value): boolean {
  if (value === undefined || value == null) {
    return true;
  }
  value = String(value);

  return value.trim().length <= 0 || !value;
}

function validateZipCode(zip): boolean {
  if (zip === '' || zip === null) {
    return false;
  }

  const zipPattern = /^\d{5}(?:-?\d{4})?$/;
  return !zipPattern.test(zip);
}

function generateViewUrl(): string {
  return  '{{current_url}}?overlay=info&type=client';
}

/**
 * Validation for Client Info Data
 * For Model: ClientDetails
 */


const clientInfoFirstName: WarningDefinitionI = {
  id: 'firstName',
  deepId: 'firstName',
  viewUri: generateViewUrl,
  viewFieldId: 'clientInfoPrimaryFirstName',
  warnings: [{
    label: 'Required First Name for client.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const clientInfoLastName: WarningDefinitionI = {
  id: 'lastName',
  deepId: 'lastName',
  viewUri: generateViewUrl,
  viewFieldId: 'clientInfoPrimaryLastName',
  warnings: [{
    label: 'Required Last Name for client',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const clientInfoDOB: WarningDefinitionI = {
  id: 'dob',
  deepId: 'dob',
  viewUri: generateViewUrl,
  viewFieldId: 'clientInfoPrimaryDOB',
  warnings: [{
    label: 'Required Date of Birth for client.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};
// Arbella RTR PRODUCT-1985
const clientInfoSecondaryDOB: WarningDefinitionI = {
  id: 'secondaryDOB',
  deepId: 'secondaryDOB',
  viewUri: generateViewUrl,
  viewFieldId: 'clientInfoSecondaryDOB',
  warnings: [{
    label: 'Required Date of Birth for secondary client.',
    condition: (val, fullObj: ClientDetails, additionalData: AdditionalDataI) => {
      return (fullObj.secondaryFirstName && fullObj.secondaryLastName && !fullObj.secondaryDOB);
    },
    group: WARNING_GROUPS.general,
    carriers: []
  },
  {
    label: (val, fullObj) => 'Required Date of Birth for secondary client.',
    condition: (val, fullObj: ClientDetails, additionalData) => {
      if ((fullObj.secondaryFirstName || fullObj.secondaryLastName) && !fullObj.secondaryDOB) {
       return true;
      }
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }
]
};

const clientInfoSecondaryFirstName: WarningDefinitionI = {
  id: 'secondaryFirstName',
  deepId: 'secondaryFirstName',
  viewUri: generateViewUrl,
  viewFieldId: 'clientSecondaryFirstName',
  warnings: [{
    label: 'Required First Name for secondary client',
    condition: (val, fullObj: ClientDetails, additionalData: AdditionalDataI) => {
      return((fullObj.secondaryLastName || fullObj.secondaryDOB) && !fullObj.secondaryFirstName);
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const clientInfoSecondaryLastName: WarningDefinitionI = {
  id: 'secondaryLastName',
  deepId: 'secondaryLastName',
  viewUri: generateViewUrl,
  viewFieldId: 'clientSecondaryLastName',
  warnings: [{
    label: 'Required Last Name for secondary client',
    condition: (val, fullObj: ClientDetails, additionalData: AdditionalDataI) => {
      return((fullObj.secondaryFirstName || fullObj.secondaryDOB) && !fullObj.secondaryLastName);
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};


export const WARNINGS_DEFINITIONS_INFO_CLIENT_FOR_HOME: WarningDefinitionI[] = [
  clientInfoFirstName,
  clientInfoLastName,
  clientInfoDOB,
  clientInfoSecondaryDOB,
  clientInfoSecondaryFirstName,
  clientInfoSecondaryLastName
];


// Client Addresses
// ------------------------------------------------------------------------------

/**
 * Validation for Client Info Addresses Data
 * For Model: ClientAddress
 */

function generateAddressFieldId(addr: ClientAddress, customName: string): string {
  return (addr && addr.addressType) ? customName + '_' + addr.addressType : customName;
}

function checkIfMoveInDateIsLessThanSpecifiedNumberOfYearsFromQuoteEffectiveDate(addresses: ClientAddress[], quote: Quote, numberOfYears: number = 3): boolean {
  let result = false;
  const currentAddress = addresses.find(addr => addr.addressType === 'StreetAddress');

if (currentAddress && currentAddress.residencyDate && quote.effectiveDate && !isNaN(numberOfYears)) {
  const difference = differenceInYears(
    new Date(quote.effectiveDate),
    new Date(currentAddress.residencyDate)
  );
  result = difference < numberOfYears;
}

  return result;
}

function validatePriorAddressBasedOnMoveInDate(value: string, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo, requiredForCarriers: string[] = [], numberOfYears: number = 3): boolean {
  if (fullObj.addressType === 'PreviousAddress') {
    const isLessThanSpecifiedYears = (additionalData.observedData && additionalData.observedData.length)
      ? checkIfMoveInDateIsLessThanSpecifiedNumberOfYearsFromQuoteEffectiveDate(<ClientAddress[]>additionalData.observedData, additionalData.quote, numberOfYears)
      : false;

    return isLessThanSpecifiedYears && Validate.isRequiredForSelectedPlansIfEmptyValue(
      value,
      requiredForCarriers,
      additionalData.quoteSelectedPlansIds
    );
  }

  return false;
}

// HELPERS
  // ----------------------------------------------------------------------------
  // Check if any element of an array (anyElementArr) is in another array (arrayToCheckIfIsIn)
 function anyElementIsInArray(anyElementArr: string[], arrayToCheckIfIsIn: string[]): boolean {
    return anyElementArr.some(el => arrayToCheckIfIsIn.indexOf(el) !== -1);
  }


// Providence (Plan 97), Travelers (Plan 95), Travelers New (Plan ID 283)
const priorRequiredForPlansWithinOneYear: string[] = ['97', '95', '283', '310'];
// ASI (Plan 105),
const priorRequiredForPlansWithinTwoYears: string[] = ['105'];
// National General (Plan 119), Quincy Mutual (Plan 92), Union Mutual of Vermont (Plan 93), Vermont Mutual (Plan 96)
const priorRequiredForPlansWithinThreeYears: string[] = ['119', '92', '93', '96'];

// NLC
const priorRequiredForPlansWithinFourYears: string[] = ['317'];


const clientInfoAddrAddr1: WarningDefinitionI = {
  id: 'address1',
  deepId: 'address1',
  viewUri: generateViewUrl,
  viewFieldId: (fullObj: ClientAddress) => generateAddressFieldId(fullObj, 'clientInfoAddrAddr1'),
  warnings: [{
    label: 'Required Current Client Address',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return fullObj.addressType === 'StreetAddress' && Validate.isEmptyValue(value);
    },
    group: WARNING_GROUPS.general,
    carriers: []
  },
  {
    label: 'Required Current Client Prior Address Street',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return validatePriorAddressBasedOnMoveInDate(value, fullObj, additionalData, priorRequiredForPlansWithinFourYears, 4);
    },
    group: WARNING_GROUPS.carrier,
    carriers: priorRequiredForPlansWithinFourYears
  },
  {
    label: 'Required Current Client Prior Address Street',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return validatePriorAddressBasedOnMoveInDate(value, fullObj, additionalData, priorRequiredForPlansWithinThreeYears, 3);
    },
    group: WARNING_GROUPS.carrier,
    carriers: priorRequiredForPlansWithinThreeYears
  },
  {
    label: 'Required Current Client Prior Address Street',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return validatePriorAddressBasedOnMoveInDate(value, fullObj, additionalData, priorRequiredForPlansWithinTwoYears, 2);
    },
    group: WARNING_GROUPS.carrier,
    carriers: priorRequiredForPlansWithinTwoYears
  },
  {
    label: 'Required Current Client Prior Address Street',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return validatePriorAddressBasedOnMoveInDate(value, fullObj, additionalData, priorRequiredForPlansWithinOneYear, 1);
    },
    group: WARNING_GROUPS.carrier,
    carriers: priorRequiredForPlansWithinOneYear
  },
  {
    label: 'Required Current Client Prior Address Street',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return((fullObj.zip || fullObj.state || fullObj.city) && !fullObj.address1 && fullObj.addressType === "PreviousAddress");
    },
    group: WARNING_GROUPS.general,
    carriers: []
  },]
};

// Required for:
// Safety (Standard), MAPFRE
const clientInfoAddrCityCarriers = ['91', '112'];
const clientInfoAddrCity: WarningDefinitionI = {
  id: 'city',
  deepId: 'city',
  viewUri: generateViewUrl,
  viewFieldId: (fullObj: ClientAddress) => generateAddressFieldId(fullObj, 'clientInfoAddrCity'),
  warnings: [{
    label: 'Required Current Client City',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return fullObj.addressType === 'StreetAddress' && Validate.isRequiredForSelectedPlansIfEmptyValue(
        value,
        clientInfoAddrCityCarriers,
        additionalData.quoteSelectedPlansIds
      );
    },
    group: WARNING_GROUPS.carrier,
    carriers: clientInfoAddrCityCarriers
  },
  {
    label: 'Required Current Client City',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return fullObj.addressType === 'StreetAddress' && Validate.isEmptyValue(value);
    },
    group: WARNING_GROUPS.general,
    carriers: []
  },
  {
    label: 'Required Current Client Prior Address City',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return validatePriorAddressBasedOnMoveInDate(value, fullObj, additionalData, priorRequiredForPlansWithinFourYears, 4);
    },
    group: WARNING_GROUPS.carrier,
    carriers: priorRequiredForPlansWithinFourYears
  },
  {
    label: 'Required Current Client Prior Address City',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return validatePriorAddressBasedOnMoveInDate(value, fullObj, additionalData, priorRequiredForPlansWithinThreeYears, 3);
    },
    group: WARNING_GROUPS.carrier,
    carriers: priorRequiredForPlansWithinThreeYears
  },
  {
    label: 'Required Current Client Prior Address City',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return validatePriorAddressBasedOnMoveInDate(value, fullObj, additionalData, priorRequiredForPlansWithinTwoYears, 2);
    },
    group: WARNING_GROUPS.carrier,
    carriers: priorRequiredForPlansWithinTwoYears
  },
  {
    label: 'Required Current Client Prior Address City',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return validatePriorAddressBasedOnMoveInDate(value, fullObj, additionalData, priorRequiredForPlansWithinOneYear, 1);
    },
    group: WARNING_GROUPS.carrier,
    carriers: priorRequiredForPlansWithinOneYear
  },
  {
    label: 'Required Current Client Prior Address City',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return((fullObj.address1 || fullObj.state || fullObj.zip) && !fullObj.city && fullObj.addressType === "PreviousAddress");
    },
    group: WARNING_GROUPS.general,
    carriers: []
  },]
};

// Required for:
// Safety (Standard), MAPFRE
const clientInfoAddrStateCarriers = ['91', '112'];
const clientInfoAddrState: WarningDefinitionI = {
  id: 'state',
  deepId: 'state',
  viewUri: generateViewUrl,
  viewFieldId: (fullObj: ClientAddress) => generateAddressFieldId(fullObj, 'clientInfoAddrState'),
  warnings: [{
    label: 'Required Current Client State',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return fullObj.addressType === 'StreetAddress' && Validate.isRequiredForSelectedPlansIfEmptyValue(
        value,
        clientInfoAddrStateCarriers,
        additionalData.quoteSelectedPlansIds
      );
    },
    group: WARNING_GROUPS.carrier,
    carriers: clientInfoAddrStateCarriers
  },
  {
    label: 'Required Current Client State',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return fullObj.addressType === 'StreetAddress' && Validate.isEmptyValue(value);
    },
    group: WARNING_GROUPS.general,
    carriers: []
  },
  {
    label: 'Required Current Client Prior Address State',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return validatePriorAddressBasedOnMoveInDate(value, fullObj, additionalData, priorRequiredForPlansWithinFourYears, 4);
    },
    group: WARNING_GROUPS.carrier,
    carriers: priorRequiredForPlansWithinFourYears
  },
  {
    label: 'Required Current Client Prior Address State',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return validatePriorAddressBasedOnMoveInDate(value, fullObj, additionalData, priorRequiredForPlansWithinThreeYears, 3);
    },
    group: WARNING_GROUPS.carrier,
    carriers: priorRequiredForPlansWithinThreeYears
  },
  {
    label: 'Required Current Client Prior Address State',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return validatePriorAddressBasedOnMoveInDate(value, fullObj, additionalData, priorRequiredForPlansWithinTwoYears, 2);
    },
    group: WARNING_GROUPS.carrier,
    carriers: priorRequiredForPlansWithinTwoYears
  },
  {
    label: 'Required Current Client Prior Address State',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return validatePriorAddressBasedOnMoveInDate(value, fullObj, additionalData, priorRequiredForPlansWithinOneYear, 1);
    },
    group: WARNING_GROUPS.carrier,
    carriers: priorRequiredForPlansWithinOneYear
  },
  {
    label: 'Required Current Client Prior Address State',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return((fullObj.address1 || fullObj.zip || fullObj.city) && !fullObj.state && fullObj.addressType === "PreviousAddress");
    },
    group: WARNING_GROUPS.general,
    carriers: []
  },]
};

// Required for:
// Safety (Standard), MAPFRE, Bunker Hill (Plan ID 113)
const clientInfoAddrZipCarriers = ['91', '112', '113'];
const clientInfoAddrZip: WarningDefinitionI = {
  id: 'zip',
  deepId: 'zip',
  viewUri: generateViewUrl,
  viewFieldId: (fullObj: ClientAddress) => generateAddressFieldId(fullObj, 'clientInfoAddrZip'),
  warnings: [{
    label: 'Required Current Client Zip',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return fullObj.addressType === 'StreetAddress' && Validate.isRequiredForSelectedPlansIfEmptyValue(
        value,
        clientInfoAddrZipCarriers,
        additionalData.quoteSelectedPlansIds
      );
    },
    group: WARNING_GROUPS.carrier,
    carriers: clientInfoAddrZipCarriers
  },
  {
    label: 'Required Current Client Zip',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return fullObj.addressType === 'StreetAddress' && Validate.isEmptyValue(value);
    },
    group: WARNING_GROUPS.general,
    carriers: []
  },
  {
    label: 'Required Current Client Prior Address Zip',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return validatePriorAddressBasedOnMoveInDate(value, fullObj, additionalData, priorRequiredForPlansWithinFourYears, 4);
    },
    group: WARNING_GROUPS.carrier,
    carriers: priorRequiredForPlansWithinFourYears
  },
  {
    label: 'Required Current Client Prior Address ZIP',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return validatePriorAddressBasedOnMoveInDate(value, fullObj, additionalData, priorRequiredForPlansWithinThreeYears, 3);
    },
    group: WARNING_GROUPS.carrier,
    carriers: priorRequiredForPlansWithinThreeYears
  },
  {
    label: 'Required Current Client Prior Address ZIP',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return validatePriorAddressBasedOnMoveInDate(value, fullObj, additionalData, priorRequiredForPlansWithinTwoYears, 2);
    },
    group: WARNING_GROUPS.carrier,
    carriers: priorRequiredForPlansWithinTwoYears
  },
  {
    label: 'Required Current Client Prior Address ZIP',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return validatePriorAddressBasedOnMoveInDate(value, fullObj, additionalData, priorRequiredForPlansWithinOneYear, 1);
    },
    group: WARNING_GROUPS.carrier,
    carriers: priorRequiredForPlansWithinOneYear
  },
  {
    label: 'Required Current Client Prior Address ZIP',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataHomeClientInfo) => {
      return((fullObj.address1 || fullObj.state || fullObj.city) && !fullObj.zip && fullObj.addressType === "PreviousAddress");
    },
    group: WARNING_GROUPS.general,
    carriers: []
  },
  {
    label: 'Client Prior Address Zip code is not Valid',
    condition: (value: any, fullObj: ClientAddress) => {
      if(fullObj.addressType === "PreviousAddress") {
        return validateZipCode(value);
      }
    },
    group: WARNING_GROUPS.general,
    carriers: []
  },
  {
    label: 'Client Current Address Zip code is not Valid',
    condition: (value: any, fullObj: ClientAddress) => {
      if(fullObj.addressType === "StreetAddress") {
        return validateZipCode(value);
      }
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }
]
};



// clientInfoAddrResidencyDate (Move-In Date)
// Required for:
// ASI, MAPFRE, MSA/NGM Insurance, National General, Providence Mutual,
// Quincy Mutual Group, Travelers, Union Mutual of Vermont, Vermont Mutual, Travelers New (Plan ID 283)
const clientInfoAddrResidencyDateCarriers = ['317', '105', '112', '204', '119', '97', '92', '95', '93', '96', '283', '292', '303', '310', '306', '313', '316'];
// Hanover New (Plan ID 282)
const clientInfoAddrResidencyDateDependingFormType = ['282']; // https://bostonsoftware.atlassian.net/browse/SPR-2540
const clientInfoAddrResidencyDate: WarningDefinitionI = {
  id: 'residencyDate',
  deepId: 'residencyDate',
  viewUri: generateViewUrl,
  viewFieldId: (fullObj: ClientAddress) => generateAddressFieldId(fullObj, 'clientInfoAddrResidencyDate'),
  warnings: [{
    label: 'Required Move-In Date',
    condition: (value, fullObj, additionalData: AdditionalDataHomeClientInfo) => {
      return fullObj.addressType === 'StreetAddress' && Validate.isRequiredForSelectedPlansIfEmptyValue(
        value,
        clientInfoAddrResidencyDateCarriers,
        additionalData.quoteSelectedPlansIds
      );
    },
    group: WARNING_GROUPS.carrier,
    carriers: clientInfoAddrResidencyDateCarriers
  },
  {
    label: 'Required Move-In Date',
    condition: (value, fullObj, additionalData: AdditionalDataHomeClientInfo) => {
      // console.log('additionalData >>', additionalData, additionalData.quoteFormTypes);
      // additionalData.quoteSelectedPlansIds.push('282')
      return fullObj.addressType === 'StreetAddress'
        && anyElementIsInArray(['HO3', 'HO5', 'HO6'], additionalData.quoteFormTypes)
        && Validate.isRequiredForSelectedPlansIfEmptyValue(
          value,
          clientInfoAddrResidencyDateDependingFormType,
          additionalData.quoteSelectedPlansIds
        );
    },
    group: WARNING_GROUPS.carrier,
    carriers: clientInfoAddrResidencyDateDependingFormType
  }]
};



export const WARNINGS_DEFINITIONS_INFO_CLIENT_ADDRESSES_FOR_HOME: WarningDefinitionI[] = [
  clientInfoAddrAddr1,
  clientInfoAddrCity,
  clientInfoAddrState,
  clientInfoAddrZip,
  clientInfoAddrResidencyDate
];


// Client Contact Methods
// ------------------------------------------------------------------------------

/**
 * Validation for Client Info Addresses Data
 * For Model: ClientContactMethod
 */

function generateContactMethodViewFieldId(fullObj: ClientContactMethod): string {
  return fullObj.type ? 'cliContact' + fullObj.type : 'no_id';
}


// Required for:
// Concord Group
const clientInfoContactMethodEmailCarriers = ['64'];
const clientInfoContactMethodEmail: WarningDefinitionI = {
  id: 'value',
  deepId: 'value',
  viewUri: generateViewUrl,
  viewFieldId: generateContactMethodViewFieldId, // 'cliContactEmail',
  warnings: [{
    label: 'Required Email address for client.',
    condition: (value: any, fullObj: ClientContactMethod, additionalData: AdditionalDataHomeClientInfo) => {

      if (fullObj.type === 'Email') {
        // https://bostonsoftware.atlassian.net/browse/SPR-2069
        let tmpEDocumentDiscountOption: CoverageItemParsed;

        if (additionalData.carrierOptionsParsed.length) {
          tmpEDocumentDiscountOption = additionalData.carrierOptionsParsed
            .find(el => el.coverageCode === 'BSC-HOME-023156');
            // .find(el => el.coverageCode === 'BSC-AUTO-002003');
        }

        if (tmpEDocumentDiscountOption && tmpEDocumentDiscountOption.isActive) {
          return Validate.isEmptyValue(fullObj.value);
        }
      }

      return false;
    },
    group: WARNING_GROUPS.carrier,
    carriers: clientInfoContactMethodEmailCarriers
  },
  {
    label: (val: string, fullObj: ClientContactMethod) => 'Client\'s Phone number incorrect: ' + fullObj.type,
    condition: (value: string, fullObj: ClientContactMethod) => {

      switch (fullObj.type) {
        case 'MobilePhone':
        case 'HomePhone':
        case 'BusinessPhone':
          if (value && !Validate.isValidPhoneNumber(value)) {
            return true;
          }
          break;
        default:
          return false;
      }

      return false;
    },
    group: WARNING_GROUPS.general,
    carriers: [],
  },
  {
    label: (val: string, fullObj: ClientContactMethod) => 'Client\'s Email address incorrect.',
    condition: (value: string, fullObj: ClientContactMethod) => {
      if (fullObj.type === 'Email' && value && !Validate.isValidEmail(value)) {
        return true;
      }

      return false;
    },
    group: WARNING_GROUPS.general,
    carriers: [],
  },
  {
    label: (val: string, fullObj: ClientContactMethod) => 'Client\'s Fax Number incorrect.',
    condition: (value: string, fullObj: ClientContactMethod) => {
      if (fullObj.type === 'Fax' && value && !Validate.isValidPhoneNumber(value)) {
        return true;
      }

      return false;
    },
    group: WARNING_GROUPS.general,
    carriers: [],
  }]
};


export const WARNINGS_DEFINITIONS_INFO_CLIENT_CONTACT_METHODS_FOR_HOME: WarningDefinitionI[] = [
  clientInfoContactMethodEmail
];
