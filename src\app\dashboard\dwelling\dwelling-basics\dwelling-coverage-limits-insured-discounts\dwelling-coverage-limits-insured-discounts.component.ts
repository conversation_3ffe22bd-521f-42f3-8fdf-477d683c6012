
import {take} from 'rxjs/operators';
import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { StorageService } from 'app/shared/services/storage-new.service';
import { DwellingService } from 'app/dashboard/app-services/dwelling.service';
import { CoveragesService } from 'app/dashboard/app-services/coverages.service';
import { FilterOption } from 'app/app-model/filter-option';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { ClientsService } from 'app/dashboard/app-services/clients.service';
import { ClientDetails } from 'app/app-model/client';
import { MoneyService } from 'app/shared/services/money.service';
import createNumberMask from 'text-mask-addons/dist/createNumberMask';
import { SubscriptionLike as ISubscription } from 'rxjs';
import { OptionsService } from 'app/dashboard/app-services/options.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import {
  Coverage,
  CoverageItem,
  CoverageValue,
  CoveragesData,
  CoverageApiResponseData,
  PolicyItemDefaultDataAPIResponse
} from 'app/app-model/coverage';
import { ModalboxComponent } from '../../../../shared/components/modalbox/modalbox.component';


const NONE = 'None';

interface IModalLouMessages {
  setDwellingValue: string;
  percentageWrong: string;
  minimalValue: string;
}

const MODAL_LOU_MESSAGES: IModalLouMessages = {
  setDwellingValue: 'Please first set correct value for Dwelling field.',
  percentageWrong: 'Loss of Use coverage limit cannot be reduced.',
  minimalValue: 'Loss of Use needs to be at least 20% of Dwelling value'
}

@Component({
    selector: 'app-dwelling-coverage-limits-insured-discounts',
    templateUrl: './dwelling-coverage-limits-insured-discounts.component.html',
    styleUrls: ['./dwelling-coverage-limits-insured-discounts.component.scss'],
    standalone: false
})

export class DwellingCoverageLimitsInsuredDiscountsComponent implements OnInit {
  private coveragesInitialized = false;
  public coverages: CoverageItem[] = [];

  // public coverageDwelling:CoverageItem = new CoverageItem(); // It looks like it's not in use
  // public coverageOtherStructures:CoverageItem = new CoverageItem(); // It looks like it's not in use
  // public coveragePersonalProperty:CoverageItem = new CoverageItem(); // It looks like it's not in use
  // public coverageLossOfUse:CoverageItem = new CoverageItem(); // It looks like it's not in use
  // public coverageFairRentalValue: CoverageItem = new CoverageItem(); // It looks like it's not in use

  public coverageQuoteDwelling = '';
  public coverageQuoteOtherStructures = '';
  public coverageQuotePersonalProperty = '';
  public coverageQuoteLossOfUse = '';
  public coverageQuotePersonalLiability:string|any = ''; // TODO:: THE Type should be precised !!!
  public coverageQuoteMedicalPayments:string|any = ''; // TODO:: THE Type should be precised !!!
  public coverageQuoteAllPerils: FilterOption = null;
  public coverageQuoteWindHail: FilterOption = null;
  // public coverageQuoteAllPerils:string = '';
  // public coverageQuoteWindHail:string = '';
  public coverageQuoteSpecialCoverage = '';
  public coverageQuoteFairRentalValue = '';

  public optionQuoteFRV = '';
  public optionQuoteBSCEC = '';
  public optionQuoteBSCOSEC = '';
  public optionQuoteBSCPPEC = '';
  public optionQuoteBSCINFRV = '';
  public optionQuoteBSCLOUEC = '';
  public optionInflation = '';
  public optionLossFreeYears = '';

  public coveragePersonalLiabilityOptions:any[] = [];
  public coverageMedicalPaymentsOptions: any[] = [];
  public coverageAllPerilsOptions: any[] = [];
  public coverageWindHailOptions: any[] = [];

  public coverageInflationOptions: any[] = ['0', '2', '4', '6', '8', '12'];
  public coverageLossFreeYearsOptions: any[] = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10'];

  public coverageExtendedOptions: any[] = [{id: null, text: ' &nbsp;'}, 'Extended Coverage', 'Vandalism & MM', 'EC, Vandalism & MM'];

  private subscribtionGetCoverages;
  private subscriptionQuote;
  private subscriptionQuoteCoverages;
  private subscriptionQuoteOptions;
  private subscriptionDwellingData;
  public quote;
  private quoteCoverages;
  private quoteCoveragesItems;
  private quoteOptions;
  private quoteOptionsItems;

  // insured discounts
  private subscriptionGetClients;
  public insurerIsSmoker = false;

  private clientId: string;

  private clientDetails: ClientDetails =  new ClientDetails();

  public dp1FormType: Array<string> = ['Basic (DP 1)', 'DP1'];
  private dwelling;
  public lockMEDPM = false;
  private helperTextMaskChangeEventIssueWorkaroundInputValueDuringFocus = '';

  public coveragesNotDefaultPercentages = [];
  public defaultCoverageValue = {};
  private quoteCoveragesItemsTmp;
  private updatingCoverageCode = {
    coverageCode: '',
    value: ''
  };

  constructor(
    private storageService: StorageService,
    private dwellingService: DwellingService,
    private coveragesService: CoveragesService,
    private overlayLoaderService: OverlayLoaderService,
    private clientsService: ClientsService,
    public moneyService: MoneyService,
    private optionsService: OptionsService,
    private apiCommonService: ApiCommonService
  ) { }

  public moneyMask = createNumberMask();

  ngOnInit() {
    this.getCoverages();
    this.getQuote();
    this.getQuoteCoverages();
    this.getQuoteOptions();
    this.getQuoteDwelling();
  }

  ngOnDestroy() {
    this.subscribtionGetCoverages && this.subscribtionGetCoverages.unsubscribe();
    this.subscriptionQuote && this.subscriptionQuote.unsubscribe();
    this.subscriptionGetClients && this.subscriptionGetClients.unsubscribe();
    this.subscriptionQuoteCoverages && this.subscriptionQuoteCoverages.unsubscribe();
    this.subscriptionQuoteOptions && this.subscriptionQuoteOptions.unsubscribe();
    this.subscriptionDwellingData && this.subscriptionDwellingData.unsubscribe();
  }

  private getQuote() {
    this.subscriptionQuote = this.storageService.getStorageData('selectedQuote').subscribe( data => {
      if (data) {
        this.quote = JSON.parse(JSON.stringify(data));
      }
    });
  }

  private getQuoteCoverages() {
    this.subscriptionQuoteCoverages = this.storageService.getStorageData('homeQuoteCoverages').subscribe( data => {
      if (data && data.meta && data.meta.href) {
        this.quoteCoverages = JSON.parse(JSON.stringify(data));
        if (this.quoteCoverages && this.quoteCoverages.items && this.quoteCoverages.items.length) {
          this.quoteCoveragesItems = this.quoteCoverages.items[0];
          this.assignQuoteCoverages();
          if (this.quoteCoveragesItems.coverages) {
            this.checkPLvalue(this.quoteCoveragesItems.coverages);
          }
        } else if (this.quoteCoverages && this.quoteCoverages.coverages && this.quoteCoverages.coverages.length) {
          this.quoteCoveragesItems = this.quoteCoverages;
          this.assignQuoteCoverages();
          if (this.quoteCoveragesItems.coverages) {
            this.checkPLvalue(this.quoteCoveragesItems.coverages);
          }
        } else {
          this.overlayLoaderService.showLoader();

          this.coveragesService.createHomeCoverageByUriWithDefaultValues(this.quote.coveragesStandard.href).pipe(
            take(1))
            .subscribe(
              (res: CoveragesData) => {
                this.quoteCoverages.items = [res];
                // Save to storage
                const tmpRes = new CoverageApiResponseData();
                tmpRes.items = [res];
                this.storageService.setStorageData('homeQuoteCoverages', tmpRes);
                this.overlayLoaderService.hideLoader();
              },
              err => {
                console.log(err);
                this.overlayLoaderService.hideLoader();
              });
        }
      } else {
        this.coveragesService.getHomeQuoteCoverages$(this.quote.coveragesStandard.href).pipe(take(1)).subscribe();
      }
    });
  }

  private checkPLvalue(coverages) {
    if (coverages && coverages.length) {
      const plCoverage = coverages.find(cov => cov.coverageCode === 'PL');
      if (plCoverage && plCoverage.values && plCoverage.values[0] && plCoverage.values[0].value && plCoverage.values[0].value === NONE) {
        const medpmCoverage = coverages.find(cov => cov.coverageCode === 'MEDPM');
        if (medpmCoverage && medpmCoverage.values && medpmCoverage.values[0] && medpmCoverage.values[0].value) {
          if (medpmCoverage.values[0].value !== NONE) {
            this.updateCoverageAPI('MEDPM', NONE);
          }
        } else {
          this.updateCoverageAPI('MEDPM', NONE);
        }
        this.lockMEDPM = true;
      } else {
        this.lockMEDPM = false;
      }
    }
  }

  private getQuoteDwelling() {
    this.subscriptionDwellingData = this.storageService.getStorageData('dwelling').subscribe(data => {
      if (data) {
        this.dwelling = JSON.parse(JSON.stringify(data));
      }
    });
  }

  public updateQuoteCoverage(coverageCode, value) {
    this.coveragesNotDefaultPercentages = [];
    if (value.indexOf('$') > -1) {
      value = value.replace('$', '').replace(/,/g, '');
    }
    this.updateCoverageAPI(coverageCode, value);
    if (coverageCode === 'PL') {
      if (value === NONE) {
        this.lockMEDPM = true;
        if (this.coverageMedicalPaymentsOptions.length && this.coverageMedicalPaymentsOptions[0].id !== NONE) {
          this.coverageMedicalPaymentsOptions = [{id: NONE, text: NONE}, ...this.coverageMedicalPaymentsOptions];
        }
        this.updateCoverageAPI('MEDPM', NONE);
      } else {
        this.lockMEDPM = false;
        setTimeout( () => {
          const medpm = this.quoteCoveragesItems.coverages.find( item => item.coverageCode === 'MEDPM' );
          if (medpm && medpm.values && medpm.values.length && medpm.values[0] && medpm.values[0].value === NONE) {
            this.updateCoverageAPI('MEDPM', '1000');
            if (this.coverageMedicalPaymentsOptions.length && this.coverageMedicalPaymentsOptions[0].id === NONE) {
              this.coverageMedicalPaymentsOptions = this.coverageMedicalPaymentsOptions.splice(1, this.coverageMedicalPaymentsOptions.length - 1);
            }
          }
        }, 100);
      }
    }
  }

  private updateCoverageAPI(coverageCode, value) {
    if (this.quoteCoveragesItems && this.quoteCoveragesItems.coverages && this.quoteCoveragesItems.coverages.length) {
      let inCoverages = false;
      this.quoteCoveragesItems.coverages = this.quoteCoveragesItems.coverages.map( (item) => {
        if (item.coverageCode === coverageCode) {
          let val = new CoverageValue();
          val.value = value;
          item.values = [val];
          inCoverages = true;
        }
        return item;
      });
      if (!inCoverages) {
        let newCoverage = new Coverage();
        newCoverage.values.push(new CoverageValue());
        newCoverage.coverageCode = coverageCode;
        newCoverage.values[0].value = value;
        this.quoteCoveragesItems.coverages.push(newCoverage);
      }
    } else {
      let newCoverage = new Coverage();
      newCoverage.values.push(new CoverageValue());
      newCoverage.coverageCode = coverageCode;
      newCoverage.values[0].value = value;
      this.quoteCoveragesItems.coverages.push(newCoverage);
    }
    this.coverageDwellingUpdate(coverageCode, value);
    this.updateCoveragesAPI(this.quoteCoveragesItems);
  }

  private updateCoveragesAPI(coveragesObj: CoveragesData) {
    this.overlayLoaderService.showLoader();

    // this.apiCommonService.putByUri(coveragesObj.meta.href, coveragesObj).take(1).subscribe( res => {
    //   this.storageService.setStorageData('homeQuoteCoverages', res);
    //   this.assignQuoteCoverages();
    //   this.overlayLoaderService.hideLoader();
    // }, err => this.overlayLoaderService.hideLoader());


    // Remove coverages without values, to remove (reset) them from API
    // It's forced by the way API works
    const coveragesToSendToAPI: Coverage[] = coveragesObj.coverages.filter((el: Coverage) => {
      return el.values && el.values.length && el.values[0] && el.values[0].value;
    });

    const dataToSend: CoveragesData = JSON.parse(JSON.stringify(coveragesObj));
    dataToSend.coverages = coveragesToSendToAPI;

    this.apiCommonService.putByUri(coveragesObj.meta.href, dataToSend).pipe(take(1)).subscribe( res => {
      const tmpRes = new CoverageApiResponseData();
      tmpRes.items = [res];
      this.storageService.setStorageData('homeQuoteCoverages', tmpRes);

      this.assignQuoteCoverages();
      this.overlayLoaderService.hideLoader();
    }, err => this.overlayLoaderService.hideLoader());
  }

  public decideOnDefaultCoverages(type: string): void {
    switch (type) {
      case 'leave':
        this.updateCoveragesAPI(this.quoteCoveragesItemsTmp);
        break;
      case 'all':
        if (this.quote.policyType === 'Homeowner') {
          this.quoteCoveragesItemsTmp.coverages.forEach( item => {
            if (this.updatingCoverageCode.coverageCode === 'DWELL') {                                 // 10
              if (item.coverageCode === 'OS') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value / 10).toString();  // 1
              }
              if (item.coverageCode === 'PP') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value / 2).toString();   // 5
              }
              if (item.coverageCode === 'LOU') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value / 5).toString();   // 2
              }
            }
            if (this.updatingCoverageCode.coverageCode === 'OS') {
              if (item.coverageCode === 'DWELL') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value * 10).toString();
              }
              if (item.coverageCode === 'PP') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value * 5).toString();
              }
              if (item.coverageCode === 'LOU') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value * 2).toString();
              }
            }
            if (this.updatingCoverageCode.coverageCode === 'PP') {
              if (item.coverageCode === 'DWELL') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value * 2).toString();
              }
              if (item.coverageCode === 'OS') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value / 5).toString();
              }
              if (item.coverageCode === 'LOU') {
                item.values[0].value = Math.round((+this.updatingCoverageCode.value / 5) * 2).toString();
              }
            }
            if (this.updatingCoverageCode.coverageCode === 'LOU') {
              if (item.coverageCode === 'DWELL') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value * 5).toString();
              }
              if (item.coverageCode === 'OS') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value / 2).toString();
              }
              if (item.coverageCode === 'PP') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value * 2.5).toString();
              }
            }
          });
        }
        if (this.quote.policyType === 'Condo' || this.quote.policyType === 'Tenant') {
          let times = 0.4;
          if (this.quote.policyType === 'Tenant') {
            times = 0.2;
          }
          this.quoteCoveragesItemsTmp.coverages.forEach( item => {
            if (this.updatingCoverageCode.coverageCode === 'PP') {
              if (item.coverageCode === 'LOU') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value * times).toString();
              }
            }
          });
        }
        this.updateCoveragesAPI(this.quoteCoveragesItemsTmp);
        break;
      case 'unmodified':
        this.quoteCoveragesItemsTmp.coverages.forEach( item => {
          let inNotDefault = false;
          this.coveragesNotDefaultPercentages.forEach( defaultItem => {
            if (defaultItem.coverageCode === item.coverageCode) {
              inNotDefault = true;
            }
          });
          if (!inNotDefault) {
            if (this.updatingCoverageCode.coverageCode === 'DWELL') {                                 // 10
              if (item.coverageCode === 'OS') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value / 10).toString();  // 1
              }
              if (item.coverageCode === 'PP') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value / 2).toString();   // 5
              }
              if (item.coverageCode === 'LOU') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value / 5).toString();   // 2
              }
            }
            if (this.updatingCoverageCode.coverageCode === 'OS') {
              if (item.coverageCode === 'DWELL') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value * 10).toString();
              }
              if (item.coverageCode === 'PP') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value * 5).toString();
              }
              if (item.coverageCode === 'LOU') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value * 2).toString();
              }
            }
            if (this.updatingCoverageCode.coverageCode === 'PP') {
              if (item.coverageCode === 'DWELL') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value * 2).toString();
              }
              if (item.coverageCode === 'OS') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value / 5).toString();
              }
              if (item.coverageCode === 'LOU') {
                item.values[0].value = Math.round((+this.updatingCoverageCode.value / 5) * 2).toString();
              }
            }
            if (this.updatingCoverageCode.coverageCode === 'LOU') {
              if (item.coverageCode === 'DWELL') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value * 5).toString();
              }
              if (item.coverageCode === 'OS') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value / 2).toString();
              }
              if (item.coverageCode === 'PP') {
                item.values[0].value = Math.round(+this.updatingCoverageCode.value * 2.5).toString();
              }
            }
          }
        });
        this.updateCoveragesAPI(this.quoteCoveragesItemsTmp);
        break;
    }
  }

  private dwellFieldsToUpdate = ['DWELL', 'OS', 'PP', 'LOU'];
  private coverageDwellingUpdate(coverageCode, dwellValue) {
    if (coverageCode === 'DWELL' && this.quote.policyType === 'Homeowner') {
      this.quoteCoveragesItems.coverages = this.quoteCoveragesItems.coverages.map( (item) => {
        if (item.coverageCode === 'OS') {
          item.values[0].value = Math.round(+dwellValue / 10).toString();
        }
        if (item.coverageCode === 'PP') {
          item.values[0].value = Math.round(+dwellValue / 2).toString();
        }
        if (item.coverageCode === 'LOU') {
          item.values[0].value = Math.round(+dwellValue / 5).toString();
        }
        return item;
      });
      if (this.quoteCoveragesItems.coverages.length) {
        let missingCoverages = ['DWELL', 'OS', 'PP', 'LOU'];
        this.dwellFieldsToUpdate.forEach( (dftu) => {
          this.quoteCoveragesItems.coverages.forEach( (item) => {
            let coverageInd = missingCoverages.indexOf(item.coverageCode);
            if (coverageInd > -1) {
              missingCoverages.splice(coverageInd, 1);
            }
          });
        });
        missingCoverages.forEach( item => {
          const newCoverage = new Coverage();
          newCoverage.values.push(new CoverageValue());
          newCoverage.coverageCode = item;
          if (item === 'OS') {
            newCoverage.values[0].value = Math.round(+dwellValue / 10).toString();
          }
          if (item === 'PP') {
            newCoverage.values[0].value = Math.round(+dwellValue / 2).toString();
          }
          if (item === 'LOU') {
            newCoverage.values[0].value = Math.round(+dwellValue / 5).toString();
          }
          this.quoteCoveragesItems.coverages.push(newCoverage);
        });
      }
    }
    if (coverageCode === 'PP' && (this.quote.policyType === 'Condo' || this.quote.policyType === 'Tenant')) {
      let times = 0.4;
      if (this.quote.policyType === 'Tenant') {
        times = 0.2;
      }
      this.quoteCoveragesItems.coverages = this.quoteCoveragesItems.coverages.map( (item) => {
        if (item.coverageCode === 'LOU') {
          item.values[0].value = Math.round(+dwellValue * times).toString();
        }
        return item;
      });
      if (this.quoteCoveragesItems.coverages.length) {
        const missingCoverages = ['LOU'];
        this.dwellFieldsToUpdate.forEach( (dftu) => {
          this.quoteCoveragesItems.coverages.forEach( (item) => {
            const coverageInd = missingCoverages.indexOf(item.coverageCode);
            if (coverageInd > -1) {
              missingCoverages.splice(coverageInd, 1);
            }
          });
        });
        missingCoverages.forEach( item => {
          const newCoverage = new Coverage();
          newCoverage.values.push(new CoverageValue());
          newCoverage.coverageCode = item;
          if (item === 'LOU') {
            newCoverage.values[0].value = Math.round(+dwellValue * times).toString();
          }
          this.quoteCoveragesItems.coverages.push(newCoverage);
        });
      }
    }
  }


  private getCoverages() {
    this.subscribtionGetCoverages = this.storageService.getStorageData('homeStandardCoveragesList').subscribe( response => {
      if (response.length) {
        this.coverages = response;
        this.initializeCoverages(this.coverages);
      }
    });
  }

  private initializeCoverages(coverages: CoverageItem[]) {
    // assign coverages
    // this.coverageDwelling = this.findCoverageByCode(coverages, 'DWELL');
    // this.coverageOtherStructures = this.findCoverageByCode(coverages, 'OS');
    // this.coveragePersonalProperty = this.findCoverageByCode(coverages, 'PP');
    // this.coverageLossOfUse = this.findCoverageByCode(coverages, 'LOU');
    // this.coverageFairRentalValue = this.findCoverageByCode(coverages, 'FRV');

    // set select options
    this.coveragePersonalLiabilityOptions = this.formatCoverageValues([{value: NONE}, {value: '100000'}, {value: '200000'}, {value: '300000'}, {value: '400000'}, {value: '500000'}, {value: '1000000'}], true);
    this.coverageMedicalPaymentsOptions = this.formatCoverageValues([{value: NONE}, {value: '1000'}, {value: '2000'}, {value: '3000'}, {value: '4000'}, {value: '5000'}, {value: '10000'}], true);
    this.coverageAllPerilsOptions = this.formatCoverageValues([{value: '100'}, {value: '250'}, {value: '500'}, {value: '1000'}, {value: '2500'}, {value: '5000'}, {value: '10000'}]);
    this.coverageWindHailOptions = this.formatCoverageValues([{value: NONE}, {value: '2000'}, {value: '5000'}, {value: '1%'}, {value: '2%'}, {value: '3%'}, {value: '4%'}, {value: '5%'}]);

    this.coveragesInitialized = true;

    if (this.quoteCoveragesItems && this.quoteCoveragesItems.coverages && this.quoteCoveragesItems.coverages.length) {
      this.assignQuoteCoverages();
    }
  }

  private findCoverageByCode(coverages: CoverageItem[], code: string): CoverageItem {
    let result: CoverageItem = new CoverageItem();

    if (!coverages || !code) { return result; }

    coverages.map(coverage => {
      if (coverage.coverageCode === code) {
        return result = coverage;
      }
    });

    return result;
  }

  private assignQuoteCoverages() {
    this.coverageQuoteDwelling = this.prepareCoverageData(this.quoteCoveragesItems.coverages, 'DWELL');
    this.coverageQuoteOtherStructures = this.prepareCoverageData(this.quoteCoveragesItems.coverages, 'OS');
    this.coverageQuotePersonalProperty = this.prepareCoverageData(this.quoteCoveragesItems.coverages, 'PP');
    this.coverageQuoteLossOfUse = this.prepareCoverageData(this.quoteCoveragesItems.coverages, 'LOU');
    this.coverageQuotePersonalLiability = this.prepareCoverageData(this.quoteCoveragesItems.coverages, 'PL', true);
    this.coverageQuoteMedicalPayments = this.prepareCoverageData(this.quoteCoveragesItems.coverages, 'MEDPM', true);
    this.coverageQuoteAllPerils = this.prepareCoverageData(this.quoteCoveragesItems.coverages, 'APDED');
    this.coverageQuoteWindHail = this.prepareCoverageData(this.quoteCoveragesItems.coverages, 'WHDED');
    this.coverageQuoteSpecialCoverage = this.prepareCoverageData(this.quoteCoveragesItems.coverages, 'AASPC');
    this.coverageQuoteFairRentalValue = this.prepareCoverageData(this.quoteCoveragesItems.coverages, 'FRV');

    this.optionLossFreeYears = this.prepareCoverageData(this.quoteCoveragesItems.coverages, 'BSC_LOSSFREEYRS');
  }

  private dropdownCoverages = ['PL', 'MEDPM', 'RCDWELL', 'APDED', 'WHDED'];
  private prepareCoverageData(coverages, code: string, isMoney: boolean = false): string|any {
    let result: any = '';

    coverages.filter(coverage => {
      if (coverage.coverageCode === code) {
        if (coverage.values && coverage.values.length) {
          if (this.dropdownCoverages.indexOf(code) > -1) {
            result = {
              id: coverage.values[0].value,
              text: coverage.values[0].value
            };
            if (isMoney) {
              result = {
                id: coverage.values[0].value,
                text: this.moneyService.formatToMoney(coverage.values[0].value)
              };
            }
          } else {
            result = coverage.values[0].value > 0 ? coverage.values[0].value : '' ;
          }
        }
      }
    });

    return result;
  }

  private formatCoverageValues(values: any[], isMoney: boolean = false): FilterOption[] {
    let tempArr: FilterOption[] = [];

    if (!values || !values.length) { return tempArr; }

    tempArr = values.map((item, index) => {
      let result = {
        text: item.value,
        id: item.value
      };
      if (isMoney) {
        result = {
          id: item.value,
          text: this.moneyService.formatToMoney(item.value)
        };
      }
      return result;
    });

    return tempArr;
  }

  // Options

  private getQuoteOptions() {
    this.subscriptionQuoteOptions = this.storageService.getStorageData('dwellingQuoteOptions').subscribe( data => {
      let quoteOptionsUrl = '';
      if (this.quote && this.quote.options && this.quote.options.href) {
        quoteOptionsUrl = this.quote.options.href;
      } else {
        quoteOptionsUrl = this.quote.coverages.href;
      }
      if (data && data.meta && data.meta.href) {
        this.quoteOptions = JSON.parse(JSON.stringify(data));
        if (this.quoteOptions && this.quoteOptions.items && this.quoteOptions.items.length) {
          this.quoteOptionsItems = this.quoteOptions.items[0];
          this.assignQuoteOptions();
        } else {
          this.overlayLoaderService.showLoader();
          this.optionsService.createDwellingOptionsByUriWithDefaultValues(quoteOptionsUrl).pipe(
            take(1))
            .subscribe((res: CoveragesData) => {
              this.quoteOptions.items = [res];
              const tmpRes = new CoverageApiResponseData();
              tmpRes.items = [res];
              // this.storageService.setStorageData('dwellingQuoteOptions', tmpRes);
              this.storageService.setStorageDataDwellingQuoteOptions(tmpRes);
              this.overlayLoaderService.hideLoader();
            },
            err => {
              console.log(err);
              this.overlayLoaderService.hideLoader();
            });
        }
      } else {
        this.optionsService.getDwellingOptions$(quoteOptionsUrl).pipe(take(1)).subscribe();
      }
    });
  }

  private prepareExtendedCoverageData(coverages, code: string): string|any {
    const result: any = '';

    const ec = coverages.find(coverage => coverage.coverageCode === code);
    const vmmCode = this.extendedCodes.find( codes => codes[code] );
    const vmm = coverages.find(coverage => coverage.coverageCode === vmmCode[code]);

    let valueToShow;
    if (ec && ec.values && ec.values.length && ec.values[0].value === 'true') {
      valueToShow = this.coverageExtendedOptions[1];
    }
    if (vmm && vmm.values && vmm.values.length && vmm.values[0].value === 'true') {
      valueToShow = this.coverageExtendedOptions[2];
    }
    if ((ec && ec.values && ec.values.length && ec.values[0].value === 'true') && (vmm && vmm.values && vmm.values.length && vmm.values[0].value === 'true')) {
      valueToShow = this.coverageExtendedOptions[3];
    }

    return valueToShow;
  }

  private assignQuoteOptions() {
    this.optionQuoteFRV = this.prepareCoverageData(this.quoteOptionsItems.coverages, 'FRV', true);

    this.optionQuoteBSCEC = this.prepareExtendedCoverageData(this.quoteOptionsItems.coverages, 'BSC_EC');
    this.optionQuoteBSCOSEC = this.prepareExtendedCoverageData(this.quoteOptionsItems.coverages, 'BSC_OSEC');
    this.optionQuoteBSCPPEC = this.prepareExtendedCoverageData(this.quoteOptionsItems.coverages, 'BSC_PPEC');
    this.optionQuoteBSCINFRV = this.prepareExtendedCoverageData(this.quoteOptionsItems.coverages, 'BSC_INFRV');
    this.optionQuoteBSCLOUEC = this.prepareExtendedCoverageData(this.quoteOptionsItems.coverages, 'BSC_LOUEC');

    this.optionInflation = this.prepareCoverageData(this.quoteOptionsItems.coverages, 'BSC-DFIRE-000243');
  }

  private updateOptions(coverageCode, value, removeValue: boolean = false) {
    if (this.quoteOptionsItems && this.quoteOptionsItems.coverages && this.quoteOptionsItems.coverages.length) {
      let inCoverages = false;
      this.quoteOptionsItems.coverages = this.quoteOptionsItems.coverages.map( (item) => {
        if (item.coverageCode === coverageCode) {
          if (item.values && item.values[0] && item.values[0].value) {
            item.values[0].value = value;
          } else {
            item.values = [{value: value}];
          }
          if (removeValue) {
            item.values = [];
          }
          inCoverages = true;
        }
        return item;
      });
      if (!inCoverages) {
        const newCoverage = new Coverage();
        newCoverage.values.push(new CoverageValue());
        newCoverage.coverageCode = coverageCode;
        if (!removeValue) {
          newCoverage.values[0].value = value;
        }
        this.quoteOptionsItems.coverages.push(newCoverage);
      }
    } else {
      const newCoverage = new Coverage();
      newCoverage.values.push(new CoverageValue());
      newCoverage.coverageCode = coverageCode;
      if (!removeValue) {
        newCoverage.values[0].value = value;
      }
      this.quoteOptionsItems.coverages.push(newCoverage);
    }
  }

  private extendedCodes = [{'BSC_EC': 'BSC_VMM'}, {'BSC_OSEC': 'BSC_OSVMM'}, {'BSC_PPEC': 'BSC_PPVMM'}, {'BSC_INFRV': 'BSC_FRVVMM'}, {'BSC_LOUEC': 'BSC_LOUVMM'}];
  public updateQuoteOption(coverageCode: string, value, coverageCodeExt: boolean = false) {
    if (!coverageCodeExt) {
      if (value && value.indexOf('$') > -1) {
        value = value.replace('$', '').replace(/,/g, '');
      }
      this.updateOptions(coverageCode, value);
    } else {
      const vvmField = this.extendedCodes.find( codes => codes[coverageCode]);
      if (value === null || value === 'null') {
        this.updateOptions(coverageCode, null, true);
        this.updateOptions(vvmField[coverageCode], null, true);
      } else if (value === this.coverageExtendedOptions[1]) {
        this.updateOptions(coverageCode, 'true');
        this.updateOptions(vvmField[coverageCode], null, true);
      } else if (value === this.coverageExtendedOptions[2]) {
        this.updateOptions(coverageCode, null, true);
        this.updateOptions(vvmField[coverageCode], 'true');
      } else if (value === this.coverageExtendedOptions[3]) {
        this.updateOptions(coverageCode, 'true');
        this.updateOptions(vvmField[coverageCode], 'true');
      }
    }
    this.updateOptionsAPI(this.quoteOptionsItems);
  }

  private updateOptionsAPI(coveragesObj) {
    this.overlayLoaderService.showLoader();

    // this.apiCommonService.putByUri(coveragesObj.meta.href, coveragesObj).take(1).subscribe( res => {
    //   this.quoteOptions.items = [JSON.parse(JSON.stringify(res))];
    //   this.storageService.setStorageData('dwellingQuoteOptions', this.quoteOptions);
    //   this.assignQuoteOptions();
    //   this.overlayLoaderService.hideLoader();
    // }, err => this.overlayLoaderService.hideLoader());

    // Remove coverages without values, to remove (reset) them from API
    // It's forced by the way API works
    const coveragesToSendToAPI: Coverage[] = coveragesObj.coverages.filter((el: Coverage) => {
      return el.values && el.values.length && el.values[0] && el.values[0].value;
    });

    const dataToSend: CoveragesData = JSON.parse(JSON.stringify(coveragesObj));
    dataToSend.coverages = coveragesToSendToAPI;

    this.apiCommonService.putByUri(coveragesObj.meta.href, dataToSend).pipe(take(1)).subscribe( res => {
      this.quoteOptions.items = [JSON.parse(JSON.stringify(res))];
      // this.storageService.setStorageData('dwellingQuoteOptions', this.quoteOptions);
      this.storageService.setStorageDataDwellingQuoteOptions(this.quoteOptions);
      this.assignQuoteOptions();
      this.overlayLoaderService.hideLoader();
    }, err => this.overlayLoaderService.hideLoader());
  }

  // HELPER - textMask Input change event issue workaround
  // ----------------------------------------------------------------------------
  public helperOnInputFocusTextMaskChangeEventIssueWorkaround(event: Event): void {
    const currentInput = <HTMLInputElement>this.getElFromEvent(event);
    if (event.type === 'focus') {
      this.helperTextMaskChangeEventIssueWorkaroundInputValueDuringFocus = currentInput.value;
    }
  }

  public helperOnInputBlurTextMaskChangeEventIssueWorkaroundUpdateQuoteCoverage(event: Event, coverageCode: string, value: string): void {
    const currentInput = <HTMLInputElement>this.getElFromEvent(event);
    if (event.type === 'blur') {
      if (this.helperTextMaskChangeEventIssueWorkaroundInputValueDuringFocus !== currentInput.value) {
        // this.updateQuoteCoverage(coverageCode, value);
        this.updateQuoteCoverage(coverageCode, currentInput.value);
      }
    }
  }

  public helperOnInputBlurTextMaskChangeEventIssueWorkaroundUpdateQuoteOption(event: Event, coverageCode: string, value: string, coverageCodeExt: boolean = false): void {
    const currentInput = <HTMLInputElement>this.getElFromEvent(event);
    if (event.type === 'blur') {
      if (this.helperTextMaskChangeEventIssueWorkaroundInputValueDuringFocus !== currentInput.value) {
        // this.updateQuoteCoverage(coverageCode, value);
        this.updateQuoteOption(coverageCode, currentInput.value, coverageCodeExt);
      }
    }
  }

  protected getElFromEvent(ev): HTMLElement {
    return ev.srcElement || ev.target;
  }

  // https://bostonsoftware.atlassian.net/browse/SPR-2083
  // ----------------------------------------------------------------------------

  // SPR-2083 - Loss of Use
  @ViewChild('refDwelling') refDwelling: ElementRef;
  @ViewChild('refModalLossOfUse') refModalLossOfUse: ModalboxComponent;

  private lossOfUsePreviousValue = '';
  private lossOfUseAdjustedValue = '';
  public lossOfUseModalMessage = '';
  public lossOfUseValidationOnFocus(): void {
    this.lossOfUseModalMessage = '';
    this.lossOfUsePreviousValue = this.coverageQuoteLossOfUse ? this.coverageQuoteLossOfUse.replace('$', '').replace(/,/g, '') : this.coverageQuoteLossOfUse;
    this.lossOfUseAdjustedValue = this.lossOfUsePreviousValue;
  }

  // According to https://bostonsoftware.atlassian.net/browse/SPR-2443 - the validation is not in use
  // public lossOfUseValidationOnBlur(event: Event, coverageCode: string, value: string): void {
  //   if (this.coverageQuoteLossOfUse.indexOf('$') > -1) {
  //     this.coverageQuoteLossOfUse = this.coverageQuoteLossOfUse.replace('$', '').replace(/,/g, '');
  //   }

  //   if (this.coverageQuoteDwelling.indexOf('$') > -1) {
  //     this.coverageQuoteDwelling = this.coverageQuoteDwelling.replace('$', '').replace(/,/g, '');
  //   }

  //   const dwellingValue = parseInt(this.coverageQuoteDwelling, 10);
  //   const previousValue: number = parseInt(this.lossOfUsePreviousValue, 10);
  //   const currentValue: number = parseInt(this.coverageQuoteLossOfUse, 10);

  //   if (!isNaN(dwellingValue)) {
  //     const minPercentDwellingValue: number = dwellingValue * 0.2;

  //     if (!currentValue || isNaN(currentValue)) {
  //       // Adjust value
  //       this.lossOfUseAdjustedValue = minPercentDwellingValue.toString();
  //       this.lossOfUseModalMessage = MODAL_LOU_MESSAGES.minimalValue;
  //     } else if (!isNaN(currentValue) && currentValue < minPercentDwellingValue) {
  //       this.lossOfUseModalMessage = MODAL_LOU_MESSAGES.percentageWrong;
  //     }

  //   } else if (!dwellingValue || isNaN(dwellingValue)) {
  //     this.lossOfUseModalMessage = MODAL_LOU_MESSAGES.setDwellingValue;//'Please first set correct value for Dwelling field.';
  //   }

  //   // If there is a message, show modal and do not update API
  //   if (this.lossOfUseModalMessage && this.refModalLossOfUse) {
  //     this.refModalLossOfUse.open();
  //   } else {
  //     this.helperOnInputBlurTextMaskChangeEventIssueWorkaroundUpdateQuoteCoverage(event, coverageCode, this.coverageQuoteLossOfUse);
  //   }
  // }

  public actionModalLossOfUseOk(ev: Event): void {
    ev.preventDefault();
    this.refModalLossOfUse && this.refModalLossOfUse.close();
  }

  public actionModalLossOfUseStateChange(event, refModalBtnOk: HTMLButtonElement, refLosOfUseInput: HTMLInputElement): void {

    if (event.isOpen === false) {
      this.coverageQuoteLossOfUse = this.lossOfUseAdjustedValue;

      if (this.lossOfUseModalMessage === MODAL_LOU_MESSAGES.setDwellingValue) {
        this.refDwelling && this.refDwelling.nativeElement.focus();
      } else {
        refLosOfUseInput && refLosOfUseInput.focus();
      }

    } else if (event.isOpen === true) {
      refModalBtnOk && refModalBtnOk.focus();
    }
  }


  // SPR-2083 - Wind/Hail
  public windHailFieldIsInvalid(): boolean {
    let isInvalid = false;

    let windHailCurrentValue: string = null;
    let windHailCurrentValueIsPercent = false;

    let windHailValue = 0;
    let windHailCalculatedValue = 0;
    let allPerilsValue = 0;
    let dwellingValue = 0;

    if (this.coverageQuoteAllPerils && this.coverageQuoteAllPerils.id) {
      const tmpVal: any = parseInt(this.coverageQuoteAllPerils.id, 10);
      allPerilsValue = !isNaN(tmpVal) ? tmpVal : 0;
    }

    if (this.coverageQuoteDwelling) {
      let tmpVal: any = this.coverageQuoteDwelling.replace('$', '').replace(',', '');
      tmpVal = parseInt(tmpVal, 10);
      dwellingValue = !isNaN(tmpVal) ? tmpVal : 0;
    }

    if (this.coverageQuoteWindHail && this.coverageQuoteWindHail.id) {
      windHailCurrentValue = this.coverageQuoteWindHail.id;
    }

    if (windHailCurrentValue === 'None') {
      isInvalid = false;
      return false;
    } else if (windHailCurrentValue && windHailCurrentValue.indexOf('%') !== -1) {
      windHailCurrentValueIsPercent = true;
      windHailValue = parseInt(windHailCurrentValue.replace('%', '').trim(), 10);
      windHailCalculatedValue = !isNaN(windHailValue) ? dwellingValue * (windHailValue / 100) : 0;
    } else {
      windHailValue = parseInt(windHailCurrentValue, 10);
      windHailCalculatedValue = !isNaN(windHailValue) ? windHailValue : 0;
    }

    if (windHailCalculatedValue <= allPerilsValue) {
      isInvalid = true;
    }

    // If value for Dwelling field is not set, mark Wind / Hail as valid
    if (windHailCurrentValueIsPercent && !dwellingValue) {
      isInvalid = false;
    }

    return isInvalid;
  }
  // # END: SPR-2083

}
