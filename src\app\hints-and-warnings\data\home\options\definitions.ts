import { StorageService } from 'app/shared/services/storage-new.service';
import { AdditionalDataI, WARNING_GROUPS, WarningDefinitionI } from 'app/hints-and-warnings/model/warnings';
import { CoverageItemParsed, CoverageItemChild } from 'app/app-model/coverage';
import { Validate } from 'app/hints-and-warnings/validators';
import { generateViewFieldIds } from '../../../../../testing/helpers/warning-definitions';

// Common
// ------------------------------------------------------------------------------
function validPolicyItemParsed(value, fullObj: CoverageItemParsed): boolean {
    if (fullObj.coverageCode === 'BSC-HOME-010067') { return false; }
  return fullObj.isRequired && !fullObj.isActive && !fullObj.isDisabled;
}

function generateUqOptionViewId(policy: CoverageItemParsed): string {
  return policy.viewUqId;
}

function generateUqOptionViewInteractId(policy: CoverageItemParsed): string {
  return policy.viewUqId + '_modal-launcher';
  // return policy.viewUqId + '_checkbox';
}


// Home Options Tab, Carrier Options
// ------------------------------------------------------------------------------
/**
 * Validation for Home Carrier Options Data
 * For Model: CoverageItemParsed
 */

function generateViewUrlCarrierOptions(fullObj: CoverageItemParsed): string {
  return '/dashboard/home/<USER>/' + fullObj.quoteResourceId + '/options';
}

const policyItemHomeParsedCarrierOptionLoss: WarningDefinitionI = {
  id: 'currentValue',
  deepId: 'currentValue',
  viewUri: generateViewUrlCarrierOptions,
  viewFieldId: generateUqOptionViewId,
  viewFieldInteractId: generateUqOptionViewInteractId,
  warnings: [
    {
      label: (val, fullObj) => 'Required ' + fullObj.description  + '.',
      condition: (val, fullObj: CoverageItemParsed , additionalData) => {
        let returnValue = false;
        if (fullObj.coverageCode === 'BSC-HOME-023207') {
          const lossFreeYears = additionalData && additionalData.lossFreeYears ? +additionalData.lossFreeYears.replace('_', '') : 5;

          const lossesYear1 = additionalData && additionalData.lossesYear1 ? +additionalData.lossesYear1.replace('_', '') : 0;
          const lossesYear2 = additionalData && additionalData.lossesYear2 ? +additionalData.lossesYear2.replace('_', '') : 0;
          const lossesYear3 = additionalData && additionalData.lossesYear3 ? +additionalData.lossesYear3.replace('_', '') : 0;
          const lossesYear4 = additionalData && additionalData.lossesYear4 ? +additionalData.lossesYear4.replace('_', '') : 0;
          const lossesYear5 = additionalData && additionalData.lossesYear5 ? +additionalData.lossesYear5.replace('_', '') : 0;

          const totalLosses = lossesYear1 + lossesYear2 + lossesYear3 + lossesYear4 + lossesYear5;

            if (lossFreeYears === 0 && totalLosses > 0 && fullObj.isActive === false) {
              returnValue = true;
            } else if (lossFreeYears === 1 && totalLosses > 0 && fullObj.isActive === false) {
              returnValue = true;
            } else if (lossFreeYears === 2 && totalLosses > 0 && fullObj.isActive === false) {
              returnValue = true;
            } else if (lossFreeYears === 3 && totalLosses > 0 && fullObj.isActive === false) {
              returnValue = true;
            } else if (lossFreeYears === 4 && totalLosses > 0) {
              returnValue = true;
            } else if (lossFreeYears === 5) {
              returnValue = false;
            }


          fullObj.isRequired = returnValue;
          console.log(fullObj, returnValue);
          return returnValue;
        }
      },
      carriers: ['105'],
      group: WARNING_GROUPS.carrier
    },
  ]
};


const policyItemHomeParsedCarrierOption: WarningDefinitionI = {
  id: 'currentValue',
  deepId: 'currentValue',
  viewUri: generateViewUrlCarrierOptions,
  viewFieldId: generateUqOptionViewId,
  viewFieldInteractId: generateUqOptionViewInteractId,
  warnings: [
    {
       label: (val, fullObj: CoverageItemParsed) => 'Required ' + fullObj.description  + '.',
      condition: (val, fullObj: CoverageItemParsed, additionalData) => {
        if (fullObj.coverageCode === 'BSC-HOME-000142-000075') {
        const watercraft = additionalData.find(c => c.coverageCode === 'BSC-HOME-000075');
        let hasValue;
        let returnValue = false;
       fullObj.isDisabled = watercraft.isActive ? false : true;
       if (fullObj.isDisabled) {
         fullObj.currentValue = '';
         fullObj.currentValueData.keyValue = [];
         fullObj.currentValueData.values = [];
         fullObj.isActive = false;
       }
        if (watercraft.isActive) {
        watercraft.values.forEach(element => {
          if (element.currentValue !== '0') {
            hasValue = true;
          }
        });
      }

        let childHasValue;
        fullObj.currentValueData.keyValue.forEach(element => {
         if (element[1] !== '0') {
           childHasValue = true;
         }
        });
        if (fullObj.isActive && !childHasValue && hasValue) {
          returnValue = true;
        } else if (!fullObj.isActive && hasValue) {
          returnValue = true;
        } else {
          returnValue = false;
        }
        return returnValue;
      }


      },
      group: WARNING_GROUPS.carrier,
      carriers: ['89', '90']
    },
    {
      label: (val, fullObj: CoverageItemParsed) => 'Required ' + fullObj.description  + '.',
     condition: (val, fullObj: CoverageItemParsed, additionalData) => {

       if (fullObj.coverageCode === 'BSC-HOME-022828' ) {
      let returnValue = true;
     const foundItem = fullObj.currentValueData.keyValue.find(x => x[1] !== '0');
      returnValue = foundItem && foundItem.length > 0 ? false : true;
       return returnValue;
     }


     },
     group: WARNING_GROUPS.carrier,
     carriers: ['174']
   }
    , {
    label: (val, fullObj: CoverageItemParsed) => 'Required ' + fullObj.description  + '.',
    condition: validPolicyItemParsed,
    group: WARNING_GROUPS.carrier,
    carriers: (fullObj: CoverageItemParsed) => {
      if (fullObj.additionalData && fullObj.additionalData.requiredForCarriers && fullObj.additionalData.requiredForCarriers.length) {
        return fullObj.additionalData.requiredForCarriers;
      } else {
        return fullObj.ratingPlanId ? fullObj.ratingPlanId.split(',') : [];
      }
    }
  },
  {
    label: (val, fullObj: CoverageItemParsed) => 'Option: ' + fullObj.description  + ' is selected but no values are chosen for the option.', // https://bostonsoftware.atlassian.net/browse/SPR-2580
    condition: (val, fullObj: CoverageItemParsed) => {
      const item: any = fullObj.values[0];
      if ((fullObj.coverageCode === 'BSC-HOME-023036' || fullObj.coverageCode === 'BSC-HOME-25091') && item.currentValue === 'No') { return false; } else {
          return Validate.coverageItemParsedErrorIfActiveAndNoSubOptionsSelected(fullObj);
      }


    },
    group: WARNING_GROUPS.carrier,
    carriers: (fullObj: CoverageItemParsed) => {
      if (fullObj.additionalData && fullObj.additionalData.requiredForCarriers && fullObj.additionalData.requiredForCarriers.length) {
        return fullObj.additionalData.requiredForCarriers;
      } else {
        return fullObj.ratingPlanId ? fullObj.ratingPlanId.split(',') : [];
      }
    }
  },
  {
    label: (val, fullObj: CoverageItemParsed) => {
      if (fullObj.additionalData && fullObj.additionalData.errorMessage) {
        return fullObj.additionalData.errorMessage;
      } else {
        return 'Option: ' + fullObj.description + ' has some error.';
      }
    },
    condition: (val, fullObj: CoverageItemParsed) => {
      if (fullObj.additionalData && fullObj.additionalData.hasError) {
        return true;
      }

      return false;
    },
    group: WARNING_GROUPS.carrier,
    carriers: (fullObj: CoverageItemParsed) => {
      if (fullObj.additionalData && fullObj.additionalData.requiredForCarriers && fullObj.additionalData.requiredForCarriers.length) {
        return fullObj.additionalData.requiredForCarriers;
      } else {
        return fullObj.ratingPlanId ? fullObj.ratingPlanId.split(',') : [];
      }
    }
  },
  {
    label: 'Premier not compatible with Security Plus or Security Plus Elite',
    condition: (val, fullObj: CoverageItemParsed, additionalData: AdditionalDataI) => {
      // https://bostonsoftware.atlassian.net/browse/SPRC-516
      // BSC-HOME-023055 -> National General - Premier
      // BSC-HOME-023058 -> National General - Security Plus Elite
      // BSC-HOME-023044 -> National General - Security Plus Endorsement
      if (fullObj.ratingPlanId === '119' && fullObj.coverageCode === 'BSC-HOME-023055' && fullObj.isActive) {
        const relatedOptions = additionalData.observedData
          .filter((opt: CoverageItemParsed) => {
            return opt.coverageCode === 'BSC-HOME-023058' || opt.coverageCode === 'BSC-HOME-023044';
          });

        const isActiveSomRelatedOption = relatedOptions.find(opt => opt.isActive);

        return (isActiveSomRelatedOption) ? true : false;
      }

      return false;
    },
    group: WARNING_GROUPS.carrier,
    carriers: ['119'] // "National General"
  },
  {
    label: 'Safe Property Credit requires consistency with Dog in Household',
    condition: (val, fullObj: CoverageItemParsed, additionalData: AdditionalDataI) => {
      // https://bostonsoftware.atlassian.net/browse/SPR-2508
      // BSC-HOME-023007 -> Bunker Hill - Safe Property Credit
      // BSC-HOME-022992 -> Bunker Hill - There is a dog in the household

      // if (fullObj.ratingPlanId === '113' && fullObj.coverageCode === 'BSC-HOME-023007' && !fullObj.isActive) {
      //   let relatedOption = additionalData.observedData
      //     .find((opt: CoverageItemParsed) => {
      //       return opt.coverageCode === 'BSC-HOME-022992';
      //     });

      //   if (relatedOption) {
      //     console.log('%c RELATED OPTION', 'color:red', relatedOption);
      //   }

      //   return (relatedOption && relatedOption.isActive) ? true : false;
      // }

      if (fullObj.ratingPlanId === '113' && fullObj.coverageCode === 'BSC-HOME-023007') {
        let currentOptionIsWrongValue = false;
        if (fullObj.isActive && fullObj.currentValue.endsWith(' No')) {
          currentOptionIsWrongValue = true;
        }


        const relatedOption = additionalData.observedData
          .find((opt: CoverageItemParsed) => {
            return opt.coverageCode === 'BSC-HOME-022992';
          });

        let checkUpOptionsConsistency = false;

        if (relatedOption) {
          checkUpOptionsConsistency = (relatedOption.isActive && relatedOption.currentValue.endsWith(' Yes')) ? true : false;
        }

        if (checkUpOptionsConsistency && currentOptionIsWrongValue) {
          return true;
        }

      }

      return false;
    },
    group: WARNING_GROUPS.carrier,
    carriers: ['113'] // "Bunker Hill"
  },
  // https://bostonsoftware.atlassian.net/browse/SPR-2554
  {
    label: (val, fullObj: CoverageItemParsed) => 'ASI - Renter Occupied requires response consistency', // 'Required ' + fullObj.description  + '.',
    condition: (val, fullObj: CoverageItemParsed, additionalData: AdditionalDataI) => {
      let hasError = false;

      if (fullObj.coverageCode === 'BSC-HOME-022927' && fullObj.values) {
        const dwellRenterOccupied: CoverageItemChild = <CoverageItemChild>fullObj.values.find((el: CoverageItemChild) => el.description === 'Dwell Renter Occupied:');
        const hasAnnualContract: CoverageItemChild = <CoverageItemChild>fullObj.values.find((el: CoverageItemChild) => el.description === 'Has Annual Contract:');
        const hasShortTermContract: CoverageItemChild = <CoverageItemChild>fullObj.values.find((el: CoverageItemChild) => el.description === 'Has Short Term Contract:');

        if (dwellRenterOccupied.currentValue === 'No') {
          if (hasAnnualContract.currentValue === 'Yes' || hasShortTermContract.currentValue === 'Yes') {
            hasError = true;
          }
        }

        if (dwellRenterOccupied.currentValue === 'Yes') {
          if (hasAnnualContract.currentValue === 'Yes' && hasShortTermContract.currentValue === 'Yes') {
            hasError = true;
          } else if (hasAnnualContract.currentValue === 'No' && hasShortTermContract.currentValue === 'No') {
            hasError = true;
          } else if (hasAnnualContract.currentValue === 'Yes' && hasShortTermContract.currentValue === 'No') {
            hasError = false;
          } else if (hasAnnualContract.currentValue === 'No' && hasShortTermContract.currentValue === 'Yes') {
            hasError = false;
          }
        }

        // console.log('fullObj ASI >>>', fullObj);
        // console.log('fullObj ASI dwellRenterOccupied >>>', dwellRenterOccupied);
        // console.log('fullObj ASI hasAnnualContract >>>', hasAnnualContract);
        // console.log('fullObj ASI hasShortTermContract >>>', hasShortTermContract);

      }

      return hasError;
    },
    group: WARNING_GROUPS.carrier,
    carriers: (fullObj: CoverageItemParsed) => {
      if (fullObj.additionalData && fullObj.additionalData.requiredForCarriers && fullObj.additionalData.requiredForCarriers.length) {
        return fullObj.additionalData.requiredForCarriers;
      } else {
        return fullObj.ratingPlanId ? fullObj.ratingPlanId.split(',') : [];
      }
      // return ['105']; // ASI - AMERICAN STRATEGIC INSURANCE
    }
  },
  // Arbella RTR https://bostonsoftware.atlassian.net/browse/PRODUCT-1985
  {
    label: (val, fullObj) => 'Arbella - Additional Pool Information Required',
    condition: (val, fullObj, additionalData) => {
      if (fullObj.coverageCode === 'BSC-HOME-25110' && fullObj.values) {
        const storageService: StorageService = new StorageService();
    let hasPool;
    let hasError = true;
      storageService.getStorageData('dwelling').subscribe(x => hasPool = x.poolOnPremisesInd);
      const hasdivingBoard: CoverageItemChild = <CoverageItemChild>fullObj.values.find((el: CoverageItemChild) => el.description === 'Diving Board');
      if (!hasPool) {
          hasError = false;
        } else {
          hasError = true;
        }

      if (hasPool && (hasdivingBoard.currentValue === 'Yes') || hasPool && (hasdivingBoard.currentValue === 'No')) {
          hasError = false;
        }

    return hasError;
      }
    },
    group: WARNING_GROUPS.carrier,
    carriers: ['290']
  },
  // PRODUCT-2298
  {
    label: (val, fullObj) => 'Foremost - Is Pool Fenced Required',
    condition: (val, fullObj, additionalData) => {
      const storageService: StorageService = new StorageService();
      let formType;
      storageService.getStorageData('selectedQuote').subscribe(x => formType = x.formType);
      if (fullObj.coverageCode === 'BSC-HOME-25255' && fullObj.values && formType !== 'HO4') {


    let hasPool;
    let hasError = true;
    const answered: CoverageItemChild = <CoverageItemChild>fullObj.values.find((el: CoverageItemChild) => el.description === 'Please select value');
      storageService.getStorageData('dwelling').subscribe(x => hasPool = x.poolOnPremisesInd);
      if (!hasPool) {
          hasError = false;
        } else {
          hasError = true;
        }
        if (hasPool && answered.currentValue) {
          hasError = false;
        }

    return hasError;
      }
    },
    group: WARNING_GROUPS.carrier,
    carriers: ['303', '316']
  },
  {
  label: (val, fullObj: CoverageItemParsed) => 'Required ' + fullObj.description  + '.',
  condition: (val, fullObj: CoverageItemParsed, additionalData: CoverageItemParsed[]) => {
    const isFeatureEnabled = JSON.parse(localStorage.getItem('features')).find(x => x.name === 'SprApp_FairPlanPIC');
    if (!isFeatureEnabled) { return false; }
    if (fullObj.coverageCode !== 'BSC-HOME-010067') { return false; }

    // Find SLS (SPLS) in general options
    const spls = additionalData.find(cov => cov.definedCode === 'SPLS');

    // Get dwelling value from session storage
    let warningResult = false;
    const storageService = new StorageService();
    storageService.getStorageData('homeQuoteCoverages').subscribe((x: any) => {
      const coverages = x.coverages ? x.coverages : (x.items && x.items[0] && x.items[0].coverages ? x.items[0].coverages : []);
    const dwell = coverages.find(c => c.coverageCode === 'DWELL');
      const dwellValue = dwell && dwell.values && dwell.values[0] ? +dwell.values[0].value : 0;
      if (dwellValue <= 1000000) {
        // fullObj.isActive = false;
        // fullObj.isDisabled = true;
        // fullObj.currentValueData.keyValue = [];
        //   fullObj.currentValueData.values = [];
        //   fullObj.currentValue = '';

        return false;
      }
      // If SLS is active, disable PIC
      if (spls && spls.isActive) {
        warningResult = false; // Show warning
      }

      // If dwelling > 1M and PIC is not disabled, require PIC
      if (dwellValue > 1000000 && !spls.isActive) {
        warningResult = !fullObj.isActive;
        fullObj.isRequired = warningResult; // Show warning if not active
      }
      if (spls.isActive) {
        fullObj.isRequired = false;
      }
    });

    return warningResult;
  },
  group: WARNING_GROUPS.carrier,
  carriers: ['33'] // Add carrier IDs if needed
}

  ]
};


export const WARNINGS_DEFINITIONS_HOME_OPTIONS_CARRIER_OPTIONS: WarningDefinitionI[] = [
  policyItemHomeParsedCarrierOption
];

export const WARNINGS_DEFINITIONS_HOME_OPTIONS_CARRIER_OPTIONS_LOSS: WarningDefinitionI[] = [
  policyItemHomeParsedCarrierOptionLoss
];


// Home Options Tab, General Options
// ------------------------------------------------------------------------------
/**
 * Validation for Home General Options Data
 * For Model: PolicyItemParsed
 */

function generateViewUrlGeneralOptions(fullObj: CoverageItemParsed): string {
  return '/dashboard/home/<USER>/' + fullObj.quoteResourceId + '/options/general';
}

const policyItemHomeParsedGeneralOption: WarningDefinitionI = {
  id: 'currentValue',
  deepId: 'currentValue',
  viewUri: generateViewUrlGeneralOptions,
  viewFieldId: generateUqOptionViewId,
  viewFieldInteractId: generateUqOptionViewInteractId,
  warnings: [{
    label: (val, fullObj: CoverageItemParsed) => 'Required ' + fullObj.description  + '.',
    condition: validPolicyItemParsed,
    group: WARNING_GROUPS.carrier,
    carriers: (fullObj: CoverageItemParsed) => {
      if (fullObj.additionalData && fullObj.additionalData.requiredForCarriers && fullObj.additionalData.requiredForCarriers.length) {
        return fullObj.additionalData.requiredForCarriers;
      } else {
        return fullObj.ratingPlanId ? fullObj.ratingPlanId.split(',') : [];
      }
    }
  },
  {
    label: (val, fullObj: CoverageItemParsed) => 'Option: ' + fullObj.description  + ' is selected but no values are chosen for the option.', // https://bostonsoftware.atlassian.net/browse/SPR-2580
    condition: (val, fullObj: CoverageItemParsed) => {
    // return return fullObj.isRequired && !fullObj.isActive && !fullObj.isDisabled;
    // SPR-3181
    const pioChecked: CoverageItemChild = <CoverageItemChild>fullObj.values.find((el: CoverageItemChild) => el.description === 'P.I.O. in an other structure?');
    if (fullObj.coverageCode !== 'BSC-HOME-000042' && pioChecked && pioChecked.currentValue !== 'Yes') {
       return Validate.coverageItemParsedErrorIfActiveAndNoSubOptionsSelected(fullObj); } else if (fullObj.isActive && (fullObj.currentValue.includes('null') || fullObj.currentValue === '') && fullObj.coverageCode !== 'BSC-HOME-000042') { return Validate.coverageItemParsedErrorIfActiveAndNoSubOptionsSelected(fullObj); }
    },
    group: WARNING_GROUPS.general,
    carriers: []
  },
  {
    label: (val, fullObj: CoverageItemParsed) =>
      'Dwelling amount is too high for the selected Loss Settlement - Special endorsement',
    condition: (val, fullObj: CoverageItemParsed) => {
       const isFeatureEnabled = JSON.parse(localStorage.getItem('features')).find(x => x.name === 'SprApp_FairPlanPIC');
    if (!isFeatureEnabled) { return false; }
      let returnValue = false;
      if (fullObj.definedCode === 'SPLS') {
        const storageService = new StorageService();
        let dwellingValue;
    let formType;
      storageService.getStorageData('selectedQuoteFormTypes').subscribe(x => formType = x[0]);

     if (formType !== 'HO3') {return false; }
        storageService.getStorageData('homeQuoteCoverages').subscribe((x: any) => {
            const coverages = x.coverages ? x.coverages : (x.items && x.items[0] && x.items[0].coverages ? x.items[0].coverages : []);
          const dwell = coverages?.find(c => c.coverageCode === 'DWELL');

          dwellingValue = dwell.values[0].value;
            const keyValue = fullObj.currentValueData.keyValue;
               if (dwellingValue <= 1000000) {
        return false;
      }
            if (Array.isArray(keyValue)) {
            for (const [percent, selected] of keyValue) {

              if (selected === 'Yes') {

                if (percent.startsWith('50') && dwellingValue > 2000000) {
                  returnValue = true;
                  break;
                }
                if (percent.startsWith('60') && dwellingValue > 1666667) {
                  returnValue = true;
                  break;
                }
                if (percent.startsWith('70') && dwellingValue > 1428571) {
                  returnValue = true;
                  break;
                }
              }
            }
            }


           });
           return returnValue;
      }
     },
    group: WARNING_GROUPS.carrier,
    carriers: ['33']
  },
  {
  label: (val, fullObj: CoverageItemParsed) => 'Required ' + fullObj.description  + '.',
  condition: (val, fullObj: CoverageItemParsed, additionalData: CoverageItemParsed[]) => {
     const isFeatureEnabled = JSON.parse(localStorage.getItem('features')).find(x => x.name === 'SprApp_FairPlanPIC');
    if (!isFeatureEnabled) { return false; }
    if (fullObj.coverageCode !== 'BSC-HOME-000456') { return false; }

    const pic = additionalData.find(c => c.coverageCode === 'BSC-HOME-010067');

    // Get dwelling value from session storage
    let warningResult = false;
    const storageService = new StorageService();
     let formType;
       storageService.getStorageData('selectedQuoteFormTypes').subscribe(x => formType = x[0]);

     if (formType !== 'HO3') {return false; }
    // Find PIC in carrier options
    storageService.getStorageData('homeQuoteCoverages').subscribe((x: any) => {
      const coverages = x.coverages ? x.coverages : (x.items && x.items[0] && x.items[0].coverages ? x.items[0].coverages : []);
  const dwell = coverages.find(c => c.coverageCode === 'DWELL');
      const dwellValue = dwell && dwell.values && dwell.values[0] ? +dwell.values[0].value : 0;
      if (pic && !pic.isActive && fullObj.isActive) { pic.isRequired = false; }
      // If PIC is active, disable this option
      if (pic && pic.isActive) {
        // fullObj.isDisabled = true;
        // fullObj.isActive = false;
        // fullObj.currentValue = null;
        // if (fullObj.currentValueData) {
        //   fullObj.currentValueData.values = [];
        // }
        warningResult = false; // Show warning
      }
      // If dwelling > 2M, disable this option
     if (dwellValue > 2000000) {
        // fullObj.isDisabled = true;
        // fullObj.isActive = false;
        // fullObj.currentValue = null;
        // if (fullObj.currentValueData) {
        //   fullObj.currentValueData.values = [];
        // }
        warningResult = false; // Show warning
      } else if (dwellValue > 1000000 && dwellValue <= 2000000 && !pic?.isActive) {

        warningResult = !fullObj.isActive; // Show warning if not active
        fullObj.isRequired = warningResult;
      }
    });
    return warningResult;
  },
  group: WARNING_GROUPS.carrier,
  carriers: ['33']
}

  ]
};

export const WARNINGS_DEFINITIONS_HOME_OPTIONS_GENERAL_OPTIONS: WarningDefinitionI[] = [
  policyItemHomeParsedGeneralOption
];
