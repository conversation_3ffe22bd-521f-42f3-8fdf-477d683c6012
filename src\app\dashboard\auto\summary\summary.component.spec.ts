import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import {
    SummaryCarrierOptionsSelectedComponent,
} from './summary-carrier-options-selected/summary-carrier-options-selected.component';
import { SummaryDriversComponent } from './summary-drivers/summary-drivers.component';
import { SummaryHistoryComponent } from './summary-history/summary-history.component';
import { SummaryRmvDetailsComponent } from './summary-rmv-details/summary-rmv-details.component';
import { SummaryVehiclesComponent } from './summary-vehicles/summary-vehicles.component';
import { SummaryComponent } from './summary.component';


describe('SummaryComponent', () => {
  let component: SummaryComponent;
  let fixture: ComponentFixture<SummaryComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [
        SummaryComponent,
        SummaryCarrierOptionsSelectedComponent,
        SummaryDriversComponent,
        SummaryHistoryComponent,
        SummaryRmvDetailsComponent,
        SummaryVehiclesComponent
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SummaryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
