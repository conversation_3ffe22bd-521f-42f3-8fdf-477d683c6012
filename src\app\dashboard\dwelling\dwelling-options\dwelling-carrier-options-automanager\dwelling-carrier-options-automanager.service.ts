
import {take} from 'rxjs/operators';
import { Injectable } from "@angular/core";
import { BehaviorSubject ,  SubscriptionLike as ISubscription } from "rxjs";

// Services
import { OptionsService } from "app/dashboard/app-services/options.service";
import { SpecsService } from "app/dashboard/app-services/specs.service";
import { StorageService } from "app/shared/services/storage-new.service";
import { SubsService } from "app/dashboard/app-services/subs.service";
import { AgencyUserService } from "app/shared/services/agency-user.service";
import { StorageGlobalService } from "app/shared/services/storage-global.service";

// Models
import {
  QuoteDwelling,
  QuotePlan,
  QuotePlanListAPIResponse
} from "app/app-model/quote";
import {
  Coverage,
  PolicyCoveragesData,
  CoverageItem,
  CoverageItemParsed,
  PolicyItemDefaultDataAPIResponse
} from "app/app-model/coverage";

interface TriggerSourceI {
  triggerBy?: "SelectedPlans" | "QuoteFormType" | "QuoteEffectiveDate";
}

interface PromiseDataAfterSettingDefaultValuesNew {
  policiesToUpdate: CoverageItemParsed[];
  policyItemsParsed: CoverageItemParsed[];
}

interface PromiseDataAfterSavingToApi {
  status: string;
  policyItemsParsed: CoverageItemParsed[];
}

@Injectable()
export class DwellingCarrierOptionsAutomanagerService {
  private serviceIsInitialized: boolean = false;

  private subscriptionQuote: ISubscription;
  private subscriptionQuoteIsNew: ISubscription;
  private subscriptionSelectedPlans: ISubscription;
  private subscriptionSelectedQuoteTypes: ISubscription;
  private subscriptionParserTrigger: ISubscription;
  private subscriptionGeneralOptionsPolicyItemParsed: ISubscription;

  private _parserTrigger: BehaviorSubject<TriggerSourceI> = new BehaviorSubject(
    {}
  );

  private quote: QuoteDwelling;
  private quoteId: string = "";
  private quoteLob: string = "";
  private quoteFormTypes: string[] = [];
  private quoteIsNew: boolean = false;
  private selectedPlans: QuotePlan[] = [];
  private selectedPlansIds: string[] = [];
  private quoteBeforeChange: QuoteDwelling = null;
formType;
  // Required for updating coverages - we need all options from every view
  private generalOptionsPolicies: CoverageItemParsed[] = [];

  private preventParsingPoliciesUntilDataSavedToAPIServer = false;

  constructor(
    private storageService: StorageService,
    private agencyUserService: AgencyUserService,
    private specsService: SpecsService,
    private optionsService: OptionsService,
    private subsService: SubsService
  ) {}

  public initialize(): Promise<void> {
    if (this.serviceIsInitialized) {
      return Promise.resolve();
    }

    this.serviceIsInitialized = true;
    console.log("Initialize DwellingCarrierOptionsAutomanagerService");

    return Promise.all([
      this.subscribeQuote(),
      this.subscribeQuoteIsNew(),
      this.subscribeSelectedPlans(),
      this.subscribeSelectedQuoteFormTypes(),
      this.subscribeGeneralOptionsPolicyItemParsed()
    ])
      .then(() => {
        return this.initPolicyItemsParser();
      })
      .catch(err => {
        console.log(err);
      });
  }

  public destroy(): void {
    this.serviceIsInitialized = false;

    this.subscriptionQuote && this.subscriptionQuote.unsubscribe();
    this.subscriptionQuoteIsNew && this.subscriptionQuoteIsNew.unsubscribe();
    this.subscriptionSelectedPlans &&
      this.subscriptionSelectedPlans.unsubscribe();
    this.subscriptionSelectedQuoteTypes &&
      this.subscriptionSelectedQuoteTypes.unsubscribe();
    this.subscriptionParserTrigger &&
      this.subscriptionParserTrigger.unsubscribe();
    this.subscriptionGeneralOptionsPolicyItemParsed &&
      this.subscriptionGeneralOptionsPolicyItemParsed.unsubscribe();
  }

  private subscribeQuote(): Promise<QuoteDwelling> {
    return new Promise((resolve, reject) => {
      this.subscriptionQuote = this.storageService
        .getStorageData("selectedQuote")
        .subscribe((quote: QuoteDwelling) => {
          this.quote = JSON.parse(JSON.stringify(quote));
          this.quoteId = quote.resourceId;
          this.quoteLob = quote.lob;
          this.formType = quote.formType;
          resolve(this.quote);

          if (!this.quoteBeforeChange) {
            this.quoteBeforeChange = JSON.parse(JSON.stringify(quote));
          } else if (
            this.quoteBeforeChange.effectiveDate !== this.quote.effectiveDate
          ) {
            this.quoteBeforeChange = JSON.parse(JSON.stringify(quote));

            // INIT POLICY ITEMS PARSING After Quote Effective date change
            // Emit Observable to Parse Items
            this._parserTrigger.next({ triggerBy: "QuoteEffectiveDate" });
          }
        });
    });
  }

  private subscribeSelectedQuoteFormTypes(): Promise<string[]> {
    return new Promise((resolve, reject) => {
      this.subscriptionSelectedQuoteTypes = this.storageService
        .getStorageData("selectedQuoteFormTypes")
        .subscribe((res: string[]) => {
          this.quoteFormTypes = res;
          if(this.quoteIsNew) {console.log('isnoew');this.formType = res[0];}
          resolve(this.quoteFormTypes);

          // Emmit Observable to Parse Items
          this._parserTrigger.next({ triggerBy: "QuoteFormType" });
        });
    });
  }

  private subscribeQuoteIsNew(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.subscriptionQuoteIsNew = this.storageService
        .getStorageData("isNewQuote")
        .subscribe((res: boolean) => {
          this.quoteIsNew = res;
          resolve(this.quoteIsNew);
        });
    });
  }

  private subscribeSelectedPlans(): Promise<QuotePlan[]> {
    return new Promise((resolve, reject) => {
      this.subscriptionSelectedPlans = this.storageService
        .getStorageData("selectedPlan")
        .subscribe((res: QuotePlanListAPIResponse) => {
          if (res && res.items && res.items.length) {
            this.selectedPlans = JSON.parse(JSON.stringify(res.items[0].items));
            this.selectedPlansIds = this.selectedPlans.map(
              plan => plan.ratingPlanId
            );
            resolve(this.selectedPlans);

            // INIT POLICY ITEMS PARSING
            // Emmit Observable to Parse Items
            this._parserTrigger.next({ triggerBy: "SelectedPlans" });
          }
        });
    });
  }

  private subscribeGeneralOptionsPolicyItemParsed(): void {
    this.subscriptionGeneralOptionsPolicyItemParsed = this.storageService
      .getStorageData("dwellingGeneralOptionsParsed")
      .subscribe((items: CoverageItemParsed[]) => {
        this.generalOptionsPolicies = items;
      });
  }

  private initPolicyItemsParser(): Promise<void> {
    let delay = 500;
    let timer;

    return new Promise((resolve, reject) => {
      this.subscriptionParserTrigger = this._parserTrigger
        .asObservable()
        .subscribe(res => {
          timer && clearTimeout(timer);

          // Prevent geting Policies and parse data until data is saved to API with default values - if necessary
          if (this.preventParsingPoliciesUntilDataSavedToAPIServer) return;

          timer = setTimeout(() => {
            // INFO:: https://bostonsoftware.atlassian.net/browse/SPRC-435
            // For now do not fetch an process any options
            // so we need to return resolved promise
            resolve();
            // this.getOptionsPoliciesListAndParseData()
            //  .then(() => resolve())
            //  .catch(err => reject(err));
          }, delay);
        });
    });
  }

  private getOptionsPoliciesListAndParseData(): Promise<void> {
    let selectedPlansIdsString = this.selectedPlansIds.join(",");
    let states = this.quote.state;
    let quoteFormTypesString = this.quoteFormTypes.join(",");
    let quoteEffectiveDate =
      this.quote && this.quote.effectiveDate ? this.quote.effectiveDate : "";

    let agencyId;
    this.agencyUserService.userData$.pipe(
      take(1))
      .subscribe(agent => (agencyId = agent.agencyId));

    return new Promise((resolve, reject) => {
      // TODO:: Currently there are no carrier options for Dewlling Carrier Options
      // Probably the call should be made to API Endpoint with coverageGroups=policyOptionsCarrier (currently not available);
      this.specsService
        .getRatingCoverages(
          states,
          this.quoteLob,
          "policyCoveragesStandard",
          selectedPlansIdsString,
          quoteFormTypesString,
          quoteEffectiveDate
        ).pipe(
        take(1))
        .subscribe(
          res => {
            let policiesItemsHome: CoverageItem[] = [];
            if (res && res.items && res.items.length) {
              policiesItemsHome = [...res.items];
            }

            if (policiesItemsHome && this.quoteId) {
              let policyItemsHomeParsed: CoverageItemParsed[] = [];
              this.processPoliciesItemsParsing(
                policiesItemsHome,
                this.selectedPlans,
                this.quoteId,
                this.quoteFormTypes
              )
                .then((res: CoverageItemParsed[]) => {
                  policyItemsHomeParsed = JSON.parse(JSON.stringify(res));
                  // ---------------
                  // if there are no default values setting opertions, update storage service
                  this.storageService.setStorageData(
                    "dwellingCarrierOptionsParsed",
                    policyItemsHomeParsed
                  );
                  //----------------
                  return policyItemsHomeParsed;
                })
                // // For Now we are not setting default values - waiting for API
                // .then((res:PolicyItemParsed[]) => {
                //   policyItemsHomeParsed = res;
                //   return this.setDefaultValuesForOptionsIfThisIsNewQuote(policyItemsHomeParsed, agencyId, this.quoteIsNew)
                // })
                // .then((res: PromiseDataAfterSettingDefaultValuesNew) => {
                //   // Save To Storage
                //   policyItemsHomeParsed = res.policyItemsParsed;
                //   this.storageService.setStorageData('dwellingCarrierOptionsParsed', policyItemsHomeParsed);

                //   // Update API with default Values
                //   return this.savePoliciesToApiIfNewPoliciesToUpdate(policyItemsHomeParsed, res.policiesToUpdate);
                // })
                .then(() => resolve())
                .catch(err => {
                  console.log(err);
                  reject(err);
                });
            }
          },
          err => reject(err)
        );
    });
  }

  public processPoliciesItemsParsing(
    policies: CoverageItem[],
    selectedPlans: QuotePlan[],
    quoteId: string,
    quoteSelectedFormTypes: string[]
  ): Promise<CoverageItemParsed[]> {
    let arrPoliciesItemsParsed = this.optionsService.parsePoliciesItemsToPoliciesItemsParsed(
      policies
    );
    arrPoliciesItemsParsed = this.optionsService.orderObjectsArrayByProperty(
      arrPoliciesItemsParsed,
      "description"
    );

    // Required for setting default values if new Quote - to check if options
    // has been alredy saved in storage or not
    let policiesItemsFromStorage: CoverageItemParsed[] = [];

    this.storageService
      .getStorageData("dwellingCarrierOptionsParsed").pipe(
      take(1))
      .subscribe(res => (policiesItemsFromStorage = res));
    //--------------------------------------------------------------------------

    return this.loadPolicyCoverages(quoteId).then(
      (data: PolicyCoveragesData) => {
        arrPoliciesItemsParsed = arrPoliciesItemsParsed.map(item => {
          item = this.setAdditionalDataForCoverageItemParsed(item);
          item = this.optionsService.sortPolicyItemParsedChildItems(item);
          item = this.optionsService.setPolicyItemStatusBasedOnPolicyCoverages(
            item,
            data.coverages
          );
          item = this.setPolicyItemStatusBasedOnRequirements(
            item,
            quoteSelectedFormTypes
          );

          item.endpointUrl = data.endpointURL;
          item = this.optionsService.setPolicyItemParsedIsNewFromAPIValue(
            item,
            policiesItemsFromStorage
          );
          item.quoteResourceId = quoteId;

          if (item.inputType != "Dropdown") {
            item.values = this.optionsService.orderObjectsArrayByProperty(
              item.values,
              "value"
            );
          }

          return item;
        });
        return arrPoliciesItemsParsed;
      }
    );
  }

  private loadPolicyCoverages(quoteId: string): Promise<PolicyCoveragesData> {
    let policyCoverageData: PolicyCoveragesData = new PolicyCoveragesData();

    return new Promise((resolve, reject) => {
      if (quoteId) {
        this.optionsService
          .getPolicies(quoteId).pipe(
          take(1))
          .subscribe(res => {
            if (res.items.length === 0) {
              this.optionsService
                .createCoverageByUri(res.meta.href).pipe(
                take(1))
                .subscribe(res => {
                  policyCoverageData.coverages = res.coverages;
                  policyCoverageData.endpointURL = res.meta.href;
                  resolve(policyCoverageData);
                });
            } else {
              policyCoverageData.coverages = res.items[0].coverages;

              if (res.items[0].meta && res.items[0].meta.href) {
                policyCoverageData.endpointURL = res.items[0].meta.href;
              } else if (res.items[0]) {
                policyCoverageData.endpointURL =
                  res.meta.href + "/" + res.items[0].resourceId;
              } else {
                policyCoverageData.endpointURL =
                  "api_does_not_returned_data_to_create_uri";
              }

              resolve(policyCoverageData);
            }
          });
      } else {
        reject("Error Load Policy Coverages, quoteId Not defined");
      }
    });
  }

  // Methods for setting default values
  //----------------------------------------------------------------------------

  // Helper for method setDefaultValuesForOptionsIfThisIsNewQuote
  private helperSetDefaultValues(
    data: PolicyItemDefaultDataAPIResponse,
    policiesParsed: CoverageItemParsed[]
  ): PromiseDataAfterSettingDefaultValuesNew {
    const policiesParsedWithDefaultValues = this.optionsService.setDefaultValuesForOptions(
      policiesParsed,
      data.items
    );
    const policiesToUpdate = policiesParsedWithDefaultValues.filter(
      policyParsed =>
        data.items.some(
          item =>
            item.coverageCode === policyParsed.coverageCode &&
            policyParsed.isNewFromAPI
        )
    );

    const promiseResponse: PromiseDataAfterSettingDefaultValuesNew = {
      policiesToUpdate: policiesToUpdate,
      policyItemsParsed: policiesParsedWithDefaultValues
    };

    return promiseResponse;
  }

  private setDefaultValuesForOptionsIfThisIsNewQuote(
    policiesParsed: CoverageItemParsed[],
    agencyId: string,
    isNewQuote: boolean
  ): Promise<PromiseDataAfterSettingDefaultValuesNew> {
    return new Promise((resolve, reject) => {
      // console.log('Quote is new', isNewQuote);
      if (!isNewQuote) {
        const promiseResponse: PromiseDataAfterSettingDefaultValuesNew = {
          policiesToUpdate: [],
          policyItemsParsed: policiesParsed
        };
        return resolve(promiseResponse);
      }

      // >> TODO:: Change to dwelling default option
      this.storageService
        .getStorageData("homeDefaultOptions").pipe(
        take(1))
        .subscribe(
          (res: PolicyItemDefaultDataAPIResponse) => {
            if (res && res.items && res.items.length) {
              const promiseResponse = this.helperSetDefaultValues(
                res,
                policiesParsed
              );
              resolve(promiseResponse);
            } else {
              // API Fallback
              // >> TODO:: Change to dwelling default option
              this.subsService
                .getDefaultHomeCarrierOptions(agencyId, this.formType).pipe(
                take(1))
                .subscribe(
                  (res: PolicyItemDefaultDataAPIResponse) => {
                    const promiseResponse = this.helperSetDefaultValues(
                      res,
                      policiesParsed
                    );
                    resolve(promiseResponse);
                  },
                  err => reject(err)
                );
            }
          },
          err => {
            reject(err);
          }
        );
    });
  }

  // private savePoliciesWithDefaultValuesToAPI(policiesParsed:PolicyItemParsed[])
  private savePoliciesToApiIfNewPoliciesToUpdate(
    policiesParsed: CoverageItemParsed[],
    policiesToUpdate: CoverageItemParsed[]
  ): Promise<PromiseDataAfterSavingToApi> {
    return new Promise((resolve, reject) => {
      let promiseResponse: PromiseDataAfterSavingToApi = {
        status: "",
        policyItemsParsed: policiesParsed
      };

      // Do not send request to update if no new options with default values
      if (!policiesToUpdate || !policiesToUpdate.length) {
        promiseResponse.status =
          "No new options to Update default values, Do not send API Request";
        return resolve(promiseResponse);
      }

      // We need to also update policies General Options from the Options General View
      // The mechanism of updating policeis (coverages) is strange - only options sent to API will be set as selected
      // (if the option was selected before and not sent to API during update, next time data is load, this option will be unselected )
      let allPoliciesToSearchActiveOptions: CoverageItemParsed[] = [];
      allPoliciesToSearchActiveOptions = [
        ...policiesParsed,
        ...this.generalOptionsPolicies
      ];

      let endpointUrl = policiesParsed[0].endpointUrl;
      let activePoliciesToUpdate = allPoliciesToSearchActiveOptions.filter(
        item => item.isActive
      );
      let readyAllPoliciesToUpdate: Coverage[] = this.optionsService.convertPolicyItemParsedArrayToPolicyArray(
        activePoliciesToUpdate
      );

      // Update remote data
      let newCoverageData = {
        coverages: readyAllPoliciesToUpdate
      };

      this.optionsService
        .updatePoliciesByUri(endpointUrl, newCoverageData).pipe(
        take(1))
        .subscribe(
          res => {
            promiseResponse.status =
              "Set and saved Policies default values for new options";
            // console.log(promiseResponse.status);
            resolve(promiseResponse);
          },
          err => {
            promiseResponse.status =
              "Error occurred During updating, not set default values";
            // console.log(promiseResponse.status);
            resolve(promiseResponse);
          }
        );
    });
  }

  private setAdditionalDataForCoverageItemParsed(item: CoverageItemParsed): CoverageItemParsed {
    if (!item.additionalData) {
      item.additionalData = {};
    }

    return item;
  }

  // Check if any element of an array (anyElementArr) is in another array (arrayToCheckIfIsIn)
  private anyElementIsInArray(
    anyElementArr: string[],
    arrayToCheckIfIsIn: string[]
  ): boolean {
    return anyElementArr.some(el => arrayToCheckIfIsIn.indexOf(el) != -1);
  }

  // Requirements
  //----------------------------------------------------------------------------
  public setPolicyItemStatusBasedOnRequirements(
    policyItemHomeParsed: CoverageItemParsed,
    selectedQuoteFormTypes: string[]
  ): CoverageItemParsed {
    // switch (policyItemHomeParsed.coverageCode) {
    //   case 'SOME_CODE':
    //     break;
    // }

    return policyItemHomeParsed;
  }
}
