<ng-template [ngIf]="!leaveQuote.isOpen">
  <h2 class="o-heading">New Dwelling Quote</h2>
  <div class="box box--silver u-spacing--1-5">
    <div class="row">
      <div class="col-xs-12">
        <table class="form-table">
          <tbody>

            <tr class="form-table__row" [class.is-required-field]="false">
              <td class="form-table__cell">State:</td>
              <td class="form-table__cell">
                <sm-autocomplete *ngIf="this.states?.length"
                #refState
                [readonly]="false"
                [required]="true"
                [id]="'total-states'"
                [name]="'total-states'"
                [options]="states"
                [activeOption]="selectedState"
                [disabled]="this.states?.length === 1 ? true : false"
                (onSelect)="selectState($event,refState, refSubscriptionDwellingInfoPopup, refPlansSelector)">
              </sm-autocomplete>
              </td>
            </tr>

            <tr class="form-table__row">
              <td class="form-table__cell u-width-150px">Effective Date:</td>
              <td class="form-table__cell">
                <app-datepicker-input
                  #refModelEffectiveDate
                  [required]="true"
                  [selectDate]="modelEffectiveDate"
                  (onDateChange)="onDateChange($event)">
                </app-datepicker-input>
              </td>
            </tr>

            <tr class="form-table__row" *ngIf="showWarningInThePast">
              <td class="form-table__cell form-table__cell--autoH"></td>
              <td class="form-table__cell form-table__cell--autoH">
                <p class="u-color-sunset u-t-weight-normal">Effective Date is in the Past.</p>
              </td>
            </tr>

            <tr class="form-table__row">
              <td class="form-table__cell">Form Type:</td>
              <td class="form-table__cell">
                <sm-select
                  [options]="formType"
                  [activeOption]="selectedFormTypeId"
                  [hasSearch]="false"
                  [multiselect]="false"
                  (onSelect)="selectFormType($event, refPlansSelector)">
                </sm-select>
              </td>
            </tr>

            <tr class="form-table__row" [class.is-required-field]="false">
              <td class="form-table__cell">Plans on Quote:</td>
              <td class="form-table__cell">
                <app-plans-selector #refPlansSelector [plans]="plansOnQuotesFiltered" [activePlans]="selectedPlan" (onSelect)="refreshValue($event)" [css]="'aboutbox__link o-link o-link--expand sidebox__link-dropdown-toggle'"></app-plans-selector>
              </td>
            </tr>

          </tbody>
        </table>

      </div>
    </div>
  </div>

  <div class="row u-spacing--1-5">
    <div class="col-xs-12 u-align-right u-remove-letter-spacing">
      <ng-container *ngIf="useToImportQuote">
        <button type="button" (click)="handleImportQuote()" class="o-btn u-spacing--left-2" style="margin-left:1.8rem;">Import</button>
      </ng-container>
      <ng-container *ngIf="!useToImportQuote">
          <button *ngIf="validUserInput" (click)="handleCreateManuallyClick($event)" class="o-btn u-spacing--left-2" style="margin-left:1.8rem;">Create</button>
          <button *ngIf="!validUserInput" class="o-btn u-spacing--left-2 is-disabled" style="margin-left:1.8rem;">Create</button>
      </ng-container>
      <button (click)="handleCancelClick($event)" class="o-btn o-btn--idle u-spacing--left-2" style="margin-left:1.8rem;">Cancel</button>
    </div>
  </div>
</ng-template>

<div class="leave-quote--inside">
  <app-leave-quote #leaveQuote></app-leave-quote>
</div>

<app-modalbox #refSubscriptionDwellingInfoPopup (click)="$event.stopPropagation()" smToOverlay [mainContainerCss]="'force-to-front'">
  <div class="box box--silver u-spacing--1-5">
    <p *ngFor="let message of subscriptionMessages">{{message}}</p>
  </div>
  <div class="row u-spacing--2">
    <div class="col-xs-12 u-align-right">
      <button (click)="refSubscriptionDwellingInfoPopup.closeModalbox();" class="o-btn">Ok</button>
    </div>
  </div>
</app-modalbox>
