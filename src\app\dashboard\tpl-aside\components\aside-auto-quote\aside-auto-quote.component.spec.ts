import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { StubSubsService } from '../../../../../testing/stubs/services/subs.service.provider';
import { SubsService } from '../../../app-services/subs.service';
import { AgencyUserService } from 'app/shared/services/agency-user.service';
import { StubAgencyUserService } from 'testing/stubs/services/agency-user.service.provider';
import { By } from '@angular/platform-browser';
import { async, ComponentFixture, fakeAsync, inject, TestBed, tick } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { RouterTestingModule } from '@angular/router/testing';

import { data as AGENCY_USER } from 'testing/data/agencies/user';
import { AUTO_QUOTE as QUOTE_AUTO } from 'testing/data/quotes/quote-auto';
import { AUTO_QUOTE_PLAN_LIST as RATING_PLANS_DATA_AUTO } from 'testing/data/quotes/quote-plan-list/auto';
import { data as RATING_PLANS_SUBS } from 'testing/data/subs/rating-plans';
import { expectLastCallArgs } from 'testing/helpers/all';

import { StubDatepickerModalComponent } from 'testing/stubs/components/datepicker-modal.component';
import { StubLeaveQuoteComponent } from 'testing/stubs/components/leave-quote.component';
import { StubModalboxComponent } from 'testing/stubs/components/modalbox.component';
import { StubPlansSelectorComponent } from 'testing/stubs/components/plans-selector.component';
import { StubSelectComponent } from 'testing/stubs/components/select.component';
import { StubdateFormatPipe } from 'testing/stubs/pipes/am-date-format.pipe';
import { StubAgencyUserServiceProvider } from 'testing/stubs/services/agency-user.service.provider';
import { StubOverlayLoaderServiceProvider } from 'testing/stubs/services/overlay-loader.service.provider';
import { StubPremiumsServiceProvider } from 'testing/stubs/services/premiums.service.provider';
import { StubQuotesService } from 'testing/stubs/services/quotes.service.provider';
import { StubQuotesServiceProvider } from 'testing/stubs/services/quotes.service.provider';
import { StubSubsServiceProvider } from 'testing/stubs/services/subs.service.provider';

import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Helpers } from 'app/utils/helpers';

import { AsideAutoQuoteComponent } from './aside-auto-quote.component';

describe('Component: AsideAutoQuote', () => {
  let component: AsideAutoQuoteComponent;
  let fixture: ComponentFixture<AsideAutoQuoteComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [
        FormsModule,
        RouterTestingModule
      ],
      declarations: [
        AsideAutoQuoteComponent,
        StubSelectComponent,
        StubdateFormatPipe,
        StubDatepickerModalComponent,
        StubModalboxComponent,
        StubLeaveQuoteComponent,
        StubPlansSelectorComponent
      ],
      providers: [
        StorageService,
        StubQuotesServiceProvider,
        StubOverlayLoaderServiceProvider,
        StubAgencyUserServiceProvider,
        StubSubsServiceProvider,
        StubPremiumsServiceProvider,
        StorageGlobalService
      ]
    })
    .compileComponents();
  }));

  describe('when selectedPlan is available', () => {
    beforeEach(fakeAsync(inject([StorageService, StorageGlobalService],
      (storageService: StorageService, storageGlobalService: StorageGlobalService) => {
      storageService.setStorageData('selectedQuote', Helpers.deepClone(QUOTE_AUTO));
      storageService.setStorageData('selectedPlan', Helpers.deepClone(RATING_PLANS_DATA_AUTO));
      storageGlobalService.setSubs('plans', Helpers.deepClone(RATING_PLANS_SUBS.items));

      fixture = TestBed.createComponent(AsideAutoQuoteComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
      tick();
    })));

    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should react to effective date change', fakeAsync(inject([QuotesService, StorageService],
      (quotesService: StubQuotesService, storageService: StorageService) => {
        spyOn(quotesService, 'updateQuoteInfo').and.callThrough();
        spyOn(storageService, 'setStorageData').and.callThrough();

        const datepicker = <StubDatepickerModalComponent>fixture.debugElement.query(
          By.directive(StubDatepickerModalComponent)).componentInstance;
        datepicker.onSave.emit({
          date: new Date(2017, 3, 27)
        });

        fixture.detectChanges();
        tick();

        fixture.detectChanges();
        tick();

        expect(component.quote.effectiveDate).toEqual('2017-04-27');
        expect(component.quote.expirationDate).toEqual('2018-04-27');
        expect(quotesService.updateQuoteInfo).toHaveBeenCalled();
        expect(storageService.setStorageData).toHaveBeenCalledWith(
          'selectedQuote',
          component.quote
        );
      })));

    it('should react to selected plan change', fakeAsync(inject([QuotesService],
      (quotesService: StubQuotesService) => {
        spyOn(quotesService, 'updateQuoteByUrl').and.callThrough();

        const plansSelector = fixture.debugElement.query(By.directive(StubPlansSelectorComponent)).componentInstance;
        plansSelector.onSelect.emit({
          selectedOption: [{
            text: 'name',
            id: 'id',
            data: {
              name: 'name'
            }
          }]
        });

        fixture.detectChanges();
        tick();

        fixture.detectChanges();
        tick();

        expectLastCallArgs(quotesService.updateQuoteByUrl).toEqual([
          jasmine.any(String),
          jasmine.objectContaining({
            items: [
              jasmine.objectContaining({
                name: 'name'
              })
            ]
          }),
          jasmine.any(Boolean)
        ]);
      })));
  });

  describe('when selectedPlan is not available', () => {
    beforeEach(fakeAsync(inject([StorageService, StorageGlobalService],
      (storageService: StorageService, storageGlobalService: StorageGlobalService) => {
      storageService.setStorageData('selectedQuote', Helpers.deepClone(QUOTE_AUTO));
      storageGlobalService.setSubs('plans', Helpers.deepClone(RATING_PLANS_SUBS.items));

      fixture = TestBed.createComponent(AsideAutoQuoteComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
      tick();
    })));

    it('should create', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('when first selectedPlan has no items', () => {
    beforeEach(fakeAsync(inject([StorageService, AgencyUserService, SubsService],
      (storageService: StorageService, agencyUserService: StubAgencyUserService, subsService: StubSubsService) => {
      storageService.setStorageData('selectedQuote', Helpers.deepClone(QUOTE_AUTO));
      const modifiedSelectedPlan = Helpers.deepClone(RATING_PLANS_DATA_AUTO);
      modifiedSelectedPlan.items[0].items = [];
      storageService.setStorageData('selectedPlan', modifiedSelectedPlan);
      agencyUserService.userData = Helpers.deepClone(AGENCY_USER);
      subsService.plans = Helpers.deepClone(RATING_PLANS_SUBS);

      fixture = TestBed.createComponent(AsideAutoQuoteComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
      tick();
    })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('when there are no AUTOP plans available in subs', () => {
    beforeEach(fakeAsync(inject([StorageService, AgencyUserService, SubsService],
      (storageService: StorageService, agencyUserService: StubAgencyUserService, subsService: StubSubsService) => {
      storageService.setStorageData('selectedQuote', Helpers.deepClone(QUOTE_AUTO));
      storageService.setStorageData('selectedPlan', Helpers.deepClone(RATING_PLANS_DATA_AUTO));
      agencyUserService.userData = Helpers.deepClone(AGENCY_USER);
      subsService.plans = Helpers.deepClone(RATING_PLANS_SUBS);

      fixture = TestBed.createComponent(AsideAutoQuoteComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
      tick();
    })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });
  });
});
