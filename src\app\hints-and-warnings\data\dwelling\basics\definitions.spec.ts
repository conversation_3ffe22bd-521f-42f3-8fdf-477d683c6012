import { Coverage } from 'app/app-model/coverage';
import { WARNINGS_DEFINITIONS_DWELLING_BASICS } from './definitions';
import {
  generateCarriers,
  generateLabels,
  generateViewFieldIds,
  generateViewURIs,
  runConditions,
} from 'testing/helpers/warning-definitions';

describe('Definitions: Dwelling Basics', () => {
  describe('when home basics validator is used', () => {
    let definitions: any[];
    let coverage: Coverage;

    beforeEach(() => {
      definitions = WARNINGS_DEFINITIONS_DWELLING_BASICS;
      coverage = {
        coverageCode: 'DWELL',
        coverageDescription: '',
        meta: {
          href: '',
          rel: []
        },
        resourceName: '',
        values: []
      };
    });

    it('allows to check if required fields are provided', () => {
      const errors = runConditions(definitions, coverage, {});

      expect(errors).toEqual(['value']);
    });

    it('generates viewFieldIds without errors', () => {
      expect(() => {
        generateViewFieldIds(definitions, coverage);
      }).not.toThrow();
    });

    it('generates viewURIs without errors', () => {
      expect(() => {
        generateViewURIs(definitions, coverage);
      }).not.toThrow();
    });

    it('generates labels without errors', () => {
      const codes = [
        'DWELL',
        'PP',
        'OS',
        'LOU',
        'PL',
        'MEDPM'
      ];

      codes.forEach(code => {
        coverage.coverageCode = code;

        expect(() => {
          generateLabels(definitions, coverage);
        }).not.toThrow(code);
      });
    });

    it('generates carriers without errors', () => {
      expect(() => {
        generateCarriers(definitions, coverage);
      }).not.toThrow();
    });
  });
});
