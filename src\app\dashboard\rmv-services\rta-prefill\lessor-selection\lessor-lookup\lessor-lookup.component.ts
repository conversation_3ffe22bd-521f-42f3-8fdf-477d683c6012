import { Component, OnInit, Input, EventEmitter, Output } from '@angular/core';
import { Lessor, LessorLookupData } from 'app/app-model/PrefillRMV';
import { LookupsService } from 'app/dashboard/app-services/lookups.service';

@Component({
    selector: 'app-lessor-lookup',
    templateUrl: './lessor-lookup.component.html',
    styleUrls: ['./lessor-lookup.component.scss'],
    standalone: false
})
export class LessorLookupComponent implements OnInit {
  searchType = 'nameOrFid';
  @Input() searchQuery;
  @Input() results: LessorLookupData[];
  selectedLessor: LessorLookupData;
  loadingLessors = false;
  dataInitiated = false;
  @Input() paginationResultsCount = 0;
  public paginationResultLimit = 10;
  public paginationResultShowFrom = 0;
  public paginationCurrentPage = 1;
  @Input() modalBox;
  @Output() public onSelectedLessorClick: EventEmitter<
    any
  > = new EventEmitter();

  constructor(private lookupService: LookupsService) {}

  ngOnInit() {
    this.filter(true);
  }

  reset() {
    this.searchQuery = '';
    this.selectedLessor = null;
    this.searchType = 'nameOrFid';
    this.filter(true);

  }

  filter(showResultsFromFirstPage = false) {
    if (showResultsFromFirstPage) {
      this.paginationResultShowFrom = 0;
      this.paginationCurrentPage = 1;
    }
    this.loadingLessors = true;
    this.lookupService
      .getLessors(
        this.searchQuery,
        this.searchType,
        this.paginationResultShowFrom
      )
      .subscribe(x => {
        this.dataInitiated = true;
        this.results = x.items;
        this.loadingLessors = false;
        this.paginationResultsCount = x.size;
      });
  }

  setSelectedLessor(lessor) {
    this.selectedLessor = lessor;
  }

  saveSelectedLessor() {
    this.onSelectedLessorClick.emit(this.selectedLessor);
    this.modalBox.close();
  }

  closeModal() {
    this.modalBox.close();
  }

  public isLessorSelected(row: LessorLookupData): boolean {
    return row === this.selectedLessor;
  }

  public paginationPageChange(data) {
    if (this.dataInitiated && this.paginationResultShowFrom !== data.startAt) {
      this.paginationResultShowFrom = data.startAt;
      this.filter();
    }
  }
}
