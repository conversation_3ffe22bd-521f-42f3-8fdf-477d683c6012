
<app-loader [loading]="!showVehicleDetailsForm" [loadingText]="'Loading, please wait...'"></app-loader>

<ng-container *ngIf="showVehicleDetailsForm">
  <app-loader [loading]="loadingVehicleRelatedData" [cssClass]="'loader--content-top loader--with-opacity'" [loadingText]="'Loading..'"></app-loader>
</ng-container>

<section class="section section--compact u-spacing--0-5" *ngIf="showVehicleDetailsForm">
  <div class="u-flex u-flex--spread">
    <div class="">
      <h1 class="o-heading">Vehicle Details</h1>
    </div>
    <div class="">
      <!-- *** -->
      <button (click)="addNewVehicle($event)" class="o-btn o-btn--action o-btn--i_round-plus" id="add-vehicle-modal">Add Vehicle</button>

      <!-- *** -->
      <button class="o-btn o-btn--action o-btn--i_trash" id="delete-vehicle-modal">Delete vehicle</button>
      <app-confirmbox
        [launcher]="'#delete-vehicle-modal'"
        [question]="'Are you sure you want to delete this vehicle?'"
        [askAbout]="vehicleNameToDisplay(selectedVehicle)"
        [confirmBtnText]="'Yes, delete vehicle'"
        (onAccept)="confirmAcceptedDelete($event)"
        (onCancel)="confirmCanceledDelete($event)">
      </app-confirmbox>
      <button class="o-btn o-btn--action o-btn" id="reorder-vehicle-modal"><i class="fas fa-arrows-alt"></i> Reorder Vehicles</button>
      <app-modalbox #modalReorderVehicle [launcher]="'#reorder-vehicle-modal'" [css]="'u-width-650px'">
        <h2 class="o-heading o-heading--red">Reorder Vehicles</h2>
        <div class="box box--silver u-spacing--1-5">
          <p>Select the vehicle(s) you would like to reorder and use the arrows or drag and drop to move them to the desired order</p>
          <div style="margin-left:100px;">
            <p-orderList [value]="vehicles" dragdrop="true" dragdropScope="drivers" [listStyle]="{'width': '15em'}">
          <ng-template let-veh pTemplate="item" class="u-width-480px">
            <div style="font-size:14px;margin:15px 5px 0 0">{{vehicleNameToDisplay(veh)}}</div>
          </ng-template>
        </p-orderList>
          </div>

        </div>
        <div class="row u-spacing--2">
          <div class="col-xs-12 u-align-right">
            <button class="o-btn" (click)="saveReorder(modalReorderVehicle)">Save</button>

            <button class="o-btn o-btn--idle u-spacing--left-2"
              (click)="modalReorderVehicle.closeModalbox()">Cancel</button>
          </div>
        </div>
      </app-modalbox>
      <!-- *** -->
      <button class="o-btn o-btn--action o-btn--i_search" id="rmv-lookup-vehicle-modal">RMV Lookup</button>
      <app-modalbox #modalRmvVehicle class="coverages-modal" [launcher]="'#rmv-lookup-vehicle-modal'" [css]="'u-width-750px'" (onStateChange)="rmvLookupModalStateChange($event)">
        <ng-template [ngIf]="modalRmvVehicle.isOpen">
          <h1 class="o-heading o-heading--red">RMV Lookup</h1>

        <div class="box box--silver u-spacing--1-5" >
         <app-rmv-vehicle-lookup #refVehicleLookup [refModalbox]=""></app-rmv-vehicle-lookup>
        </div>

        <div class="row u-spacing--2">
          <div class="col-xs-12 u-align-right">
            <!--button (click)="modalRmvVehicle.closeModalbox($event)" class="o-btn u-spacing--right-2">Lookup</button-->
            <button (click)="refVehicleLookup.rmvHandleLookupButtonAction($event, modalRmvVehicle)" class="o-btn"
              [disabled]="refVehicleLookup.checkForErrors()">Lookup</button>
            <button (click)="modalRmvVehicle.closeModalbox($event)" class="o-btn o-btn--idle u-spacing--left-2">Cancel</button>
          </div>
        </div>
        </ng-template>

      </app-modalbox>
    </div>
  </div>

<!-- FORM DETAILS -->

  <div class="row u-spacing--1">
    <div class="col-xs-12">
      <div class="box box--silver">
        <form name="formVehicleDetails" id="formVehicleDetails" novalidate>
          <div class="row o-columns">
            <div class="col-xs-6">
              <table class="form-table">
                <tr class="form-table__row" [class.is-required-field]="refVehicleType.ngModel?.invalid">
                  <td class="form-table__cell form-table__cell--label  u-width-130px">
                    <label for="vehicleType">Vehicle Type:</label>
                  </td>
                  <td class="form-table__cell u-width-220px">
                    <sm-autocomplete
                      #refVehicleType
                      [options]="optionsVehicleTypes"
                      [activeOption]="selectedVehicle.vehicleType"
                      [name]="'vehicleType'"
                      [id]="'vehicleType'"
                      [searchFromBegining]="false"
                      [allowEmptyValue]="false"
                      [allowCustomText]="false"
                      [required]="true"
                      (onSelect)="actionOnVehicleTypeFieldSelect($event, 'vehicleType')">
                    </sm-autocomplete>
                  </td>
                </tr>

                <tr class="form-table__row" [class.is-required-field]="refVehicleUsage.ngModel?.invalid">
                  <td class="form-table__cell form-table__cell--label  u-width-130px">
                    <label for="vehicleUsage">Usage:</label>
                  </td>
                  <td class="form-table__cell u-width-220px">
                    <sm-autocomplete
                      #refVehicleUsage
                      [options]="optionsUsage"
                      [activeOption]="selectedVehicle.usage"
                      [name]="'vehicleUsage'"
                      [id]="'vehicleUsage'"
                      [searchFromBegining]="false"
                      [allowEmptyValue]="false"
                      [allowCustomText]="false"
                      [required]="true"
                      (onSelect)="onSelectFieldDataSelected($event, 'usage')">
                    </sm-autocomplete>
                  </td>
                </tr>

                <tr class="form-table__row" [class.is-required-field]="refVehicleYear.ngModel?.invalid">
                  <td class="form-table__cell form-table__cell--label  u-width-130px">
                    <label for="vehicleYear">Year:</label>
                  </td>
                  <td class="form-table__cell u-width-220px">
                    <sm-autocomplete
                      #refVehicleYear
                      [options]="optionsYear"
                      [activeOption]="selectedVehicle.year"
                      [name]="'vehicleYear'"
                      [id]="'vehicleYear'"
                      [searchFromBegining]="false"
                      [allowEmptyValue]="false"
                      [allowCustomText]="false"
                      [required]="true"
                      (onSelect)="actionOnYearFieldSelect($event, 'year')">
                    </sm-autocomplete>

                  </td>
                </tr>

                <!--tr class="form-table__row" [class.is-required-field]="refVehicleMake.ngModel?.invalid" -->
                <tr class="form-table__row" [class.is-required-field]="isInvalidMakeField()">
                  <td class="form-table__cell form-table__cell--label  u-width-130px">
                    <label for="vehicleMake">Make:</label>
                  </td>
                  <td class="form-table__cell u-width-220px">
                    <sm-autocomplete
                      #refVehicleMake
                      [options]="optionsMake"
                      [activeOption]="selectedVehicle.make"
                      [name]="'vehicleMake'"
                      [id]="'vehicleMake'"
                      [searchFromBegining]="false"
                      [allowEmptyValue]="false"
                      [allowCustomText]="false"
                      [delayedOptions]="true"
                      [required]="true"
                      [readonly]="fieldMakeDisabled"
                      (onSelect)="actionOnMakeFieldSelect($event, 'make')">
                    </sm-autocomplete>

                  </td>
                </tr>


                <!-- <tr class="form-table__row" [ngClass]="{'is-required-field': refVehicleModel?.ngModel?.invalid}">

                  <td class="form-table__cell form-table__cell--label  u-width-130px">
                    <label for="vehicleModel">Model:</label>
                  </td>
                  <td class="form-table__cell u-width-220px">
                    <div>
                      <sm-autocomplete
                        #refVehicleModel
                        [options]="optionsModel"
                        [activeOption]="selectedVehicle.model"
                        [name]="'vehicleModel'"
                        [id]="'vehicleModel'"
                        [css]="(fieldModelSwitchToText) ? 'no-dropdown-btn' : ''"
                        [searchFromBegining]="false"
                        [allowEmptyValue]="false"
                        [allowCustomText]="fieldModelSwitchToText"
                        [delayedOptions]="true"
                        [required]="validateIsRequiredVehicleModel()"
                        [readonly]="fieldModelDisabled"
                        (onSelect)="actionOnModelFieldSelect($event, 'model')">
                      </sm-autocomplete>
                    </div>
                  </td>
                </tr> -->

                <tr *ngIf="!fieldModelSwitchToText" class="form-table__row" [ngClass]="{'is-required-field': refVehicleModel?.ngModel?.invalid}">

                  <td class="form-table__cell form-table__cell--label  u-width-130px">
                    <label for="vehicleModel">Model:</label>
                  </td>
                  <td class="form-table__cell u-width-220px">
                    <div>
                      <sm-autocomplete
                        #refVehicleModel
                        [options]="optionsModel"
                        [activeOption]="selectedVehicle.model"
                        [name]="'vehicleModel'"
                        [id]="'vehicleModel'"
                        [searchFromBegining]="false"
                        [allowEmptyValue]="false"
                        [delayedOptions]="true"
                        [required]="validateIsRequiredVehicleModel()"
                        [readonly]="fieldModelDisabled"
                        (onSelect)="actionOnModelFieldSelect($event, 'model')">
                      </sm-autocomplete>
                    </div>

                    <div class="localFormFieldIcon">
                      <button
                        type="button"
                        class="o-btn o-btn--action o-btn--i_round-plus"
                        (click)="modalEditModelManuallyOpen($event)"
                        title="Edit Manually"></button>
                    </div>
                  </td>
                </tr>

                <tr *ngIf="fieldModelSwitchToText" class="form-table__row" [class.is-required-field]="refVehicleModel?.invalid">
                  <td class="form-table__cell form-table__cell--label  u-width-130px">
                    <label for="vehicleModel">Model:</label>
                  </td>
                  <td class="form-table__cell u-width-220px">
                    <div>
                      <input
                        #refVehicleModelTextInput
                        #refVehicleModel="ngModel"
                        type="text"
                        name="vehicleModel"
                        id="vehicleModel"
                        maxlength="45"
                        [(ngModel)]="selectedVehicle.model"
                        [required]="validateIsRequiredVehicleModel()"
                        [readonly]="fieldModelDisabled"
                        [dataToSave]="selectedVehicle">
                    </div>
                  </td>
                </tr>

                <!--
                <tr class="form-table__row" *ngIf="!fieldTrimIsHidden" [class.is-required-field]="refVehicleTrim?.ngModel?.invalid">

                  <td class="form-table__cell form-table__cell--label  u-width-130px">
                    <label for="vehicleTrim">Trim:</label>
                  </td>
                  <td class="form-table__cell u-width-220px">

                    <div>
                      <sm-autocomplete
                        #refVehicleTrim
                        [options]="optionsTrim"
                        [activeOption]="selectedVehicle.trimLevel"
                        [name]="'vehicleTrim'"
                        [id]="'vehicleTrim'"
                        [css]="(trimFieldIsInTextMode) ? 'no-dropdown-btn' : ''"
                        [searchFromBegining]="false"
                        [allowEmptyValue]="trimFieldIsInTextMode"
                        [allowCustomText]="trimFieldIsInTextMode"
                        [delayedOptions]="true"
                        [required]="!trimFieldIsInTextMode"
                        [readonly]="fieldTrimDisabled && !trimFieldIsInTextMode"
                        (onSelect)="actionOnTrimFieldSelect($event, 'trim')">
                      </sm-autocomplete>
                    </div>
                  </td>
                </tr>
                -->
                <ng-container *ngIf="!fieldTrimIsHidden">
                  <ng-container *ngIf="trimFieldIsInTextMode">
                    <tr class="form-table__row">
                      <td class="form-table__cell form-table__cell--label  u-width-130px">
                        <label for="vehicleTrim">Trim:</label>
                      </td>
                      <td class="form-table__cell u-width-220px">
                        <input type="text" name="vehicleTrim" id="vehicleTrim" [(ngModel)]="selectedVehicle.trimLevel" [dataToSave]="selectedVehicle">
                      </td>
                    </tr>
                  </ng-container>

                  <ng-container *ngIf="!trimFieldIsInTextMode">
                    <tr class="form-table__row" [class.is-required-field]="refVehicleTrim?.ngModel?.invalid">
                      <td class="form-table__cell form-table__cell--label  u-width-130px">
                        <label for="vehicleTrim">Trim:</label>
                      </td>
                      <td class="form-table__cell u-width-220px">
                        <sm-autocomplete
                          #refVehicleTrim
                          [options]="optionsTrim"
                          [activeOption]="selectedVehicle.trimLevel"
                          [name]="'vehicleTrim'"
                          [id]="'vehicleTrim'"
                          [searchFromBegining]="false"
                          [allowEmptyValue]="false"
                          [allowCustomText]="false"
                          [delayedOptions]="true"
                          [required]="true"
                          [readonly]="fieldTrimDisabled"
                          (onSelect)="actionOnTrimFieldSelect($event, 'trim')">
                        </sm-autocomplete>
                      </td>
                    </tr>
                  </ng-container>
                </ng-container>


                <tr *ngIf="!fieldCcDisplacementIsHidden" class="form-table__row" [class.is-required-field]="refVehicleCc.invalid">
                  <td class="form-table__cell form-table__cell--label  u-width-130px">
                    <label for="vehicleVin">CC's:</label>
                  </td>
                  <td class="form-table__cell u-width-220px">
                    <input
                      #refVehicleCc="ngModel"
                      pattern="\d*"
                      (keydown)="numbersOnly($event)"
                      [required]="true"
                      type="text"
                      name="vehicleDisplacement"
                      id="vehicleDisplacement"
                      [(ngModel)]="selectedVehicle.displacement"
                      [dataToSave]="selectedVehicle">
                  </td>
                </tr>

                <tr class="form-table__row" [class.is-required-field]="validateIsRequiredVinField(selectedVehicle.vin, refVehicleVin) || vinIsRequiredIfMotorcycle()">
                  <td class="form-table__cell form-table__cell--label  u-width-130px">
                    <label for="vehicleVin">VIN:</label>
                  </td>
                  <td class="form-table__cell u-width-220px">
                    <input #refVehicleVin="ngModel" [required]="fieldVinRequired" type="text" name="vehicleVin" id="vehicleVin" maxlength="17"
                      #refInput
                      [ngModel]="selectedVehicle.vin"
                      (paste)="checkVinValue($event.clipboardData)"
                      (keypress)="keyPressAlphanumeric($event)"
                      (ngModelChange)="actionOnVinFieldNgModelChange($event, refInput)"
                      (blur)="actionOnVinFieldBlur()">
                      <!-- (keyup)="actionOnVinFieldTypeing($event, refVehicleVin)"-->

                      <div class="localFormFieldLoader">
                        <app-loader [loading]="loadingGetVehicleDetailByVINAndUpdateVehicleData" [sizeInPixels]="15" [cssClass]="'loader--transparent'"></app-loader>
                      </div>
                  </td>
                </tr>

                <tr class="form-table__row">
                  <td class="form-table__cell form-table__cell--label  u-width-130px">
                    <label for="vehicleLicensePlate">License Plate:</label>
                  </td>
                  <td class="form-table__cell u-width-220px">
                    <input #refVehicleLicensePlate="ngModel" type="text" name="vehicleLicensePlate" id="vehicleLicensePlate" maxlength="45"
                      required
                      [(ngModel)]="selectedVehicle.licensePlate"
                      [dataToSave]="selectedVehicle">
                  </td>
                </tr>

                <tr class="form-table__row" *ngIf="!fieldBodyStyleIsHidden" [ngClass]="{'is-required-field': refVehicleBodyStyle.ngModel?.invalid}">
                  <td class="form-table__cell form-table__cell--label  u-width-130px">
                    <label for="vehicleBodyStyle">Body Style:</label>
                  </td>
                  <td class="form-table__cell u-width-220px">
                    <sm-autocomplete
                      #refVehicleBodyStyle
                      [options]="optionsBodyStyles"
                      [activeOption]="selectedVehicle.bodyStyle"
                      [name]="'vehicleBodyStyle'"
                      [id]="'vehicleBodyStyle'"
                      [searchFromBegining]="false"
                      [allowEmptyValue]="false"
                      [allowCustomText]="false"
                      [required]="fieldBodyStyleRequired"
                      [readonly]="fieldBodyStyleDisabled"
                      (onSelect)="actionOnBodyStyleFieldSelect($event, 'bodyStyle')">
                    </sm-autocomplete>
                  </td>
                </tr>

              </table>
            </div> <!-- /.col -->
            <div class="col-xs-6">
              <table class="form-table">
                <tr class="form-table__row" [class.is-required-field]="refVehicleGaragingAddr.invalid">
                  <td class="form-table__cell form-table__cell--label  u-width-130px">
                    <label for="vehicleLocationAddress1">Garaging Address:</label>
                  </td>
                  <td class="form-table__cell u-width-220px">
                    <input
                      #refVehicleGaragingAddr="ngModel"
                      type="text"
                      name="vehicleLocationAddress1"
                      id="vehicleLocationAddress1"
                      maxlength="60"
                      [required]="validateisRequiredVehicleLocationGaragingAddress()"
                      [(ngModel)]="selectedVehicleLocationData.address1"
                      (change)="manageLocationsOnDataChangeTrigger()">
                  </td>
                  <td class="form-table__cell u-width-120px" #addressDiv *ngIf="addresses | async as addressList">
                    <button type="button" class="o-btn" style="padding:5px" pButton pRipple icon="pi pi-ellipsis" (click)="menu.toggle($event);setEvent(addressList)">
                      <i class="pi pi-ellipsis-h"></i>
                    </button>
                    <p-menu #menu [popup]="true" [model]="addressList" [style]="{'top':'30px', 'left': '10px'}" appendTo="body"></p-menu>
                  </td>
                </tr>

                <tr class="form-table__row" [class.is-required-field]="fieldCityIsRequired()">
                  <td class="form-table__cell form-table__cell--label  u-width-130px">
                    <label for="vehicleCity">City:</label>
                  </td>
                  <td class="form-table__cell u-width-220px">
                    <sm-autocomplete
                      #refVehicleCity
                      [options]="optionsCity"
                      [caseSensitiveOptionsIdMatching]="false"
                      [activeOption]="selectedVehicleLocationData.city"
                      [name]="'vehicleLocationCity'"
                      [id]="'vehicleLocationCity'"
                      maxlength="45"
                      [searchFromBegining]="false"
                      [allowEmptyValue]="false"
                      [allowCustomText]="false"
                      [delayedOptions]="true"
                      [required]="true"
                      (onSelect)="actionOnCityFieldSelect($event, 'city')">
                    </sm-autocomplete>
                  </td>
                </tr>

                <tr class="form-table__row" [class.is-required-field]="refVehicleState.ngModel?.invalid || refVehicleZipCode.invalid">
                  <td class="form-table__cell form-table__cell--label  u-width-130px">
                    <label for="vehicleState">State &amp; Zip:</label>
                  </td>
                  <td class="form-table__cell u-width-220px">
                    <div class="u-show-iblock u-width-65px u-spacing--right-1">
                      <sm-autocomplete
                        #refVehicleState
                        [options]="optionsState"
                        [activeOption]="selectedVehicleLocationData.state"
                        [css]="(refVehicleState.ngModel?.invalid) ? 'error' : ''"
                        [name]="'vehicleLocationState'"
                        [id]="'vehicleLocationState'"
                        maxlength="45"
                        [searchFromBegining]="false"
                        [allowEmptyValue]="false"
                        [allowCustomText]="false"
                        [readonly]="fieldLocationStateDisabled"
                        [required]="true"
                        (onSelect)="actionOnStateFieldSelect($event, 'state')">
                      </sm-autocomplete>
                    </div>
                    <div class="u-show-iblock u-width-65px">
                      <!-- input #refVehicleZipCode="ngModel" type="text" name="vehicleZipCode" id="vehicleZipCode" [(ngModel)]="selectedVehicleLocationData.zip" (blur)="updateVehicleLocationOnBlur($event)" [dataToSave]="selectedVehicleLocationData" -->
                      <input
                        #refVehicleZipCode="ngModel"
                        type="text"
                        name="vehicleLocationZip"
                        id="vehicleLocationZip"
                        [class]="(refVehicleZipCode.invalid) ? 'error' : ''"
                        [required]="validateisRequiredVehicleLocationZip()"
                        [(ngModel)]="selectedVehicleLocationData.zip"
                        pattern="^[0-9]{5}(?:-[0-9]{4})?$|^[0-9]{9}$"
                        style="width: 12ch;"
                        inputmode="numeric"
                        (change)="manageLocationsOnDataChangeTrigger()"
                        [mask]="'00000-0000||00000'"
                        >
                    </div>
                  </td>
                </tr>

                <!-- tr class="form-table__row" [class.is-required-field]="refVehicleAnnualMiles.invalid" -->
                <tr class="form-table__row" [class.is-required-field]="isInvalidAnnualMiles(selectedVehicle.annualMiles)">
                  <td class="form-table__cell form-table__cell--label  u-width-130px">
                    <label for="vehicleAnnualMiles">Annual Miles:</label>
                  </td>
                  <td class="form-table__cell u-width-220px">
                    <input #refVehicleAnnualMiles="ngModel" type="text" name="vehicleAnnualMiles" id="vehicleAnnualMiles"
                      [required]="fieldAnnualMilesIsRequired"
                      pattern="\d*"
                      maxlength="5"
                      (keydown)="numbersOnly($event)"
                      [(ngModel)]="selectedVehicle.annualMiles"
                      [dataToSave]="selectedVehicle">
                  </td>
                </tr>

                <tr class="form-table__row">
                  <td class="form-table__cell form-table__cell--label  u-width-130px">
                  </td>
                  <td class="form-table__cell u-width-220px">
                    <span class="u-cursor-default" (click)="checkbocCarrierModalHandler()">
                      <i class="o-btn o-btn--checkbox" [ngClass]="{'is-active': selectedVehicle.ignoreCarrierCityAndMiles}"></i>
                      Ignore carrier city and miles
                    </span>

                    <app-confirmbox
                      [launcher]="''"
                      [question]="'Ignore Carrier Garaging City and Miles?'"
                      [askAbout]="'Checking this box will skip the carrier\'s validation of the vehicle and could lead to inaccurate quotes.'"
                      [confirmBtnText]="'Yes, Ignore'"
                      [templateVersion]="2"
                      [isOpen]="checkbocCarrierModalIsOpen"
                      (onStateChange)="modalStateChange($event)"
                      (onAccept)="ignoreCarrierModalAccept()">
                    </app-confirmbox>
                  </td>
                </tr>

                <tr class="form-table__row" [class.is-required-field]="refVehicleValue.invalid">
                  <td class="form-table__cell form-table__cell--label  u-width-130px">
                    <label for="vehicleValue">Price New / Value:</label>
                  </td>
                  <td class="form-table__cell u-width-220px">
                    <input #refVehicleValue="ngModel" type="text" name="vehiclePriceValue" id="vehiclePriceValue"
                      pattern="\d*"
                      (keydown)="numbersOnly($event)"
                      [(ngModel)]="selectedVehicle.priceValue"
                      [required]="fieldPriceNewValueRequired"
                      [disabled]="!fieldPriceNewValueRequired"
                      (change)="actionOnPriceNewValueFieldChange()">
                      <!-- [dataToSave]="selectedVehicle" -->
                  </td>
                </tr>

                <tr class="form-table__row" *ngIf="!fieldSymbolsIsHidden">
                  <td class="form-table__cell form-table__cell--label  u-width-130px">
                    <label>Symbols:</label>
                  </td>
                  <td class="form-table__cell u-width-220px">
                    <div id="vehicleSymbolsPriceValue">
                      <button id="iso-vrg-modal" class="o-btn o-btn--link" (click)="openSymbolsModalbox($event)">View and Update</button>
                    </div>

                    <app-modalbox #refModalSymbols [launcher]="'#iso-vrg-modal'">
                      <app-vehicle-symbol
                        #refVehicleSymbols
                        [selectedVehicle]="selectedVehicle"
                        [selectedVehiclePlanSymbols]="selectedVehiclePlanSymbolsForSymbolsComponent"
                        [selectedVehicleGeneralDetails]="selectedVehicleDetails"
                        (onSymbolsUpdated)="onVehicleSymbolsUpdated($event)"
                      ></app-vehicle-symbol>
                      {{ helpCheckIfPriceValueVieldIsRequired(refVehicleSymbols.fieldPriceNewValueIsRequired) }}

                      <div class="u-spacing--2 u-flex u-flex--spread">
                        <div class="">
                          <a class="o-link u-color-pelorous js-not-available-yet">View Tips for Symbols</a>
                        </div>
                        <div class="">
                          <button (click)="refVehicleSymbols.callSymbolsUpdate($event); refModalSymbols.closeModalbox($event)" class="o-btn u-spacing--right-2">Save</button>
                          <button (click)="refVehicleSymbols.callResetSymbolsData($event); refModalSymbols.closeModalbox($event)" class="o-btn o-btn--idle">Cancel</button>
                        </div>
                      </div>
                    </app-modalbox>

                  </td>
                </tr>

                <tr class="form-table__row" [class.is-required-field]="refVehicleOwner.ngModel?.invalid">
                  <td class="form-table__cell form-table__cell--label  u-width-130px">
                    <label for="vehicleOwner">Owner:</label>
                  </td>
                  <td class="form-table__cell u-width-220px">
                    <sm-autocomplete
                      #refVehicleOwner
                      [options]="optionsOwner"
                      [activeOption]="parseOperatorOwnerDataToOption(selectedVehicle.owner, optionsOwner)"
                      [name]="'vehicleOwner'"
                      [id]="'vehicleOwner'"
                      [searchFromBegining]="false"
                      [allowEmptyValue]="false"
                      [allowCustomText]="false"
                      [required]="true"
                      (onSelect)="actionOnOwnerOperatorFieldsSelect($event, 'owner')">
                    </sm-autocomplete>
                  </td>
                </tr>

                <tr class="form-table__row" [class.is-required-field]="refVehicleOperator.ngModel?.invalid || checkIfVehicleIsExcludedForDriverByDriverMetaHref(selectedVehicle?.operator?.meta?.href)">
                  <td class="form-table__cell form-table__cell--label  u-width-130px">
                    <label for="vehicleOperator">Operator:</label>
                  </td>
                  <td class="form-table__cell u-width-220px">
                    <sm-autocomplete
                      #refVehicleOperator
                      [options]="optionsOperator"
                      [activeOption]="parseOperatorOwnerDataToOption(selectedVehicle.operator, optionsOperator)"
                      [name]="'vehicleOperator'"
                      [id]="'vehicleOperator'"
                      [searchFromBegining]="false"
                      [allowEmptyValue]="false"
                      [allowCustomText]="false"
                      [required]="true"
                      (onSelect)="actionOnOwnerOperatorFieldsSelect($event, 'operator')">
                    </sm-autocomplete>

                  </td>
                </tr>

              </table>
            </div> <!-- /.col -->
          </div> <!-- / .row -->
        </form> <!-- / #formVehicleDetails -->

      </div> <!-- / .box -->
    </div>
  </div> <!-- / .row -->
</section>

<app-modalbox #refModalExcludedVehicle [css]="'u-width-380px'">
  <h2 class="u-t-size--1-5rem u-color-sunset u-align-left">Warning!</h2>
  <p class="u-spacing--1-5 u-spacing--bottom-2 u-align-left">
    You can not set driver <span class="u-color-sunset">{{excludedForDriverName}}</span> as operator, because this vehicle is excluded
    for this driver.
  </p>

  <hr>

  <div class="row u-spacing--1">
    <div class="col-xs-12 u-align-right">
      <button type="button" (click)="refModalExcludedVehicle.close()" class="o-btn u-spacing--right-1">OK</button>
    </div>
  </div>
</app-modalbox>

<app-modalbox #refModalEditManuallyModel [css]="'u-width-380px'">
  <h2 class="u-t-size--1-5rem u-color-sunset u-align-left">Manually Enter Model?</h2>
  <p class="u-spacing--1-5 u-spacing--bottom-2 u-align-left">
      Would you like to manually enter a Vehicle Model that is currently
      unavailable for selection?
  </p>

  <hr>

  <div class="row u-spacing--2">
    <div class="col-xs-12 u-align-right">
      <button type="button" (click)="modalEditModelManuallyContinue()" class="o-btn u-spacing--right-1">Yes, Continue</button>
      <button type="button" (click)="modalEditModelManuallyCancel()" class="o-btn o-btn--idle">Cancel</button>
    </div>
  </div>
</app-modalbox>


<app-vehicle-options
  *ngIf="this.vehicles.length && !componentVehicleOptionsIsHidden"
  [selectedVehicle]="selectedVehicle"
  [selectedVehicleGeneralDetails]="selectedVehicleGeneralDetailsForOptionsUpdate"
  (onOptionsUpdated)="onVehicleOptionsUpdate($event)">
</app-vehicle-options>

<app-leave-quote #leaveQuote></app-leave-quote>
