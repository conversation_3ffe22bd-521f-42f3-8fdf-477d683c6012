import { fakeAsync, inject, TestBed, tick } from '@angular/core/testing';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';

import { Subject } from 'rxjs';

import { expectLastCallArgs } from 'testing/helpers/data-expect';
import {
    MockActivatedRoute, MockActivatedRouteProvider
} from 'testing/stubs/activated-route.provider';
import { MockRouter, MockRouterProvider } from 'testing/stubs/router.provider';

import { Helpers } from 'app/utils/helpers';

import { WarningDefinitionI, WarningItem } from '../model/warnings';
import { CURRENT_URL_VARIABLE, HintsAndWarningsService } from './hints-and-warnings.service';

const ALWAYS_TRUE_WARNING = Helpers.deepFreeze({
  label: 'always true error',
  condition: true,
  group: 'test'
});

const TRUE_IF_SHORT_WARNING = Helpers.deepFreeze({
  label: 'short error',
  condition: (fieldValue, fullObjectToCheck?, additionalData?) => {
    return String(fieldValue).length < 4;
  },
  group: 'test'
});

const TRUE_IF_NOT_NUMBER = Helpers.deepFreeze({
  label: (fieldValue, fullObjectToCheck?, additionalData?) => {
    return String(fieldValue) + ' is not a number error';
  },
  condition: (fieldValue, fullObjectToCheck?, additionalData?) => {
    return Number.isNaN(Number(fieldValue));
  },
  group: 'numbers'
});

const DEFINITIONS: WarningDefinitionI[] = Helpers.deepFreeze([
  {
    id: 'propertyA',
    deepId: 'propertyA.fieldA.name',
    viewUri: CURRENT_URL_VARIABLE,
    viewFieldId: 'propertyA-fieldA-name',
    warnings: [
      ALWAYS_TRUE_WARNING,
      TRUE_IF_SHORT_WARNING,
      TRUE_IF_NOT_NUMBER
    ]
  }, {
    id: 'propertyB',
    deepId: 'propertyB.fieldC.quantity',
    viewUri: (fullObjectToCheck?: any) => {
      return 'numbers-uri';
    }  ,
    viewFieldId: (fullObjectToCheck?: any) => {
      return 'propertyB-fieldC-quantity-' + fullObjectToCheck.propertyB.fieldC.quantity;
    },
    warnings: [
      TRUE_IF_SHORT_WARNING,
      TRUE_IF_NOT_NUMBER
    ]
  }
]);

const SINGLE_DATA_ITEM: any = Helpers.deepFreeze({
  propertyA: {
    fieldA: {
      name: 'A-name-value',
      count: 42
    },
    fieldB: {
      name: 'B-name-value',
      query: 'query text'
    }
  },
  propertyB: {
    fieldA: {
      name: 'Another-name-value'
    },
    fieldC: {
      name: {
        prefix: 'prx',
        suffix: 'sfx',
        text: 'something'
      },
      quantity: 4
    }
  }
});

const SECOND_DATA_ITEM = Helpers.deepFreeze({
  propertyB: {
    fieldC: {
      quantity: 'a lot'
    }
  }
});

const THIRD_DATA_ITEM = Helpers.deepFreeze({
  propertyB: {
    fieldC: {
      quantity: 5690
    }
  }
});

describe('Service: HintsAndWarningsService', () => {
  let service: HintsAndWarningsService;
  let data$: Subject<any>;
  let additionalData$: Subject<any>;
  let warningsSpy: any;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        HintsAndWarningsService,
        MockActivatedRouteProvider,
        MockRouterProvider
      ]
    });
  });

  beforeEach(inject([HintsAndWarningsService, Router, ActivatedRoute],
    (srv: HintsAndWarningsService, router: MockRouter, activatedRoute: MockActivatedRoute) => {
    service = srv;

    router.events.next(new NavigationEnd(0, 'test-url#propertyA-fieldA-name', null));
    activatedRoute.fragment.next('propertyA-fieldA-name');

    data$ = new Subject<any>();
    additionalData$ = new Subject<any>();
  }));

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should allow to access current URL', inject([Router, ActivatedRoute], (router: MockRouter, activatedRoute: MockActivatedRoute) => {
    const spy = jasmine.createSpy('urlSpy');
    service.getRouteCurrentUrl$.subscribe(spy);

    router.events.next(new NavigationEnd(0, 'some-url#fragment', null));
    activatedRoute.fragment.next('fragment');

    expectLastCallArgs(spy).toEqual(['some-url']);
  }));

  describe('when there is a singular data item to monitor', () => {
    let fragmentEl: any;

    beforeEach(fakeAsync(() => {
      service.watchObjectDataForHintsAndWarnings(DEFINITIONS, data$, 'group');

      warningsSpy = jasmine.createSpy('warningsSpy');
      service.allWarnings$.subscribe(warningsSpy);

      fragmentEl = {
        id: 'propertyA-fieldA-name',
        classList: jasmine.createSpyObj('classList', ['add', 'remove'])
      };
      spyOn(document, 'getElementById').and.returnValue(fragmentEl);

      data$.next(SINGLE_DATA_ITEM);
      tick();
    }));

    it('should generate warnings', () => {
      expect(warningsSpy).toHaveBeenCalled();
    });

    it('should include warnings with fixed true display condition', () => {
      expectLastCallArgs(warningsSpy).toEqual([
        jasmine.arrayContaining([
          jasmine.objectContaining({
            showWarning: true,
            label: 'always true error',
            group: 'test',
            viewUri: CURRENT_URL_VARIABLE,
            viewFieldId: 'propertyA-fieldA-name'
          })
        ])
      ]);
    });

    it('should include warnings with calculated display condition', () => {
      expectLastCallArgs(warningsSpy).toEqual([
        jasmine.arrayContaining([
          jasmine.objectContaining({
            showWarning: true,
            label: 'short error',
            group: 'test',
            viewUri: 'numbers-uri',
            viewFieldId: 'propertyB-fieldC-quantity-4',
          })
        ])
      ]);
    });

    it('should correctly handle numeric values', () => {
      expectLastCallArgs(warningsSpy).not.toEqual([
        jasmine.arrayContaining([
          jasmine.objectContaining({
            showWarning: true,
            label: '4 is not a number error',
            group: 'test',
            viewUri: 'numbers-uri',
            viewFieldId: 'propertyB-fieldC-quantity-4',
          })
        ])
      ]);
    });

    it('should make it possible to clear all the warnings', fakeAsync(() => {
      service.clearAllWarnings();
      tick();
      expectLastCallArgs(warningsSpy).toEqual([[]]);
    }));

    it('should add warn CSS class to element if it has warning associated', () => {
      expect(fragmentEl.classList.add).toHaveBeenCalled();
      expect(fragmentEl.classList.remove).toHaveBeenCalled();
    });
  });

  describe('when there is data items array to monitor', () => {
    beforeEach(fakeAsync(() => {
      service.watchObjectDataForHintsAndWarnings(DEFINITIONS, data$, 'group');

      warningsSpy = jasmine.createSpy('warningsSpy');
      service.allWarnings$.subscribe(warningsSpy);

      data$.next([SINGLE_DATA_ITEM, SECOND_DATA_ITEM]);
      tick();
    }));

    it('should generate warnings', () => {
      expect(warningsSpy).toHaveBeenCalled();
    });

    it('should generate warnings for each item', () => {
      expectLastCallArgs(warningsSpy).toEqual([
        jasmine.arrayContaining([
          jasmine.objectContaining({
            showWarning: true,
            label: 'short error',
            group: 'test',
            viewUri: 'numbers-uri',
            viewFieldId: 'propertyB-fieldC-quantity-4',
          }),
          jasmine.objectContaining({
            showWarning: true,
            label: 'a lot is not a number error',
            group: 'numbers',
            viewUri: 'numbers-uri',
            viewFieldId: 'propertyB-fieldC-quantity-a lot',
          })
        ])
      ]);
    });

    it('should handle near instant data array updates', fakeAsync(() => {
      data$.next([SINGLE_DATA_ITEM, SECOND_DATA_ITEM, THIRD_DATA_ITEM]);
      tick();

      expectLastCallArgs(warningsSpy).toEqual([
        jasmine.arrayContaining([
          jasmine.objectContaining({
            viewFieldId: 'propertyB-fieldC-quantity-4',
          }),
          jasmine.objectContaining({
            viewFieldId: 'propertyB-fieldC-quantity-a lot',
          }),
          jasmine.objectContaining({
            viewFieldId: 'propertyB-fieldC-quantity-5690',
          }),
        ])
      ]);
    }));

    it('should handle delayed data array updates', fakeAsync(() => {
      tick(400);

      data$.next([SINGLE_DATA_ITEM, SECOND_DATA_ITEM, THIRD_DATA_ITEM]);
      tick();

      expectLastCallArgs(warningsSpy).toEqual([
        jasmine.arrayContaining([
          jasmine.objectContaining({
            viewFieldId: 'propertyB-fieldC-quantity-4',
          }),
          jasmine.objectContaining({
            viewFieldId: 'propertyB-fieldC-quantity-a lot',
          }),
          jasmine.objectContaining({
            viewFieldId: 'propertyB-fieldC-quantity-5690',
          }),
        ])
      ]);
    }));
  });

  describe('when there are multiple data items to monitor', () => {
    beforeEach(fakeAsync(() => {
      service.watchObjectDataForHintsAndWarnings(DEFINITIONS, data$, 'group');
      service.watchObjectDataForHintsAndWarnings(DEFINITIONS, additionalData$, 'another-group');

      warningsSpy = jasmine.createSpy('warningsSpy');
      service.allWarnings$.subscribe(warningsSpy);

      data$.next(Helpers.deepClone(SINGLE_DATA_ITEM));
      tick(400);

      additionalData$.next(Helpers.deepClone(SECOND_DATA_ITEM));
      tick(400);
    }));

    it('generates warnings', () => {
      expect(warningsSpy).toHaveBeenCalled();
    });

    it('merges warnings from both data sources', () => {
      expectLastCallArgs(warningsSpy).toEqual([
        jasmine.arrayContaining([
          jasmine.objectContaining({
            viewFieldId: 'propertyB-fieldC-quantity-4',
          }),
          jasmine.objectContaining({
            viewFieldId: 'propertyB-fieldC-quantity-a lot',
          })
        ])
      ]);
    });
  });

  describe('when there is additional data to monitor', () => {
    beforeEach(fakeAsync(() => {
      service.watchObjectDataForHintsAndWarningsWithAdditionalData(DEFINITIONS, data$, 'group', additionalData$);

      warningsSpy = jasmine.createSpy('warningsSpy');
      service.allWarnings$.subscribe(warningsSpy);

      data$.next(Helpers.deepClone(SINGLE_DATA_ITEM));
      tick(400);
    }));

    it('generates warnings', () => {
      expect(warningsSpy).toHaveBeenCalled();
    });

    it('re-generates warnings if the additional data was updated', fakeAsync(() => {
      const anotherSpy = jasmine.createSpy('anotherSpy');

      service.allWarnings$.subscribe(anotherSpy);

      additionalData$.next({ additional: 'additionalValue' });
      tick(400);

      expect(anotherSpy).toHaveBeenCalled();
    }));
  });

  // panel state should probably be separate service, it has nothing in common with other properties
  describe('regarding panel state', () => {
    it('should make it possible to request panel opening', fakeAsync(() => {
      const panelSpy = jasmine.createSpy('panelStateSpy');
      service.panelState$.subscribe(panelSpy);

      service.notifyWarningPanelShouldBeOpen(true);
      tick();

      expectLastCallArgs(panelSpy).toEqual([true]);
    }));

    it('should make it possible to notify about panel state', fakeAsync(() => {
      const panelSpy = jasmine.createSpy('panelStateSpy');
      service.panelStateIsOpen$.subscribe(panelSpy);

      service.setPanelStateIsOpen(true);
      tick();

      expectLastCallArgs(panelSpy).toEqual([true]);
    }));
  });

  // navigation utilities should probably be separate service
  describe('regarding navigation', () => {
    const warningItem: WarningItem = Object.assign(new WarningItem(), {
      label: 'Error label',
      showWarning: true,
      viewUri: CURRENT_URL_VARIABLE,
      viewFieldId: 'fieldId',
      carrier: [],
      uqId: 'uniqueId',
      uqGroupId: 'uniqueGroupId',
      group: 'test',
      queryParams: {}
    });
    // const warningItem: WarningItem = {
    //   label: 'Error label',
    //   showWarning: true,
    //   viewUri: CURRENT_URL_VARIABLE,
    //   viewFieldId: 'fieldId',
    //   carrier: [],
    //   uqId: 'uniqueId',
    //   uqGroupId: 'uniqueGroupId',
    //   group: 'test',
    //   queryParams: {}
    // };
    const containerEl = {
      id: 'scrollContainer'
    };
    const fragmentEl = {
      id: 'fragment',
      focus: jasmine.createSpy('focus'),
      classList: {
        add: jasmine.createSpy('add')
      },
      offsetTop: 5,
      offsetParent: containerEl
    };

    it('should allow to navigate from WarningItem to correct document fragment', fakeAsync(inject([Router], (router: MockRouter) => {
      spyOn(router, 'navigate');

      router.events.next(new NavigationEnd(0, 'test-uri', null));
      tick();

      service.goTo(warningItem);
      tick();

      expectLastCallArgs(router.navigate).toEqual([
        ['test-uri'], {
          fragment: 'fieldId',
          queryParams: {}
        }]);
    })));

    it('should make it possible to focus the element from fragment', fakeAsync(() => {
      spyOn(document, 'getElementById').and.returnValues(fragmentEl, containerEl);

      service.handleFindFocusAndInteractWithWarnElement('fragment', 'scrollContainer');
      tick();

      expect(fragmentEl.focus).toHaveBeenCalled();
    }));

    it('should make it possible to retry focusing the element from fragment', fakeAsync(() => {
      spyOn(document, 'getElementById').and.returnValues(undefined, undefined, fragmentEl, containerEl);

      service.handleFindFocusAndInteractWithWarnElement('fragment', 'scrollContainer');

      tick();
      tick(500);
      tick();

      expect(fragmentEl.focus).toHaveBeenCalled();
    }));
  });
});
