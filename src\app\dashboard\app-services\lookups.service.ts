import { map } from 'rxjs/operators';
import { Injectable } from '@angular/core';

import { Observable, BehaviorSubject, of } from 'rxjs';
import { ApiService } from 'app/shared/services/api.service';
import { FilterOption } from 'app/app-model/filter-option';
import { ClientDetails } from 'app/app-model/client';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { PrefillRmv } from 'app/app-model/PrefillRMV';
import { OwnerLookupRequest } from 'app/app-model/owner-lookup';

const VEHICLES_URI = '/lookups/vehicles/modelYears/';

@Injectable()
export class LookupsService {
  private newRmvClientInfo: ClientDetails = new ClientDetails();
  private _newRmvClientInfo: BehaviorSubject<any> = new BehaviorSubject(
    this.newRmvClientInfo
  );

  constructor(
    private apiService: ApiService,
    private apiCommonService: ApiCommonService
  ) {}

  // Get Vehicle Makes
  public getVehicleMakes(year: string): Observable<any> {
    const uri = VEHICLES_URI + year + '/makes';

    return this.apiCommonService.getByUri(uri);
  }

  public getVehicleMakesAsOptions(year: string): Observable<FilterOption[]> {
    return this.getVehicleMakes(year).pipe(
      map(res => {
        let options = [];
        options = res.items.map(item => ({ id: item.name, text: item.name }));
        return options;
      })
    );
  }

  // Get Vehicle Models
  public getVehicleModels(year: string, make: string): Observable<any> {
    const uri =
      VEHICLES_URI + year + '/makes/' + encodeURIComponent(make) + '/models/';

    return this.apiCommonService.getByUri(uri);
  }

  public getVehicleModelsAsOptions(
    year: string,
    make: string
  ): Observable<FilterOption[]> {
    return this.getVehicleModels(year, make).pipe(
      map(res => {
        let options = [];
        options = res.items.map(item => ({
          id: item.name,
          text: item.name
        }));
        return options;
      })
    );
  }

  // Get Vehicle Model Details
  public getVehicleModelDetails(
    year: string,
    make: string,
    model: string,
    policyEffectiveDate: string = ''
  ): Observable<any> {
    let uri =
      VEHICLES_URI +
      year +
      '/makes/' +
      encodeURIComponent(make) +
      '/models/' +
      encodeURIComponent(model);

    if (policyEffectiveDate) {
      uri += '?policyEffectiveDate=' + policyEffectiveDate;
    }

    return this.apiCommonService.getByUri(uri);
  }

  // Get Vehicle Data By VIN Number
  public getVehicleDataByVin(
    vin: string,
    policyEffectiveDate: string = ''
  ): Observable<any> {
    if (vin) {
      vin = vin.toUpperCase();
    }

    let uri = '/lookups/vehicles/vins/' + vin;

    if (policyEffectiveDate) {
      uri += '?policyEffectiveDate=' + policyEffectiveDate;
    }

    return this.apiCommonService.getByUri(uri);
  }

  public postVehicleDataByVin(data: any): Observable<any> {
    const uri = '/lookups/rmv/vehicles/vins';

    return this.apiCommonService.postByUri(uri, data);
  }

  public postVehiclesDataByVin(data: any, ): Observable<any> {
    const uri = `/lookups/rmv/vehicles/vin`;

    return this.apiCommonService.postByUri(uri, data);
  }

  public updateVehicleDataByVin(data: any): Observable<any> {
    const uri = '/lookups/rmv/vehicles/vins';

    return this.apiCommonService.putByUri(uri, data);
  }

  public postVehicleDataByPlate(data: any): Observable<any> {
    const uri = '/lookups/rmv/vehicles/plates';

    return this.apiCommonService.postByUri(uri, data);
  }

  public postVehiclesDataByPlate(data: any): Observable<any> {
    const uri = `/lookups/rmv/vehicles/plate`;

    return this.apiCommonService.postByUri(uri, data);
  }

  public updateVehicleDataByPlate(data: any): Observable<any> {
    const uri = '/lookups/rmv/vehicles/plates';

    return this.apiCommonService.putByUri(uri, data);
  }

  // New quote by RMV lookup
  public newRmvQuote(data: any): Observable<any> {
    const uri = '/lookups/rmv/drivers/newquote';

    return this.apiCommonService.postByUri(uri, data);
  }

  public updateNewRmvClient(client): void {
    this.newRmvClientInfo = client;
    this._newRmvClientInfo.next(this.newRmvClientInfo);
  }

  public get getNewRmvClient$(): Observable<any> {
    return this._newRmvClientInfo.asObservable();
  }

  // Vehicle Plan Symbols
  public postVehiclePlanSymbols(
    vehicleYear: string,
    data: any
  ): Observable<any> {
    const uri = VEHICLES_URI + vehicleYear + '/planSymbols';

    return this.apiCommonService.postByUri(uri, data);
  }

  public getMaipDownPayment(data: any): Observable<any> {
    const uri = '/lookups/MaipArcDownpayment';

    return this.apiCommonService.postByUri(uri, data);
  }

  public getLienholders(
    searchCriteria = '',
    criteriaType = 'nameOrCode',
    limit = 10,
    offset = 0
  ) {

    const uri = `/specs/lienholders?limit=${limit}&${criteriaType}=${searchCriteria}&offset=${offset}`;
    return this.apiCommonService.getByUri(uri);
  }

  public getLessors(searchCriteria = '', criteriaType = 'name', offset = 0) {
    const limit = 10;
    const uri = `/specs/lessors?limit=${limit}&${criteriaType}=${searchCriteria}&offset=${offset}`;
    return this.apiCommonService.getByUri(uri);
  }

  public prefillRMVForm(prefill: PrefillRmv) {
    const uri = `/lookups/prefillRmvForm`;
    return this.apiCommonService.postByUri(uri, prefill);
  }

  public getEstamps(arc= false) {
     const uri = arc ? `estamps/providersonly?arcEnabled=1` : `/estamps/providers`;
    return this.apiCommonService.getByUri(uri);
  }

  public getEstampsAsOptions() {
    return this.getEstamps().pipe(map(res => {
      let options = [];
      options = res.map(item => ({id: item.companyCode, text: `${item.writingCompanyName} (${item.companyCode})`}));
      return options;
    }));
  }



  public getAgencyFees(type) {
    const uri = `/specs/rmvservices/agentfees?transactionType=${type}`;
    return this.apiCommonService.getByUri(uri);
  }


  public getAgencyFeesAsOptions(type) {
  return this.getAgencyFees(type).pipe(map(res => {
    console.log(res);
    let options = [];
    options = res.items[0].fees.map(fee => ({id: fee.amount.cents, text: fee.amount.dollar}));
    return options;
  }));
  }

  vehicleLookup(vehicle, lookupType = '') {
    const uri = `/lookups/vehicle?lookuptype=${lookupType}`;
    return this.apiCommonService.postByUri(uri, vehicle);

  }

  ownerLookup(request: OwnerLookupRequest) {
    const uri = `/lookups/owner`;
    return this.apiCommonService.postByUri(uri, request);
  }

  plateLookup(request) {
    const uri = `/lookups/vehicle/plate`;
    return this.apiCommonService.postByUri(uri, request);
  }
}

