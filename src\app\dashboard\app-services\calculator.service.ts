import { Injectable } from '@angular/core';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { Observable } from 'rxjs';

export interface IProRateRequestData {
  EffectiveDate: string;
  CancelDate: string;
  OriginalAmount: number;
}

export interface IProRateResponseData {
  calculatedAmount: number;
}

export interface IShortRateRequestData {
  EffectiveDate: string;
  CancelDate: string;
  OriginalAmount: number
}

export interface IShortRateResponseData {
  calculatedAmount: number;
}

@Injectable()
export class CalculatorService {

  constructor(
    private apiCommonService: ApiCommonService
  ) { }

  public getProRate(data: IProRateRequestData): Observable<IProRateResponseData> {
    return this.apiCommonService.postByUri('/prorate', data);
  }

  public getShortRate(data: IShortRateRequestData): Observable<IShortRateResponseData> {
    return this.apiCommonService.postByUri('/shortrate', data);
  }

}
