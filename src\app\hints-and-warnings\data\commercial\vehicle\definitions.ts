import { WarningDefinitionI, WARNING_GROUPS } from './../../../model/warnings';
import { Validate } from 'app/hints-and-warnings/validators';
import { CommercialVehicle } from 'app/app-model/vehicle';

const generateViewUrl = (fullObj): string => {
  return  `/dashboard/commercial-auto/quotes/${fullObj.quoteSessionId}/vehicles`; // + '?overlay=info&type=client';
};

const OriginalCostCheck = (value, fullObj, additionalData) => {
  return value > 0 ? false : true;
};

const NullCheck = (value, fullObj, additionalData) => {
  console.log(value, fullObj);
  return value === null || value === '' ? true : false;
};


function returnOriginalCostId(fullObj: CommercialVehicle): string {
  return `originalCostNew_${fullObj.vin}`;
 }

 function returnTypeId(fullObj: CommercialVehicle): string {
  return `com-veh-type_${fullObj.vin}`;
 }

 function returnGarageId(fullObj: CommercialVehicle): string {
  return `com-gar_${fullObj.vin}`;
 }

 function returnUsageId(fullObj: CommercialVehicle): string {
  return `com-veh-use_${fullObj.vin}`;
 }

 function generateUqOptionViewInteractId(veh): string {
  return `edit-${veh.resourceId}`;
  // return policy.viewUqId + '_checkbox';
}


// Liberty Specific (298)
const PriceGreaterThan0: WarningDefinitionI = {
  id: 'priceValue',
  deepId: 'priceValue',
  viewUri: generateViewUrl,
  viewFieldId: returnOriginalCostId ,
  viewFieldInteractId: generateUqOptionViewInteractId,
  warnings: [{
    label: 'Original Cost New must be greater than 0',
    condition: NullCheck,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};
const VehicleUseRequired: WarningDefinitionI = {
  id: 'usage',
  deepId: 'usage',
  viewUri: generateViewUrl,
  viewFieldId: returnUsageId ,
  viewFieldInteractId: generateUqOptionViewInteractId,
  warnings: [{
    label: 'Usage is required',
    condition: NullCheck,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};
const VehicleTypeRequired: WarningDefinitionI = {
  id: 'vehicleType',
  deepId: 'vehicleType',
  viewUri: generateViewUrl,
  viewFieldId: returnTypeId ,
  viewFieldInteractId: generateUqOptionViewInteractId,
  warnings: [{
    label: 'Type is required',
    condition: NullCheck,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const VehicleGaragingRequired: WarningDefinitionI = {
  id: 'garagingAddress',
  deepId: 'garagingAddress',
  viewUri: generateViewUrl,
  viewFieldId: returnGarageId ,
  viewFieldInteractId: generateUqOptionViewInteractId,
  warnings: [{
    label: 'Garaging is required',
    condition: NullCheck,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};


export const WARNINGS_DEFINITIONS_COM_AUTO_VEHICLES: WarningDefinitionI[] = [
PriceGreaterThan0,
VehicleUseRequired,
VehicleTypeRequired,
VehicleGaragingRequired

];
