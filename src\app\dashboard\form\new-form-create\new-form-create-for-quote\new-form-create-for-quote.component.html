<div class="row">
  <div class="col-xs-12">
    <h1 class="o-heading o-heading--for-section o-heading--notransform">New Form</h1>
  </div>
</div>

<hr>

<div class="row u-spacing--1-5">
  <div class="col-xs-12">
    <app-loader [loading]="loadingForms"></app-loader>

    <h3 class="o-heading">Select Form</h3>

    <div class="u-spacing--1-5">
      <p-scrollPanel styleClass="u-height-max-340px">
        <div class="u-padd--bottom-1 u-width-100">
          <table class="table table--compact table--hoverable form-table u-width-100">
            <tbody class="table__tbody">
              <tr class="table__tr" *ngFor="let row of arrFormsAll;" (click)="setSelectedForm(row)">
                <td class="table__td">
                  <div class="checklist__btns">
                    <label class="o-checkable">
                      <input type="radio" name="form-group" [checked]="isFormSelected(row.number)" (change)="setSelectedForm(row)" />
                      <i class="o-btn o-btn--radio"></i>
                    </label>
                  </div>
                </td>
                <td class="table__td u-color-pelorous">
                  <a class="modal-launcher-quote-inbox">{{row.name}}</a>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </p-scrollPanel>
    </div>

    <p class="local-info">
      New Business Applications requiring Premiums should be added from the Plan Summary screen
    </p>
  </div>
</div>

<div class="row u-spacing--2">
  <div class="col-xs-12 u-align-right">
    <button
      type="button"
      class="o-btn u-spacing--right-1"
      (click)="actionCreateForm()">Create Form</button>
    <button type="button" class="o-btn o-btn--idle" (click)="actionClose()">Close</button>
  </div>
</div>

<div class="modalNestedModals">
  <app-modalbox #refModalQuoteNotSaved [css]="'u-width-430px horizontal-centered'" (click)="$event.stopPropagation()">
    <h2 class="u-t-size--1-5rem u-color-sunset u-align-left">Save Quote?</h2>
    <p class="u-spacing--1-5 u-spacing--bottom-2 u-align-left u-color-sun-juan">
        In order to prefill the form with quote data and to save the form, your
        quote will need to be saved. Do you want to save your quote before proceeding?
    </p>
    <hr>
    <div class="row u-spacing--2">
      <div class="col-xs-12 u-align-right">
        <button type="button" class="o-btn u-spacing--right-1" (click)="actionSaveQuoteYes()">Yes, Save Quote</button>
        <button type="button" class="o-btn o-btn--outlined u-spacing--right-1" (click)="actionSaveQuoteNo()">Don't Save</button>
        <button type="button" class="o-btn o-btn--idle" (click)="actionSaveQuoteCancel()">Close</button>
      </div>
    </div>
  </app-modalbox>



  <app-modalbox #refModalPlanSelector [css]="'u-width-500px horizontal-centered'" (click)="$event.stopPropagation()">
    <app-plan-selector *ngIf="refModalPlanSelector.isOpen"
      [selectedForm]="selectedForm"
      (onNewFormAutoClick)="actionPlanSelectorOnNewFormAutoClick()"
      (onCancelClick)="refModalPlanSelector.close()">
    </app-plan-selector>
  </app-modalbox>



  <app-modalbox #refModalNoFormSelected [css]="'u-width-300px horizontal-centered'" (click)="$event.stopPropagation()">
    <h1 class="o-heading o-heading--red u-spacing--bottom-1-5">No form selected.</h1>
    <div class="box box--silver u-spacing--1-5 u-flex u-flex--spread u-flex--to-top">
      Please select a form.
    </div>
    <div class="row u-spacing--2">
      <div class="col-xs-12 u-align-right">
        <hr class="o-hr u-spacing--bottom-1-5" />
        <button (click)="refModalNoFormSelected.closeModalbox()" class="o-btn o-btn--idle u-spacing--left-2">Close</button>
      </div>
    </div>
  </app-modalbox>
</div>
