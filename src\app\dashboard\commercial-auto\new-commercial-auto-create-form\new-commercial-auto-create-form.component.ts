import { Observable } from 'rxjs';
import { ComDriver } from 'app/app-model/driver';
import { Quote } from 'app/app-model/quote';
import { Component, OnInit, Input, OnDestroy, ViewChild, EventEmitter, Output } from '@angular/core';
import { FilterOption } from 'app/app-model/filter-option';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { AgencyUserService, UserData } from 'app/shared/services/agency-user.service';
import { SubsService } from 'app/dashboard/app-services/subs.service';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { PlansSelectorComponent } from 'app/shared/components/plans-selector/plans-selector.component';
import { LeaveQuoteComponent } from 'app/shared/components/leave-quote/leave-quote.component';
import { Router } from '@angular/router';
import { RouteService } from 'app/shared/services/route.service';
import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { DriversService } from 'app/dashboard/app-services/drivers.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { ClientsService, CLIENT_TYPES } from 'app/dashboard/app-services/clients.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { VehiclesService } from 'app/dashboard/app-services/vehicles.service';
import { CoveragesService } from 'app/dashboard/app-services/coverages.service';
import { LeaveQuoteService } from 'app/shared/services/leave-quote.service';
import { ClientDetails, ClientContactMethod, ClientAddress } from 'app/app-model/client';
import { LocationsService } from 'app/dashboard/app-services/locations.service';
import { LocationData } from 'app/app-model/location';
import { ComAutoPolicyCoverageService } from 'app/dashboard/app-services/com-auto-policy-coverage.service';
import { CoverageItem, Coverage, CoverageItemValue, CoverageValue, CoveragesData } from 'app/app-model/coverage';
import { resolve } from 'q';
import { ComCoveredAutoSymbolsService } from 'app/dashboard/app-services/com-covered-auto-symbols.service';
import { CoveredAutoSymbols } from 'app/app-model/com-covered-auto-symbols';
import { first, take } from 'rxjs/operators';
import { parseISO, format, isBefore, startOfDay, addYears } from 'date-fns';

interface EventToEmitInterface {
  event: Event;
}

@Component({
    selector: 'app-new-commercial-auto-create-form',
    templateUrl: './new-commercial-auto-create-form.component.html',
    styleUrls: ['./new-commercial-auto-create-form.component.scss'],
    standalone: false
})
export class NewCommercialAutoCreateFormComponent implements OnInit, OnDestroy {

  @Input()
  public get effectiveDate(): string { return this.modelEffectiveDate; }
  public set effectiveDate(val: string) {
    if (val) {
      this.modelEffectiveDate = val;
    } else {
      this.modelEffectiveDate = null;
    }
  }

  @Output() public onCancelClick: EventEmitter<EventToEmitInterface> = new EventEmitter();
  @Output() public onCreateManuallyClick: EventEmitter<EventToEmitInterface> = new EventEmitter();


  public modelEffectiveDate: string = new Date().toISOString();
  public plansOnQuotes: FilterOption[] = [];
  public plansCount = 0;
  public selectedPlan: any; // QuotePlan
  public invalidPlanQuoteField = false;
  private resetPlansSubscribtion;
  private agencyInfo;
  private createdDriver: ComDriver;
  private createdClientDetails: ClientDetails;
  private createdClientContactMethod: ClientContactMethod;
  private createdClientAddress: ClientAddress;
  private selectedPlansOnQuote = [];

  constructor(
    private router: Router,
    private agencyUserService: AgencyUserService,
    private subsService: SubsService,
    private quotesService: QuotesService,
    private driversService: DriversService,
    private storageService: StorageService,
    private clientsService: ClientsService,
    private overlayLoaderService: OverlayLoaderService,
    private specsService: SpecsService,
    private routeService: RouteService,
    private storageGlobalService: StorageGlobalService,
    private apiCommonService: ApiCommonService,
    private vehicleService: VehiclesService,
    private coveragesService: CoveragesService,
    private leaveQuoteService: LeaveQuoteService,
    private locationsService: LocationsService,
    private comAutoPolicyCoverageService: ComAutoPolicyCoverageService,
    private comCoveredAutoSymbolsService: ComCoveredAutoSymbolsService
  ) { }
  @ViewChild('refPlansSelector', {static: true}) refPlansSelector: PlansSelectorComponent;

  @ViewChild('leaveQuote') leaveQuote: LeaveQuoteComponent;

  canDeactivate(): Observable<boolean> | boolean {
    this.leaveQuote.detectConfirmation();
    return this.leaveQuote.detectConfirmationObservable.asObservable().pipe(first());
  }

  ngOnInit() {

    this.agencyUserService.userData$.subscribe(agent => {
      this.agencyInfo = agent;
    });

    this.processPlans();

  }

  ngOnDestroy() {
    this.resetPlansSubscribtion && this.resetPlansSubscribtion.unsubscribe();
  }

public onDateChange($ev: { date: string | Date | null }): void {
  try {
    if ($ev && $ev.date) {
      // Handle both string and Date objects
      const dateValue = typeof $ev.date === 'string' ? $ev.date : $ev.date.toISOString();
      this.modelEffectiveDate = format(parseISO(dateValue), 'yyyy-MM-dd');
    } else {
      this.modelEffectiveDate = null;
    }
  } catch (error) {
    console.error('Error formatting date:', error);
    this.modelEffectiveDate = null;
  }
}

  public get showWarningInThePast(): boolean {
    return this.modelEffectiveDate && this.dateIsInThePast();
  }

  private dateIsInThePast(): boolean {
    return this.modelEffectiveDate && isBefore(parseISO(this.modelEffectiveDate), startOfDay(new Date()));
  }

  private filterByLob(plans, lob) {
    const filteredPlans = [];

    plans.forEach(plan => {
      if (plan.lob === lob) {
        filteredPlans.push(plan);
      }
    });

    return filteredPlans;
  }
  private processPlans() {
    const plans = JSON.parse(JSON.stringify(this.storageGlobalService.takeSubs('plans')));
    const plansFilteredByLob = this.filterByLob(plans, 'AUTOB');

    if (plansFilteredByLob.length) {
      this.setPlansOnQuotes(plansFilteredByLob);
    } else {
      this.resetPlans();
    }
  }

  private resetPlans() {
    this.overlayLoaderService.showLoader('Loading Plans...');
    this.agencyUserService.userData$.subscribe(agent => {
      if (agent) {
        this.resetPlansSubscribtion = this.subsService.getRatingPlans(agent.agencyId).subscribe(response => {
          this.overlayLoaderService.hideLoader();

          this.setPlansOnQuotes(this.filterByLob(response.items, 'AUTOB'));
          this.refPlansSelector.init();
        });
      }
    });
  }

  private setPlansOnQuotes(plans) {
    this.plansOnQuotes = [];
    plans.forEach(item => {
      this.plansOnQuotes.push({ text: item.name, id: item.ratingPlanId, data: item });
    });
    this.plansOnQuotes.sort((a, b) => {
      if (a.text < b.text) { return -1; } else if (a.text > b.text) { return 1; } else { return 0; }
    });

    this.plansCount = this.plansOnQuotes.length;

    if (this.plansCount) {
      this.selectedPlan = [];
      this.selectedPlansOnQuote = [];
      this.plansOnQuotes.forEach((plan, index) => {
        this.selectedPlan.push(plan);
        this.selectedPlansOnQuote.push(plan.data);
      });
    }
  }

  public refreshValue($event) {
    this.selectedPlansOnQuote = [];
    for (const option of $event.selectedOption) {
      this.selectedPlansOnQuote.push(option.data);
    }

    this.selectedPlan = $event.selectedOption;
    this.invalidPlanQuoteField = false;
  }
  public handleCancelClick($ev): void {
    this.onCancelClick.emit({
      event: $ev
    });
  }

  public handleCreateManuallyClick($ev): void {
    this.leaveQuote.forceConfirmation = true;
    this.leaveQuote.confirm().then(answer => {
      if (answer) {
        this.redirectToCreateQuoteView()
          .then(() => {
            this.createQuoteManually();
          });

        if (this.invalidPlanQuoteField) {
          return;
        }

        this.onCreateManuallyClick.emit({
          event: $ev
        });
      }
    });
  }

  private redirectToCreateQuoteView(): Promise<any> {
    return this.router.navigateByUrl('/dashboard/quotes/new');
  }


  public createQuoteManually() {
    if (!this.selectedPlan) {
      this.invalidPlanQuoteField = true;
      return;
    }

    this.invalidPlanQuoteField = false;

    let letCreateQuote = true;
    if (this.routeService.isImported) {
      letCreateQuote = window.confirm(this.routeService.text);
    }

    if (letCreateQuote) {
      this.quotesService.setIsImportedQuote(false);
      let quoteData;
      if (this.selectedPlansOnQuote.length) {
        quoteData = {
          lob: this.selectedPlansOnQuote[0].lob,
          state: this.selectedPlansOnQuote[0].state
        };
      }

      this.overlayLoaderService.showLoader();
      this.quotesService.createNewQuote({
        lob: quoteData.lob,
        state: quoteData.state,
        effectiveDate: this.modelEffectiveDate,
        expirationDate: format(addYears(parseISO(this.modelEffectiveDate), 1), 'yyyy-MM-dd'),
        policyPeriod: 12
      }).subscribe(
        data => {
          if (data) {

            let srcQuoteSessionId = null;
            this.storageService
              .getStorageData('srcQuoteSessionId')
              .subscribe(sessionId => {
                if (sessionId) {
                  srcQuoteSessionId = sessionId;
                }
              });

            // if new quote created manually
            this.storageService.clearStorage();
            this.storageService.clearQuoteStoredData();

            this.storageService.setStorageData('isNewQuote', true);
            console.log('SET IS NEW QUOTE!!!!');

            let quote = data;
            // Set Quote Info
            quote = this.setQuoteInfo(quote);

            this.storageService.setStorageData('selectedQuote', quote);

            const quotePlanList = {
              items: this.selectedPlansOnQuote
            };
            if (!this.selectedPlansOnQuote.length) {
              quotePlanList.items = [this.selectedPlan.data];
            }

            this.quotesService.updateQuoteByUrl(quote.quotePlanList.href, quotePlanList).pipe(take(1)).subscribe(updatedQuote => {
              // Reset data
              this.createdDriver = null;

              this.createdClientDetails = null;
              this.createdClientContactMethod = null;
              this.createdClientAddress = null;

              // Create commercial client
              this.createDriver(quote.resourceId)
                .then(driver => {
                  this.createdDriver = driver;
                  this.storageService.setStorageData('comDriverList', [driver]); // manually cast to array - only 1 new driver
                  return driver;
                })
                .then(() => {
                  return this.createClient(quote.resourceId);
                })
                .then((clientDetails: ClientDetails) => {
                  return this.setClientProperClientTypeAndUpdate(clientDetails);
                })
                .then(clientDetails => {
                  this.createdClientDetails = clientDetails;
                  return this.createClientData(clientDetails);
                })
                .then(() => this.getQuoteClients(quote.resourceId))
                .then(() => this.createQuoteLocation(quote))
                .then(() => this.getSpecQuoteCoverages())
                .then((data: CoverageItem[]) => this.createCoverages(quote, data))
                .then(() => this.getSpecSymbols())
                .then((data: CoveredAutoSymbols) => this.createSymbols(quote, data))
                .then(() => {
                  // Assign created client ID to Quote
                  quote.client.resourceId = this.createdClientDetails.resourceId;
                  this.storageService.setStorageData('selectedQuote', quote);

                  // this.leaveQuoteService.saveStorageQuoteDataSnapshot();

                  this.router.navigateByUrl('/dashboard/commercial-auto/quotes/' + quote.resourceId + '/locations?overlay=info&type=client'); // + '?overlay=info&type=client');
                  this.leaveQuoteService.saveStorageQuoteDataSnapshot();
                  this.overlayLoaderService.hideLoader();
                })
                .catch(err => {
                  console.log('CREATE QUOTE ERR: ', err);
                  this.overlayLoaderService.hideLoader();
                });
            });
          }
        },
        error => {
          console.log(error);
        });
    }
  }
  private createDriver(quoteId: string): Promise<ComDriver> {
    return new Promise((resolve, reject) => {
      this.driversService.createComDriver(quoteId)
        .pipe(take(1))
        .subscribe(
          (driver: ComDriver) => resolve(driver),
          err => reject(err)
        );
    });
  }
  private createClient(quoteID: string): Promise<ClientDetails> {
    return new Promise((resolve, reject) => {
      this.clientsService.createQuoteClient(quoteID)
        .pipe(take(1))
        .subscribe(
          (clientDetails: ClientDetails) => resolve(clientDetails),
          err => reject(err)
        );
    });
  }

  private setClientProperClientTypeAndUpdate(client: ClientDetails): Promise<ClientDetails> {
    return new Promise((resolve, reject) => {
      client.type = CLIENT_TYPES.commercial;
      this.clientsService.updateClientDetails(client)
        .then((res: ClientDetails) => {
          resolve(client);
        })
        .catch(err => reject(err));
    });
  }

  private createClientData(client: ClientDetails): Promise<any> {
    const promiseAddresses = this.clientsService.getClientAddressesAndCreateNotExisting(client)
      .then((res: ClientAddress[]) => {
        this.storageService.setStorageData('clientAddresses', JSON.parse(JSON.stringify(res)));
      })
      .catch(err => console.log(err));

    const promiseContactMethods = this.clientsService.getClientContactMethodsAndCreateNotExisting(client)
      .then((res: ClientContactMethod[]) => {
        this.storageService.setStorageData('clientContactMethods', JSON.parse(JSON.stringify(res)));
      })
      .catch(err => console.log(err));

    return Promise.all([promiseAddresses, promiseContactMethods]);
  }


  private getQuoteClients(quoteId: string): Promise<any> {
    return this.clientsService.getClientsList(quoteId)
      .toPromise()
      .then(res => {
        if (res.items[0] && !res.items[0].type) {
          res.items[0].type = CLIENT_TYPES.commercial;
        }

        this.storageService.setStorageData('selectedClient', res.items[0]);
        this.storageService.setStorageData('clients', res.items);
      })
      .catch(err => console.log(err => console.log('CLIENTS ERR:', err)));
  }

  private updateClientDetails(clientDetailsData: ClientDetails): Promise<ClientDetails> {
    return this.apiCommonService.putByUri(clientDetailsData.meta.href, clientDetailsData)
      .toPromise()
      .then(res => res)
      .catch(err => console.log(err));
  }

  private createQuoteLocation(quote: Quote): Promise<LocationData[]> {
    return new Promise(resolve => {
      this.locationsService.createLocation(quote.resourceId)
        .subscribe((data: LocationData) => {
          console.log('Created Location for Quote');
          this.storageService.setStorageData('quoteLocations', [data]);
          resolve([data]);
        });
    });
  }

  private setQuoteInfo(quote: Quote): Quote {
    let userData: UserData;

    this.agencyUserService.userData$
      .pipe(first())
      .subscribe(data => userData = data);

    // Quote agency contact
    if (!quote.agent && userData) {
      quote.agent = userData.user.userId;
    }

    return quote;
  }

  // -----

  private getSpecQuoteCoverages(): Promise<CoverageItem[]> {
    return new Promise((resolve, reject) => {
      this.comAutoPolicyCoverageService.GetQuoteCoverageSpecByUri().subscribe (
        specData => {
          const specQuoteCoverages: CoverageItem[] = specData.items;
          this.storageService.setStorageData('commercialAutoSpecCoverages', specQuoteCoverages);
          resolve(specQuoteCoverages);
        },
        err => reject(err)
      );
    });
  }

  private createCoverages(quote: Quote, specCoverages: CoverageItem[]): Promise<CoveragesData> {
    return this.comAutoPolicyCoverageService.createPolicyInfoCoveragesWithDefaults(quote, specCoverages)
      .then((res: CoveragesData) => {
        this.storageService.setStorageData('commercialAutoQuoteCoverages', res);
        return res;
      });
  }

  private getSpecSymbols(): Promise<CoveredAutoSymbols> {
    return new Promise((resolve, reject) => {
      this.comCoveredAutoSymbolsService.GetSymbolsSpecByUri().subscribe (
        (specData) => {
          const specSymbols: CoveredAutoSymbols = new CoveredAutoSymbols();
          specSymbols.symbols = specData.items;
          specSymbols.meta = specData.meta;
          this.storageService.setStorageData('coveredAutoSpecSymbols', specSymbols);
          resolve(specSymbols);
        },
        err => reject(err)
      );
    });
  }

  private createSymbols(quote: Quote, specCoveredAutoSymbols: CoveredAutoSymbols): Promise<CoveredAutoSymbols> {
    return this.comCoveredAutoSymbolsService.createSymbolsWithDefaults(quote, specCoveredAutoSymbols)
      .then((res: CoveredAutoSymbols) => {
        this.storageService.setStorageData('coveredAutoSymbols', res);
        return res;
      });
  }
}
