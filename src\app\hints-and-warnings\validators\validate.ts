import { CoverageItemParsed, CoverageItemParsedForAutoDriverOptionsHintsAndWarnings } from 'app/app-model/coverage';
export class Validate {

  public static isEmptyValue(value: any): boolean {
    if (value === undefined || value == null) {
      return true;
    }

    if (typeof value === 'object') {
      return Object.keys(value).length <= 0;
    }

    value = String(value);
    return value.trim().length <= 0 || !value;
  }

  public static isRadioInputValueNotSet(value): boolean {
    return (value === true || value === false) ? false : true;
  }

  public static isRadioInputValueNotSet2(value): boolean {
    return (value === 'Yes' || value === 'No') ? false : true;
  }

  public static isRequiredForSelectedPlans(arrPlansIdsRequiredFor: string[], selectedPLansIds: string[]): boolean {
    let isInSelectedPlans = false;

    for (const id of selectedPLansIds) {
      if (arrPlansIdsRequiredFor && arrPlansIdsRequiredFor.length && arrPlansIdsRequiredFor.indexOf(id) !== -1) {
        isInSelectedPlans = true;
        break;
      }
    }

    return isInSelectedPlans;
  }

  public static isRequiredForSelectedPlansIfEmptyValue(value: any, arrPlansIdsRequiredFor: string[], selectedPLansIds: string[]): boolean {
    // console.log('%c >>>>', 'color:red', Validate.isRequiredForSelectedPlans(arrPlansIdsRequiredFor, selectedPLansIds),  Validate.isEmptyValue(value));
    return Validate.isRequiredForSelectedPlans(arrPlansIdsRequiredFor, selectedPLansIds) && Validate.isEmptyValue(value);
  }

  public static isRequiredForSelectedFormTypes(formTypewsRequiredFor: string[], selectedFormTypes: string[]): boolean {
    if (!formTypewsRequiredFor || !selectedFormTypes) {
      return false;
    }
    return formTypewsRequiredFor.some(el => selectedFormTypes.indexOf(el) !== -1);
  }

  public static isRequiredForSelectedFormTypesIfEmptyValue(value: any, formTypewsRequiredFor: string[], selectedFormTypes: string[]): boolean {
    return Validate.isRequiredForSelectedFormTypes(formTypewsRequiredFor, selectedFormTypes) && Validate.isEmptyValue(value);
  }

  public static checkCoverageValueByCode(code: string, fullObj) {
    if (fullObj && fullObj.coverageCode === code && !Validate.isEmptyValue(fullObj.value)) {
      return false;
    }
    return true;
  }

  // public static coverageItemParsedErrorIfActiveAndNoSubOptionsSelected(item: CoverageItemParsed | CoverageItemParsedForAutoDriverOptionsHintsAndWarnings): boolean {
  //   return item.inputType !== 'Checkbox' && item.inputType !== 'FCRADisclosure' && !item.currentValue && item.isActive;
  // }

  public static coverageItemParsedErrorIfActiveAndNoSubOptionsSelected(item: CoverageItemParsed | CoverageItemParsedForAutoDriverOptionsHintsAndWarnings): boolean {
    let result = false;

    if (item.inputType !== 'Checkbox' && item.inputType !== 'FCRADisclosure' && item.isActive) {
      // result = true;
      if (item.currentValueData && item.currentValueData.keyValue.length) {

        for (const el in item.currentValueData.keyValue) {
          // If there is value not set for any field (0 - key, 1 - value)
          if (!item.currentValueData.keyValue[el][1]) {
            result = true;
            break;
          }
        }
      }
    }

    return result;
  }

  public static isValidPhoneNumber(number: string): boolean {
    let isPhoneNumberValid = number.length !== 10 && number !== '';

    return !isPhoneNumberValid;
  }

  public static isValidEmail(email: string): boolean {
    const pattern: RegExp = /^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    return pattern.test(email);
  }
}
