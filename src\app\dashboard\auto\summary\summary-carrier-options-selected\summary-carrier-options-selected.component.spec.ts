import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import {
    SummaryCarrierOptionsSelectedComponent
} from './summary-carrier-options-selected.component';

describe('SummaryCarrierOptionsSelectedComponent', () => {
  let component: SummaryCarrierOptionsSelectedComponent;
  let fixture: ComponentFixture<SummaryCarrierOptionsSelectedComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ SummaryCarrierOptionsSelectedComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SummaryCarrierOptionsSelectedComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
