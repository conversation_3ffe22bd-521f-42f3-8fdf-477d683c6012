<section class="section">
  <div class="u-flex u-flex--spread">
    <div class="">
      <h1 class="o-heading">Client Quotes</h1>
    </div>
    <div class="">
    </div>
  </div>

  <div class="row u-spacing--1-5">
    <div class="col-xs-12">
      <app-loader [loading]="loadingQuotesList" [cssClass]="'loader--with-opacity'" [loadingText]="'Loading, please wait...'"></app-loader>

      <table class="table table--compact table--fixed">
        <thead class="table__thead">
          <tr class="">
            <th class="table__th u-width-50px">LOB</th>
            <th class="table__th u-width-70px">State</th>
            <th class="table__th u-width-110px">Effective</th>
            <th class="table__th u-width-110px">Expiration</th>
            <th class="table__th u-width-110px">Saved</th>
            <th class="table__th">Notes</th>
          </tr>
        </thead>
        <tbody class="table__tbody">
          <tr class="table__tr" *ngFor="let row of arrQuotesAll">
            <td class="table__td  u-color-pelorous">
              <a routerLink="/dashboard/dwelling/quotes/{{row.resourceId}}" target="_blank">
                <i class="o-icon o-icon--md o-icon--i-{{row.lob}} table__td-icon"></i>
              </a>
            </td>
            <td class="table__td">{{row.state}}</td>
            <td class="table__td">{{formatInitialDate(row.effectiveDate)}}</td>
            <td class="table__td">{{formatInitialDate(row.expirationDate)}}</td>
            <td class="table__td">{{formatInitialDate(row.lastModifiedDate)}}</td>
            <td class="table__td">
              <div class="u-t-nowrap">{{row.description}}</div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</section>

<section class="section">
  <div class="u-flex u-flex--spread u-flex--to-middle">
    <div class="">
      <app-pagination [totalRecords]="paginationQResultsCount" [recordsLimit]="paginationQResultLimit" (onPageChange)="paginationQuotePageChange($event)"></app-pagination>
    </div>
    <div class="">
      <app-results-limiter (onChange)="onQResultLimitChange($event)"></app-results-limiter>
    </div>
  </div>
</section>

<section class="section">
  <div class="u-flex u-flex--spread">
    <div class="">
      <h1 class="o-heading">Client Forms</h1>

    </div>
    <div class="">
    </div>
  </div>

  <div class="row u-spacing--1-5">
    <div class="col-xs-12">
      <app-loader [loading]="loadingFormsList" [cssClass]="'loader--with-opacity'" [loadingText]="'Loading, please wait...'"></app-loader>

      <table class="table table--compact table--fixed">
        <thead class="table__thead">
          <tr class="">
            <th class="table__th u-width-120px">Form Type</th>
            <th class="table__th u-width-80px">Saved</th>
            <th class="table__th u-width-60px">Agent</th>
            <th class="table__th u-width-110px">Notes</th>
          </tr>
        </thead>
        <tbody class="table__tbody">
          <tr class="table__tr" *ngFor="let row of arrFormsAll">
            <td class="table__td u-color-pelorous">
              <a (click)="openAgencyForm(row)" title="{{row.formName}}">
                <span *ngIf="!row.formName">No form type</span><span *ngIf="row.formName">{{row.formName}}</span>
              </a></td>
            <td class="table__td">{{formatInitialDate(row.lastModifiedDate)}}</td>
            <td class="table__td">{{row.lastModifyingAgentName}}</td>
            <td class="table__td">{{row.notes}}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</section>

<section class="section">
  <div class="u-flex u-flex--spread u-flex--to-middle">
    <div class="">
      <app-pagination [currentPage]="paginationFCurrentPage" [totalRecords]="formsCount" [recordsLimit]="paginationFResultLimit" (onPageChange)="paginationFormPageChange($event)"></app-pagination>
    </div>
    <div class="">
      <app-results-limiter (onChange)="onFResultLimitChange($event)" [description]="'forms to show'"></app-results-limiter>
    </div>
  </div>
</section>

<app-leave-quote #leaveQuote [messageFor]="'client'"></app-leave-quote>
