
import {timer as observableTimer,  BehaviorSubject ,  SubscriptionLike as ISubscription ,  Observable } from 'rxjs';

import {switchMap, first, take} from 'rxjs/operators';
import { STATES } from './../../../app-services/state.service';
import { ActivatedRoute, Router } from '@angular/router';
import { Component, OnDestroy, OnInit, ViewChild, ElementRef } from '@angular/core';
import { Quote, QuotePlan } from 'app/app-model/quote';
import {
  Vehicle,
  VehicleGeneralDetails,
  VehicleOptions,
  VehiclePlanSymbols,
  VehicleRMVLookupData,
  VehicleGeneralDetailsHelper,
  VehicleOptionsForVehicle
} from 'app/app-model/vehicle';

import { AgencyUserService } from 'app/shared/services/agency-user.service';
import { Driver } from 'app/app-model/driver';
import { DriversService } from 'app/dashboard/app-services/drivers.service';
import { FilterOption } from 'app/app-model/filter-option';
import { LeaveQuoteComponent } from 'app/shared/components/leave-quote/leave-quote.component';
import { LocationData } from 'app/app-model/location';
import { LocationsService } from 'app/dashboard/app-services/locations.service';
import { LookupsService } from 'app/dashboard/app-services/lookups.service';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { OverlayRouteService } from 'app/overlay/services/overlay-route.service';
import { REQUIRED_PLANS_SYMBOLS_VRG } from 'app/dashboard/app-services/plans.service';
import { RouteService } from 'app/shared/services/route.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { SubsService } from 'app/dashboard/app-services/subs.service';
import { SymbolsService, SymbolsUpdateInProgressI } from 'app/dashboard/app-services/symbols.service';
import { Town } from 'app/app-model/specs';
import { Validate } from 'app/hints-and-warnings/validators';
import { VehicleSymbolComponent } from 'app/dashboard/auto/vehicles/vehicle-symbol/vehicle-symbol.component';
import { VehiclesService, VEHICLE_OPTIONS_MAKE_TRAILER, VEHICLE_OPTIONS_MAKE_MOTORCYCLE, VEHICLE_TYPES } from 'app/dashboard/app-services/vehicles.service';
import { CoveragesService } from 'app/dashboard/app-services/coverages.service';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { ClientDetails, ClientAddress } from 'app/app-model/client';
import { ClientsService } from 'app/dashboard/app-services/clients.service';
import { PremiumsService } from 'app/dashboard/auto/premiums/premiums.service';
import { NgModel } from '@angular/forms';
import { BroadcastService } from 'app/shared/services/broadcast.service';
import { IModalStateChange, ModalboxComponent } from 'app/shared/components/modalbox/modalbox.component';
import { VehiclesSymbolsAutomanagerService } from '../vehicles-symbols-automanager/vehicles-symbols-automanager.service';
import { MetaData } from 'app/app-model/_common';
import { QuotesService } from '../../../app-services/quotes.service';
import { MenuItem } from 'primeng/api';

interface filterOptionAdditionalDataInterface {
  uqId: string;
  generalDetail: VehicleGeneralDetails;
}

// interface VehicleTypesI {
//   privatePassenger: string;
//   pickup: string;
//   motorcycle: string;
//   trailer: string;
// }

class FilterOptionTrim {
  constructor(
    public id: string = '',
    public text: string = '',
    public data: filterOptionAdditionalDataInterface = {
      generalDetail: new VehicleGeneralDetails(),
      uqId: ''
    }
  ) {}
}

const CITY_OPTION_OUT_OF_STATE = 'Out of State';

// const VEHICLE_TYPES: VehicleTypesI = {
//   privatePassenger: 'Private Passenger',
//   pickup: 'Pickup',
//   motorcycle: 'Motorcycle',
//   trailer: 'Trailer'
// }

@Component({
    selector: 'app-vehicle-details',
    templateUrl: './vehicle-details.component.html',
    styleUrls: ['./vehicle-details.component.scss'],
    standalone: false
})
export class VehicleDetailsComponent implements OnInit, OnDestroy {
  addresses;

  public get plateTypeOptionsSorted(): FilterOption[] {
    return this.plateTypeOptions.sort((a: FilterOption, b: FilterOption) => {
      return a.id < b.id ? -1 : a.id > b.id ? 1 : 0;
    });
  }

  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private vehicleService: VehiclesService,
    private storageService: StorageService,
    private locationsService: LocationsService,
    private lookupsService: LookupsService,
    private driverService: DriversService,
    private subsService: SubsService,
    private agencyUserService: AgencyUserService,
    private specsService: SpecsService,
    private overlayLoaderService: OverlayLoaderService,
    private overlayRouteService: OverlayRouteService,
    private symbolsService: SymbolsService,
    private routeService: RouteService,
    private coveragesService: CoveragesService,
    private storageGlobalService: StorageGlobalService,
    private apiCommonService: ApiCommonService,
    private clientsService: ClientsService,
    private premiumsService: PremiumsService,
    private broadcastService: BroadcastService,
    private vehiclesSymbolsAutomanagerService: VehiclesSymbolsAutomanagerService,
    private quoteService: QuotesService
  ) { }

  get rmvShowPlateForm(): boolean {
    return this.rmvLookupFormSelected === 'plate';
  }

  get rmvShowVinForm(): boolean {
    return this.rmvLookupFormSelected === 'vin';
  }

  public get trimFieldIsInTextMode(): boolean {
    return this.fieldTrimSwitchToText || this.helperVehicleIsFromYearOrPrior(1984);
  }

  private quoteId = '';
  private vehicleId = '';

  public showVehicleDetailsForm = false;

  private subscriptionInitVehicleGeneralDetail;
  private subscriptionVehicleMakes;
  private subscriptionVehicleModels;
  private subscriptionVehicleModelDetail;
  private subscriptionVehicleRMVVinLookup;
  private subscriptionVehicleRMVPlateLookup;
  private subscriptionVehicleVinLookup;
  private subscriptionVehiclesPlanSymbols;
  private subscriptionQuoteLocations: ISubscription;
  private subscriptionSymbolsUpdatingState: ISubscription;
  private subscriptionNewRmvQuote: ISubscription;

  private allowVehicleUpdateOnRouteChange = false;
  private allowUpdateVehicleSymbolsOnInit = false;
  private allowSymbolsUpdateOnEffectiveDateChange = false;

  private userData: any;

  private timerCheckPriceValueRequire;
  private timerActionPriceFieldBlurSymbolsUpdate;

  private subscription;
  private userDataSubscription;
  private routeSubscription;
  // private storeSubscription;
  private quoteSubscription;
  private quotePlansSubscription;
  // public modalIsOpen:boolean = false;

  private selectedQuote: Quote;
  private selectedQuotePlansList: QuotePlan[] = [];
  private quoteSelectedPlansIds: string[] = [];
  private selectedQuotePreviousEffectiveDate: string;
  private quoteLocations: LocationData[] = [];

  public vehicles: Vehicle[] = [];
  public selectedVehicle: Vehicle = new Vehicle();
  public selectedVehicleLocationData = new LocationData();
  public selectedVehicleDetails: VehicleGeneralDetails = new VehicleGeneralDetails();
  public selectedVehiclePlanSymbols: VehiclePlanSymbols[] = [];

  // ------
  public selectedVehicleGeneralDetailsForOptionsUpdate: VehicleGeneralDetails = new VehicleGeneralDetails();
  public selectedVehiclePlanSymbolsForSymbolsComponent: VehiclePlanSymbols[] = [];
  // ------

  public drivers: Driver[] = [];
  public driversInitialStateDoNotEdit: Driver[] = []; // Required for Motorcycle management;

  public optionsVehicleTypes: FilterOption[] = []; // string[] = [];
  public optionsUsage: FilterOption[] = [];
  public optionsYear: string[] = [];
  public optionsMake: FilterOption[] = [];
  public optionsModel: FilterOption[] = [];
  public optionsTrim: FilterOptionTrim[] = []; // FilterOption[] = [];
  public optionsCity: FilterOption[] = [];
  public optionsState: string[] | FilterOption[] = [];
  public optionsOwner: FilterOption[] = [];
  public optionsOperator: FilterOption[] = [];
  public optionsBodyStyles: FilterOption[] = [];

  public fieldMakeDisabled = true;
  public fieldModelDisabled = true;
  public fieldTrimDisabled = true;
  public fieldLocationStateDisabled = true;
  public fieldBodyStyleDisabled = false;

  public fieldTrimSwitchToText = false;
  public fieldVinRequired = true;
  public fieldPriceNewValueRequired = false;
  public fieldAnnualMilesIsRequired = true;

  public fieldModelSwitchToText = false;
  public fieldModelIsInvalid = false;
  public fieldTrimIsInvalid = false;


  // // - SPR-2090  TEST
  // public fieldTrimIsHidden: boolean = false;
  // public fieldBodyStyleIsHidden: boolean = false;
  // public fieldSymbolsIsHidden: boolean = false;
  // public componentVehicleOptionsIsHidden: boolean = false;
  // // - SPR-2090  TEST

  public fieldBodyStyleRequired = false;
  // public fieldBodyStyleHighlight:boolean = false;
  // private fieldPriceNewValueHasChanged: boolean = false;
  public symbolsUpdateInProgress = false;

  public fieldVinInvalid = false;
  public plateTypeOptions: FilterOption[] = [
    {id: 'AHN' , text: 'AHN - Auto Home Normal'},
    {id: 'AHR' , text: 'AHR - Auto Home Reserved'},
    {id: 'AMN' , text: 'AMN - Ambulance Normal'},
    {id: 'AMN' , text: 'AMN - Ambulance (Animal)'},
    {id: 'AMR' , text: 'AMR - Ambulance Reserved'},
    {id: 'BUN' , text: 'BUN - Bus Normal'},
    {id: 'BUR' , text: 'BUR - Bus Reserved'},
    {id: 'BUV' , text: 'BUV - Bus Vanity'},
    {id: 'CON' , text: 'CON - Commercial Normal'},
    {id: 'COR' , text: 'COR - Commercial Reserved'},
    {id: 'COV' , text: 'COV - Commercial Vanity'},
    {id: 'LVN' , text: 'LVN - Livery Normal'},
    {id: 'LVR' , text: 'LVR - Livery Reserved'},
    {id: 'LVV' , text: 'LVV - Livery Vanity'},
    {id: 'MCN' , text: 'MCN - Motorcycle Normal'},
    {id: 'MCR' , text: 'MCR - Motorcycle Reserved'},
    {id: 'MCV' , text: 'MCV - Motorcycle Vanity'},
    {id: 'MVN' , text: 'MVN - Municipal Normal'},
    {id: 'PAN' , text: 'PAN - Passenger Normal'},
    {id: 'PAR' , text: 'PAR - Passenger Reserved'},
    {id: 'PAV' , text: 'PAV - Passenger Vanity'},
    {id: 'PAS' , text: 'PAS - Passenger Special'},
    {id: 'PAY' , text: 'PAY - Year of Manufacture'},
    {id: 'SBN' , text: 'SBN - School Bus Normal'},
    {id: 'SBR' , text: 'SBR - School Bus Reserved'},
    {id: 'SPN' , text: 'SPN - School Pupil'},
    {id: 'SMN' , text: 'SMN - Semi Trailer Normal'},
    {id: 'SMR' , text: 'SMR - Semi Trailer Reserved'},
    {id: 'TRN' , text: 'TRN - Trailer Normal'},
    {id: 'TRR' , text: 'TRR - Trailer Reserved'},
    {id: 'TAN' , text: 'TAN - Taxi Normal'},
    {id: 'TAR' , text: 'TAR - Taxi Reserved'},
    {id: 'VPN' , text: 'VPN - Van Pool Normal'},
    {id: 'DLN' , text: 'DLN - Dealer Normal'},
    {id: 'DLV' , text: 'DLV - Dealer Vanity'},
    {id: 'RPN' , text: 'RPN - Repair Normal'},
    {id: 'RPV' , text: 'RPV - Repair Vanity'},
    {id: 'FAN' , text: 'FAN - Farm Normal'},
    {id: 'OCN' , text: 'OCN - Owner/Contractor'},
    {id: 'BDN' , text: 'BDN - Boat Dealer Normal'},
    {id: 'DMN' , text: 'DMN - Dealer Motorcycle'},
    {id: 'TPN' , text: 'TPN - Transport Normal'},
    {id: 'AHV' , text: 'AHV - Camper Vanity Plate'},
    {id: 'APN' , text: 'APN - Apportioned Normal Plates'},
    {id: 'APR' , text: 'APR - Apportioned Reserved Plates'},
    {id: 'APV' , text: 'APV - Apportioned Vanity Plates'},
    {id: 'ATN' , text: 'ATN - Transit Police Normal Plate'},
    {id: 'AXN' , text: 'AXN - Transit Police Motorcycle Plate'},
    {id: 'LNV' , text: 'LNV - Livery Limited Use Plate'},
    {id: 'MCS' , text: 'MCS - Motorcycle Special Plate'},
    {id: 'MXN' , text: 'MXN - Motorcycle Municipal Plate'},
    {id: 'STN' , text: 'STN - State Vehicle Plate'},
    {id: 'SXN' , text: 'SXN - State Motorcycle Plate'},
  ];

  /*
{id: 'AHV', text: 'AHV - Camper Vanity Plate'},
{id: 'APN', text: 'APN - Apportioned Normal Plates'},
{id: 'APR', text: 'APR - Apportioned Reserved Plates'},
{id: 'APV', text: 'APV - Apportioned Vanity Plates'},
{id: 'ATN', text: 'ATN - Transit Police Normal Plate'},
{id: 'AXN', text: 'AXN - Transit Police Motorcycle Plate'},
{id: 'LNV', text: 'LNV - Livery Limited Use Plate'},
{id: 'MCS', text: 'MCS - Motorcycle Special Plate'},
{id: 'MXN', text: 'MXN - Motorcycle Municipal Plate'},
{id: 'STN', text: 'STN - State Vehicle Plate'},
{id: 'SXN', text: 'SXN - State Motorcycle Plate'},

  */

  public loadingVehicleRelatedData = false;
  public vehicleModelIsInManualMode = false;
  public vinNumberLookupGeneralDetailsNotFound = false;

  // @ViewChild('refVehicleSymbols') public symbolsComponent:VehicleSymbolsComponent;
  @ViewChild(VehicleSymbolComponent) public symbolsComponent: VehicleSymbolComponent;
  @ViewChild('refModalEditManuallyModel') public refModalEditManuallyModel: ModalboxComponent;
  @ViewChild('refVehicleModelTextInput') public refVehicleModelTextInput: ElementRef;
  @ViewChild('refModalExcludedVehicle') public refModalExcludedVehicle: ModalboxComponent;

  @ViewChild('leaveQuote') leaveQuote: LeaveQuoteComponent;

  // TEST - SPR-2543
  private subscriptionRMVReportsBroadcast: ISubscription;


  private _manageLocationDataUpdatingFinished: BehaviorSubject<boolean> = new BehaviorSubject(true);


  public loadingGetVehicleDetailByVINAndUpdateVehicleData = false;
  private lookupGetVehicleDetailByVINAndUpdateVehicleDataTimer: number;


  // Owner/Operator fields
  // public actionOnOwnerOperatorFieldsSelect($ev, propertyName):void {
  //   const data = {
  //     meta: {
  //       href: $ev.selectedOption.data.meta.href
  //     }
  //   };

  //   // TODO:: Show Information that the driver can not be set as operator if has excluded vehicle

  //   this.selectedVehicle[propertyName] = data;
  //   this.updateVehicleData();

  //   // If motorcycle, force on driver to set 'Motorcycle Field to yes'
  //   if (propertyName === 'operator') {
  //     this.setForOperatorDriverMotorcycleValue();
  //   }
  // }

  public excludedForDriverName: string;


  // Ignore Carrier City And Miles
  // ---------------------------------------------------------

  public checkbocCarrierModalIsOpen = false;

  private updateVehicleSubscription: ISubscription;


  // RMV Lookup
  // -------------------------------------------------------------------------------
  public rmvLookupFormSelected = 'plate'; // ['plate', 'vin'];
  public rmvLookupVinNumber = '';
  public rmvLookupPlateNumber = '';
  public rmvLookupPlateType = 'PAN';
  public rmvLookupInvalidVIN = false;
  public rmvLookupInvalidPlate = false;

  // Copy First vehicle Address to Client Info
  // ----------------------------------------------------------------------------
  private subscriptionClients: ISubscription;
  private subscriptionClientAddresses: ISubscription;
  private isQuoteCreatedByRmv = false; // indicate if the quote has been created via RMV (only for newly created quote).
  private clientDetails: ClientDetails;
  private clientAddress: ClientAddress; // StreetAddress
  private isAllowedToUpdateClientAddress = false;


  // Switch between form types for Vehicle Type
  // ----------------------------------------------------------------------------
  // public fieldMakeSwitchToText: boolean = false;

  public fieldTrimIsHidden = false;
  public fieldBodyStyleIsHidden = false;
  public fieldSymbolsIsHidden = false;
  public fieldCcDisplacementIsHidden = true;
  public componentVehicleOptionsIsHidden = false;
  canDeactivate(): Observable<boolean>| Promise<boolean> | boolean {
    let subscription: ISubscription;

    this.leaveQuote.detectConfirmation();
    const promiseLeaveQuote = this.leaveQuote.detectConfirmationObservable.asObservable().pipe(take(1)).toPromise();

    // Updated Garagging Address if changed
    const promiseUpdateLocation: Promise<boolean> = new Promise(resolve => {
      subscription = this._manageLocationDataUpdatingFinished.asObservable()
        .subscribe(res => {
          if (res === false) {
            this.overlayLoaderService.showLoader('Updating Garaging Address');
          }

          // Resolve promise when the garaging address updating is not in progress anymore
          if (res === true) {
            resolve(true);
            this.overlayLoaderService.hideLoader();
          }
        });
    });

    const promisesToResolve: Array<Promise<boolean>> = [
      promiseUpdateLocation,
      promiseLeaveQuote
    ];

    return Promise.all(promisesToResolve).then(arrRes => {
      const completeLength = arrRes.length;
      const lenghtOfTrueRes = arrRes.filter(el => el === true).length;

      subscription && subscription.unsubscribe();

      return (completeLength === lenghtOfTrueRes) ? true : false;
    });

    // this.leaveQuote.detectConfirmation();
    // return this.leaveQuote.detectConfirmationObservable.asObservable().first();
  }

  ngOnInit() {
    this.initialReset();

    // Handle Routing
    this.handleRouteChange();
    this.subscribeSymbolsUpdatingState();

    // 1 GET USER INFO
    this.subscribeUserData();
    // 2 GET QUOTE INFO
    this.subscribeSelectedQuote();
    this.subscribeSelectedQuotePlans();
    this.subscribeQuoteLocations();
    // 3 GET DRIVERS LIST
    // this.subscribeDrivers();
    this.initDriversList();
    // 4 GET VEHICLES LIST
    this.initVehiclesList()
      .then(() => {
        this.allowVehicleUpdateOnRouteChange = true;
        this.initDataToDisplay();
      });

    this.initStaticOptions();
    this.subscribeVehicles();
    this.addresses = this.quoteService.getQuoteAndClientAddresses(this.quoteId);


    this.subscribeRMVReportsBroadcast(); // TEST - SPR-2543

    // this.initClientAddressUpdating();
  }

  ngOnDestroy() {
    this.subscription && this.subscription.unsubscribe();
    this.routeSubscription && this.routeSubscription.unsubscribe();
    // this.storeSubscription && this.storeSubscription.unsubscribe();
    this.userDataSubscription && this.userDataSubscription.unsubscribe();
    this.quoteSubscription && this.quoteSubscription.unsubscribe();
    this.quotePlansSubscription && this.quotePlansSubscription.unsubscribe();
    this.subscriptionQuoteLocations && this.subscriptionQuoteLocations.unsubscribe();
    this.subscriptionSymbolsUpdatingState && this.subscriptionSymbolsUpdatingState.unsubscribe();
    // this.subscriptionDrivers && this.subscriptionDrivers.unsubscribe();
    this.subscriptionRMVReportsBroadcast && this.subscriptionRMVReportsBroadcast.unsubscribe();

    this.destroyClientAddressUpdating();

    // ---
    this.closeApiCalls();
  }

  // Subscriptions
  // -------------------------------------------------------------------------------
  private subscribeSelectedQuote(): void {
    this.quoteSubscription = this.storageService.getStorageData('selectedQuote').subscribe(
      res => {
        //  console.log('QUOTE', res);
        this.selectedQuote = res;
        this.quoteId = res.resourceId;

        // Update previous Effective Date
        if (this.selectedQuotePreviousEffectiveDate !== this.selectedQuote.effectiveDate) {
          this.selectedQuotePreviousEffectiveDate = this.selectedQuote.effectiveDate;
        }
      }
    );
  }

  setEvent(addressList: MenuItem[]) {
   addressList.forEach(i => {
     i.command =  (e) => this.setAddress(i.state);
   });

  }

  setAddress(address) {
    console.log('setaddress',address);
    const {address1, city, state, zip, addressType, fireDistrict, address2} = address;
    this.selectedVehicleLocationData.address1 = address1;
    this.selectedVehicleLocationData.address2 =  address2;
    this.selectedVehicleLocationData.city =  this.titleCase(city);
    this.selectedVehicleLocationData.state =  state;
    this.selectedVehicleLocationData.zip =  zip;

    this.selectedVehicleLocationData.addressType = addressType;
    this.manageLocationsOnDataChangeTrigger();
  }

   titleCase(str) {
    return str.toLowerCase().split(' ').map(function(word) {
      return (word.charAt(0).toUpperCase() + word.slice(1));
    }).join(' ');
  }

  private subscribeSelectedQuotePlans(): void {
    this.quotePlansSubscription = this.storageService.getStorageData('selectedPlan').subscribe(plans => {
      if (plans && plans.items && plans.items.length) {
        this.selectedQuotePlansList = plans.items[0].items;
        this.quoteSelectedPlansIds = this.selectedQuotePlansList.map(plan => plan.ratingPlanId);
        // console.log('SELECTED PLANS:', this.selectedQuotePlansList);

        // Check if Body Style Field Is required
        this.fieldBodyStyleRequired = this.checkIfBodyStyleFieldRequiredBasedOnSelectedPlans(this.selectedQuotePlansList);
        // this.fieldBodyStyleHighlight = this.checkIfBodyStyleFieldRequiredBasedOnSelectedPlans(this.selectedQuotePlansList);
      }
    });
  }


  private subscribeQuoteLocations(): void {
    this.subscriptionQuoteLocations = this.storageService.getStorageData('quoteLocations').subscribe((locations: LocationData[]) => {
      this.quoteLocations = locations;
    });
  }

  private subscribeVehicles(forceInitDataToDisplay: boolean = false): void {
    this.subscription = this.storageService.getStorageData('vehiclesList').subscribe((data: Vehicle[]) => {
      this.vehicles = data;
      // console.log('SUBSCRIBE VEHICLES:', this.vehicles);
      // this.initDataToDisplay(true);

      // Reinit after RMV VIN or PLATE search
      // It can not be reinitialized after every storage update
      if (this.selectedVehicle.resourceId && this.selectedVehicle.resourceId === this.vehicleId && !forceInitDataToDisplay) {
        const updatedVehicle = this.vehicles.find( veh => this.vehicleId === veh.resourceId);

        // Do not initDataToDisplay on vehicle symbols changed - symbols are updated by vehicle-symbols-automanager
        // and can be changed after year change
        let updatedVehicleToCompare: Vehicle;
        if (updatedVehicle) {
          updatedVehicleToCompare = JSON.parse(JSON.stringify(updatedVehicle));
          updatedVehicleToCompare.symbols = [];
        } else {
          updatedVehicleToCompare = updatedVehicle;
        }

        let selectedVehicleToCompare: Vehicle;
        if (this.selectedVehicle) {
          selectedVehicleToCompare = JSON.parse(JSON.stringify(this.selectedVehicle));
          selectedVehicleToCompare.symbols = [];
        } else {
          selectedVehicleToCompare = this.selectedVehicle;
        }

        if (updatedVehicle && (JSON.stringify(selectedVehicleToCompare) !== JSON.stringify(updatedVehicleToCompare))) {
          console.log('initDataToDisplay 1', this.vehicles);
          this.initDataToDisplay(true);
        }

        // if (updatedVehicle && (JSON.stringify(this.selectedVehicle) !== JSON.stringify(updatedVehicle))) {
        //   this.initDataToDisplay(true);
        // }

      } else {
        console.log('initDataToDisplay 2');
        this.initDataToDisplay(true);
      }
    });
  }
  private subscribeRMVReportsBroadcast() {
    this.subscriptionRMVReportsBroadcast = this.broadcastService.on('overlay.rmv.reports.applychanges')
      .subscribe(() => {
        console.log('Apply Changes RMV - Reinitialize vehicles subscription');
        this.subscription && this.subscription.unsubscribe();
        this.subscribeVehicles(true);
      });
  }
  // TEST - SPR-2543 -- END

  private subscribeUserData(): void {
    this.userDataSubscription = this.agencyUserService.userData$
      .subscribe(data => {
        this.userData = data;
    });
  }

  private subscribeSymbolsUpdatingState(): void {
    this.subscriptionSymbolsUpdatingState = this.symbolsService.getSymbolsUpdatingInProgress$
      .subscribe((data: SymbolsUpdateInProgressI) => {
        this.symbolsUpdateInProgress = (data && this.selectedVehicle.resourceId in data) ? data[this.selectedVehicle.resourceId] : false;
      });
  }

  // private subscriptionDrivers: ISubscription;
  // private subscribeDrivers(): void {
  //   this.storageService.getStorageData('driversList')
  //     .subscribe((drivers: Driver[]) => {
  //       this.drivers = drivers;
  //     })
  // }

  private handleRouteChange(): void {
    this.routeSubscription = this.activatedRoute.params.subscribe(params => {
      this.vehicleId = params['vehicleId'];

      if (this.allowVehicleUpdateOnRouteChange) {
        this.initDataToDisplay();
      }
    });
  }

  private closeApiCalls(): void {
    this.subscriptionInitVehicleGeneralDetail && this.subscriptionInitVehicleGeneralDetail.unsubscribe();
    this.subscriptionVehicleMakes && this.subscriptionVehicleMakes.unsubscribe();
    this.subscriptionVehicleModels && this.subscriptionVehicleModels.unsubscribe();
    this.subscriptionVehicleModelDetail && this.subscriptionVehicleModelDetail.unsubscribe();

    if (this.subscriptionVehicleVinLookup) {
      this.symbolsService.setSymbolsUpdatingInProgress(this.selectedVehicle, false);
      this.subscriptionVehicleVinLookup.unsubscribe();
    }
    // this.subscriptionVehicleVinLookup && this.subscriptionVehicleVinLookup.unsubscribe();

    if (this.subscriptionVehiclesPlanSymbols) {
      this.symbolsService.setSymbolsUpdatingInProgress(this.selectedVehicle, false);
      this.subscriptionVehiclesPlanSymbols.unsubscribe();
    }

    // Calls with overlay loader
    if (this.subscriptionVehicleRMVVinLookup) {
      this.subscriptionVehicleRMVVinLookup.unsubscribe();
      this.overlayLoaderService.hideLoader();
    }
    if (this.subscriptionVehicleRMVPlateLookup) {
      this.subscriptionVehicleRMVPlateLookup.unsubscribe();
      this.overlayLoaderService.hideLoader();
    }
  }

  private initialReset(): void {
    this.vehicles = [];
  }

  private initVehiclesList(): Promise<any> {
    // Reset hehicle list and Get from API
    // return this.vehicleService.getVehiclesList(this.quoteId)
    //   .toPromise()
    //   .then(res => {
    //     // console.log(res.items)
    //     this.vehicles = res.items;
    //     this.storageService.setStorageData('vehiclesList', this.vehicles);
    //     return(this.vehicles);
    //   })
    //   .catch(err => console.log(err))

    return new Promise((resolve, reject) => {
      this.storageService.getStorageData('vehiclesList').pipe(
        take(1))
        .subscribe(res => {
          console.log('INIT VEHICLES:', res);
          this.vehicles = res;
          resolve(this.vehicles);
        },
        err => reject(err)
      );
    });
  }



  private initDriversList(): Promise<Driver[]> {
    return new Promise((resolve, reject) => {
      this.storageService.getStorageData('driversList').pipe(
      take(1))
      .subscribe((drivers: Driver[]) => {
        this.drivers = drivers;
        this.driversInitialStateDoNotEdit = JSON.parse(JSON.stringify(drivers));
        this.setOptionsVehicleOwner();
        this.setOptionsVehicleOperator();
        resolve(this.drivers);
      });

      // this.driverService.getDriversList(this.quoteId)
      // .take(1)
      // .subscribe(
      //   res => {
      //     console.log('Drivers from API', res.items);
      //     this.drivers = res.items;
      //     this.setOptionsVehicleOwner();
      //     this.setOptionsVehicleOperator();
      //     resolve(this.drivers)
      //   },
      //   err => reject(err)
      // );
    });
  }

  // Initi Data to initDataToDisplay
  // -------------------------------------------------------------------------------
  private initDataToDisplay(preventAddNewVehicle: boolean = false): void {
    this.selectedVehicleDetails = new VehicleGeneralDetails();                          // Reset selected vehicle details;
    this.selectedVehicleGeneralDetailsForOptionsUpdate = new VehicleGeneralDetails();   // Reset selected vehicle details for options component;
    this.selectedVehicleLocationData = new LocationData();                              // Reset selected vehicle location;
    this.fieldBodyStyleDisabled = false;
    this.allowUpdateVehicleSymbolsOnInit = false;
    this.allowSymbolsUpdateOnEffectiveDateChange = false;
    this.vinNumberLookupGeneralDetailsNotFound = false;

    this.helperSwitchFieldModelTypeAndRequiredFieldsRulesToTextInput(false);

    this.closeApiCalls();

    if (this.vehicleId) {
      const vehicle = this.vehicles.find(veh => this.vehicleId === veh.resourceId);

      console.log('Selected VEHICLE:', vehicle);

      if (vehicle) {
        // this.loadingVehicleRelatedData = (preventAddNewVehicle) ? false : true;addressList
        this.loadingVehicleRelatedData = true;
        this.selectedVehicle = vehicle;
        this.switchFormForVehicleType(this.selectedVehicle.vehicleType);
        this.showVehicleDetailsForm = true;
        this.initSelectedVehicleOptionsProperty();
        this.initSelectedVehicleLocationData();
        this.setDefaultSelectedOptionsForVehicle();
        this.ignoreCarrierModalAccept;
        if (+vehicle.annualMiles < 0) {
          vehicle.annualMiles = '';
        }

        this.updateOptionsVehicle()
          .then(() => {
            // https://bostonsoftware.atlassian.net/browse/SPR-2929
            this.checkIfVehicleModelIsInManualMode();
            return this.initSelectedVehicleGeneralDetails();
          })
          .then(() => {
            this.allowUpdateVehicleSymbolsOnInit = true;
            this.allowSymbolsUpdateOnEffectiveDateChange = true;

            // Check symbols Data if there are no symbols for selected vehicle (Quote added by RMV Lookup)
            if (this.selectedVehicle.symbols && this.selectedVehicle.symbols.length < 1) {
              this.symbolsRetrievePlanSymbols(this.selectedVehicle, this.selectedVehicleDetails);
            } else
            // Update Vehicle Symbols After RMV Lookup
            if (this.selectedVehicle.resourceName === 'Vehicle RMV') {
              this.symbolsRetrievePlanSymbols(this.selectedVehicle, this.selectedVehicleDetails);
              this.updateVehicleData();
            }
            this.loadingVehicleRelatedData = false;
          })
          .catch(
            err => {
              console.log('There is no valid VIN for the Vehicle - can\'t get GeneralDetails ');
              this.loadingVehicleRelatedData = false;
            });


        this.fieldBodyStyleDisabled = (this.selectedVehicle.bodyStyle) ? true : false;

        setTimeout(() => {
          this.initSelectedVehicleOwner();
          this.initSelectedVehicleOperator();
        });
      } else {
        console.log('Can\'t find vehicle with ID:', this.vehicleId);
        // Redirect to vehicle uri of current quote
        // let redirect = '/dashboard/quotes/' + this.quoteId + '/vehicles';
        const redirect = '/dashboard/auto/quotes/' + this.quoteId + '/vehicles';
        this.router.navigate([redirect], { replaceUrl: true });
      }
    } else if (this.vehicles && this.vehicles.length) {
      // let redirect = '/dashboard/' + this.vehicles[0].meta.href;
      const redirect = '/dashboard/auto/' + this.vehicles[0].meta.href;
      this.router.navigate([redirect], { replaceUrl: true });
    } else if (!preventAddNewVehicle) {
      this.addNewVehicle();
    }
    this.setOptionsLocationCity(this.selectedQuote.lob, this.selectedVehicleLocationData.state);

  }



  private initSelectedVehicleGeneralDetails(): Promise<any> {
    if (!this.selectedVehicle.vin || !this.vinValidate(this.selectedVehicle.vin)) {
      return Promise.reject('No VIN defined for the vehicle');
    }

    this.subscriptionInitVehicleGeneralDetail && this.subscriptionInitVehicleGeneralDetail.unsubscribe();

    return new Promise((resolve, reject) => {
      this.subscriptionInitVehicleGeneralDetail = this.lookupsService.getVehicleDataByVin(this.selectedVehicle.vin).pipe(
        first())
        .subscribe(
          (generalDetail: VehicleGeneralDetails) => {
            // if (!generalDetail) {
            //   console.log('%c 1: VIN number not found, Allow to manually populate vehicle data', 'color:gold');
            //     // TODO:: Allow to manually populate vehicle data
            // }

            // TODO:: Allow to manually populate vehicle data
            this.handleVinLookupNoVehicleGeneralDetails(generalDetail, 'initSelectedVehicleGeneralDetails');

            this.setSelectedVehicleGeneralGeneralDetails(generalDetail);
            resolve(generalDetail);
          },
          err => {
            console.log('TRIM OPTION PREPARE BY VIN ERROR', err);
            reject(err);
          }
        );
    });
  }

  private initSelectedVehicleOptionsProperty(): void {
    if (this.selectedVehicle.options == null) {
      this.selectedVehicle.options = new VehicleOptions();
    }
  }

  private initSelectedVehicleOwner(): void {
    if (!this.selectedVehicle.owner || !this.selectedVehicle.owner.meta.href) {
      // if (this.optionsOwner[0]) {
      //   this.setSelectedVehicleOwner(this.optionsOwner[0].data);
      //   this.updateVehicleData();
      // } else {
        this.initDriversList()
          .then(() => {
            if (this.optionsOwner[0]) {
              this.setSelectedVehicleOwner(this.optionsOwner[0].data);
              this.updateVehicleData();
            }
          })
          .catch(err => console.log(err));
      // }
    }
  }

  private initSelectedVehicleOperator(): void {
    if (!this.selectedVehicle.operator || !this.selectedVehicle.operator.meta.href) {
      // if (this.optionsOperator[0]) {
      //   this.setSelectedVehicleOperator(this.optionsOperator[0].data);
      //   this.updateVehicleData();
      // } else {
        this.initDriversList()
          .then(() => {
            if (this.optionsOperator[0].data) {
              this.setSelectedVehicleOperator(this.optionsOperator[0].data);
              this.updateVehicleData();
            }
          })
          .catch(err => console.log(err));
      // }
    }
  }


  // Quote Location Logic - SPRC-267
  // ----------------------------------------------------------------------------
  private initSelectedVehicleLocationData(): void {
    // console.log('INIT LOCATION')

    if (this.selectedVehicle.garagingAddress == null || !this.selectedVehicle.garagingAddress.meta.href) {
      console.log('this.selectedVehicle ||| ', this.selectedVehicle);
      // Try to get already existing location
      if (this.quoteLocations.length) {
        // console.log('INIT LOCATION - FROM STORAGE::', this.quoteLocations);
        let tmpLocationData = this.quoteLocations[0];

        // Make sure to use the location that is already used by other vehicle (the first one).
        if (this.vehicles && this.vehicles[0] && this.vehicles[0].garagingAddress && this.vehicles[0].garagingAddress.meta && this.vehicles[0].garagingAddress.meta.href) {
          const tmpLocationToSet = this.quoteLocations.find((l: LocationData) => {
            return l.meta.href === this.vehicles[0].garagingAddress.meta.href;
          });

          if (tmpLocationToSet) {
            tmpLocationData = tmpLocationToSet;
          }
        }

        this.selectedVehicleLocationData = JSON.parse(JSON.stringify(tmpLocationData));
        this.selectedVehicle.garagingAddress = {meta: {href: ''}};
        this.selectedVehicle.garagingAddress.meta.href = this.selectedVehicleLocationData.meta.href;
        this.setSelectedVehicleLocationStateAndValue();
        this.updateVehicleData();
        this.initClientAddressUpdating(); // Manage Client Address
      } else {
        // console.log('INIT LOCATION - CREATE NEW::');

        this.locationsService.createLocation(this.quoteId)
          .subscribe(
            (res: LocationData) => {
              this.storageService.updateQuoteLocationsSingleItem(res);

              this.selectedVehicleLocationData = JSON.parse(JSON.stringify(res));
              this.selectedVehicle.garagingAddress = {meta: {href: ''}};
              this.selectedVehicle.garagingAddress.meta.href = this.selectedVehicleLocationData.meta.href;
              this.setSelectedVehicleLocationStateAndValue();
              this.updateVehicleData();
              this.initClientAddressUpdating(); // Manage Client Address
            },
            err => console.log('LOCATION DATA CREATE ERROR: ', err)
          );

      }
    } else {
      // this.helperLocationGetAndSetExistingLocationDataFromAPI();

      // Optimization - Try to get location data from storage
      if (this.quoteLocations.length) {
        // console.log('INIT LOCATION FROM STORAGE IF EXISTS')
        const locationToSet = this.quoteLocations.find((item: LocationData) => {
          return item.meta.href === this.selectedVehicle.garagingAddress.meta.href;
        });

        if (locationToSet) {
          this.selectedVehicleLocationData = JSON.parse(JSON.stringify(locationToSet));
          this.setSelectedVehicleLocationStateAndValue();
          this.initClientAddressUpdating(); // Manage Client Address
        } else {
          this.helperLocationGetAndSetExistingLocationDataFromAPI()
            .then(() => this.initClientAddressUpdating())
            .catch(err => console.log(err));
        }

      } else {
        // Else get Data from API
        // console.log('INIT LOCATION FROM API');
        this.helperLocationGetAndSetExistingLocationDataFromAPI()
          .then(() => this.initClientAddressUpdating())
          .catch(err => console.log(err));
      }

    }

  }

  private helperLocationGetAndSetExistingLocationDataFromAPI(): Promise<any> {
    return new Promise<void>((resolve, reject) => {
      this.locationsService.getLocationByUri(this.selectedVehicle.garagingAddress.meta.href).pipe(
        take(1))
        .subscribe(
          res => {
            this.selectedVehicleLocationData = res;
            this.setSelectedVehicleLocationStateAndValue();
            resolve();
          },
          err => {
            console.log('Location data read error: ', err);
            reject(err);
          }
        );
    });
  }
  public manageLocationsOnDataChangeTrigger() {
    this._manageLocationDataUpdatingFinished.next(false);
    this.manageLocationsOnDataChange()
      .then(() => {
        this._manageLocationDataUpdatingFinished.next(true);
        this.updateClientAddress();
        this.addresses = this.quoteService.getQuoteAndClientAddresses(this.quoteId);
        // ReRate
        this.premiumsService.rerateAll();
      })
      .catch(() => this._manageLocationDataUpdatingFinished.next(true));
  }

  /**
   * @returns Promise<boolean> - true if location has been successfully updated, false if error occurred
   */
  public manageLocationsOnDataChange(): Promise<boolean> {
    console.log('Manage Location Data For Vehicle', this.selectedVehicleLocationData);

    return new Promise((resolve, reject) => {
      // Check up if any other vehicle uses this address
      const locationsMetaHrefsUsedByOtherVehicles: string[] = this.vehicles
      .filter((veh: Vehicle) => veh.resourceId !== this.selectedVehicle.resourceId)
      .map((veh: Vehicle) => {
        return (veh.garagingAddress && veh.garagingAddress.meta && veh.garagingAddress.meta.href) ? veh.garagingAddress.meta.href : '';
      });

      const locationsUsedByOtherVehicles: LocationData[] = this.quoteLocations
        .filter((loc: LocationData) => {
          return locationsMetaHrefsUsedByOtherVehicles.some(href => href === loc.meta.href);
        });

      // Check up if current location is in use by other vehicle
      const sameLocationDataUsedByOtherVehicle: LocationData = locationsUsedByOtherVehicles
        .find(item => {
          return item.meta.href === this.selectedVehicleLocationData.meta.href;
        });

      // Check up if any location has the same data as the one we have currently in form (this.selectedVehicleLocationData)
      const foundSameLocationdDataValuesAsCurrentValues: LocationData = locationsUsedByOtherVehicles
        .find(item => {
          const result = this.compareLocationDataIfAreTheSame(item, this.selectedVehicleLocationData);
          return result;
        });

      // Start with location management
      if (sameLocationDataUsedByOtherVehicle) {
        console.log('%c THIS LOCATION IS IN USE BY OTHER VEHICLE', 'color:red');

        if (foundSameLocationdDataValuesAsCurrentValues) {
          console.log('%c -- LOCATION WITH THE SAME DATA FOUND', 'color:#2fb1d2');
          console.log('%c -- Assign location to vehicle', 'color:#2fb1d2');
          // Assign location to vehicle
          // Update Vehicle garagingAddress with LocationData href

          this.selectedVehicleLocationData = JSON.parse(JSON.stringify(foundSameLocationdDataValuesAsCurrentValues));
          this.selectedVehicle.garagingAddress.meta.href = this.selectedVehicleLocationData.meta.href;
          this.updateVehicleData().then(result => resolve(result));
        } else {
          console.log('%c -- NOT FOUND LOCATION WITH THE SAME DATA', 'color:#2fb1d2');
          console.log('%c -- Create new location data for vehicle', 'color:#2fb1d2');
          // Create new location
          // Update Storage
          // Update Vehicle garagingAddress with LocationData href

          const newLocationDataToSend = new LocationData();
          newLocationDataToSend.addressType = 'Garaging';
          newLocationDataToSend.address1 = this.selectedVehicleLocationData.address1;
          newLocationDataToSend.address2 = this.selectedVehicleLocationData.address2;
          newLocationDataToSend.city = this.selectedVehicleLocationData.city;
          newLocationDataToSend.state = this.selectedVehicleLocationData.state;
          newLocationDataToSend.zip = this.selectedVehicleLocationData.zip;

          this.locationsService.createLocation(this.quoteId, newLocationDataToSend)
          .subscribe(
            (res: LocationData) => {
              this.storageService.updateQuoteLocationsSingleItem(res);

              this.selectedVehicleLocationData = JSON.parse(JSON.stringify(res));
              this.selectedVehicle.garagingAddress = {meta: {href: ''}};
              this.selectedVehicle.garagingAddress.meta.href = this.selectedVehicleLocationData.meta.href;
              this.setSelectedVehicleLocationStateAndValue();
              this.updateVehicleData().then(result => resolve(result));
              console.log('CREATED NEW LOCATION DATA');
            },
            err => console.log('LOCATION DATA CREATE ERROR: ', err)
          );
        }
      } else {
        console.log('%c THIS LOCATION IS NOT IN USE BY ANY OTHER VEHICLE', 'color:green');

        if (foundSameLocationdDataValuesAsCurrentValues) {
          console.log('%c -- LOCATION WITH THE SAME DATA FOUND', 'color:#2fb1d2');
          console.log('%c -- Assign found location to vehicle, and delete current location data', 'color:#2fb1d2');

          // Delete current location data (this.selectedVehicleLocationData)
          // Assign found location (with the same data) to vehicle
          // Update storage
          // Update Vehicle garagingAddress with LocationData href

          this.locationsService.deleteLocationByUri(this.selectedVehicleLocationData.meta.href).pipe(
            take(1))
            .subscribe(res => {
              // Deleting location data has too big delay, so data in storage is updated just after the request was sent
              // although it should be done after response
              console.log('Deleted Location', res);
            });

            this.storageService.deleteQuoteLocationsSingleItem(this.selectedVehicleLocationData);
            this.selectedVehicleLocationData = JSON.parse(JSON.stringify(foundSameLocationdDataValuesAsCurrentValues));
            this.selectedVehicle.garagingAddress.meta.href = this.selectedVehicleLocationData.meta.href;
            console.log('UPDATE VEHICLE DATA');

            this.updateVehicleData().then(result => resolve(result));

        } else {
          console.log('%c -- NOT FOUND LOCATION WITH THE SAME DATA', 'color:#2fb1d2');
          console.log('%c -- Update current location data', 'color:#2fb1d2');
          // Update current location data (this.selectedVehicleLocationData)
          // Update Storage

          this.updateVehicleLocationData().then(result => resolve(result));
          this.storageService.updateQuoteLocationsSingleItem(this.selectedVehicleLocationData);
        }
      }

    });
  }

  private compareLocationDataIfAreTheSame(location1: LocationData, location2: LocationData): boolean {
    // Normalize addresses
    location1.address1 = (location1.address1) ? location1.address1.trim() : '';
    location1.address2 = (location1.address2) ? location1.address2.trim() : '';
    location1.city = (location1.city) ? location1.city.trim() : '';
    location1.state = (location1.state) ? location1.state.trim() : '';
    location1.zip = (location1.zip) ? location1.zip.trim() : '';

    location2.address1 = (location2.address1) ? location2.address1.trim() : '';
    location2.address2 = (location2.address2) ? location2.address2.trim() : '';
    location2.city = (location2.city) ? location2.city.trim() : '';
    location2.state = (location2.state) ? location2.state.trim() : '';
    location2.zip = (location2.zip) ? location2.zip.trim() : '';

    if (location1.address1 === location2.address1
      && location1.address2 === location2.address2
      && location1.city === location2.city
      && location1.state === location2.state
      && location1.zip === location2.zip) {
        return true;
      }

    return false;
  }
  // -------- END SPRC-267-------------------------------------------------------


  // Options setting
  // -------------------------------------------------------------------------------
  private initStaticOptions(): void {
    this.setOptionsVehicleTypes();
    this.setOptionsVehicleUsage();
    this.setOptionsVehicleYears();
    this.setOptionsVehicleBodyStyle();
    this.setOptionsLocationStates(this.selectedQuote.lob);
  }

  private updateOptionsVehicle(): Promise<any> {
    this.optionsMake = [];
    this.optionsModel = [];
    this.optionsTrim = [];

    this.fieldVinInvalid = false;

    return Promise.all([
      this.setOptionsVehicleMake(),
      this.setOptionsVehicleModel(),
      this.setOptionsVehicleTrim()
    ]);
  }

  private setDefaultSelectedOptionsForVehicle(): void {
    !this.selectedVehicle.vehicleType && (this.selectedVehicle.vehicleType = 'Private Passenger');   // Set default to Private Passenger
    !this.selectedVehicle.usage && (this.selectedVehicle.usage = 'Pleasure');               // Set default to Pleasure
  }

  private setOptionsVehicleTypes(): void {
    this.optionsVehicleTypes = [
      {id: VEHICLE_TYPES.privatePassenger, text: VEHICLE_TYPES.privatePassenger},
      {id: VEHICLE_TYPES.pickup, text: VEHICLE_TYPES.pickup},
      {id: VEHICLE_TYPES.motorcycle, text: VEHICLE_TYPES.motorcycle},
      {id: VEHICLE_TYPES.trailer, text: VEHICLE_TYPES.trailer},
    ];
  }

  private setOptionsVehicleYears(): void {
    this.optionsYear = this.helperGenerateYearsOptions();
  }

  private setOptionsVehicleUsage(): void {
    this.optionsUsage = [
      {id: 'Pleasure', text: 'Pleasure'},
      {id: 'Commute', text: 'Commute'},
      {id: 'Business', text: 'Business'},
      {id: 'Farm', text: 'Farm'},
    ];
  }


  private setOptionsLocationStates(lob: string): void {
    // let subscription = this.subsService.getRatingStates(this.userData.agencyId, lob)
    //   .subscribe(
    //     states => {
    //       this.optionsState = states.items.map(state => new FilterOption(state.code, state.code, state));
    //       subscription.unsubscribe();
    //     },
    //     err => console.log(err)
    //   );
    this.optionsState = STATES;
  }

  setOptionsLocationCity(lob: string, state: string): void {
    this.optionsCity = [new FilterOption(CITY_OPTION_OUT_OF_STATE, CITY_OPTION_OUT_OF_STATE)];
    const tmpOptions = [new FilterOption(CITY_OPTION_OUT_OF_STATE, CITY_OPTION_OUT_OF_STATE)];

    this.specsService.getTownsList(state)
      .subscribe(
        res => {
          Array.prototype.push.apply(tmpOptions, this.helperTownsToCitiesOptions(res.items));
          this.optionsCity = tmpOptions;
        },
        err => console.log(err)
      );
  }

  private setOptionsVehicleOwner(): void {
    this.optionsOwner = this.drivers.map(this.helperDriverDataToFilterOption);
  }

  private setOptionsVehicleOperator(): void {
    this.optionsOperator = this.drivers.map(this.helperDriverDataToFilterOption);
  }



  private setOptionsVehicleMake(): Promise<any> {
    this.fieldMakeDisabled = true;
    const vehicleOptionsMake: VehicleOptionsForVehicle = {
      vehicleResourceId: this.selectedVehicle.resourceId,
      options: [],
    };

    if (!this.selectedVehicle.year) { return; }
    this.subscriptionVehicleMakes && this.subscriptionVehicleMakes.unsubscribe();

    if (this.selectedVehicle.vehicleType === VEHICLE_TYPES.trailer) {
      return new Promise(resolve => {
        const options = VEHICLE_OPTIONS_MAKE_TRAILER;

        this.fieldMakeDisabled = true;
        this.optionsMake = options;
        vehicleOptionsMake.options = options;
        this.storageService.updateVehiclesOptionsMakeSingleItem(vehicleOptionsMake);
        resolve(options);
      });
    }

    if (this.selectedVehicle.vehicleType === VEHICLE_TYPES.motorcycle) {
      return new Promise(resolve => {
        const options = VEHICLE_OPTIONS_MAKE_MOTORCYCLE;

        this.fieldMakeDisabled = false;
        this.optionsMake = options;
        vehicleOptionsMake.options = options;
        this.storageService.updateVehiclesOptionsMakeSingleItem(vehicleOptionsMake);
        resolve(options);
      });
    }

    return new Promise((resolve, reject) => {
      this.subscriptionVehicleMakes = this.lookupsService.getVehicleMakesAsOptions(this.selectedVehicle.year).pipe(
        first())
        .subscribe(
          res => {
            this.fieldMakeDisabled = false;
            this.optionsMake = res;
            vehicleOptionsMake.options = res;
            this.storageService.updateVehiclesOptionsMakeSingleItem(vehicleOptionsMake);
            resolve(res);
          },
          err => {
            console.log(err);
            reject(err);
          }
        );
    });

  }

  private setOptionsVehicleModel(): Promise<any> {
    if (this.selectedVehicle.vehicleType === VEHICLE_TYPES.trailer) {
      return new Promise(resolve => {
        const options = [];
        this.fieldModelDisabled = false;
        this.optionsModel = options;
        this.helperSwitchFieldModelTypeAndRequiredFieldsRulesToTextInput(true, false);
        resolve(options);
      });
    } else if (this.selectedVehicle.vehicleType === VEHICLE_TYPES.motorcycle) {
      return new Promise(resolve => {
        const options = [];
        this.fieldModelDisabled = false;
        this.optionsModel = options;
        this.helperSwitchFieldModelTypeAndRequiredFieldsRulesToTextInput(true, false);
        resolve(options);
      });
    } else {
      this.fieldModelDisabled = true;

      if (!this.selectedVehicle.year || !this.selectedVehicle.make) { return; }

      this.subscriptionVehicleModels && this.subscriptionVehicleModels.unsubscribe();
      return new Promise((resolve, reject) => {
        this.subscriptionVehicleModels = this.lookupsService.getVehicleModelsAsOptions(this.selectedVehicle.year, this.selectedVehicle.make).pipe(
        first())
        .subscribe(
          res => {
            const forceRequiredVin = this.helperVehicleIsFromYearOrPrior(1984);
            this.fieldModelDisabled = false;
            this.optionsModel = res;
            this.helperSwitchFieldModelTypeAndRequiredFieldsRulesToTextInput(this.optionsModel.length <= 0);
            // this.helperSwitchFieldModelTypeAndRequiredFieldsRulesToTextInput(this.optionsModel.length <= 0, forceRequiredVin);
            resolve(res);
          },
          err => {
            console.log(err);
            reject(err);
          }
        );
      });
    }
  }

  private setOptionsVehicleBodyStyle(): void {
    const options: string[] = ['Van', 'Wagon', 'Pickup Truck', 'SUV', 'All Other PP Types' ];
    this.optionsBodyStyles = options.map(option => new FilterOption(option, option));
  }


  private setOptionsVehicleTrim(): Promise<any> {
    this.fieldTrimDisabled = true;

    if (!this.selectedVehicle.year || !this.selectedVehicle.make || !this.selectedVehicle.model) {
      return; // Promise.reject('Vehicle Trim options faild');
    }

    return this.lookupVehicleModelDetail()
      .then(res => {
        this.optionsTrim = this.helperProccessTrimsOptions(res.items);
        this.fieldTrimDisabled = false;
        return {response: res, options: this.optionsTrim};
      })
      .catch(err => console.log(err));
  }

  private setSelectedVehicleGeneralGeneralDetails(vehicleGeneralDetails: VehicleGeneralDetails): void {
    this.selectedVehicleDetails = vehicleGeneralDetails;
  }

  private setSelectedVehicleDataBasedOnVehicleDetail(generalDetail: VehicleGeneralDetails, vin?: string): void {
    // Prevent error: eg. for VIN 'ZAM39NKA5D0067197' API is not returning results
    if (!generalDetail) {
      console.log('Error: The API does not returned VehicleGeneralDetails for vehicle: ', this.selectedVehicle);
      return;
    }

    this.setSelectedVehicleGeneralGeneralDetails(generalDetail);
    this.selectedVehicle.year = generalDetail.year;
    this.selectedVehicle.make = generalDetail.manufacturer.name;
    this.selectedVehicle.model = generalDetail.detail.modelName;
    this.selectedVehicle.vin = vin || generalDetail.detail.vin;

    this.selectedVehicle.trimLevel = this.vehicleService.concatVehicleDetailDescriptionFields(generalDetail);

    this.autoPopulateSelectedVehicleBodyStyle(generalDetail);

    this.updateOptionsVehicle()
      .then(() => {
        this.setSelectedVehicleGeneralGeneralDetails(generalDetail);
        this.processVehicleOptionsComponetUpdate();
      });
  }

  private setSelectedVehicleLocationStateAndValue(): void {
    const prevStateValue = this.selectedVehicleLocationData.state;
    this.fieldLocationStateDisabled = !(this.selectedVehicleLocationData.city === CITY_OPTION_OUT_OF_STATE);


    if (this.fieldLocationStateDisabled) {
      this.selectedVehicleLocationData.state = 'MA'; // this.selectedQuote.state

      if (prevStateValue !== this.selectedVehicleLocationData.state) {
        this.updateVehicleLocationData();
      }
    }
  }

  private setSelectedVehicleOwner(driver: Driver): void {
    const data = {
      meta: {
        href: driver.meta.href
      }
    };
    this.selectedVehicle.owner = data;
  }

  private setSelectedVehicleOperator(driver: Driver): void {
    const data = {
      meta: {
        href: driver.meta.href
      }
    };
    this.selectedVehicle.operator = data;
  }

  private autoPopulateSelectedVehicleBodyStyle(genDetails: VehicleGeneralDetails): void {
    if (!genDetails.detail.vin) {
      this.fieldBodyStyleDisabled = false;
    } else if (genDetails.detail.vin && !genDetails.massBodyStyleGroup) {
      this.fieldBodyStyleDisabled = false;
    } else {
      this.fieldBodyStyleDisabled = true;
      this.selectedVehicle.bodyStyle = genDetails.massBodyStyleGroup || '';
    }
  }


  // Lookup calls
  // -------------------------------------------------------------------------------
  private lookupVehicleModelDetail(): Promise<any> {
    return new Promise((resolve, reject) => {
      if (!this.selectedVehicle.year || !this.selectedVehicle.make || !this.selectedVehicle.model) {
        reject('[lookupVehicleModelDetail] :: Missing at least one of the value: year, make, model');
      }

      this.subscriptionVehicleModelDetail && this.subscriptionVehicleModelDetail.unsubscribe();
      this.subscriptionVehicleModelDetail = this.lookupsService.getVehicleModelDetails(
        this.selectedVehicle.year,
        this.selectedVehicle.make,
        this.selectedVehicle.model,
        this.selectedQuote.effectiveDate
      ).pipe(first())
      .subscribe(
        res => resolve(res),
        err => reject(err)
      );
    });
  }

  private RMVlookupVehicleByVIN(vin: string = ''): Promise<any> {
    let data;
    if (vin) {
      vin = vin.toUpperCase();
    }

    // if (!this.vinValidate(vin)) { return Promise.reject('Invalid VIN'); }

    // if (this.vinValidate(vin)) {
      data = {
        quoteId: this.quoteId,
        policyEffectiveDate: this.selectedQuote.effectiveDate,
        vin: vin,
        resourceId: this.selectedVehicle.resourceId
      };
    // }

    if (this.subscriptionVehicleRMVVinLookup) {
      this.subscriptionVehicleRMVVinLookup.unsubscribe();
      this.overlayLoaderService.hideLoader();
    }

    this.overlayLoaderService.showLoader();

    return this.lookupsService.updateVehicleDataByVin(data)
    .toPromise()
    .then((RMVvehicle: VehicleRMVLookupData) => {
      this.overlayLoaderService.hideLoader();
      this.showRMVvehicleReport(RMVvehicle);
      return RMVvehicle;
    })
    .catch(err => {
      this.overlayLoaderService.hideLoader();
      console.log('RMV Invalid VIN Error', err);

      const errorVehicle = new VehicleRMVLookupData();
        errorVehicle.messages.push({
            message: 'The VIN provided is invalid. Please provide a valid VIN number and try again.'
          });
          this.showRMVvehicleReport(errorVehicle);
    });
  }

  private RMVlookupVehicleByPlate(plateNumber: string = '', plateType: string = '') {
    const data = {
      quoteId: this.quoteId,
      policyEffectiveDate: this.selectedQuote.effectiveDate,
      plateNumber: plateNumber,
      plateType: plateType,
      resourceId: this.selectedVehicle.resourceId
    };

    if (this.subscriptionVehicleRMVPlateLookup) {
      this.subscriptionVehicleRMVPlateLookup.unsubscribe();
      this.overlayLoaderService.hideLoader();
    }

    this.overlayLoaderService.showLoader();

    return this.lookupsService.updateVehicleDataByPlate(data)
      .toPromise()
      .then((RMVvehicle: VehicleRMVLookupData) => {
        this.overlayLoaderService.hideLoader();
        this.showRMVvehicleReport(RMVvehicle);
        return RMVvehicle;
      })
      .catch(err => {
        this.overlayLoaderService.hideLoader();
        console.log('RMV Invalid Plate Type Error', err);

        const errorVehicle = new VehicleRMVLookupData();
          errorVehicle.messages.push({
             message: 'The Plate provided is invalid/expired. Please provide a valid Plate number, verify the plate type and try again.'
           });
           this.showRMVvehicleReport(errorVehicle);
      });
  }
  private lookupGetVehicleDetailByVINAndUpdateVehicleData(vin: string, delay: number = 0): Promise<any> {
    // let vin = '1HGCP26389A117873' // 2009 Honda Accord // 1hgcp26389A117873
    // let vin = '5GAKVBKD7FJ128908' // 2015 Buick Enclave

    // Reset loader
    this.loadingGetVehicleDetailByVINAndUpdateVehicleData = false;

    if (vin) {
      vin = vin.toUpperCase();
    }

    const selectedVehicle = JSON.parse(JSON.stringify(this.selectedVehicle));

    if (this.lookupGetVehicleDetailByVINAndUpdateVehicleDataTimer) {
      clearTimeout(this.lookupGetVehicleDetailByVINAndUpdateVehicleDataTimer);
    }

    if (this.subscriptionVehicleVinLookup) {
      this.subscriptionVehicleVinLookup.unsubscribe();
      this.symbolsService.setSymbolsUpdatingInProgress(selectedVehicle, false);
    }

    if (!this.vinValidate(vin)) { return Promise.reject('Invalid VIN'); }

    this.symbolsService.setSymbolsUpdatingInProgress(selectedVehicle, true);
    this.loadingGetVehicleDetailByVINAndUpdateVehicleData = true;

    return new Promise((resolve, reject) => {
      this.lookupGetVehicleDetailByVINAndUpdateVehicleDataTimer = window.setTimeout(() => {
        this.subscriptionVehicleVinLookup = this.lookupsService.getVehicleDataByVin(vin, this.selectedQuote.effectiveDate).pipe(
          take(1))
          .subscribe(
            generalDetails => {
              if (!generalDetails) {
                console.log('VIN number not found.');
              }

              // TODO:: Allow to manually populate vehicle data
              this.handleVinLookupNoVehicleGeneralDetails(generalDetails, 'lookupGetVehicleDetailByVINAndUpdateVehicleData');

              this.loadingGetVehicleDetailByVINAndUpdateVehicleData = false;
              this.fieldVinInvalid = false;
              this.setSelectedVehicleDataBasedOnVehicleDetail(generalDetails, vin);
              // 2019-07-23 :: FIX FOR: https://bostonsoftware.atlassian.net/browse/SPR-3279
              // this.symbolsRetrievePlanSymbols(selectedVehicle, generalDetails);
              this.symbolsRetrievePlanSymbols(this.selectedVehicle, generalDetails);
              this.updateVehicleData();
              resolve(generalDetails);
            },
            err => {
              this.loadingGetVehicleDetailByVINAndUpdateVehicleData = false;
              this.symbolsService.setSymbolsUpdatingInProgress(selectedVehicle, false);
              console.log('ERRROR >>>', err);
              this.fieldVinInvalid = true;
              this.fieldMakeDisabled = true;
              this.fieldModelDisabled = true;
              this.fieldTrimDisabled = true;
              this.fieldBodyStyleDisabled = false;

              this.optionsTrim = [];

              // Reset Form Data
              this.selectedVehicle.year = '';
              this.selectedVehicle.make = '';
              this.selectedVehicle.model = '';
              this.selectedVehicle.bodyStyle = '';
              this.selectedVehicle.trimLevel = '';

              this.updateVehicleData();
              reject(err);
            }
          );
      }, delay);
    });


  }

  private showRMVvehicleReport(RMVvehicle: VehicleRMVLookupData) {
    // console.log('%c RMVvehicle', 'color:red;font-weight:bold', RMVvehicle);
    this.vehicleService.selectedRMVvehicle = RMVvehicle;
    this.storageService.setStorageData('rmvVehicleLookup', [ RMVvehicle ]);
    this.overlayRouteService.go('rmvreports', 'vehicle', RMVvehicle.make + '-' + RMVvehicle.model + '-' + RMVvehicle.vin);
  }

  // Symbols
  // -------------------------------------------------------------------------------
  private symbolsRetrievePlanSymbols(vehicle: Vehicle, genDetails: VehicleGeneralDetails): Promise<void> {
    this.symbolsService.setSymbolsUpdatingInProgress(vehicle, false);

    if (this.subscriptionVehiclesPlanSymbols) {
      this.subscriptionVehiclesPlanSymbols.unsubscribe();
    }

    // Prevent error: eg. for VIN 'ZAM39NKA5D0067197' API is not returning results
    if (!genDetails) {
      console.log('Error: There are no VehicleGeneralDetails for vehicle: ', this.selectedVehicle);
      return Promise.resolve();
    }

    return new Promise(resolve => {
      this.symbolsService.setSymbolsUpdatingInProgress(vehicle, true);
      this.subscriptionVehiclesPlanSymbols = this.symbolsService.retrieveVehiclePlanSymbols(
        vehicle,
        genDetails,
        this.selectedQuote.effectiveDate,
        this.selectedQuotePlansList
      ).pipe(take(1))
      .subscribe(
          res => {
            this.selectedVehiclePlanSymbols = res;
            this.processVehicleSymbolsComponentUpdate();
            this.symbolsService.setSymbolsUpdatingInProgress(vehicle, false);
            resolve();
          },
          err => {
            console.log(err);
            this.symbolsService.setSymbolsUpdatingInProgress(vehicle, false);
            resolve();
          }
      );
    });
  }

  private resetVehicleSymbols(): void {
    this.selectedVehicle.symbols = [];
    if (this.symbolsComponent) {
      this.symbolsComponent.callRefreshSelectedVehicleData(this.selectedVehicle);
      this.symbolsComponent.resetSelectedVehicleSymbolsValues();
    }
  }


  // Select fields change data action
  // -------------------------------------------------------------------------------
  public actionOnVehicleTypeFieldSelect($ev, propertyName): void {
    // this.switchFormForVehicleType($ev.id);
    this.selectedVehicle.vehicleType = $ev.id;

    this.selectedVehicle.make = '';
    this.selectedVehicle.model = '';
    this.selectedVehicle.bodyStyle = '';
    this.selectedVehicle.vin = '';
    this.selectedVehicle.trimLevel = '';
    this.selectedVehicle.annualMiles = '';
    this.selectedVehicle.priceValue = '';
    this.selectedVehicle.displacement = '';
    this.selectedVehicle.options = new VehicleOptions();

    this.switchFormForVehicleType($ev.id);

    this.resetVehicleSymbols();
    this.onSelectFieldDataSelected($ev, propertyName);

    this.setOptionsVehicleMake();
    this.setOptionsVehicleModel();

    // this.setOptionsVehicleMake()
    //   .then(() => this.setOptionsVehicleModel())
    //   .catch(err => console.log('ERR: ', err));

    // Set correct data for Driver
    this.setForOperatorDriverMotorcycleValue();
  }


  public actionOnYearFieldSelect($ev, propertyName): void {

    if (this.selectedVehicle.vehicleType !== VEHICLE_TYPES.trailer) {
      this.selectedVehicle.make = '';
    }

    // https://bostonsoftware.atlassian.net/browse/SPR-2929
    if (!this.vinNumberLookupGeneralDetailsNotFound) {
      this.selectedVehicle.vin = '';
      this.selectedVehicle.model = '';
      this.selectedVehicle.trimLevel = '';
      this.fieldModelDisabled = true;
      this.fieldTrimDisabled = true;
    }

    // this.selectedVehicle.model = '';
    this.selectedVehicle.bodyStyle = '';
    // this.selectedVehicle.trimLevel = '';
    this.selectedVehicle.options = new VehicleOptions();

    this.fieldMakeDisabled = true;
    // this.fieldModelDisabled = true;
    // this.fieldTrimDisabled = true;
    this.fieldBodyStyleDisabled = false;
    this.fieldVinInvalid = false;

    switch (this.selectedVehicle.vehicleType) {
      case VEHICLE_TYPES.motorcycle:
        // TODO
        break;
      case VEHICLE_TYPES.trailer:
        this.fieldModelDisabled = false;
        break;
    }

    if (!this.vinNumberLookupGeneralDetailsNotFound) {
      this.resetVehicleSymbols();
    }

    // this.resetVehicleSymbols();

    // Set Symbols requirements (use automanager service)
    this.vehiclesSymbolsAutomanagerService.performVehicleSymbolsUpdate(this.selectedVehicle);
    this.onSelectFieldDataSelected($ev, propertyName);
    this.setOptionsVehicleMake();
  }

  public actionOnMakeFieldSelect($ev, propertyName): void {

    // https://bostonsoftware.atlassian.net/browse/SPR-2929
    if (!this.vinNumberLookupGeneralDetailsNotFound) {
      this.selectedVehicle.vin = '';
      this.selectedVehicle.model = '';
      this.selectedVehicle.trimLevel = '';
      this.fieldModelDisabled = true;
    } else {
      this.fieldModelDisabled = false;
    }

    // this.selectedVehicle.model = '';
    this.selectedVehicle.bodyStyle = '';
    // this.selectedVehicle.trimLevel = '';
    this.selectedVehicle.options = new VehicleOptions();

    // this.fieldModelDisabled = true;
    this.fieldTrimDisabled = true;
    this.fieldBodyStyleDisabled = false;
    this.fieldVinInvalid = false;

    switch ($ev.id) {
      case VEHICLE_TYPES.motorcycle:
        // TODO
        break;
      case VEHICLE_TYPES.trailer:
        this.fieldModelDisabled = false;
        break;
    }

    if (!this.vinNumberLookupGeneralDetailsNotFound) {
      this.resetVehicleSymbols();
    }
    // this.resetVehicleSymbols();

    this.onSelectFieldDataSelected($ev, propertyName);

    // // https://bostonsoftware.atlassian.net/browse/SPR-2929
    // if (!this.vinNumberLookupGeneralDetailsNotFound) {
    //   this.setOptionsVehicleModel();
    // } else {
    //   const currentStateFieldTrimSwitchToText = this.fieldTrimSwitchToText;
    //   this.setOptionsVehicleModel()
    //     .then(() => {
    //       this.fieldTrimSwitchToText = currentStateFieldTrimSwitchToText;
    //     });
    // }

    // https://bostonsoftware.atlassian.net/browse/SPR-2929
    if (!this.vinNumberLookupGeneralDetailsNotFound) {
      this.setOptionsVehicleModel();
    }

    // this.setOptionsVehicleModel();
  }

  public actionOnModelFieldSelect($ev, propertyName): void {
    this.selectedVehicle.bodyStyle = '';
    this.selectedVehicle.vin = '';
    this.selectedVehicle.trimLevel = '';
    this.selectedVehicle.options = new VehicleOptions();

    this.fieldTrimDisabled = true;
    this.fieldBodyStyleDisabled = false;
    this.fieldVinInvalid = false;

    this.resetVehicleSymbols();

    this.onSelectFieldDataSelected($ev, propertyName);

    // If this is custom option (custom text), do not try to update date automatically
    if ($ev.selectedOption.data && $ev.selectedOption.data._internal_custom_option) {
      this.updateVehicleData();
      return;
    }

    // Check
    this.checkIfVehicleModelIsInManualMode();

    this.setOptionsVehicleTrim().then(val => {

      // If only one trim option, choose as selected
      if (this.optionsTrim.length && this.optionsTrim.length === 1) {
        this.setSelectedVehicleGeneralGeneralDetails(this.optionsTrim[0].data.generalDetail);
        this.processVehicleOptionsComponetUpdate();
        this.autoPopulateSelectedVehicleBodyStyle(this.optionsTrim[0].data.generalDetail);
        this.selectedVehicle.trimLevel = this.optionsTrim[0].id;

        // Update VIN
        if (!this.selectedVehicle.vin) {
          this.selectedVehicle.vin = this.optionsTrim[0].data.generalDetail.detail.vin;
        }

        // Retrive Vehicle Symbols
        if (this.selectedVehicle.bodyStyle) {
          // Reset Price New / Value
          this.selectedVehicle.priceValue = '';
          this.symbolsRetrievePlanSymbols(this.selectedVehicle, this.optionsTrim[0].data.generalDetail);
        }
      }

      this.updateVehicleData();
    })
    .catch(err => console.log(err));

  }

  private actionOnBodyStyleFieldSelect($ev, propertyName: string): void {
    this.selectedVehicle.bodyStyle = $ev.id;

    this.updateVehicleData();
  }

  // RMV lookup select plate type
  private selectRMVlookupPlateType(event) {
    this.rmvLookupPlateType = event.id;
  }

  // 'Trim' field
  private actionOnTrimFieldSelect($ev, propertyName): void {
    this.selectedVehicle.trimLevel = $ev.id;
    this.fieldVinInvalid = false;

    // If this is custom option (custom text), do not try to update date automatically
    if ($ev.selectedOption.data && $ev.selectedOption.data._internal_custom_option) {
      this.updateVehicleData();
      return;
    }

    if ($ev.selectedOption.data && $ev.selectedOption.data.generalDetail) {
      this.setSelectedVehicleGeneralGeneralDetails($ev.selectedOption.data.generalDetail);
      this.processVehicleOptionsComponetUpdate();
    }

    // if (!this.selectedVehicle.vin) {
    this.fieldVinInvalid = false;
    this.selectedVehicle.vin = this.selectedVehicleDetails.vin;
    // }

    this.autoPopulateSelectedVehicleBodyStyle(this.selectedVehicleDetails);

    // ?? Check if the bodyStyle has value ??
    if (this.selectedVehicle.bodyStyle) {
      // Reset Price New / Value
      this.selectedVehicle.priceValue = '';
      this.symbolsRetrievePlanSymbols(this.selectedVehicle, this.selectedVehicleDetails);
    }

    this.updateVehicleData();
  }

  // Price New/Value - Symbols update
  public actionOnPriceNewValueFieldFocus(): void {
    // this.fieldPriceNewValueHasChanged
  }

  // public actionOnPriceNewValueFieldBlur():void {
  public actionOnPriceNewValueFieldChange(): void {
    if (this.selectedVehicle.vehicleType === VEHICLE_TYPES.trailer || this.selectedVehicle.vehicleType === VEHICLE_TYPES.motorcycle) {
      this.updateVehicleData();
      return;
    }

    this.timerActionPriceFieldBlurSymbolsUpdate && clearTimeout(this.timerActionPriceFieldBlurSymbolsUpdate);
    this.symbolsComponent.callUpdateSelectedVehiclePriceNewValue(this.selectedVehicle.priceValue);

    if (!this.selectedVehicle) {
      // Repeat action if there is no SelectedVehicle or Vehicle General details set yet.
      this.timerActionPriceFieldBlurSymbolsUpdate = setTimeout(() => {
        // this.actionOnPriceNewValueFieldBlur()
        this.actionOnPriceNewValueFieldChange();
      }, 400);
    } else {
      if (this.selectedVehicle.year && this.symbolsComponent.allowToRecalculateSymbols()) {
        this.symbolsRetrievePlanSymbols(this.selectedVehicle, this.selectedVehicleDetails);
      }
    }

    this.updateVehicleData();
  }

  // Location: City, State
  private actionOnCityFieldSelect($ev, propertyName): void {
    this.onVehicleLocationDataSelect($ev, propertyName);
    this.setSelectedVehicleLocationStateAndValue();
  }

  private actionOnStateFieldSelect($ev, propertyName): void {
    this.onVehicleLocationDataSelect($ev, propertyName);
    this.setOptionsLocationCity(this.selectedQuote.lob, this.selectedVehicleLocationData.state);

  }

  public actionOnOwnerOperatorFieldsSelect($ev, propertyName: string): void {
    const data = {
      meta: {
        href: $ev.selectedOption.data.meta.href
      }
    };

    // Show Information that the driver can not be set as operator if has excluded vehicle
    // and prevent change value
    if (propertyName === 'operator') {
      if (this.checkIfVehicleIsExcludedForDriverByDriverMetaHref($ev.selectedOption.data.meta.href)) {
        // console.log('%c Vehicle Is Excluded for driver: ', 'color:red', $ev.selectedOption);
        if (this.refModalExcludedVehicle) {
          const tmpForDriver: Driver = this.getDriverByDriverMetaHref($ev.selectedOption.data.meta.href);
          this.excludedForDriverName = this.driverService.generateDriverName(tmpForDriver);

          this.refModalExcludedVehicle.open();

          // To force autocomplete input to show the correct value, we need to set null and then current value;
          const tmpData = JSON.parse(JSON.stringify(this.selectedVehicle[propertyName]));
          this.selectedVehicle[propertyName] = null;

          setTimeout(() => {
            this.selectedVehicle[propertyName] = tmpData;
          });
          return;
        }
      }
    }

    this.selectedVehicle[propertyName] = data;
    this.updateVehicleData();

    // If motorcycle, force on driver to set 'Motorcycle Field to yes'
    if (propertyName === 'operator') {
      this.setForOperatorDriverMotorcycleValue();
    }
  }

  private getDriverByDriverMetaHref(driverMetaHref: string): Driver {
    const driver: Driver = this.drivers.find((d: Driver) => d.meta.href === driverMetaHref);
    return driver;
  }

  public checkIfVehicleIsExcludedForDriverByDriverMetaHref(driverMetaHref: string): boolean {
    let isExcluded = false;
    // const driver: Driver = this.drivers.find((d: Driver) => d.meta.href === driverMetaHref);
    const driver: Driver = this.getDriverByDriverMetaHref(driverMetaHref);

    if (driver) {
      if (driver.exclusions && driver.exclusions.length) {
        const selectedVehicleIsExcludedIndex: number = driver.exclusions
          .findIndex((i) => i.meta.href === this.selectedVehicle.meta.href);

        if (selectedVehicleIsExcludedIndex !== -1) {
          isExcluded = true;
          // console.log('%c Vehicle Is Excluded for driver: ', 'color:red', driver);
          // console.log('Driver can not be set, show warning');
        }
      }
    }

    return isExcluded;
  }



  // https://bostonsoftware.atlassian.net/browse/SPRC-593
  private setForOperatorDriverMotorcycleValue(): void {
    const driverToInitialState = this.driversInitialStateDoNotEdit.find((driver: Driver) => driver.meta.href === this.selectedVehicle.operator.meta.href);
    const driverToUpdate = this.drivers.find((driver: Driver) => driver.meta.href === this.selectedVehicle.operator.meta.href);

    if (driverToUpdate && driverToInitialState) {

      if (this.selectedVehicle.vehicleType === VEHICLE_TYPES.motorcycle) {
        driverToUpdate.motorcycle = true;
      } else {
        // driverToUpdate.motorcycle = false;

        // Set previous Value
        driverToUpdate.motorcycle = driverToInitialState.motorcycle;
      }

      if (driverToUpdate.motorcycle !== driverToInitialState.motorcycle) {
        this.apiCommonService.putByUri(driverToUpdate.meta.href, driverToUpdate).pipe(
          take(1))
          .subscribe(res => {
            this.storageService.updateDriversListSingleItem(this.quoteId, driverToUpdate);
          });
      }

      // console.log('this.driversInitialStateDoNotEdit >>> ', this.driversInitialStateDoNotEdit);
    } else {
      console.log('DRIVER NOT FOUND');
    }

    // Checkup all vehicles if some of them is motorcycle
    // and has specific driver set as operator. Base on this reset value for drivers
    const driversForMotorcycleAsOperatorsHrefs: string[] = [];

    this.vehicles.forEach((vehicle: Vehicle) => {
      if (vehicle.vehicleType === VEHICLE_TYPES.motorcycle) {
        if (vehicle.operator.meta.href) {
          if (driversForMotorcycleAsOperatorsHrefs.indexOf(vehicle.operator.meta.href) === -1) {
            driversForMotorcycleAsOperatorsHrefs.push(vehicle.operator.meta.href);
          }
        }
      }
    });

    this.drivers.forEach((driver: Driver) => {
      const driverShouldHaveMotorcycle: boolean = driversForMotorcycleAsOperatorsHrefs.indexOf(driver.meta.href) !== - 1;

      if (driverShouldHaveMotorcycle) {
        // Do Nothing, the Driver should stay with motorcycle set to true
      } else {
        // Reset value if there is no values set for driver.motorcycleLicenseType
        if (driver.motorcycle && !driver.motorcycleLicenseType) {
          driver.motorcycle = false;

          // console.log('%c Reset MOTORCYCLE value for driver: ', 'color:red',  driver);

          this.apiCommonService.putByUri(driver.meta.href, driver).pipe(
            take(1))
            .subscribe(res => {
              this.storageService.updateDriversListSingleItem(this.quoteId, driver);
            });
        }
      }

    });
  }


  // Input text Actions
  // -------------------------------------------------------------------------------
  public actionOnVinFieldNgModelChange($event: any, inputRef: HTMLInputElement): void {
    const cursorPosition: number = inputRef.selectionStart;
    this.selectedVehicle.vin = $event.toUpperCase();

    // this.performVinLookup(this.selectedVehicle.vin);
    this.updateVehicleData(300);

    if (this.helperVehicleIsFromYearOrPrior(1984)) {
      // this.updateVehicleData(500);
      return;
    } else {
      this.performVinLookup(this.selectedVehicle.vin);
    }

    // Set cursor in the right position
    setTimeout(() => {
      if (!inputRef.setSelectionRange) {
        return;
      }

      inputRef.setSelectionRange(cursorPosition, cursorPosition);
    });
  }

  private performVinLookup(vinNumber: string): void {
    // Do not perform lookup if vehicle type 'Trailer' or 'Motorcycle'
    if (this.selectedVehicle.vehicleType === VEHICLE_TYPES.trailer || this.selectedVehicle.vehicleType === VEHICLE_TYPES.motorcycle) {
      return;
    }

    this.lookupGetVehicleDetailByVINAndUpdateVehicleData(vinNumber, 300)
      .catch(err => console.log(err));
  }

  public actionOnVinFieldBlur(): void {
    if (this.selectedVehicle.vehicleType === VEHICLE_TYPES.trailer || this.selectedVehicle.vehicleType === VEHICLE_TYPES.motorcycle) {
      this.updateVehicleData();
    } else {
      if (!this.selectedVehicle.vin || !this.vinValidate(this.selectedVehicle.vin)) {
        this.vinNumberLookupGeneralDetailsNotFound = false;
        this.helperSwitchFieldModelTypeAndRequiredFieldsRulesToTextInput(false, true);
        this.setToStorageVehicleGeneralDetailsHelper(null, this.selectedVehicle, false);
      }
    }
  }

  private checkbocCarrierModalHandler(): void {
    if (this.selectedVehicle.ignoreCarrierCityAndMiles) {
      this.selectedVehicle.ignoreCarrierCityAndMiles = !this.selectedVehicle.ignoreCarrierCityAndMiles;
      this.updateVehicleData();
    } else {
      this.checkbocCarrierModalIsOpen = true;
    }
  }

  private ignoreCarrierModalAccept($ev, field) {
    this.selectedVehicle.ignoreCarrierCityAndMiles = true;
    this.updateVehicleData();
  }

  private modalStateChange($ev) {
    this.checkbocCarrierModalIsOpen = $ev.isOpen;
  }

  // Methods used for additional components update
  // ------------------------------------------------------

  private processVehicleOptionsComponetUpdate(): void {
    this.selectedVehicleGeneralDetailsForOptionsUpdate = this.selectedVehicleDetails;
  }

  private processVehicleSymbolsComponentUpdate(): void {
    // this.selectedVehicle = <Vehicle>Object.assign({},this.selectedVehicle);
    if (this.symbolsComponent) {
      this.symbolsComponent.callRefreshSelectedVehicleData(this.selectedVehicle);
      this.selectedVehiclePlanSymbolsForSymbolsComponent = this.selectedVehiclePlanSymbols;
    }
  }



  // Events methods call in template
  // ------------------------------------------------------

  private onSelectFieldDataSelected($ev, propertyName): void {
    this.selectedVehicle[propertyName] = $ev.id;
    this.updateVehicleData();
  }

  private onVehicleLocationDataSelect($ev, propertyName): void {
    this.selectedVehicleLocationData[propertyName] = $ev.id;
    // this.manageLocationsOnDataChange();
    this.manageLocationsOnDataChangeTrigger();
  }
  /**
   * @returns Promise<boolean> - true if vehicle has been successfully updated, false if error occurred
   */
  private updateVehicleData(updateWithDelay: number = 0): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.updateVehicleSubscription && this.updateVehicleSubscription.unsubscribe();

      this.updateVehicleSubscription = observableTimer(updateWithDelay).pipe(
        switchMap(() => this.vehicleService.updateVehicle(this.selectedVehicle.parentId, this.selectedVehicle.resourceId, this.selectedVehicle)),
        take(1), )
        .subscribe(
          res => {
            console.log('Vehicle update', res);
            resolve(true);
          },
          err => {
            console.log(err);
            resolve(false);
          }
        );
    });
  }

  /**
   * @returns Promise<boolean> - true if location has been successfully updated, false if error occurred
   */
  private updateVehicleLocationData(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.locationsService.updateLocationByUri(this.selectedVehicleLocationData.meta.href, this.selectedVehicleLocationData).pipe(
        take(1))
        .subscribe(
          res => {
            console.log('Location update:', res);
            // let tmpLocatonDataForVehicle = this.vehicleService.convertLocationToLocationForVehicle(res, this.selectedVehicle);
            // this.storageService.updateVehicleLocationsForVehiclesSingle(tmpLocatonDataForVehicle);
            this.updateVehicleLocationDataForVehicle();
            resolve(true);
          },
          err => {
            console.log(err);
            resolve(false);
          }
        );
    });
  }

  public updateVehicleLocationDataForVehicle(): void {
    const tmpLocatonDataForVehicle = this.vehicleService.convertLocationToLocationForVehicle(this.selectedVehicleLocationData, this.selectedVehicle);
    this.storageService.updateVehiclesLocationsForVehiclesSingleItem(tmpLocatonDataForVehicle);
  }

  public onVehicleOptionsUpdate($ev): void {
    this.selectedVehicle.options = Object.assign({}, $ev.options);
    this.updateVehicleData();
  }

  public onVehicleSymbolsUpdated($ev): void {
    // console.log('Symbols updated', $ev);
    this.selectedVehicle.symbols = $ev.symbols;
    this.selectedVehicle.priceValue = $ev.priceValue;
    this.updateVehicleData();
  }


  public openSymbolsModalbox(ev): void {
    ev && ev.preventDefault();
    // Force update selectedVehicle in symbols component
    // this.selectedVehicle = <Vehicle>Object.assign({}, this.selectedVehicle);
    this.symbolsComponent.callRefreshSelectedVehicleData(this.selectedVehicle);
  }

  // Actions above the form detail (Add Vehicle, Delete Vehicle, Rmv Lookup)
  // -------------------------------------------------------------------------------

  private confirmAcceptedDelete(data) {
    this.deleteSelectedVehicle();
  }

  private confirmCanceledDelete(data) {
    console.log(data);
  }

  private addNewVehicle($ev?: Event): void {
    $ev && $ev.preventDefault();

    this.overlayLoaderService.showLoader();

    this.vehicleService.createVehicle(this.quoteId, {}, 'private', false)
      .subscribe(
        res => {


          this.overlayLoaderService.showLoader();
          this.coveragesService.createAutoStandardAndAdditionalCoveragesForVehicle(res)
            .then(() => {
              this.storageService.updateVehiclesListSingleItem(res.parentId, res);
              this.addNewVehicleSuccess(res);
              this.overlayLoaderService.hideLoader();
              this.overlayLoaderService.hideLoader();
            })
            .catch(err => {
              console.log(err);
              this.overlayLoaderService.hideLoader();
              this.overlayLoaderService.hideLoader();
            });
        },
        err => console.log(err)
      );
  }

  private addNewVehicleSuccess(response: Vehicle): void {
    if (response && response.meta) {
      this.router.navigate(['/dashboard/auto/' + response.meta.href]);
    }
  }


  private deleteSelectedVehicle($ev?: Event): void {
    $ev && $ev.preventDefault();
    const tmpVehicleId = this.vehicleId;

    this.overlayLoaderService.showLoader();
    this.vehicleService.deleteVehicleById(this.quoteId, this.vehicleId)
      .subscribe(
        res => {
          const sameGarageCount = this.vehicles.filter(x => x.garagingAddress.meta.href === this.selectedVehicle.garagingAddress.meta.href);
        const sameAsClient = this.selectedVehicleLocationData.address1?.toLowerCase() === this.clientAddress.address1?.toLowerCase();
          if (sameGarageCount.length === 0 && !sameAsClient && this.selectedVehicleLocationData.address1) {
            this.locationsService.deleteLocationByUri(this.selectedVehicle.garagingAddress.meta.href).subscribe();
            this.storageService.deleteQuoteLocationsSingleItem(this.selectedVehicleLocationData);

          }

          // TODO:: REMOVE EXLUSIONS (vehicles) from driver
          this.removeDeletedExcludedVehiclesOnDrivers(this.selectedVehicle);
          // this.selectedVehicle

          this.deleteVehicleSuccess(res);
          this.overlayLoaderService.hideLoader();
        },
        err => console.log('vehicle delete error: ', err)
      );
  }

  private removeDeletedExcludedVehiclesOnDrivers(vehicle: Vehicle): void {
    this.drivers.forEach(driver => {
      if (driver.exclusions && driver.exclusions.length) {
        const exclusionToRemoveIndex = driver.exclusions.findIndex(el => el.meta.href === vehicle.meta.href);

        if (exclusionToRemoveIndex !== -1) {
          driver.exclusions.splice(exclusionToRemoveIndex, 1);

          this.apiCommonService.putByUri(driver.meta.href, driver).pipe(
            take(1))
            .subscribe(res => {
              this.storageService.updateDriversListSingleItem(this.quoteId, driver);
            });
        }
      }
    });
  }

  private deleteVehicleSuccess(res): void {
    if (this.vehicles[0]) {
      this.selectedVehicle = this.vehicles[0];
      console.log(this.selectedVehicle);
      // this.router.navigate(['/dashboard/' + this.vehicles[0].meta.href]);
      this.router.navigate(['/dashboard/auto' + this.vehicles[0].meta.href]);
    } else {
      // this.router.navigate(['/dashboard/quotes/' + this.quoteId + '/vehicles']);
      this.router.navigate(['/dashboard/auto/quotes/' + this.quoteId + '/vehicles']);
    }
  }

  private openRMVlookup(modalRef) {
    this.rmvLookupVinNumber = this.selectedVehicle.vin;
    this.rmvLookupPlateNumber = this.selectedVehicle.licensePlate;
    this.rmvLookupPlateType = 'PAN';
    modalRef.openModalbox();
  }

  private rmvHandleLookupButtonAction($ev, refModalbox) {
    $ev.preventDefault();

    switch (this.rmvLookupFormSelected) {
      case 'vin':
        // this.lookupGetVehicleDetailByVIN(this.rmvLookupVinNumber);
        refModalbox.closeModalbox();
        this.rmvHandleVinLookup(this.rmvLookupVinNumber);
        break;
      case 'plate':
        refModalbox.closeModalbox();
        this.rmvHandlePlateLookup(this.rmvLookupPlateNumber, this.rmvLookupPlateType);
        break;
    }

    if (this.rmvLookupInvalidVIN) {
      return;
    }

    refModalbox && refModalbox.closeModalbox();
  }

  private rmvHandleVinLookup(vin: string): void {
    if (vin) {
      vin = vin.toUpperCase();
    }

    if (vin.length === 17 && !this.vinValidate(vin)) {
      this.rmvLookupInvalidVIN = true;
      return;
    }
    if (vin.length < 11 && vin.length > 18) {
      this.rmvLookupInvalidVIN = true;
      return;
    }

    this.rmvLookupInvalidVIN = false;

    this.RMVlookupVehicleByVIN(vin).catch(err => console.log('RMV Lookup By VIN ERROR:', err));
  }

  private rmvHandlePlateLookup(plate: string, plateType: string): void {
    // if (!this.plateValidate(plate)) {
    //   this.rmvLookupInvalidPlate = true;
    //   return;
    // }

    this.rmvLookupInvalidPlate = false;

    this.RMVlookupVehicleByPlate(plate, plateType)
      .then()
      .catch(err => console.log('RMV LOOKUP VEHICLE BY PLATE ERROR:', err));
  }

  public setRmvFormType(type: string) {
    this.rmvLookupFormSelected = type;
  }

  public rmvLookupVinNumberOnChange($event: any, inputRef: HTMLInputElement): void {
    const cursorPosition: number = inputRef.selectionStart;
    this.rmvLookupVinNumber = $event.toUpperCase();

    // Set cursor in the right position
    setTimeout(() => {
      if (!inputRef.setSelectionRange) {
        return;
      }

      inputRef.setSelectionRange(cursorPosition, cursorPosition);
    });
  }

  public rmvResetVinValidationStatus($ev?: Event): void {
    this.rmvLookupInvalidVIN = false;
  }

  public rmvResetPlateValidationStatus($ev?: Event): void {
    this.rmvLookupInvalidPlate = false;
  }

  public rmvLookupModalStateChange(data: IModalStateChange): void {
    if (data.isOpen === false) {
      this.setRmvFormType('plate');
      console.log(data);
    }
  }


  // Validation Methods
  // ---------------------------------------------------------------------------

  // VIN Validation
  public vinValidate(vin) {

    const validVin = this.vehicleService.vinValidate(vin);
    const validPartialVin = (vin && vin.length === 10);

    this.rmvLookupInvalidVIN = !validVin;
    if (validPartialVin) {
      this.rmvLookupInvalidVIN = false;
    }

    return validVin || validPartialVin;
  }

  checkVinValue(event) {
    const vin = event.getData('Text').trim();
    return this.vinValidate(vin);
  }

  public vinValidatePartial(vin) {
    let validVin = false;
    if (vin.length === 17) {
      validVin = this.vehicleService.vinValidate(vin);
    } else if (vin.length > 10 && vin.length < 18) {
      validVin = true;
    }
    this.rmvLookupInvalidVIN = !validVin;
    return validVin ;
  }

  keyPressAlphanumeric(event) {
    const inp = String.fromCharCode(event.keyCode);
    if (/[a-zA-Z0-9&]/.test(inp)) {
      return true;
    } else {
      event.preventDefault();
      return false;
    }
  }

  public vinValidateIfNotEmpty(vin) {
    if (!vin) {
      return true;
    } else {
      return this.vinValidate(vin);
    }
  }

  private plateValidate(plate) {
    const validPlate = this.vehicleService.plateValidate(plate);
    this.rmvLookupInvalidPlate = !validPlate;

    return validPlate;
  }

  public validateIsRequiredVinField(vin: string, refVinInputModel: NgModel) {
    // if (this.selectedVehicle.vehicleType === VEHICLE_TYPES.trailer || this.selectedVehicle.vehicleType === VEHICLE_TYPES.motorcycle) {
    //   return false;
    // } else {
    //   return this.fieldVinInvalid || !this.vinValidateIfNotEmpty(vin) || refVinInputModel.invalid;
    // }
    if (this.selectedVehicle.vehicleType === VEHICLE_TYPES.trailer || this.selectedVehicle.vehicleType === VEHICLE_TYPES.motorcycle) {
      return false;
    } else if (this.helperVehicleIsFromYearOrPrior(1984)) {
      return (!vin) ? true : false;
    } else {
      return this.fieldVinInvalid || !this.vinValidateIfNotEmpty(vin) || refVinInputModel.invalid;
    }

  }

  public vinIsRequiredIfMotorcycle() {
    const requiredForPlans = ['291'];
    return Validate.isRequiredForSelectedPlansIfEmptyValue(
      this.selectedVehicle.vin,
      requiredForPlans,
      this.quoteSelectedPlansIds
    );
  }

  // JHMFA1F6&A123412

  private validateisRequiredVehicleLocationGaragingAddress(): boolean {
    // Required for:
    // Hanover, Travelers, National General, Green Mountain, Preffered New, Safeco, Hanover New
    const requiredForPlans = ['20', '27', '172', '21', '29', '25', '281', '291', '312'];
    return Validate.isRequiredForSelectedPlansIfEmptyValue(
      this.selectedVehicleLocationData.address1,
      requiredForPlans,
      this.quoteSelectedPlansIds
    );
  }

  private validateisRequiredVehicleLocationZip(): boolean {
    // Required for:
    // Hanover, Travelers, National General, Harleysville, MetLife, Norfolk and Dedham, Hanover New
    // Preferred New, Progressive, Safeco
    const requiredForPlans = ['20', '27', '172', '16', '24', '14', '29', '26', '25', '281', '312', '315', '318'];
    return Validate.isRequiredForSelectedPlansIfEmptyValue(
      this.selectedVehicleLocationData.zip,
      requiredForPlans,
      this.quoteSelectedPlansIds
    );
  }

  public validateIsRequiredVehicleModel(): boolean {
    return this.selectedVehicle.vehicleType !== VEHICLE_TYPES.trailer && this.selectedVehicle.vehicleType !== VEHICLE_TYPES.motorcycle;
  }

  // Helpers
  // -------------------------------------------------------------------------------
  private checkIfBodyStyleFieldRequiredBasedOnSelectedPlans(selectedPlans: QuotePlan[]): boolean {
    let required = false;
    const requireForPlans  = REQUIRED_PLANS_SYMBOLS_VRG;
    // let requireForPlans = [
    //   { ratingPlanId: '18', name: 'Quincy Mutual Group' },
    //   { ratingPlanId: '2', name: 'MAIP (CAR)' },
    //   { ratingPlanId: '4', name: 'Commerce' },
    // ];

    for (const plan of requireForPlans) {
      const exists = selectedPlans.findIndex(selectedPlan => selectedPlan.ratingPlanId === plan.ratingPlanId);
      if (exists > -1) {
        required = true;
        break;
      }
    }
    return required;
  }

  // public isBodyStyleRequired(): boolean {
  //   return this.vehiclesSymbolsAutomanagerService.requiredVehicleBodyStyle(this.selectedVehicle.resourceId);
  // }

  // Help switch Model field type between text input and select input
  private helperSwitchFieldModelTypeAndRequiredFieldsRulesToTextInput(isTextInput: boolean , forceVinRequired: boolean = false, doNotChangeModelInputType: boolean = false): void {
    if (isTextInput) {
      if (!doNotChangeModelInputType) {
        this.fieldModelSwitchToText = true;
      }
      // this.fieldModelSwitchToText = true;
      this.fieldTrimSwitchToText = true;
      this.fieldVinRequired = false;
      this.fieldBodyStyleDisabled = false;
    } else {
      if (!doNotChangeModelInputType) {
        this.fieldModelSwitchToText = false;
      }
      // this.fieldModelSwitchToText = false;
      this.fieldTrimSwitchToText = false;
      this.fieldVinRequired = true;
      this.fieldBodyStyleDisabled = (this.selectedVehicle.bodyStyle) ? true : false;
    }

    if (forceVinRequired) {
      this.fieldVinRequired = true;
    }
  }

  // Return formated neme of the Vehicle
  public vehicleNameToDisplay(vehicle: Vehicle): string {
    return this.vehicleService.vehicleNameToDisplay(vehicle);
  }

  // Convert Driver data to option
  private helperDriverDataToFilterOption(driver: Driver, index: number, arr: Driver[]): FilterOption {
    const option = new FilterOption();
    const firstName = driver.firstName || '';
    const lastName = driver.lastName || '';
    let additionalData = '';
    let moreDriversWithTheSameName = false;


    // Check up if there is more than one driver is with the same name
    const driverWithTheSameNames: Driver[] = arr.filter((tmpDriver: Driver) => {
      return driver.firstName === tmpDriver.firstName && driver.lastName === tmpDriver.lastName;
    });

    if (driverWithTheSameNames && driverWithTheSameNames.length > 1) {
      moreDriversWithTheSameName = true;
    }

    if (moreDriversWithTheSameName && driver.dateOfBirth) {
      const [year, month, day] = driver.dateOfBirth.split('-');
      additionalData = (year) ? ` (${year})` : '';
    }

    option.id = firstName + ' ' + lastName + additionalData;
    option.text = firstName + ' ' + lastName + additionalData;
    option.data = driver;

    if (!firstName && !lastName) {
      option.text = 'New Driver';
    }

    return option;
  }

  // Parse driver meta data to option
  public parseOperatorOwnerDataToOption(selectedData, optionsList: FilterOption[]): FilterOption|undefined {
    const properOption = optionsList.find(option => {
      return option.data && selectedData && option.data.meta.href === selectedData.meta.href;
    });

    return properOption || undefined; // new FilterOption();
  }


  // Generate years options
  private helperGenerateYearsOptions(): string[] {
    const date = new Date();
    const endDate = date.getFullYear() + 1;
    const starDate = 1940;
    const range = endDate - starDate;
    const arrOptions = [];

    for (let i = 0; i <= range; i++) {
      const year = endDate - i;
      arrOptions.push(year + '');
    }

    return arrOptions;
  }


  // Eliminate duplicated results by concatenated string description fields
  // https://bostonsoftware.atlassian.net/wiki/display/CON/Vehicle+Lookup+in+SPR+API
  private helperProccessTrimsOptions(arrDetails: VehicleGeneralDetails[]): FilterOptionTrim[] {
    const optionsTrims: FilterOptionTrim[] = [];


    arrDetails.forEach(generalDetail => {
      const tmpFullDescription = this.vehicleService.concatVehicleDetailDescriptionFields(generalDetail);

      if (optionsTrims.findIndex(option => option.data.uqId === tmpFullDescription) === -1) {
        const tmpOption = this.helperCreateVehicleTrimOption(tmpFullDescription, generalDetail);
        optionsTrims.push(tmpOption);
      }
    });

    return optionsTrims;
  }

  private helperCreateVehicleTrimOption(optionConcatenatedDescription: string, generalDetail: VehicleGeneralDetails): FilterOptionTrim {
    const newOption = new FilterOptionTrim();
    const additionalData: filterOptionAdditionalDataInterface = {uqId: optionConcatenatedDescription, generalDetail: generalDetail};

    newOption.id = optionConcatenatedDescription; // generalDetail.detail.bodyStyle;
    newOption.text = optionConcatenatedDescription;
    newOption.data = additionalData;

    return newOption;
  }

  // From API: 4-Door Sedan 2.4L 4 -Cylinder Premium Package
  // GENERATED: 4-Door Sedan 4 -Cylinder 2.4L Premium Package

  // Convert Towns objects to Cities options
  private helperTownsToCitiesOptions(townsList: Town[]): FilterOption[] {
    let options: FilterOption[] = [];

    options = townsList.map(town => {
      const option = new FilterOption();
      option.id = town.townName;
      option.text = town.townName;
      option.data = town;

      return option;
    });

    return options;
  }

  private helperGetRatingPlansIds(plans: QuotePlan[]): string[] {
    return plans.map(plan => plan.ratingPlanId);
  }

  public helpCheckIfPriceValueVieldIsRequired(isRequired: boolean) {
    this.timerCheckPriceValueRequire && clearTimeout(this.timerCheckPriceValueRequire);

    this.timerCheckPriceValueRequire = setTimeout(() => {
      if (this.selectedVehicle.vehicleType === VEHICLE_TYPES.trailer || this.selectedVehicle.vehicleType === VEHICLE_TYPES.motorcycle) {
        this.fieldPriceNewValueRequired = true;
      } else {
        this.fieldPriceNewValueRequired = isRequired || false;
      }
    }, 1);
  }

  // filter - numbers only
  public numbersOnly(event) {
    const digits = /^[0-9]{1}$/;
    const multiKey = event.ctrlKey || event.metaKey;
    const keyNormalized = (event && event.key) ? event.key.toLocaleLowerCase() : '';

    if (!(
      keyNormalized === 'backspace' ||
      keyNormalized === 'delete' ||
      keyNormalized === 'tab' ||
      keyNormalized === 'arrowleft' ||
      keyNormalized === 'left' ||
      keyNormalized === 'arrowright' ||
      keyNormalized === 'right' ||
      keyNormalized === 'a' && multiKey ||
      keyNormalized === 'z' && multiKey ||
      keyNormalized === 'c' && multiKey ||
      keyNormalized === 'v' && multiKey ||
      digits.test(event.key)
    )) { event.preventDefault(); }
  }

  private initClientAddressUpdating(): void {
    this.subscribeNewRmvQuote();
    this.subscribeClients();
    this.subscribeClientAddresses();
  }

  private destroyClientAddressUpdating(): void {
    this.subscriptionNewRmvQuote && this.subscriptionNewRmvQuote.unsubscribe();
    this.subscriptionClients && this.subscriptionClients.unsubscribe();
    this.subscriptionClientAddresses && this.subscriptionClientAddresses.unsubscribe();
  }

  private subscribeNewRmvQuote(): void {
    this.subscriptionNewRmvQuote && this.subscriptionNewRmvQuote.unsubscribe();
    this.subscriptionNewRmvQuote = this.storageService.getStorageData('newRmvQuoteData')
      .subscribe(res => {
        if (res) {
          this.isQuoteCreatedByRmv = true;
        } else {
          this.isQuoteCreatedByRmv = false;
        }

        this.isAllowedToCopyAddress();
      });
  }

  private subscribeClients(): void {
    let localClients = [];
    this.subscriptionClients && this.subscriptionClients.unsubscribe();
    this.subscriptionClients = this.storageService.getStorageData('clients').subscribe(clients => {
      if (clients.length && clients[0].addresses !== undefined && clients[0].addresses.href) {
        localClients = clients;
        this.clientDetails = JSON.parse(JSON.stringify(localClients[0]));
      } else {
        this.clientsService.getClientsList(this.quoteId).pipe(
          take(1))
          .subscribe(clients => {
            localClients = clients.items;
            this.clientDetails = JSON.parse(JSON.stringify(localClients[0]));
          });
      }
      this.isAllowedToCopyAddress();
    });
  }

  private subscribeClientAddresses(): void {
    this.subscriptionClientAddresses && this.subscriptionClientAddresses.unsubscribe();
    this.subscriptionClientAddresses = this.storageService.getStorageData('clientAddresses')
      .subscribe((res: ClientAddress[]) => {
        const foundAddress = res.find(el => el.addressType === 'StreetAddress');

        if (foundAddress) {
          this.clientAddress = foundAddress;
        }

        this.isAllowedToCopyAddress();
      });
  }

  private isAllowedToCopyAddress(): boolean {
    let selectedVehicleWithSourceLocation = false;
    let clientAddressNotSet = false;
    let vehicleAddressNotSet = false;
    const quoteCreatedByRMV: boolean = this.isQuoteCreatedByRmv; // false;

    const firstVehicle = JSON.parse(JSON.stringify(this.vehicles[0]));
    // let addressOfFirstVehicle = this.quoteLocations.find(item => item.meta.href === firstVehicle.garagingAddress.meta.href);
    let addressOfFirstVehicle: LocationData;

    if (firstVehicle && firstVehicle.garagingAddress && firstVehicle.garagingAddress.meta && firstVehicle.garagingAddress.meta.href) {
      addressOfFirstVehicle = this.quoteLocations.find(item => item.meta.href === firstVehicle.garagingAddress.meta.href);
    }

    if (this.selectedVehicle.resourceId && this.selectedVehicle.resourceId === firstVehicle.resourceId) {
      selectedVehicleWithSourceLocation = true;
    }

    if (this.clientAddress
      && !this.clientAddress.address1
      && !this.clientAddress.address2
      && !this.clientAddress.city
      && !this.clientAddress.state
      && !this.clientAddress.zip) {
        clientAddressNotSet = true;
      }

    if (addressOfFirstVehicle
      && !addressOfFirstVehicle.address1
      && !addressOfFirstVehicle.address2
      && !addressOfFirstVehicle.city
      // && !addressOfFirstVehicle.state
      && !addressOfFirstVehicle.zip) {
        vehicleAddressNotSet = true;
    }

    // this.isAllowedToUpdateClientAddress is set to true after client addresses in storage are updated if before update this.isAllowedToUpdateClientAddress was set to true;
    this.isAllowedToUpdateClientAddress = !quoteCreatedByRMV && selectedVehicleWithSourceLocation && clientAddressNotSet && vehicleAddressNotSet;
    return this.isAllowedToUpdateClientAddress;
  }

  private updateClientAddress(): void {
    if (this.isAllowedToUpdateClientAddress) {
      const clonedClientAddress: ClientAddress = JSON.parse(JSON.stringify(this.clientAddress));

      clonedClientAddress.address1 = this.selectedVehicleLocationData.address1;
      clonedClientAddress.address2 = this.selectedVehicleLocationData.address2;
      clonedClientAddress.city = this.selectedVehicleLocationData.city;
      clonedClientAddress.state = this.selectedVehicleLocationData.state;
      clonedClientAddress.zip = this.selectedVehicleLocationData.zip;

      if (clonedClientAddress.meta.href) {
        this.apiCommonService.putByUri(clonedClientAddress.meta.href, clonedClientAddress)
          .toPromise()
          .then(res => {
            // console.log('UPDATED ADDR:', res)
            this.storageService.updateClientAddressesSingleItem(res);
            this.isAllowedToUpdateClientAddress = true; // The updating data was allowed, so let it be still allowed;
          })
          .catch(err => console.log('UPDATE ADDR ERROR:', err, clonedClientAddress));
      }
    }
  }

  private switchFormForVehicleType(vehicleType: string): void {

    this.fieldMakeDisabled = true;
    this.fieldModelDisabled = true;
    this.fieldTrimDisabled = true;
    this.fieldBodyStyleDisabled = false;
    this.fieldVinInvalid = false;
    this.fieldPriceNewValueRequired = true;
    this.fieldAnnualMilesIsRequired = true;

    switch (vehicleType) {
      case VEHICLE_TYPES.privatePassenger:
        this.switchToPrivatePassenger();
        break;
      case VEHICLE_TYPES.pickup:
        this.switchToPickup();
        break;
      case VEHICLE_TYPES.motorcycle:
        this.switchToMotorcycle();
        break;
      case VEHICLE_TYPES.trailer:
        this.switchToTrailer();
        break;
      default:
        this.switchToPrivatePassenger();
    }
  }

  private switchToPrivatePassenger(): void {
    this.fieldTrimIsHidden = false;
    this.fieldBodyStyleIsHidden = false;
    this.fieldSymbolsIsHidden = false;
    this.componentVehicleOptionsIsHidden = false;
    this.fieldCcDisplacementIsHidden = true;

    this.fieldVinRequired = true;
    this.fieldTrimSwitchToText = false;
  }

  private switchToPickup(): void {
    this.fieldTrimIsHidden = false;
    this.fieldBodyStyleIsHidden = false;
    this.fieldSymbolsIsHidden = false;
    this.componentVehicleOptionsIsHidden = false;
    this.fieldCcDisplacementIsHidden = true;

    this.fieldVinRequired = true;
    this.fieldTrimSwitchToText = false;
  }

  private switchToMotorcycle(): void {
    this.fieldTrimIsHidden = true;
    this.fieldBodyStyleIsHidden = true;
    this.fieldSymbolsIsHidden = true;
    this.componentVehicleOptionsIsHidden = false;
    this.fieldCcDisplacementIsHidden = false;
  }

  private switchToTrailer(): void {
    this.fieldModelDisabled = false;
    this.fieldMakeDisabled = true;
    this.fieldPriceNewValueRequired = true;
    this.fieldVinRequired = false;
    this.fieldAnnualMilesIsRequired = false;

    this.fieldTrimIsHidden = true;
    this.fieldBodyStyleIsHidden = true;
    this.fieldSymbolsIsHidden = true;
    this.componentVehicleOptionsIsHidden = true;
    this.fieldCcDisplacementIsHidden = true;

    this.selectedVehicle.make = 'Trailer';
  }

  // public get vinFieldIsRequired(): boolean {
  //   return this.fieldVinRequired && (this.selectedVehicle.year && Number(this.selectedVehicle.year) <= 1984);
  // }

  private helperVehicleIsFromYearOrPrior(year: number): boolean {
    let result = false;

    if (this.selectedVehicle.year) {
      const vehicleYear: number = Number(this.selectedVehicle.year);
      if (!isNaN(vehicleYear)) {
        result = vehicleYear <= year;
      }
    }

    return result;
  }

  // Validations
  // ---------------------------------------------------------------------------
  public isInvalidMakeField(): boolean {
    let wrongOptionSet = false;

    if (this.selectedVehicle && this.selectedVehicle.make && this.optionsMake && this.optionsMake.length) {
      const selectedOptionInOptions: FilterOption = this.optionsMake
        .find((o: FilterOption) => o.id === this.selectedVehicle.make);

      wrongOptionSet = selectedOptionInOptions ? false : true;
    }

    return !this.selectedVehicle.make || wrongOptionSet;
  }

  public isInvalidAnnualMiles(value: string): boolean {
    let result = false;
    const annualMilesValue: number = Number(value);

    if (isNaN(annualMilesValue) || !value && this.fieldAnnualMilesIsRequired) {
      result = true;
    } else if (annualMilesValue >= 99999) {
      result = true;
    }

    return result;
  }

  // https://bostonsoftware.atlassian.net/browse/SPR-2649
  public fieldCityIsRequired(): boolean {
    let wrongValueSetForCity = false;
    if (this.selectedVehicleLocationData && this.selectedVehicleLocationData.city && this.optionsCity) {
      const townsListNames: string[] = this.optionsCity.map((o: FilterOption) => o.id);
      const townAvailableOnTheList = townsListNames.includes(this.selectedVehicleLocationData.city);
      wrongValueSetForCity = !townAvailableOnTheList;
    }

    return wrongValueSetForCity || Validate.isEmptyValue(this.selectedVehicleLocationData.city);
  }

  // https://bostonsoftware.atlassian.net/browse/SPR-2929
  private checkIfVehicleModelIsInManualMode(): void {
    console.log('Check if Vehicle Model in manual mode');
    // fieldTrimSwitchToText

    if (this.optionsModel && this.optionsModel.length) {
      const vehicleModelMatchAvailableOptions = this.optionsModel.find(opt => {
        const optionId: string = opt && opt.id ? opt.id.toUpperCase() : '';
        const vehicleModeUpper: string = this.selectedVehicle.model ? this.selectedVehicle.model.toUpperCase() : this.selectedVehicle.model;
        return optionId === vehicleModeUpper;
      });

      if (!vehicleModelMatchAvailableOptions && this.selectedVehicle.model) {
        this.vehicleModelIsInManualMode = true;
      } else {
        this.vehicleModelIsInManualMode = false;
      }

      this.helperSwitchFieldModelTypeAndRequiredFieldsRulesToTextInput(this.vehicleModelIsInManualMode, false, true);
    }
  }

  // Modal Edit Vehicle Model Manually
  // ---------------------------------------------------------------------------
  public modalEditModelManuallyOpen(): void {
    this.refModalEditManuallyModel && this.refModalEditManuallyModel.open();
  }

  public modalEditModelManuallyContinue(): void {
    this.helperSwitchFieldModelTypeAndRequiredFieldsRulesToTextInput(true);
    this.refModalEditManuallyModel && this.refModalEditManuallyModel.close();

    setTimeout(() => {
      if (this.refVehicleModelTextInput && this.refVehicleModelTextInput.nativeElement) {
        this.selectedVehicle.model = '';
        this.refVehicleModelTextInput.nativeElement.focus();
      }
    }, 10);
  }

  public modalEditModelManuallyCancel(): void {
    this.refModalEditManuallyModel && this.refModalEditManuallyModel.close();
  }

  // Vin Lookup - No General Details found for the Vehicle
  // ---------------------------------------------------------------------------
  private handleVinLookupNoVehicleGeneralDetails(data: VehicleGeneralDetails, byMethod: string = ''): void {
    this.vinNumberLookupGeneralDetailsNotFound = false;
    if (!data) {
      // console.log(`%c VIN Lookup - No General Details Found: ${byMethod}`, 'color:#c59600');
      this.vinNumberLookupGeneralDetailsNotFound = true;
      this.helperSwitchFieldModelTypeAndRequiredFieldsRulesToTextInput(true, true);
    }

    this.setToStorageVehicleGeneralDetailsHelper(data, this.selectedVehicle);
  }

  private setToStorageVehicleGeneralDetailsHelper(data: VehicleGeneralDetails, vehicle: Vehicle, returnedEmptyVehicleGeneralDetailsForceValue: boolean = null): void {
    const dataToUpdate: VehicleGeneralDetailsHelper = new VehicleGeneralDetailsHelper();
    dataToUpdate.vehicleResourceId = vehicle.resourceId;
    dataToUpdate.vehicleGeneralDetails = data;
    dataToUpdate.validVinNumber = (vehicle.vin) ? this.vehicleService.vinValidate(vehicle.vin) : false;
    dataToUpdate.validPartialVinNumber = (vehicle.vin && typeof vehicle.vin === 'string' && vehicle.vin.length === 10) ? true : false;
    // dataToUpdate.returnedEmptyVehicleGeneralDetails = (!data) ? true : false;

    if (returnedEmptyVehicleGeneralDetailsForceValue !== null && typeof returnedEmptyVehicleGeneralDetailsForceValue === 'boolean') {
      dataToUpdate.returnedEmptyVehicleGeneralDetails = returnedEmptyVehicleGeneralDetailsForceValue;
    } else {
      dataToUpdate.returnedEmptyVehicleGeneralDetails = (!data) ? true : false;
    }

    this.storageService.updateVehicleGeneralDetailsHelperSingleItem(dataToUpdate);
  }

  saveReorder(modal) {
    let index = 1;
    this.vehicles.forEach(veh => {
      veh.orderIndex = index;
      index++;
      this.vehicleService.updateVehicleByUri(veh.meta.href, veh, true).subscribe();
    });
    setTimeout(() => {this.storageService.setStorageData('vehiclesList', this.vehicles); }, 1000);
 modal.closeModalbox();
   }


}
