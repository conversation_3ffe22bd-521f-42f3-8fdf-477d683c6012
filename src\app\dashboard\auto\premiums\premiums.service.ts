import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { Injectable } from '@angular/core';

import { BehaviorSubject, Observable } from 'rxjs';
import { StorageService } from 'app/shared/services/storage-new.service';
import { SymbolsService } from 'app/dashboard/app-services/symbols.service';

@Injectable()
export class PremiumsService {
  public tmpQuoteData;

  public tmpQuoteGeneralData;
  public tmpQuotePlanData;

  private storageSubscription;
  private quoteIdSubscription;
  private generalDataRerateSubscription;
  public rates;

  public loadingDataInProgress = false;

  private currentRateIndex = 0;
  private _currentRateIndex: BehaviorSubject<any> = new BehaviorSubject(this.currentRateIndex);

  public planSummary;
  quote: any;

  constructor(
    private storageService: StorageService,
    private symbolsService: SymbolsService,
    private quotesService: QuotesService
  ) {
    this.getRatesAndRerate();
  }

  ngOnDestroy() {
    this.closeStorageSubscriptionAndRerate();
    this.quoteIdSubscription && this.quoteIdSubscription.unsubscribe();
  }

  public getRatesAndRerate() {
    this.getQuoteId();
    this.getRates();
    this.rerateGeneral();
  }

  public closeStorageSubscriptionAndRerate(loadingQuote: boolean = false) {
    this.storageSubscription && this.storageSubscription.unsubscribe();
    this.generalDataRerateSubscription && this.generalDataRerateSubscription.unsubscribe();
    if (loadingQuote) {
      this.rates = null;
      this.storageService.setStorageData('rates', this.rates);
    }
  }

  public addPlanRerate(carrierName: string = ''): void {
    if (carrierName) {
      const carrierNameLower = carrierName.toLowerCase();
      if (this.rates && this.rates.length) {
        if (carrierNameLower && carrierNameLower !== 'general') {
          this.rates.forEach( rate => {
            if (rate && rate.items && rate.items.length) {
              rate.items.forEach( singleRate => {
                if ((singleRate && singleRate.planName && singleRate.planName.toLowerCase() === carrierNameLower)
                    || (singleRate && singleRate.planName && singleRate.planName.toLowerCase() === carrierNameLower)
                    || (singleRate && singleRate.planName && singleRate.planName.toLowerCase().indexOf(carrierNameLower) > -1)) {
                  singleRate.rerate = true;
                  singleRate.rate = null;
                  singleRate.review = null;
                  singleRate.premium = null;

                  this.quotesService.updateRatedCarrierInfoByCarrierName(this.quote.resourceId, carrierNameLower);
                }
              });
            }
          });
        } else {
          this.rerateAll();
        }
        this.storageService.setStorageData('rates', this.rates);
      }
    }
  }

  public rerateById(ids: Array<string> = []) {
    if (this.rates && this.rates.length && ids.length) {
      this.rates.map( rate => {
        if (rate && rate.items && rate.items.length) {
          rate.items.forEach( singleRate => {
            ids.forEach(id => {
              if (singleRate && singleRate.ratingPlanId.toString() === id) {
                singleRate.rerate = true;
                singleRate.rate = null;
                singleRate.review = null;
                singleRate.premium = null;
                this.quotesService.updateRatedCarrierInfoByCarrierId(this.quote.resourceId, singleRate.planName);

              }
            });
          });
        }
        return rate;
      });
      this.storageService.setStorageData('rates', this.rates);
    }
  }

  public getRates() {
    this.storageSubscription = this.storageService.getStorageData('rates').subscribe( res => {
      if (res && res.length) {
        this.rates = JSON.parse(JSON.stringify(res));
      }
    });
  }

  public getQuoteId() {
    this.quoteIdSubscription = this.storageService.getStorageData('selectedQuote').subscribe( res => {
      if (res) {
        this.quote = JSON.parse(JSON.stringify(res));
      }
    });

  }

  public rerateAll() {
    if (this.rates && this.rates.length) {
      this.rates.forEach( rate => {
        if (rate && rate.items && rate.items.length) {
          rate.items.forEach( singleRate => {
            singleRate.rerate = true;
            singleRate.rate = null;
            singleRate.review = null;
            singleRate.premium = null;


          });
          this.quotesService.updateRatedCarrierInfoAll(this.quote.resourceId);
        }
      });
      this.storageService.setStorageData('rates', this.rates);
    }
  }

  public rerateGeneral(url: string = '') {
    if (!url) {
      url = window.location.href;
    }
    this.generalDataRerateSubscription && this.generalDataRerateSubscription.unsubscribe();
    if (url.indexOf('/auto/') > -1) {
      this.generalDataRerateSubscription = this.storageService.generalData$.subscribe( data => {
        setTimeout( () => {
          let dataString = JSON.stringify(data);
          const dataJson = JSON.parse(dataString);
          dataJson.vehicles = this.symbolsService.orderVehiclesSymbols(dataJson.vehicles);
          dataString = JSON.stringify(dataJson);
          if (this.tmpQuoteGeneralData && this.tmpQuoteGeneralData !== dataString) {
            this.rerateAll();
            this.storageService.setStorageData('rates', this.rates);
          }
          this.tmpQuoteGeneralData = dataString;
        }, 500);
      });
    } else if (url.indexOf('/home/') > -1 || url.indexOf('/dwelling/') > -1 || url.indexOf('/umbrella/') > -1) {
      this.generalDataRerateSubscription = this.storageService.generalHomeData$.subscribe( data => {
        setTimeout( () => {
          const dataString = JSON.stringify(data);
          if (this.tmpQuoteGeneralData && this.tmpQuoteGeneralData !== dataString) {
            this.rerateAll();
            this.storageService.setStorageData('rates', this.rates);
          }
          this.tmpQuoteGeneralData = dataString;
        }, 500);
      });
    }
  }

  public displayRates(rates): string {
    let ratesText = '$';
    if (rates.length > 1) {
      if (rates[0].premium !== null || rates[0].premium !== '') {
        const minVal = rates.reduce((min, p) => p.premium < min ? p.premium : min, rates[0].premium);
        const maxVal = rates.reduce((max, p) => p.premium > max ? p.premium : max, rates[0].premium);
        ratesText = (minVal === maxVal) ? ratesText + minVal : ratesText + minVal + ' - $' + maxVal;
        // ratesText += minVal + ' - $' + maxVal;
      } else {
        const minVal = rates.reduce((min, p) => p.quoteInfo.premium < min ? p.quoteInfo.premium : min, rates[0].quoteInfo.premium);
        const maxVal = rates.reduce((max, p) => p.quoteInfo.premium > max ? p.quoteInfo.premium : max, rates[0].quoteInfo.premium);
        ratesText = (minVal === maxVal) ? ratesText + minVal : ratesText + minVal + ' - $' + maxVal;
        // ratesText += minVal + ' - $' + maxVal;
      }
    } else {
      if (rates[0].premium || Number.isInteger(parseInt(rates[0].premium))) {
        ratesText += rates[0].premium;
      } else {
        ratesText += rates[0].quoteInfo.premium;
      }
    }
    return ratesText;
  }

  public updateCurrentRateIndex(index) {
    this.currentRateIndex = index;
    this._currentRateIndex.next(this.currentRateIndex);
  }

  public get getCurrentRateIndex$(): Observable<number> {
    this._currentRateIndex.next(this.currentRateIndex);
    return this._currentRateIndex.asObservable();
  }
}
