import { async, ComponentFixture, fakeAsync, inject, TestBed, tick } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';

import { DWELLING_PROTECTION_CLASS } from 'testing/data/dwellings/protection-class';
import { DWELLING_SUBSYSTEMS } from 'testing/data/dwellings/subsystems';
import { DWELLINGS } from 'testing/data/quotes/dwellings';
import { DWELLING_LOCATION } from 'testing/data/quotes/locations/dwelling';
import { HOME_QUOTE_PLAN_LIST } from 'testing/data/quotes/quote-plan-list/home';
import {
    HOME_CARRIER_POLICY_OPTIONS_PARSED
} from 'testing/data/specs/rating-coverages/HOME/policy-options-carrier-parsed';
import { MA_TOWNS } from 'testing/data/specs/rating-states/ma-towns';
import { HOME_COVERAGE_DEFAULTS } from 'testing/data/subs/coverage-defaults/home-policy';
import { changeTextInputValue } from 'testing/helpers/all';
import { StubAutocompleteComponent } from 'testing/stubs/components/autocomplete.component';
import { StubConfirmboxComponent } from 'testing/stubs/components/confirmbox.component';
import { StubModalboxComponent } from 'testing/stubs/components/modalbox.component';
import { StubSelectComponent } from 'testing/stubs/components/select.component';
import { StubTooltipComponent } from 'testing/stubs/components/tooltip.component';
import { StubBlurDirective } from 'testing/stubs/directives/blur.directive';
import { StubDetectSystemDirective } from 'testing/stubs/directives/detect-system.directive';
import { MockRouterProvider } from 'testing/stubs/router.provider';
import { StubClientsServiceProvider } from 'testing/stubs/services/clients.service.provider';
import { StubCoveragesServiceProvider } from 'testing/stubs/services/coverages.service.provider';
import {
    StubDwellingService, StubDwellingServiceProvider
} from 'testing/stubs/services/dwelling.service.provider';
import {
    StubOverlayLoaderServiceProvider
} from 'testing/stubs/services/overlay-loader.service.provider';
import {
    StubSpecsService, StubSpecsServiceProvider
} from 'testing/stubs/services/specs.service.provider';
import { StubSubsServiceProvider } from 'testing/stubs/services/subs.service.provider';

import { DwellingService } from 'app/dashboard/app-services/dwelling.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Helpers } from 'app/utils/helpers';

import { DwellingInformationComponent } from './dwelling-information.component';

describe('Component: DwellingInformation', () => {
  let component: DwellingInformationComponent;
  let fixture: ComponentFixture<DwellingInformationComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [FormsModule],
      declarations: [
        DwellingInformationComponent,
        StubAutocompleteComponent,
        StubSelectComponent,
        StubTooltipComponent,
        StubBlurDirective,
        StubModalboxComponent,
        StubDetectSystemDirective,
        StubConfirmboxComponent
      ],
      providers: [
        StorageGlobalService,
        StubCoveragesServiceProvider,
        StorageService,
        StubSubsServiceProvider,
        StubDwellingServiceProvider,
        StubOverlayLoaderServiceProvider,
        StubSpecsServiceProvider,
        MockRouterProvider,
        StubClientsServiceProvider
      ]
    })
    .compileComponents();
  }));

  beforeEach(fakeAsync(inject([StorageService, DwellingService, SpecsService],
    (storageService: StorageService, dwellingService: StubDwellingService,
      specsService: StubSpecsService) => {
      storageService.setStorageData('selectedPlan', Helpers.deepClone(HOME_QUOTE_PLAN_LIST));
      storageService.setStorageData('dwelling', Helpers.deepClone(DWELLINGS.items[0]));
      storageService.setStorageData('dwellingSubsystems', Helpers.deepClone(DWELLING_SUBSYSTEMS));
      storageService.setStorageData('dwellingProtectionClass', Helpers.deepClone(DWELLING_PROTECTION_CLASS.items[0]));
      storageService.setStorageData('homeDefaultOptions', Helpers.deepClone(HOME_COVERAGE_DEFAULTS));
      storageService.setStorageData('homeCarrierOptionsParsed', Helpers.deepClone(HOME_CARRIER_POLICY_OPTIONS_PARSED));
      storageService.setStorageData('selectedQuoteFormTypes', ['HO5']);

      dwellingService.dwellingLocation = Helpers.deepClone(DWELLING_LOCATION);
      specsService.townsList = Helpers.deepClone(MA_TOWNS);

      fixture = TestBed.createComponent(DwellingInformationComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
      tick();
  })));

  it('should be created', () => {
    expect(component).toBeTruthy();
  });

  it('should destroy without errors', () => {
    expect(() => {
      fixture.destroy();
    }).not.toThrow();
  });

  describe('when updating dwelling information', () => {
    const inputs: { [k: string]: HTMLInputElement } = {};
    const autocompletes: { [k: string]: StubAutocompleteComponent } = {};
    let dwellingService: StubDwellingService;

    beforeEach(inject([DwellingService], (_dwellingService: StubDwellingService) => {
      dwellingService = _dwellingService;

      spyOn(dwellingService, 'updateDwellingLocation').and.callThrough();
      spyOn(dwellingService, 'updateDwellingProtectionClass').and.callThrough();
      spyOn(dwellingService, 'updateDwelling').and.callThrough();

      ['#dwellingAddress', '#dwellingZip', '#dwellingState', '#constructionYear', '#dwellingAge', '#livingSpaceArea',
        '#isTownhouse', '#hasPoolYes', '#hasPoolNo'].forEach((item) => {
        inputs[item] = fixture.debugElement.query(By.css(item)).nativeElement;
      });

      ['#dwellingCity', '#constructionMaterial', '#utilizationTypeCode', '#storiesCount', '#families',
        '#mortgageeCount', '#townhouseUnitsCount'].forEach((item) => {
        autocompletes[item] = fixture.debugElement.query(By.css(item)).componentInstance;
      });
    }));

    it('updates dwellingLocation if address changes', fakeAsync(() => {
      changeTextInputValue(inputs['#dwellingAddress'], '123 Awesome St', fixture);

      expect(component.dwellingLocation.address1).toEqual('123 Awesome St');
      expect(dwellingService.updateDwellingLocation).toHaveBeenCalled();
    }));

    it('updates dwellingProtectionClass and dwellingLocation if city changes', fakeAsync(() => {
      autocompletes['#dwellingCity'].activeOption = component.cities[0];

      fixture.detectChanges();
      tick(50);

      expect(dwellingService.updateDwellingProtectionClass).toHaveBeenCalled();
      expect(dwellingService.updateDwellingLocation).toHaveBeenCalled();
    }));

    it('updates dwellingLocation if state changes', fakeAsync(() => {
      changeTextInputValue(inputs['#dwellingState'], 'TX', fixture);

      expect(component.dwellingLocation.state).toEqual('TX');
      expect(dwellingService.updateDwellingLocation).toHaveBeenCalled();
    }));

    it('updates dwellingProtectionClass and dwellingLocation if zip changes', fakeAsync(() => {
      changeTextInputValue(inputs['#dwellingZip'], '54321', fixture);

      expect(component.dwellingLocation.zip).toEqual('54321');
      expect(dwellingService.updateDwellingProtectionClass).toHaveBeenCalled();
      expect(dwellingService.updateDwellingLocation).toHaveBeenCalled();
    }));

    it('updates dwelling if construction changes', fakeAsync(() => {
      autocompletes['#constructionMaterial'].activeOption = component.construction[0];

      fixture.detectChanges();
      tick(50);

      fixture.detectChanges();
      tick(300);

      expect(dwellingService.updateDwelling).toHaveBeenCalled();
    }));

    it('updates dwelling if construction year changes', fakeAsync(() => {
      changeTextInputValue(inputs['#constructionYear'], '1879', fixture);

      expect(component.dwelling.constructionYear).toEqual('1879');

      fixture.detectChanges();
      tick(300);

      expect(dwellingService.updateDwelling).toHaveBeenCalled();
    }));

    it('updates dwelling age if construction year changes', fakeAsync(() => {
      changeTextInputValue(inputs['#constructionYear'], '1879', fixture);

      fixture.detectChanges();
      tick(300);

      expect(inputs['#dwellingAge'].value).toEqual(((new Date()).getFullYear() - 1879).toString());
    }));

    it('updates dwelling if construction age changes', fakeAsync(() => {
      changeTextInputValue(inputs['#dwellingAge'], '50', fixture);

      expect(component.dwelling.constructionAge).toEqual('50');

      fixture.detectChanges();
      tick(300);

      expect(dwellingService.updateDwelling).toHaveBeenCalled();
    }));

    it('updates construction year if dwelling age changes', fakeAsync(() => {
      changeTextInputValue(inputs['#dwellingAge'], '50', fixture);

      fixture.detectChanges();
      tick(300);

      expect(inputs['#constructionYear'].value).toEqual(((new Date()).getFullYear() - 50).toString());
    }));
  });
});
