<!--<section class="section">
  <app-search-by-name (filterByName)="filterByName($event)" (resetSearch)="resetSearch()"></app-search-by-name>
</section>
<section class="section" style="padding-top: 0;">
  <div class="row">
    <div class="col-xs-12">
      <app-filter  [id]="'filter_transactions'" [name]="'Transactions'" [hasSearch]="false" [options]="filterTransactionOptions" [label]="filterTransactionLabel" (onChange)="onFilterDataChange($event)"></app-filter>

      <app-filter  [id]="'filter_agents'" [name]="'Agents'" [hasSearch]="true" [options]="filterAgentsOptions" [label]="filterAgentsLabel" (onChange)="onFilterDataChange($event)"></app-filter>

      <app-filter #refFilterDates [id]="'filter_dates'" [name]="'Dates'" [label]="filterDatesLabel" [disabled]="searchQuery && searchQuery">
        <div class="box u-width-650px">
          <div class="row row-narrow">
            <div class="col-xs-3">
              <div class="u-padd--bottom-1">Date Type</div>

              <sm-autocomplete
                [options]="filterDatesTypesOptions"
                [activeOption]="filterDatesTypesSelectedOption"
                [searchFromBegining]="true"
                (onSelect)="onDatesParamsChangeHandler($event, 'dateType')">
              </sm-autocomplete>

            </div>
            <div class="col-xs-3">
              <div class="u-padd--bottom-1">Date Range</div>

              <sm-autocomplete
                [options]="filterDatesRangeOptions"
                [activeOption]="filterDatesRangeSelectedOption"
                [searchFromBegining]="true"
                (onSelect)="onDatesParamsChangeHandler($event, 'dateRange')">
              </sm-autocomplete>

            </div>
            <div class="col-xs-3">
              <div class="u-padd--bottom-1">Starting</div>
              <app-datepicker-input #datepickerStartingView [id]="'dateStarting'" [dateRangeEnd]="dateRange.end" (onDateChange)="onDatesParamsChangeHandler($event, 'dateRangeStart')" [selectDate]="dateRange.start" [returnDateFormat]="'MMM d, yyyy'">
              </app-datepicker-input>
            </div>
            <div class="col-xs-3">
              <div class="u-padd--bottom-1">Ending</div>
              <app-datepicker-input #datepickerEndingView [id]="'dateEnding'" [dateRangeStart]="dateRange.start" class="'to-right'" (onDateChange)="onDatesParamsChangeHandler($event, 'dateRangeEnd')" [selectDate]="dateRange.end" [returnDateFormat]="'MMM d, yyyy'">
              </app-datepicker-input>
            </div>
          </div>
        </div>
        <button (click)="getTransactionsFromFilteredDates()" name="searchbtn" class="o-btn u-float-right">Search</button>
      </app-filter>


      <app-filter  [id]="'filter_status'" [name]="'Status'" [hasSearch]="true" [options]="filterStatusOptions" (onChange)="onFilterDataChange($event)"></app-filter>
  </div>
  </div>
</section>

<section class="section">
  <div class="row">
    <div class="col-xs-12">

      <table class="table table--compact table--fixed table--hoverable-grey">
        <thead class="table__thead">
          <tr class="">
            <th class="table__th u-width-150px">Name</th>
            <th class="table__th u-width-150px">Transaction</th>
            <th class="table__th u-width-120px">Date</th>
            <th class="table__th u-width-115px">Agent</th>
            <th class="table__th">Status</th>
          </tr>
        </thead>

        <tbody class="table__tbody" *ngIf="arrTransactionsAll">
          <tr class="table__tr" *ngFor="let row of arrTransactionsAll">
            <td class="table__td">
              <i class="o-icon o-icon--md o-icon--i-{{row.lob}} table__td-icon"></i>
            </td>
            <td class="table__td u-color-pelorous">
              <a [routerLink]="generateRouterLinkValue(row)">
                <span *ngIf="!row.client.firstName && !row.client.lastName && !row.client.name && !row.client.businessName">--</span>
                <span *ngIf="row.client.businessName">{{row.client.businessName}}</span>
                <span *ngIf="row.client.name">{{row.client.name}}</span>
                <span *ngIf="row.client.firstName">{{row.client.firstName}} </span>
                <span *ngIf="row.client.lastName">{{row.client.lastName}}</span>
              </a>
            </td>
           <td class="table__td">{{row?.client?.city}}<span *ngIf="row.client.city && row.client.state">, </span>{{row?.client?.state}}</td>
            <td class="table__td">{{parseLastModifiedDate(row.effectiveDate)}}</td>
            <td class="table__td">{{row.agent}}</td>
            <td class="table__td">{{row.status}}</td>
          </tr>
        </tbody>
        <tbody class="table__tbody" *ngIf="!arrTransactionsAll?.length">
          <tr class="table__tr">
            <td [colSpan]="!arrTransactionsAll?.length ? 7 : 6"><p class="u-padd--bottom-1 u-padd--1">There are no results that match your search.</p></td>
          </tr>
        </tbody>
      </table>

    </div>
  </div>
</section>


<section class="section">
  <div class="u-flex u-flex--spread u-flex--to-middle">
    <div class="">
      <app-pagination [currentPage]="paginationCurrentPage" [totalRecords]="paginationResultsCount" [recordsLimit]="paginationResultLimit" (onPageChange)="paginationPageChange($event)"></app-pagination>
    </div>
    <div class="">
      <app-results-limiter (onChange)="onResultLimitChange($event)"></app-results-limiter>
    </div>
  </div>
</section>
-->
