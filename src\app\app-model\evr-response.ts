export interface AttachmentData {
  typeCode: string;
  typeDescription: string;
  data: string;
  image: string;
  thumbnail: string;
}

export interface ResponseDetail {
  attachmentData: AttachmentData[];
}

export interface Message {
  message: string;
}

export interface ResultStatus {
  status: string;
  messages: Message[];
}

export interface EvrResult {
  transactionType: string;
  atlasValidatedTransactionKey: string;
  atlasTransactionKey: string;
  atlasRegistrationKey: string;
  responseDetail: ResponseDetail;
  resultStatus: ResultStatus;
  registrationExpirationDate: string
  decalsBalance: number
}
