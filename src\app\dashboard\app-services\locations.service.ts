import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from 'app/shared/services/api.service';
import { LocationData } from 'app/app-model/location';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { catchError } from 'rxjs/operators';

@Injectable()
export class LocationsService {

  constructor(
    private apiService: ApiService,
    private apiCommonService: ApiCommonService
  ) { }

  // Get locations List
  public getLocationsList(quoteId: string): Observable<any> {
    const uri = '/quotes/' + quoteId + '/locations';
    return this.getLocationsListByUri(uri);
  }

  public getLocationsListByUri(uri: string): Observable<any> {
    return this.apiCommonService.getByUri(uri);
  }

  // Create location
  public createLocation(quoteId: string, data: any= {}): Observable<any> {
    const uri = '/quotes/' + quoteId + '/locations';
    return this.createLocationByUri(uri, data);
  }

  public createLocationByUri(uri: string, data: any= {}): Observable<any> {
    return this.apiCommonService.postByUri(uri, data);
  }

  // Get location by quoteId and locationId
  public getLocation(quoteId: string, locationId: string): Observable<any> {
    const uri = '/quotes/' + quoteId + '/locations/' + locationId;
    return this.getLocationByUri(uri);
  }

  public getLocationByUri(uri: string): Observable<any> {
    return this.apiCommonService.getByUri(uri);
  }

  // Update location
  public updateLocation(quoteId: string, locationId: string, locationData: LocationData): Observable<any> {
    const uri = '/quotes/' + quoteId + '/locations/' + locationId;
    return this.updateLocationByUri(uri, locationData).pipe(catchError
    (err => {
      if (err.status === 400 && err.error.message === 'Validation Failed') {
        alert(err.error.errors[0].message);
        return null;
      }
    }));
  }

  public updateLocationByUri(uri: string, locationData: LocationData): Observable<any> {
    return this.apiCommonService.putByUri(uri, locationData);
  }

  // Delete location
  public deleteLocation(quoteId: string, locationId: string): Observable<any> {
    const uri = '/quotes/' + quoteId + '/locations/' + locationId;
    return this.deleteLocationByUri(uri);
  }

  public deleteLocationByUri(uri: string): Observable<any> {
    return this.apiCommonService.deleteByUri(uri);
  }
}
