import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { UmbrellaTabsNavComponent } from './umbrella-tabs-nav.component';
import { RouterTestingModule } from '@angular/router/testing';
import { StorageService } from 'app/shared/services/storage-new.service';

describe('UmbrellaTabsNavComponent', () => {
  let component: UmbrellaTabsNavComponent;
  let fixture: ComponentFixture<UmbrellaTabsNavComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      declarations: [UmbrellaTabsNavComponent],
      providers: [
        StorageService
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(UmbrellaTabsNavComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
