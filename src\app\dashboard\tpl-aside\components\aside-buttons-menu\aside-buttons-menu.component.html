<!--[if IE]><!-->

  <!--<![endif]-->

<div class="sidebox sidebox--wider">
<h6 *ngIf="!formsOnlyUser">Rating</h6>
<br>
  <ng-container *ngIf="!formsOnlyUser">
    <a #refBtnNewAuto class="app-button-square app-button-square--auto"
      [userSubscriptionRating]="{lob: 'AUTOP', warningPopup: refSubscriptionAutoInfoPopup, createFormContainer: refTooltipNewAuto}"
      (showPopup)="setMsg($event)"><p>Auto</p></a>

    <!-- TOOLTIPS: AUTO -->
    <sm-tooltip #refTooltipNewAuto [css]="'tooltip-new tooltip-new--box tooltip-new--menu-aside-btns'"
      [useLauncherWidth]="false" [preventCloseContentClick]="true" [position]="'my-top-left-at-top-right'"
      [positionLauncher]="refBtnNewAuto" [positionPreventAdjust]="false">

      <app-new-auto-create-form *ngIf="refTooltipNewAuto.isOpen"
        (onCreateManuallyClick)="createManuallyClick($event, refTooltipNewAuto)"
        (onCancelClick)="cancelClick($event, refTooltipNewAuto)">
      </app-new-auto-create-form>
    </sm-tooltip>

    <app-modalbox #refSubscriptionAutoInfoPopup>
      <div *ngIf="subscriptionMessages.length" class="box box--silver u-spacing--1-5">
        <p *ngFor="let message of subscriptionMessages">{{message}}</p>
      </div>
      <div class="row u-spacing--2">
        <div class="col-xs-12 u-align-right">
          <button
            (click)="refSubscriptionAutoInfoPopup.closeModalbox(); closeSubscriptionWarning(proceed, refTooltipNewAuto)"
            class="o-btn">OK</button>
        </div>
      </div>
    </app-modalbox>

    <!-- **** -->

    <a #refBtnNewHome class="app-button-square app-button-square--home"
      [userSubscriptionRating]="{lob: 'HOME', warningPopup: refSubscriptionHomeInfoPopup, createFormContainer: refTooltipNewHome}"
      (showPopup)="setMsg($event)"><p>Home</p></a>

    <!-- TOOLTIPS: HOME -->
    <sm-tooltip #refTooltipNewHome [css]="'tooltip-new tooltip-new--box tooltip-new--menu-aside-btns'"
      [useLauncherWidth]="false" [preventCloseContentClick]="true" [position]="'my-top-left-at-top-right'"
      [positionLauncher]="refBtnNewHome" [positionPreventAdjust]="false">

      <app-new-home-create-form *ngIf="refTooltipNewHome.isOpen"
        (onCreateManuallyClick)="createManuallyClick($event, refTooltipNewHome)"
        (onCancelClick)="cancelClick($event, refTooltipNewHome)">
      </app-new-home-create-form>
    </sm-tooltip>

    <app-modalbox #refSubscriptionHomeInfoPopup>
      <div class="box box--silver u-spacing--1-5">
        <p *ngFor="let message of subscriptionMessages">{{message}}</p>
      </div>
      <div class="row u-spacing--2">
        <div class="col-xs-12 u-align-right">
          <button
            (click)="refSubscriptionHomeInfoPopup.closeModalbox(); closeSubscriptionWarning(proceed, refTooltipNewHome)"
            class="o-btn">Ok</button>
        </div>
      </div>
    </app-modalbox>

    <!-- **** -->

    <a #refBtnNewDwelling class="app-button-square app-button-square--dwelling"
      [userSubscriptionRating]="{lob: 'DFIRE', warningPopup: refSubscriptionDwellingInfoPopup, createFormContainer: refTooltipNewDwelling}"
      (showPopup)="setMsg($event)"><p>Dwelling</p></a>

    <!-- TOOLTIPS: DWELLING -->
    <sm-tooltip #refTooltipNewDwelling [css]="'tooltip-new tooltip-new--box tooltip-new--menu-aside-btns'"
      [useLauncherWidth]="false" [preventCloseContentClick]="true" [position]="'my-top-left-at-top-right'"
      [positionLauncher]="refBtnNewDwelling" [positionPreventAdjust]="false">

      <app-new-dwelling-create-form *ngIf="refTooltipNewDwelling.isOpen"
        (onCreateManuallyClick)="createManuallyClick($event, refTooltipNewDwelling)"
        (onCancelClick)="cancelClick($event, refTooltipNewDwelling)">
      </app-new-dwelling-create-form>
    </sm-tooltip>

    <app-modalbox #refSubscriptionDwellingInfoPopup>
      <div class="box box--silver u-spacing--1-5">
        <p *ngFor="let message of subscriptionMessages">{{message}}</p>
      </div>
      <div class="row u-spacing--2">
        <div class="col-xs-12 u-align-right">
          <button
            (click)="refSubscriptionDwellingInfoPopup.closeModalbox(); closeSubscriptionWarning(proceed, refTooltipNewDwelling)"
            class="o-btn">OK</button>
        </div>
      </div>
    </app-modalbox>

    <!-- **** -->

    <a #refBtnNewUmbrella class="app-button-square app-button-square--umbrella"
      [userSubscriptionRating]="{lob: 'PUMBR', warningPopup: refSubscriptionUmbrellaInfoPopup, createFormContainer: refTooltipNewUmbrella}"
      (showPopup)="setMsg($event)"><p>Umbrella</p></a>

    <!-- TOOLTIPS: UMBRELLA -->
    <sm-tooltip #refTooltipNewUmbrella [css]="'tooltip-new tooltip-new--box tooltip-new--menu-aside-btns'"
      [useLauncherWidth]="false" [preventCloseContentClick]="true" [position]="'my-top-left-at-top-right'"
      [positionLauncher]="refBtnNewUmbrella" [positionPreventAdjust]="false">

      <app-new-umbrella-create-form *ngIf="refTooltipNewUmbrella.isOpen"
        (onCreateManuallyClick)="createManuallyClick($event, refTooltipNewUmbrella)"
        (onCancelClick)="cancelClick($event, refTooltipNewUmbrella)">
      </app-new-umbrella-create-form>
    </sm-tooltip>

    <app-modalbox #refSubscriptionUmbrellaInfoPopup>
      <div *ngIf="subscriptionMessages.length"  class="box box--silver u-spacing--1-5">
        <p *ngFor="let message of subscriptionMessages">{{message}}</p>
      </div>
      <div class="row u-spacing--2">
        <div class="col-xs-12 u-align-right">
          <button
            (click)="refSubscriptionUmbrellaInfoPopup.closeModalbox(); closeSubscriptionWarning(proceed, refTooltipNewUmbrella)"
            class="o-btn">OK</button>
        </div>
      </div>
    </app-modalbox>

    <div *ngIf="isFeatureEnabled('CommercialAuto')">
      <a #refBtnNewCommercial class="app-button-square app-button-square--commercial"
        [userSubscriptionRating]="{lob: 'AUTOB', warningPopup: refSubscriptionCommercialInfoPopup, createFormContainer: refTooltipNewCommercial}"
        (showPopup)="setMsg($event)"><p>Comm</p></a>

      <!-- TOOLTIPS: COMMERCIAL -->
      <sm-tooltip #refTooltipNewCommercial [css]="'tooltip-new tooltip-new--box tooltip-new--menu-aside-btns'"
        [useLauncherWidth]="false" [preventCloseContentClick]="true" [position]="'my-top-left-at-top-right'"
        [positionLauncher]="refBtnNewCommercial" [positionPreventAdjust]="false">

        <app-new-commercial-auto-create-form *ngIf="refTooltipNewCommercial.isOpen"
          (onCreateManuallyClick)="createManuallyClick($event, refTooltipNewCommercial)"
          (onCancelClick)="cancelClick($event, refTooltipNewCommercial)">
        </app-new-commercial-auto-create-form>
      </sm-tooltip>

      <app-modalbox #refSubscriptionCommercialInfoPopup>
        <div class="box box--silver u-spacing--1-5">
          <p *ngFor="let message of subscriptionMessages">{{message}}</p>
        </div>
        <div class="row u-spacing--2">
          <div class="col-xs-12 u-align-right">
            <button
              (click)="refSubscriptionCommercialInfoPopup.closeModalbox(); closeSubscriptionWarning(proceed, refTooltipNewCommercial)"
              class="o-btn">OK</button>
          </div>
        </div>
      </app-modalbox>
    </div>


  </ng-container></div>
  <div class="sidebox">
   <!-- **** RMV Service -->

<h6>RMV Services </h6>

<br>
<a #refBtnNewRMVService (click)="navigateToRmvService()"
[userSubscription]="{code: 'RMV',lob: 'AUTOP' , warningPopup: refSubscriptionRmvInfoPopup}" (showPopup)="setMsg($event)"
class="app-button app-button--rmv rmv--button">
    <img style="width:25px; height:25px; margin-right: 10px; vertical-align:middle" src="assets/images/common/all-services.png" alt=""> <span>All Services</span></a>
<div style="margin-left: 8px;border: 1px solid #F28900;padding-top: 5px;" *ngIf="!rmvExpired && !formsExpired">
  <input placeholder="Plate Number" [(ngModel)]="plateNumber" style="width:92%;margin-left:7px;">
  <button class="rmv-r-button" style="margin-right: 5px;margin-left:5px;" (click)="registrationValidation('RegistrationRenewalDataValidationEasy')"
  [userSubscription]="{code: 'RMV', warningPopup: refSubscriptionRmvInfoPopup}" (showPopup)="setMsg($event)" [disabled]="!plateNumber"
  >Renew</button>
  <button class="rmv-r-button" (click)="registrationValidation('RegistrationReinstatementDataValidationEasy')"
  [userSubscription]="{code: 'RMV', warningPopup: refSubscriptionRmvInfoPopup}" [disabled]="!plateNumber" (showPopup)="setMsg($event)"
  >Reinstate</button>
  <!-- <app-modalbox #registrationSuccess [preventCloseOnBackdropClick]="true">
    <app-aside-registration-response *ngIf="regData"[data]="regData" [plateNumber]="plateNumber" [transaction]="transactionType" [modalbox]="registrationSuccess"></app-aside-registration-response>
  </app-modalbox> -->

  <app-modalbox #rmvNotEnabled >
    <div class="box box--silver u-align-center u-spacing--1-5">
      <h3>{{rmvNotEnabledMessage}}</h3>
    </div>
    <div class="row u-spacing--2">
      <div class="col-xs-12 u-align-right">
        <button
          (click)="rmvNotEnabled.closeModalbox()"
          class="o-btn">OK</button>
      </div>
    </div>
  </app-modalbox>

</div>

<br>
<app-modalbox #refSubscriptionRmvInfoPopup>
  <div class="box box--silver u-spacing--1-5">
    <p *ngFor="let message of subscriptionMessages">{{message}}</p>
  </div>
  <div class="row u-spacing--2">
    <div class="col-xs-12 u-align-right">
      <button
        (click)="refSubscriptionRmvInfoPopup.closeModalbox()"
        class="o-btn">OK</button>
    </div>
  </div>
</app-modalbox>
  </div>



    <!-- **** -->

  <!-- **** -->
  <div class="sidebox">


  <h6>ACORD </h6>

  <br>
  <a #refBtnNewForm class="app-button app-button--file"
    [userSubscription]="{code: 'Forms', warningPopup: refSubscriptionFormInfoPopup, createFormContainer: refTooltipNewForm}"
    (showPopup)="setMsg($event)">New Form</a>

  <!-- TOOLTIPS: FORM -->
  <sm-tooltip #refTooltipNewForm
    [arrowOverride]="355"
    [top]="'new-form-top'"
    [css]="'tooltip-new tooltip-new--box tooltip-new--menu-aside-btns u-width-750px u-width-max-750px'"
    [useLauncherWidth]="false" [preventCloseContentClick]="true" [position]="'my-top-left-at-top-right'"
    [positionLauncher]="refBtnNewForm" [positionPreventAdjust]="false" [positionVerticalAdjustType]="'direct'">

    <app-new-form-create *ngIf="refTooltipNewForm.isOpen" (createForm)="refTooltipNewForm.close()"
      (cancel)="refTooltipNewForm.close()" (formsLoadEnd)="adjustTooltipPosition(refTooltipNewForm)">
    </app-new-form-create>
  </sm-tooltip>

  <app-modalbox #refSubscriptionFormInfoPopup>
    <div class="box box--silver u-spacing--1-5">
      <p *ngFor="let message of subscriptionMessages">{{message}}</p>
    </div>
    <div class="row u-spacing--2">
      <div class="col-xs-12 u-align-right">
        <button
          (click)="refSubscriptionFormInfoPopup.closeModalbox(); closeSubscriptionWarning(proceed, refTooltipNewForm)"
          class="o-btn">OK</button>
      </div>
    </div>
  </app-modalbox>
</div>
  <!-- **** -->


 <!-- / .sidebox -->
