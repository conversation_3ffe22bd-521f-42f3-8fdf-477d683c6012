export class AgencyForm {
  constructor(
    public agencyFormId: string = null,
    public identifier: string = null,
    public city: string = null,
    //    public agencyFormIdentifier:string = null,
    public customerName: string = null,
    public meta = {
      href: <string>''
    },
    public lastModifiedDate: string = null,
    public lastModifyingAgentName: string = null,
    public formName: string = null,
    public formNumber: string = null,
    public quoteSessionId: string = null,
    public resourceId: string = null,
    public resourceName: string = 'AgencyForm',
    public state: string = null,
    public authorizationToken =
      {
        requestId: <string>'',
        ticket: <string>''
      }
  ) { }
}

export class FormData {
  constructor(
    public agencyId: string = '',
    public creationUserId: number = 0,
    public Number: string = '',
    public Data: string = '',
    public clientId: number = 0,
    public quoteSessionId: string = '',
    public newClientFirstName: string = '',
    public newClientLastName: string = '',
    public rateResponseId: string = '',
    public rateResponseIndex: number = 0,
    public ratingPlanId: number = 0,
    public drivers: string[] = [],
    public vehicles: string[] = [],
    public maskPrivateInformation: boolean = false
  ) { }
}


// export class Form {
//   constructor (
//     public formIdentifier: string = null,
//     public number: string = null,
//     public name: string = null,
//     public vehicleLimit: number = 1,
//     public driverLimit: number = 0,
//     public meta = {
//       href: <string>''
//     },
//     public quoteSessionId: string = null,
//     public resourceId: string = null,
//     public resourceName: string = 'Form',
//   ) {}
// }

export class Form {
  constructor(
    // public formIdentifier: string = null,
    public number: string = null,
    public name: string = null,
    public vehicleLimit: number = 1,
    public driverLimit: number = 0,
    public meta = {
      href: <string>''
    },
    // public quoteSessionId: string = null,
    // public resourceId: string = null,
    public resourceName: string = 'Form',
    public formTypeId: number = null,
    public usageOrder: number = 0,
  ) { }
}
