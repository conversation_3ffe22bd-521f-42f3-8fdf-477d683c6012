<div class="row">
  <div class="col-xs-12">
    <h1 class="o-heading o-heading--for-section o-heading--notransform">New Form</h1>
  </div>
</div>

<hr>

<div class="row u-spacing--1-5 u-position--relative">
  <app-loader [loading]="loadingForms"></app-loader>
  <div class="col-xs-7">
    <h3 class="o-heading">Frequently Used Forms</h3>

    <div class="u-spacing--1-5">
      <table class="table table--hoverable form-table u-spacing--right-2">
        <tbody class="table__tbody">
          <tr class="table_tr_popup" *ngFor="let row of arrFrequentForms;" (click)="selectFormNumber(row.number)">
            <td class="table__td">
              <div class="checklist__btns">
                <label class="o-checkable">
                  <input type="radio" name="selectedFormNumber" [id]="'selectedFormNumber_' + row.number"
                    [value]="row.number" [(ngModel)]="selectedFormNumber">
                  <i class="o-btn o-btn--radio"></i>
                </label>
              </div>
            </td>
            <td class="table__td">
              <a class="u-color-pelorous">{{row.name}}</a>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <br>
    <h3 class="o-heading">Remaining Forms</h3>
    <div class="u-spacing--1-5">
      <p-scrollPanel [style]="{width:'100%', height: '150px'}">
        <table class="table table--hoverable form-table u-spacing--right-2">
          <tbody class="table__tbody">
            <tr class="table_tr_popup" *ngFor="let row of arrRemainingForms;" (click)="selectFormNumber(row.number)">
              <td class="table__td">
                <div class="checklist__btns">
                  <label class="o-checkable">
                    <input type="radio" name="selectedFormNumber" [id]="'selectedFormNumber_' + row.number"
                      [value]="row.number" [(ngModel)]="selectedFormNumber">
                    <i class="o-btn o-btn--radio"></i>
                  </label>
                </div>
              </td>
              <td class="table__td">
                <a class="u-color-pelorous">{{row.name}}</a>
              </td>
            </tr>
          </tbody>
        </table>
      </p-scrollPanel>
    </div>
  </div>

  <div class="col-xs-5">
    <h3 class="o-heading">Form Options</h3>

    <div class="u-spacing--1-5">
      <div class="box box--silver u-bg-white">
        <div *ngIf="selectedFormNumber !== 'RTA'">
        <p>Select the client:</p>

        <ul class="checklist u-spacing--1">
          <li class="checklist__item checklist__item--alt">
            <label class="o-checkable">
              <input type="radio" name="createUserType" value="new" (change)="actionClientTypeChange()"
                [(ngModel)]="createFormClientType">
              <i class="o-btn o-btn--radio"></i>
              <span>Create New Client</span>
            </label>

            <div *ngIf="viewShowCreateNewClientForm" class="checklist__label checklist__label--additional">
              <table class="form-table form-table--fixed u-spacing--1">
                <tr class="form-table__row">
                  <td class="form-table__cell u-width-120px">
                    Input name or view new client screen.
                  </td>
                </tr>
                <tr class="form-table__row">
                  <td class="form-table__cell u-width-200px">
                    <input type="text" id="newClientFirstName" required placeholder="First Name"
                      name="newClientFirstName" [(ngModel)]="newClientFirstName" [dataToSave]="newClientFirstName" (blur)="validateNewClientName()">
                  </td>
                </tr>
                <tr class="form-table__row">
                  <td class="form-table__cell u-width-200px">
                    <input type="text" id="newClientLastName" required placeholder="Last Name" name="newClientLastName" (blur)="validateNewClientName()"
                      [(ngModel)]="newClientLastName" [dataToSave]="newClientLastName">
                  </td>
                </tr>
              </table>
            </div>
          </li>

          <li class="checklist__item checklist__item--alt">
            <label class="o-checkable">
              <input type="radio" name="createUserType" value="existing" (change)="actionClientTypeChange()"
                [(ngModel)]="createFormClientType">
              <i class="o-btn o-btn--radio"></i>
              <span>Find Existing Client</span>
            </label>
            <a *ngIf="viewShowExistingClientLabel" class="checklist__label checklist__label--additional u-no-underline"
              (click)="openClientSelectorModal()">
              <p *ngIf="viewShowExistingClientLabelSelectClient" class="checklist__label-comment">Select the client
              </p>
              <p *ngIf="!viewShowExistingClientLabelSelectClient" class="checklist__label-comment">Client:
                {{clientDetails?.firstName}} {{clientDetails?.lastName}}</p>
            </a>
          </li>

          <li class="checklist__item checklist__item--alt">
            <label class="o-checkable">
              <input type="radio" name="createUserType" value="none" (change)="actionClientTypeChange()"
                [(ngModel)]="createFormClientType">
              <i class="o-btn o-btn--radio"></i>
              <span>None</span>
            </label>
            <div class="checklist__label checklist__label--additional">
            </div>
          </li>
        </ul>

        <hr>
        </div>

        <ng-template #AttachDetails>
          <div>
            <hr>
            Selection of the RTA Form will launch the RMV Services workflow.  This workflow will run multiple lookups to prefill the form saving time and eliminating omissions and mistakes that can cause RTA’s to be rejected by the RMV.
            <hr>
            RTA’s created in this manner result in GetReady pre-approval.  GetReady produces a QR Coded RTA that has been pre-approved by the RMV and greatly reduces wait time at RMV branches.
            <hr>
            In the case of agents who are enabled for EVR-Lite, the RMV Services workflow is required to process transactions.
            <hr>
            </div>
        </ng-template>

        <p>To attach a form to a quote, you need to create the form while viewing the quote.</p>
        <hr *ngIf="createFormClientType !== 'none' && selectedFormNumber === 'RTA'">
        <p *ngIf="selectedFormNumber === 'RTA' then AttachDetails"></p>
        <p  *ngIf="createFormClientType !== 'none'">
          Select Client as None to enable Prefill from RMV.
        </p>


      </div>

    </div>

  </div>
</div>
<ng-container *ngIf="evrEligibility$ | async as evrEligible">

<div class="row u-spacing--1-5">
  <div *ngIf="selectedFormNumber === 'RTA' then goToRmv else standard">

  </div>



  <ng-template #standard>
    <div class="col-xs-12 u-align-right">
    <button type="button" class="o-btn u-spacing--right-1" [ngClass]="{'o-btn--idle': selectedFormNumber === 'RTA'}" (click)="actionCreateForm()">Create Form</button>
    <button type="button" class="o-btn u-spacing--right-1"
    [ngClass]="{'o-btn--idle': selectedFormNumber === 'RTA' && evrEligible.isEvrLiteEligible }"
    style="text-transform:none" *ngIf="selectedFormNumber === 'RTA'" [disabled]="createFormClientType !=='none'" [routerLink]="['/dashboard/rmv-services']" routerLinkActive="router-link-active" >Prefill from RMV</button>
    <button type="button" class="o-btn o-btn--idle" (click)="actionCancel()">Cancel</button>
  </div>
  </ng-template>
  <ng-template #goToRmv>
    <div class="col-xs-12 u-align-right">
      <button type="button" class="o-btn u-spacing--right-1"
    style="text-transform:none"  [routerLink]="['/dashboard/rmv-services/rta-prefill']" [queryParams]="{'type':'PrefillNewTitleAndRegistration'}" routerLinkActive="router-link-active" (click)="actionCancel()">Go to RMV Services</button>
    <button type="button" class="o-btn o-btn--idle" (click)="actionCancel()">Cancel</button>
  </div>
  </ng-template>
</div>
</ng-container>
<div class="modalNestedModals">
  <app-modalbox #refClientSelectorModal [css]="'u-width-760px horizontal-centered'" (click)="$event.stopPropagation()">
    <app-client-selector #refClientSelector *ngIf="refClientSelectorModal.isOpen"
      (onClientSelected)="actionClientSelectorClientSelected($event)"
      (onClientCancelClick)="actionClientSelectorCancel($event)">
    </app-client-selector>
  </app-modalbox>

  <app-modalbox #refModalValidationWarning [css]="'u-width-300px horizontal-centered'"
    (click)="$event.stopPropagation()">
    <h1 class="o-heading o-heading--red u-spacing--bottom-1-5">{{validationWarning?.header}}</h1>
    <div class="box box--silver u-spacing--1-5 u-flex u-flex--spread u-flex--to-top">
      {{validationWarning?.text}}
    </div>
    <div class="row u-spacing--2">
      <div class="col-xs-12 u-align-right">
        <hr class="o-hr u-spacing--bottom-1-5" />
        <button (click)="refModalValidationWarning.closeModalbox()"
          class="o-btn o-btn--idle u-spacing--left-2">Close</button>
      </div>
    </div>
  </app-modalbox>
</div>
