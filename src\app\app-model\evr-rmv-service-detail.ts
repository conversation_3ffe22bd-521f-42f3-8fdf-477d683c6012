export interface AttachmentData {
  typeCode: string;
  typeDescription: string;
  type: string;
  name: string;
  data: string;
  image: string;
  thumbnail: string;
}

export interface RequiredDocument {
  typeCode: string;
  typeDescription: string;
  status: string;
}

export interface LineItem {
  amount: number;
  description: string;
  type: string;
}

export interface RmvFee {
  total: number;
  rmvFee: number;
  bscFee: number;
  bankTransferTotal: number;
  useBankTransferOption: boolean;
  useAgencyBill: boolean;
  agencyBillTotal:number;
  lineItems: LineItem[];
}

export interface ValidateDetail {
  attachmentData: AttachmentData[];
  requiredDocuments: RequiredDocument[];
  requiredEvrLiteDocuments: RequiredDocument[];
  rmvFeeRequired: boolean;
  rmvFees: RmvFee;
}

export interface Owner {
  id: number;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  license: string;
  licenseState: string;
}

export interface Vehicle {
  id: number;
  lookupType: string;
  usage: string;
  plateNumber: string;
  vin: string;
  plateType: string;
  odometer: string;
  registrationType: string;
  reassignedPlate: string;
  registrationReason: string;
  ownership: string;
  condition: string;
  primaryColor: string;
  transmission: string;
  passengers: string;
  outOfStateTitleNumber: string;
  titleState: string;
  titleIssueDate: string;
}

export interface GaragingAddres {
  type: string;
  referenceType: string;
  referenceId: string;
  street: string;
  unitOrApt: string;
  city: string;
  state: string;
  zip: string;
}

export interface Addres {
  street: string;
  unitOrApt: string;
  city: string;
  state: string;
  zip: string;
}

export interface Seller {
  businessName: string;
  firstName: string;
  lastName: string;
  address: Addres;
}

export interface PurchaseAndSalesInformation {
  purchaseDate: string;
  purchaseState: string;
  seller: Seller;
  taxExempt: string;
  taxExemptType: string;
  purchaseType: string;
  dealerFid: string;
  totalSalePrice: string;
  auctionSale: string;
  maResidentAtTimeOfPurchase: string;
  maSalesTaxPreviouslyPaid: string;
  proofOfNoTaxRequired: string;
}

export interface EStampInfo {
  effectiveDate: string;
  policyChangeDate?: string;
  writingCompanyName: string;
  companyCode: number;
  agencyName: string;
  signedBy: string;
}

export interface Lessor {
  id: number;
  fid: string;
}

export interface Lienholder {
  id: number;
  code: string;
}

export interface BusinessOwner {
  id: number;
  fid: string;
}

export interface RmvServicesUserEnteredData {
  owners: Owner[];
  vehicles: Vehicle[];
  garagingAddress: GaragingAddres;
  purchaseAndSalesInformation: PurchaseAndSalesInformation;
  estampInfo: EStampInfo;
  lessors: Lessor[];
  lienholders: Lienholder[];
  businessOwners: BusinessOwner[];
}

export interface EvrLiteDetails {
  rmvServicesId: number;
  workflowType: string;
  userId: number;
  ownerFirstName: string;
  ownerLastName: string;
  ownership:string
  companyOrgId: number;
  companyFacilityId: number;
  transactionId: string;
  transactionType: string;
  transactionTypeDesc: string;
  atlasValidatedTransactionKey: string;
  atlasRegistrationKey: string;
  status: string;
  owner: string;
  email: string;
  agentEmail: string;
  vin: string;
  plate: string;
  savedDate: string;
  lastModifiedDate: string;
  documents: AttachmentData[];
  validateDetails: ValidateDetail;
  rmvServicesUserEnteredData: RmvServicesUserEnteredData;
  assignedPlate?: string
}
