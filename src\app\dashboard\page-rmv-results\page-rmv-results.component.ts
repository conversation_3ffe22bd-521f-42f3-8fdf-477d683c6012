
import {take} from 'rxjs/operators';
import { fork<PERSON>oin ,  SubscriptionLike as ISubscription } from 'rxjs';
import { FilterOption } from 'app/app-model/filter-option';
import { ModalboxComponent } from 'app/shared/components/modalbox/modalbox.component';
import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';

import { Driver, RmvDriverResult, DFLDriver } from 'app/app-model/driver';
import { DriversService } from '../app-services/drivers.service';
import { OverlayRouteService } from 'app/overlay/services/overlay-route.service';
import { RmvService } from 'app/dashboard/app-services/rmv.service';
import { Router } from '@angular/router';
import { StorageService } from 'app/shared/services/storage-new.service';
import { VehiclesService } from 'app/dashboard/app-services/vehicles.service';
import { QuoteNewRMVResponseData } from 'app/app-model/quote';
import { BroadcastService } from 'app/shared/services/broadcast.service';

import { DatepickerInputComponent } from 'app/shared/components/datepicker-input/datepicker-input.component';
import { format, parseISO } from 'date-fns';
const mmddyFormat = 'MM/dd/yyyy';


@Component({
    selector: 'app-page-rmv-results',
    templateUrl: './page-rmv-results.component.html',
    styleUrls: ['./page-rmv-results.component.scss'],
    standalone: false
})
export class PageRmvResultsComponent implements OnInit {
  private subscription;
  public rmvData: QuoteNewRMVResponseData;
  public dateFirstLicenseData: DFLDriver[] = [];
  private drivers: Driver[];
  private vehiclesWithExtraData: Array<any>;
  private driversCount = 0;
  private vehiclesCount = 0;
  public selectedVehicle = {};
  private subscriptionBroadcastOpen: ISubscription;
  @ViewChild('modalRmvResultsDFL') public modalRmvResultsDFL: ModalboxComponent;
  @ViewChild('modalCustomDFL') public modalCustomDFL: ModalboxComponent;
  @ViewChild('refPickerDateFirstLic') public refPickerDateFirstLic: DatepickerInputComponent;
  updateDriversAll: any;
  customDate = '';
  currentlySelectedDriver;

  constructor(
    private storageService: StorageService,
    private vehiclesService: VehiclesService,
    private overlayRouteService: OverlayRouteService,
    private rmvService: RmvService,
    private driverService: DriversService,
    private router: Router,
    private broadcastService: BroadcastService
  ) {}

  ngOnInit() {
    this.getRmvResultFromStorage();
    this.updateDFLDriversModal();
    this.registerBroadcastGetDFLInfo();
    // this.modalRmvResultsDFL.openModalbox();

    this.rmvData.vehicles.forEach(v => {
      v.isIncluded=true;
    })
  }

  ngOnDestroy() {
    this.subscription && this.subscription.unsubscribe();
    this.updateDriversAll && this.updateDriversAll.unsubscribe();
  }

  private registerBroadcastGetDFLInfo(): void {
    this.subscriptionBroadcastOpen = this.broadcastService
      .on('getDFLInfo')
      .subscribe(data => {
        this.getDFLInfo();
      });
  }

  private getRmvResultFromStorage(): void {
    this.subscription = this.storageService
      .getStorageData('responseNewRmvQuoteResult')
      .subscribe((res: QuoteNewRMVResponseData) => (this.rmvData = res));
  }

  getDFLInfo() {
    this.modalRmvResultsDFL.open();
  }

  public updateCustomDateChange(date) {

    if (date) {
      this.customDate = date;
    } else {
      this.customDate = null;
    }console.log(date, this.customDate);
  }

  updateCustomDate(driver: DFLDriver) {
    const currentDriver = this.dateFirstLicenseData.find(
      d => d.resourceId === this.currentlySelectedDriver.resourceId
    );

    const dflOptions = Object.assign([], currentDriver.options);

    const customOptionInd = dflOptions.findIndex(
      d => d.text.includes('Custom') || d.text.includes('custom')
    );
    const existingCustomOptionInd = dflOptions.findIndex(d =>
      d.text.includes('(Custom)')
    );

    if (existingCustomOptionInd > -1) {
      dflOptions[existingCustomOptionInd] = {
        id: this.customDate,
        text: this.helpDisplayLossDate(this.customDate) + ' (Custom)'
      };
    } else {
      dflOptions.splice(customOptionInd, 0, {
        id: this.customDate,
        text: this.helpDisplayLossDate(this.customDate) + ' (Custom)'
      });
    }
    currentDriver.selectedOption = {
      id: this.customDate,
      text: this.helpDisplayLossDate(this.customDate) + ' (Custom)'
    };

    currentDriver.options = dflOptions;

    this.modalCustomDFL.close();
  }

  closeModalboxCustomDate() {
    this.customDate = '';
    this.modalCustomDFL.close();
  }
  updateDFLDriversModal() {
    if (this.rmvData) {
      if (this.rmvData.drivers) {
        this.rmvData.drivers.forEach(d => {
          const dflDriver = new DFLDriver();
          dflDriver.resourceId = d.resourceId;
          dflDriver.firstname = d.firstName;
          dflDriver.lastname = d.lastName;
          dflDriver.options = this.getDriverFirstLicOptions(d);

          if (d.firstLicenseOptions) {
            const defaultOpt = d.firstLicenseOptions.find(dr => dr.selected);
            if (defaultOpt) {
              dflDriver.selectedOption = {
                id: defaultOpt.date ? defaultOpt.date : defaultOpt.label,
                text:
                  (defaultOpt.date ? this.helpDisplayLossDate(defaultOpt.date) : defaultOpt.label) +
                  (this.helpDisplayLossDate(defaultOpt.date)  ? ' (' + defaultOpt.label + ')' : '')
              };
            }
          }
          this.dateFirstLicenseData.push(dflDriver);
        });
      }
    }
  }

 public helpDisplayLossDate(date: string): string {
  return date ? format(new Date(date), 'MM/dd/yyyy') : '';
}
  public selectDriverDFL($event, driver: DFLDriver) {
    const newDFL = $event.id;
    this.currentlySelectedDriver = driver;
    if (newDFL) {
      if (newDFL.includes('custom') || newDFL.includes('Custom')) {
        this.modalCustomDFL.open();
        this.refPickerDateFirstLic.focus(false);
      } else {
        driver.selectedOption = { id: $event.id, text: $event.text };
      }
    }
  }

  private showRMVvehicleReport(RMVvehicle) {
    this.vehiclesService.selectedRMVvehicle = this.rmvData.vehicles[0];
    this.storageService.setStorageData(
      'rmvVehicleLookup',
      this.rmvData.vehicles
    );
    this.overlayRouteService.go(
      'rmvreports',
      'vehicle',
      RMVvehicle.make + '-' + RMVvehicle.model + '-' + RMVvehicle.vin
    );
  }

  private saveDFLRouteToQuote() {
    // Update dfl data from
    let driverResources;
    let vehicleResources;
    const promises = [];

    this.vehiclesService
      .getVehiclesList(this.rmvData.quoteId).pipe(
        take(1))
        .subscribe(res => {
          vehicleResources = res.items;
          if (vehicleResources && vehicleResources.length > 0)
          {
            vehicleResources.forEach(veh => {

              this.rmvData.vehicles.forEach(rmvVeh => {
                  if (!rmvVeh.isIncluded)
                  {
                    const promVeh = this.vehiclesService
                    .deleteVehicleById(this.rmvData.quoteId, rmvVeh.resourceId)
                    .toPromise();
                    promises.push(promVeh);
                  }
              });
            });
          }
        });


    this.driverService
      .getDriversList(this.rmvData.quoteId).pipe(
      take(1))
      .subscribe(res => {
        driverResources = res.items;
        if (driverResources && driverResources.length > 0) {
          driverResources.forEach(item => {
            if (item.resourceId) {
              const dfldriver = this.dateFirstLicenseData.find(
                dfl => dfl.resourceId === item.resourceId
              );
              item.firstLicensed = dfldriver.selectedOption && dfldriver.selectedOption.id ? dfldriver.selectedOption.id : '';
              const prom = this.driverService
                .updateDriverByUri(item.meta.href, item)
                .toPromise();
              promises.push(prom);
            }
          });
          Promise.all(promises).then(() => {
            if (this.rmvData && this.rmvData.meta && this.rmvData.meta.href) {
              this.router.navigate([
                '/dashboard/auto/rmv-load' + this.rmvData.meta.href
              ],{queryParams: {newQuote: true}});
            }
          });
        }
      });


  }

  private selectVehicleToDelete(vehicle) {
    this.selectedVehicle = vehicle;
  }

  private deleteVehicleConfirn($event) {
    console.log('delete: ', $event);
  }

  public generateRmvReportDriverId(driver): string {
    // console.log('DRIVER >>>', driver);
    return this.rmvService.generateRmvReportDriverId(driver);
  }

  public doNotDisplayGeneralMessage(value: string): boolean {
    if (value.startsWith('See')) {
      return false;
    }

    return true;
  }

  public getDriverFirstLicOptions(driver: RmvDriverResult): FilterOption[] {
    if (driver.firstLicenseOptions) {
      let options = [];
      options = driver.firstLicenseOptions
        .filter(function(rmvOpt) {
          if (!rmvOpt.label.includes('Custom') && !rmvOpt.date) {
            return false;
          }
          return true;
        })
        .map(item => ({
          id: item.date ? item.date : item.label,
          text:
            (item.date ? this.helpDisplayLossDate(item.date) : item.label) +
            (this.helpDisplayLossDate(item.date) ? ' (' + item.label + ')' : '')
        }));

      return options;
    }
  }
  public getDriverFirstLicActiveOpt(driver: RmvDriverResult): FilterOption {
    // if (driver) {
    //   let found = driver.firstLicenseOptions.find(function(element) {
    //     return element.date === driver.licenseFirstDate;
    //   });
    //   return new FilterOption(
    //     found.date,
    //     found.date + ' ( ' + found.label + ' )'
    //   );
    // }
    return new FilterOption();
  }

  public selecteddfl($event, driver: DFLDriver) {
    if ($event.text.indexOf('Custom') >= 0) {
      this.modalCustomDFL.open();
    }
    driver.selectedOption = $event.id;
  }

  public selectDriverFirstLic(driver: RmvDriverResult) {}
}
