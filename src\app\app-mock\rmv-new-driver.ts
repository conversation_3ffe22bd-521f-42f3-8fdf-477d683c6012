import {
  Driver,
  DriverRmv,
  DriverRmvToSend,
  RmvDriverResult
} from 'app/app-model/driver';

export const RMV_NEW_DRIVER_GINA: RmvDriverResult = {
  firstName: 'GINA',
  lastName: 'LAUFER',
  middleName: 'MARIE',
  dateOfBirth: '1986-08-21',
  gender: 'F',
  licenseNumber: '*********',
  licenseFirstDate: '2013-12-18',
  licenseState: 'MA',
  sdip: '00',
  unpaid: '',
  totalPremium: '',
  address1: '72 LEDGEBROOK RD',
  address2: '',
  city: 'WEYMOUTH',
  state: 'MA',
  zip: '021900000',
  licenseStatus: null,
  licenseExpirationDate: null,
  trainingDate: null,
  trainingStatus: false,
  policyNumber: 'PRA00002078358',
  policyStatus: 'P',
  policyCarrier: '731',
  policyEffectiveDate: '12/20/2016',
  policyExpirationDate: '12/20/2017',
  resourceId: '1111111111111111111',
  incidents: [
    {
      incidentDate: '2014-04-15',
      surchargeDate: '2014-05-07',
      typeOfIncident: '',
      amountPaid: '380',
      descOfIncident: 'Glass'
    }
  ],
  updDetails: [
    {
      number: 'PRA00002078358',
      type: 'P',
      unpaid: '0',
      carrier: '731',
      status: 'ACTV',
      statusEffectiveDate: '2016-12-20',
      effectiveDate: '12/20/2016',
      expirationDate: '12/20/2017',
      upthDetails: [
        {
          status: 'ACTV',
          reason: 'NBUS',
          effectiveDate: '2016-12-20',
          unpaid: '0',
          agentCode: '731',
          transactionDate: '2016-11-14'
        }
      ]
    },
    {
      number: '9948734982321',
      type: 'P',
      unpaid: '0',
      carrier: '823',
      status: 'CANC',
      statusEffectiveDate: null,
      effectiveDate: '12/20/2015',
      expirationDate: '12/20/2016',
      upthDetails: [
        {
          status: 'CANC',
          reason: 'OTHR',
          effectiveDate: '2016-12-20',
          unpaid: '0',
          agentCode: '823',
          transactionDate: '2016-12-23'
        },
        {
          status: 'EXPI',
          reason: 'NBUS',
          effectiveDate: '2015-12-20',
          unpaid: '0',
          agentCode: '823',
          transactionDate: '2015-11-03'
        }
      ]
    },
    {
      number: '9331087781',
      type: 'P',
      unpaid: '0',
      carrier: '723',
      status: 'CANC',
      statusEffectiveDate: null,
      effectiveDate: '12/20/2014',
      expirationDate: '12/20/2015',
      upthDetails: [
        {
          status: 'CANC',
          reason: 'CANC',
          effectiveDate: '2015-12-20',
          unpaid: '0',
          agentCode: '723',
          transactionDate: '2015-12-21'
        },
        {
          status: 'ACAN',
          reason: 'CANC',
          effectiveDate: '2015-12-20',
          unpaid: '0',
          agentCode: '723',
          transactionDate: '2015-09-08'
        },
        {
          status: 'EXPI',
          reason: 'VADD',
          effectiveDate: '2014-12-20',
          unpaid: '0',
          agentCode: '723',
          transactionDate: '2014-12-09'
        },
        {
          status: 'EXPI',
          reason: 'RENW',
          effectiveDate: '2014-12-20',
          unpaid: '0',
          agentCode: '723',
          transactionDate: '2014-10-23'
        }
      ]
    },
    {
      number: '9331087781',
      type: 'P',
      unpaid: '0',
      carrier: '723',
      status: 'EXPI',
      statusEffectiveDate: '2013-12-20',
      effectiveDate: '12/20/2013',
      expirationDate: '12/20/2014',
      upthDetails: [
        {
          status: 'EXPI',
          reason: 'VADD',
          effectiveDate: '2014-11-30',
          unpaid: '0',
          agentCode: '723',
          transactionDate: '2014-12-08'
        },
        {
          status: 'EXPI',
          reason: 'RCAN',
          effectiveDate: '2013-12-20',
          unpaid: '0',
          agentCode: 'IST6',
          transactionDate: '2014-01-23'
        },
        {
          status: 'ACAN',
          reason: 'UNDW',
          effectiveDate: '2014-02-08',
          unpaid: '0',
          agentCode: 'IST6',
          transactionDate: '2014-01-20'
        },
        {
          status: 'EXPI',
          reason: 'NBUS',
          effectiveDate: '2013-12-20',
          unpaid: '0',
          agentCode: 'IST6',
          transactionDate: '2014-01-20'
        }
      ]
    }
  ],
  messages: [],
  firstLicenseOptions: []
};

export const RMV_NEW_DRIVER_MATTHEW: RmvDriverResult = {
  firstName: 'MATTHEW',
  lastName: 'LAUFER',
  middleName: 'R',
  dateOfBirth: '1986-02-17',
  gender: 'M',
  licenseNumber: '*********',
  licenseFirstDate: '2002-08-27',
  licenseState: 'MA',
  sdip: '00',
  unpaid: null,
  totalPremium: null,
  address1: '72 LEDGEBROOK RD',
  address2: '',
  city: 'WEYMOUTH',
  state: 'MA',
  zip: '021900000',
  licenseStatus: null,
  licenseExpirationDate: null,
  trainingDate: null,
  trainingStatus: false,
  policyNumber: '',
  policyStatus: null,
  policyCarrier: null,
  policyEffectiveDate: null,
  policyExpirationDate: null,
  resourceId: '222222222222222222222',
  incidents: [
    {
      incidentDate: '2014-08-19',
      surchargeDate: '2014-09-17',
      typeOfIncident: null,
      amountPaid: '0',
      descOfIncident: 'Speeding'
    }
  ],
  updDetails: [],
  messages: [],
  firstLicenseOptions: []
};

export const RMV_NEW_DRIVERS: RmvDriverResult[] = [
  RMV_NEW_DRIVER_GINA,
  RMV_NEW_DRIVER_MATTHEW
];
