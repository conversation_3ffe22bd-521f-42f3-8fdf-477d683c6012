import { Collection } from 'app/app-model/_common';
import { ApiResponse, MetaData } from './_common';


export class BasicPolicyInfo {
  constructor(
    public meta = { href: <string>'' },
    public location: string = '',
    public policyLimits: string = '',
    public underlyingLimits: string = '',
    public quoteSessionId: string = '',
    public resourceId: string = '',
    public resourceName: string = 'BasicPolicyInfo',
    public parentId: string = ''
  ) { }
}

export class BasicPolicyInfoApiResponse extends ApiResponse<BasicPolicyInfo> {}
