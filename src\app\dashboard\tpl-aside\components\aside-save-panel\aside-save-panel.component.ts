
import {first} from 'rxjs/operators';
import { SubscriptionLike as ISubscription } from 'rxjs';
import { Component, OnInit, ViewChild, ElementRef } from '@angular/core';
import { ApiService } from 'app/shared/services/api.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { OverlayRouteService } from 'app/overlay/services/overlay-route.service';
import { AgencyUserService } from 'app/shared/services/agency-user.service';
import { LeaveQuoteComponent } from 'app/shared/components/leave-quote/leave-quote.component';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { LeaveQuoteService } from 'app/shared/services/leave-quote.service';
import { ModalboxComponent } from 'app/shared/components/modalbox/modalbox.component';
import { TooltipComponent } from 'app/shared/modules/sm-popups/components/tooltip/tooltip.component';
import { ISendSuccesfull } from 'app/shared/components/send-to-bsc/send-to-bsc.component';
import { parseISO } from 'date-fns/parseISO';
import { format, isSameDay, subDays } from 'date-fns';


@Component({
    selector: 'app-aside-save-panel',
    templateUrl: './aside-save-panel.component.html',
    styleUrls: ['./aside-save-panel.component.scss'],
    standalone: false
})
export class AsideSavePanelComponent implements OnInit {
  @ViewChild('descriptionModal') public descriptionModal: ModalboxComponent;
  @ViewChild('descriptionTextarea') public descriptionTextarea: ElementRef;

  quoteUrl: string;
  private subscriptionQuote;
  // private subscriptionAgency;
  public description: string;
  public lastModifiedDate: any;
  private subscription: ISubscription;
  // private subscriptionSendToBsc;
  private quote;
  public isMaipArcQuote = false;

  // public data = {
  //   name: '',
  //   phone: '',
  //   subject: '',
  //   message: '',
  //   carrier: ''
  // };

  // @ViewChild('leaveQuote') leaveQuote: LeaveQuoteComponent;

  constructor(
    private storageService: StorageService,
    private quotesService: QuotesService,
    private overlayLoaderService: OverlayLoaderService,
    private overlayRouteService: OverlayRouteService,
    private agencyUserService: AgencyUserService,
    private apiService: ApiService,
    private apiCommonService: ApiCommonService,
    private leaveQuoteService: LeaveQuoteService
  ) {}

  ngOnInit() {
    this.checkIfMaipArcQuote();
    // this.getAgentData();
    this.subscription = this.storageService
      .getStorageData('selectedQuote')
      .subscribe(res => {
        this.quote = res;
        if (this.quote.lastModifiedDate) {
          this.lastModifiedDate = this.parseLastModifiedDate(
            this.quote.lastModifiedDate
          );
        } else {
          this.lastModifiedDate = false;
        }

        if (res && res.meta && res.meta.href) {
          this.quoteUrl = res.meta.href;
        }

      });
  }

  ngOnDestroy() {
    this.subscription && this.subscription.unsubscribe();
    this.subscriptionQuote && this.subscriptionQuote.unsubscribe();
    // this.subscriptionAgency && this.subscriptionAgency.unsubscribe();

  }

  private checkIfMaipArcQuote(): void {
    // const tmpIsMaipArc = localStorage.getItem('maipArcQuote');
    const tmpIsMaipArc = sessionStorage.getItem('maipArcQuote');
    if (tmpIsMaipArc) {
      const isMaipArc = JSON.parse(tmpIsMaipArc);
      this.isMaipArcQuote = isMaipArc.isMaipArcQuote;
    }
  }

  public saveQuote() {
    this.quotesService.saveQuoteCompleteProcess(this.quote);
  }

  public saveAsQuote(description, refModal) {
    const quote = JSON.parse(JSON.stringify(this.quote));
    quote.description = description || '';
    this.overlayLoaderService.showLoader('Saving As...');
    this.quotesService
      .saveAsQuote(quote.meta.href, quote).pipe(
      first())
      .subscribe(
        res => {
          this.storageService.setStorageData('selectedQuote', res);
          this.leaveQuoteService.saveStorageQuoteDataSnapshot();
        },
        err => {},
        () => {
          this.overlayLoaderService.hideLoader();
          refModal.closeModalbox();
        }
      );
  }

  public actionOnSaveAs(ev: Event): void {
    ev.preventDefault();

    if (this.descriptionModal) {
      this.description = '';
      this.descriptionModal.open();

      if (this.descriptionTextarea && this.descriptionTextarea.nativeElement) {
        this.focus(this.descriptionTextarea.nativeElement);
      }
    }
  }

  public createNewQuoteSession(refModal) {
    const quote = JSON.parse(JSON.stringify(this.quote));
    this.overlayLoaderService.showLoader('Creating new session...');
    this.quotesService
      .createNewQuoteSession(quote).pipe(
      first())
      .subscribe(
        res => {

         /* let urlSubStr = "";
          if (res.lob === "AUTOP") {
            urlSubStr = "auto";
          } else if (res.lob === "HOME") {
            urlSubStr = "home";
          } else if (res.lob === "PUMBR") {
            urlSubStr = "umbrella";
          } else if (res.lob === "DWELL") {
            urlSubStr = "dwelling";
          }*/

          const newQuoteSessionURL =
            '#/dashboard/quotes/' + res.quoteSessionId;

         // let currentUrl = this.apiService.sprAppUrl(newQuoteSessionURL);

          window.open(newQuoteSessionURL, '_blank');
        },
        err => {},
        () => {
          this.overlayLoaderService.hideLoader();
          refModal.closeModalbox();
        }
      );
  }

  public focus(element) {
    setTimeout(() => {
      element.focus();
    }, 0);
  }

private parseLastModifiedDate(lastModifiedDate: string): string {
  const now = new Date();
  const yesterday = subDays(now, 1);
  const lastModified = parseISO(lastModifiedDate);

  if (isSameDay(now, lastModified)) {
    return 'Today at ' + format(lastModified, 'h:mm a');
  } else if (isSameDay(yesterday, lastModified)) {
    return 'Yesterday at ' + format(lastModified, 'h:mm a');
  } else {
    return format(lastModified, 'EEEE, MMMM d, yyyy [at] h:mm a');
  }
}

  public closeTooltipWithDelay(refTooltip: TooltipComponent): void {
    if (refTooltip) {
      setTimeout(() => {
        refTooltip.close();
      });
    }
  }

  public onSendToBscConfirmation(refModalBox: ModalboxComponent): void {
    if (refModalBox) {
      refModalBox.close();
    }
  }

  public onSendToBscCancel(refModalBox: ModalboxComponent): void {
    if (refModalBox) {
      refModalBox.close();
    }
  }


  // TEST --------
  public testCanLeaveQuote(ev: Event): void {
    ev.preventDefault();

    console.log('Check if can leave the quote');
    this.leaveQuoteService.checkIfTheQuoteIsSaved();
  }

  public fakeSave(ev: Event): void {
    ev.preventDefault();
    console.log('Fake Save');
    this.leaveQuoteService.saveStorageQuoteDataSnapshot();
  }

}
