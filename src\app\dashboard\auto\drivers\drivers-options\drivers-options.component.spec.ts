import { StorageService } from 'app/shared/services/storage-new.service';
import { MockActivatedRouteProvider } from 'testing/stubs/activated-route.provider';
import { StubOptionsViewComponent } from 'testing/stubs/components/options-view.component';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { DriversOptionsComponent } from './drivers-options.component';

describe('DriversOptionsComponent', () => {
  let component: DriversOptionsComponent;
  let fixture: ComponentFixture<DriversOptionsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [
        DriversOptionsComponent,
        StubOptionsViewComponent
      ],
      providers: [
        MockActivatedRouteProvider,
        StorageService
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DriversOptionsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });
});
