import { By } from '@angular/platform-browser';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { StubModalboxComponent } from 'testing/stubs/components/modalbox.component';

import { QuoteInboxComponent } from './quote-inbox.component';

describe('Component: QuoteInbox', () => {
  let component: QuoteInboxComponent;
  let fixture: ComponentFixture<QuoteInboxComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [
        QuoteInboxComponent,
        StubModalboxComponent
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(QuoteInboxComponent);
    component = fixture.componentInstance;
    // component.inCurrentRelease = true;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should allow to delete selected item from details modal', () => {
    const record = component.quoteInbox[0];

    const link = fixture.debugElement.query(By.css('.modal-launcher-quote-inbox'));
    link.triggerEventHandler('click', {});

    fixture.detectChanges();

    const btn = fixture.debugElement.query(By.directive(StubModalboxComponent)).query(By.css('.o-btn--outlined'));
    btn.triggerEventHandler('click', {});

    fixture.detectChanges();

    expect(component.quoteInbox).not.toContain(record);
  });
});
