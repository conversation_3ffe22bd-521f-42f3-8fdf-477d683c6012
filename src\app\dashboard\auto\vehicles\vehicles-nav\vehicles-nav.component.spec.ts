import { async, ComponentFixture, fakeAsync, inject, TestBed, tick } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';

import { VEHICLES } from 'testing/data/quotes/vehicles';
import { StubTooltipComponent } from 'testing/stubs/components/tooltip.component';

import { VehiclesService } from 'app/dashboard/app-services/vehicles.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Helpers } from 'app/utils/helpers';

import { VehiclesNavComponent } from './vehicles-nav.component';
import { StubVehiclesServiceProvider } from 'testing/stubs/services/vehicles.service.provider';

describe('Component: VehiclesNav', () => {
  let component: VehiclesNavComponent;
  let fixture: ComponentFixture<VehiclesNavComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      declarations: [ VehiclesNavComponent, StubTooltipComponent ],
      providers: [
        StubVehiclesServiceProvider,
        StorageService
      ]
    })
    .compileComponents();
  }));

  beforeEach(fakeAsync(inject([StorageService], (storageService: StorageService) => {
    storageService.setStorageData('vehiclesList', Helpers.deepClone(VEHICLES.items));

    fixture = TestBed.createComponent(VehiclesNavComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
    tick();
  })));

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
