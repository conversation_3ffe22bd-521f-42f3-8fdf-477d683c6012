import { AgencyUserService } from './../../shared/services/agency-user.service';
import { CoveredAutoSymbolsSpecApiResponseData, CoveredAutoSymbolsApiResponseData, CoveredAutoSymbols, CoveredAutoSymbolItem } from 'app/app-model/com-covered-auto-symbols';
import { Injectable } from '@angular/core';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { Observable,  throwError as throwError } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { HttpErrorResponse, HttpClient, HttpHeaders } from '@angular/common/http';
import { Quote } from 'app/app-model/quote';

@Injectable()
export class ComCoveredAutoSymbolsService {
  private symbolsSpecApiResponseData: CoveredAutoSymbolsSpecApiResponseData;
  private symbolsApiResponseData: CoveredAutoSymbolsApiResponseData;
  private coveredAutoSymbols: CoveredAutoSymbols;

  //private specUri = 'assets/data/covered-auto-symbols-spec.json';
  private specUri = '/specs/coveredAutoSymbols';
  private coverageAutoSymbolsBaseUri = '/quotes/id/coveredAutoSymbols';

  constructor(
    private apiCommonService: ApiCommonService,
    private agencyUserService: AgencyUserService,
    private httpClient: HttpClient
  ) { }

    // Get Spec coverage for coverage group: 'PolicyStandardCoverage'
    public GetSymbolsSpecByUri() {
     // return this.httpClient.get(this.specUri)
     return this.apiCommonService.getByUri(this.specUri)
      .pipe(
        catchError(this.handleError)
      );
    }

        // Get Covered Auto Symbols
        public GetSymbols(quoteId: string): Observable<CoveredAutoSymbolsApiResponseData> {
          const uri = this.coverageAutoSymbolsBaseUri.replace(/id/gi, quoteId);
          return this.apiCommonService.getByUri(uri);
        }

      // Create Covered Auto Symbols
      public CreateSymbols(quoteId: string, data: any= {}): Observable<any> {
        const uri = this.coverageAutoSymbolsBaseUri.replace(/id/gi, quoteId);
        return this.apiCommonService.postByUri(uri, data);
      }

      // Update Covered Auto Symbols
      public UpdateSymbols(quoteId: string, data: CoveredAutoSymbols): Observable<CoveredAutoSymbols> {
        const uri = data.meta.href;
        return this.apiCommonService.putByUri(uri, data);
      }

      // Delete Covered Auto Symbols
      public DeleteSymbols(quoteId: string): Observable<any> {
        const uri = this.coverageAutoSymbolsBaseUri.replace(/id/gi, quoteId);
        return this.apiCommonService.deleteByUri(uri);
      }

    //#region Private Methods
    private handleError(err: HttpErrorResponse) {
      let errorMessage = '';
      if (err.error instanceof ErrorEvent) {
        errorMessage = `An error occurred: ${err.error.message}`;
      } else {
        errorMessage = `Server returned code: ${err.status}, error message is: ${err.message}`;
      }

      console.log(errorMessage);

      return throwError(errorMessage);
    }
    //#endregion


  // ---------------------------------------------------------------------------
  public buildInitialSymbolsWithDefaultValue(specSymbols: CoveredAutoSymbols): CoveredAutoSymbols {
    const coveredAutoSymbols: CoveredAutoSymbols = new CoveredAutoSymbols();

    let agencyId = '';
    this.agencyUserService.userData$.subscribe(x => agencyId = x.agencyId)
    let uri = `/subs/${agencyId}/symbolDefaults`;
    this.apiCommonService.getByUri(uri).subscribe(data => {
      data.items.forEach(symbol => {
        coveredAutoSymbols.symbols.push(symbol);
      });
    })
  /*  specSymbols.symbols.forEach(s => {
      const coveredAutoSymbolItem: CoveredAutoSymbolItem = new CoveredAutoSymbolItem();
      coveredAutoSymbolItem.coveredAutoSymbolType = s.coveredAutoSymbolType;
      coveredAutoSymbolItem.coveredAutoSymbolValues = ['7']; // This is default value for all symbols
      coveredAutoSymbolItem.linkedCoverageCode = s.linkedCoverageCode;
      coveredAutoSymbols.symbols.push(coveredAutoSymbolItem);
    });
    */
    return coveredAutoSymbols;
  }

  public createSymbolsWithDefaults(quote: Quote, specCoveredAutoSymbols: CoveredAutoSymbols): Promise<CoveredAutoSymbols> {
    return new Promise((resolve, reject) => {
      const coveredAutoSymbols: CoveredAutoSymbols = this.buildInitialSymbolsWithDefaultValue(specCoveredAutoSymbols)
      setTimeout(() => {
        this.CreateSymbols(quote.resourceId, coveredAutoSymbols).subscribe((symbols: CoveredAutoSymbols) => {
        resolve(symbols);
      }, err => reject(err));
      },1000)

    })
  }

  /*
public createPolicyInfoCoveragesWithDefaults(quote: Quote, specQuoteCoverages: CoverageItem[]): Promise<CoveragesData> {
    return new Promise((resolve, reject) => {
      const coverageData: CoveragesData = this.buildInitialQuoteCoverageFromSpecWithDefault(specQuoteCoverages);
      this.CreateQuoteCoverages(quote.resourceId, coverageData)
        .subscribe((res: CoveragesData) => {
          resolve(res);
        }, err => reject(err))
    });
  }
  */
}
