import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';

import { StubRateServiceProvider } from 'testing/stubs/services/rate.service.provider';

import { StorageService } from 'app/shared/services/storage-new.service';

import { HomeOptionsNavComponent } from './home-options-nav.component';

describe('HomeOptionsNavComponent', () => {
  let component: HomeOptionsNavComponent;
  let fixture: ComponentFixture<HomeOptionsNavComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      declarations: [HomeOptionsNavComponent],
      providers: [
        StorageService,
        StubRateServiceProvider
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(HomeOptionsNavComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });
});
