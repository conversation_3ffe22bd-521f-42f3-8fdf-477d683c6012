import { async, ComponentFixture, fakeAsync, inject, TestBed, tick } from '@angular/core/testing';
import { ActivatedRoute, Router } from '@angular/router';
import { RouterTestingModule } from '@angular/router/testing';

import { Observable } from 'rxjs';

import { data as AGENCY_USER } from 'testing/data/agencies/user';
import { DWELLING_PROTECTION_CLASS } from 'testing/data/dwellings/protection-class';
import { CLIENTS } from 'testing/data/quotes/clients';
import { HOME_STANDARD_COVERAGES } from 'testing/data/quotes/coverages/home-standard';
import { data as DRIVERS_DATA } from 'testing/data/quotes/drivers';
import { DWELLINGS } from 'testing/data/quotes/dwellings';
import { data as GUIDELINE_OVERRIDES_DATA } from 'testing/data/quotes/guideline-overrides';
import { DWELLING_LOCATION } from 'testing/data/quotes/locations/dwelling';
import { data as LOSS_HISTORY_DATA } from 'testing/data/quotes/loss-history';
import { data as POLICY_HISTORY_DATA } from 'testing/data/quotes/policy-history';
import { AUTO_QUOTE as QUOTE_DATA_AUTOP } from 'testing/data/quotes/quote-auto';
import { HOME_QUOTE } from 'testing/data/quotes/quote-home';
import { AUTO_QUOTE_PLAN_LIST as PLAN_LIST_DATA } from 'testing/data/quotes/quote-plan-list/auto';
import { VEHICLES as VEHICLES_DATA } from 'testing/data/quotes/vehicles';
import { data as RMV_QUOTE_DATA_DATA } from 'testing/data/rmv-quote-data';
import { HOME_COVERAGE_DEFAULTS } from 'testing/data/subs/coverage-defaults/home-policy';
import { data as VEHICLE_ADDITIONAL_OPTIONS } from 'testing/data/vehicles/additional-options';
import { data as VEHICLE_STANDARD_COVERAGES_DATA } from 'testing/data/vehicles/standard-coverages';
import { expectLastCallArgs } from 'testing/helpers/data-expect';
import {
    MockActivatedRoute, MockActivatedRouteProvider
} from 'testing/stubs/activated-route.provider';
import { StubModalboxComponent } from 'testing/stubs/components/modalbox.component';
import {
    StubUserSubscriptionDirective
} from 'testing/stubs/directives/user-subscription.directive';
import {
    StubAgencyUserService, StubAgencyUserServiceProvider
} from 'testing/stubs/services/agency-user.service.provider';
import {
    StubClientsService, StubClientsServiceProvider
} from 'testing/stubs/services/clients.service.provider';
import {
    StubCoveragesService, StubCoveragesServiceProvider
} from 'testing/stubs/services/coverages.service.provider';
import {
    StubDwellingService, StubDwellingServiceProvider
} from 'testing/stubs/services/dwelling.service.provider';
import {
    StubLocationsService, StubLocationsServiceProvider
} from 'testing/stubs/services/locations.service.provider';
import { StubLookupsServiceProvider } from 'testing/stubs/services/lookups.service.provider';
import { StubOptionsServiceProvider } from 'testing/stubs/services/options.service.provider';
import {
    StubOverlayLoaderServiceProvider
} from 'testing/stubs/services/overlay-loader.service.provider';
import { StubPremiumsServiceProvider } from 'testing/stubs/services/premiums.service.provider';
import {
    StubQuotesService, StubQuotesServiceProvider
} from 'testing/stubs/services/quotes.service.provider';
import { StubSpecsServiceProvider } from 'testing/stubs/services/specs.service.provider';
import {
    StubSubsService, StubSubsServiceProvider
} from 'testing/stubs/services/subs.service.provider';
import {
    StubVehiclesService, StubVehiclesServiceProvider
} from 'testing/stubs/services/vehicles.service.provider';

import { ClientsService } from 'app/dashboard/app-services/clients.service';
import { CoveragesService } from 'app/dashboard/app-services/coverages.service';
import { DwellingService } from 'app/dashboard/app-services/dwelling.service';
import { LocationsService } from 'app/dashboard/app-services/locations.service';
import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { SubsService } from 'app/dashboard/app-services/subs.service';
import { VehiclesService } from 'app/dashboard/app-services/vehicles.service';
import {
    OverlayClientQuoteInfoService
} from 'app/overlay/services/overlay-client-quote-info.service';
import { AgencyUserService } from 'app/shared/services/agency-user.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Helpers } from 'app/utils/helpers';

import { QuoteLoaderComponent } from './quote-loader.component';
import { CurrentPageService } from 'app/shared/services/current-page.service';
import { QUOTE_COVERAGE_LEVELS } from 'testing/data/subs/quote-coverage-levels';

class StubOverlayClientQuoteInfoService {
  resetData() { }
}


describe('Component: QuoteLoader', () => {
  let component: QuoteLoaderComponent;
  let fixture: ComponentFixture<QuoteLoaderComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [ RouterTestingModule ],
      declarations: [
        QuoteLoaderComponent,
        StubUserSubscriptionDirective,
        StubModalboxComponent
      ],
      providers: [
        StorageService,
        StubQuotesServiceProvider,
        StubOverlayLoaderServiceProvider,
        StubClientsServiceProvider,
        {
          provide: OverlayClientQuoteInfoService,
          useClass: StubOverlayClientQuoteInfoService
        },
        StubAgencyUserServiceProvider,
        StubDwellingServiceProvider,
        StubPremiumsServiceProvider,
        StubVehiclesServiceProvider,
        StubLocationsServiceProvider,
        MockActivatedRouteProvider,
        StubCoveragesServiceProvider,
        StubLookupsServiceProvider,
        StubSubsServiceProvider,
        StubSpecsServiceProvider,
        StubOptionsServiceProvider,
        StorageGlobalService,
        CurrentPageService
      ]
    })
    .compileComponents();
  }));

  describe('when default data is available', () => {
    beforeEach(fakeAsync(() => {
      fixture = TestBed.createComponent(QuoteLoaderComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
      tick();
    }));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });

    it('should react to "id" parameter change in URL and load auto quote', fakeAsync(inject(
      [ActivatedRoute, StorageService, QuotesService, ClientsService, AgencyUserService, Router, VehiclesService, ApiCommonService],
      (activatedRoute: MockActivatedRoute, storageService: StorageService,
        quotesService: StubQuotesService, clientsService: StubClientsService,
        agencyUserService: StubAgencyUserService, router: Router,
        vehiclesService: StubVehiclesService, apiCommonService: ApiCommonService) => {
        clientsService.clients = Helpers.deepClone(CLIENTS);
        agencyUserService.userData = Helpers.deepClone(AGENCY_USER);
        vehiclesService.vehicles = Helpers.deepClone(VEHICLES_DATA);

        spyOn(router, 'navigate');
        spyOn(quotesService, 'getDataByUrl').and.returnValues(
          Observable.of(Helpers.deepClone(QUOTE_DATA_AUTOP)),
          Observable.of(Helpers.deepClone(PLAN_LIST_DATA)),
          Observable.of(Helpers.deepClone(DRIVERS_DATA))
        );
        spyOn(apiCommonService, 'getByUri').and.returnValues(
          Observable.of(Helpers.deepClone(VEHICLE_STANDARD_COVERAGES_DATA)),
          Observable.of(Helpers.deepClone(VEHICLE_ADDITIONAL_OPTIONS)),
          Observable.of(Helpers.deepClone(VEHICLE_STANDARD_COVERAGES_DATA)),
          Observable.of(Helpers.deepClone(VEHICLE_ADDITIONAL_OPTIONS))
        );

        activatedRoute.params.next({
          id: 'fe3d5969-8a96-40eb-97a7-7c96e74eeec3'
        });

        fixture.detectChanges();
        tick();

        fixture.detectChanges();
        tick();

        expect(router.navigate).toHaveBeenCalled();
      })));

    it('should navigate to drivers tab if there are no drivers', fakeAsync(inject(
      [ActivatedRoute, QuotesService, ClientsService, AgencyUserService, Router, ApiCommonService],
      (activatedRoute: MockActivatedRoute, quotesService: StubQuotesService,
        clientsService: StubClientsService, agencyUserService: StubAgencyUserService,
        router: Router, apiCommonService: ApiCommonService) => {
        clientsService.clients = Helpers.deepClone(CLIENTS);
        agencyUserService.userData = Helpers.deepClone(AGENCY_USER);

        spyOn(router, 'navigate');
        spyOn(quotesService, 'getDataByUrl').and.returnValues(
          Observable.of(Helpers.deepClone(QUOTE_DATA_AUTOP)),
          Observable.of(Helpers.deepClone(PLAN_LIST_DATA)),
          Observable.of({
            items: []
          })
        );
        spyOn(apiCommonService, 'getByUri').and.returnValues(
          Observable.of(Helpers.deepClone(QUOTE_COVERAGE_LEVELS)),
          Observable.of(Helpers.deepClone(VEHICLE_STANDARD_COVERAGES_DATA)),
          Observable.of(Helpers.deepClone(VEHICLE_ADDITIONAL_OPTIONS))
        );

        activatedRoute.params.next({
          id: 'fe3d5969-8a96-40eb-97a7-7c96e74eeec3'
        });

        fixture.detectChanges();
        tick();

        fixture.detectChanges();
        tick();

        expect((<jasmine.Spy>router.navigate).calls.mostRecent().args[0][0]).toContain('/drivers');
      })));

    it('should mark quote as new when it is from RMV', fakeAsync(inject(
      [ActivatedRoute, StorageService, ClientsService, QuotesService, AgencyUserService, Router, ApiCommonService],
      (activatedRoute: MockActivatedRoute, storageService: StorageService,
        clientsService: StubClientsService, quotesService: StubQuotesService,
        agencyUserService: StubAgencyUserService, router: Router,
        apiCommonService: ApiCommonService) => {
        storageService.setStorageData('newRmvQuoteData', Helpers.deepClone(RMV_QUOTE_DATA_DATA));
        clientsService.clients = Helpers.deepClone(CLIENTS);
        agencyUserService.userData = Helpers.deepClone(AGENCY_USER);

        spyOn(router, 'navigate');
        spyOn(quotesService, 'getDataByUrl').and.returnValues(
          Observable.of(Helpers.deepClone(QUOTE_DATA_AUTOP)),
          Observable.of({ items: [] }),
          Observable.of(Helpers.deepClone(DRIVERS_DATA))
        );
        spyOn(apiCommonService, 'getByUri').and.returnValues(
          Observable.of(Helpers.deepClone(QUOTE_COVERAGE_LEVELS)),
          Observable.of(Helpers.deepClone(VEHICLE_STANDARD_COVERAGES_DATA)),
          Observable.of(Helpers.deepClone(VEHICLE_ADDITIONAL_OPTIONS))
        );

        activatedRoute.params.next({
          id: 'fe3d5969-8a96-40eb-97a7-7c96e74eeec3'
        });

        fixture.detectChanges();
        tick();

        fixture.detectChanges();
        tick();

        expect(router.navigate).toHaveBeenCalled();
      })));

    it('should react to "id" parameter change in URL and load home quote', fakeAsync(inject(
      [ActivatedRoute, StorageService, QuotesService, ClientsService, DwellingService, Router,
        AgencyUserService, CoveragesService],
      (activatedRoute: MockActivatedRoute, storageService: StorageService,
        quotesService: StubQuotesService, clientsService: StubClientsService,
        dwellingService: StubDwellingService, router: Router,
        agencyUserService: StubAgencyUserService, coveragesService: StubCoveragesService) => {
        clientsService.clients = Helpers.deepClone(CLIENTS);
        dwellingService.dwellings = Helpers.deepClone(DWELLINGS);
        dwellingService.dwellingProtectionClass = Helpers.deepClone(DWELLING_PROTECTION_CLASS);
        quotesService.guidelineOverrides = {
          first: null,
          last: null,
          limit: null,
          meta: {href: ''},
          next: null,
          offset: null,
          previous: null,
          resourceName: '',
          size: null,
          items: []
        };
        agencyUserService.userData = Helpers.deepClone(AGENCY_USER);
        coveragesService.homeQuoteCoverages = Helpers.deepClone(HOME_STANDARD_COVERAGES);

        spyOn(router, 'navigate');

        spyOn(quotesService, 'getDataByUrl').and.returnValues(
          Observable.of(Helpers.deepClone(HOME_QUOTE)),
          Observable.of(Helpers.deepClone(PLAN_LIST_DATA))
        );

        activatedRoute.params.next({
          id: 'fe3d5969-8a96-40eb-97a7-7c96e74eeec3'
        });

        fixture.detectChanges();
        tick();

        fixture.detectChanges();
        tick();

        expect(router.navigate).toHaveBeenCalled();
      })));

    it('should ignore URL change if there is no "id" param', fakeAsync(inject(
      [ActivatedRoute, StorageService, AgencyUserService, Router],
      (activatedRoute: MockActivatedRoute, storageService: StorageService, agencyUserService: StubAgencyUserService, router: Router) => {
        agencyUserService.userData = Helpers.deepClone(AGENCY_USER);
        spyOn(router, 'navigate');

        activatedRoute.params.next({});

        fixture.detectChanges();
        tick();

        fixture.detectChanges();
        tick();

        expect(router.navigate).not.toHaveBeenCalled();
      })));

    it('should be able to handle quote loading error', fakeAsync(inject(
      [ActivatedRoute, StorageService, QuotesService, ClientsService, Router],
      (activatedRoute: MockActivatedRoute, storageService: StorageService,
        quotesService: StubQuotesService, clientsService: StubClientsService, router: Router) => {
        clientsService.clients = Helpers.deepClone(CLIENTS);

        spyOn(router, 'navigate');

        spyOn(quotesService, 'getDataByUrl').and.returnValues(
          Observable.throw(new Error('error!'))
        );

        activatedRoute.params.next({
          id: 'fe3d5969-8a96-40eb-97a7-7c96e74eeec3'
        });

        fixture.detectChanges();
        tick();

        fixture.detectChanges();
        tick();

        expect(router.navigate).not.toHaveBeenCalled();
      })));

    it('should react to "id" parameter change in URL and load auto quote even when no plans are available', fakeAsync(inject(
      [ActivatedRoute, StorageGlobalService, QuotesService, ClientsService, AgencyUserService, Router, ApiCommonService],
      (activatedRoute: MockActivatedRoute, storageGlobalService: StorageGlobalService,
        quotesService: StubQuotesService, clientsService: StubClientsService,
        agencyUserService: StubAgencyUserService, router: Router,
        apiCommonService: ApiCommonService) => {
        agencyUserService.userData = Helpers.deepClone(AGENCY_USER);
        clientsService.clients = Helpers.deepClone(CLIENTS);
        storageGlobalService.setSubs('plans', Helpers.deepClone(PLAN_LIST_DATA.items));

        spyOn(router, 'navigate');

        spyOn(quotesService, 'getDataByUrl').and.returnValues(
          Observable.of(Helpers.deepClone(QUOTE_DATA_AUTOP)),
          Observable.of({ items: [] }),
          Observable.of(Helpers.deepClone(DRIVERS_DATA))
        );

        spyOn(apiCommonService, 'getByUri').and.returnValues(
          Observable.of(Helpers.deepClone(QUOTE_COVERAGE_LEVELS)),
          Observable.of(Helpers.deepClone(VEHICLE_STANDARD_COVERAGES_DATA)),
          Observable.of(Helpers.deepClone(VEHICLE_ADDITIONAL_OPTIONS))
        );

        activatedRoute.params.next({
          id: 'fe3d5969-8a96-40eb-97a7-7c96e74eeec3'
        });

        fixture.detectChanges();
        tick();

        fixture.detectChanges();
        tick();

        expect(router.navigate).toHaveBeenCalled();
      })));

    it('should react to "id" parameter change in URL and load home quote when there is dwelling location available', fakeAsync(inject(
      [ActivatedRoute, StorageService, QuotesService, ClientsService, DwellingService, Router,
        AgencyUserService, CoveragesService],
      (activatedRoute: MockActivatedRoute, storageService: StorageService,
        quotesService: StubQuotesService, clientsService: StubClientsService,
        dwellingService: StubDwellingService, router: Router,
        agencyUserService: StubAgencyUserService, coveragesService: StubCoveragesService) => {
        clientsService.clients = Helpers.deepClone(CLIENTS);
        dwellingService.dwellings = Helpers.deepClone(DWELLINGS);
        dwellingService.dwellingLocation = Helpers.deepClone(DWELLING_LOCATION);
        quotesService.guidelineOverrides = {
          first: null,
          last: null,
          limit: null,
          meta: { href: '' },
          next: null,
          offset: null,
          previous: null,
          resourceName: '',
          size: null,
          items: []
        };
        agencyUserService.userData = Helpers.deepClone(AGENCY_USER);
        coveragesService.homeQuoteCoverages = Helpers.deepClone(HOME_STANDARD_COVERAGES);

        spyOn(router, 'navigate');

        spyOn(quotesService, 'getDataByUrl').and.returnValues(
          Observable.of(Helpers.deepClone(HOME_QUOTE)),
          Observable.of(Helpers.deepClone(PLAN_LIST_DATA))
        );

        activatedRoute.params.next({
          id: 'fe3d5969-8a96-40eb-97a7-7c96e74eeec3'
        });

        fixture.detectChanges();
        tick();

        fixture.detectChanges();
        tick();

        expect(router.navigate).toHaveBeenCalled();
      })));

    it('should react to "id" parameter change in URL and load home quote when there is dwelling loss history available', fakeAsync(inject(
      [ActivatedRoute, StorageService, QuotesService, ClientsService, DwellingService, Router, SubsService,
        AgencyUserService, CoveragesService],
      (activatedRoute: MockActivatedRoute, storageService: StorageService,
        quotesService: StubQuotesService, clientsService: StubClientsService,
        dwellingService: StubDwellingService, router: Router,
        subsService: StubSubsService, agencyUserService: StubAgencyUserService,
        coveragesService: StubCoveragesService,
      ) => {
        agencyUserService.userData = Helpers.deepClone(AGENCY_USER);
        clientsService.clients = Helpers.deepClone(CLIENTS);
        dwellingService.dwellings = Helpers.deepClone(DWELLINGS);
        dwellingService.lossHistory = Helpers.deepClone(LOSS_HISTORY_DATA);
        quotesService.guidelineOverrides = {
          first: null,
          last: null,
          limit: null,
          meta: { href: '' },
          next: null,
          offset: null,
          previous: null,
          resourceName: '',
          size: null,
          items: []
        };
        subsService.defaultHomeCarrierOptions = Helpers.deepClone(HOME_COVERAGE_DEFAULTS);
        coveragesService.homeQuoteCoverages = Helpers.deepClone(HOME_STANDARD_COVERAGES);

        spyOn(router, 'navigate');

        spyOn(quotesService, 'getDataByUrl').and.returnValues(
          Observable.of(Helpers.deepClone(HOME_QUOTE)),
          Observable.of(Helpers.deepClone(PLAN_LIST_DATA))
        );

        activatedRoute.params.next({
          id: 'fe3d5969-8a96-40eb-97a7-7c96e74eeec3'
        });

        fixture.detectChanges();
        tick();

        fixture.detectChanges();
        tick();

        expect(router.navigate).toHaveBeenCalled();
      })));

    it('should react to "id" parameter change in URL and load home quote when there are guidelines overrides available', fakeAsync(inject(
      [ActivatedRoute, StorageService, QuotesService, ClientsService, DwellingService, Router, AgencyUserService, CoveragesService],
      (activatedRoute: MockActivatedRoute, storageService: StorageService,
        quotesService: StubQuotesService, clientsService: StubClientsService,
        dwellingService: StubDwellingService, router: Router,
        agencyUserService: StubAgencyUserService, coveragesService: StubCoveragesService) => {
        clientsService.clients = Helpers.deepClone(CLIENTS);
        dwellingService.dwellings = Helpers.deepClone(DWELLINGS);
        quotesService.guidelineOverrides = Helpers.deepClone(GUIDELINE_OVERRIDES_DATA);
        agencyUserService.userData = Helpers.deepClone(AGENCY_USER);
        coveragesService.homeQuoteCoverages = Helpers.deepClone(HOME_STANDARD_COVERAGES);

        spyOn(router, 'navigate');

        spyOn(quotesService, 'getDataByUrl').and.returnValues(
          Observable.of(Helpers.deepClone(HOME_QUOTE)),
          Observable.of(Helpers.deepClone(PLAN_LIST_DATA))
        );

        activatedRoute.params.next({
          id: 'fe3d5969-8a96-40eb-97a7-7c96e74eeec3'
        });

        fixture.detectChanges();
        tick();

        fixture.detectChanges();
        tick();

        expect(router.navigate).toHaveBeenCalled();
      })));

    it('should react to "id" parameter change in URL and route to drivers tab if there are vehicle data available', fakeAsync(inject(
      [ActivatedRoute, CoveragesService, QuotesService, ClientsService, DwellingService, LocationsService, AgencyUserService, Router,
        VehiclesService, ApiCommonService],
      (activatedRoute: MockActivatedRoute, coveragesService: StubCoveragesService,
        quotesService: StubQuotesService, clientsService: StubClientsService,
        dwellingService: StubDwellingService, locationsService: StubLocationsService, agencyUserService: StubAgencyUserService,
        router: Router, vehiclesService: StubVehiclesService, apiCommonService: ApiCommonService) => {
        agencyUserService.userData = Helpers.deepClone(AGENCY_USER);
        clientsService.clients = Helpers.deepClone(CLIENTS);
        dwellingService.dwellings = Helpers.deepClone(DWELLINGS);
        vehiclesService.vehicles = Helpers.deepClone(VEHICLES_DATA);
        coveragesService.vehicleCoverage = Helpers.deepClone(VEHICLE_STANDARD_COVERAGES_DATA);
        quotesService.policyHistory = Helpers.deepClone(POLICY_HISTORY_DATA);

        spyOn(router, 'navigate');

        spyOn(quotesService, 'getDataByUrl').and.returnValues(
          Observable.of(Helpers.deepClone(QUOTE_DATA_AUTOP)),
          Observable.of(Helpers.deepClone(PLAN_LIST_DATA)),
          Observable.of(Helpers.deepClone(DRIVERS_DATA))
        );

        spyOn(apiCommonService, 'getByUri').and.returnValues(
          Observable.of(Helpers.deepClone(VEHICLE_STANDARD_COVERAGES_DATA)),
          Observable.of(Helpers.deepClone(VEHICLE_ADDITIONAL_OPTIONS)),
          Observable.of(Helpers.deepClone(VEHICLE_STANDARD_COVERAGES_DATA)),
          Observable.of(Helpers.deepClone(VEHICLE_ADDITIONAL_OPTIONS))
        );

        activatedRoute.params.next({
          id: 'fe3d5969-8a96-40eb-97a7-7c96e74eeec3'
        });

        fixture.detectChanges();
        tick();

        fixture.detectChanges();
        tick();

        expect(router.navigate).toHaveBeenCalled();
      })));
  });

  // TODO: add tests for when UserSubscriptionDirective returns something else than 'All systems checked, please proceed'

  // describe('when importedQuote setting is on', () => {
  //   beforeEach(inject([QuotesService], (quotesService: StubQuotesService) => {
  //     quotesService.importedQuote = true;
  //     spyOn(quotesService, 'setIsImportedQuote');

  //     fixture = TestBed.createComponent(QuoteLoaderComponent);
  //     component = fixture.componentInstance;
  //     fixture.detectChanges();
  //   }));

  //   it('should honor the setting', inject([QuotesService], (quotesService: StubQuotesService) => {
  //     expectLastCallArgs(quotesService.setIsImportedQuote).toEqual([]);
  //   }));
  // });
});
