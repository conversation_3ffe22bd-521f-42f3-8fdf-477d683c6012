<section class="section section--compact u-spacing--2-5">
  <div class="row">
    <div class="col-xs-12">
      <h1 class="o-heading">Dwelling Information</h1>
    </div>
  </div>

  <div class="row u-spacing--1">
    <div class="col-xs-12">
      <div class="box box--silver">
        <div class="row o-columns">
          <div class="col-xs-6">
            <table class="form-table">

              <tr class="form-table__row" [ngClass]="{'is-required-field': isRequiredDwellingAddress()}">
                <td class="form-table__cell u-width-120px">
                  <label for="">Address:</label>
                </td>
                <td class="form-table__cell u-width-180px">
                  <input
                    #refDwellingAddress="ngModel"
                    fieldAutofocus
                    type="text"
                    id="dwellingAddress"
                    name="dwellingAddress"
                    required
                    [(ngModel)]="dwellingLocation.address1"
                    maxlength="60"
                    (change)="updateDwellingLocation()">
                </td>
                <td class="form-table__cell u-width-70px"></td>
              </tr>

              <tr class="form-table__row" [ngClass]="{'is-required-field': refDwellingCity.ngModel?.invalid}">
                <td class="form-table__cell u-width-120px">
                  <label for="">City:</label>
                </td>
                <td class="form-table__cell u-width-180px">
                  <sm-autocomplete
                    #refDwellingCity
                    [options]="cities"
                    [name]="'city'"
                    [id]="'city'"
                    [activeOption]="getSelectedCityOption()"
                    [caseSensitiveOptionsIdMatching]="false"
                    [readonly]="cities.length ? false : true"
                    [required]="true"
                    [allowEmptyValue]="false"
                    (onSelect)="updateDwellingDetails($event, 'city')">
                  </sm-autocomplete>
                  <!-- getSelectedCityOption() -->
                </td>
                <td class="form-table__cell u-width-160px" *ngIf="dwellingLocation.state !=='RI'" style="color: blue;"><a target="_blank" href="https://bostonsoftware.com/resource-center/suffolk-county-street-lookup/">Boston District Lookup</a></td>
              </tr>
              <tr class="form-table__row" [ngClass]="{'is-required-field':fieldFireDistrictIsRequired() }">
                <td class="form-table__cell u-width-120px">
                  <label for="">Fire District:</label>
                </td>
                <td class="form-table__cell u-width-180px">
                  <sm-autocomplete
                    [options]="fireDistricts"
                    [name]="'fireDistricts'"
                    [id]="'fireDistricts'"
                    [activeOption]=  "getSelectedFireDistrictOption()"
                    [caseSensitiveOptionsIdMatching]="false"
                    [allowEmptyValue]="true"
                    (onSelect)="updateDwellingDetails($event, 'fireDistrict')"

                    >
                  </sm-autocomplete>
                  <!-- getSelectedCityOption() -->
                </td>
                <td class="form-table__cell u-width-70px"></td>
              </tr>

              <tr class="form-table__row" [ngClass]="{'is-required-label': isRequiredState() || isRequiredZipCode()}">
                <td class="form-table__cell form-table__cell--label u-width-120px">
                  <label for="dwellingState">State &amp; Zip:</label>
                </td>
                <td class="form-table__cell u-width-220px">
                  <div class="u-show-iblock u-width-65px u-spacing--right-1" [ngClass]="{'is-required-field': isRequiredState()}">
                    <input
                      #refStateModel="ngModel"
                      type="text"
                      name="dwellingState"
                      id="dwellingState"
                      required
                      disabled
                      (change)="updateDwellingLocation()"
                      [(ngModel)]="dwellingLocation.state">
                  </div>
                  <div class="u-show-iblock u-width-65px" [class.is-required-field]="refZipCodeModel.invalid">
                    <input #refZipCodeModel="ngModel" type="text" [required]="true" [(ngModel)]="dwellingLocation.zip" name="dwellingZip"
                    style="width: 12ch;" id="dwellingZip" pattern="^[0-9]{5}(?:-[0-9]{4})?$|^[0-9]{9}$"
                    (change)="updateDwellingDetails($event.target.value, 'townCode')" [mask]="'00000-0000||00000'">
                  </div>
                </td>
              </tr>

              <tr class="form-table__row" [ngClass]="{'is-required-field': dwelling.constructionMaterialTypeCode ? false : true}">
                <td class="form-table__cell u-width-120px">
                  <label for="">Construction:</label>
                </td>
                <td class="form-table__cell u-width-180px">
                  <sm-autocomplete
                    [options]="construction"
                    [activeOption]="dwelling.constructionMaterialTypeCode"
                    [required]="dwelling.constructionMaterialTypeCode ? false : true"
                    [id]="'constructionMaterial'"
                    [name]="'constructionMaterial'"
                    [allowEmptyValue]="true"
                    (onSelect)="selected($event, dwelling, 'constructionMaterialTypeCode')">
                  </sm-autocomplete>
                </td>
                <td class="form-table__cell u-width-70px"></td>
              </tr>

              <tr class="form-table__row">
                <td class="form-table__cell u-width-120px" [ngClass]="{'is-required-field': fieldYearBuiltIsInvalid()}">
                  <label for="">Year Built / Age:</label>
                </td>
                <td colspan="2" class="form-table__cell u-width-220px">
                  <div class="u-show-iblock u-width-50px u-spacing--right-1" [ngClass]="{'is-required-field': fieldYearBuiltIsInvalid()}">
                    <input
                      id="constructionYear"
                      name="constructionYear"
                      type="text"
                      required
                      #dwellingYear="ngModel"
                      #constructionYear
                      pattern="\d*"
                      (keydown)="numbersOnly($event)"
                      [(ngModel)]="dwelling.constructionYear"
                      (focus)="setLastValidConstructionYear(constructionYear.value)"
                      (change)="actionOnConstructionYearChange()">
                  </div>

                  <div class="u-show-iblock u-width-25px u-color-slate-grey">
                    or
                  </div>
                  <div class="u-show-iblock u-width-50px">
                    <input
                      id="dwellingAge"
                      name="dwellingAge"
                      type="text"
                      pattern="\d*"
                      (keydown)="numbersOnly($event)"
                      [(ngModel)]="dwelling.constructionAge"
                      (change)="actionOnConstructionAgeChange()">

                    <app-modalbox #refWarningModalbox [css]="'u-width-380px tight'" (onStateChange)="actionOnModalboxClose($event)">
                      <p class="u-spacing--1">
                        {{errorMessage}}
                      </p>

                      <div class="row u-spacing--2">
                        <div class="col-xs-12 u-align-right">
                          <button class="o-btn" (click)="actionOnModalboxBtnClose($event)">
                            Close
                          </button>
                        </div>
                      </div>
                    </app-modalbox>

                  </div>
                </td>
              </tr>

              <tr class="form-table__row" [ngClass]="{'is-required-field': fieldSquareFootageIsRequired()}">
                <td class="form-table__cell u-width-120px">
                  <label for="">Square Footage:</label>
                </td>
                <td colspan="2" class="form-table__cell u-width-220px">
                  <div class="u-show-iblock u-width-50px">
                    <input type="text"
                      required
                      name="livingSpaceArea"
                      id="livingSpaceArea"
                      [(ngModel)]="dwelling.livingSpaceArea"
                      [dataToSave]="dwelling">
                  </div>
                </td>
              </tr>

            </table>
          </div>
          <div class="col-xs-6">
            <table class="form-table">

              <tr class="form-table__row" [ngClass]="{'is-required-field': dwelling.utilizationTypeCode ? false : true}">
                <td class="form-table__cell u-width-120px">
                  <label for="">Occupancy:</label>
                </td>
                <td class="form-table__cell u-width-180px">
                  <sm-autocomplete
                    [options]="occupancy"
                    [activeOption]="dwelling.utilizationTypeCode"
                    [required]="dwelling.utilizationTypeCode ? false : true"
                    [allowEmptyValue]="true"
                    [id]="'utilizationTypeCode'"
                    [name]="'utilizationTypeCode'"
                    (onSelect)="selected($event, dwelling, 'utilizationTypeCode')">
                  </sm-autocomplete>
                </td>
                <td class="form-table__cell u-width-70px"></td>
              </tr>

              <tr class="form-table__row" [ngClass]="{'is-required-field': fieldNumOfStoriesIsRequired()}">
                <td class="form-table__cell u-width-120px">
                  <label for="">Num. of Stories:</label>
                </td>
                <td class="form-table__cell u-width-180px">
                  <sm-autocomplete
                    [options]="stories"
                    [activeOption]="dwelling.storiesCount"
                    [allowEmptyValue]="false"
                    [id]="'storiesCount'"
                    [name]="'storiesCount'"
                    (onSelect)="selected($event, dwelling, 'storiesCount')">
                  </sm-autocomplete>
                </td>
                <td class="form-table__cell u-width-70px"></td>
              </tr>

              <tr class="form-table__row">
                <td class="form-table__cell u-width-120px">
                  <label for="">Townhouse:</label>
                </td>

                <td colspan="2" class="form-table__cell u-width-220px">
                  <div class="u-show-iblock u-width-30px">
                    <label class="o-checkable" [ngClass]="{'radio-disabled': townhouseCheckckboxIsDisabled()}">
                      <input type="checkbox" name="isTownhouse" id="isTownhouse" [(ngModel)]="dwelling.designStyleTownhouseInd" (change)="selected({id: dwelling.designStyleTownhouseInd}, dwelling, 'designStyleTownhouseInd')" [disabled]="townhouseCheckckboxIsDisabled()">
                      <i class="o-btn o-btn--checkbox"></i>
                    </label>
                  </div>

                  <!-- div class="u-show-iblock u-width-40px" [ngClass]="{'is-required-field': refUnits.ngModel?.invalid}" -->
                  <div class="u-show-iblock u-width-40px" [class.is-required-field]="townhouseUnitsFieldIsRequired()">
                    <label for="">Units:</label>
                  </div>

                  <!-- div class="u-show-iblock u-width-60px" [ngClass]="{'is-required-field': refUnits.ngModel?.invalid}" -->
                  <div class="u-show-iblock u-width-60px" [class.is-required-field]="townhouseUnitsFieldIsRequired()">
                    <sm-autocomplete
                      #refUnits
                      [id]="'townhouseUnitsCount'"
                      [options]="townhouseUnits"
                      [disabled]="townhouseUnitsFieldIsDisabled()"
                      [activeOption]="dwelling.townhouseUnitsCount"
                      [allowEmptyValue]="false"
                      [required]="townhouseUnitsFieldIsRequired()"
                      (onSelect)="selected($event, dwelling, 'townhouseUnitsCount', true, true)">
                    </sm-autocomplete>
                  </div>
                </td>
              </tr>

              <tr class="form-table__row" [ngClass]="{'is-required-field': refFamilies.ngModel?.invalid}">
                <td class="form-table__cell u-width-120px">
                  <label for="">Families:</label>
                </td>
                <td colspan="2" class="form-table__cell u-width-220px">
                  <div class="u-show-iblock u-width-50px">
                    <sm-autocomplete
                      #refFamilies
                      [options]="families"
                      [activeOption]="dwelling.familiesCount"
                      [required]="true"
                      [id]="'families'"
                      [name]="'families'"

                      [allowEmptyValue]="true"
                      (onSelect)="selected($event, dwelling, 'familiesCount')">
                    </sm-autocomplete>
                  </div>
                </td>
              </tr>

              <tr class="form-table__row" [ngClass]="{'is-required-field': fieldMortgageesIsRequired(), 'is-disabled-field-regular': !isHO4()}">
                <td class="form-table__cell u-width-120px">
                  <label for="">Mortgagees:</label>
                </td>
                <td colspan="2" class="form-table__cell u-width-220px">
                  <div class="u-show-iblock u-width-50px">
                    <sm-autocomplete
                      [disabled]="!isHO4()"
                      [id]="'mortgageeCount'"
                      [name]="'mortgageeCount'"
                      [options]="mortgagees"
                      [activeOption]="dwelling.mortgageeCount"
                      [allowEmptyValue]="false"
                      (onSelect)="selected($event, dwelling, 'mortgageeCount', true, true)">
                    </sm-autocomplete>
                  </div>
                </td>
              </tr>

              <tr class="form-table__row" [ngClass]="{'is-required-field': fieldPoolIsRequired()}">
                <td class="form-table__cell">
                  Pool:
                </td>
                <td class="form-table__cell" colspan="2" id="poolOnPremisesInd">
                  <label class="o-checkable u-spacing--right-2" [appDetectSystem] [removeRadio]="true">
                    <input type="radio" name="hasPool" id="hasPoolYes" [value]="true" [(ngModel)]="dwelling.poolOnPremisesInd" (change)="selected({id: true}, dwelling, 'poolOnPremisesInd')">
                    <i class="o-btn o-btn--radio"></i>
                    <span>Yes</span>
                  </label>
                  <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
                    <input type="radio" name="hasPool" id="hasPoolNo" [value]="false" [(ngModel)]="dwelling.poolOnPremisesInd" (change)="selected({id: false}, dwelling, 'poolOnPremisesInd')">
                    <i class="o-btn o-btn--radio"></i>
                    <span>No</span>
                  </label>
                </td>
              </tr>

            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <app-confirmbox
    [isOpen]="showOccupancyConfirmation"
    [question]="'Exclude Personal Liability and Medical Payments Coverage?'"
    [confirmBtnText]="'Yes'"
    [cancelBtnText]="'No'"
    (onAccept)="occupancyAccept()"
    (onCancel)="occupancyDecline()">
  </app-confirmbox>
</section>
