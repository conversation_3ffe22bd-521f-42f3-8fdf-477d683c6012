<section class="section section--compact u-spacing--2-5">
    <div class="row">
      <h1 class="o-heading" style="color: #0b71ac; padding-left: 20px;">Vehicle</h1>
    </div>
    <div class="row u-spacing--1">
      <div class="col-xs-12">
        <div class="box box--silver">
          <div class="row">
            <div class="col-md-2">
              <label for="plate">Plate:</label>
            </div>
            <div class="col-md-2"><input required [(ngModel)]="plate" name="plate" id="plate" placeholder="Plate"></div>
            <div class="col-md-4"> 
               <sm-autocomplete #refPlateSelector required [options]="plateTypeOptions" [activeOption]="'PANPL'"
                                placeholder="Type" [searchFromBegining]="false" [allowSearchById]="true"
                                (onSelect)="selectRMVlookupPlateType($event,plateType)" [(ngModel)]="plateType" name="plateType">
            </sm-autocomplete></div>
          </div>
        </div>
      </div>
    </div>
  </section>
  