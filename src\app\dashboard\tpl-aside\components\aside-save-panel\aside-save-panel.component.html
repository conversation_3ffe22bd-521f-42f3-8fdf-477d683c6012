<div class="sidebox sidebox--darker">
  <div class="aboutbox" *ngIf="!isMaipArcQuote">
    <h3
      class="aboutbox__title aboutbox__title--md"
      *ngIf="lastModifiedDate">
      Last Saved:
    </h3>

    <p
      class="u-color-pattens-blue u-spacing--bottom-2"
      *ngIf="lastModifiedDate">
      {{ lastModifiedDate }}
    </p>

    <p>
      <button
        class="app-button app-button--save-quote"
        (click)="saveQuote()">
        Save Quote
      </button>
    </p>

  <!--
    <p class="u-spacing--2">
      <button
        class="o-btn"
        type="button"
        (click)="testCanLeaveQuote($event)">
        Check leave quote
      </button>
    </p>

    <p class="u-spacing--2">
      <button
        class="o-btn"
        type="button"
        (click)="fakeSave($event)">
        Fake Save
      </button>
    </p>
  -->

    <p
      class="u-spacing--2"
      *ngIf="lastModifiedDate">
      <button
        id="saveAsButton"
        type="button"
        class="u-color-orange"
        (click)="actionOnSaveAs($event)">
        Save As...
      </button>
    </p>

    <app-modalbox
      #descriptionModal>
      <p class="u-spacing--bottom-1 u-color-charcoal">Quote Description:</p>
      <textarea
        #descriptionTextarea
        class="aside-save-panel__modal-quote-description"
        rows="4"
        cols="50"
        required
        placeholder="Notes..."
        [(ngModel)]="description"></textarea>
      <div class="u-spacing--2 u-align-right">
        <button
          class="o-btn o-btn--idle"
          (click)="descriptionModal.closeModalbox()">
          Cancel
        </button>
        <button
          class="o-btn aside-save-panel__btn u-spacing--left-1"
          (click)="saveAsQuote(description, descriptionModal)">
          Save
        </button>
      </div>
    </app-modalbox>


    <a #refTooltipFileActionLauncher class="aboutbox__link
        o-link
        o-link--expand
        u-spacing--2-5" >File Actions</a>


    <sm-tooltip
      #relTooltipFileActions
      [css]="'tooltip-new tooltip-new--menu-bottom'"
      [launcher]="refTooltipFileActionLauncher"
      [positionLauncher]="refTooltipFileActionLauncher"
      [preventCloseContentClick]="true">

      <ul (click)="closeTooltipWithDelay(relTooltipFileActions);" class="tooltip__menu u-align-center u-color-pelorous">
        <li class="tooltip__menu-item">
          <a (click)="createNewQuoteSession(descriptionModal)" class="tooltip__menu-link tooltip__menu-link_block" >Create New Quote Session</a>
        </li>
        <!-- Hidden as a part of SPR-4994-->
        <!-- <li class="tooltip__menu-item">
          <button #refSendToBscBtn type="button" id="send-to-bsc-aside" class="tooltip__menu-link">Send Quote to Boston Software</button>
        </li> -->
      </ul>
    </sm-tooltip>


    <app-modalbox #modalSendToBsc [launcher]="'#send-to-bsc-aside'" [preventCloseOnBackdropClick]="true">
      <app-send-to-bsc *ngIf="modalSendToBsc.isOpen"
        (confirmationEmail)="onSendToBscConfirmation(modalSendToBsc)"
        (cancel)="onSendToBscCancel(modalSendToBsc)">
      </app-send-to-bsc>
    </app-modalbox>
  </div>

  <div class="aboutbox" *ngIf="isMaipArcQuote">
    <h3 class="aboutbox__title aboutbox__title--md">
      ARC Quote
    </h3>
    <p class="u-color-pattens-blue u-spacing--bottom-2">
      Cannot be saved
    </p>
  </div>
</div>

<!--
<app-leave-quote #leaveQuote></app-leave-quote>
-->
