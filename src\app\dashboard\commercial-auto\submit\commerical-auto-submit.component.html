<section class="section section--thick" [positionFixed]="'absolute'">
  <div class="u-flex u-flex--spread">
    <div class="u-spacing--left-2-5">
    </div>
    <div class="u-position--relative u-align-right">
      <a class="o-btn" (click)="submitAll()">Submit All</a>
      <app-loader [loading]="loadingAll"></app-loader>
    </div>
  </div>
</section>

<section class="section section--compact">
  <div class="row">
    <div class="col-xs-12">
      <table class="table table--fixed table--wide-right table--hoverable-grey u-color-raven table--td-height-md">
        <tbody class="table_tbody">
          <tr class="table__tr table__tr--premium" *ngFor="let plan of selectedPlanObject?.items">
            <td class="table__td u-width-180px">
              <div class="u-align-center">
                <a *ngIf='!plan.review && !plan.error && !plan.submitSuccess && !plan.rerate && !planStatusIsError(plan)' class="o-btn o-btn--small-fixed u-width-60px u-t-weight--bold"
                  (click)="submitQuote(plan)">SUBMIT</a>
                <app-loader [loading]="plan.loading"></app-loader>
                <span *ngIf='plan.submitSuccess && !plan.rerate && !plan.review' class="o-link o-link--blue o-link--initial">SUBMITTED</span>
                <div *ngIf="planStatusIsError(plan)" class="u-t-upper u-color-app-auto">
                  <button (click)="submitQuote(plan)"
                    class="o-btn o-btn--small-fixed o-btn--error u-width-60px u-t-weight--bold">Error</button>
                </div>
                <button (click)="submitQuote(plan)"
                class="o-btn o-btn--small-fixed u-width-70px u-t-weight--bold"
                *ngIf="plan.rerate && !plan.review"
                >Re-Submit</button>

                <div *ngIf="planStatusIsDeclined(plan)" class="u-t-upper u-color-orange">
                  <button (click)="submitQuote(plan)"
                  class="o-btn o-btn--small-fixed o-btn--declined u-width-70px u-t-weight--bold">Declined</button>
                </div>
                <button (click)="showWarnings(plan)"
                class="o-btn o-btn--small-fixed u-width-60px u-t-weight--bold o-btn--orange js-modal-review"
                *ngIf="plan.review && !plan.error">Review</button>
                <div class="o-tag o-tag--price" *ngIf="plan.rate && plan.rate.items && plan.rate.items.length && plan.rate.items[0].premium !== 0 && !planStatusIsError(plan) && !planStatusIsDeclined(plan)">
                  {{ premiumsService.displayRates(plan.rate.items) }}
                </div>
              </div>
            </td>
            <td class="table__td">
              {{plan.name}}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</section>

<!-- PREMIUMS WARNINGS MODAL -->
<!-- [css]="'centered'" [backdropCss]="'not-visible'" -->
<app-modalbox #refModalWarningsPremiums [launcher]="'.js-modal-review'">
  <div class="u-flex u-flex--spread">
    <div class="">
      <h2 class="o-heading o-heading--red u-show-iblock u-align-v-middle u-spacing--right-1">Review warnings: <span class="o-heading--notransform">{{ reviewingPlan?.name }}</span></h2>
    </div>
  </div>

  <div class="box box--silver u-spacing--1-5">
    <!-- <p-scrollPanel styleClass="u-height-max-215px"> -->
      <div *ngIf="generalWarnings && generalWarnings.length">
        <p class="u-spacing--bottom-1">This plan cannot be rated because:</p>
        <ul class="list list--nowrap u-color-pelorous u-spacing--left-3-5">
          <li (click)="focusWarnElement(item.elementId, refModalWarningsPremiums)" class="list__item" *ngFor="let item of generalWarnings; let i = index">
            <!-- <app-warning-link [css]="'warnings-panel__link'" [openPanel]="true" [warningItem]="item"></app-warning-link> -->
            <app-hints-and-warnings-link [css]="'warnings-panel__link'" [openPanel]="true" [warningItem]="item"></app-hints-and-warnings-link>
          </li>
        </ul>
      </div>
      <div *ngIf="reviewingPlan && reviewingPlan.carrierWarnings && reviewingPlan.carrierWarnings.length">
        <p class="u-spacing--1 u-spacing--bottom-1">In addition, {{ reviewingPlan?.name }} requires:</p>
        <ul class="list list--nowrap u-color-pelorous u-spacing--left-3-5">
          <li (click)="focusWarnElement(item.elementId, refModalWarningsPremiums)" class="list__item" *ngFor="let item of reviewingPlan.carrierWarnings; let i = index">
            <!-- <app-warning-link [css]="'warnings-panel__link'" [openPanel]="true" [warningItem]="item"></app-warning-link> -->
            <app-hints-and-warnings-link [css]="'warnings-panel__link'" [openPanel]="true" [warningItem]="item"></app-hints-and-warnings-link>
          </li>
        </ul>
      </div>
    <!-- </p-scrollPanel> -->
  </div>

  <div class="row u-spacing--2">
    <div class="col-xs-12 u-align-right">
      <button (click)="resolveWarnings(generalWarnings, reviewingPlan, refModalWarningsPremiums)" class="o-btn">Resolve Warnings</button>
      <button (click)="refModalWarningsPremiums.closeModalbox()" class="o-btn o-btn--idle u-spacing--left-1-5">Close</button>
    </div>
  </div>
</app-modalbox>

