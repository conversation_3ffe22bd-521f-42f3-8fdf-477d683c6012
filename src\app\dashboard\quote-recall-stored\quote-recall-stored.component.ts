import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute, NavigationExtras } from '@angular/router';

import { QuotesService } from '../app-services/quotes.service';
import { OverlayLoaderService } from '../../shared/services/overlay-loader.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { SubscriptionLike as ISubscription } from 'rxjs';

@Component({
    selector: 'app-quote-recall-stores',
    templateUrl: './quote-recall-stored.component.html',
    styleUrls: ['./quote-recall-stored.component.scss'],
    standalone: false
})
export class QuoteRecallStoredComponent implements OnInit {
  private routeSubscription: ISubscription;
  public errorMessage;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private quotesService: QuotesService,
    private overlayLoaderService: OverlayLoaderService,
    private storageService: StorageService,
  ) { }

  ngOnInit() {
    this.getQuoteUrl()
  }

  ngOnDestroy() {
    this.routeSubscription && this.routeSubscription.unsubscribe();
  }

  private getQuoteUrl() {
    this.routeSubscription = this.route.params.subscribe( params => {
      this.quotesService.getRecallQuoteInboundUrl(params['id']).subscribe( data => {
        if (data) {
          // this.quotesService.setIsImportedQuote(true); // This is set in quote-loader.component
          this.storageService.setStorageData('recallQuoteInfo', data);
          this.router.navigate(['/dashboard/quotes', {imported: true}]);
        };
        // this.overlayLoaderService.hideLoader();
      }, err => {
        this.errorMessage = err;
        // this.overlayLoaderService.hideLoader();
      });
    });
  }

}
