import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiService } from 'app/shared/services/api.service';

import { StorageService } from 'app/shared/services/storage-new.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { Incident } from 'app/app-model/incident';

@Injectable()
export class IncidentsService {
  public incident: Incident;

  constructor(
    private apiService: ApiService,
    private storageService: StorageService,
    private apiCommonService: ApiCommonService
  ) { }

  // Incidents
  private incidentsUri(id: string) {
    return '/drivers/' + id + '/incidents';
  }

  public addIncident(driverId: string, data: any): Observable<any> {
    return this.apiCommonService.postByUri(this.incidentsUri(driverId), data);
  }

  public getIncidents(uri: string): Observable<any> {
    return this.apiCommonService.getByUri(uri);
  }

  public editIncident(driverId: string, data: any): Observable<any> {
    return this.apiCommonService.putByUri(this.incidentsUri(driverId) + '/' + data.resourceId, data);
  }

  public deleteIncident(uri: string): Observable<any> {
    return this.apiCommonService.deleteByUri(uri);
  }
}
