
<section class="section">
  <div class="row padding-row">
    <div class="col-xs-12">
        <h3>Document Destruction Affirmation</h3>
       <div class="padding-bottom" style="margin-top: -20px;">EVR Participants must destroy all physical documents between 90-120 days after processing a transaction</div>

    </div>

  </div>
    <div class="row">
      <div class="col-xs-12">
<div class="padding-bottom">
        <h4>Documents have been destroyed through: <span class="space-padding" style="margin-left: 21px;"> {{documentDestructionDetails.destroyedThroughDate | date: "MM/dd/yyyy"}}</span></h4>
        <h4>Documents eligible to be destroyed through: <span class="space-padding">{{documentDestructionDetails.eligibleToDestroyThroughDate | date: "MM/dd/yyyy"}}</span></h4>
        </div>


      </div>
      <div style="padding: 25px;">
      <div class="u-float-left">
        <div class="row padding-row">
          <div class="col-md-8" style="padding-left:0;margin-right: -4%;">
        Show eligible documents from {{destroyedFromDate | date: "MM/dd/yyyy"}} to
          </div>
          <div class="col-md-4 col-xs-3">
            <sm-autocomplete [options]="dateRange" [activeOption]="filteredDate" (onSelect)="setDate($event)" [(ngModel)]="filteredDate" name="filteredDate"></sm-autocomplete>
          </div>
        </div>
      </div>

      <div class="u-float-right">
        <button class="o-btn" [disabled]="list.length === 0" (click)="documentDestructionConfirm.open()">Confirm Destruction of all documents</button>
    </div>
    </div>

    </div>
  </section>

  <section class="section">
    <div class="row">
      <div class="col-xs-12">
        <table class="table table--compact table--hoverable-grey">
          <thead class="table__thead">
            <tr class="">
              <th class="table__th ">Transaction Date</th>
              <th class="table__th ">Owner</th>
              <th class="table__th">TransactionType</th>
              <th class="table__th ">Vehicle</th>
              <th class="table__th ">Documents to Destroy</th>
            </tr>
          </thead>
          <tbody class="table__tbody" *ngIf="list">
            <tr class="table__tr" *ngFor="let row of list">
              <td class="table__td">
                {{row.lastModifiedDate | date}}
              </td>
                  <td class="table__td">{{row.ownerFirstName}} {{row.ownerLastName}}</td>
                  <td class="table__td">{{splitWords(row.transactionType)}}</td>
                  <td class="table__td">
                    <span *ngIf="row.vin; else plate">{{ row.vin }}</span>
                    <ng-template #plate>
                      <span>{{ row.plate }}</span>
                    </ng-template>
                  </td>
                  <td class="table__td">{{row.requiredDocuments.join(', ')}}<span *ngIf="row.otherDocumentsCount > 0">, Other({{row.otherDocumentsCount}})</span></td>
            </tr>
          </tbody>
          <tbody class="table__tbody" *ngIf="!list?.length">
            <tr class="table__tr">
              <td colspan="5">
                <p class="u-padd--bottom-1 u-padd--1">
                  There are no results that match your search.
                </p>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

  </section>

  <section class="section">
    <div class="u-flex u-flex--spread u-flex--to-middle">
      <div class="">
        <app-pagination
          [currentPage]="paginationCurrentPage"
          [totalRecords]="size"
          [recordsLimit]="limit"
          (onPageChange)="paginationPageChange($event)"
        ></app-pagination>
      </div>
      <div class="">
        <app-results-limiter
          [description]="'Transactions to show'"
          (onChange)="onResultLimitChange($event)"
        ></app-results-limiter>
      </div>
    </div>
    <div style="padding-top:20px;">
        <button class="o-btn" [routerLink]="'/dashboard/rmv-services'">RETURN TO RMV DASHBOARD</button>
    </div>

    <app-modalbox #documentDestructionConfirm>
      <br>

                  <div class="box box--silver" style="margin-bottom: 20px;">
                      <p>
                        You are confirming that you've destroyed all documents starting on {{destroyedFromDate | date: 'MM/dd/yyyy'}} through {{filteredDate | date: 'MM/dd/yyyy'}}
                      </p>
                      <p class="line-space">Click proceed to continue</p>

                     </div>
                     <div class="col-xs-12 u-align-right u-remove-letter-spacing">
                    <button type="button" (click)="destroyDocuments();documentDestructionConfirm.close()" class="o-btn" style="margin-left:1.8rem;">Proceed</button>

                    <button type="button" (click)="documentDestructionConfirm.close()" class="o-btn o-btn--idle u-spacing--left-2" style="margin-left:1.8rem;">Cancel</button>
                  </div>

      </app-modalbox>

      <app-modalbox #error >
        <br>

                    <div class="box box--silver" style="margin-bottom: 20px;" *ngIf="response">
                      <p [innerHtml]="response.message[0]"></p>
                       </div>
                       <div class="col-xs-12 u-align-right u-remove-letter-spacing">

                      <button type="button" (click)="error.close()" class="o-btn o-btn--idle u-spacing--left-2" style="margin-left:1.8rem;">Close</button>
                    </div>

        </app-modalbox>



