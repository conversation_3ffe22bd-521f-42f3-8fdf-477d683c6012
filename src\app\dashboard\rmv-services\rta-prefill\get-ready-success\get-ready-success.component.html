<div class="row">
    <div class="offset-md-10"><button class="o-btn o-btn--small" (click)="modalbox.close()">Close</button></div>
</div>
<h6 style="color: green;text-align:center">Success! The RMV has provided a unique barcoded PDF for this transaction.</h6>
<div class="row text-center">
    <div class="offset-md-3">
       <button class="o-btn" style="margin-top:20px" (click)="getPdf()">DOWNLOAD BARCODED RTA FORM</button>
    </div>
    <div class="box box--silver" style="margin-top:20px;">


    <div class="row">
       <p>This Validated PDF must be used to successfully complete the GetReady transaction at the RMV.</p>
    </div>

    <div class="row">
        <div class="offset-md-3">
            <b>RMV Required Documents:</b>
            <ul class="dashed" *ngFor="let document of responseData?.responseDetail?.requiredDocuments">
                <li>{{document}}</li>
            </ul>
        </div>
    </div>
    <div class="row" *ngIf="responseData?.responseDetail?.rmvFees && responseData?.responseDetail?.rmvFees.lineItems.length > 0">
        <div class="offset-md-3">

            <div style="padding-top: 10px;">
            <span style="padding-bottom:1rem"><b>Transaction Fees</b></span>

            <ul class="dashed" *ngFor="let fee of responseData.responseDetail.rmvFees.lineItems | filterBy:['type']: ['Standard']">
                <li>{{fee.description}} {{fee.amount |currency}}</li>
            </ul>
            </div>
            <div style="padding-top: 20px;">
                <span style="padding-bottom:1rem"><b>Payment Option Fees</b></span>
                <ul class="dashed" *ngFor="let fee of responseData.responseDetail.rmvFees.lineItems | filterBy:['type']: ['CreditCardFee','BankTransferFee']">
                    <li>{{fee.description}} {{fee.amount |currency}}</li>
                </ul>
            </div>
            <div style="padding-top: 20px;">
                <span style="padding-bottom: 1rem"><b>Total</b></span>
                <ul class="dashed" *ngIf="responseData.responseDetail.rmvFees.bankTransferTotal && responseData.responseDetail.rmvFees.bankTransferTotal !== '0'">
                  <li>
                    <span
                      >If Paying by Credit Card
                      {{
                        responseData.responseDetail.rmvFees.total | currency
                      }}</span
                    >
                  </li>
                  <li>
                    <span
                      >If Paying by Bank Transfer
                      {{
                        responseData.responseDetail.rmvFees.bankTransferTotal
                          | currency
                      }}</span
                    >
                  </li>
                </ul>
                <ul class="dashed" *ngIf="!responseData.responseDetail.rmvFees.bankTransferTotal || responseData.responseDetail.rmvFees.bankTransferTotal === '0'">
                  <li>{{responseData.responseDetail.rmvFees.total | currency}}</li>
                </ul>
              </div>
    </div>
    </div>
<div class="row" style="text-align:center">
    <img src="assets/images/common/rmv_getready_logo.png" alt="">
</div>


</div>
