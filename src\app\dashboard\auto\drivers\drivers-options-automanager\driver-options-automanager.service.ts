
import { take } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { BehaviorSubject, SubscriptionLike as ISubscription } from 'rxjs';

// Services
import { OptionsService } from 'app/dashboard/app-services/options.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { AgencyUserService } from 'app/shared/services/agency-user.service';
import { DriversService } from 'app/dashboard/app-services/drivers.service';


// Models
import { Driver, DriverOptionsAndCoverages } from 'app/app-model/driver';
import { QuoteAuto, QuotePlan, QuotePlanListAPIResponse } from 'app/app-model/quote';

import {
  PolicyCoveragesData,
  CoverageItem,
  Coverage,
  CoverageItemParsed,
} from 'app/app-model/coverage';
import { Vehicle } from '../../../../app-model/vehicle';

interface TriggerSourceI {
  triggerBy?: 'SelectedPlans' | 'Drivers' | 'QuoteEffectiveDate' | 'Vehicles';
}

interface PromiseDataAfterSettingDefaultValuesNew {
  policiesToUpdate: CoverageItemParsed[];
  policyItemsParsed: CoverageItemParsed[];
}

interface PromiseDataAfterSavingToApi {
  status: string;
  policyItemsParsed: CoverageItemParsed[];
}

@Injectable()
export class DriverOptionsAutomanagerService {
  private serviceIsInitialized = false;

  private subscriptionQuote: ISubscription;
  private subscriptionQuoteIsNew: ISubscription;
  private subscriptionSelectedPlans: ISubscription;
  private subscriptionParserTrigger: ISubscription;
  private subscriptionDrivers: ISubscription;
  private subscriptionVehicles: ISubscription;

  private _parserTrigger: BehaviorSubject<TriggerSourceI> = new BehaviorSubject({});

  private quote: QuoteAuto;
  private quoteId = '';
  private quoteLob = '';
  private quoteIsNew = false;
  private selectedPlans: QuotePlan[] = [];
  private selectedPlansIds: string[] = [];
  private drivers: Driver[] = [];
  private vehicles: Vehicle[] = [];

  private quoteBeforeChange: QuoteAuto = null;
  private updatedDriverCoverages: any[];

  constructor(
    private storageService: StorageService,
    private agencyUserService: AgencyUserService,
    private specsService: SpecsService,
    private optionsService: OptionsService,
    private driversService: DriversService,
  ) { }

  public initialize(): Promise<void> {
    if (this.serviceIsInitialized) {
      return Promise.resolve();
    }

    this.serviceIsInitialized = true;
    console.log('Initialize Load Driver Data 1');
    return Promise.all([
      this.subscribeQuote(),
      this.subscribeQuoteIsNew(),
      this.subscribeSelectedPlans(),
      this.subscribeDrivers(),
      this.subscribeVehicles()
    ]).then(() => {
      return this.initPolicyItemsParser();
    }).catch(err => {
      console.log(err);
    });
  }

  public destroy(): void {
    this.serviceIsInitialized = false;

    this.subscriptionQuote && this.subscriptionQuote.unsubscribe();
    this.subscriptionQuoteIsNew && this.subscriptionQuoteIsNew.unsubscribe();
    this.subscriptionSelectedPlans && this.subscriptionSelectedPlans.unsubscribe();
    this.subscriptionParserTrigger && this.subscriptionParserTrigger.unsubscribe();
    this.subscriptionDrivers && this.subscriptionDrivers.unsubscribe();
    this.subscriptionVehicles && this.subscriptionVehicles.unsubscribe();
  }

  private subscribeQuote(): Promise<QuoteAuto> {
    return new Promise((resolve, reject) => {
      this.subscriptionQuote = this.storageService.getStorageData('selectedQuote')
        .subscribe((quote: QuoteAuto) => {
          this.quote = JSON.parse(JSON.stringify(quote));
          this.quoteId = quote.resourceId;
          this.quoteLob = quote.lob;

          resolve(this.quote);

          if (!this.quoteBeforeChange) {
            this.quoteBeforeChange = JSON.parse(JSON.stringify(quote));
          } else if (this.quoteBeforeChange.effectiveDate !== this.quote.effectiveDate) {
            this.quoteBeforeChange = JSON.parse(JSON.stringify(quote));

            // INIT POLICY ITEMS PARSING After Quote Effective date change
            // Emit Observable to Parse Items
            this._parserTrigger.next({ triggerBy: 'QuoteEffectiveDate' });
          }
        });
    });
  }


  private subscribeQuoteIsNew(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.subscriptionQuoteIsNew = this.storageService.getStorageData('isNewQuote')
        .subscribe((res: boolean) => {
          this.quoteIsNew = res;
          resolve(this.quoteIsNew);
        });
    });
  }

  private subscribeVehicles(): Promise<Vehicle[]> {
    return new Promise((resolve, reject) => {
      this.subscriptionVehicles = this.storageService.getStorageData('vehiclesList')
        .subscribe((vehicles: Vehicle[]) => {
          this.vehicles = JSON.parse(JSON.stringify(vehicles));
          resolve(this.vehicles);

          // INIT POLICY ITEMS PARSING
          // Emmit Observable to Parse Items
          this._parserTrigger.next({ triggerBy: 'Vehicles' });
        });
    });
  }

  private subscribeSelectedPlans(): Promise<QuotePlan[]> {
    return new Promise((resolve, reject) => {
      this.subscriptionSelectedPlans = this.storageService.getStorageData('selectedPlan')
        .subscribe((res: QuotePlanListAPIResponse) => {
          if (res && res.items && res.items.length) {
            this.selectedPlans = JSON.parse(JSON.stringify(res.items[0].items));
            this.selectedPlansIds = this.selectedPlans.map(plan => plan.ratingPlanId);
            resolve(this.selectedPlans);

            // INIT POLICY ITEMS PARSING
            // Emmit Observable to Parse Items
            this._parserTrigger.next({ triggerBy: 'SelectedPlans' });
          }
        });
    });
  }

  private subscribeDrivers(): Promise<Driver[]> {
    let driversCount = 0;

    return new Promise((resolve, reject) => {
      this.subscriptionDrivers = this.storageService.getStorageData('driversList')
        .subscribe((res: Driver[]) => {
          this.drivers = JSON.parse(JSON.stringify(res));
          resolve(this.drivers);

          // Emmit Observable to Parse Items
          if (this.drivers.length !== driversCount) {
            driversCount = this.drivers.length;
            this._parserTrigger.next({ triggerBy: 'Drivers' });
          }
        });
    });
  }

  private initPolicyItemsParser(): Promise<void> {
    const delay = 500;
    let timer;

    return new Promise((resolve, reject) => {
      this.subscriptionParserTrigger = this._parserTrigger.asObservable().subscribe(res => {
        timer && clearTimeout(timer);

        timer = setTimeout(() => {
          this.getOptionsPoliciesListAndParseData()
            .then(() => resolve())
            .catch(err => reject(err));
        }, delay);
      });
    });
  }

  private getOptionsPoliciesListAndParseData(): Promise<void> {
    const selectedPlansIdsString = this.selectedPlansIds.join(',');
    const states = this.quote.state;
    const quoteEffectiveDate = (this.quote && this.quote.effectiveDate) ? this.quote.effectiveDate : '';

    let agencyId;
    this.agencyUserService.userData$.pipe(take(1)).subscribe(agent => agencyId = agent.agencyId);

    return new Promise((resolve, reject) => {
      this.specsService.getRatingCoverages(states, this.quoteLob, 'driver', selectedPlansIdsString, '', quoteEffectiveDate).pipe(
        take(1))
        .subscribe(res => {
          let policiesItems: CoverageItem[] = [];
          if (res && res.items && res.items.length) {
            policiesItems = [...res.items];
          }

          if (policiesItems && this.quoteId) {
            this.processPoliciesItemsParsing(
              policiesItems,
              this.selectedPlans,
              this.quoteId,
              this.vehicles,
            )
              .then((res: DriverOptionsAndCoverages[]) => {
                this.storageService.setAutoDriversOptionsAndCoverages(res);
              })
              .then(() => resolve())
              .catch(err => {
                console.log(err);
                reject(err);
              });
          }
        });
    });
  }

  public processPoliciesItemsParsing(policies: CoverageItem[], selectedPlans: QuotePlan[], quoteId: string,
    vehicles: Vehicle[]): Promise<DriverOptionsAndCoverages[]> {
    let arrPoliciesItemsParsed = this.optionsService.parsePoliciesItemsToPoliciesItemsParsed(policies);
    arrPoliciesItemsParsed = this.optionsService.orderObjectsArrayByProperty(arrPoliciesItemsParsed, 'description');

    const promisesLoadPolicyCoveragesForDriver = [];

    this.drivers.forEach((el: Driver) => {
      const tmpPromise = this.loadPolicyCoveragesForDriver(el)
        .then((data: PolicyCoveragesData) => {
          let tmpClonedArrPoliciesItemsParsed = JSON.parse(JSON.stringify(arrPoliciesItemsParsed));

          tmpClonedArrPoliciesItemsParsed = tmpClonedArrPoliciesItemsParsed.map(item => {
            item = this.optionsService.sortPolicyItemParsedChildItems(item);
            item = this.optionsService.setPolicyItemStatusBasedOnPolicyCoverages(item, data.coverages);
            item = this.setPolicyItemStatusBasedOnRequirements(item, vehicles);

            item.endpointUrl = data.endpointURL;
            item.quoteResourceId = quoteId;

            if (item.inputType !== 'Dropdown') {
              item.values = this.optionsService.orderObjectsArrayByProperty(item.values, 'value');
            }

            // SPR-5186
            const unmatchedCoverages: Coverage[] = [];
            this.updatedDriverCoverages.forEach(coverage => {
              if (!arrPoliciesItemsParsed.some(parsedCoverage => parsedCoverage.coverageCode === coverage.coverageCode)) {
                unmatchedCoverages.push(coverage);
              }
            });

            if(unmatchedCoverages.length > 0) {
              this.updatedDriverCoverages = this.updatedDriverCoverages.filter(coverage => {
                  return !unmatchedCoverages.some(unmatchedCoverage => unmatchedCoverage.coverageCode === coverage.coverageCode);
              });

              this.driversService.updateDriverOptions(item.endpointUrl, this.updatedDriverCoverages)
                .pipe(take(1))
                .subscribe(
                  resp => console.log('Updated Coverages'),
                  err => console.log('Update Error: ', err)
                );
            }

            return item;
          });

          // display coverages and options for the driver only, filter the rest out
          // const filteredDriverCoveragesAndOptions = arrPoliciesItemsParsed.filter((x) => {
          //   return data.coverages.some((y) => {
          //     return y.coverageCode === x.coverageCode || x.carrierNotes !== null;
          //   });
          // });

          const tmpOptionsAndCoveragesForDriver = new DriverOptionsAndCoverages();
          tmpOptionsAndCoveragesForDriver.diver = el;
          tmpOptionsAndCoveragesForDriver.driverResourceId = el.resourceId;
          tmpOptionsAndCoveragesForDriver.options = tmpClonedArrPoliciesItemsParsed;

          return tmpOptionsAndCoveragesForDriver;
        });

      promisesLoadPolicyCoveragesForDriver.push(tmpPromise);
    });

    return Promise.all(promisesLoadPolicyCoveragesForDriver);
  }

  private loadPolicyCoveragesForDriver(driver: Driver): Promise<PolicyCoveragesData> {
    const policyCoverageData: PolicyCoveragesData = new PolicyCoveragesData();

    return new Promise((resolve, reject) => {
      if (driver && driver.coverages && driver.coverages.meta && driver.coverages.meta.href) {

        this.driversService.getDriverOptions(driver.coverages.meta.href)
          .subscribe(res => {

            // set the global variable to use the coverage data in processPoliciesItemsParsing
            this.updatedDriverCoverages = res.items[0].coverages;

            policyCoverageData.coverages = res.items[0].coverages;

            if (res.items[0].meta && res.items[0].meta.href) {
              policyCoverageData.endpointURL = res.items[0].meta.href;
            } else if (res.items[0]) {
              policyCoverageData.endpointURL = res.meta.href + '/' + res.items[0].resourceId;
            } else {
              policyCoverageData.endpointURL = 'api_does_not_returned_data_to_create_uri';
            }

            resolve(policyCoverageData);
          });
      } else {
        reject('Error Load Policy Coverages, driver.coverages.meta.href Not defined');
      }
    });
  }

  private setPolicyItemStatusBasedOnRequirements(policy: CoverageItemParsed, vehicles: Vehicle[]): CoverageItemParsed {
    const anyVehicleIsMotorcycle = vehicles.some(vehicle => vehicle.vehicleType === 'Motorcycle');

    switch (policy.coverageCode) {
      // Safeco Motorcycle - Years of Experience [https://bostonsoftware.atlassian.net/browse/SPRC-596]
      case 'BSC-AUTO-002218':
        anyVehicleIsMotorcycle ? policy.isRequired = true : policy.isRequired = false;
        break;
    }

    return policy;
  }

}
