
import { map, first } from 'rxjs/operators';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { Observable, forkJoin } from 'rxjs';
import { GENERAL_WARNINGS, WARNINGS } from 'app/app-mock/warnings';
import { NavigationExtras, Router, NavigationEnd } from '@angular/router';
import { WarningDefinitionI, WarningDataI, WarningItem, WARNING_GROUPS } from 'app/hints-and-warnings/model/warnings';

import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { PremiumsService } from 'app/dashboard/auto/premiums/premiums.service';
import { RateService } from 'app/shared/services/rate.service';
import { LookupsService } from 'app/dashboard/app-services/lookups.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { HintsAndWarningsService } from 'app/hints-and-warnings/services/hints-and-warnings.service';
import { QuotePlanSummary, RateResponse, DownPaymentData, DownPaymentResponseData } from 'app/app-model/quote';
import { AgencyUserService, UserData } from 'app/shared/services/agency-user.service';
import { SubsService } from 'app/dashboard/app-services/subs.service';

const BIG_FAKE_PREMIUM = 999999; // Deprecated


type TSortType = 'Carrier' | 'Rate';
type TSortOrder = 'ASC' | 'DESC';
const STORAGE_SORT_TYPE_NAME = 'currentPremiumsSortType';
const STORAGE_SORT_ORDER_NAME = 'currentPremiumsSortOrder';

//Rating plan ids to show authentication error help
const idsToShowAuthErrorHelp: string[] = ['11', '13','18','21','28'];

@Component({
    selector: 'app-premiums-table',
    templateUrl: './premiums-table.component.html',
    styleUrls: ['./premiums-table.component.scss'],
    standalone: false
})

export class PremiumsTableComponent implements OnInit, OnDestroy {
  downPaymentSet: boolean;

  constructor(
    private hintsAndWarningsService: HintsAndWarningsService,
    private overlayLoaderService: OverlayLoaderService,
    private apiCommonService: ApiCommonService,
    private storageService: StorageService,
    private rateService: RateService,
    private router: Router,
    public premiumsService: PremiumsService,
    private lookupsService: LookupsService,
    private agencyUserService: AgencyUserService,
    private subsService:SubsService
  ) { }
  private quote;
  private subscription;
  private rateSubscription;
  private currentPlansubscription;
  private downPaymentsubscription;
  private subscriptionChanges;
  public quotePlanObject;
  public loadingAll = false;
  public isMaipArcQuote = false;
  public downPayment: DownPaymentResponseData;

  public ratedPlans: any = [];
  public generalWarnings;
  public agentId;
  private carrierWarnings;
  private currentRatesSubscription;
  private currentPlan;
  private routerSubscription;
  public ratingPlanId;

  private subscriptionRateAll;
  private planSummaryRequestSubscription;

  private userSubscription;

  private subscriptionWarnings;
  private delayTimer;
  private allWarnings: WarningItem[] = [];


  // Sorting
  public filterOptions: Array<string> = ['Carrier', 'Rate'];
  public currentSortTypeOption: TSortType = 'Rate';
  public currentSortOrderOption: TSortOrder = 'ASC';

  public reviewingPlan;
  downPaymentPremium

  ngOnInit() {
    this.checkIfMaipArcQuote();
    this.rateService.getRates();
    this.premiumsService.getRates();

    this.processListSorting();

    this.getCurrentPlan();
    this.checkIfRequirementsMet();
    if (!this.premiumsService.loadingDataInProgress) {
      this.premiumsService.rerateGeneral();
    }
    this.routerCheck();

    this.getPlanSummaryRequest();
    // this.getCurrentDownPayment();

    setTimeout(() => {
      this.getCurrentDownPayment();
    });



    // this.initListSorting();
    // setTimeout(() => {
    //   this.initListSorting();
    // }, 5000);
  }

  ngOnDestroy() {
    this.rateService.unsubscribeGetRates();
    this.subscription && this.subscription.unsubscribe();
    this.rateSubscription && this.rateSubscription.unsubscribe();
    this.currentPlansubscription && this.currentPlansubscription.unsubscribe();
    this.subscriptionChanges && this.subscriptionChanges.unsubscribe();
    this.subscriptionWarnings && this.subscriptionWarnings.unsubscribe();
    this.currentRatesSubscription && this.currentRatesSubscription.unsubscribe();
    this.routerSubscription && this.routerSubscription.unsubscribe();
    this.planSummaryRequestSubscription && this.planSummaryRequestSubscription.unsubscribe();
    this.subscriptionRateAll && this.subscriptionRateAll.unsubscribe();
    this.downPaymentsubscription && this.downPaymentsubscription.unsubscribe();
    this.userSubscription && this.userSubscription.unsubscribe();
  }

  private setUserAgent(): void {

    let userData: UserData;

    this.agencyUserService.userData$.pipe(
      first())
      .subscribe(data => userData = data);

    // Quote agency contact
    if (userData) {
      this.agentId = userData.user.userId;
    }
  }

  private checkIfMaipArcQuote(): void {
    // let tmpIsMaipArc = localStorage.getItem('maipArcQuote');
    const tmpIsMaipArc = sessionStorage.getItem('maipArcQuote');
    console.log('%c tmpIsMaipArc', 'color:red', tmpIsMaipArc);
    if (tmpIsMaipArc) {
      const isMaipArc = JSON.parse(tmpIsMaipArc);
      this.isMaipArcQuote = isMaipArc.isMaipArcQuote;
    }
  }

  private checkIfRequirementsMet(): void {
    this.subscriptionWarnings = this.hintsAndWarningsService.allWarnings$.subscribe(warnings => {
      this.delayTimer && clearTimeout(this.delayTimer);

      this.delayTimer = setTimeout(() => {
        this.allWarnings = warnings; // .filter(item => item.showWarning);
        this.generalWarnings = warnings.filter(warning => warning.group === WARNING_GROUPS.general && warning.showWarning);
        this.carrierWarnings = warnings.filter(warning => warning.group === WARNING_GROUPS.carrier && warning.showWarning);

        if (this.generalWarnings && this.generalWarnings.length) {
          if (this.quotePlanObject && this.quotePlanObject.items && this.quotePlanObject.items.length) {
            this.quotePlanObject.items.forEach(plan => {
              plan.review = true;
            });
          }
        } else {
          if (this.quotePlanObject && this.quotePlanObject.items && this.quotePlanObject.items.length) {
            this.quotePlanObject.items.forEach(plan => {
              plan.review = false;
            });
          }
        }
        if (this.carrierWarnings && this.carrierWarnings.length) {
          this.carrierWarnings.forEach(element => {
            element.carrier.forEach(carrierId => {
              if (this.quotePlanObject && this.quotePlanObject.items && this.quotePlanObject.items.length) {
                this.quotePlanObject.items.forEach(plan => {
                  if (plan.ratingPlanId === carrierId) {
                    plan.review = true;
                  }
                });
              }
            });
          });
        }
      });
    });
  }

  private getCurrentPlan(): void {
    this.currentPlansubscription = this.storageService.getStorageData('selectedPlan').subscribe(res => {
      if (res && res.items && res.items.length) {
        this.currentPlan = JSON.parse(JSON.stringify(res));
        this.quotePlanObject = JSON.parse(JSON.stringify(res.items[0]));
        this.clearPlans();
        this.getCurrentRates()
          .then(() => {
            this.processListSorting();
          });
      }
    });
  }

  private clearPlans(force: boolean = false) {
    if (this.quotePlanObject && this.quotePlanObject.items && this.quotePlanObject.items.length) {
      this.quotePlanObject.items.forEach(plan => {
        if (!plan.rerate || force) {
          plan.error = null;
          plan.rate = null;
          plan.rerate = null;
          plan.premium = null;
        }
      });
    }
  }

  private getCurrentRates(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.currentRatesSubscription = this.storageService.getStorageData('rates').subscribe(res => {
        if (res && res.length) {
          this.ratedPlans = res;
          if (this.quotePlanObject && this.quotePlanObject.items && this.quotePlanObject.items.length) {
            this.quotePlanObject.items = this.quotePlanObject.items.map((plan, index) => {
              this.ratedPlans.forEach(ratedPlan => {
                if (ratedPlan && ratedPlan.items && ratedPlan.items.length) {
                  if (!ratedPlan.items[0].rerate) {
                    if (ratedPlan.items[0].ratingPlanId === Number(plan.ratingPlanId)) {
                      plan.rate = JSON.parse(JSON.stringify(ratedPlan));
                      if (!ratedPlan.loading) {
                        plan.loading = false;
                      }
                      plan.premium = null;
                      if (plan.rate && plan.rate.items && plan.rate.items.length && plan.rate.items[0].premium !== '' && plan.rate.items[0].premium !== 0 && plan.rate.items[0].premium !== '0') {
                        plan.premium = plan.rate.items[0].premium;
                      } else {
                        // force for ordering
                        plan.premium = BIG_FAKE_PREMIUM; // Deprecated
                      }
                      plan.rerate = null;
                      if (ratedPlan.items[0].msgStatusCd === 'Error' || ratedPlan.items[0].msgStatusCd === 'DataError' || ratedPlan.items[0].msgStatusCd === 'Unknown') {
                        plan.error = ratedPlan.items[0].msgStatusDesc;
                        if (!plan.error && ratedPlan.items[0].systemMessages && ratedPlan.items[0].systemMessages.length) {
                          plan.error = ratedPlan.items[0].systemMessages[0].message;
                        }
                      }

                      if (ratedPlan.items[0].msgStatusCd === 'Rejected' || ratedPlan.items[0].msgStatusCd === 'Declined') {
                        plan.declined = ratedPlan.items[0].msgStatusDesc;
                        if (!plan.declined && ratedPlan.items[0].systemMessages && ratedPlan.items[0].systemMessages.length) {
                          plan.declined = ratedPlan.items[0].systemMessages[0].message;
                        }
                      }
                    }
                  } else {
                    if (ratedPlan.items[0].ratingPlanId === Number(plan.ratingPlanId)) {
                      plan.rerate = true;
                      plan.rate = null;
                      plan.error = null;
                      plan.premium = null;
                    }
                  }
                }
              });

              if (this.premiumsService.tmpQuoteData && this.premiumsService.tmpQuoteData.items && this.premiumsService.tmpQuoteData.items.length) {
                if (plan.items && plan.items.length) {
                  plan.items.forEach(planItem => {
                    if (this.premiumsService.tmpQuoteData.items[index] && this.premiumsService.tmpQuoteData.items[index].items && this.premiumsService.tmpQuoteData.items[index].items.length) {
                      this.premiumsService.tmpQuoteData.items[index].items.forEach(oldPlanItem => {
                        if (planItem.coverageCode === oldPlanItem.coverageCode) {
                          if (planItem.currentValue !== oldPlanItem.currentValue && !planItem.rate) {
                            this.quotePlanObject.items[index].rerate = true;
                            this.quotePlanObject.items[index].error = null;
                            this.quotePlanObject.items[index].rate = null;
                            this.quotePlanObject.items[index].premiums = null;
                          }
                        }
                      });
                    }
                  });
                }
              }
              return plan;
            });
            this.premiumsService.tmpQuoteData = JSON.parse(JSON.stringify(this.quotePlanObject));
          }
        }

        resolve();
      });
    });
  }

  private getCurrentDownPayment(): void {
    this.downPaymentsubscription = this.storageService.getStorageData('downPayment').subscribe(res => {
      if (res && this.isMaipArcQuote) {
        this.downPayment = JSON.parse(JSON.stringify(res));
      }
    });

  }

  private removeRate(plan) {
    const plans = [];
    this.ratedPlans.forEach(ratedPlan => {
      if (ratedPlan && ratedPlan.items && ratedPlan.items.length && ratedPlan.items[0].carrier !== plan.name) {
        plans.push(ratedPlan);
      }
    });
    this.storageService.setStorageData('rates', plans);
  }

  private mapRatedPlans(newRate) {
    let inRatedPlans = false;
    this.ratedPlans = this.ratedPlans.map(ratedPlan => {
      if (ratedPlan && ratedPlan.items && ratedPlan.items.length) {
        // if ((newRate && newRate.items && newRate.items.length) && ratedPlan && ratedPlan.items && ratedPlan.items.length && ratedPlan.items[0].carrier === newRate.items[0].carrier) {
        if ((newRate && newRate.items && newRate.items.length) && ratedPlan && ratedPlan.items && ratedPlan.items.length && ratedPlan.items[0].ratingPlanId === newRate.items[0].ratingPlanId) {
          ratedPlan = newRate;
          inRatedPlans = true;
        }
      }
      return ratedPlan;
    });
    if (!inRatedPlans) {
      this.ratedPlans.push(newRate);
    }
    this.storageService.setStorageData('rates', this.ratedPlans);
  }

  public rate(uri: string, plan): Promise<any> {
    return new Promise<void>((resolve, reject) => {
      if (!plan.review) {
        plan.loading = true;
        plan.error = null;
        plan.declined = null;
        plan.rerate = null;
        plan.rate = null;
        plan.premium = null;
        return this.rateSubscription = this.apiCommonService.getByUri(uri).subscribe(response => {
          plan.loading = false;
          if (this.quotePlanObject && this.quotePlanObject.items && this.quotePlanObject.items.length) {
            this.quotePlanObject.items[this.quotePlanObject.items.indexOf(plan)].rate = response;
          }
          if (response.items[0].msgStatusCd === 'Error' || response.items[0].msgStatusCd === 'DataError' || response.items[0].msgStatusCd === 'Unknown') {
            plan.error = response.items[0].msgStatusDesc;
            if (!plan.error && response.items[0].systemMessages && response.items[0].systemMessages.length) {
              plan.error = response.items[0].systemMessages[0].message;
            }
          }

          if (response.items[0].msgStatusCd === 'Rejected' || response.items[0].msgStatusCd === 'Declined') {
            plan.declined = response.items[0].msgStatusDesc;
            if (!plan.declined && response.items[0].systemMessages && response.items[0].systemMessages.length) {
              plan.declined = response.items[0].systemMessages[0].message;
            }
          }

          this.mapRatedPlans(response);
          this.processListSorting();
          resolve();
        }, err => {
          plan.error = true;
          plan.loading = false;
          resolve();
        });
      } else {
        plan.premium = BIG_FAKE_PREMIUM; // Deprecated
        resolve();
      }
    });
  }

  public rateAll(continueRating: boolean = false): void {
    if (!continueRating) {
      this.ratedPlans = [];
      this.storageService.setStorageData('rates', []);
    }
    let plansRating = []
    this.quotePlanObject.items.forEach(plan => {
      const uri: string = this.quotePlanObject.meta.href + '/rateResponses/' + plan.ratingPlanId;
      this.rate(uri, plan);
      this.loadingAll = false;
    });
    if (!this.loadingAll) {
      this.storageService.getStorageData('rates').subscribe(
        x => {

          if (x.length === 2) {
            let maipIndex = this.ratedPlans.findIndex(x => x.items[0].carrier.includes('MA CAR Rates'));
            let arcIndex = this.ratedPlans.findIndex(x => !x.items[0].carrier.includes('MA CAR Rates'));
            const data: DownPaymentData = {
              maipPremium: this.ratedPlans[maipIndex].items[0].premium,
              arcPremium: this.ratedPlans[arcIndex].items[0].premium,
              maipArcId: 'test'
            };
            this.downPaymentPremium = data.arcPremium < data.maipPremium ? data.arcPremium : data.maipPremium
            if(this.downPaymentPremium != null && !this.downPaymentSet)
            {
              this.lookupsService.getMaipDownPayment(data).subscribe(result => {
              this.downPayment = JSON.parse(JSON.stringify(result));
              this.storageService.setStorageData('downPayment', this.downPayment);
              });
              this.downPaymentSet = true;
            }
          }
        }
      );
    }


  }


  private rateAllSingleFn(uri): Observable<any> {
    return this.rateService.getRate(uri).pipe(map(res => {
      this.processListSorting();
      return res;
    }));
  }

  private getPlanSummaryRequest() {
    this.planSummaryRequestSubscription = this.rateService.openSummary$.subscribe(data => {
      if (data) {
        // this.stopAndRestartRateAll();
        this.rateService.openSummary(false);
      }
    });
  }

  private stopAndRestartRateAll() {
    this.subscriptionRateAll && this.subscriptionRateAll.unsubscribe();
    setTimeout(() => this.rateAll(true), 500);
  }


  public onFilterChange($ev) {
    if ($ev && $ev.filter) {
      this.currentSortTypeOption = $ev.filter;

      if (this.currentSortTypeOption === $ev.filter) {
        this.currentSortOrderOption = (this.currentSortOrderOption === 'ASC') ? 'DESC' : 'ASC';
      } else {
        this.currentSortOrderOption = 'ASC';
      }

      this.sortList(this.currentSortTypeOption, this.currentSortOrderOption);

      // Save Sort Order Option to local storage
      window.sessionStorage.setItem(STORAGE_SORT_TYPE_NAME, this.currentSortTypeOption);
      window.sessionStorage.setItem(STORAGE_SORT_ORDER_NAME, this.currentSortOrderOption);
    }
  }

  private processListSorting(): void {
    const sortTypeCurrOption: TSortType = <TSortType>window.sessionStorage.getItem(STORAGE_SORT_TYPE_NAME);
    const sortOrderCurrOption: TSortOrder = <TSortOrder>window.sessionStorage.getItem(STORAGE_SORT_ORDER_NAME);
    this.currentSortTypeOption = sortTypeCurrOption || 'Rate';
    this.currentSortOrderOption = sortOrderCurrOption || 'ASC';

    this.sortList(this.currentSortTypeOption, this.currentSortOrderOption);
  }

  // Sorting Premiums list
  // ---------------------------------------------------------------------------
  private sortList(sortType: TSortType, sortOrder?: TSortOrder): void {
    switch (sortType.toLowerCase()) {
      case 'rate': {
        // First sort by Carrier name - in case if there are no premiums yet;
        this.sortQuotePlanObjectItemsByCarrierName('ASC');
        this.sortQuotePlanObjectItemsByCarrierPremium(sortOrder);
        break;
      }
      case 'carrier': {
        this.sortQuotePlanObjectItemsByCarrierName(sortOrder);
        break;
      }
    }
  }

  private sortQuotePlanObjectItemsByCarrierPremium(sortOrder: TSortOrder): void {
    if (this.quotePlanObject && this.quotePlanObject.items && this.quotePlanObject.items.length) {
      this.quotePlanObject.items.sort((planA, planB) => {
        let valA: number;
        let valB: number;

        // Plans with status 'Declined' or 'Error' should be always in the end of the list.
        if (sortOrder === 'ASC') {
          valA = (this.planStatusIsError(planA) || this.planStatusIsDeclined(planA)) ? 9999999999 : (planA.premium && typeof planA.premium === 'number') ? planA.premium : -1;
          valB = (this.planStatusIsError(planB) || this.planStatusIsDeclined(planB)) ? 9999999999 : (planB.premium && typeof planB.premium === 'number') ? planB.premium : -1;
        } else {
          valA = (this.planStatusIsError(planA) || this.planStatusIsDeclined(planA)) ? -2 : (planA.premium && typeof planA.premium === 'number') ? planA.premium : -1;
          valB = (this.planStatusIsError(planB) || this.planStatusIsDeclined(planB)) ? -2 : (planB.premium && typeof planB.premium === 'number') ? planB.premium : -1;
        }

        if (planA.rate && planA.rate.items && planA.rate.items.length && planA.rate.items.length > 1) {
          const aTmpPremiums: number[] = planA.rate.items.map(p => p.premium);
          const minValue = Math.min.apply(null, aTmpPremiums);
          valA = minValue;
        }

        if (planB.rate && planB.rate.items && planB.rate.items.length && planB.rate.items.length > 1) {
          const aTmpPremiums: number[] = planB.rate.items.map(p => p.premium);
          const minValue = Math.min.apply(null, aTmpPremiums);
          valB = minValue;
        }

        if (sortOrder === 'ASC') {
          return (valA < valB) ? -1 : (valA > valB) ? 1 : 0;
        } else {
          return (valA < valB) ? 1 : (valA > valB) ? -1 : 0;
        }
      });
    }
  }

  private sortQuotePlanObjectItemsByCarrierName(sortOrder: TSortOrder): void {
    if (this.quotePlanObject && this.quotePlanObject.items && this.quotePlanObject.items.length) {

      this.quotePlanObject.items.sort((planA, planB) => {
        const valA: string = planA.name;
        const valB: string = planB.name;

        if (sortOrder === 'ASC') {
          return (valA < valB) ? -1 : (valA > valB) ? 1 : 0;
        } else {
          return (valA < valB) ? 1 : (valA > valB) ? -1 : 0;
        }
      });
    }
  }
  // Sorting Premiums list - END


  public goToSummary(plan) {
    let rateRequestId;
    if (plan.rate.items && plan.rate.items && plan.rate.items.length) {
      rateRequestId = plan.rate.items[0].rateRequestId;
    }
    const uri = '/' + plan.rate.meta.href.split('rateResponses')[0] + 'planSummaries/' + rateRequestId;

    let isError;
    isError = (this.planStatusIsError(plan) || this.planStatusIsDeclined(plan));

    this.overlayLoaderService.showLoader();
    if (!isError) {
      this.apiCommonService.getByUri(uri).subscribe(planSummary => {
        this.overlayLoaderService.hideLoader();
        this.rateService.setPlanSummary(planSummary);

        console.log('%c SET PLAN SUMMARY 1: ', 'color:red', planSummary);

        this.storageService.setStorageData('planSummary', planSummary);

        const params: NavigationExtras = {
          queryParams: { overlay: 'plan-summary' }
        };
        this.router.navigate([], params);

      }, err => {
        this.rateService.setPlanSummary({
          manualError: err
        });
        this.overlayLoaderService.hideLoader();

        console.log('%c SET PLAN SUMMARY 2: ', 'color:red', this.rateService.getPlanSummary());
        this.storageService.setStorageData('planSummary', this.rateService.getPlanSummary());

        const params: NavigationExtras = {
          queryParams: { overlay: 'plan-summary' }
        };
        this.router.navigate([], params);
      });
    } else {
      console.log('%c SET PLAN SUMMARY 3: ', 'color:red', plan);
      this.storageService.setStorageData('planSummary', plan);
      this.overlayLoaderService.hideLoader();

      const params: NavigationExtras = {
        queryParams: { overlay: 'plan-summary' }
      };
      this.router.navigate([], params);

    }

    // this.stopAndRestartRateAll();
  }
  public showWarnings(plan) {
    this.reviewingPlan = plan;
    this.reviewingPlan.carrierWarnings = [];
    this.carrierWarnings.forEach(element => {
      if (element.carrier.indexOf(plan.ratingPlanId) > -1) {
        this.reviewingPlan.carrierWarnings.push(element);
      }
    });
    this.checkIfRequirementsMet();
  }

  private closeModalbox(refModal): void {
    refModal.closeModalbox();
  }

  public focusWarnElement(elementID: string, refModal = null) {
    if (refModal) {
      this.closeModalbox(refModal);
    }
    setTimeout(() => {
      const warnElement = document.getElementById(elementID);
      // this.warningService.focusWarnElement(warnElement);
    });
  }

  public resolveWarnings(generalWarnings: WarningItem[], reviewingPlan, refModal = null) {
    if (refModal) {
      this.closeModalbox(refModal);
    }

    if (generalWarnings && generalWarnings.length) {
      this.hintsAndWarningsService.goTo(generalWarnings[0]);
    } else if (reviewingPlan && reviewingPlan.carrierWarnings && reviewingPlan.carrierWarnings.length) {
      this.hintsAndWarningsService.goTo(reviewingPlan.carrierWarnings[0]);
    }
  }

  private routerCheck() {
    if (this.router && this.router.events) {
      this.routerSubscription = this.router.events.subscribe(val => {
        if (val instanceof NavigationEnd) {
          this.premiumsService.rerateGeneral(val.url);
        }
      });
    }
  }

  public planStatusIsError(plan): boolean {
    return plan.error
      || (plan.rate && plan.rate.items && plan.rate.items.length && plan.rate.items[0].msgStatusCd === 'Error')
      || (plan.rate && plan.rate.items && plan.rate.items.length && plan.rate.items[0].msgStatusCd === 'DataError');
  }

  public planStatusIsDeclined(plan): boolean {
    return plan.declined
      || (plan.rate && plan.rate.items && plan.rate.items.length && (plan.rate.items[0].msgStatusCd === 'Declined'
        || plan.rate.items[0].msgStatusCd === 'Rejected'));
  }

  public planStatusIsAuthError(plan): boolean {
    return plan?.error && plan?.error.includes('Authentication Error');
  }

  public showDownPaymentColumn(plan): boolean {
    return this.isMaipArcQuote && plan.rate && plan.rate.items && plan.rate.items.length
      && (plan.rate.items[0].premium !== 0) && !this.planStatusIsError(plan)
      && !this.planStatusIsDeclined(plan) && this.downPaymentPremium === plan.rate.items[0].premium;
  }

  public showBtnViewPlanSummary(plan): boolean {
    return plan.rate && plan.rate.items && plan.rate.items.length && !plan.loading;
  }


  public nameColSpan(plan): number {
    let span = 3;
    // showDownPaymentColumn(plan)
    if (plan.tips) {
      span -= 1;
    }

    if (this.showDownPaymentColumn(plan)) {
      span -= 1;
    }

    return span;
  }

  public showBtnPlanAuthError(plan): boolean {
    let ret = idsToShowAuthErrorHelp.includes(plan.ratingPlanId);
    return ret;
  }

  public PlanAuthErrorHelpLink(plan) {
    let ret = "";
    let tagLink = "";

    switch (plan.ratingPlanId) {
      case '13' :
      {
        ret = "https://bostonsoftware.happyfox.com/kb/article/113-arbella-authentication-errors/";
        tagLink="#Arbella_help";
        break;
      }
        case '11':
             {
        ret = "https://bostonsoftware.happyfox.com/kb/article/104-how-to-fix-a-safety-authentication-error/";
        tagLink="#Safety_help";
        break;
      }
      case '21':
           {
        ret = "https://bostonsoftware.happyfox.com/kb/article/132-concord-group-authentication-errors/";
        tagLink="#Concord_help";
        break;
      }
      case '18':
         {
        ret = "https://bostonsoftware.happyfox.com/kb/article/171-quincy-mutual-authentication-errors/";
        tagLink="#Quincy_help";
        break;
      }
      case '28' :
        {
        ret = "https://bostonsoftware.happyfox.com/kb/article/129-plymouth-rock-authentication-errors/";
        tagLink="#Plymounth_help";
        break;
      }

    }
    this.logData("#authentication-errors", ret, tagLink)

     window.open(ret, "_blank");
  }

  logData(tag, url, tagLink) {
    const {user, facilityId} = JSON.parse(localStorage.getItem('userData')) || {} as any;


    const log = {
      message: '/'+facilityId + '/'+ user.userId + '/#AutoPremiums/'+ tagLink +'/'+url,
      statusText:'OK',
      status:'200',
      url:'',
      name:'Plan Summary',
      tag: tag,
      userId:user.userId,
      facilityId

  };
  this.subsService.log(log).subscribe()

  }

  private openModalbox(refModal): void {
    refModal.openModalbox();
  }

}
