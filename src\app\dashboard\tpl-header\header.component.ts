
import {filter, startWith, take} from 'rxjs/operators';
import {
  Component,
  HostListener,
  OnD<PERSON>roy,
  OnInit,
  isDevMode,
  ViewChild,
} from '@angular/core';
import { NgForm } from '@angular/forms';
import { ActivatedRoute, NavigationEnd, Router, RouterEvent } from '@angular/router';

import { StorageService } from 'app/shared/services/storage-new.service';
import { AgencyUserService, UserSubscriptionInformationAPIResopnseI } from 'app/shared/services/agency-user.service';
import { ApiService } from 'app/shared/services/api.service';
import { AuthService } from 'app/shared/services/auth.service';
import { TooltipComponent } from 'app/shared/modules/sm-popups/components/tooltip/tooltip.component';
import { ModalboxComponent, IModalStateChange } from 'app/shared/components/modalbox/modalbox.component';
import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { RouteService } from 'app/shared/services/route.service';
import { LeaveQuoteService } from 'app/shared/services/leave-quote.service';
import { UserSubscriptionRatingEmitDataI } from 'app/shared/directives/user-subscription-rating.directive';
import { SubscriptionLike as ISubscription } from 'rxjs';
import { INewFormCreateEventFormType } from '../form/new-form-create/new-form-create.component';
import { SubsService } from 'app/dashboard/app-services/subs.service';

import { formatDistanceToNow } from 'date-fns';

const MODAL_NEW_FORM_DEFAULT_CSS = 'u-width-450px horizontal-centered';

@Component({
    selector: 'app-header',
    templateUrl: './header.component.html',
    styleUrls: ['./header.component.scss'],
    standalone: false
})
export class HeaderComponent implements OnInit, OnDestroy {
  constructor(
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private authService: AuthService,
    private agencyUserService: AgencyUserService,
    private quotesService: QuotesService,
    private routeService: RouteService,
    private apiService: ApiService,
    private storageService: StorageService,
    private leaveQuoteService: LeaveQuoteService,
    private subsService: SubsService
  ) {
    this.checkIfViewingQuote();

  }

  public get isDevEnv(): boolean {
    return isDevMode();
  }

  public get getUserName() {
    return this.userData && this.userData.user
      ? this.userData.user.firstName +
          ' ' +
          this.userData.user.lastName.substring(0, 1) +
          '.'
      : '';
  }

  // SubMenu handling
  public get submenuOpen(): boolean {
    // conjunction of all possible submenus states
    return this.submenuOpenQuotesIsVisible;
  }
  visible: boolean;

  private activatedRouteSubscription: ISubscription;
  private subscriptionSubscriptionInformation: ISubscription;

  public arrQuotesAll;

  public subscriptionMessages: string[] = [];
  public proceed = true;
  public loadingQuotes = false;

  // possible subMenus visibility states
  public submenuOpenQuotesIsVisible = false;

  private subscription;
  private dashboardSubscription;
  private userData: any;
  private createFormContainer: ModalboxComponent;
  private routerSubscription;
  public viewingQuote = false;

  private submenuCloseTimer = {};
  private submenuCloseDelay = 100; // miliseconds

  public formsOnlyUser = false;
  public cssWidthForNewFormModal: string = MODAL_NEW_FORM_DEFAULT_CSS;

  @ViewChild('refSubscriptionInfoPopup') public refSubscriptionInfoPopup: ModalboxComponent;
  @ViewChild('refCopyInfoPopup') public refCopyInfoPopup: ModalboxComponent;
  @ViewChild('refModalToolsCalculator') public refModalToolsCalculator: ModalboxComponent;
  @ViewChild('refModalToolsVinLookup') public refModalToolsVinLookup: ModalboxComponent;
enableTabs;
  viewingRmvService: boolean;
  // ------
  private preventingMultipleRequest = false;

    openDialog() {
    this.visible = true;
  }

  ngOnInit() {
    this.subscribeSubscriptionInformation();

    this.subscription = this.agencyUserService.userData$.subscribe(
      data => (this.userData = data)
    );
  }




  ngOnDestroy() {
    this.subscription && this.subscription.unsubscribe();
    this.dashboardSubscription && this.dashboardSubscription.unsubscribe();
    this.routerSubscription && this.routerSubscription.unsubscribe();
    this.activatedRouteSubscription && this.activatedRouteSubscription.unsubscribe();

    this.subscriptionSubscriptionInformation && this.subscriptionSubscriptionInformation.unsubscribe();
  }

  @HostListener('window:beforeunload', ['$event'])
  promptBeforeClose($event) {
      // if (this.routeService.inUrl && !this.routeService.isMaipArc && !this.leaveQuoteService.checkIfTheQuoteIsSaved()) {
      if (this.routeService.isViewingQuote && !this.routeService.isMaipArc && !this.leaveQuoteService.checkIfTheQuoteIsSaved()) {
        $event.returnValue = 'Changes you made may not be saved.';
        return $event.returnValue;
      }
  }


  // https://bostonsoftware.atlassian.net/browse/SPR-2778
  private subscribeSubscriptionInformation(): void {
    this.subscriptionSubscriptionInformation = this.agencyUserService.getSubscriptionInformation$
      .subscribe((res: UserSubscriptionInformationAPIResopnseI) => {
        this.formsOnlyUser = this.agencyUserService.checkIfUserSubscriptionAllowOnlyForms(res);
      });
  }

  private checkIfViewingQuote(): void {
    const matchUrl = /dashboard\/auto|commercial-auto|home|dwelling|umbrella\/quotes/i;
    const otherMatchurl = /dashboard\/rmv-services/i;
    this.activatedRouteSubscription = this.router.events.pipe(
      filter(e => e instanceof NavigationEnd),
      startWith(this.activatedRoute) // use the injected Route to load the initial state
    ).subscribe((e: NavigationEnd) => {
      if (matchUrl.test(e.urlAfterRedirects)) {
        this.viewingQuote = true;
      } else if (otherMatchurl.test(e.urlAfterRedirects)) {
        this.viewingRmvService = true;
      } else {
        this.viewingQuote = false; this.viewingRmvService = false;
      }
    });
  }




  public userLogout(): void {
    let letGoToQuote = true;
    if (this.routeService.isImported) {
      letGoToQuote = window.confirm(this.routeService.text);
    }

    // Confirm Quote Save
    if (letGoToQuote) {
      this.leaveQuoteService.requestCheckingIfQuoteSaved().pipe(
        take(1))
        .subscribe((allow: boolean) => {
          if (allow) {
            // The timeout is required to properly redirect user after quote is saved
            setTimeout(() => {
              // get username before clearing userData
              const username = this.agencyUserService.getUserDataSession().user.userName;
              this.quotesService.setIsImportedQuote(false);
              this.quotesService.setIsMaipArcQuote(false);
              this.agencyUserService.clearUserData();
              this.authService.logout();
              this.authService.logoutAdminApp(username);
            }, 20);
          }
        });
    }

  }

  public openSubmenu(propertyName: string): void {
    this.submenuCloseTimer[propertyName] &&
      clearTimeout(this.submenuCloseTimer[propertyName]);

    this[propertyName] = true;
  }

  public closeSubmenu(propertyName: string): void {
    this.submenuCloseTimer[propertyName] = setTimeout(() => {
      this[propertyName] = false;
    }, this.submenuCloseDelay);
  }


  private startNewFormCreate(): void {
    if (this.viewingQuote && this.refCopyInfoPopup) {
      // If in quote, open question if copy data
      this.refCopyInfoPopup.open();
    } else {
      // Else open create form
      this.createFormContainer.open();
    }
  }

  public setMsg(data: UserSubscriptionRatingEmitDataI, refCopyInfoPopup?: ModalboxComponent): void {
    this.subscriptionMessages = data.messages;
    this.proceed = data.proceed;
    this.createFormContainer = data.createFormContainer as ModalboxComponent;

    if (!data.hasWarning && this.proceed) {
      this.startNewFormCreate();
    }
  }

  private openCopyQuoteType(copy: boolean) {
    let srcQuoteSessionId = null;

    if (copy) {
      this.storageService.getStorageData('selectedQuote').pipe(
        take(1))
        .subscribe(quote => {
          if (quote && quote.quoteSessionId) {
            srcQuoteSessionId = quote.quoteSessionId;
          }
        });
    }

    this.storageService.setStorageData('srcQuoteSessionId',  srcQuoteSessionId );

    if (this.createFormContainer && !this.createFormContainer.isOpen) {
      this.createFormContainer.open();
    }
  }


  public refreshDashboard(ev: Event): void {
    ev && ev.preventDefault();
    if (
      this.router.routerState.snapshot.url.endsWith('dashboard') ||
      this.router.routerState.snapshot.url.endsWith('dashboard/')
    ) {
      this.router.navigate(['dashboard'], {
        skipLocationChange: true,
        queryParams: { reload: Date.now() }
      });
    } else {
      this.router.navigate(['dashboard']);
    }
  }

  public goToLink(url: string): void {
    let letGoToQuote = true;
    if (this.routeService.isImported) {
      letGoToQuote = window.confirm(this.routeService.text);
    }

    if (letGoToQuote) {
      this.quotesService.setIsImportedQuote(false);
      this.quotesService.setIsMaipArcQuote(false);
      this.router.navigate([url]);
    }
  }

  private getQuotes(): Promise<any> {
    return new Promise<void>((resolve, reject) => {
      this.quotesService
        .getQuotes('0', '5').pipe(
        take(1))
        .subscribe(
          quotes => {
            this.arrQuotesAll = quotes.items;
            this.mapAllQuotes();
            resolve();
          },
          err => reject(err)
        );
    });
  }

  private mapAllQuotes() {
    this.arrQuotesAll = this.arrQuotesAll.map(quote => {
      if (quote && quote.lastModifiedDate) {
        quote.lastModifiedDate = this.parseLastModifiedDate(
          quote.lastModifiedDate
        );
      }
      return quote;
    });
  }

  private parseLastModifiedDate(lastModifiedDate: string) {
    return formatDistanceToNow(new Date(lastModifiedDate), { addSuffix: true });
  }

  public createManuallyClick($ev, refModal): void {
    refModal.closeModalbox();
  }

  public cancelClick($ev, refModal): void {
    refModal.closeModalbox();
  }

  // New Form form component callbacks
  public createFormClick(data, refModalNewForm): void {
    refModalNewForm.closeModalbox();

    // TODO -- Open Form new form - Isn't it deprecated?
    const uri = (data && data.uri) ? data.uri : null;
    if (uri) {
      const formUri = this.apiService.formAppUrl(uri);
    }
  }
  public actionOnOpenLinkClick(ev: Event): void {
    ev.preventDefault();

    if (this.preventingMultipleRequest) {
      return;
    }

    // this.arrQuotesAll
    this.loadingQuotes = true;
    this.preventingMultipleRequest = true;
    this.getQuotes()
      .then(() => {
        this.loadingQuotes = false;
        this.preventingMultipleRequest = false;
      })
      .catch(err => {
        this.loadingQuotes = false;
        this.preventingMultipleRequest = false;
      });
  }

  public onSendToBscConfirmation(refModalBox: ModalboxComponent): void {
    if (refModalBox) {
      refModalBox.close();
    }
  }
  public onSendToBscCancel(refModalBox: ModalboxComponent): void {
    if (refModalBox) {
      refModalBox.close();
    }
  }

  public setModalNewFormCssClass(data: INewFormCreateEventFormType) {
    if (data && data.formType) {
      let tmpCss = MODAL_NEW_FORM_DEFAULT_CSS;

      switch (data.formType) {
        case 'question':
          tmpCss = 'u-width-450px horizontal-centered';
          break;
        case 'new':
          tmpCss = 'u-width-750px horizontal-centered';
          break;
        case 'quote':
          tmpCss = 'u-width-450px horizontal-centered';
          break;
        default:
          tmpCss = MODAL_NEW_FORM_DEFAULT_CSS;
      }

      setTimeout(() => {
        this.cssWidthForNewFormModal = tmpCss;
      });
    }
  }

  public modalNewFormOnStateChange(data: IModalStateChange): void {
    if (data && !data.isOpen === false) {
      this.cssWidthForNewFormModal = MODAL_NEW_FORM_DEFAULT_CSS;
    }
  }

  // Subscription info Modal
  // ---------------------------------------------------------------------------
  public actionModalSubscriptionInfoOk(): void {
    if (this.refSubscriptionInfoPopup) {
      this.refSubscriptionInfoPopup.close();
    }

    if (this.createFormContainer && this.proceed) {
      this.startNewFormCreate();
    }
  }

  // Copy Quote Modal
  // ---------------------------------------------------------------------------
  public actionModalCopyQuoteYes(): void {
    this.refCopyInfoPopup && this.refCopyInfoPopup.close();
    this.openCopyQuoteType(true);
  }

  public actionModalCopyQuoteNo(): void {
    this.refCopyInfoPopup && this.refCopyInfoPopup.close();
    this.openCopyQuoteType(false);
  }

  // Tools Menu
  // ---------------------------------------------------------------------------
  public toolsOpenCalculatorModal(): void {
    this.refModalToolsCalculator && this.refModalToolsCalculator.open();
  }

  public toolsOpenVinLookupModal(): void {
    this.refModalToolsVinLookup && this.refModalToolsVinLookup.open();
  }
  public IntSetupHelpLink() {
    let ret = '';
    let tag = '';

    ret = 'https://bostonsoftware.happyfox.com/kb/section/15/';
    tag = '#Integration_help';
    this.logData(tag);
    window.open(ret, '_blank');
  }

  logData(tag) {
    const {user, facilityId} = JSON.parse(localStorage.getItem('userData')) || {} as any;

    const log = {
      message: 'agency:' + facilityId + '//user:' + user.userId,
      statusText: 'OK',
      status: '200',
      url: '',
      name: 'Integration Setup Help',
      tag: tag,
      userId: user.userId,
      facilityId

  };
  this.subsService.log(log).subscribe();

  }
}
