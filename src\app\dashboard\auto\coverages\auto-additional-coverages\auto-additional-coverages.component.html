<section class="section section--compact">
  <div class="row">
    <div class="col-xs-12 u-spacing--1">
      <div class="o-container o-container--scroll-horizontal">

<app-modalbox #refModalApplyCarrierDefaults class="u-width-200px">
  <h1 class="o-heading o-heading--red">Apply carrier defaults</h1>
  <div class="box box--silver u-spacing--1-5">
    <div class="row o-columns">
      <div class="col-xs-12">
        <table class="form-table form-table--fixed">
          <tr class="form-table__row" >
            <td class="form-table__cell u-width-150px">
              <label for="">Select Vehicle to Apply: </label>
            </td>
          </tr>
          <tr class="form-table__row" >
            <td class="form-table__cell u-width-150px">
                <sm-autocomplete [id]="'VehicleDefault'" [name]="'applyDefaultsViewModel.DefaultVehicleSelected'" [options]="applyDefaultsViewModel.DefaultVehicles"
                  [activeOption]="'applyDefaultsViewModel.selectedCarrier'" [allowEmptyValue]="false"
                  (onSelect)="selectedVehicleForDefaults($event)">
                </sm-autocomplete>
            </td>
          </tr>

          <tr class="form-table__row" >
            <td class="form-table__cell u-width-150px">
              <label for="">Select Carrier Defaults to Apply: </label>
            </td>
          </tr>
          <tr class="form-table__row" >
            <td class="form-table__cell u-width-150px">
                <sm-autocomplete [id]="'CarrierDefault'" [name]="'applyDefaultsViewModel.DefaultCarrierSelected'" [options]="applyDefaultsViewModel.DefaultCarrierOptions"
                  [activeOption]="'applyDefaultsViewModel.DefaultCarrierSelected'" [allowEmptyValue]="false"
                  (onSelect)="selectedCarrierForDefaults($event)">
                </sm-autocomplete>
            </td>
          </tr>

          <tr class="form-table__row">
            <td class="form-table__cell">
              How would you like to update:
            </td>
          </tr>
          <tr class="form-table__row">

            <td class="form-table__cell u-align-right">
              <label class="o-checkable u-spacing--right-2" [appDetectSystem] [removeRadio]="true">
                <input type="radio" name="ApplyDefaultsType" id="defaultOverwriteYes" [value]="applyDefaultsViewModel.DefaultTypeOptions[0]"
                  [(ngModel)]="applyDefaultsViewModel.DefaultType">
                <i class="o-btn o-btn--radio"></i>
                <span>Overwrite All Options</span>
              </label>
              <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
                <input type="radio" name="ApplyDefaultsType" id="defaultOverwriteNo" [value]="applyDefaultsViewModel.DefaultTypeOptions[1]"
                  [(ngModel)]="applyDefaultsViewModel.DefaultType">
                <i class="o-btn o-btn--radio"></i>
                <span>Only Options Not Set</span>
              </label>
            </td>
          </tr>
        </table>
      </div>
    </div>
  </div>
  <div class="row u-spacing--2">
    <div class="col-xs-12 u-align-right">
      <button class="o-btn u-spacing--right-2" [disabled]="applyDefaultsViewModelInvalid()" (click)="ApplyDefaultsSave()" >
        Save
      </button>
      <button class="o-btn o-btn--idle" (click)="refModalApplyCarrierDefaults.closeModalbox()">Cancel</button>
    </div>
  </div>
</app-modalbox>

        <app-loader [loading]="updatingOptions" [cssClass]="'loader--content-top loader--with-opacity'"
          [loadingText]="'Updating options, please wait...'"></app-loader>
        <div style="overflow-y: auto; height: 600px;">
          <table class="table" style="width:auto">
            <thead>
              <tr>
                <th class="fixedHeader">
                  <div class="u-width-min-330px"></div>
                </th>
                <th *ngFor="let vehicleCoveragesData of vehiclesAdditionalCoverages" class="u-width-190px fixedHeader">
                  {{displayVehicleNameByResourceId(vehicleCoveragesData.vehicleResourceId)}}
                </th>
              </tr>
            </thead>

            <!-- Seperate tbody for every carrier -->
            <tbody class="collapse" [class.in]="toggleState[tbody.carrierName]"
              *ngFor="let tbody of coveragesTableDataWithGroups.tbodys">
              <tr>
                <th [colSpan]="tbody.colsCountPerRow">
                  <button type="button" class="o-action o-action--toggle"
                    [class.is-active]="toggleState[tbody.carrierName]"
                    (click)="toggleStateSwitch($event, tbody.carrierName)">
                    {{tbody.carrierName}}
                  </button>
                </th>
              </tr>

              <tr *ngFor="let row of tbody.rows">
                <td>
                  <button class="o-btn o-btn--dialog" [ngClass]="{'is-active': commentIconActive(row.carrierNotes)}"
                    [id]="'_button'" (click)="openDialogLinkInNewWindow($event, row.carrierNotes)">
                  </button>
                  {{row.label}}
                </td>
                <td *ngFor="let cell of row.cells" class="u-align-center">

                  <span *ngIf="cell.policyParsedToDisplay?.viewHasModalbox">
                    <label class="o-checkable" for="{{cell.policyParsedToDisplay.viewUqId}}">
                      <input type="checkbox" #refMdl="ngModel"
                        [name]="'checkbox_' + cell.policyParsedToDisplay.viewUqId"
                        [id]="cell.policyParsedToDisplay.viewUqId" [disabled]="cell.policyParsedToDisplay.isDisabled"
                        [required]="cell.policyParsedToDisplay.isRequired"
                        [ngModel]="cell.policyParsedToDisplay.isActive"
                        (click)="handleModalboxCheckboxClick($event, cell.policyParsedToDisplay, cell.vehicleCoverages, tbody, tbody.carrierName)">
                      <i class="o-btn o-btn--checkbox {{cell.policyParsedToDisplay.isRequired && !cell.policyParsedToDisplay.isActive && !cell.policyParsedToDisplay.isDisabled ? 'is-required-field' :''}} {{cell.policyParsedToDisplay.isDisabled ? 'is-disabled' :''}}"
                        [class.is-active]="cell.policyParsedToDisplay.isActive"></i>
                    </label>
                  </span>

                  <span *ngIf="!cell.policyParsedToDisplay?.viewHasModalbox">
                    <label class="o-checkable">
                      <input type="checkbox" [name]="'checkbox_' + cell.policyParsedToDisplay.viewUqId"
                        [id]="cell.policyParsedToDisplay.viewUqId" [disabled]="cell.policyParsedToDisplay.isDisabled"
                        [required]="cell.policyParsedToDisplay.isRequired"
                        [(ngModel)]="cell.policyParsedToDisplay.isActive"
                        (ngModelChange)="changePolicyStatusForVehicle(cell.policyParsedToDisplay, cell.vehicleCoverages, tbody, tbody.carrierName)">
                      <i
                        class="o-btn o-btn--checkbox {{cell.policyParsedToDisplay.isRequired && !cell.policyParsedToDisplay.isActive && !cell.policyParsedToDisplay.isDisabled ? 'is-required-field' :''}} {{cell.policyParsedToDisplay.isDisabled ? 'is-disabled' :''}}"></i>
                    </label>
                  </span>
                  <button *ngIf="policyHasSelectedChildValues(cell.policyParsedToDisplay)"
                    class="o-btn o-btn--action local-btn-info"
                    (click)="handleOpenModalboxWithSelectedValues($event, cell.policyParsedToDisplay, cell.vehicleCoverages, tbody, tbody.carrierName)">
                    <i class="icon-info"></i>
                  </button>

                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</section>


<app-modalbox #refModalboxOptions>
  <div id="NOT_FCRADisclosure" *ngIf="modalboxSelectedPolicyParsed.inputType !== 'FCRADisclosure'">
    <h1 class="o-heading o-heading--red">{{modalboxSelectedPolicyParsed.description}}</h1>

    <div class="box box--silver u-spacing--1-5">
      <app-loader [loading]="modalboxLoadingData" [cssClass]="''" [loadingText]="'Updating options, please wait...'">
      </app-loader>

      <!-- options (radio) -->
      <div *ngIf="modalboxSelectedPolicyParsed.inputType === 'Dropdown'" class="u-flex"
        [class.u-flex--two-col]="modalboxSelectedPolicyParsed.values.length > 9">
        <div class="checklist__item"
          *ngFor="let option of modalboxSelectedPolicyParsed.values; let optionIndex = index">
          <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
            <input type="radio" name="parsedItemOption"
              [checked]="modalboxDropdownRadioChecked(modalboxSelectedPolicyParsed, option.value, optionIndex)"
              (change)="setCurrentValueForPolicyParsed(modalboxSelectedPolicyParsed, option.value)">
            <i class="o-btn o-btn--radio"></i>
            <span>{{option.value}}</span>
          </label>
        </div>
      </div>
      <!-- / options (radio) -->

      <!-- datepicker -->
      <table class="form-table" *ngIf="modalboxSelectedPolicyParsed.inputType === 'Date'"
        [ngClass]="{'is-required-field': this.modalboxErrorPreventSaveAndClose && !refDatePicker.selectDate}">
        <tr class="form-table__row">
          <td class="form-table__cell">
            <label for="refDatePicker">Enter date:</label>
          </td>
          <td class="form-table__cell">
            <app-datepicker-input #refDatePicker [dateInputFormat]="'yyyy-MM-dd'" [returnDateFormat]="'yyyy-MM-dd'"
              [selectDate]="modalboxSelectedDate"
              (onDateChange)="setCurrentValueForPolicyParsed(modalboxSelectedPolicyParsed, $event.formatedDate)">
            </app-datepicker-input>
            <!--[returnDateFormat]="'yyyy-MM-dd'"-->
          </td>
        </tr>
      </table>
      <!-- / datepicker -->

      <!-- text -->
      <table class="form-table" *ngIf="modalboxSelectedPolicyParsed.inputType === 'Text'"
        [ngClass]="{'is-required-field': this.modalboxErrorPreventSaveAndClose && !refInputText.value}">
        <tr class="form-table__row" *ngIf="modalboxSelectedPolicyParsed.coverageCode !== 'BSC-AUTO-002304'">
          <td class="form-table__cell">
            <label for="refDatePicker" >{{setLabel(modalboxSelectedPolicyParsed.coverageCode)}}</label>
          </td>
          <td class="form-table__cell">
            <input #refInputText type="text" [required]="true" [(ngModel)]="modalboxSelectedPolicyParsed.currentValue"
              (change)="setCurrentValueForPolicyParsed(modalboxSelectedPolicyParsed, $event.target.value)" />
          </td>
        </tr>
        <tr class="form-table__row" *ngIf="modalboxSelectedPolicyParsed.coverageCode === 'BSC-AUTO-002304'">
          <td class="form-table__cell">
            <label for="refDatePicker"
              [ngClass]="{'is-required-field': modalboxSelectedPolicyParsed.currentValue == '' || !refYearsInputText}">Enter
              Value</label>
          </td>
          <td class="form-table__cell">
            <input #refYearsInputText="ngModel" type="number" min="0" max="99" name="yearsOwned" [required]="true"
              [(ngModel)]="modalboxSelectedPolicyParsed.currentValue"
              (keyup)="setCurrentValueForYearsParsed(modalboxSelectedPolicyParsed, $event.target.value,refYearsInputText)" />
          </td>
          <td class="form-table__cell is-required-field" [hidden]="refYearsInputText.valid"><span>Years Owned is
              required and must be between 0-99</span></td>
        </tr>
      </table>
      <!-- / text -->

      <!-- AgencyCode | AgentCode -->
      <table class="form-table"
        *ngIf="modalboxSelectedPolicyParsed.inputType === 'AgencyCode' || modalboxSelectedPolicyParsed.inputType === 'AgentCode'">
        <tr class="form-table__row" *ngIf="modalboxAgencyCodesOptions?.length">
          <td class="form-table__cell">
            <label for="refDatePicker">Agent Code:</label>
          </td>
          <td class="form-table__cell">
            <sm-autocomplete #AgentCodeSelection [options]="modalboxAgencyCodesOptions"
              [activeOption]="modalboxSelectedAgencyCodeId" [searchFromBegining]="true"
              (onSelect)="actionOnModalboxAgentCodeSelect($event, modalboxSelectedPolicyParsed)">
            </sm-autocomplete>
          </td>
          <td class="form-table__cell"></td>
        </tr>

        <tr class="form-table__row" *ngIf="modalboxGroupPlansOptions?.length">
          <td class="form-table__cell">
            <label for="refDatePicker">Group Plan:</label>
          </td>
          <td class="form-table__cell">
            <sm-autocomplete #GroupPlanSelection [options]="modalboxGroupPlansOptions"
              [activeOption]="modalboxSelectedGroupPlanId" [searchFromBegining]="true"
              (onSelect)="actionOnModalboxGroupPlanSelect($event, modalboxSelectedPolicyParsed)">
            </sm-autocomplete>
          </td>
          <td class="form-table__cell"></td>
        </tr>

        <tr class="form-table__row" *ngIf="!modalboxAgencyCodesOptions?.length">
          <td class="form-table__cell">
            <label for="refDatePicker">No Agency Codes available for this Carrier.</label>
          </td>
        </tr>
      </table>
      <!-- / AgencyCode | AgentCode -->


      <!-- Parent -->
      <table class="form-table" *ngIf="modalboxSelectedPolicyParsed.inputType === 'Parent'">
        <tr class="form-table__row" *ngFor="let item of modalboxChildItemsToDisplay; let itemIndex = index;"
          [ngClass]="{'is-required-field': this.modalboxErrorPreventSaveAndClose && !item.inputValue}">

          <td class="form-table__cell" *ngIf="item.sourceChildItem.inputType !== 'Checkbox'">
            <label>{{item.sourceChildItem.description}}</label>
          </td>

          <td class="form-table__cell">
            <div *ngIf="item.sourceChildItem.inputType == 'Dropdown'">
              <sm-autocomplete #AgentCodeSelection *ngIf="item.selectOptions.length" [options]="item.selectOptions"
                [activeOption]="item.inputValue" [searchFromBegining]="true"
                (onSelect)="actionOnModalboxChildSelect($event, item)">
              </sm-autocomplete>

              <span *ngIf="!item.selectOptions.length">There are no available options</span>
            </div>


            <div *ngIf="item.sourceChildItem.inputType == 'Text'">
              <input [type]="checkType(item.sourceChildItem)" [required]="true" [(ngModel)]="item.inputValue" />
            </div>

            <div *ngIf="item.sourceChildItem.inputType == 'Checkbox'">
              <label class="o-checkable">
                <input #refInputCheckbox type="checkbox" [name]="'parsedItemChildOption_' + itemIndex"
                  [checked]="item.inputValue == 'Yes'"
                  (change)="actionOnModalboxChildCheckboxChange($event, item, refInputCheckbox)">
                <!-- (change)="" -->
                <i class="o-btn o-btn--checkbox"></i>
                {{item.sourceChildItem.description}}
              </label>

            </div>

            <div *ngIf="item.sourceChildItem.inputType == 'Date'">
              <app-datepicker-input #refDatePicker [dateInputFormat]="'yyyy-MM-dd'" [returnDateFormat]="'yyyy-MM-dd'"
                [selectDate]="item.inputValue" (onDateChange)="actionOnModalboxChildDateSelect($event, item)">
              </app-datepicker-input>
              <!--[returnDateFormat]="'yyyy-MM-dd'"-->
            </div>
          </td>
          <td class="form-table__cell"></td>
        </tr>
      </table>
      <!-- / Parent -->


    </div><!-- /.box -->


    <div class="row u-spacing--2">
      <div class="col-xs-12 u-align-right">
        <button (click)="actionOnModalboxOptionsSave($event)" class="o-btn u-spacing--right-2"
          [disabled]="disableSave">Save</button>
        <button (click)="closeModalbox($event)" class="o-btn o-btn--idle">Cancel</button>
      </div>
    </div>
  </div><!-- / #NOT_FCRADisclosure-->


  <div id="FCRADisclosure" *ngIf="modalboxSelectedPolicyParsed.inputType === 'FCRADisclosure'">
    <div class="row">
      <div class="col-xs-12">
        <h1 class="u-color-sunset u-t-size--1-5rem u-spacing--0">
          FCRA Disclosure
        </h1>

        <div class="u-spacing--1-5" style="font-weight:normal">
          <p class="o-text">
            The Federal Fair Credit Reporting Act, Driver's Protection Act,
            analogous state laws, and other state and federal laws govern the use
            of reports delivered via the Boston Software Single Point Rating system.
            It is your responsibility to ensure that your requests are lawful.
            When ordering reports for underwriting purposes, you must have
            a completed application and/or the verbal request of the subject consumer.
            The Federal Fair Credit Reporting Act imposes criminal penalties - including
            a fine, up to two years in prison, or both - against anyone who knowingly
            and willfully obtains information on a consumer from a consumer reporting
            agency under false pretenses, and other penalties for anyone who obtains such
            consumer information without a permissible purpose.
          </p>

          <p class="o-text u-spacing--2-5">
            Select "Yes" to indicate your understanding and acceptance of these terms.<br>
            Select "No" to decline acceptance of these terms.
          </p>
        </div>
      </div>
    </div>

    <hr>

    <div class="row u-spacing--2">
      <div class="col-xs-12 u-align-right">
        <button #saveButton (click)="actionOnModalboxOptionsSave($event)" class="o-btn u-spacing--right-2">Yes</button>
        <button (click)="closeModalbox($event)" class="o-btn o-btn--idle">No</button>
      </div>
    </div>
  </div> <!-- / #FCRADisclosure -->

</app-modalbox>
