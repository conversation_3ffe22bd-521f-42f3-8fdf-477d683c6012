import { async, ComponentFixture, fakeAsync, inject, TestBed, tick } from '@angular/core/testing';

import { data as AGENCY_USER } from 'testing/data/agencies/user';
import { DWELLING_COVERAGES as DWELLING_COVERAGES } from 'testing/data/quotes/coverages/dwelling';
import { DWELLING_QUOTE } from 'testing/data/quotes/quote-dwelling';
import { DWELLING_QUOTE_PLAN_LIST } from 'testing/data/quotes/quote-plan-list/dwelling';
import {
    DWELLING_GENERAL_POLICY_OPTIONS
} from 'testing/data/specs/rating-coverages/DFIRE/policy-options-general';
import {
    StubAgencyUserService, StubAgencyUserServiceProvider
} from 'testing/stubs/services/agency-user.service.provider';
import {
    REAL_OPTIONS_SERVICE_TOKEN, StubOptionsService, StubOptionsServiceProvider
} from 'testing/stubs/services/options.service.provider';
import {
    StubSpecsService, StubSpecsServiceProvider
} from 'testing/stubs/services/specs.service.provider';
import { StubSubsServiceProvider } from 'testing/stubs/services/subs.service.provider';

import { ApiResponse } from 'app/app-model/_common';
import { CoveragesData } from 'app/app-model/coverage';
import { OptionsService } from 'app/dashboard/app-services/options.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { AgencyUserService } from 'app/shared/services/agency-user.service';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Helpers } from 'app/utils/helpers';

import {
    DwellingGeneralOptionsAutomanagerComponent
} from './dwelling-general-options-automanager.component';

describe('Component: DwellingGeneralOptionsAutomanager', () => {
  let component: DwellingGeneralOptionsAutomanagerComponent;
  let fixture: ComponentFixture<DwellingGeneralOptionsAutomanagerComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [
        DwellingGeneralOptionsAutomanagerComponent
      ],
      providers: [
        StorageService,
        StubAgencyUserServiceProvider,
        StubSpecsServiceProvider,
        StubOptionsServiceProvider,
        StubSubsServiceProvider,
        StorageGlobalService,
        { provide: REAL_OPTIONS_SERVICE_TOKEN, useClass: OptionsService },
      ]
    })
    .compileComponents();
  }));

  describe('when all data is available', () => {
    beforeEach(fakeAsync(inject(
      [StorageService, AgencyUserService, SpecsService, OptionsService],
      (storageService: StorageService, agencyUserService: StubAgencyUserService,
        specsService: StubSpecsService, optionsService: StubOptionsService) => {
        storageService.setStorageData('selectedQuote', Helpers.deepClone(DWELLING_QUOTE));
        storageService.setStorageData('selectedPlan', Helpers.deepClone(DWELLING_QUOTE_PLAN_LIST));
        agencyUserService.userData = Helpers.deepClone(AGENCY_USER);
        specsService.ratingCoverages = Helpers.deepClone(DWELLING_GENERAL_POLICY_OPTIONS);
        optionsService.coverages = Helpers.deepClone(DWELLING_COVERAGES);

        fixture = TestBed.createComponent(DwellingGeneralOptionsAutomanagerComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
        tick(500);
      })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });

    it('should destroy without errors', () => {
      expect(() => {
        fixture.destroy();
      }).not.toThrow();
    });
  });

  describe('when no policies are returned', () => {
    beforeEach(fakeAsync(inject(
      [StorageService, AgencyUserService, SpecsService, OptionsService],
      (storageService: StorageService, agencyUserService: StubAgencyUserService,
        specsService: StubSpecsService, optionsService: StubOptionsService) => {
        storageService.setStorageData('selectedQuote', Helpers.deepClone(DWELLING_QUOTE));
        storageService.setStorageData('selectedPlan', Helpers.deepClone(DWELLING_QUOTE_PLAN_LIST));
        agencyUserService.userData = Helpers.deepClone(AGENCY_USER);
        specsService.ratingCoverages = Helpers.deepClone(DWELLING_GENERAL_POLICY_OPTIONS);
        optionsService.coverages = new ApiResponse<CoveragesData>();

        fixture = TestBed.createComponent(DwellingGeneralOptionsAutomanagerComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
        tick(500);
      })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });
  });
});
