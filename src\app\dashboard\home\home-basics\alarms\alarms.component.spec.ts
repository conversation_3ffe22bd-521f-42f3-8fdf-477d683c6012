import { StubOverlayLoaderServiceProvider } from 'testing/stubs/services/overlay-loader.service.provider';
import { ComponentFixture, TestBed, async, fakeAsync, inject, tick } from '@angular/core/testing';

import { AlarmsComponent } from './alarms.component';
import { DwellingService } from 'app/dashboard/app-services/dwelling.service';
import { FormsModule } from '@angular/forms';
import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { DWELLING_PROTECTION_DEVICES } from 'testing/data/dwellings/protection-devices';
import { Helpers } from 'app/utils/helpers';
import { DWELLINGS } from 'testing/data/quotes/dwellings';
import { StubDwellingService } from 'testing/stubs/services/dwelling.service.provider';

describe('Component: Alarms', () => {
  let component: AlarmsComponent;
  let fixture: ComponentFixture<AlarmsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [ FormsModule ],
      declarations: [ AlarmsComponent ],
      providers: [
        StorageService,
        DwellingService,
        QuotesService,
        SpecsService,
        StubOverlayLoaderServiceProvider
      ]
    })
    .compileComponents();
  }));

  describe('when all data is available in storage', () => {
    beforeEach(fakeAsync(inject([StorageService], (storageService: StorageService) => {
      storageService.setStorageData('dwelling', Helpers.deepClone(DWELLINGS.items[0]));
      storageService.setStorageData('dwellingProtectionDevices', Helpers.deepClone(DWELLING_PROTECTION_DEVICES.items[0]));

      fixture = TestBed.createComponent(AlarmsComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
      tick();
    })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('when only basic data is available in storage', () => {
    beforeEach(fakeAsync(inject([StorageService, DwellingService],
      (storageService: StorageService, dwellingService: StubDwellingService) => {
        storageService.setStorageData('dwelling', Helpers.deepClone(DWELLINGS.items[0]));
        dwellingService.dwellingProtectionDevices = Helpers.deepClone(DWELLING_PROTECTION_DEVICES);

        fixture = TestBed.createComponent(AlarmsComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
        tick();
    })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });
  });
});
