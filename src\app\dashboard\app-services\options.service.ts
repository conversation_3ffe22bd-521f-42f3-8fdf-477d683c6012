
import { take, map } from 'rxjs/operators';
import { ApiService } from 'app/shared/services/api.service';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, Subject, SubscriptionLike as ISubscription } from 'rxjs';
import { StorageService } from 'app/shared/services/storage-new.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import {
  Coverage,
  CoverageApiResponseData,
  CoveragesData,
  CoverageItem,
  CoverageItemParsed,
  CoverageValue,
  CoverageItemChild,
  CoverageItemParsedCurrentValueData
} from 'app/app-model/coverage';
import { Helpers } from 'app/utils/helpers';

class UpdatePoliciesByUriRequestData {
  constructor(
    public subscription: ISubscription = null,
    public observable: Subject<any> = null
  ) { }
}

interface UpdatePoliciesByUriDataI {
  [url: string]: UpdatePoliciesByUriRequestData;
}


@Injectable()
export class OptionsService {

  constructor(
    private http: HttpClient,
    private apiService: ApiService,
    private storageService: StorageService,
    private apiCommonService: ApiCommonService
  ) { }

  private updatePoliciesByUriData: UpdatePoliciesByUriDataI = {};

  public defaultValuesDataSource = Helpers.deepFreeze({
    'coverages': [
      {
        'meta': null,
        'coverageCode': 'FRV',
        'coverageDescription': null,
        'values': [],
        'resourceName': 'Coverage'
      },
      {
        'meta': null,
        'coverageCode': 'BSC_EC',
        'coverageDescription': 'Dwelling Extended Coverage',
        'values': [],
        'resourceName': 'Coverage'
      },
      {
        'meta': null,
        'coverageCode': 'BSC_OSEC',
        'coverageDescription': 'Other Structures Extended Coverage',
        'values': [],
        'resourceName': 'Coverage'
      },
      {
        'meta': null,
        'coverageCode': 'BSC_PPEC',
        'coverageDescription': 'Personal Property Extended Coverage',
        'values': [],
        'resourceName': 'Coverage'
      },
      {
        'meta': null,
        'coverageCode': 'BSC_INFRV',
        'coverageDescription': 'Fair Rental Value Extended Coverage',
        'values': [],
        'resourceName': 'Coverage'
      },
      {
        'meta': null,
        'coverageCode': 'BSC_LOUEC',
        'coverageDescription': 'Loss Of Use Extended Coverage',
        'values': [],
        'resourceName': 'Coverage'
      },
      {
        'meta': null,
        'coverageCode': 'BSC_VMM',
        'coverageDescription': 'Dwelling Vandalism and MM',
        'values': [],
        'resourceName': 'Coverage'
      },
      {
        'meta': null,
        'coverageCode': 'BSC_OSVMM',
        'coverageDescription': 'Other Structures Vandalism and MM',
        'values': [],
        'resourceName': 'Coverage'
      },
      {
        'meta': null,
        'coverageCode': 'BSC_PPVMM',
        'coverageDescription': 'Personal Property Vandalism and MM',
        'values': [],
        'resourceName': 'Coverage'
      },
      {
        'meta': null,
        'coverageCode': 'BSC_FRVVMM',
        'coverageDescription': 'Fair Rental Value Vandalism and MM',
        'values': [],
        'resourceName': 'Coverage'
      },
      {
        'meta': null,
        'coverageCode': 'BSC_LOUVMM',
        'coverageDescription': 'Loss Of Use Vandalism and MM',
        'values': [],
        'resourceName': 'Coverage'
      }
    ]
  });

  public createCoverageByUri(url: string): Observable<any> {
    return this.apiCommonService.postByUri(url, {});
  }

  public updatePoliciesByUri(url: string, coverages: any): Observable<any> {
    // return this.http.put(this.apiService.url(url), coverages).map((response: Response) => {
    //   let res = response ;
    //   console.log(res)
    //   return res;
    // });

    // Prevent API stream copy issue during updating
    // let _observable: BehaviorSubject<any> = new BehaviorSubject(null);

    if (this.updatePoliciesByUriData[url]) {
      this.updatePoliciesByUriData[url].subscription && this.updatePoliciesByUriData[url].subscription.unsubscribe();
    } else {
      this.updatePoliciesByUriData[url] = new UpdatePoliciesByUriRequestData();
      this.updatePoliciesByUriData[url].observable = new Subject();
    }

    this.updatePoliciesByUriData[url].subscription = this.http.put(this.apiService.url(url), coverages).pipe(map((response) => {
      const res = response;
      return res;
    }), take(1)).subscribe(data => {
      this.updatePoliciesByUriData[url].observable.next(data);
    }, err => {
      this.updatePoliciesByUriData[url].observable.error(err);
    });

    return this.updatePoliciesByUriData[url].observable.asObservable();
  }

  public getPolicies(quoteId: string, optionsEndpoint: boolean = false): Observable<any> {
    if (optionsEndpoint) {
      return this.apiCommonService.getByUri('/quotes/' + quoteId + '/options/');
    } else {
      return this.apiCommonService.getByUri('/quotes/' + quoteId + '/coverages/');
    }
  }



  // Helpers
  // ----------------------------------------------------------------------------
  public convertPolicyItemParsedToPolicy(policyParsed: CoverageItemParsed): Coverage {
    const policy: Coverage = new Coverage();
    let policyValues: CoverageValue[] = [];

    if (policyParsed.inputType === 'Parent') {
      policyValues = policyParsed.values.map((child: CoverageItemChild) => {
        const tmpPolicyVal = new CoverageValue();
        tmpPolicyVal.valueId = child.questionIndex.toString();
        tmpPolicyVal.questionIndex = child.questionIndex.toString();
        tmpPolicyVal.value = child.currentValue;
        return tmpPolicyVal;
      });
    } else {
      const policyValue: CoverageValue = new CoverageValue();
      policyValue.valueId = '1';
      policyValue.value = policyParsed.currentValue;
      policyValues = [policyValue];
    }

    policy.coverageCode = policyParsed.coverageCode;
    policy.coverageDescription = policyParsed.description;
    policy.values = policyValues;

    return policy;
  }

  public convertPolicyItemParsedArrayToPolicyArray(policiesParsed: CoverageItemParsed[]): Coverage[] {
    const policies: Coverage[] = policiesParsed.map(item => this.convertPolicyItemParsedToPolicy(item));
    return policies;
  }

  public setPolicyItemStatusBasedOnPolicyCoverages(policy: CoverageItemParsed, policyCoverages: Coverage[]): CoverageItemParsed {
    let valuesCount = 0;
    let valuesArr: string[] = [];
    const keyValueArr: Array<string>[] = []; // [['key', 'value'], ['key', 'value']]

    if (policyCoverages && policyCoverages.length) {
      const matchedItem: Coverage = policyCoverages.find(item => item.coverageCode === policy.coverageCode);
      if (matchedItem) {
        policy.isActive = true;

        if (policy.inputType === 'Parent' && policy.values && policy.values.length) {
          const tmpArrCurrValue = [];

          policy.values.forEach((childItem: CoverageItemChild, index) => {
            if (childItem.questionIndex !== undefined || childItem.questionIndex !== null) {
            const valueItem = matchedItem.values.find(val => val.questionIndex === childItem.questionIndex.toString());
            let value = (valueItem && valueItem.value !== undefined) ? valueItem.value : '';

            valuesCount++;

            if (policy.values.length > 1 || childItem.inputType === 'Checkbox') {
              if (childItem.inputType === 'Checkbox') { value === 'Yes' ? value = 'Yes' : value = 'No'; }
              tmpArrCurrValue.push(childItem.description + ' ' + value);
              keyValueArr.push([childItem.description, value]);
            } else {
              tmpArrCurrValue.push(value);
              keyValueArr.push(['', value]);
            }

            // Add CurrentValue to Child item (to avoid issue with diseapiring values)
            if (valueItem) {
              childItem.currentValue = valueItem.value;
            }
          }

          });

          valuesArr = tmpArrCurrValue;
          policy.currentValue = tmpArrCurrValue.join(' \n');

        } else {
          let tmpCurrentValue = (matchedItem.values.length && matchedItem.values[0].value) ? matchedItem.values[0].value : '';

          // For new quote, if the default value is set in system, the value codes comes separated with '-'
          // but they need to be be saved to API with '/' separator
          // https://bostonsoftware.atlassian.net/browse/SPRC-602
          if (policy.inputType === 'AgencyCode' && tmpCurrentValue) {
            tmpCurrentValue = tmpCurrentValue.replace('-', '/');
          }

          policy.currentValue = tmpCurrentValue;

          valuesCount = 1;
          valuesArr = [tmpCurrentValue];
          keyValueArr.push(['', tmpCurrentValue]);

          if (policy.currentValue && policy.currentValue === 'false') {
            policy.isActive = false;
          }
        }
      }
    }

    const policyCurrentValueData: CoverageItemParsedCurrentValueData = new CoverageItemParsedCurrentValueData();
    policyCurrentValueData.valuesCount = valuesCount;
    policyCurrentValueData.values = valuesArr;
    policyCurrentValueData.keyValue = keyValueArr;

    policy.currentValueData = policyCurrentValueData;

    return policy;
  }

  public convertDateStringFormatForTheOption(inputDate: string): string {
    const datePatternToConvert = /\d{4}\-\d{2}\-\d{2}/ig;

    if (inputDate && datePatternToConvert.test(inputDate)) {
      const stringToReturn = inputDate.replace(datePatternToConvert, (match) => {
        const [year, month, day] = match.split('-');
        return month + '/' + day + '/' + year;
      });

      inputDate = stringToReturn;
    }

    return inputDate;
  }

  public parsePoliciesItemsToPoliciesItemsParsed(policies: CoverageItem[], extraIdExtention: string = ''): CoverageItemParsed[] {
    return policies.map((item: CoverageItem) => {
      const normalizedDescription = item.description.replace(/[^a-zA-Z0-9]/g, '');
      const policyItemParsed = Object.assign(new CoverageItemParsed(), item);
      policyItemParsed.viewUqId = item.coverageCode + '_' + normalizedDescription;

      if (extraIdExtention) {
        policyItemParsed.viewUqId += '_' + extraIdExtention;
      }

      policyItemParsed.viewHasModalbox = this.helpPoliciesItemsParsedHasModalbox(item);

      return policyItemParsed;
    });
  }

  public helpPoliciesItemsParsedHasModalbox(policy: CoverageItem): boolean {
    return policy.values.length > 0
      || policy.inputType === 'Date'
      || policy.inputType === 'AgencyCode'
      || policy.inputType === 'AgentCode'
      || policy.inputType === 'Text'
      || policy.inputType === 'Parent'
      || policy.inputType === 'FCRADisclosure'
      || policy.coverageCode === 'BSC-HOME-010067'
      || policy.coverageCode === 'BSC-DFIRE-010067';
  }

  public sortPolicyItemParsedChildItems(policy: CoverageItemParsed): CoverageItemParsed {
    if (policy.inputType === 'Parent' && policy.values.length > 1) {
      policy.values.sort((a: CoverageItemChild, b: CoverageItemChild) => {
        return a.viewOrderId - b.viewOrderId;
      });
    }
    return policy;
  }

  public setPolicyItemParsedIsNewFromAPIValue(policyItemParsed: CoverageItemParsed, policyItemParsedFromStorage: CoverageItemParsed[]): CoverageItemParsed {
    const isInStorage = policyItemParsedFromStorage.find(item => item.coverageCode === policyItemParsed.coverageCode);
    policyItemParsed.isNewFromAPI = (isInStorage) ? false : true;
    return policyItemParsed;
  }

  public setDefaultValuesForOptions(policiesParsed: CoverageItemParsed[], defaultValues: Coverage[]): CoverageItemParsed[] {
    let tmpPoliciesParsedWithDefaultValues: CoverageItemParsed[] = [];
    const clonePoliciesParsed = JSON.parse(JSON.stringify(policiesParsed));

    if (clonePoliciesParsed.length) {
      tmpPoliciesParsedWithDefaultValues = clonePoliciesParsed.map((item: CoverageItemParsed) => {
        if (item.isNewFromAPI && defaultValues.length) {
          return this.setPolicyItemStatusBasedOnPolicyCoverages(item, defaultValues);
        } else {
          return item;
        }
      });
    }

    return tmpPoliciesParsedWithDefaultValues;
  }

  public setDefaultValuesForOptionsWithReplaceOption(policiesParsed: CoverageItemParsed[], defaultValues: Coverage[], isReplace = true): CoverageItemParsed[] {
    let tmpPoliciesParsedWithDefaultValues: CoverageItemParsed[] = [];
    const clonePoliciesParsed = JSON.parse(JSON.stringify(policiesParsed));

    if (clonePoliciesParsed.length) {
      tmpPoliciesParsedWithDefaultValues = clonePoliciesParsed.map((item: CoverageItemParsed) => {
        if (defaultValues.length) {
          if (!isReplace && item.currentValue !== '' && item.currentValue !== null && item.currentValue !== 'false') {
            return item;
          }
          return this.setPolicyItemStatusBasedOnPolicyCoverages(item, defaultValues);
        } else {
          return item;
        }
      });
    }

    return tmpPoliciesParsedWithDefaultValues;
  }


  public orderObjectsArrayByProperty<T>(data: T[], accessor: string, removeSpecialCharacters: boolean = true, tryToSortByNumberFoundInAccessor: boolean = false): T[] {
    let sorted: T[] = [];
    let result: T[] = [];
    const valuesAreNumbers = false;
    // const specialCharsRegExp = /\`|\~|\!|\@|\#|\$|\%|\^|\&|\*|\(|\)|\+|\=|\[|\{|\]|\}|\||\\|\'|\<|\,|\.|\>|\?|\/|\"|\;|\:|\s/g;
    // https://bostonsoftware.atlassian.net/browse/SPRC-555
    const specialCharsRegExp = /\`|\~|\!|\@|\#|\$|\%|\^|\&|\(|\)|\+|\=|\[|\{|\]|\}|\||\\|\'|\<|\,|\.|\>|\?|\/|\"|\;|\:|\s/g;

    const dataWithUndefined = [];
    const dataWithBolleanTrue = [];
    const dataWithBolleanFalse = [];
    const dataWithNull = [];
    const dataWithValues = [];

    data.forEach(el => {
      if (el[accessor] === undefined) {
        dataWithUndefined.push(el);
      } else if (el[accessor] === true) {
        dataWithBolleanTrue.push(el);
      } else if (el[accessor] === false) {
        dataWithBolleanFalse.push(el);
      } else if (el[accessor] === null) {
        dataWithNull.push(el);
      } else {
        dataWithValues.push(el);
      }
    });


    sorted = dataWithValues.sort(function (a, b) {
      let valA, valB;
      let result;
      let accessorValueA, accessorValueB;

      if (tryToSortByNumberFoundInAccessor) {
        accessorValueA = parseInt(a[accessor], 10);
        accessorValueB = parseInt(b[accessor], 10);
      } else {
        accessorValueA = a[accessor];
        accessorValueB = b[accessor];
      }

      if (isNaN(accessorValueA) && isNaN(accessorValueB)) {
        valA = accessorValueA.toString().toLowerCase();
        valB = accessorValueB.toString().toLowerCase();
      } else {
        valA = accessorValueA;
        valB = accessorValueB;
      }


      // if (isNaN(a[accessor]) && isNaN(b[accessor])) {
      //   valA = a[accessor].toString().toLowerCase();
      //   valB = b[accessor].toString().toLowerCase();
      // } else {
      //   valA = a[accessor];
      //   valB = b[accessor];
      // }

      if (removeSpecialCharacters) {
        if (valA && typeof valA === 'string') {
          valA = valA.replace(/\*+/ig, '*');
          valA = valA.replace(specialCharsRegExp, '');
        }

        if (valB && typeof valB === 'string') {
          valB = valB.replace(/\*+/ig, '*');
          valB = valB.replace(specialCharsRegExp, '');
        }
      }

      return (valA < valB) ? -1 : (valA > valB) ? 1 : 0;
    });

    result = [...sorted, ...dataWithBolleanFalse, ...dataWithBolleanTrue, ...dataWithNull, ...dataWithUndefined];
    return result;
  }


  // DWELLING OPTIONS
  // -------------------------------------------------------------------------------

  // public createHomeCoverage(url: string, coverageData: any):Observable<CoveragesData> {
  //   return this.createDwellingOptionByUri(url, coverageData);
  // }

  public getDwellingOptions$(uri: string): Observable<CoverageApiResponseData> {
    return this.http.get(this.apiService.url(uri)).pipe(map((res: CoverageApiResponseData) => {
      // this.storageService.setStorageData('dwellingQuoteOptions', res);
      this.storageService.setStorageDataDwellingQuoteOptions(res);
      return res;
    }));
  }

  public setDefaultsToOptionsOtherThanDP1() {
    const localDefaultValuesDataSource = Helpers.deepClone(this.defaultValuesDataSource);
    // let localDefaultValuesDataSource = JSON.parse(JSON.stringify(this.defaultValuesDataSource));
    localDefaultValuesDataSource.coverages = localDefaultValuesDataSource.coverages.map((coverage, index) => {
      if (index > 0) {
        coverage.values = [{
          value: 'true'
        }];
      }
      return coverage;
    });
    return localDefaultValuesDataSource;
  }

  public createDwellingOptionsByUriWithDefaultValues(uri: string, quoteFormType: string = null): Observable<CoveragesData> {
    let defaultOptions = this.defaultValuesDataSource;
    if (quoteFormType && quoteFormType !== 'DP1' && quoteFormType !== 'Basic (DP 1)') {
      defaultOptions = this.setDefaultsToOptionsOtherThanDP1();
    }
    return this.createDwellingOptionByUri(uri, defaultOptions);
  }

  public updateDwellingOptionByUri(url: string, coverageData: any = {}): Observable<any> {
    return this.apiCommonService.putByUri(url, coverageData);
  }

  public createDwellingOptionByUri(url: string, coverageData: any = {}): Observable<CoveragesData> {
    return this.apiCommonService.postByUri(url, coverageData);
  }

  // public generateDwellingDefaultOptions(): Coverage[] {
  //   let options:any = [
  //     {
  //       coverageCode: 'FRV',
  //       coverageDescription: 'Fair Rental Value'
  //     },
  //     {
  //       coverageCode: 'BSC_EC',
  //       coverageDescription: 'Dwelling Extended Coverage',
  //     },
  //     {
  //       coverageCode: 'BSC_OSEC',
  //       coverageDescription: 'Other Structures Extended Coverage',
  //     },
  //     {
  //       coverageCode: 'BSC_PPEC',
  //       coverageDescription: 'Personal Property Extended Coverage',
  //     },
  //     {
  //       coverageCode: 'BSC_INFRV',
  //       coverageDescription: 'Fair Rental Value Extended Coverage',
  //     },
  //     {
  //       coverageCode: 'BSC_LOUEC',
  //       coverageDescription: 'Loss Of Use Extended Coverage',
  //     },
  //     {
  //       coverageCode: 'BSC_VMM',
  //       coverageDescription: 'Dwelling Vandalism and MM',
  //     },
  //     {
  //       coverageCode: 'BSC_OSVMM',
  //       coverageDescription: 'Other Structures Vandalism and MM',
  //     },
  //     {
  //       coverageCode: 'BSC_PPVMM',
  //       coverageDescription: 'Personal Property Vandalism and MM',
  //     },
  //     {
  //       coverageCode: 'BSC_FRVVMM',
  //       coverageDescription: 'Fair Rental Value Vandalism and MM',
  //     },
  //     {
  //       coverageCode: 'BSC_LOUVMM',
  //       coverageDescription: 'Loss Of Use Vandalism and MM',
  //     }
  //   ];

  //   return options = options.map( option => {
  //     const opt = new Coverage();
  //     opt.values = [new CoverageValue()];
  //     opt.coverageCode = option.coverageCode;
  //     opt.coverageDescription = option.coverageDescription;
  //     opt.values[0].value = '';
  //     return opt
  //   });
  // }
}
