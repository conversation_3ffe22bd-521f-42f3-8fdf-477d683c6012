
import {take} from 'rxjs/operators';
import { STATES } from './../../app-services/state.service';
import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { ComDriver } from 'app/app-model/driver';
import { Quote } from 'app/app-model/quote';
import { DriversService } from 'app/dashboard/app-services/drivers.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { RouteService } from 'app/shared/services/route.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { FilterOption } from 'app/app-model/filter-option';
import { Validate } from 'app/hints-and-warnings/validators';
import { PremiumsService } from 'app/dashboard/auto/premiums/premiums.service';
import { format } from 'date-fns';
@Component({
    selector: 'app-commercial-auto-drivers',
    templateUrl: './commercial-auto-drivers.component.html',
    styleUrls: ['./commercial-auto-drivers.component.scss'],
    standalone: false
})
export class CommercialAutoDriversComponent implements OnInit {

  private selectedQuote: Quote;
  private quoteId = '';
  disableButton;
  public drivers: ComDriver[] = [];
  private modalTmpDrivers: ComDriver[] = this.drivers;
  public selectedDriver: ComDriver = new ComDriver();
  public selectedDriverIndex: number;
  public licenseStates: string[];
  optionsState: FilterOption[];
  private subscription;
  private storageDriversSubscription;
  private allowDriverDataUpdate = true;
  quoteSubscription: any;

  constructor(
    private router: Router,
    private driversService: DriversService,
    private storageService: StorageService,
    private overlayLoaderService: OverlayLoaderService,
    private premiumsService: PremiumsService
  ) { }


  ngOnInit() {
    this.initialReset();
    this.initStaticOptions();
    this.subscribeSelectedQuote();
    this.subscribeDrivers();
  }

  checkForErrors($event) {
    $event ? (this.disableButton = true) : (this.disableButton = false);
  }

  private subscribeSelectedQuote(): void {
    this.quoteSubscription = this.storageService.getStorageData('selectedQuote').subscribe(
      res => {
        //  console.log('QUOTE', res);
        this.selectedQuote = res;
        if (res && res.resourceId) {
          this.quoteId = res.resourceId;
        }

      }
    );
  }

  private initialReset(): void {
    this.drivers = [];
  }

  private initStaticOptions() {
    this.setOptionsLicenseStates();
  }

  private setOptionsLicenseStates() {
    this.licenseStates  = STATES;
  }

  private subscribeDrivers() {
    this.storageDriversSubscription && this.storageDriversSubscription.unsubscribe();
    this.storageDriversSubscription = this.storageService.getStorageData('comDriverList').subscribe(data => {
      if (data && data.length > 0) {
        this.drivers = data;
        this.setIndexOfDrivers();
      } else {
        this.getDriversFromAPI();
      }

    });
  }

  public onInputDataChange(driver?): void {
    this.saveDriverData(driver);
  }

public onInputDateChange(driver, $ev): void {
   if ($ev.date) { driver.dateOfBirth = format(new Date($ev.date), 'yyyy-MM-dd'); }
    this.saveDriverData(driver);
}

  public setDefaultDriverLicenseState() {
    if (!this.selectedDriver.licenseState || this.selectedDriver.licenseState === '') {
      this.selectedDriver.licenseState = 'MA';
      // this.saveDriverDataIndependently(this.selectedDriver);
    }
  }

  private saveDriverData(data: ComDriver) {
        this.driversService.updateComDriver(this.quoteId, data.resourceId, data).subscribe(
          res => {
            this.premiumsService.rerateAll();
            this.storageService.setStorageData('comDriverList', this.drivers);
          },
          err => console.log(err)
        );
  }

  public addDriver() {
    // this.overlayLoaderService.showLoader();
    this.driversService.createComDriver(this.quoteId).subscribe( data => {
      // this.selectedDriver = data;
      // this.overlayLoaderService.hideLoader();
      this.drivers.push(data);
      this.setIndexOfDrivers();
      this.storageService.setStorageData('comDriverList', this.drivers);
    });
  }

  private setIndexOfDrivers() {
    let index = 0;
      this.drivers.forEach(driver => {
        driver.orderIndex = index;
        index ++;
      });
  }

  public deleteDriver() {
    this.overlayLoaderService.showLoader();
    this.selectedDriver = this.drivers[this.selectedDriverIndex];
    this.driversService.deleteComDriver(this.selectedDriver.meta.href).subscribe( data => {
      let driverToRemove;
      let driverToRemoveIndex;
      // driverToRemove = this.drivers.filter( (element, index) => {
      //   if (element.resourceId === this.selectedDriver.resourceId) {
      //     driverToRemoveIndex = index;
      //   }
      // });

      driverToRemove = this.drivers.find((element, index) => {
        if (element.resourceId === this.selectedDriver.resourceId) {
          driverToRemoveIndex = index;
          return true;
        }
      });

      this.drivers.splice(driverToRemoveIndex, 1);
      this.setIndexOfDrivers();
      this.storageService.setStorageData('comDriverList', this.drivers);
      this.premiumsService.rerateAll();
      this.overlayLoaderService.hideLoader();

      // this.router.navigate(['/dashboard' + url]);
      this.router.navigate(['/dashboard/commercial-auto/quotes/' + this.quoteId + '/drivers']);
   });
  }

   private getDriversFromAPI() {
    if (this.drivers && this.drivers.length === 0) {
      this.driversService.getComDriversListByUri('/quotes/' + this.quoteId + '/comdrivers').pipe(take(1)).subscribe( res => {
        if (res.items && res.items.length === 0) {
          this.addDriver();
        } else {
          this.drivers = res.items;
          this.storageService.setStorageData('comDriverList', this.drivers);
        }
      });
    }
  }

  public selected($event, data: ComDriver, field: string) {
      data[field] = $event.text;
      this.saveDriverData(data);
  }

  public selectDriverForDelete(index: number): void {
    this.selectedDriverIndex = index;
  }

  public confirmDelete() {
    console.log('Deleted driver with index: ', this.selectedDriverIndex);
    this.deleteDriver();
  }

  public confirmCanceled($event) {
    console.log('Cancel: ', $event);
  }

  public trackByFn(index) {
    return index;
  }

  public ValidateField(data) {
      return Validate.isEmptyValue(data);
    }
  }

