import { inject, TestBed } from '@angular/core/testing';

import { data as AUTO_COVERAGES } from 'testing/data/quotes/coverages/auto';
import { data as HOME_COVERAGES } from 'testing/data/quotes/coverages/home';
import { HOME_STANDARD_COVERAGES } from 'testing/data/quotes/coverages/home-standard';
import { AUTO_QUOTE as AUTO_QUOTE } from 'testing/data/quotes/quote-auto';
import { data as VEHICLE } from 'testing/data/quotes/vehicle';
import { data as VEHICLE_ADDITIONAL_OPTIONS } from 'testing/data/vehicles/additional-options';
import { data as VEHICLE_STANDARD_COVERAGES } from 'testing/data/vehicles/standard-coverages';
import {
    DataCustomMatchers, expect, expectLastConnectionPayload, expectLastConnectionUrl
} from 'testing/helpers/all';
import { MockBackend, setupMockBackend } from 'testing/setups/mock-backend';

import { Coverage, CoverageForVehicle, CoverageValue } from 'app/app-model/coverage';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Helpers } from 'app/utils/helpers';

import { CoveragesService } from './coverages.service';
import { QuotesService } from './quotes.service';
import { SpecsService } from './specs.service';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { AgencyUserService } from 'app/shared/services/agency-user.service';

describe('Service: Coverages', () => {
  let service: CoveragesService;
  let mockBackend: MockBackend;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        CoveragesService,
        StorageService,
        StorageGlobalService,
        {
          provide: QuotesService, useValue: {}  // unused
        },
        {
          provide: SpecsService, useValue: {}  // unused
        },
        AgencyUserService
      ]
    });

    jasmine.addMatchers(DataCustomMatchers);
  });

  beforeEach(inject([CoveragesService], (_service: CoveragesService) => {
    service = _service;
  }));

  it('can instantiate service when inject service', () => {
    expect(service).toBeTruthy();
  });

  it('lets to filter out empty coverages', () => {
    const filtered = service.filterEmptyCoverages(Helpers.deepClone(HOME_STANDARD_COVERAGES.items[0].coverages));

    expect(filtered).toEqual([
      jasmine.objectContaining({ coverageCode: 'APDED' }),
      jasmine.objectContaining({ coverageCode: 'WHDED' }),
      jasmine.objectContaining({ coverageCode: 'DWELL' }),
      jasmine.objectContaining({ coverageCode: 'OS' }),
      jasmine.objectContaining({ coverageCode: 'PL' }),
      jasmine.objectContaining({ coverageCode: 'MEDPM' }),
      jasmine.objectContaining({ coverageCode: 'RCDWELL' }),
      jasmine.objectContaining({ coverageCode: 'RCCONT' })
    ]);
  });

  describe('when working with Auto coverages', () => {
    it('allows to create coverage by URI and save it in storage', inject(
      [StorageService], (storageService: StorageService) => {
        mockBackend = setupMockBackend(AUTO_COVERAGES.items[0]);
        spyOn(storageService, 'updateVehiclesListSingleItem');

        service.createCoverageByUri('/quotes/quote-id/coverages', AUTO_COVERAGES.items[0]).subscribe();

        expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/coverages');
        expectLastConnectionPayload(mockBackend).toEqual(AUTO_COVERAGES.items[0]);
        expect(storageService.updateVehiclesListSingleItem).toHaveBeenCalledWith(AUTO_COVERAGES.items[0].parentId, AUTO_COVERAGES.items[0]);
      }));

    it('allows to parse Coverage to specific CoverageForVehicle', () => {
      const parsed = service.convertCoverageToCoverageParsedData(
        AUTO_COVERAGES.items[0].coverages[0], VEHICLE.resourceId, AUTO_QUOTE.quoteSessionId
      );

      expect(parsed).toHaveSamePropertiesAs(new CoverageForVehicle());
    });

    it('allows to convert ApiResponse to CoverageForVehicle list', () => {
      const parsed = service.convertCoverageApiResponseDataToCoverageParsedDataArray(AUTO_COVERAGES);

      expect(parsed[0]).toHaveSamePropertiesAs(new CoverageForVehicle());
    });

    it('allows to convert multiple ApiResponses to CoverageForVehicle list', () => {
      const parsed = service.convertCoverageApiResponseDataArrayToCoverageParsedDataArray(
        [AUTO_COVERAGES, VEHICLE_ADDITIONAL_OPTIONS, VEHICLE_STANDARD_COVERAGES]
      );

      expect(parsed[0]).toHaveSamePropertiesAs(new CoverageForVehicle());
    });
  });

  describe('when working with Home coverages', () => {
    it('allows to retrieve coverages list by URI and save them in storage', inject(
      [StorageService], (storageService: StorageService) => {
        mockBackend = setupMockBackend(HOME_COVERAGES);
        spyOn(storageService, 'setStorageData');

        service.getHomeQuoteCoverages$('/quotes/quote-id/coverages').subscribe();

        expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/coverages');
        expect(storageService.setStorageData).toHaveBeenCalledWith('homeQuoteCoverages', HOME_COVERAGES);
      }));

    it('allows to generate default home standard coverage values', () => {
      const coverages = service.generateHomeDefaultStandardCoverages();

      expect(coverages).toEqual([
        jasmine.objectContaining({ coverageCode: 'APDED' }),
        jasmine.objectContaining({ coverageCode: 'DWELL' }),
        jasmine.objectContaining({ coverageCode: 'LOU' }),
        jasmine.objectContaining({ coverageCode: 'MEDPM' }),
        jasmine.objectContaining({ coverageCode: 'OS' }),
        jasmine.objectContaining({ coverageCode: 'PL' }),
        jasmine.objectContaining({ coverageCode: 'PP' }),
        jasmine.objectContaining({ coverageCode: 'RCCONT' }),
        jasmine.objectContaining({ coverageCode: 'RCDWELL' }),
        jasmine.objectContaining({ coverageCode: 'WHDED' })
      ]);
    });

    it('allows to create home coverage with default values', () => {
      mockBackend = setupMockBackend(HOME_STANDARD_COVERAGES.items[0]);

      service.createHomeCoverageByUriWithDefaultValues(
        '/quotes/quote-id/coveragesStandard',
        [new Coverage('APDED', '', {}, '', [
          { value: '500', description: '' }
        ]), new Coverage('PL', '', {}, '', [
            { value: '40000', description: '' }
        ])]
      ).subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/coveragesStandard');
      expectLastConnectionPayload(mockBackend).toEqual({
        coverages: jasmine.arrayContaining([
          jasmine.objectContaining({
            coverageCode: 'APDED',
            values: [jasmine.objectContaining({ value: '500' })]
          }),
          jasmine.objectContaining({
            coverageCode: 'PL',
            values: [jasmine.objectContaining({ value: '40000' })]
          })
        ])
      });

      const defaultStandardCoverages = [];
      service.generateHomeDefaultStandardCoverages().forEach((cov: Coverage) => {
        defaultStandardCoverages.push(jasmine.objectContaining({ coverageCode: cov.coverageCode }));
      });
      expectLastConnectionPayload(mockBackend).toEqual({
        coverages: jasmine.arrayContaining(defaultStandardCoverages)
      });
    });
  });
});
