export interface AttachmentData {
	typeCode: string;
	typeDescription: string;
	data: string;
  image: string;
  thumbnail: string;
}

export interface RequiredDocument {
	typeCode: string;
	typeDescription: string;
}

export interface LineItem {
	amount: number;
	description: string;
}

export interface RmvFee {
	total: number;
	lineItems: LineItem[];
}

export interface ValidateDetail {
	attachmentData: AttachmentData[];
	requiredDocuments: RequiredDocument[];
	rmvFeeRequired: boolean;
	rmvFees: RmvFee;
}

export interface Owner {
	id: number;
	firstName: string;
	lastName: string;
	dateOfBirth: string;
	license: string;
	licenseState: string;
}

export interface Vehicle {
	id: number;
	lookupType: string;
	usage: string;
	plateNumber: string;
	vin: string;
	plateType: string;
	odometer: string;
	odometerCode: string;
	registrationType: string;
	reassignedPlate: string;
	registrationReason: string;
	ownership: string;
	condition: string;
	primaryColor: string;
	transmission: string;
	passengers: string;
	outOfStateTitleNumber: string;
	titleState: string;
	titleIssueDate: string;
}

export interface GaragingAddres {
	type: string;
	referenceType: string;
	referenceId: string;
	street: string;
	unitOrApt: string;
	city: string;
	state: string;
	zip: string;
}

export interface Addres {
	street: string;
	unitOrApt: string;
	city: string;
	state: string;
	zip: string;
}

export interface Seller {
	businessName: string;
	firstName: string;
	lastName: string;
	address: Addres;
}

export interface PurchaseAndSalesInformation {
	purchaseDate: string;
	purchaseState: string;
	seller: Seller;
	taxExempt: string;
	taxExemptType: string;
	purchaseType: string;
	dealerFid: string;
	totalSalePrice: string;
	auctionSale: string;
	maResidentAtTimeOfPurchase: string;
	maSalesTaxPreviouslyPaid: string;
	proofOfNoTaxRequired: string;
}

export interface EStampInfo {
	effectiveDate: string;
	writingCompanyName: string;
	companyCode: number;
	agencyName: string;
	signedBy: string;
}

export interface Lessor {
	id: number;
	fid: string;
}

export interface Lienholder {
	id: number;
	code: string;
}

export interface BusinessOwner {
	id: number;
	fid: string;
}

export interface RmvServicesUserEnteredData {
	owners: Owner[];
	vehicles: Vehicle[];
	garagingAddress: GaragingAddres;
	purchaseAndSalesInformation: PurchaseAndSalesInformation;
	eStampInfo: EStampInfo;
	lessors: Lessor[];
	lienholders: Lienholder[];
	businessOwners: BusinessOwner[];
}

export interface EvrLiteAdd {
	transactionType: string;
	transactionTypeDesc: string;
	workflowType: string;
	ownership: string;
	agencyId: string;
	userId: number;
	email: string;
	atlasValidatedTransactionKey: string;
	atlasRegistrationKey: string;
	status: string;
	validateDetails: ValidateDetail;
	rmvServicesUserEnteredData: RmvServicesUserEnteredData;
}
