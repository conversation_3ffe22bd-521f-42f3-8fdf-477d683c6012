import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { StubOptionsViewComponent } from 'testing/stubs/components/options-view.component';

import { StorageService } from 'app/shared/services/storage-new.service';

import { CarrierOptionsComponent } from './carrier-options.component';

describe('CarrierOptionsComponent', () => {
  let component: CarrierOptionsComponent;
  let fixture: ComponentFixture<CarrierOptionsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [
        CarrierOptionsComponent,
        StubOptionsViewComponent
      ],
      providers: [
        StorageService
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CarrierOptionsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });
});
