<section class="section section--compact u-spacing--2-5">
  <div class="u-flex u-flex--spread">
    <div class="">
      <h1 class="o-heading">Loss History</h1>
    </div>
    <div>
      <button class="modalDeleteLossHistory o-btn o-btn--action o-btn--i_cancel" id="btn-delate-all-loss-data">Delete All Loss Data</button>
    </div>
  </div>

  <div class="row u-spacing--1">
    <div class="col-xs-12">
      <div class="box box--silver">

        <div class="row o-columns" >
          <div class="col-xs-12">

            <p *ngIf="!lossFreeYearsRequired">Selected Plan(s) do not require loss history.</p>

            <p *ngIf="lossFreeYearsRequired && requirementYears < 1" class="u-color-orange">Selected Plan(s) require number of Loss Free Years.</p>

            <p *ngIf="lossFreeYearsRequired && requirementYears > 1" class="u-color-orange">Selected Plan(s) require number of losses in each of the past {{requirementYears}} years.</p>

            <p class="u-color-orange" *ngIf="(((lossHistory && lossHistory.items && lossHistory.items.length < 3) || (lossHistory && !lossHistory.items))) && lossDetailsRequired">Enter loss details for the 3 most recent losses only.</p>

            <hr *ngIf="lossCountRequired || lossFreeYearsRequired"/>

          <!--   <p class="u-color-orange" *ngIf="requirementYears === 1">Selected Plan(s) require number of losses for the last year.</p>


          -->
          <span style="font-style: italic;" *ngIf="lossFreeYearsRequired">Loss information can affect both loss surcharges and loss free credits.</span>
          <br><br>
            <table class="form-table" *ngIf="lossFreeYearsRequired">

              <tr class="form-table__row" [class.is-required-field]="lossFreeYearsRequired && !lossHistory.lossFreeYears">

                <td class="form-table__cell u-width-250px">
                  <label for="">Total Num. of Loss Free Years:</label>
                </td>
                <td class="form-table__cell u-width-125px">
                  <sm-autocomplete
                    #refTotalNumberOfLossFreeYears
                    [required]="true"
                    [id]="'total-number-loss-free-years'"
                    [name]="'total-number-loss-free-years'"
                    [options]="totalNumberOfLossFreeYears"
                    [activeOption]="selectedTotalNumberOfLossFreeYears"
                    (onSelect)="updateTotalNumberOfLossFreeYears($event,refTotalNumberOfLossFreeYears, modalChangeNumLossesValidationMsg)">
                  </sm-autocomplete>
                </td>
                <td class="form-table__cell"><p class="u-t-muted" *ngIf="selectedTotalNumberOfLossFreeYears > 0">(No losses since {{displayDate}})</td>
              </tr>
            </table>

            <div *ngIf="rowsQty?.length && selectedTotalNumberOfLossFreeYears !== 'Unspecified' && lossFreeYearsRequired">
              <div *ngFor="let row of rowsQty; let itemIndex = index">
                <hr/>
                <table class="form-table">
                  <tbody>
                    <tr class="form-table__row" >
                        <td class="form-table__cell u-width-250px">
                          <label for="">Losses from {{ row.from }} - {{ row.to }}:</label>
                        </td>
                        <td class="form-table__cell u-width-55px" [ngClass]="{'is-required-field': (lossCountRequired || lossDetailsRequired) && !lossHistory[row.lossesYear] }">
                          <sm-autocomplete
                            #refLossesFrom
                            [required]="(lossCountRequired || lossDetailsRequired) && !lossHistory[row.lossesYear]"
                            [name]="row.lossesYear"
                            [id]="row.lossesYear"
                            [options]="getLossesOptions(itemIndex)"
                            [activeOption]="lossHistory[row.lossesYear]"
                            (onSelect)="updateSingleLossHistoryCount($event, row.lossesYear,refLossesFrom, modalLossDetailsValidationMsg,row.from)">
                          </sm-autocomplete>
                        </td>
                    </tr>
                  </tbody>
                </table>
                <table *ngIf="row.items && row.items.length" class="form-table form-table--full">
                  <tbody>
                    <tr *ngFor="let loss of row.items" class="form-table__row">
                      <td class="form-table__cell u-width-15px"></td>
                      <td class="form-table__cell u-width-100px">
                        {{ helpDisplayLossDate(loss.lossDate) }}
                      </td>
                      <td class="form-table__cell">
                        {{ loss.descriptionParsed }}
                      </td>
                      <td class="form-table__cell u-width-110px u-align-right">
                        {{ loss.lossAmount | currency:'USD':true:'1.0-2' }}
                      </td>
                      <td *ngIf="isAnyCatastrophic(row.items)" class="form-table__cell u-width-120px u-padd--left-3 u-align-right">
                        <span *ngIf="loss.catastrophicLossInd">Catastrophic</span>
                      </td>
                      <td class="form-table__cell u-width-130px u-align-right">
                        <button type="button" class="o-link o-link--blue u-spacing--right-1"
                          (click)="editSingleLossHistory(loss, modalEditSingleLossHistory,row)">Edit</button>
                        <button type="button" class="o-link o-link--blue delete-single-loss"
                          (click)="deleteSingleLossHistory(loss)">Delete</button>
                      </td>
                    </tr>
                  </tbody>
                </table>
                <div *ngIf="lossHistory[row.lossesYear] && (!row.items || (row.items && (('_' + row.items.length) !== lossHistory[row.lossesYear])))">
                  <button *ngIf="((lossHistory && lossHistory.items && lossHistory.items.length < 3) || (lossHistory && !lossHistory.items))"
                    type="button" id="modal-details-{{row.lossesYear}}" class="o-btn o-btn--action o-btn--pointer o-btn--i_round-plus o-link--blue u-spacing--left-3"
                    (click)="addLossHistory(modalAddLossHistory, row)">Add loss details</button>
                </div>
              </div>
            </div>

          </div>
        </div>

      </div>
    </div>
  </div>
</section>

<app-modalbox #modalDeleteLossHistory [launcher]="'.modalDeleteLossHistory'">
  <h1 class="o-heading o-heading--red u-spacing--bottom-1-5">Delete All Loss Data?</h1>
  <div>
    This would remove all loss history data.
  </div>
  <div class="row u-spacing--2">
    <div class="col-xs-12 u-align-right">
      <hr class="o-hr u-spacing--bottom-1-5"/>
      <button (click)="deleteLossHistoryCollection(); closeModal(modalDeleteLossHistory)" class="o-btn u-spacing--left-2">Yes, Delete Data</button>
      <button (click)="closeModal(modalDeleteLossHistory)" class="o-btn o-btn--idle u-spacing--left-2">Cancel</button>
    </div>
  </div>
</app-modalbox>

<app-modalbox #modalChangeNumLossesValidationMsg >
  <h1 class="o-heading o-heading--red u-spacing--bottom-1-5">Verify Information</h1>
  <div>
    Loss Free Years information is incompatible with Number of Losses.
  </div>
  <div class="row u-spacing--2">
    <div class="col-xs-12 u-align-right">
      <hr class="o-hr u-spacing--bottom-1-5"/>
      <button (click)="closeModal(modalChangeNumLossesValidationMsg)" class="o-btn o-btn--idle u-spacing--left-2">Ok</button>
    </div>
  </div>
</app-modalbox>

<app-modalbox #modalLossDetailsValidationMsg >
  <h1 class="o-heading o-heading--red u-spacing--bottom-1-5">Verify Information</h1>
  <div>
    Number of Loss Details incompatible with Number of Losses.
  </div>
  <div class="row u-spacing--2">
    <div class="col-xs-12 u-align-right">
      <hr class="o-hr u-spacing--bottom-1-5"/>
      <button (click)="closeModal(modalLossDetailsValidationMsg)" class="o-btn o-btn--idle u-spacing--left-2">Ok</button>
    </div>
  </div>
</app-modalbox>



<app-confirmbox
  [launcher]="'.delete-single-loss'"
  [question]="'Are you sure you want to delete this loss?'"
  [confirmBtnText]="'Yes, delete it'"
  (onAccept)="confirmDeleteLoss()">
</app-confirmbox>

<app-modalbox #modalAddLossHistory [css]="'size--4'">
  <h1 class="o-heading o-heading--red u-spacing--bottom-1-5">Add loss details</h1>
  <div class="box box--silver u-spacing--1-5" *ngIf="modalAddLossHistoryData">
    <p>Please provide loss details between {{ modalAddLossHistoryData.from }} and {{ modalAddLossHistoryData.to }}</p>
    <div class="u-spacing--1">
      <table class="form-table">
        <thead class="form-table__thead">
          <th class="form-table__cell">
            <span class="o-heading o-heading--label">Date</span>
          </th>
          <th class="form-table__cell">
            <span class="o-heading o-heading--label">Type of loss</span>
            </th>
          <th class="form-table__cell">
            <span class="o-heading o-heading--label">Amount</span>
          </th>
          <th class="form-table__cell">
            <span class="o-heading o-heading--label">Catastrophic</span>
          </th>
        </thead>
        <tbody>
          <tr *ngFor="let singleLoss of lossHistoryDataRows; let ind = index;" class="form-table__row">
            <td class="form-table__cell" [ngClass]="{'is-required-field': !checkLossItemDateInRange(singleLoss.lossDate)}">
              <app-datepicker-input
                [id]="'lossHistoryItemDate'+ind"
                [name]="'lossHistoryItemDate'+ind"
                [required]="true"
                [selectDate]="singleLoss.lossDate"
                (onDateChange)="updateSingleLoss($event, ind, 'lossDate')">
              </app-datepicker-input>
            </td>
            <td class="form-table__cell"  [ngClass]="{'is-required-field': refLossHistoryItemType.ngModel?.invalid}">
              <sm-autocomplete
                #refLossHistoryItemType
                [name]="'lossHistoryItemType'+ind"
                [id]="'lossHistoryItemType'+ind"
                [required]="true"
                [options]="lossesTypes"
                [activeOption]=""
                (onSelect)="updateSingleLoss($event.id, ind, 'description')">
              </sm-autocomplete>
            </td>
            <td class="form-table__cell" [ngClass]="{'is-required-field': singleLoss.lossAmount==null || singleLoss.lossAmount<0}">
              <input
                #refLossHistoryItemAmount
                type="text" id="lossHistoryItemAmount{{ind}}"
                name="lossHistoryItemAmount{{ind}}"
                [required]="true"
                [textMask]="{mask: moneyMask}"
                [(ngModel)]="singleLoss.lossAmount"
                (ngModelChange)="updateSingleLoss(singleLoss.lossAmount, ind, 'lossAmount')">
            </td>
            <td class="form-table__cell u-align-center u-padd--left-0">
              <label class="o-checkable" [htmlFor]="'lossHistoryItemCatastrophic' + ind">
                <input type="checkbox" [id]="'lossHistoryItemCatastrophic' + ind" [(ngModel)]="singleLoss.catastrophicLossInd">
                <i class="o-btn o-btn--checkbox"></i>
                Yes
              </label>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
  <div class="row u-spacing--2">
    <div class="col-xs-12 u-align-right">
      <hr class="o-hr u-spacing--bottom-1-5"/>
      <button (click)="addNewLossHistoryItems(lossHistoryDataRows); closeModal(modalAddLossHistory)" class="o-btn u-spacing--left-2" [disabled]="invalidLossDetailModal">Save Details</button>
      <button (click)="closeModal(modalAddLossHistory)" class="o-btn o-btn--idle u-spacing--left-2">Cancel</button>
    </div>
  </div>
</app-modalbox>

<app-modalbox #modalEditSingleLossHistory [css]="'size--4'">
  <h1 class="o-heading o-heading--red u-spacing--bottom-1-5">Edit loss details</h1>
  <div class="box box--silver u-spacing--1-5" *ngIf="lossItemToEdit">
    <table class="form-table">
      <thead class="form-table__thead">
        <th class="form-table__cell">
          <span class="o-heading o-heading--label">Date</span>
        </th>
        <th class="form-table__cell">
          <span class="o-heading o-heading--label">Type of loss</span>
          </th>
        <th class="form-table__cell">
          <span class="o-heading o-heading--label">Amount</span>
        </th>
        <th class="form-table__cell">
          <span class="o-heading o-heading--label">Catastrophic</span>
        </th>
      </thead>
      <tbody>
        <tr class="form-table__row">
          <td class="form-table__cell" [ngClass]="{'is-required-field': !checkLossItemDateInRange(lossItemToEdit.lossDate)}">
            <app-datepicker-input
              [id]="'lossHistoryItemDateSingle'"
              [name]="'lossHistoryItemDateSingle'"
              [required]="true"
              [selectDate]="lossItemToEdit.lossDate"
              (onDateChange)="updateSingleLoss($event, null, 'lossDate')">
            </app-datepicker-input>
          </td>
          <td class="form-table__cell"  [ngClass]="{'is-required-field': refLossHistoryItemType.ngModel?.invalid}">
            <sm-autocomplete
              #refLossHistoryItemType
              [name]="'lossHistoryItemTypeSingle'"
              [id]="'lossHistoryItemTypeSingle'"
              [options]="lossesTypes"
              [activeOption]="lossItemToEdit.description"
              (onSelect)="updateSingleLoss($event.id, null, 'description')">
            </sm-autocomplete>
          </td>
          <td class="form-table__cell" [ngClass]="{'is-required-field': lossItemToEdit.lossAmount==null || lossItemToEdit.lossAmount<0}">
            <input
              #refLossHistoryItemAmount
              required
              type="text" id="lossHistoryItemAmountSingle"
              name="lossHistoryItemAmountSingle"
              [ngModel]="lossItemToEdit.lossAmount | toNumber | currency:'USD':true:'1.0-2'"
              (ngModelChange)="updateSingleLoss($event, null, 'lossAmount')">
          </td>
          <td class="form-table__cell u-align-center u-padd--left-0">
            <label class="o-checkable" [htmlFor]="'lossHistoryItemCatastrophicSingle'">
              <input type="checkbox" [id]="'lossHistoryItemCatastrophicSingle'" [(ngModel)]="lossItemToEdit.catastrophicLossInd">
              <i class="o-btn o-btn--checkbox"></i>
              Yes
            </label>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="row u-spacing--2">
    <div class="col-xs-12 u-align-right">
      <hr class="o-hr u-spacing--bottom-1-5"/>
      <button (click)="updateSingleLossHistory(); closeModal(modalEditSingleLossHistory)" class="o-btn u-spacing--left-2" [disabled]="isEditModelInvalid(lossItemToEdit)">Save Details</button>
      <button (click)="closeModal(modalEditSingleLossHistory)" class="o-btn o-btn--idle u-spacing--left-2">Cancel</button>
    </div>
  </div>
</app-modalbox>

<app-leave-quote #leaveQuote></app-leave-quote>
