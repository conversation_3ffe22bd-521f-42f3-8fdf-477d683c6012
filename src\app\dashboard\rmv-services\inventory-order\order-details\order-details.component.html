<div class="box box--silver" style="margin-bottom: 20px;">
    <section>
    <div class="row">
        <div class="col-md-12">
            <b>Order Tracking Number:</b><span>  {{order.inventoryOrderSummary.inventoryOrderTrackingNumber}}</span>
        </div>
        <div class="row">
        <div class="col-md-6"><b style="margin-left: 14px;">Order Status:</b><span>  {{order.inventoryOrderSummary.inventoryOrderDisplayStatus}}</span></div>
      </div>
        </div>
       
    </section>
    <br>
    <section>
        <table class="table table--compact table--hoverable-grey">
            <thead class="table__thead">
              <tr class="">
                <th class="table__th ">Description</th>
                <th class="table__th ">Number of Units</th>
      
  
              </tr>
            </thead>
            <tbody class="table__tbody">
              <tr class="table__tr" *ngFor="let item of order.inventoryOrderItems">
                    <td class="table__td">{{item.inventoryTypeDescription}}</td>
                    <td class="table__td">{{item.numberOfUnits}}</td>
                   
              </tr>
            </tbody>
            </table>
   
    </section>   

   </div>