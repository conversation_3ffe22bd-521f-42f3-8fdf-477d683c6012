import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { StubDatepickerInputComponent } from 'testing/stubs/components/datepicker-input.component';
import { StubLeaveQuoteComponent } from 'testing/stubs/components/leave-quote.component';
import { StubPlansSelectorComponent } from 'testing/stubs/components/plans-selector.component';
import { MockRouterProvider } from 'testing/stubs/router.provider';
import { StubAgencyUserServiceProvider } from 'testing/stubs/services/agency-user.service.provider';
import { StubClientsServiceProvider } from 'testing/stubs/services/clients.service.provider';
import { StubDriversServiceProvider } from 'testing/stubs/services/drivers.service.provider';
import {
    StubOverlayLoaderServiceProvider
} from 'testing/stubs/services/overlay-loader.service.provider';
import { StubQuotesServiceProvider } from 'testing/stubs/services/quotes.service.provider';
import { StubSpecsServiceProvider } from 'testing/stubs/services/specs.service.provider';
import { StubSubsServiceProvider } from 'testing/stubs/services/subs.service.provider';

import { RouteService } from 'app/shared/services/route.service';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { StorageService } from 'app/shared/services/storage-new.service';

import { NewUmbrellaCreateFormComponent } from './new-umbrella-create-form.component';

describe('NewUmbrellaCreateFormComponent', () => {
  let component: NewUmbrellaCreateFormComponent;
  let fixture: ComponentFixture<NewUmbrellaCreateFormComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [
        NewUmbrellaCreateFormComponent,
        StubDatepickerInputComponent,
        StubLeaveQuoteComponent,
        StubPlansSelectorComponent
      ],
      providers: [
        StubAgencyUserServiceProvider,
        StubSubsServiceProvider,
        StubQuotesServiceProvider,
        StubDriversServiceProvider,
        StubClientsServiceProvider,
        StubOverlayLoaderServiceProvider,
        StorageService,
        StubSpecsServiceProvider,
        RouteService,
        StorageGlobalService,
        MockRouterProvider
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(NewUmbrellaCreateFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
