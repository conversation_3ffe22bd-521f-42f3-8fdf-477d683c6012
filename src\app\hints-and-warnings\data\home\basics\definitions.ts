import { WARNING_GROUPS, WarningDefinitionI, AdditionalDataI, AdditionalDataHomeBasicsCoverages} from 'app/hints-and-warnings/model/warnings';
import { CoverageStandard } from 'app/app-model/basics';
import { CoverageForVehicle } from 'app/app-model/coverage';
import { Validate } from 'app/hints-and-warnings/validators';
import { QuotesService } from 'app/dashboard/app-services/quotes.service';

function requiredField(value: any): boolean {
  return Validate.isEmptyValue(value);
}
function generateViewUrl(fullObj: CoverageStandard): string {
  return  '/dashboard/home/<USER>/' + fullObj['vehicleResourceId'] + '/basics';
}
function generateViewFieldId(fullObj: CoverageForVehicle): string {
  let fieldId = '';
  fieldId = 'field_' + fullObj.coverageCode;
  return fieldId;
}
function generateLabel(fullObj: CoverageForVehicle): string {
  let fieldName = '';
  switch (fullObj.coverageCode) {
    case 'DWELL':
      fieldName = 'Dwelling';
      break;
    case 'PP':
      fieldName = 'Personal Property';
      break;
    case 'OS':
      fieldName = 'Other Structures';
      break;
    case 'LOU':
      fieldName = 'Loss Of Use';
      break;
    case 'PL':
      fieldName = 'Personal Liability';
      break;
    case 'MEDPM':
      fieldName = 'Medical Payments';
      break;
    case 'APDED':
      fieldName = 'All Perils';
      break;
    case 'RCCONT':
      fieldName = 'Replacement Cost Contents';
      break;
  }
  return 'Required Basics ' + fieldName + '.';
}



function checkQuoteFormType(quote, coverageCode): boolean {

if (quote && (quote.formType.replace(' ', '') === 'HO6' || quote.formType.replace(' ', '') === 'HO4') && (coverageCode === 'DWELL' || coverageCode === 'OS')) {
    return false;
  }
  return true;
}


function validateWindHailStandard(value: string, fullObj, data: AdditionalDataHomeBasicsCoverages): boolean {
  let isInvalid = false;
  let windHailCurrentValueIsPercent = false;
  let windHailValue = 0;
  let windHailCalculatedValue = 0;
  let allPerilsValue = 0;
  let dwellingValue = 0;
  let personalPropertyValue = 0;

  const quoteFormTypes = QuotesService.simplifyQuoteFormType(data.quote.formType);
  const calculateForHO6orHO4FormType: boolean = ['HO4', 'HO6'].some(el => quoteFormTypes.indexOf(el) !== -1);

  const dwellingCoverage = data.observedData.find(el => el.coverageCode === 'DWELL');
  const allPerilsCoverage = data.observedData.find(el => el.coverageCode === 'APDED');
  const personalProperty = data.observedData.find(el => el.coverageCode === 'PP');

  if (dwellingCoverage && dwellingCoverage.value) {
    const tmpVal = parseInt(dwellingCoverage.value, 10);
    dwellingValue = !isNaN(tmpVal) ? tmpVal : 0;
  }

  if (allPerilsCoverage && allPerilsCoverage.value) {
    const tmpVal = parseInt(allPerilsCoverage.value, 10);
    allPerilsValue = !isNaN(tmpVal) ? tmpVal : 0;
  }

  if (personalProperty && personalProperty.value) {
    const tmpVal = parseInt(personalProperty.value, 10);
    personalPropertyValue = !isNaN(tmpVal) ? tmpVal : 0;
  }


  if (value === 'None') {
    isInvalid = false;
    return false;
  } else if (value && value.indexOf('%') !== -1) {
    windHailCurrentValueIsPercent = true;
    windHailValue = parseInt(value.replace('%', '').trim(), 10);

    // https://bostonsoftware.atlassian.net/browse/SPRC-613
    if (calculateForHO6orHO4FormType) {
      windHailCalculatedValue = !isNaN(windHailValue) ? personalPropertyValue * (windHailValue / 100) : 0;
    } else {
      windHailCalculatedValue = !isNaN(windHailValue) ? dwellingValue * (windHailValue / 100) : 0;
    }

  } else {
    windHailValue = parseInt(value, 10);
    windHailCalculatedValue = !isNaN(windHailValue) ? windHailValue : 0;
  }

  if (windHailCalculatedValue <= allPerilsValue) {
    isInvalid = true;
  }

  // If value for Dwelling field is not set, mark Wind / Hail as valid
  // if (windHailCurrentValueIsPercent && !dwellingValue) {
  if (windHailCurrentValueIsPercent && !dwellingValue && !calculateForHO6orHO4FormType) {
    isInvalid = false;
  }

  return isInvalid;
}

function windHailFieldIsInvalidTravelers(value: string, fullObj, data: AdditionalDataHomeBasicsCoverages): boolean {
  let isValid = true;
  const windHailCurrentValue: string = value;
  const validValues: string[] = ['None', '1%', '2%', '5%'];

  isValid = validValues.some(el => el === windHailCurrentValue);

  return !isValid;
}

/**
 * Validation for basics Data
 * For Model: Coverage Standard
 */

const parsedBasicsCoverageTravelers = ['283'];
const basicsNonSmokersCarriers = ['310', '304', '292'];

const basicsCoverages: WarningDefinitionI = {
  id: 'value',
  deepId: 'value',
  viewUri: generateViewUrl,
  viewFieldId: generateViewFieldId,
  warnings: [
    {
      label: (value, fullObj) => 'Required Dwelling to be set.',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeBasicsCoverages
      ) => {
        let returnValue = false
        if(fullObj.coverageCode === 'DWELL') {
         returnValue = Validate.isEmptyValue(value) && additionalData.quote.formType.includes('HO3')
        }
        return returnValue
      },
      group: WARNING_GROUPS.general,
      carriers: []
    },
    {
      label: (value, fullObj) => 'Required Other Structures to be set.',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeBasicsCoverages
      ) => {
        let returnValue = false
        if(fullObj.coverageCode === 'OS') {
         returnValue = Validate.isEmptyValue(value) && additionalData.quote.formType.includes('HO3')
        }
        return returnValue
      },
      group: WARNING_GROUPS.general,
      carriers: []
    },
    {
      label: (value, fullObj) => 'Required Personal Property to be set.',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeBasicsCoverages
      ) => {
        let returnValue = false
        if(fullObj.coverageCode === 'PP') {
          returnValue =  Validate.isEmptyValue(value) 
        }
        return returnValue
      },
      group: WARNING_GROUPS.general,
      carriers: []
    },
    {
      label: (value, fullObj) => 'Required Loss of Use to be set.',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeBasicsCoverages
      ) => {
        let returnValue = false
        if(fullObj.coverageCode === 'LOU') {
          returnValue =  Validate.isEmptyValue(value) 
        }
        return returnValue
      },
      group: WARNING_GROUPS.general,
      carriers: []
    },
    {
      label: (value, fullObj) => 'Required Non-Smoker to be set.',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeBasicsCoverages
      ) => {
        let returnValue = false
        if(fullObj.coverageCode === 'NONSMOKE') {
         returnValue = Validate.isRadioInputValueNotSet2(value) &&
         Validate.isRequiredForSelectedPlans(basicsNonSmokersCarriers,  additionalData.quoteSelectedPlansIds)
        }
        return returnValue
      },
      group: WARNING_GROUPS.carrier,
      carriers: basicsNonSmokersCarriers
    }, 
    {
      label: 'Wind / Hail value can not be less or equal to All Perils value.',
      condition: (value, fullObj, data: AdditionalDataHomeBasicsCoverages) => {
        if (fullObj.coverageCode === 'WHDED') {
          return validateWindHailStandard(value, fullObj, data);
        }
        else {
        return false;
        }
      },
      group: WARNING_GROUPS.general,
      carriers: []
    },
    {
      label: 'Valid values for Wind/Hail are 1%, 2% and 5%',
      condition: (value, fullObj, data: AdditionalDataHomeBasicsCoverages) => {
        if (fullObj.coverageCode === 'WHDED') {
          return Validate.isRequiredForSelectedPlans(parsedBasicsCoverageTravelers, data.quoteSelectedPlansIds)
                 && windHailFieldIsInvalidTravelers(value, fullObj, data);
        }else {
  
        return false;}
      },
      group: WARNING_GROUPS.carrier,
      carriers: parsedBasicsCoverageTravelers
    }
    // {
    //   label: (value, fullObj) => {
    //     return generateLabel(fullObj);
    //   },
    //   condition: (value, fullObj, data: AdditionalDataHomeBasicsCoverages) => {
    //     return (fullObj.coverageCode !== 'RCDWELL') && requiredField(fullObj.value) && checkQuoteFormType(data.quote, fullObj.coverageCode);
    //   },
    //   group: WARNING_GROUPS.general,
    //   carriers: []
    // }
  ]
};

export const WARNINGS_DEFINITIONS_HOME_BASICS: WarningDefinitionI[] = [
  basicsCoverages
];
