export interface AttachmentData {
	typeCode: string;
	typeDescription: string;
	data: string;
	additionalDetails: string;
}

export interface RequiredDocument {
	typeCode: string;
	typeDescription: string;
}

export interface LineItem {
	amount: number;
	description: string;
	type: string;
}

export interface RmvFee {
	total: number;
	lineItems: LineItem[];
  agencyBillTotal: number;
}

export interface ResponseDetail {
	attachmentData: AttachmentData[];
	requiredDocuments: RequiredDocument[];
	rmvFeeRequired: boolean;
	rmvFees: RmvFee;
}

export interface Message {
	message: string;
}

export interface ResultStatus {
	status: string;
	message: Message[];
}

export interface EvrValidate {
	transactionType: string;
	atlasTransactionKey: string;
	atlasValidatedTransactionKey: string;
	atlasRegistrationKey: string;
	responseDetail: ResponseDetail;
	resultStatus: ResultStatus;
}
