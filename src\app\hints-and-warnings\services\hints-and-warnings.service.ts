
import {filter} from 'rxjs/operators';
import { ActivatedRoute, NavigationEnd, NavigationExtras, Router } from '@angular/router';
import { WARNING_GROUPS, WarningDataI, WarningDefinitionI, WarningGroup, WarningItem } from 'app/hints-and-warnings/model/warnings';

import { BehaviorSubject ,  SubscriptionLike as ISubscription ,  Observable } from 'rxjs';
import { Injectable } from '@angular/core';

const CSS_HAS_WARN_FOCUS = 'has-warning-focus';

export const LOCATION_FRAGMENT_SEPARATOR = '__interact=';
export const CURRENT_URL_VARIABLE = '{{current_url}}';

export interface IHintsAndWarningsLocationFragmentElements {
  elementIdToFocus: string;
  elementIdToInteract: string;
}

@Injectable()
export class HintsAndWarningsService {
  private routeFragment = undefined;
  private routeCurrentUrl:string = undefined;
  private _routeCurrentUrl:BehaviorSubject<string> = new BehaviorSubject(this.routeCurrentUrl);

  private allWarningsGroups:WarningGroup[] = [];
  private allWarnings:WarningItem[] = [];
  private _allWarnings:BehaviorSubject<WarningItem[]> = new BehaviorSubject(this.allWarnings);

  private panelState:boolean | undefined = undefined;
  private _panelState:BehaviorSubject<boolean|undefined> = new BehaviorSubject(this.panelState);

  private panelStateIsOpen:boolean | undefined = undefined;
  private _panelStateIsOpen:BehaviorSubject<boolean|undefined> = new BehaviorSubject(this.panelStateIsOpen);

  constructor(
    private router:Router,
    private activatedRoute:ActivatedRoute
  ) {

    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd))
      .subscribe((val: NavigationEnd) => {
        this.routeCurrentUrl = val.url.split('#')[0];
        this._routeCurrentUrl.next(this.routeCurrentUrl);
      })

    this.activatedRoute.fragment.subscribe(fragment => {
      this.routeFragment = fragment;
    });
  }

  public get getRouteCurrentUrl$():Observable<string> {
    return this._routeCurrentUrl.asObservable();
  }


  // Warnings Data
  //----------------------------------------------------------------------------
  public get allWarnings$():Observable<WarningItem[]> {
    return this._allWarnings.asObservable();
  }


  public createWarningGroupIfNotExists(uniqueGroupId:string):void {
    let groupExists = this.allWarningsGroups.find(warningGroup => warningGroup.id == uniqueGroupId);

    if (!groupExists) {
      this.allWarningsGroups.push(new WarningGroup(uniqueGroupId));
    }
  }

  public clearWarningsGroup(uniqueGroupId:string):void {
    let groupExists = this.allWarningsGroups.find(warningGroup => warningGroup.id == uniqueGroupId);

    if (groupExists) {
      groupExists.warnings = [];
      this.concatWarningsGroupsToAllWarnings();
      this._allWarnings.next(this.allWarnings);
    }
  }


  public clearAllWarnings():void {
    this.allWarningsGroups = [];
    this.allWarnings = [];
    this._allWarnings.next(this.allWarnings);
  }


  //----------
  private chooseCorrectWayToCheckDataAndUpdateAdditionalData(warningsDefinition:WarningDefinitionI[], dataToCheck:Object|Object[],  warningsGroupUqId:string, additionalData:any = {}): void {
    const clonedAdditionalData = JSON.parse(JSON.stringify(additionalData));
    const tmpAdditionalData = (!additionalData) ? Object.assign({}, {observedData: dataToCheck}) : Object.assign(clonedAdditionalData, {observedData: dataToCheck});;

    // If Array check every item
    if (Array.isArray(dataToCheck)) {
      dataToCheck.forEach(singleData => this.checkObjectDataForHintsAndWarningsByDeepId(warningsDefinition, singleData, warningsGroupUqId, tmpAdditionalData));
    } else {
       this.checkObjectDataForHintsAndWarningsByDeepId(warningsDefinition, dataToCheck, warningsGroupUqId, tmpAdditionalData);
    }
  }
  //---------


  /**
   * @param {WarningDefinitionI[]} warningsDefinition - The array of warning definitions
   * @param {Observable<Object|Object[]>} dataToWatch - Observable object or array of objects
   * @param {string} warningsGroupUqId - The string used to assign WarningItem to specific group eg. Name of the objectToCheck
   *
   * @return {ISubscription} - Subscription of the observable object
   */
  public watchObjectDataForHintsAndWarnings(warningsDefinition:WarningDefinitionI[], dataToWatch:Observable<Object|Object[]>, warningsGroupUqId:string, additionalData: any = {}):ISubscription {
    let timer;
    let delay:number = 200;

    this.createWarningGroupIfNotExists(warningsGroupUqId);

    return dataToWatch.subscribe(data => {
      timer && clearTimeout(timer);
      timer = setTimeout(() => {
        this.clearWarningsGroup(warningsGroupUqId);
        this.chooseCorrectWayToCheckDataAndUpdateAdditionalData(warningsDefinition, data, warningsGroupUqId, additionalData);
      });
    });
  }


 /**
   * @param {WarningDefinitionI[]} warningsDefinition - The array of warning definitions
   * @param {Observable<Object|Object[]>} dataToWatch - Observable object or array of objects
   * @param {string} warningsGroupUqId - The string used to assign WarningItem to specific group eg. Name of the objectToCheck
   *
   * @return {ISubscription} - Subscription of the observable object
   */
  public watchObjectDataForHintsAndWarningsAUTOBAddress(warningsDefinition:WarningDefinitionI[], dataToWatch:Observable<Object|Object[]>, warningsGroupUqId:string, additionalData: any = {}):ISubscription {
    let timer;
    let delay:number = 200;

    this.createWarningGroupIfNotExists(warningsGroupUqId);

    return dataToWatch.subscribe(data => {
      timer && clearTimeout(timer);
      timer = setTimeout(() => {
        this.clearWarningsGroup(warningsGroupUqId);
        this.chooseCorrectWayToCheckDataAndUpdateAdditionalData(warningsDefinition, data[0], warningsGroupUqId, additionalData);
      });
    });
  }


  /**
   * @param {WarningDefinitionI[]} warningsDefinition - The array of warning definitions
   * @param {Observable<Object|Object[]>} dataToWatch - Observable object or array of objects
   * @param {string} warningsGroupUqId - The string used to assign WarningItem to specific group eg. Name of the objectToCheck
   * @param {Observable<any>} - Additional data as Observable
   *
   * @return {ISubscription[]} - Array of Subscription of the observable object and Additional Data
   */
  public watchObjectDataForHintsAndWarningsWithAdditionalData(warningsDefinition:WarningDefinitionI[], dataToWatch: Observable<Object|Object[]>, warningsGroupUqId:string, additionalData?:Observable<any>):ISubscription[] {
    let localDataToWatch;
    let localAdditionalData;
    let timer;
    let delay:number = 200;

    this.createWarningGroupIfNotExists(warningsGroupUqId);

    let dataSubscription:ISubscription = dataToWatch.subscribe(data => {
      localDataToWatch = data;

      timer && clearTimeout(timer);
      timer = setTimeout(() => {
        this.clearWarningsGroup(warningsGroupUqId);
        this.chooseCorrectWayToCheckDataAndUpdateAdditionalData(warningsDefinition, localDataToWatch, warningsGroupUqId, localAdditionalData);
      }, delay);
    });

    let additionalSubscription: ISubscription = additionalData.subscribe(data => {
      localAdditionalData = data;

      if (localDataToWatch) {
        timer && clearTimeout(timer);
        timer = setTimeout(() => {
          this.clearWarningsGroup(warningsGroupUqId);
          this.chooseCorrectWayToCheckDataAndUpdateAdditionalData(warningsDefinition, localDataToWatch, warningsGroupUqId, localAdditionalData);
        }, delay);
      }
    });

    return [dataSubscription, additionalSubscription];
  }


  /**
   * @param {WarningDefinitionI[]} warningsDefinition - The array of warning definitions
   * @param {Object} objectToCheck - The object that will be checked
   * @param {string} uniqueGroupId - The string used to assign WarningItem to specific group eg. Name of the objectToCheck
   */
  public checkObjectDataForHintsAndWarningsByDeepId(warningsDefinition:WarningDefinitionI[], objectToCheck:any, uniqueGroupId:string, additionalData:any = {}):void {
    warningsDefinition.forEach((val) => {
      let tmpArrPath = val.deepId.split('.');
      let fieldExists = false;
      let fieldValue = tmpArrPath.reduce((prev, curr, index) => {

        // make sure if this is the last property in the path
        if (index === tmpArrPath.length - 1 && typeof prev === 'object' && prev && curr in prev) {
          fieldExists = true;
        }

        return prev && prev[curr];
      }, objectToCheck );

      if (fieldExists) {
        this.manageWarningsAndHints(val, fieldValue, objectToCheck, additionalData, uniqueGroupId);
      } else {
        console.warn('There is no such field:', val.deepId);
      }
    });

    // Concat all Groups Item Warnings and assign them to allWarnings
    this.concatWarningsGroupsToAllWarnings();
    this._allWarnings.next(this.allWarnings);
  }


  private manageWarningsAndHints(warningDefinition:WarningDefinitionI, fieldValue:any, fullObjectToCheck:any, additionalData:any = {}, uniqueGroupId:string):void {
    let tmpViewFieldUrl = '';
    let fieldUrl = '';
    let fieldUrlQueryParams = null;
    let tmpViewFieldId = '';
    let tmpViewFieldInteractId = '';

    // View URI
    if(typeof warningDefinition.viewUri === 'function') {
      tmpViewFieldUrl = warningDefinition.viewUri(fullObjectToCheck, additionalData);
    } else {
      tmpViewFieldUrl = warningDefinition.viewUri;
    }

    // View Field ID
    if(typeof warningDefinition.viewFieldId === 'function') {
      tmpViewFieldId = warningDefinition.viewFieldId(fullObjectToCheck);
    } else {
      tmpViewFieldId = warningDefinition.viewFieldId;
    }

    // View Field Interact ID (for click event)
    if ('viewFieldInteractId' in warningDefinition && warningDefinition.viewFieldInteractId) {
      if (typeof warningDefinition.viewFieldInteractId === 'function') {
        tmpViewFieldInteractId = warningDefinition.viewFieldInteractId(fullObjectToCheck);
      } else {
        tmpViewFieldInteractId = warningDefinition.viewFieldInteractId
      }
    }

    fieldUrl = tmpViewFieldUrl.split('?')[0];
    fieldUrlQueryParams = this.router.parseUrl(tmpViewFieldUrl).queryParams;

    // Process Warnings for the field
    if (warningDefinition.warnings && warningDefinition.warnings.length) {
      warningDefinition.warnings.forEach((warning:WarningDataI, index:number) => {
        let addWarning = null;
        let warningLabel = '';
        let warningCarriers = [];

        // Condition
        if(typeof warning.condition === 'function') {
          addWarning = warning.condition(fieldValue, fullObjectToCheck, additionalData);
        } else {
          addWarning = warning.condition;
        }

        // Label
        if(typeof warning.label === 'function') {
          warningLabel = warning.label(fieldValue, fullObjectToCheck, additionalData);
        } else {
          warningLabel = warning.label;
        }

        // Carriers
        if(typeof warning.carriers === 'function') {
          warningCarriers = warning.carriers(fullObjectToCheck, additionalData);
        } else {
          warningCarriers = warning.carriers || [];
        }

        // Generate WarningItem
        let warningItem = new WarningItem();
        warningItem.showWarning = addWarning;
        warningItem.label = warningLabel;
        warningItem.viewFieldId = tmpViewFieldId;
        warningItem.viewUri = fieldUrl;
        warningItem.queryParams = fieldUrlQueryParams;
        warningItem.carrier = warningCarriers;
        warningItem.group = warning.group || WARNING_GROUPS.general;
        warningItem.uqId = this.generateWarningId(tmpViewFieldId, tmpViewFieldUrl, index); // this.generateWarningId(warningDefinition, tmpViewFieldUrl, index);
        warningItem.uqGroupId = uniqueGroupId;
        warningItem.viewFieldInteractId = (tmpViewFieldInteractId) ? tmpViewFieldInteractId : null;

        this.manageFocusWarningClass(warningItem);
        this.addOrUpdateWarningOfWarningUqGroup(warningItem, uniqueGroupId);
      });
    }
  }


  private addOrUpdateWarningOfWarningUqGroup(warningItem:WarningItem, uniqueGroupId:string):void {
    let warningsGroup = this.allWarningsGroups.find((group:WarningGroup) => group.id === uniqueGroupId);

    if (!warningsGroup) {
      this.createWarningGroupIfNotExists(uniqueGroupId);
      warningsGroup = this.allWarningsGroups.find((group:WarningGroup) => group.id === uniqueGroupId);
    }

    let existingItemIndex = -1;
    let existingItem = warningsGroup.warnings.find((item:WarningItem, index:number) => {
      existingItemIndex = index;
      return item.uqId === warningItem.uqId;
    });

    if (existingItem) {
      warningsGroup.warnings.splice(existingItemIndex, 1, warningItem);
    } else {
      warningsGroup.warnings.push(warningItem);
    }
  }

  private concatWarningsGroupsToAllWarnings():void {
    this.allWarnings = [];
    this.allWarningsGroups.forEach((group:WarningGroup) => {
      this.allWarnings = this.allWarnings.concat(group.warnings);
    });
  }



  // private generateWarningId(warningDefinition:WarningDefinitionI, viewFieldUri:string, index:number):string {
  //   let uqId = warningDefinition.viewFieldId + '_' + index + '_' + window.btoa(viewFieldUri);//.substr(0,70);
  //   return uqId;
  // }

  private generateWarningId(viewFieldId:string, viewFieldUri:string, index:number):string {
    let uqId = viewFieldId + '_' + index + '_' + window.btoa(viewFieldUri);// .substr(0,70);
    return uqId;
  }


  // Panel State
  // ----------------------------------------------------------------------------
  public get panelState$():Observable<any> {
    return this._panelState.asObservable();
  }

  public notifyWarningPanelShouldBeOpen(notify:boolean|undefined = undefined):void {
    notify !== undefined && this._panelState.next(notify);
  }

  public get panelStateIsOpen$(): Observable<boolean> {
    return this._panelStateIsOpen.asObservable();
  }

  public setPanelStateIsOpen(isOpen: boolean) {
    this.panelStateIsOpen = isOpen;
    this._panelStateIsOpen.next(this.panelStateIsOpen);
  }


  // Navigation
  // ----------------------------------------------------------------------------
  public generateCorrectLocationFragment(warning: WarningItem): string {
    let fragmentData: string = (warning.viewFieldInteractId)
      ? warning.viewFieldId + LOCATION_FRAGMENT_SEPARATOR + warning.viewFieldInteractId
      : warning.viewFieldId;

    return fragmentData;
  }

  public goTo(warning:WarningItem):void {
    let fragmentData: string = this.generateCorrectLocationFragment(warning);

    let params: NavigationExtras = {
      fragment: fragmentData,
      queryParams: warning.queryParams
    };

    // this.router.navigate([warning.viewUri], params);
    this.router.navigate([this.getCorrectWarningItemUri(warning)], params);
  }

  public getCorrectWarningItemUri(warningItem:WarningItem):string {
    let url = '';

    if (warningItem && warningItem.viewUri) {
      url = warningItem.viewUri.replace(CURRENT_URL_VARIABLE, this.routeCurrentUrl);
    }

    return url;
  }

  public getLocationFragmentElements(routerFragment: string): IHintsAndWarningsLocationFragmentElements {
    let elementToFocus = '';
    let elementToInteract = '';

    if (routerFragment) {
      const fragmentData = routerFragment.split(LOCATION_FRAGMENT_SEPARATOR);
      elementToFocus = (fragmentData[0]) ? fragmentData[0] : '';
      elementToInteract = (fragmentData[1]) ? fragmentData[1] : '';
    }

    let result: IHintsAndWarningsLocationFragmentElements = {
      elementIdToFocus: elementToFocus,
      elementIdToInteract: elementToInteract
    }

    return result;
  }

  // Finding DOM element on page
  // ----------------------------------------------------------------------------
  private tryFindWarnHTMLElementTimeout;
  public handleFindFocusAndInteractWithWarnElement(warnElementID:string, containerToScrollID:string, elementToInteractID:string = undefined,  attemptsCount:number = 30):void {
    let warnElement = document.getElementById(warnElementID);
    let containerToScroll = document.getElementById(containerToScrollID);

    this.focusWarnElement(warnElement);


    if (warnElement && containerToScroll) {
      let scrollPos = this.helpGetOffsetTop(warnElement, containerToScroll);
      window.setTimeout(() => {
        containerToScroll.scrollTop = scrollPos - 10;
      });

      // Try to interact with specified element with small delay
      if (elementToInteractID) {
        const interactElement: HTMLElement = document.getElementById(elementToInteractID);
        if (interactElement) {
          window.setTimeout(() => { interactElement.click() }, 100);
        }
      }
    }

    this.tryFindWarnHTMLElementTimeout && clearTimeout(this.tryFindWarnHTMLElementTimeout);

    if (!warnElement && attemptsCount > 0) {
      this.tryFindWarnHTMLElementTimeout = setTimeout(() => {
        this.handleFindFocusAndInteractWithWarnElement(warnElementID, containerToScrollID, elementToInteractID, --attemptsCount);
      }, 300);
    }
  }

  public focusWarnElement (warnElement: HTMLElement): void {
    this.removeWarnFocusFromAllElements();

    if (warnElement) {
      warnElement.focus();
      warnElement.classList.add(CSS_HAS_WARN_FOCUS);
    }
  }

  public removeWarnFocusFromAllElements(): void {
    Array.from(document.querySelectorAll('.' + CSS_HAS_WARN_FOCUS))
      .forEach( el => el.classList.remove(CSS_HAS_WARN_FOCUS));
  }

  // TODO:: update interact element
  private manageFocusWarningClass(warningItem: WarningItem): void {
    if (this.routeFragment) {
      const fragmentData: IHintsAndWarningsLocationFragmentElements = this.getLocationFragmentElements(this.routeFragment);
      const focusFieldIdFragment = fragmentData.elementIdToFocus;
      const interactFieldIdFragment = fragmentData.elementIdToInteract;

      if (warningItem.viewFieldId === focusFieldIdFragment && this.routeCurrentUrl === this.getCorrectWarningItemUri(warningItem)) {
        let element: HTMLElement = document.getElementById(focusFieldIdFragment);

        if (element) {
          (warningItem.showWarning === true) ? element.classList.add(CSS_HAS_WARN_FOCUS) : element.classList.remove(CSS_HAS_WARN_FOCUS) ;
        }
      }
    }
  }

  // private manageFocusWarningClass(warningItem: WarningItem): void {
  //   if (this.routeFragment && warningItem.viewFieldId === this.routeFragment && this.routeCurrentUrl === this.getCorrectWarningItemUri(warningItem)) {
  //     let element: HTMLElement = document.getElementById(this.routeFragment);

  //     if (element) {
  //       (warningItem.showWarning === true) ? element.classList.add(CSS_HAS_WARN_FOCUS) : element.classList.remove(CSS_HAS_WARN_FOCUS) ;
  //     }
  //   }
  // }


  // Helpers
  // ----------------------------------------------------------------------------
  private helpGetOffsetTop(element, toParent?): number {
    let offsetTop = 0;

    do {
      if (toParent && element === toParent) {
        break;
      }

      if (!isNaN(element.offsetTop)) {
        offsetTop += element.offsetTop;
      }
    } while (element = element.offsetParent);

    return offsetTop;
  }

}
