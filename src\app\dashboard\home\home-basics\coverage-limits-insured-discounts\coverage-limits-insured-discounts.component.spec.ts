import { async, ComponentFixture, TestBed, fakeAsync, inject, tick } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';

import { TextMaskModule } from 'angular2-text-mask/dist/angular2TextMask';

import { StubAutocompleteComponent } from 'testing/stubs/components/autocomplete.component';
import { StubDatepickerInputComponent } from 'testing/stubs/components/datepicker-input.component';
import { StubModalboxComponent } from 'testing/stubs/components/modalbox.component';
import { StubTooltipComponent } from 'testing/stubs/components/tooltip.component';
import { StubDetectSystemDirective } from 'testing/stubs/directives/detect-system.directive';
import { StubClientsServiceProvider, StubClientsService } from 'testing/stubs/services/clients.service.provider';
import { StubCoveragesServiceProvider, StubCoveragesService } from 'testing/stubs/services/coverages.service.provider';
import { StubDwellingServiceProvider } from 'testing/stubs/services/dwelling.service.provider';
import {
    StubOverlayLoaderServiceProvider
} from 'testing/stubs/services/overlay-loader.service.provider';

import { StorageService } from 'app/shared/services/storage-new.service';

import {
    CoverageLimitsInsuredDiscountsComponent
} from './coverage-limits-insured-discounts.component';
import { Helpers } from 'app/utils/helpers';
import { HOME_QUOTE } from 'testing/data/quotes/quote-home';
import { HOME_STANDARD_COVERAGES } from 'testing/data/quotes/coverages/home-standard';
import { DWELLINGS } from 'testing/data/quotes/dwellings';
import { CLIENTS } from 'testing/data/quotes/clients';
import { simulateTextInput, getNativeEl, getDebugEl, changeTextInputValue, expectNativeEl } from 'testing/helpers/all';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { Observable } from 'rxjs';
import { CoveragesService } from 'app/dashboard/app-services/coverages.service';
import { HOME_POLICY_COVERAGES_STANDARD } from 'testing/data/specs/rating-coverages/HOME/policy-coverages-standard';
import { StubMoneyServiceProvider } from 'testing/stubs/services/money.service.provider';
import { ClientsService } from 'app/dashboard/app-services/clients.service';
import { By } from '@angular/platform-browser';
import { CoveragesData } from 'app/app-model/coverage';
import { ApiResponse } from 'app/app-model/_common';

describe('Component: CoverageLimitsInsuredDiscounts', () => {
  let component: CoverageLimitsInsuredDiscountsComponent;
  let fixture: ComponentFixture<CoverageLimitsInsuredDiscountsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [FormsModule, TextMaskModule],
      declarations: [
        CoverageLimitsInsuredDiscountsComponent,
        StubAutocompleteComponent,
        StubTooltipComponent,
        StubDatepickerInputComponent,
        StubModalboxComponent,
        StubDetectSystemDirective
      ],
      providers: [
        StorageService,
        StubDwellingServiceProvider,
        StubCoveragesServiceProvider,
        StubOverlayLoaderServiceProvider,
        StubClientsServiceProvider,
        StubMoneyServiceProvider
      ]
    })
      .compileComponents();

    jasmine.clock().mockDate(new Date(2018, 1, 28));
  }));

  describe('when all data is available in storage', () => {
    let apiCommonService: ApiCommonService;

    beforeEach(fakeAsync(inject([StorageService, ApiCommonService],
      (storageService: StorageService, _apiCommonService: ApiCommonService) => {
        storageService.setStorageData('selectedQuote', Helpers.deepClone(HOME_QUOTE));
        storageService.setStorageData('homeQuoteCoverages', Helpers.deepClone(HOME_STANDARD_COVERAGES));
        storageService.setStorageData('dwelling', Helpers.deepClone(DWELLINGS.items[0]));
        storageService.setStorageData('homeStandardCoveragesList', Helpers.deepClone(HOME_POLICY_COVERAGES_STANDARD.items));
        storageService.setStorageData('clients', CLIENTS.items);

        apiCommonService = _apiCommonService;
        spyOn(apiCommonService, 'putByUri').and.callFake((uri, data) => {
          return Observable.of(data);
        });

        fixture = TestBed.createComponent(CoverageLimitsInsuredDiscountsComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
        tick();
    })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });

    it('should update coverage limits when dwelling coverage changes', fakeAsync(() => {
      const inputDE = getDebugEl(fixture, '#field_DWELL');
      const input = inputDE.nativeElement;

      inputDE.triggerEventHandler('focus', {
        srcElement: input,
        target: input,
        type: 'focus'
      });
      fixture.detectChanges();
      tick();

      changeTextInputValue(input, '250000', fixture);

      inputDE.triggerEventHandler('blur', {
        srcElement: input,
        target: input,
        type: 'blur'
      });
      fixture.detectChanges();
      tick(300);

      expect(apiCommonService.putByUri).toHaveBeenCalledWith(
        '/quotes/ea56e24a-5834-48d9-b449-ae312dc1c49f/coveragesStandard/fb9272de-bc89-4ca4-b0cb-551f402b6868',
        jasmine.objectContaining({
          coverages: jasmine.arrayContaining([
            jasmine.objectContaining({
              coverageCode: 'DWELL',
              values: [
                jasmine.objectContaining({
                  value: '250000'
                })
              ]
            })
          ])
        }));
    }));

    it('should update coverage limits when personal liability coverage changes', fakeAsync(() => {
      const autocomplete: StubAutocompleteComponent = getDebugEl(fixture, '#field_PL').componentInstance;

      autocomplete.activeOption = component.coveragePersonalLiabilityOptions[1];
      fixture.detectChanges();
      tick(300);

      expect(apiCommonService.putByUri).toHaveBeenCalledWith(
        '/quotes/ea56e24a-5834-48d9-b449-ae312dc1c49f/coveragesStandard/fb9272de-bc89-4ca4-b0cb-551f402b6868',
        jasmine.objectContaining({
          coverages: jasmine.arrayContaining([
            jasmine.objectContaining({
              coverageCode: 'PL',
              values: [
                jasmine.objectContaining({
                  value: '200000'
                })
              ]
            })
          ])
        }));
    }));

    it('should update client data if insurer date of birth changes', fakeAsync(inject([ClientsService],
      (clientsService: StubClientsService) => {
        spyOn(clientsService, 'updateClient').and.callThrough();

        const datepicker: StubDatepickerInputComponent = fixture.debugElement.query(
          By.directive(StubDatepickerInputComponent)).componentInstance;

        datepicker.onDateChange.emit({
          date: new Date(1973, 8, 15)
        });
        fixture.detectChanges();
        tick();

        expect(clientsService.updateClient).toHaveBeenCalled();
      })));

    it('should calculate age if insurer date of birth changes', fakeAsync(() => {
      const datepicker: StubDatepickerInputComponent = fixture.debugElement.query(
        By.directive(StubDatepickerInputComponent)).componentInstance;

      datepicker.onDateChange.emit({
        date: new Date(1973, 8, 15)
      });
      fixture.detectChanges();
      tick();

      const input = getNativeEl(fixture, '#insurerAge');
      expect(input.value).toEqual('44');
    }));
  });

  describe('when only basic data is available in storage', () => {
    beforeEach(fakeAsync(inject([StorageService, CoveragesService],
      (storageService: StorageService, coveragesService: StubCoveragesService) => {
        storageService.setStorageData('selectedQuote', Helpers.deepClone(HOME_QUOTE));
        storageService.setStorageData('dwelling', Helpers.deepClone(DWELLINGS.items[0]));
        storageService.setStorageData('homeStandardCoveragesList', Helpers.deepClone(HOME_POLICY_COVERAGES_STANDARD.items));
        storageService.setStorageData('clients', CLIENTS.items);

        coveragesService.homeQuoteCoverages = Helpers.deepClone(HOME_STANDARD_COVERAGES);

        fixture = TestBed.createComponent(CoverageLimitsInsuredDiscountsComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
        tick();
      })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('when empty homeQuoteCoverages is available in storage', () => {
    beforeEach(fakeAsync(inject([StorageService, CoveragesService],
      (storageService: StorageService, coveragesService: StubCoveragesService) => {
        storageService.setStorageData('selectedQuote', Helpers.deepClone(HOME_QUOTE));
        storageService.setStorageData('homeQuoteCoverages', new ApiResponse<CoveragesData>());
        storageService.setStorageData('dwelling', Helpers.deepClone(DWELLINGS.items[0]));
        storageService.setStorageData('homeStandardCoveragesList', Helpers.deepClone(HOME_POLICY_COVERAGES_STANDARD.items));
        storageService.setStorageData('clients', CLIENTS.items);

        coveragesService.homeQuoteCoverages = Helpers.deepClone(HOME_STANDARD_COVERAGES);

        fixture = TestBed.createComponent(CoverageLimitsInsuredDiscountsComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
        tick();
      })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });
  });
});
