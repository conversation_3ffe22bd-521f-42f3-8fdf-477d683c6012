<section class="section section--compact u-bg-dark-blue">

  <div class="row">
    <div class="col-xs-12">
      <nav class="tabs">
        <ul class="tabs__list">
          <!-- li class="tabs__item">
            <a class="tabs__link" routerLink="/dashboard/summary" routerLinkActive="is-active">Summary</a>
          </li -->

          <li class="tabs__item">
            <a class="tabs__link" routerLink="{{uriDrivers}}" [class.is-active]="activeTab === 'driver'">Drivers</a>
          </li>
          <li class="tabs__item">
            <a class="tabs__link" routerLink="{{uriVehicle}}" [class.is-active]="activeTab === 'vehicle'">Vehicles</a>
          </li>
          <li class="tabs__item">
            <a *ngIf="vehiclesExist" class="tabs__link" routerLink="{{quoteUri}}/coverages" [class.is-active]="activeTab === 'coverage'">Coverages</a>
            <span *ngIf="!vehiclesExist" class="tabs__link tabs__link--disabled">Coverages</span>
          </li>
          <li class="tabs__item">
            <a class="tabs__link" routerLink="{{quoteUri}}/options" [class.is-active]="activeTab === 'options'">Options</a>
          </li>
          <li class="tabs__item">
            <a class="tabs__link" routerLink="{{quoteUri}}/forms" [class.is-active]="activeTab === 'forms'">Forms</a>
          </li>
          <li class="tabs__item">
            <a class="tabs__link" routerLink="{{quoteUri}}/premiums" [class.is-active]="activeTab === 'premiums'">Premiums</a>
          </li>
        </ul>
        <!--
        <div class="tabs__right">
        </div>
        -->
      </nav><!-- / .tabs -->

    </div>
  </div>
</section>
