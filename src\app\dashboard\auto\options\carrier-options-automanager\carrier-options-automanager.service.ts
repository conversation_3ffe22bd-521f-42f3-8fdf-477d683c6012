import { take } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { BehaviorSubject, SubscriptionLike as ISubscription } from 'rxjs';

// Services
import { OptionsService } from 'app/dashboard/app-services/options.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { SubsService } from 'app/dashboard/app-services/subs.service';
import { AgencyUserService } from 'app/shared/services/agency-user.service';

// Models
import {
  Quote,
  QuotePlan,
  QuotePolicyHistory,
  QuotePlanListAPIResponse
} from 'app/app-model/quote';
import { Vehicle, VehicleCoverages } from 'app/app-model/vehicle';
import { CoverageItemValue } from '../../../../app-model/coverage';
import {
  Coverage,
  CoverageItem,
  CoverageItemParsed,
  PolicyItemDefaultDataAPIResponse,
  PolicyCoveragesData,
  ICoverageItemParsedAdditionalData
} from 'app/app-model/coverage';

interface TriggerSourceI {
  triggerBy?: 'PolicyHistory' | 'SelectedPlans' | 'QuoteEffectiveDate';
}

interface PromiseDataAfterSettingDefaultValues {
  status: string;
  policyItemsParsed: CoverageItemParsed[];
}

class CoverageItemParsedAdditionalData
  implements ICoverageItemParsedAdditionalData {
  public agentCodeGroupPlanAddEmptyOption = false;
}

@Injectable()
export class CarrierOptionsAutomanagerService {
  private serviceIsInitialized = false;

  private subscriptionQuote: ISubscription;
  private subscriptionQuoteIsNew: ISubscription;
  private subscriptionSelectedPlans: ISubscription;
  private subscriptionVehicles: ISubscription;
  private subscriptionPolicyHistory: ISubscription;
  private subscriptionOptionsDefault: ISubscription;
  private subscriptionParserTrigger: ISubscription;
  private subscriptionAutoPolicyOptions: ISubscription;

  private _parserTrigger: BehaviorSubject<TriggerSourceI> = new BehaviorSubject(
    {}
  );

  private quote: Quote;
  private quoteId = '';
  private quoteIsNew = false;
  private selectedPlans: QuotePlan[] = [];
  private selectedPlansIds: string[] = [];
  private vehicles: Vehicle[] = [];
  private policyHistory: QuotePolicyHistory;
  private quoteBeforeChange: Quote = null;

  private preventParsingPoliciesUntilDataSavedToAPIServer = false;

  constructor(
    private storageService: StorageService,
    private agencyUserService: AgencyUserService,
    private specsService: SpecsService,
    private optionsService: OptionsService,
    private subsService: SubsService
  ) { }

  initialize(): Promise<void> {
    if (this.serviceIsInitialized) {
      return Promise.resolve();
    }

    this.serviceIsInitialized = true;
    console.log('Initialize Auto Carrier options');

    return Promise.all([
      this.subscribeQuote(),
      this.subscribeQuoteIsNew(),
      this.subscribeSelectedPlans(),
      this.subscribeVehicles(),
      this.subscribePolicyHistory(),
      this.subscribeOptionDefaults()
    ])
      .then(res => {
        return this.initPolicyItemsParser();
      })
      .then(() => {
        return this.subscribeAutoPolicyOptionsForErrorAndAvailabilityStatus();
      })
      .catch(err => {
        console.log(err);
      });
  }

  destroy() {
    this.serviceIsInitialized = false;

    this.subscriptionQuote && this.subscriptionQuote.unsubscribe();
    this.subscriptionQuoteIsNew && this.subscriptionQuoteIsNew.unsubscribe();
    this.subscriptionSelectedPlans &&
      this.subscriptionSelectedPlans.unsubscribe();
    this.subscriptionVehicles && this.subscriptionVehicles.unsubscribe();
    this.subscriptionPolicyHistory &&
      this.subscriptionPolicyHistory.unsubscribe();
    this.subscriptionOptionsDefault &&
      this.subscriptionOptionsDefault.unsubscribe();

    this.subscriptionParserTrigger &&
      this.subscriptionParserTrigger.unsubscribe();
    this.subscriptionAutoPolicyOptions &&
      this.subscriptionAutoPolicyOptions.unsubscribe();
  }

  private subscribeQuote(): Promise<Quote> {
    return new Promise((resolve, reject) => {
      this.subscriptionQuote = this.storageService
        .getStorageData('selectedQuote')
        .subscribe((quote: Quote) => {
          this.quote = JSON.parse(JSON.stringify(quote));
          this.quoteId = quote.resourceId;
          resolve(this.quote);

          if (!this.quoteBeforeChange) {
            this.quoteBeforeChange = JSON.parse(JSON.stringify(quote));
          } else if (
            this.quoteBeforeChange.effectiveDate !== this.quote.effectiveDate
          ) {
            this.quoteBeforeChange = JSON.parse(JSON.stringify(quote));

            // INIT POLICY ITEMS PARSING After Quote Effective date change
            // Emit Observable to Parse Items
            this._parserTrigger.next({ triggerBy: 'QuoteEffectiveDate' });
          }
        });
    });
  }

  private subscribeQuoteIsNew(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.subscriptionQuoteIsNew = this.storageService
        .getStorageData('isNewQuote')
        .subscribe((res: boolean) => {
          this.quoteIsNew = res;
          resolve(this.quoteIsNew);
        });
    });
  }

  private subscribeVehicles(): Promise<Vehicle[]> {
    return new Promise((resolve, reject) => {
      this.subscriptionVehicles = this.storageService
        .getStorageData('vehiclesList')
        .subscribe((vehicles: Vehicle[]) => {
          this.vehicles = JSON.parse(JSON.stringify(vehicles));
          resolve(this.vehicles);
        });
    });
  }

  private subscribePolicyHistory(): Promise<QuotePolicyHistory> {
    return new Promise((resolve, reject) => {
      this.subscriptionPolicyHistory = this.storageService
        .getStorageData('policyHistory')
        .subscribe((res: QuotePolicyHistory) => {
          this.policyHistory = JSON.parse(JSON.stringify(res));
          resolve(this.policyHistory);

          // Emmit Observable to Parse Items
          this._parserTrigger.next({ triggerBy: 'PolicyHistory' });
        });
    });
  }
  private subscribeOptionDefaults(): Promise<PolicyItemDefaultDataAPIResponse> {

    let agencyId;
    this.agencyUserService.userData$
      .pipe(take(1))
      .subscribe(agent => (agencyId = agent.agencyId));

    return new Promise((resolve, reject) => {
      this.subscriptionOptionsDefault = this.subsService
        .getDefaultAutoCarrierOptions(agencyId)
        .pipe(take(1))
        .subscribe(
          (res: PolicyItemDefaultDataAPIResponse) => {

            this.storageService.setStorageData('autoDefaultOptions', res);

            resolve(res);
          });
    });
  }

  private subscribeSelectedPlans(): Promise<QuotePlan[]> {
    return new Promise((resolve, reject) => {
      this.subscriptionSelectedPlans = this.storageService
        .getStorageData('selectedPlan')
        .subscribe((res: QuotePlanListAPIResponse) => {
          if (res && res.items && res.items.length) {
            this.selectedPlans = JSON.parse(JSON.stringify(res.items[0].items));
            this.selectedPlansIds = this.selectedPlans.map(
              plan => plan.ratingPlanId
            );
            resolve(this.selectedPlans);

            // INIT POLICY ITEMS PARSING
            // Emmit Observable to Parse Items
            this._parserTrigger.next({ triggerBy: 'SelectedPlans' });
          }
        });
    });
  }

  private initPolicyItemsParser(): Promise<void> {
    const delay = 500;
    let timer;

    return new Promise((resolve, reject) => {
      this.subscriptionParserTrigger = this._parserTrigger
        .asObservable()
        .subscribe(res => {
          timer && clearTimeout(timer);

          // Prevent geting Policies and parse data until data is saved to API with default values - if necessary
          if (this.preventParsingPoliciesUntilDataSavedToAPIServer) {
            return;
          }

          timer = setTimeout(() => {
            // console.log('%c TIME TO PARSE POLICY ITEMS', 'background: blue; color:#fff', res);
            this.getOptionsPoliciesListAndParseData()
              .then(() => resolve())
              .catch(err => reject(err));
          }, delay);
        });
    });
  }

  public getOptionsPoliciesListAndParseData(viewCategory = ''): Promise<void> {
    const selectedPlansIdsString = this.selectedPlansIds.join(',');
    const states = this.quote.state;
    const lobs = this.quote.lob;
    const quoteEffectiveDate =
      this.quote && this.quote.effectiveDate ? this.quote.effectiveDate : '';

    let agencyId;
    this.agencyUserService.userData$
      .pipe(take(1))
      .subscribe(agent => (agencyId = agent.agencyId));

    // this.preventParsingPoliciesUntilDataSavedToAPIServer = true;

    return new Promise((resolve, reject) => {
      this.specsService
        .getRatingCoverages(
          states,
          lobs,
          'policy',
          selectedPlansIdsString,
          '',
          quoteEffectiveDate,
          viewCategory
        )
        .pipe(take(1))
        .subscribe(
          res => {
            let policiesItems: CoverageItem[] = [];
            if (res && res.items && res.items.length) {
              policiesItems = [...res.items];
            }

            if (policiesItems && this.quoteId) {
              let policyItemsParsed: CoverageItemParsed[] = [];
              this.processPoliciesItemsParsing(
                policiesItems,
                this.selectedPlans,
                this.quoteId,
                this.policyHistory,
                this.vehicles,
                this.quoteIsNew
              )
                .then((res: CoverageItemParsed[]) => {
                  return this.updateCoverageResourcesIfNewItems(res);
                })
                .then((res: CoverageItemParsed[]) => {
                  return (policyItemsParsed = JSON.parse(JSON.stringify(res)));
                })
                .then((res: CoverageItemParsed[]) => {
                  this.preventParsingPoliciesUntilDataSavedToAPIServer = true;
                  return this.setDefaultValuesForOptionsIfThisIsNewQuoteAndSave(
                    res,
                    agencyId,
                    this.quoteIsNew
                  );
                })
                .then((res: PromiseDataAfterSettingDefaultValues) => {
                  // console.log('Setting Policies Default Values Status:: ', res.status);
                  this.preventParsingPoliciesUntilDataSavedToAPIServer = false;
                  policyItemsParsed = res.policyItemsParsed;

                  this.storageService.setStorageData(
                    'autoPolicyOptions',
                    policyItemsParsed
                  );
                })
                .then(() => resolve())
                .catch(err => {
                  this.preventParsingPoliciesUntilDataSavedToAPIServer = false;
                  reject(err);
                });
            }
          },
          err => {
            // this.preventParsingPoliciesUntilDataSavedToAPIServer = false;
            reject(err);
          }
        );
    });
  }

  // UPDATE
  public processPoliciesItemsParsing(
    policies: CoverageItem[],
    selectedPlans: QuotePlan[],
    quoteId: string,
    policyHistory: QuotePolicyHistory,
    vehicles: Vehicle[],
    isNewQuote: boolean
  ): Promise<CoverageItemParsed[]> {
    let arrPoliciesItemsParsed = this.optionsService.parsePoliciesItemsToPoliciesItemsParsed(
      policies
    );
    arrPoliciesItemsParsed = this.optionsService.orderObjectsArrayByProperty(
      arrPoliciesItemsParsed,
      'description'
    );

    // Required for setting default values if new Quote - to check if options
    // has been alredy saved in storage or not
    let policiesItemsFromStorage: CoverageItemParsed[] = [];
    let autoStandardCoverages: VehicleCoverages[] = [];
    let additionalVehicleCoverages: VehicleCoverages[] = [];
    let additionalVehicleCoverageOptions: VehicleCoverages;

    this.storageService
      .getStorageData('autoPolicyOptions')
      .pipe(take(1))
      .subscribe(res => (policiesItemsFromStorage = res));

    this.storageService
    .getStorageData('autoCoveragesAdditionalForVehicles')
    .pipe(take(1))
    .subscribe(res => (additionalVehicleCoverages = res));

    this.storageService
      .getStorageData('autoCoveragesStandardForVehicles')
      .pipe(take(1))
      .subscribe((res: VehicleCoverages[]) => {
    autoStandardCoverages = res;

    const updatedCoverages: VehicleCoverages[] = additionalVehicleCoverages.map(
      (item: VehicleCoverages) => {
        const standardCoverages = autoStandardCoverages.find(
          (cov: VehicleCoverages) => cov.vehicleResourceId === item.vehicleResourceId
        );
        additionalVehicleCoverageOptions = item;
        return item;
      }
    );
  });

    return this.loadPolicyCoverages(quoteId).then(
      (data: PolicyCoveragesData) => {
        arrPoliciesItemsParsed = arrPoliciesItemsParsed.map(item => {
          item = this.setPolicyItemStatusBasedOnPolicyHistory(
            item,
            policyHistory
          );
          item = this.optionsService.setPolicyItemStatusBasedOnPolicyCoverages(
            item,
            data.coverages
          );
          item = this.setPolicyItemStatusBasedOnRequirements(
            arrPoliciesItemsParsed,
            item,
            data.coverages,
            vehicles,
            additionalVehicleCoverageOptions,
            policyHistory,
            isNewQuote
          );
          item = this.setPolicyItemAdditionalData(item);

          item.endpointUrl = data.endpointURL;
          item = this.optionsService.setPolicyItemParsedIsNewFromAPIValue(
            item,
            policiesItemsFromStorage
          );

          if (item.inputType !== 'Dropdown') {
            item.values = this.optionsService.orderObjectsArrayByProperty(
              item.values,
              'value'
            );
          }
         if (item.inputType === 'Checkbox' && item.isActive && item.currentValue === '' || item.currentValue === null) {item.currentValue = 'true'; }
          return item;
        });

        // If any new items are there update the coverage resources
        // if(arrPoliciesItemsParsed.some(x=> x.isNewFromAPI) && !arrPoliciesItemsParsed.every(x=> x.isNewFromAPI))
        //   {
        //         let allPoliciesToUpdate = arrPoliciesItemsParsed.filter(item => item.isActive);
        //         let readyAllPoliciesToUpdate: Coverage[] = this.optionsService.convertPolicyItemParsedArrayToPolicyArray(allPoliciesToUpdate);
        //         // Update remote data
        //         let newCoverageData = {
        //           coverages: readyAllPoliciesToUpdate
        //         };

        //         this.optionsService.updatePoliciesByUri(data.endpointURL, newCoverageData)
        //         .take(1)
        //         .subscribe(
        //           res => console.log('Set and saved Policies update to reflect new options'),
        //           err => console.log('Error occurred During updating, not saved values: ', err)
        //         );
        //   }

        // console.log('%c >>>> ', 'background:green;color:#fff', arrPoliciesItemsParsed);
        return arrPoliciesItemsParsed;
      }
    );
  }

  private updateCoverageResourcesIfNewItems(
    arrPoliciesItemsParsed: CoverageItemParsed[]
  ) {
    return new Promise((resolve, reject) => {
      // If any new items are there update the coverage resources
      if (
        arrPoliciesItemsParsed.some(x => x.isNewFromAPI) &&
        !arrPoliciesItemsParsed.every(x => x.isNewFromAPI)
      ) {

        let agencyId ;
        this.agencyUserService.userData$
          .pipe(take(1))
          .subscribe(agent => (agencyId = agent.agencyId));

          this.subsService.getDefaultAutoCarrierOptions(agencyId)
          .pipe(take(1))
          .toPromise()
          .then((res) => {
            this.storageService.setStorageData('autoDefaultOptions', res);
             const policiesParsedWithDefaultValues = this.optionsService.setDefaultValuesForOptions(
              arrPoliciesItemsParsed,
              res.items
            );

            // const policiesToUpdate = policiesParsedWithDefaultValues.filter(
            //   policyParsed =>
            //     res.items.some(
            //       item =>
            //        item.coverageCode === policyParsed.coverageCode &&
            //        policyParsed.isNewFromAPI
            //     )
            // );

            const allPoliciesToUpdate = policiesParsedWithDefaultValues.filter(
              item => item.isActive
            );
            const readyAllPoliciesToUpdate: Coverage[] = this.optionsService.convertPolicyItemParsedArrayToPolicyArray(
              allPoliciesToUpdate
            );
            // Update remote data
            const newCoverageData = {
              coverages: readyAllPoliciesToUpdate
            };

          this.optionsService
          .updatePoliciesByUri(
          arrPoliciesItemsParsed[0].endpointUrl,
          newCoverageData
          )
          .pipe(take(1))
          .subscribe(
            resp => {
              console.log(
                'Set and saved Policies update to reflect new options'
              );
              this.storageService.setStorageData('autoPolicyOptions', policiesParsedWithDefaultValues);
              resolve(policiesParsedWithDefaultValues);
            },
            err => {
              console.log(
                'Error occurred During updating, not saved values: ',
                err
              );
              reject(err);
            }
          );
          });

      } else {
        resolve(arrPoliciesItemsParsed);
      }
    });
  }

  private loadPolicyCoverages(quoteId: string): Promise<PolicyCoveragesData> {
    const policyCoverageData: PolicyCoveragesData = new PolicyCoveragesData();

    return new Promise((resolve, reject) => {
      if (quoteId) {
        this.optionsService
          .getPolicies(quoteId)
          .pipe(take(1))
          .subscribe(res => {
            if (res.items.length === 0) {
              this.optionsService
                .createCoverageByUri(res.meta.href)
                .pipe(take(1))
                .subscribe(res => {
                  policyCoverageData.coverages = res.coverages;
                  policyCoverageData.endpointURL = res.meta.href;
                  resolve(policyCoverageData);
                });
            } else {
              policyCoverageData.coverages = res.items[0].coverages;

              if (res.items[0].meta && res.items[0].meta.href) {
                policyCoverageData.endpointURL = res.items[0].meta.href;
              } else if (res.items[0]) {
                policyCoverageData.endpointURL =
                  res.meta.href + '/' + res.items[0].resourceId;
              } else {
                policyCoverageData.endpointURL =
                  'api_does_not_returned_data_to_create_uri';
              }

              resolve(policyCoverageData);
            }
          });
      } else {
        reject('Error Load Policy Coverages, quoteId Not defined');
      }
    });
  }

  private setDefaultValuesForOptionsIfThisIsNewQuoteAndSave(
    policiesParsed: CoverageItemParsed[],
    agencyId: string,
    isNewQuote: boolean
  ): Promise<PromiseDataAfterSettingDefaultValues> {
    // console.log('IS New Quote ::', isNewQuote);
    if (!isNewQuote) {
      return Promise.resolve({
        status: 'Not a new Quote, not set default values',
        policyItemsParsed: policiesParsed
      });
    }

    return new Promise(resolve => {
      this.subsService
        .getDefaultAutoCarrierOptions(agencyId)
        .pipe(take(1))
        .subscribe(
          (res: PolicyItemDefaultDataAPIResponse) => {

            this.storageService.setStorageData('autoDefaultOptions', res);
            const policiesParsedWithDefaultValues = this.optionsService.setDefaultValuesForOptions(
              policiesParsed,
              res.items
            );
            const endpointUrl = policiesParsed[0].endpointUrl;
            const policiesToUpdate = policiesParsedWithDefaultValues.filter(
              policyParsed =>
                res.items.some(
                  item =>
                    item.coverageCode === policyParsed.coverageCode &&
                    policyParsed.isNewFromAPI
                )
            );
            const allPoliciesToUpdate = policiesParsedWithDefaultValues.filter(
              item => item.isActive
            );
            const readyAllPoliciesToUpdate: Coverage[] = this.optionsService.convertPolicyItemParsedArrayToPolicyArray(
              allPoliciesToUpdate
            );

            // Do not send request to update if no new options with default values
            if (!policiesToUpdate.length) {
              resolve({
                status: 'No new options to Update default values',
                policyItemsParsed: policiesParsedWithDefaultValues
              });
            }

            // Update remote data
            const newCoverageData = {
              coverages: readyAllPoliciesToUpdate
            };

            this.optionsService
              .updatePoliciesByUri(endpointUrl, newCoverageData)
              .pipe(take(1))
              .subscribe(
                res => {
                  resolve({
                    status:
                      'Set and saved Policies default values for new options',
                    policyItemsParsed: policiesParsedWithDefaultValues
                  });
                },
                err => {
                  resolve({
                    status:
                      'Error occurred During updating, not set default values',
                    policyItemsParsed: policiesParsed
                  });
                }
              );
          },
          err => {
            resolve({
              status:
                'Error occurred during getting default values, not set default values',
              policyItemsParsed: policiesParsed
            });
          }
        );
    });
  }

  public setPolicyItemStatusBasedOnPolicyHistory(
    policy: CoverageItemParsed,
    policyHistory: QuotePolicyHistory
  ): CoverageItemParsed {
    switch (policy.coverageCode) {
      // Auto Fields - Plymouth Rock [RTR]
      // :: Reason for no active auto policy
      case 'BSC-AUTO-002134':
        policy.isRequired =
          policyHistory['priorOrRenewingCarrier'] === 'No Prior Coverage'
            ? true
            : false;
        if (policyHistory['priorOrRenewingCarrier'] !== 'No Prior Coverage') {
          policy.isDisabled = true;
          policy.isActive = false;
        }
        break;


      // Auto Fields - MetLife [RTR]
      // :: No Prior Insurance
      case 'BSC-AUTO-001965':
        policy.isActive =
          policyHistory['priorOrRenewingCarrier'] === 'No Prior Coverage'
            ? true
            : false;
        if (
          policyHistory['priorPolicyExpirationDate'] !== null &&
          policyHistory['priorPolicyExpirationDate'] !== 'Unknown'
        ) {
          policy.isDisabled = true;
          policy.isActive = false;
        } else {
          policy.isDisabled = false;
        }
        break;

      // Auto Fields - Safeco [RTR]
      // :: No Prior Insurance
      case 'BSC-AUTO-001988':
        policy.isActive =
          policyHistory['priorOrRenewingCarrier'] === 'No Prior Coverage'
            ? true
            : false;
        break;

      // Auto Fields - Travelers [RTR]
      // :: Continuous Insurance

      // Required if Prior or Renewing Carrier !== 'No Prior Coverage'
      // Not available if Prior or Renewing Carrier === 'No Prior Coverage'
      case 'BSC-AUTO-002195':
        if (
          policy.isActive &&
          policyHistory['priorOrRenewingCarrier'] !== 'No Prior Coverage'
        ) {
          policy.isDisabled = false;
          policy.isRequired = false;
        } else if (
          policyHistory['priorOrRenewingCarrier'] !==
          'No Prior Coverage' /*&& isActiveContinuousInsurance*/
        ) {
          policy.isDisabled = false;
          policy.isRequired = true;
        } else {
          policy.isDisabled = true;
          policy.isRequired = false;
        }
        break;

        case 'BSC-AUTO-002407':
          if (
            policy.isActive &&
            policyHistory['priorOrRenewingCarrier'] === 'No Prior Coverage'
          ) {
            policy.isDisabled = false;
            policy.isRequired = false;
          } else if (
            policyHistory['priorOrRenewingCarrier'] === 'No Prior Coverage'
          ) {
            policy.isDisabled = false;
            policy.isRequired = true;
          } else {
            policy.isDisabled = true;
            policy.isRequired = false;
          }
       break;

      // Auto Fields - Travelers [RTR]
      // :: No Prior Insurance

      // Required if Prior or Renewing Carrier === 'No Prior Coverage'
      // Not available if Prior or Renewing Carrier !== 'No Prior Coverage'
      case 'BSC-AUTO-002153':
        if (
          policy.isActive &&
          policyHistory['priorOrRenewingCarrier'] === 'No Prior Coverage'
        ) {
          policy.isDisabled = false;
          policy.isRequired = false;
        } else if (
          policyHistory['priorOrRenewingCarrier'] === 'No Prior Coverage'
        ) {
          policy.isDisabled = false;
          policy.isRequired = true;
        } else {
          policy.isDisabled = true;
          policy.isRequired = false;
        }
        break;

      // Auto Fields - Progressive [RTR]
      // :: Reason for rewriting Progressive policy
      case 'BSC-AUTO-002290':
        if (policyHistory.priorOrRenewingCarrier === 'Progressive') {
          policy.isRequired = true;
          policy.isDisabled = false;
        } else {
          policy.isRequired = false;
          policy.isDisabled = true;
          policy.currentValue = '';
        }
        break;
    }

    return policy;
  }

  public modifyPolicyItemPropertiesByCode(
    policies: CoverageItemParsed[],
    policyCode: string,
    propValues: { [key: string]: any }
  ): CoverageItemParsed[] {
    const policyItem: CoverageItem = policies.find(
      item => item.coverageCode === policyCode
    );

    if (policyItem) {
      for (const property in propValues) {
        if (property in policyItem) {
          policyItem[property] = propValues[property];
        }
      }
    }

    return policies;
  }

  private setPolicyItemAdditionalData(
    item: CoverageItemParsed
  ): CoverageItemParsed {
    item.additionalData = Object.assign(
      new CoverageItemParsedAdditionalData(),
      item.additionalData
    );

    switch (item.coverageCode) {
      // https://bostonsoftware.atlassian.net/browse/SPR-2801
      case 'BSC-AUTO-001750':
      case 'BSC-AUTO-002359':
        item.additionalData.agentCodeGroupPlanAddEmptyOption = true;
        break;
      // https://bostonsoftware.atlassian.net/browse/SPR-2802
      case 'BSC-AUTO-001786':
        item.additionalData.agentCodeGroupPlanAddEmptyOption = true;
        break;
      case 'BSC-AUTO-002281':
        item.additionalData.agentCodeGroupPlanAddEmptyOption = true;
        break;
    }

    return item;
  }

  // Requirements
  // ----------------------------------------------------------------------------
  public setPolicyItemStatusBasedOnRequirements(
    policies: CoverageItemParsed[],
    policy: CoverageItemParsed,
    policyCoverages: Coverage[],
    vehicles: Vehicle[],
    additionalVehicleCoverages: VehicleCoverages,
    policyHistory: QuotePolicyHistory,
    isNewQuote: boolean
  ): CoverageItemParsed {
    const anyVehicleIsMotorcycle = vehicles.some(
      vehicle => vehicle.vehicleType === 'Motorcycle'
    );
    const glassDeductibleCoverage = additionalVehicleCoverages?.options?.find(
      coverage => coverage.coverageCode === 'BSC-AUTO-000038'
    );

    const nationwideCompDeductible = additionalVehicleCoverages?.options?.find(
      coverage => coverage.coverageCode === 'BSC-AUTO-002417'
    );

    if (glassDeductibleCoverage?.isActive === false) {
      if (nationwideCompDeductible) {
        nationwideCompDeductible.isActive = false;
        nationwideCompDeductible.isDisabled = true;
      }
    } else if (nationwideCompDeductible) {
      nationwideCompDeductible.isDisabled = false;
    }

    switch (policy.coverageCode) {

        // PRODUCT-3125
        case 'BSC-AUTO-002322':
         const lapsed = policyHistory['lapsedDaysLast12Months'] !== 'Unknown' ? +policyHistory['lapsedDaysLast12Months'] : 0;
          policy.isRequired = policyHistory['priorOrRenewingCarrier'] === 'Plymouth Rock' && lapsed < 31
           ? true : false;
           if (policyHistory['priorOrRenewingCarrier'] === 'Plymouth Rock' && lapsed < 31) {
            policy.isDisabled = false;
           } else {
              policy.isDisabled = true;
              policy.isActive = false;
              policy.currentValue = '';
              policy.currentValueData.valuesCount = 0;
              policy.currentValueData.keyValue = [];
              policy.currentValueData.values = [];
           }
          break;
          // case 'BSC-AUTO-002329':
          //  this.storageService.getStorageData('autoCoveragesStandardForVehicles').subscribe( res => {
          //   const optbi = res[0].options.find(x => x.coverageCode === 'OPTBI');
          //   const{currentValue, values} = optbi;
          //   const minIndex = values.findIndex((x: CoverageItemValue) => x.value === '250/500');
          //   const valueIndex = values.findIndex((x: CoverageItemValue) => x.value === currentValue);
          //   policy.isDisabled = minIndex <= valueIndex ? false : true;
          //   if(policy.isDisabled) { policy.currentValue = 'false'; policy.isActive = false; }
          //  });
          //  break;
      // Auto Fields - General
      // :: Force Multi-car Discount
      // case 'BSC-AUTO-000015':
      //   // policy.alreadySelected;
      //   if (isNewQuote && policy.alreadySelected !== true) {
      //     policy.isActive = true;
      //     policy.alreadySelected = true;
      //   }
      // break;

      // Auto Fields - Arbella [RTR]
      // :: Arbella - Multi Vehicle Credit
      case 'BSC-AUTO-001627':
        if (vehicles.length > 1) {
          policy.isActive = true;
        }
        break;

      // Auto Fields - Arbella [RTR]
      // :: Arbella - Multi-Car Family Discount
      // case 'BSC-AUTO-001917':
      //   if (vehicles.length > 1) {
      //     policy.isActive = true;
      //   }
      // break;

      // Auto Fields - Safety [RTR]
      // :: Agent Code and Plan
      case 'BSC-AUTO-001768':
        // Required
        // policy.isActive == true ? policy.isRequired = false : policy.isRequired = true;
        policy.isRequired = true;
        break;

      // Auto Fields - NGM [RTR]
      // :: Agent Code
      case 'BSC-AUTO-001816':
        // Required
        // policy.isActive == true ? policy.isRequired = false : policy.isRequired = true;
        policy.isRequired = true;
        break;

      // TODO:: it needs to be done in other place
      // Quincy - Corresponding Acct. Homeowners w/ Umb
      // case 'BSC-AUTO-002245':
      // if (policy.isActive) {
      //   this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-002244', {
      //     isDisabled: true,
      //     isActive: false
      //   });
      // } else {
      //   this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-002244', {
      //     isDisabled: false
      //   });
      // }
      // break;

      // Auto Fields - Quincy Mutual [RTR]
      // :: Quincy - Corresponding Account
      case 'BSC-AUTO-001741':
        if (policy.isActive) {
          this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-002040', {
            isDisabled: true,
            isActive: false
          });
        } else {
          this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-002040', {
            isDisabled: false
          });
        }
        break;

      // Auto Fields - Quincy Mutual [RTR]
      // :: Quincy - Household Member/Family Account Credit
      case 'BSC-AUTO-002040':
        if (policy.isActive) {
          this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-001741', {
            isDisabled: true,
            isActive: false
          });
        } else {
          this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-001741', {
            isDisabled: false
          });
        }
        break;

        // PRODUCT-2512
        case 'BSC-AUTO-001950':
          if (policy.isActive === true) {
            this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-002315', {
              isDisabled: true,
              isActive: false
            });
          } else {
            this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-002315', {
              isDisabled: false
            });
          }
          break;

          case 'BSC-AUTO-002315':
          if (policy.isActive === true) {
            this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-001950', {
              isDisabled: true,
              isActive: false
            });
          } else {
            this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-001950', {
              isDisabled: false
            });
          }
          break;

      // Auto Fields - Plymouth Rock [RTR]
      // :: Group Affinity Discount
      case 'BSC-AUTO-00212':
        // Cannot be combined with the New England Patriots Ins Program.
        if (policy.isActive === true) {
          this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-002219', {
            isDisabled: true,
            isActive: false
          });
        } else {
          this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-002219', {
            isDisabled: false
          });
        }
        break;

      // Auto Fields - Plymouth Rock [RTR]
      // :: New England Patriots Ins Program
      case 'BSC-AUTO-002219':
        // Cannot be combined with a Group Affinity Discount
        if (policy.isActive === true) {
          this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-002128', {
            isDisabled: true,
            isActive: false
          });
        } else {
          this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-002128', {
            isDisabled: false
          });
        }
        break;

      // Auto Fields - Plymouth Rock [RTR]
      // :: Non pay cancel prior 24 months
      case 'BSC-AUTO-001897':
        // Cannot be combined with the Paid in Full Discount
        if (policy.isActive === true) {
          this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-001896', {
            isDisabled: true,
            isActive: false
          });
        } else {
          this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-001896', {
            isDisabled: false
          });
        }
        break;

      // Auto Fields - Plymouth Rock [RTR]
      // :: Paid in Full Discount
      case 'BSC-AUTO-001896':
        // Cannot be combined with the Non pay cancel prior 24 mos  carrier option
        if (policy.isActive === true) {
          this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-001897', {
            isDisabled: true,
            isActive: false
          });
        } else {
          this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-001897', {
            isDisabled: false
          });
        }
        break;

      // Auto Fields - Plymouth Rock [RTR]
      // :: Reason for no active auto policy
      case 'BSC-AUTO-002134':
        // Required if Prior or Renewing Carrier = 'No Prior Coverage'
        if (
          policyHistory &&
          policyHistory['priorOrRenewingCarrier'] === 'No Prior Coverage'
        ) {
          policy.isRequired = true;
        } else {
          policy.isRequired = false;
          policy.isActive = false;
          policy.isDisabled = true;
        }

        // if (policy.isActive == true) {
        //   policy.isRequired = false;
        // }
        break;

      // Auto Fields - Plymouth Rock [RTR]
      // :: Agent Code
      case 'BSC-AUTO-001733':
        // Required
        // policy.isActive == true ? policy.isRequired = false : policy.isRequired = true;
        // let tmpIsMaipArc = localStorage.getItem('maipArcQuote');
        const tmpIsMaipArc = sessionStorage.getItem('maipArcQuote');
        if (tmpIsMaipArc) {
          const isMaipArc = JSON.parse(tmpIsMaipArc);
          if (isMaipArc.isMaipArcQuote) {
            policy.isRequired = false;
          } else {
            policy.isRequired = true;
          }
        } else {
          policy.isRequired = true;
        }
        break;

      // Auto Fields - MetLife [RTR]
      // :: Advised consumer reports may be ordered
      case 'BSC-AUTO-001964':
        // Required
        // policy.isActive == true ? policy.isRequired = false : policy.isRequired = true;
        policy.isRequired = true;
        this.selectedPlansIds.includes('315') ? policy.ratingPlanId = '315' : policy.ratingPlanId = '24';
        break;

      // Auto Fields - MetLife [RTR]
      // :: Home Info - Insurer
      case 'BSC-AUTO-001962':
        // Required
        // policy.isActive == true ? policy.isRequired = false : policy.isRequired = true;
        policy.isRequired = true;
        this.selectedPlansIds.includes('315') ? policy.ratingPlanId = '315' : policy.ratingPlanId = '24';
        break;

      // Auto Fields - MetLife [RTR]
      // :: Home Info - Own/Rent
      case 'BSC-AUTO-001961':
        // Required
        // policy.isActive == true ? policy.isRequired = false : policy.isRequired = true;
        policy.isRequired = true;
        this.selectedPlansIds.includes('315') ? policy.ratingPlanId = '315' : policy.ratingPlanId = '24';


        break;

        case 'BSC-AUTO-002406':
          policy.isRequired = true;
          break;
        case 'BSC-AUTO-002047':
          policy.isRequired = true;

        case 'BSC-AUTO-002415':
          policy.isRequired = true;
          break;

        case 'BSC-AUTO-002411':
          policy.isRequired = true;
          break;

          case 'BSC-AUTO-002410':
            policy.isRequired = true;
            break;

      // Auto Fields - MetLife [RTR]
      // :: Home Info - Home Type
      case 'BSC-AUTO-001960':
        // Required
        // policy.isActive == true ? policy.isRequired = false : policy.isRequired = true;
        policy.isRequired = true;

        this.selectedPlansIds.includes('315') ? policy.ratingPlanId = '315' : policy.ratingPlanId = '24';

        break;

      // Auto Fields - MetLife [RTR]
      // :: No Prior Insurance
      case 'BSC-AUTO-001965':
        // If Prior or Renewing Carrier is 'No Prior Coverage' then this should be automatically selected.
        if (
          policyHistory &&
          policyHistory['priorOrRenewingCarrier'] === 'No Prior Coverage'
        ) {
          policy.isActive = true;
        } else {
          policy.isActive = false;
        }

        // Cannot be selected if Prior Policy Expiration Date is set.
        if (
          policyHistory &&
          policyHistory['priorPolicyExpirationDate'] !== null &&
          policyHistory['priorPolicyExpirationDate'] !== 'Unknown'
        ) {
          policy.isDisabled = true;
          policy.isActive = false;
        } else {
          policy.isDisabled = false;
        }
        break;

      // Auto Fields - Safeco [RTR]
      // :: No Prior Insurance
      case 'BSC-AUTO-001985':
        // If Prior or Renewing Carrier is 'No Prior Coverage' then this should be automatically selected.
        if (
          policyHistory &&
          policyHistory['priorOrRenewingCarrier'] === 'No Prior Coverage'
        ) {
          policy.isActive = true;
        } else {
          policy.isActive = false;
        }
        break;

      // Auto Fields - Safeco [RTR]
      // :: Prem owed & not sent to carrier (12 mos)
      case 'BSC-AUTO-001980':
        // Not applicable if Vehicle Type = 'Motorcycle'
        anyVehicleIsMotorcycle
          ? (policy.isDisabled = true)
          : (policy.isDisabled = false);
        break;

      // Auto Fields - Safeco [RTR]
      // Agent Code
      case 'BSC-AUTO-001986':
        // Required
        // policy.isActive == true ? policy.isRequired = false : policy.isRequired = true;
        policy.isRequired = true;
        break;

      // Auto Fields - Safeco [RTR]
      // Residence InsuranceType w/ any carrier
      case 'BSC-AUTO-001994':
        // Required
        // policy.isActive == true ? policy.isRequired = false : policy.isRequired = true;
        policy.isRequired = true;
        break;
// SUPPORT-9522
     // case 'BSC-AUTO-002359':
     //   policy.isRequired = true;
     //   break;

      case 'BSC-AUTO-002377':
        policy.isRequired = true;
        break;

      // Auto Fields - Commerce [RTR]
      // :: Continuous Period of Insurance
      case 'BSC-AUTO-002200':
        // Required
        // policy.isActive == true ? policy.isRequired = false : policy.isRequired = true;
        policy.isRequired = true;
        break;

      case 'BSC-AUTO-002390':
        policy.isRequired = true;
        break;



      // == NOT DONE
      // Auto Fields - Safeco [RTR]
      // :: Prior Policy Expiration Date
      case 'BSC-AUTO-001989':
        // When Prior or Renewing Carrier <> 'No Prior Coverage', this should be set to the date in the Policy History section.
        break;

      // Auto Fields - Safeco [RTR]
      // :: Motorcycle - All drivers perm res of HH
      case 'BSC-AUTO-002028':
        // Required if Vehicle Type = 'Motorcycle'
        anyVehicleIsMotorcycle
          ? (policy.isRequired = true)
          : (policy.isRequired = false);
        // this.anyVehicleIsMotorcycle ? policy.isDisabled = false : policy.isDisabled = true;
        // this.anyVehicleIsMotorcycle ? policy.isDisabled = false : policy.isActive = false;

        if (anyVehicleIsMotorcycle) {
          policy.isRequired = true;
          policy.isDisabled = false;
        } else {
          policy.isRequired = false;
          policy.isDisabled = true;
        }

        // policy.isActive == true ? policy.isRequired = false : policy.isRequired = true;

        // if (!anyVehicleIsMotorcycle && policy.isActive){
        //   policy.isActive = false;
        // }
        break;

      // Auto Fields - Safeco [RTR]
      // :: Motorcycle - Any active policies w/ Safeco
      case 'BSC-AUTO-002024':
        // Not applicable if Vehicle Type = 'Motorcycle'
        anyVehicleIsMotorcycle
          ? (policy.isDisabled = false)
          : (policy.isDisabled = true);

        if (!anyVehicleIsMotorcycle && policy.isActive) {
          policy.isActive = false;
        }
        break;

      // Auto Fields - Safeco [RTR]
      // :: Motorcycle - Insurance on Primary Residence
      case 'BSC-AUTO-002025':
        // Not applicable if Vehicle Type = 'Motorcycle'
        anyVehicleIsMotorcycle
          ? (policy.isDisabled = false)
          : (policy.isDisabled = true);

        if (!anyVehicleIsMotorcycle && policy.isActive) {
          policy.isActive = false;
        }
        break;

       // NGM Agent Code
       case 'BSC-AUTO-002420':
         policy.isRequired = true;
         break;

         // NGM Vehicle Registration
      case 'BSC-AUTO-002422':
        policy.isRequired = true;
        break;

      // Auto Fields - Travelers [RTR]
      // :: Travelers - ** Agent Code
      case 'BSC-AUTO-001764':
        // Required
        // policy.isActive == true ? policy.isRequired = false : policy.isRequired = true;
        policy.isRequired = true;
        break;

      // Auto Fields - Travelers [RTR]
      // :: Continuous Insurance
      case 'BSC-AUTO-002195':
        // Required if Prior or Renewing Carrier !== 'No Prior Coverage'
        // Not available if Prior or Renewing Carrier === 'No Prior Coverage'
        if (
          policyHistory &&
          policyHistory['priorOrRenewingCarrier'] !== 'No Prior Coverage'
        ) {
          policy.isDisabled = false;
          policy.isRequired = true;
        } else {
          policy.currentValue = '';
          policy.isActive = false;
          policy.isDisabled = true;   
          policy.isRequired = false;       
        }

        if (policy.isActive === true) {
          // policy.isRequired = false;
          policy.isDisabled = false;
        }
        break;

      // Auto Fields - Travelers [RTR]
      // :: EFT Discount
      // :: Commented because: SPRC-246
      // case 'BSC-AUTO-002018':

      //   // Cannot be combined with Paid in Full
      //   if (policy.isActive == true) {
      //     this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-001632', {
      //       isDisabled: true,
      //       isActive: false
      //     });
      //    } else {
      //     this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-001632', {
      //       isDisabled: false
      //     });
      //    }
      // break;

      // Auto Fields - Travelers [RTR]
      // :: No Prior Insurance
      case 'BSC-AUTO-002153':
        // Required if Prior or Renewing Carrier === 'No Prior Coverage'
        // Not available if Prior or Renewing Carrier !== 'No Prior Coverage'
        if (
          policyHistory &&
          policyHistory['priorOrRenewingCarrier'] === 'No Prior Coverage'
        ) {
          policy.isDisabled = false;
          policy.isRequired = true;
        } else {
          policy.currentValue = '';
          policy.isActive = false;
          policy.isDisabled = true;
          policy.isRequired = false;           
        }

        if (policy.isActive === true) {
          // policy.isRequired = false;
          policy.isDisabled = false;
        }
        break;

      // Auto Fields - Travelers [RTR]
      // :: Paid in Full
      // :: Commented because: SPRC-246
      // case 'BSC-AUTO-001632':

      //   // Cannot be combined with EFT Discount
      //   if (policy.isActive == true) {
      //     this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-002018', {
      //       isDisabled: true,
      //       isActive: false
      //     });
      //    } else {
      //      this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-002018', {
      //       isDisabled: false
      //     });
      //    }
      // break;

      // Auto Fields - Travelers [RTR]
      // :: Primary Residence Type
      case 'BSC-AUTO-002152':
        // Required
        // policy.isActive == true ? policy.isRequired = false : policy.isRequired = true;
        policy.isRequired = true;
        break;

      // == NOT DONE
      // Auto Fields - Progressive [RTR]
      // :: Previous Policy Expiration Date
      case 'BSC-AUTO-002050':
        // Cannot be > 31 days priot to the Effective Date
        // if (policy.isActive == true) {
        //   policy.isRequired = false;
        // }

        break;

      // Auto Fields - Progressive [RTR]
      // :: Num Non-Pay Cancels past 24 mos
      case 'BSC-AUTO-002048':
        // Required
        // policy.isActive == true ? policy.isRequired = false : policy.isRequired = true;
        policy.isRequired = true;
        break;

      // Auto Fields - Progressive [RTR]
      // :: Primary Residence Type
      case 'BSC-AUTO-002221':
        // Required
        // policy.isActive == true ? policy.isRequired = false : policy.isRequired = true;
        policy.isRequired = true;
        break;

      // Auto Fields - Progressive [RTR]
      // :: Continuous Insurance Requirement Met
      case 'BSC-AUTO-002049':
        // Required
        // policy.isActive == true ? policy.isRequired = false : policy.isRequired = true;
        policy.isRequired = true;
        // Previous Policy Expiration Date Required field if Continuous Insurance Requirement Met = 'Yes'
        if (policy.isActive === true) {
          let isYes = false;
          const isActive =
            policies['BSC-AUTO-002050']
              ? policies['BSC-AUTO-002050'].isActive
              : undefined; // this.getPolicyPropertyValue(this.policies, "BSC-AUTO-002050", "isActive");
          const policyValue =
            policyCoverages['BSC-AUTO-002049']
              ? policyCoverages['BSC-AUTO-002049'].values
              : undefined; // this.getPolicyPropertyValue(this.coverages, "BSC-AUTO-002049", "values");
          if (policyValue !== undefined) {
            isYes = policyValue[0].value.toLowerCase().indexOf('yes') > 0;
          }

          // BSC-AUTO-002050 Required if Yes Selected
          if (isYes && !isActive) {
            this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-002050', {
              isRequired: true
            });
          } else {
            this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-002050', {
              isRequired: false
            });
          }
        } else {
          this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-002050', {
            isRequired: false
          });
        }
        break;

      // Auto Fields - Progressive [RTR]
      // :: Length of Time at Current Address
      case 'BSC-AUTO-002046':
        // Required
        // policy.isActive == true ? policy.isRequired = false : policy.isRequired = true;
        policy.isRequired = true;
        break;

      // Auto Fields - Green Mountain [RTR]
      // :: EFT Option
      case 'BSC-AUTO-001860':
        // Paid in Full Discount Cannot be selected if EFT Option is selected.
        if (policy.isActive === true) {
          this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-001859', {
            isDisabled: true,
            isActive: false
          });
        } else {
          this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-001859', {
            isDisabled: false
          });
        }
        break;

      // Auto Fields - Green Mountain [RTR]
      // :: Paid in Full Discount - 1 Pay
      case 'BSC-AUTO-001859':
        // EFT Option Cannot be selected if Paid in Full Discount - 1 Pay is selected.
        if (policy.isActive === true) {
          this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-001860', {
            isDisabled: true,
            isActive: false
          });
        } else {
          this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-001860', {
            isDisabled: false
          });
        }
        break;

      // Auto Fields - Harleysville [RTR]
      // :: FCRA Notice Read and Agreed
      case 'BSC-AUTO-001827':
        // Required
        // policy.isActive == true ? policy.isRequired = false : policy.isRequired = true;
        policy.isRequired = true;
        break;

      // Auto Fields - Preferred New [RTR]
      // :: Agent Code
      case 'BSC-AUTO-001825':
        // Required
        // policy.isActive == true ? policy.isRequired = false : policy.isRequired = true;
        policy.isRequired = true;
        break;

      // https://bostonsoftware.atlassian.net/browse/SPR-2179
      // Prog - Any Active Home w/Prog [PROGRESSIVE]
      case 'BSC-AUTO-002238':
        policy.isRequired = true;
        break;

      // https://bostonsoftware.atlassian.net/browse/SPR-2179
      // Prog - Any Active MC, boat or RV w/Prog [PROGRESSIVE]
      case 'BSC-AUTO-002239':
        policy.isRequired = true;
        break;

      // https://bostonsoftware.atlassian.net/browse/SPR-2179
      // Prog - Any Active Umb or Comml pol w/Prog [PROGRESSIVE]
      case 'BSC-AUTO-002240':
        policy.isRequired = true;
        break;

      // https://bostonsoftware.atlassian.net/browse/SPR-2179
      // Prog - Primary Residence Type [PROGRESSIVE]
      case 'BSC-AUTO-002248':
        policy.isRequired = true;
        break;

      // https://bostonsoftware.atlassian.net/browse/SPRC-600
      // Hanover New - Reason for Rewriting the Hanover Insurance Policy?
      case 'BSC-AUTO-002301':
        // Required if Prior or Renewing Carrier = 'Hanover'
        if (
          policyHistory &&
          policyHistory['priorOrRenewingCarrier'] === 'Hanover'
        ) {
          policy.isRequired = true;
          policy.isDisabled = false;
        } else {
          policy.isRequired = false;
          policy.isDisabled = true;
          policy.currentValue = '';
        }

        break;

      // Cincinnati AUTOP mutual exclusive coverages 'Capstone Policy' and 'Personal Auto Plus'

      case 'BSC-AUTO-002254':
        if (policy.isActive === true) {
          this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-002253', {
            isDisabled: true,
            isActive: false
          });
        } else {
          this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-002253', {
            isDisabled: false
          });
        }
        break;

      case 'BSC-AUTO-002253':
        if (policy.isActive === true) {
          this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-002254', {
            isDisabled: true,
            isActive: false
          });
        } else {
          this.modifyPolicyItemPropertiesByCode(policies, 'BSC-AUTO-002254', {
            isDisabled: false
          });
        }
        break;

      // https://bostonsoftware.atlassian.net/browse/SPR-2801
      case 'BSC-AUTO-001750':
      case 'BSC-AUTO-002359':
        policy.isRequired = true;
        break;

      case 'BSC-AUTO-002281':
        policy.isRequired = true;
        break;

      // Travelers New - ** Agent Code
      case 'BSC-HOME-25040':
        // Required
        // policy.isActive == true ? policy.isRequired = false : policy.isRequired = true;
        policy.isRequired = true;
        break;

      // Commerce - Prior Carrier Tenure
      case 'BSC-AUTO-002391':
        if (
          policyHistory &&
          policyHistory['priorOrRenewingCarrier'] === 'No Prior Coverage'
        ) {
          policy.isRequired = false;
        } else {
          policy.isRequired = true;
        }
        break;

        case 'BSC-AUTO-002400':
          policy.isRequired = true;
          break;

        case 'BSC-AUTO-002191':
          policy.isRequired = true;
          break;

        case 'BSC-AUTO-002451':
          policy.isRequired = true;
          break;

        case 'BSC-AUTO-002452':
          policy.isRequired = true;
          break;
    }

    return policy;
  }

  // Error Status and Availability Settings - set specific values for specific Options
  // ---------------------------------------------------------------------------
  private subscribeAutoPolicyOptionsForErrorAndAvailabilityStatus(): Promise<
    void
  > {
    let allowUpdating = true; // To prevent infinity loop validation, when storage is updated by this method

    return new Promise(resolve => {
      this.subscriptionAutoPolicyOptions = this.storageService
        .getStorageData('autoPolicyOptions')
        .subscribe((items: CoverageItemParsed[]) => {
          if (allowUpdating) {
            const updatedItems: CoverageItemParsed[] = this.performErrorsStatusAndAvailabilitySettings(
              items
            );

            allowUpdating = false;
            this.storageService.setStorageData(
              'autoPolicyOptions',
              updatedItems
            );
            allowUpdating = true;
          }

          resolve();
        });
    });
  }

  private performErrorsStatusAndAvailabilitySettings(
    items: CoverageItemParsed[]
  ): CoverageItemParsed[] {
    items = items.map(item => {
      item = this.setPolicyItemErrorStatusAndAvailability(items, item);
      return item;
    });

    return items;
  }

  private setPolicyItemErrorStatusAndAvailability(
    allAutoPolicyOptions: CoverageItemParsed[],
    autoPolicyItemParsed: CoverageItemParsed
  ): CoverageItemParsed {
    switch (autoPolicyItemParsed.coverageCode) {
      // https://bostonsoftware.atlassian.net/browse/SPR-2873
      // Quincy - Corresponding Acct. Homeowners w/ Umb
      case 'BSC-AUTO-002245':
        // [BSC-AUTO-002244] Quincy - Corresponding Acct. with Homeowners
        const relatedOption002244 = allAutoPolicyOptions.find(
          i => i.coverageCode === 'BSC-AUTO-002244'
        );
        if (relatedOption002244) {
          if (relatedOption002244.isActive) {
            autoPolicyItemParsed.isDisabled = true;
          } else {
            autoPolicyItemParsed.isDisabled = false;
          }

          if (autoPolicyItemParsed.isActive && relatedOption002244.isActive) {
            autoPolicyItemParsed.isDisabled = false;
            autoPolicyItemParsed.additionalData.hasError = true;
            autoPolicyItemParsed.additionalData.errorMessage =
              'The option can not be selected together with option "Quincy - Corresponding Acct. with Homeowners"';
          } else {
            autoPolicyItemParsed.additionalData.hasError = false;
            autoPolicyItemParsed.additionalData.errorMessage = null;
          }
        }
        break;
      // https://bostonsoftware.atlassian.net/browse/SPR-2873
      // Quincy - Corresponding Acct. with Homeowners
      case 'BSC-AUTO-002244':
        // [BSC-AUTO-002245] Quincy - Corresponding Acct. Homeowners w/ Umb
        const relatedOption002245 = allAutoPolicyOptions.find(
          i => i.coverageCode === 'BSC-AUTO-002245'
        );
        if (relatedOption002245) {
          if (relatedOption002245.isActive) {
            autoPolicyItemParsed.isDisabled = true;
          } else {
            autoPolicyItemParsed.isDisabled = false;
          }

          if (autoPolicyItemParsed.isActive && relatedOption002245.isActive) {
            autoPolicyItemParsed.isDisabled = false;
            autoPolicyItemParsed.additionalData.hasError = true;
            autoPolicyItemParsed.additionalData.errorMessage =
              'The option can not be selected together with option "Quincy - Corresponding Acct. Homeowners w/ Umb"';
          } else {
            autoPolicyItemParsed.additionalData.hasError = false;
            autoPolicyItemParsed.additionalData.errorMessage = null;
          }
        }
        break;

        case 'BSC-AUTO-002319':
        // [BSC-AUTO-002244] Quincy - Corresponding Acct. with Homeowners
        const relatedOption002319 = allAutoPolicyOptions.find(
          i => i.coverageCode === 'BSC-AUTO-002320'
        );
        if (relatedOption002319) {
          if (relatedOption002319.isActive) {
            autoPolicyItemParsed.isDisabled = true;
          } else {
            autoPolicyItemParsed.isDisabled = false;
          }

          if (autoPolicyItemParsed.isActive && relatedOption002319.isActive) {
            autoPolicyItemParsed.isDisabled = false;
            autoPolicyItemParsed.additionalData.hasError = true;
            autoPolicyItemParsed.additionalData.errorMessage =
              'The option can not be selected together with option "Quincy - Corresponding Acct. with Homeowners"';
          } else {
            autoPolicyItemParsed.additionalData.hasError = false;
            autoPolicyItemParsed.additionalData.errorMessage = null;
          }
        }
        break;
      // https://bostonsoftware.atlassian.net/browse/SPR-2873
      // Quincy - Corresponding Acct. with Homeowners
      case 'BSC-AUTO-002320':
        // [BSC-AUTO-002245] Quincy - Corresponding Acct. Homeowners w/ Umb
        const relatedOption002320 = allAutoPolicyOptions.find(
          i => i.coverageCode === 'BSC-AUTO-002319'
        );
        if (relatedOption002320) {
          if (relatedOption002320.isActive) {
            autoPolicyItemParsed.isDisabled = true;
          } else {
            autoPolicyItemParsed.isDisabled = false;
          }

          if (autoPolicyItemParsed.isActive && relatedOption002320.isActive) {
            autoPolicyItemParsed.isDisabled = false;
            autoPolicyItemParsed.additionalData.hasError = true;
            autoPolicyItemParsed.additionalData.errorMessage =
              'The option can not be selected together with option "Quincy - Corresponding Acct. Homeowners w/ Umb"';
          } else {
            autoPolicyItemParsed.additionalData.hasError = false;
            autoPolicyItemParsed.additionalData.errorMessage = null;
          }
        }
        break;

      // https://bostonsoftware.atlassian.net/browse/SPR-2992
      // PROGRESSIVE: Account Credit
      case 'BSC-AUTO-002261':
        if (autoPolicyItemParsed.isActive) {
          let hasAnyOptionSelected;
          if (
            autoPolicyItemParsed.currentValueData &&
            autoPolicyItemParsed.currentValueData.keyValue &&
            autoPolicyItemParsed.currentValueData.keyValue.length
          ) {
            hasAnyOptionSelected = autoPolicyItemParsed.currentValueData.keyValue.some(
              (item: string[]) => item[1] && item[1] === 'Yes'
            );

            if (hasAnyOptionSelected) {
              autoPolicyItemParsed.additionalData.hasError = false;
              autoPolicyItemParsed.additionalData.errorMessage = '';
            } else {
              autoPolicyItemParsed.additionalData.hasError = true;
              autoPolicyItemParsed.additionalData.errorMessage =
                'The option needs to have selected at least one underling option.';
            }
          }
        } else {
          autoPolicyItemParsed.additionalData.hasError = false;
          autoPolicyItemParsed.additionalData.errorMessage = '';
        }
        break;

        case 'BSC-AUTO-002346':
          const relatedOption002345 = allAutoPolicyOptions.find(
            i => i.coverageCode === 'BSC-AUTO-002345'
          );
          if (relatedOption002345) {
            if (relatedOption002345.isActive) {
              autoPolicyItemParsed.isDisabled = true;
            } else {
              autoPolicyItemParsed.isDisabled = false;
            }

            if (autoPolicyItemParsed.isActive && relatedOption002345.isActive) {
              autoPolicyItemParsed.isDisabled = false;
              autoPolicyItemParsed.additionalData.hasError = true;
              autoPolicyItemParsed.additionalData.errorMessage =
                'The option can not be selected together with option "Quincy – Account Credit – Homeowners"';
            } else {
              autoPolicyItemParsed.additionalData.hasError = false;
              autoPolicyItemParsed.additionalData.errorMessage = null;
            }
        }
        break;

        case 'BSC-AUTO-002345':
          const relatedOption002346 = allAutoPolicyOptions.find(
            i => i.coverageCode === 'BSC-AUTO-002346'
          );
          if (relatedOption002346) {
            if (relatedOption002346.isActive) {
              autoPolicyItemParsed.isDisabled = true;
            } else {
              autoPolicyItemParsed.isDisabled = false;
            }

            if (autoPolicyItemParsed.isActive && relatedOption002346.isActive) {
              autoPolicyItemParsed.isDisabled = false;
              autoPolicyItemParsed.additionalData.hasError = true;
              autoPolicyItemParsed.additionalData.errorMessage =
                'The option can not be selected together with option "Quincy – Account Credit – Homeowners"';
            } else {
              autoPolicyItemParsed.additionalData.hasError = false;
              autoPolicyItemParsed.additionalData.errorMessage = null;
            }
        }
        break;

        case 'BSC-AUTO-002434':
          const relatedOption002433 = allAutoPolicyOptions.find(
            i => i.coverageCode === 'BSC-AUTO-002433'
          );
          if (relatedOption002433) {
            if (relatedOption002433.isActive) {
              autoPolicyItemParsed.isDisabled = true;
            } else {
              autoPolicyItemParsed.isDisabled = false;
            }

            if (autoPolicyItemParsed.isActive && relatedOption002433.isActive) {
              autoPolicyItemParsed.isDisabled = false;
              autoPolicyItemParsed.additionalData.hasError = true;
              autoPolicyItemParsed.additionalData.errorMessage =
                'The option can not be selected together with option "Quincy – Account Credit – Homeowners"';
            } else {
              autoPolicyItemParsed.additionalData.hasError = false;
              autoPolicyItemParsed.additionalData.errorMessage = null;
            }
        }
        break;

        case 'BSC-AUTO-002433':
          const relatedOption002434 = allAutoPolicyOptions.find(
            i => i.coverageCode === 'BSC-AUTO-002434'
          );
          if (relatedOption002434) {
            if (relatedOption002434.isActive) {
              autoPolicyItemParsed.isDisabled = true;
            } else {
              autoPolicyItemParsed.isDisabled = false;
            }

            if (autoPolicyItemParsed.isActive && relatedOption002434.isActive) {
              autoPolicyItemParsed.isDisabled = false;
              autoPolicyItemParsed.additionalData.hasError = true;
              autoPolicyItemParsed.additionalData.errorMessage =
                'The option can not be selected together with option "Quincy – Account Credit – Homeowners"';
            } else {
              autoPolicyItemParsed.additionalData.hasError = false;
              autoPolicyItemParsed.additionalData.errorMessage = null;
            }
        }
        break;


      }

    return autoPolicyItemParsed;
  }
}
