export interface DocumentDestructionDetail {
	lastDestroyedDate: string;
	destroyedThroughDate: string;
	eligibleToDestroyThroughDate: string;
}

export interface DocumentDestructionResponse {
	success: boolean;
	documentDestructionDetails: DocumentDestructionDetail;
	messages: string[];
}


export interface Meta {
	href: string;
	rel: string[];
}

export interface First {
	href: string;
	rel: string[];
}

export interface Next {
	href: string;
	rel: string[];
}

export interface Last {
	href: string;
	rel: string[];
}

export interface Meta {
	href: string;
}

export interface Item {
	meta: Meta;
	rmvServicesId: number;
	workflowType: string;
	status: string;
	userId: number;
	userFirstName: string;
	userLastName: string;
	companyOrgId: number;
	companyFacilityId: number;
	transactionId: string;
	transactionType: string;
	transactionTypeDesc: string;
	ownerFirstName: string;
	ownerLastName: string;
	vin: string;
	plate: string;
	savedDate: string;
	lastModifiedDate: string;
	requiredDocuments: string[];
	otherDocumentsCount: number;
}

export interface DocumentDestructionList {
	meta: Meta;
	offset: number;
	limit: number;
	size: number;
	first: First;
	previous?: any;
	next: Next;
	last: Last;
	items: Item[];
}


export interface DocumentDestruction {
	destroyedFromDate: string;
	destroyedThroughDate: string;
}

export interface DocumentDestructionRequest {
	documentDestruction: DocumentDestruction;
}

export interface DocumentsToDestroyParameters {
    offset?: number;
    limit?: number;
    startDate?: string;
    endDate?: string;
    transactionType?: string;
    location?: string;
    vin?: string;
    plate?: string;
    owner?: string;
    workflowType?: string;
    status?: string;
    userId?: string;

}

