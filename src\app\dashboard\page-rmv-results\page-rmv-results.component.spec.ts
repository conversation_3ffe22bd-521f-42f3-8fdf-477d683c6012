import { async, ComponentFixture, inject, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { RouterTestingModule } from '@angular/router/testing';

import { data as RMV_QUOTE_DATA } from 'testing/data/rmv-quote';

import { StubDriversServiceProvider } from 'testing/stubs/services/drivers.service.provider';
import { StubLocationsServiceProvider } from 'testing/stubs/services/locations.service.provider';
import { StubOverlayRouteService } from 'testing/stubs/services/overlay-route.service.provider';
import { StubOverlayRouteServiceProvider } from 'testing/stubs/services/overlay-route.service.provider';
import { StubQuotesServiceProvider } from 'testing/stubs/services/quotes.service.provider';
import { StubRmvServiceProvider } from 'testing/stubs/services/rmv.service.provider';
import { StubSpecsServiceProvider } from 'testing/stubs/services/specs.service.provider';
import { StubVehiclesServiceProvider } from 'testing/stubs/services/vehicles.service.provider';

import { OverlayRouteService } from 'app/overlay/services/overlay-route.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Helpers } from 'app/utils/helpers';

import { PageRmvResultsComponent } from './page-rmv-results.component';

describe('Component: PageRmvResults', () => {
  let component: PageRmvResultsComponent;
  let fixture: ComponentFixture<PageRmvResultsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      declarations: [ PageRmvResultsComponent ],
      providers: [
        StubLocationsServiceProvider,
        StubSpecsServiceProvider,
        StubRmvServiceProvider,
        StorageService,
        StubVehiclesServiceProvider,
        StubQuotesServiceProvider,
        StubOverlayRouteServiceProvider,
        StubDriversServiceProvider
      ]
    })
    .compileComponents();
  }));

  beforeEach(inject([StorageService], (storageService: StorageService) => {
    storageService.setStorageData('responseNewRmvQuoteResult', Helpers.deepClone(RMV_QUOTE_DATA));

    fixture = TestBed.createComponent(PageRmvResultsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should destroy without errors', () => {
    expect(() => {
      fixture.destroy();
    }).not.toThrow();
  });

  it('should make it possible to navigate to the vehicle report', inject([OverlayRouteService],
    (overlayRouteService: StubOverlayRouteService) => {
      spyOn(overlayRouteService, 'go');

      const btn = fixture.debugElement.query(By.css('.manage-list__actions button')).nativeElement;
      btn.dispatchEvent(new Event('click'));

      fixture.detectChanges();

      expect(overlayRouteService.go).toHaveBeenCalled();
  }));
});
