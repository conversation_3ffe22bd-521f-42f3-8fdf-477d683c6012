import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { UmbrellaWatercraftComponent } from './umbrella-watercraft.component';
import { StubAutocompleteComponent } from 'testing/stubs/components/autocomplete.component';
import { FormsModule } from '@angular/forms';
import { StubModalboxComponent } from 'testing/stubs/components/modalbox.component';
import { StubConfirmboxComponent } from 'testing/stubs/components/confirmbox.component';
import { RouterTestingModule } from '@angular/router/testing';
import { StorageService } from 'app/shared/services/storage-new.service';
import { StubOverlayLoaderServiceProvider } from 'testing/stubs/services/overlay-loader.service.provider';
import { RouteService } from 'app/shared/services/route.service';
import { WatercraftsService } from 'app/dashboard/app-services/watercrafts.service';
import { StubQuotesServiceProvider } from 'testing/stubs/services/quotes.service.provider';
import { PremiumsService } from '../../../auto/premiums/premiums.service';
import { SymbolsService } from '../../../app-services/symbols.service';
import { PlansService } from '../../../app-services/plans.service';
import { StubLookupsServiceProvider } from 'testing/stubs/services/lookups.service.provider';

class StubWatercraftsService {}

describe('UmbrellaWatercraftComponent', () => {
  let component: UmbrellaWatercraftComponent;
  let fixture: ComponentFixture<UmbrellaWatercraftComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [FormsModule, RouterTestingModule],
      declarations: [
        UmbrellaWatercraftComponent,
        StubAutocompleteComponent,
        StubModalboxComponent,
        StubConfirmboxComponent
      ],
      providers: [
        StorageService,
        StubOverlayLoaderServiceProvider,
        { provide: WatercraftsService, useClass: StubWatercraftsService },
        RouteService,
        StubQuotesServiceProvider,
        PremiumsService,
        SymbolsService,
        PlansService,
        StubLookupsServiceProvider
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(UmbrellaWatercraftComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
