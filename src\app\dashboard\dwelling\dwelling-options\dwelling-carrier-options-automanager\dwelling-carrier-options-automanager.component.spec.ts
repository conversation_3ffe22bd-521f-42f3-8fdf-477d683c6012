import { StorageService } from 'app/shared/services/storage-new.service';
import { async, ComponentFixture, TestBed, fakeAsync, inject, tick } from '@angular/core/testing';

import { data as AGENCY_USER } from 'testing/data/agencies/user';
import { DWELLING_COVERAGES } from 'testing/data/quotes/coverages/dwelling';
import { StubAgencyUserServiceProvider, StubAgencyUserService } from 'testing/stubs/services/agency-user.service.provider';
import { StubOptionsServiceProvider, StubOptionsService } from 'testing/stubs/services/options.service.provider';
import { StubSpecsServiceProvider, StubSpecsService } from 'testing/stubs/services/specs.service.provider';
import { StubSubsServiceProvider } from 'testing/stubs/services/subs.service.provider';
import {
    DwellingCarrierOptionsAutomanagerComponent
} from './dwelling-carrier-options-automanager.component';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { AgencyUserService } from '../../../../shared/services/agency-user.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { OptionsService } from 'app/dashboard/app-services/options.service';
import { Helpers } from 'app/utils/helpers';
import { DWELLING_QUOTE } from 'testing/data/quotes/quote-dwelling';
import { DWELLING_QUOTE_PLAN_LIST } from 'testing/data/quotes/quote-plan-list/dwelling';
import { ApiResponse } from '../../../../app-model/_common';
import { CoveragesData } from '../../../../app-model/coverage';

describe('Component: DwellingCarrierOptionsAutomanager', () => {
  let component: DwellingCarrierOptionsAutomanagerComponent;
  let fixture: ComponentFixture<DwellingCarrierOptionsAutomanagerComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [
        DwellingCarrierOptionsAutomanagerComponent
      ],
      providers: [
        StorageService,
        StubAgencyUserServiceProvider,
        StubSpecsServiceProvider,
        StubOptionsServiceProvider,
        StubSubsServiceProvider,
        StorageGlobalService
      ]
    })
      .compileComponents();
  }));

  beforeEach(fakeAsync(inject(
    [StorageService, AgencyUserService, SpecsService, OptionsService],
    (storageService: StorageService, agencyUserService: StubAgencyUserService,
      specsService: StubSpecsService, optionsService: StubOptionsService) => {
      storageService.setStorageData('selectedQuote', Helpers.deepClone(DWELLING_QUOTE));
      storageService.setStorageData('selectedPlan', Helpers.deepClone(DWELLING_QUOTE_PLAN_LIST));
      agencyUserService.userData = Helpers.deepClone(AGENCY_USER);
      specsService.ratingCoverages = new ApiResponse<CoveragesData>();
      optionsService.coverages = Helpers.deepClone(DWELLING_COVERAGES);

      fixture = TestBed.createComponent(DwellingCarrierOptionsAutomanagerComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
      tick(500);
    })));

  it('should be created', () => {
    expect(component).toBeTruthy();
  });

  it('should destroy without errors', () => {
    expect(() => {
      fixture.destroy();
    }).not.toThrow();
  });
});
