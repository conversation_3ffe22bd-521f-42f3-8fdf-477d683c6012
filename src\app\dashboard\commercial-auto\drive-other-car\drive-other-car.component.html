<section class="section section--compact">
  <div class="row">
    <div class="col-xs-12 u-spacing--1">
      <div class="o-container o-container--scroll-horizontal">

        <table class="table table--row-border-less" style="width:auto">
            <thead>
                <tr>
                  <th><div class="u-width-min-190px"></div></th>
                  <th class="u-width-min-190px">Limits</th>
                </tr>
            </thead>
          <tbody>

            <tr *ngFor="let c of coveragesToShow; let i = index" >
                  <td>{{c.description}}</td>

              <td class="u-align-center" >
                  <sm-autocomplete
                  [fieldAutofocus]="i===0"
                  *ngIf="!c.onlyOneAvailableOption"
                  #selectCoverageInput
                  [options]="c.options"
                  [activeOption]="c.currentValue"
                  [id]="c.coverageCode"
                  [name]="c.coverageCode"
                  [required]="true"
                  [disabled]=""
                  [searchFromBegining]="true"
                  (onSelect)="onCoverageItemSelected($event, c)">
              </sm-autocomplete>

              </td>

            </tr>

          </tbody>
        </table>
        </div>
    </div>
    </div>
    </section>
