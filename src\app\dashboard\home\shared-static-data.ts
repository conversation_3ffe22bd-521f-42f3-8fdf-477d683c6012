import { FilterOption } from 'app/app-model/filter-option';

export interface IFilterOptionsForHomePolicyAndFormType extends FilterOption {
  id: string;
  text: string;
  data: {
    forPolicyType: string
  };
}

export const SharedStaticHomeDataFormTypeOptions: IFilterOptionsForHomePolicyAndFormType[] = [
  {data: {forPolicyType: 'Homeowner'}, id: 'Special (HO3)', text: 'Special (HO3)'},
  {data: {forPolicyType: 'Homeowner'}, id: 'Comprehensive (HO5 or HO3 w. 15)', text: 'Comprehensive (HO5 or HO3 w. 15)'},
  {data: {forPolicyType: 'Condo'}, id: 'HO6', text: 'HO6'},
  {data: {forPolicyType: 'Tenant'}, id: 'HO4', text: 'HO4'}
];

export const SharedStaticHomeDataFormTypeOptionsAlt: IFilterOptionsForHomePolicyAndFormType[] = [
  {data: {forPolicyType: 'Homeowner'}, id: 'Special (HO3)', text: '(HO3) Homeowner - Special'},
  {data: {forPolicyType: 'Homeowner'}, id: 'Comprehensive (HO5 or HO3 w. 15)', text: '(HO5 or HO3 w. 15) Homeowner - Comprehensive'},
  {data: {forPolicyType: 'Condo'}, id: 'HO6', text: 'HO6 - Condo'},
  {data: {forPolicyType: 'Tenant'}, id: 'HO4', text: 'HO4 - Tenant'}
];
