<section class="section section-with-subnav u-flex u-flex--spread">
  <nav class="section-with-subnav--nav">
    <a routerLink="{{quoteUri}}/coverages" routerLinkActive="is-active" [routerLinkActiveOptions]="{ exact: true }" class="o-btn o-btn--pure o-btn--i_list">Coverages 1-12</a>
    <span class="o-separator o-separator--base"></span>
    <a routerLink="{{quoteUri}}/coverages/additional-options" routerLinkActive="is-active" class="o-btn o-btn--pure o-btn--i_round-tick">Additional Coverages</a>
  </nav>

  <div class="">
    <button *ngIf="showCopyCoverages"
      id="copy-coverages"
      class="o-btn o-btn--action o-btn--i_copy"
      (click)="actionOnCopyCoveragesLink($event)">
      Copy coverages
    </button>

    <app-modalbox #refCopyCoveragesModal>
      <app-auto-coverages-copy-coverages
        (copy)="actionOnCopyCoveragesCopy($event)"
        (cancel)="actionOnCopyCoveragesCancel($event)">
      </app-auto-coverages-copy-coverages>
    </app-modalbox>


    <button *ngIf="showToggleButtons" (click)="broadcastVehicleAdditionalCovDefaults()" class="o-btn o-btn--action o-btn--i_list">Apply defaults</button>
    <button *ngIf="showToggleButtons" (click)="broadcastToggleGroupRecordsOpen()" class="o-btn o-btn--action o-btn--i_sq-plus">Show all</button>
    <button *ngIf="showToggleButtons" (click)="broadcastToggleGroupRecordsClose()" class="o-btn o-btn--action o-btn--i_sq-minus">Minimize all</button>
  </div>
</section>
