import { async, ComponentFixture, inject, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';

import { HOME_QUOTE } from 'testing/data/quotes/quote-home';

import { StorageService } from 'app/shared/services/storage-new.service';
import { Helpers } from 'app/utils/helpers';
import { DwellingOptionsNavComponent } from './dwelling-options-nav.component';

describe('DwellingOptionsNavComponent', () => {
  let component: DwellingOptionsNavComponent;
  let fixture: ComponentFixture<DwellingOptionsNavComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [ RouterTestingModule ],
      declarations: [DwellingOptionsNavComponent],
      providers: [
        StorageService
      ]
    })
    .compileComponents();
  }));

  beforeEach(inject([StorageService], (storageService: StorageService) => {
    storageService.setStorageData('selectedQuote', Helpers.deepClone(HOME_QUOTE));

    fixture = TestBed.createComponent(DwellingOptionsNavComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }));

  it('should be created', () => {
    expect(component).toBeTruthy();
  });
});
