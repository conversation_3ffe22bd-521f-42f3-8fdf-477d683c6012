import { WARNING_GROUPS, WarningDefini<PERSON>I, AdditionalData<PERSON>, AdditionalDataUmbrellaClientInfo} from 'app/hints-and-warnings/model/warnings';
import { ClientAddress, ClientContactMethod, ClientDetails } from 'app/app-model/client';
import { Validate } from 'app/hints-and-warnings/validators';
import { Quote } from 'app/app-model/quote';



function requiredField(value): boolean {
  if (value === undefined || value == null) {
    return true;
  }
  value = String(value);

  return value.trim().length <= 0 || !value;
}

function generateViewUrl(): string {
  return  '{{current_url}}?overlay=info&type=client';
}

/**
 * Validation for Client Info Data
 * For Model: ClientDetails
 */

export const WARNINGS_DEFINITIONS_INFO_CLIENT_FOR_UMBRELLA: WarningDefinitionI[] = [
];


// Client Addresses
// ------------------------------------------------------------------------------

/**
 * Validation for Client Info Addresses Data
 * For Model: ClientAddress
 */

export const WARNINGS_DEFINITIONS_INFO_CLIENT_ADDRESSES_FOR_UMBRELLA: WarningDefinitionI[] = [
];

// Client Contact Methods
// ------------------------------------------------------------------------------

/**
 * Validation for Client Info Addresses Data
 * For Model: ClientContactMethod
 */

function generateContactMethodViewFieldId(fullObj: ClientContactMethod): string {
return fullObj.type ? 'cliContact' + fullObj.type : 'no_id';
}

const clientInfoContactMethods: WarningDefinitionI = {
id: 'value',
deepId: 'value',
viewUri: generateViewUrl,
viewFieldId: generateContactMethodViewFieldId, // 'cliContactEmail',
warnings: [{
  label: (val: string, fullObj: ClientContactMethod) => 'Client\'s Phone number incorrect: ' + fullObj.type,
  condition: (value: string, fullObj: ClientContactMethod) => {

    switch (fullObj.type) {
      case 'MobilePhone':
      case 'HomePhone':
      case 'BusinessPhone':
        if (value && !Validate.isValidPhoneNumber(value)) {
          return true;
        }
        break;
      default:
        return false;
    }

    return false;
  },
  group: WARNING_GROUPS.general,
  carriers: [],
},
{
  label: (val: string, fullObj: ClientContactMethod) => 'Client\'s Email address incorrect.',
  condition: (value: string, fullObj: ClientContactMethod) => {
    if (fullObj.type === 'Email' && value && !Validate.isValidEmail(value)) {
      return true;
    }

    return false;
  },
  group: WARNING_GROUPS.general,
  carriers: [],
},
{
  label: (val: string, fullObj: ClientContactMethod) => 'Client\'s Fax Number incorrect.',
  condition: (value: string, fullObj: ClientContactMethod) => {
    if (fullObj.type === 'Fax' && value && !Validate.isValidPhoneNumber(value)) {
      return true;
    }

    return false;
  },
  group: WARNING_GROUPS.general,
  carriers: [],
}]
};


export const WARNINGS_DEFINITIONS_INFO_CLIENT_CONTACT_METHODS_FOR_UMBRELLA: WarningDefinitionI[] = [
  clientInfoContactMethods
];
