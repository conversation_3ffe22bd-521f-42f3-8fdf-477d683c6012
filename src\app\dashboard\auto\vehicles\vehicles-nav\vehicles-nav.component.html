<section class="section section-with-subnav section-vehicles">
  <nav class="section-with-subnav--nav" *ngIf="vehicles && vehicles?.length">
    <span *ngFor="let vehicle of vehicles; let last = last;">
      <a
       #refVehicleLink
       routerLink="/dashboard/auto/{{ vehicle?.meta.href }}"
       routerLinkActive="is-active"
       [routerLinkActiveOptions]="{ exact: true }"
       class="o-btn o-btn--pure o-btn--i_car">{{ vehicleNameToDisplay(vehicle) || 'Vehicle name' }}</a>
       <span *ngIf="!last" class="o-separator o-separator--base"></span>
       <sm-tooltip
        [launcher]="refVehicleLink"
        [css]="'tooltip-new tooltip-new--badge'"
        [position]="'my-center-at-bottom-center'"
        [positionLauncher]="refVehicleLink"
        [onHover]='true'>
          {{vehicleNameFull(vehicle)}}
        </sm-tooltip>
    </span>
  </nav>
</section>
