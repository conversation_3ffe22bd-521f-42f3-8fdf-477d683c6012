<section class="section section-with-subnav">
  <nav class="section-with-subnav--nav" *ngIf="drivers?.length > 0">
    <span *ngFor="let driver of drivers; let last = last;">
      <a
        routerLink="/dashboard/auto{{ driver.meta.href }}"
        routerLinkActive="is-active"
        [routerLinkActiveOptions]="{ exact: true }"
        class="o-btn o-btn--pure o-btn--i_user u-spacing--left-2">
          {{ driver.firstName }} {{ driver.lastName }}
          <span *ngIf="!driver.firstName && !driver.lastName">New Driver</span>
      </a>
      <span *ngIf="!last" class="o-separator o-separator--base"></span>
    </span>
  </nav>
</section>
