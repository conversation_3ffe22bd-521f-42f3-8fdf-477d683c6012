import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { SummaryRmvDetailsComponent } from './summary-rmv-details.component';

describe('SummaryRmvDetailsComponent', () => {
  let component: SummaryRmvDetailsComponent;
  let fixture: ComponentFixture<SummaryRmvDetailsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ SummaryRmvDetailsComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SummaryRmvDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
