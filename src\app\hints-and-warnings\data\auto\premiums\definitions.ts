import { WARNING_GROUPS, WarningDefinitionI, AdditionalDataQuotePlans } from 'app/hints-and-warnings/model/warnings';
import { QuotePlan, EQuotePlanRatingType, QuotePlanListAPIResponse } from 'app/app-model/quote';
import { Vehicle } from 'app/app-model/vehicle';


// https://bostonsoftware.atlassian.net/browse/SPR-2661

function generateViewTrailer(fullObj: QuotePlanListAPIResponse, additionalData): string {
  const vehicle = additionalData.find((v: Vehicle) => v.vehicleType === 'Trailer');
  return (vehicle && vehicle.meta && vehicle.meta.href) ? '/dashboard/auto' + vehicle.meta.href : '';
}

function generateViewMotorcycle(fullObj: QuotePlanListAPIResponse, additionalData): string {
  const vehicle = additionalData.find((v: Vehicle) => v.vehicleType === 'Motorcycle');
  return (vehicle && vehicle.meta && vehicle.meta.href) ? '/dashboard/auto' + vehicle.meta.href : '';
}

function getPlansList(data: QuotePlanListAPIResponse): QuotePlan[] {
  let quotePlans: QuotePlan[] = [];
  if (data && data.items && data.items.length) {
    quotePlans = JSON.parse(JSON.stringify(data.items[0].items));
  }
  return quotePlans;
}

/**
 * Validation for Selected plans
 * For Model: QuotePlanListAPIResponse
 */

const trailerRatingType: WarningDefinitionI = {
  id: 'resourceName', // define field that exists in the object
  deepId: 'resourceName',
  viewUri: generateViewTrailer,
  viewFieldId: 'vehicleType', // 'trailerRatingType',
  warnings: [{
    label: (value, fullObj) => 'Trailer Rating Not Supported.',
    condition: (value: string, fullObj: QuotePlanListAPIResponse, additionalData) => {
      const vehiclesNumber: number = additionalData.length;
      const trailerVehicles: Vehicle[] = additionalData.filter((v: Vehicle) => v.vehicleType === 'Trailer');
      const trailerVehiclesNumber = trailerVehicles.length;
      const plans: QuotePlan[] = getPlansList(fullObj);
      const carriers = plans.filter((p: QuotePlan) => {
        return p.trailerRatingType === EQuotePlanRatingType.None;
      });
      return trailerVehiclesNumber && trailerVehiclesNumber > 0 && carriers && carriers.length > 0;
    },
    group: WARNING_GROUPS.carrier,
    carriers: (fullObj: QuotePlanListAPIResponse, additionalData: AdditionalDataQuotePlans) => {
      const plans: QuotePlan[] = getPlansList(fullObj);

      const carriers = plans.filter((p: QuotePlan) => {
        return p.trailerRatingType === EQuotePlanRatingType.None;
      });
      return carriers.map((p: QuotePlan) => p.ratingPlanId);
    }
  },
  {
    label: (value, fullObj) => 'Trailers cannot be rated with other vehicle types.',
    condition: (value: string, fullObj: QuotePlanListAPIResponse, additionalData) => {
      const vehiclesNumber: number = additionalData.length;
      const trailerVehicles: Vehicle[] = additionalData.filter((v: Vehicle) => v.vehicleType === 'Trailer');
      const trailerVehiclesNumber = trailerVehicles.length;
      const plans: QuotePlan[] = getPlansList(fullObj);
      const carriers = plans.filter((p: QuotePlan) => {
        return p.trailerRatingType === EQuotePlanRatingType.Mono;
      });
      return trailerVehiclesNumber && trailerVehiclesNumber > 0 && vehiclesNumber !== trailerVehiclesNumber
               && carriers && carriers.length > 0;
    },
    group: WARNING_GROUPS.carrier,
    carriers: (fullObj: QuotePlanListAPIResponse, additionalData) => {
      const plans: QuotePlan[] = getPlansList(fullObj);


      const carriers = plans.filter((p: QuotePlan) => {
        return p.trailerRatingType === EQuotePlanRatingType.Mono;
      });
      return carriers.map((p: QuotePlan) => p.ratingPlanId);
    }
  }]
};

const motorcycleRatingType: WarningDefinitionI = {
  id: 'resourceName', // define field that exists in the object
  deepId: 'resourceName',
  viewUri: generateViewMotorcycle,
  viewFieldId: 'vehicleType', // 'motorcycleRatingType',
  warnings: [{
    label: (value, fullObj) => 'Motorcycle Rating Not Supported.',
    condition: (value: string, fullObj: QuotePlanListAPIResponse, additionalData) => {
      const vehiclesNumber: number = additionalData.length;
      const motorcycleVehicles: Vehicle[] = additionalData.filter((v: Vehicle) => v.vehicleType === 'Motorcycle');
      const motorcycleVehiclesNumber = motorcycleVehicles.length;

      const plans: QuotePlan[] = getPlansList(fullObj);

      const carriers = plans.filter((p: QuotePlan) => {
        return p.motorcycleRatingType === EQuotePlanRatingType.None;
      });
      console.log(additionalData, motorcycleVehicles, motorcycleVehiclesNumber, carriers);
      return motorcycleVehiclesNumber && motorcycleVehiclesNumber > 0 && carriers && carriers.length > 0;
    },
    group: WARNING_GROUPS.carrier,
    carriers: (fullObj: QuotePlanListAPIResponse, additionalData) => {
      const plans: QuotePlan[] = getPlansList(fullObj);

      const carriers = plans.filter((p: QuotePlan) => {
        return p.motorcycleRatingType === EQuotePlanRatingType.None;
      });
      return carriers.map((p: QuotePlan) => p.ratingPlanId);
    }
  },
  {
    label: (value, fullObj) => 'Motorcycles cannot be rated with other vehicle types.',
    condition: (value: string, fullObj: QuotePlanListAPIResponse, additionalData) => {
      const vehiclesNumber: number = additionalData.length;
      const motorcycleVehicles: Vehicle[] = additionalData.filter((v: Vehicle) => v.vehicleType === 'Motorcycle');
      const motorcycleVehiclesNumber = motorcycleVehicles.length;
      const plans: QuotePlan[] = getPlansList(fullObj);

      const carriers = plans.filter((p: QuotePlan) => {
        return p.motorcycleRatingType === EQuotePlanRatingType.Mono;
      });
      return motorcycleVehiclesNumber && motorcycleVehiclesNumber > 0 && vehiclesNumber !== motorcycleVehiclesNumber
      && carriers && carriers.length > 0;
    },
    group: WARNING_GROUPS.carrier,
    carriers: (fullObj: QuotePlanListAPIResponse, additionalData) => {
      const plans: QuotePlan[] = getPlansList(fullObj);

      const carriers = plans.filter((p: QuotePlan) => {
        return p.motorcycleRatingType === EQuotePlanRatingType.Mono;
      });
      return carriers.map((p: QuotePlan) => p.ratingPlanId);
    }
  }
]
};

export const WARNINGS_DEFINITIONS_AUTO_SELECTED_PLANS: WarningDefinitionI[] = [
  trailerRatingType,
  motorcycleRatingType
];
