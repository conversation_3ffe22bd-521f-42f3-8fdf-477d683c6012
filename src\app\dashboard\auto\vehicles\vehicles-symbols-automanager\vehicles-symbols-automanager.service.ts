
import {first, take} from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { SelectedVehicleSymbols, SelectedVehicleSymbolsForVehicle } from 'app/app-model/symbols';
import { Quote, QuotePlan } from 'app/app-model/quote';
import {
  Vehicle,
  VehicleGeneralDetails,
  VehiclePlanSymbols,
  VehicleSymbol,
} from 'app/app-model/vehicle';

import { SubscriptionLike as ISubscription } from 'rxjs';
import { LookupsService } from 'app/dashboard/app-services/lookups.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { SymbolsService } from 'app/dashboard/app-services/symbols.service';
import { VehiclesService } from 'app/dashboard/app-services/vehicles.service';


@Injectable()
export class VehiclesSymbolsAutomanagerService {
  private serviceIsInitialized: boolean = false;

  private subscriptionQuote: ISubscription;
  private subscriptionVehicles: ISubscription;
  private subscriptionQuotePlans: ISubscription;
  private subscriptionSelectedVehicleSymbolsForVehicle: ISubscription;
  private subscriptionsGetVehicleDetails: ISubscription[] = [];
  private subscriptionsRetrieveVehicleSymbols: ISubscription[] = [];

  private selectedQuote: Quote;
  private selectedQuotePlansList: QuotePlan[] = [];
  private selectedQuotePreviousEffectiveDate: string = null;
  // private selectedVehiclePlanSymbols: VehiclePlanSymbols[] = [];

  // private vehiclesListPreviousState: Vehicle[] = []; // Used to compare data changes
  private vehiclesList: Vehicle[] = [];
  private selectedVehicleSymbolsForVehicle:SelectedVehicleSymbolsForVehicle[] = []
  // private allowUpdateSymbolsOnPlanChange = false; // Prevent symbols update on component init
  // private allowRemoveSelectedVehicleSymbolsForVehicleFromStorage = false; // Allow after data was initialized

  private serviceInitializedWithAllOperations: boolean = false;

  constructor(
    private storageService: StorageService,
    private vehiclesService: VehiclesService,
    private lookupsService: LookupsService,
    private symbolsService: SymbolsService
  ) { }

  initialize(): Promise<void> {
    if(this.serviceIsInitialized) {
      return Promise.resolve();
    }

    this.destroy(); // Make sure that all subscriptions has been closed

    this.serviceIsInitialized = true;
    // this.allowUpdateSymbolsOnPlanChange = false; // It will be changed in subscribeSelectedQuotePlans()

    return Promise.all([
      this.subscribeVehicles(),
      this.subscribeSelectedQuote(),
      this.subscribeSelectedQuotePlans(),
      this.subscribeSelectedVehicleSymbolsForVehicle()
    ])
    .then(()=> {
      // this should be initialized only once the auto module is initialized with loaded quote
      return this.initVehiclesSymbolsCheckRequirements();
    })
    .then(() => {
      this.serviceInitializedWithAllOperations = true;
      // console.log('%cSYMBOLS INITIALIZED', 'background:pink')
    })
    .catch(err => console.log(err));
  }

  destroy() {
    this.serviceIsInitialized = false;
    // this.allowUpdateSymbolsOnPlanChange = false;

    this.subscriptionQuote && this.subscriptionQuote.unsubscribe();
    this.subscriptionVehicles && this.subscriptionVehicles.unsubscribe();
    this.subscriptionQuotePlans && this.subscriptionQuotePlans.unsubscribe();
    this.subscriptionSelectedVehicleSymbolsForVehicle && this.subscriptionSelectedVehicleSymbolsForVehicle.unsubscribe();

    this.unsubscribeAllSubscriptionsInArray(this.subscriptionsGetVehicleDetails);
    this.unsubscribeAllSubscriptionsInArray(this.subscriptionsRetrieveVehicleSymbols);
    this.subscriptionsGetVehicleDetails = [];
    this.subscriptionsRetrieveVehicleSymbols = [];

    this.vehicleSubscriptionDelayActionTimer && clearTimeout(this.vehicleSubscriptionDelayActionTimer);
    this.serviceInitializedWithAllOperations = false;
  }

  // Subscriptions
  //-------------------------------------------------------------------------------
  private vehicleSubscriptionDelayActionTimer;

  public subscribeVehicles():Promise<Vehicle[]> {
    return new Promise((resolve, reject) => {
      this.subscriptionVehicles = this.storageService.getStorageData('vehiclesList').subscribe(data => {
        this.vehiclesList = data;
        resolve(this.vehiclesList);

        // Delete Symbols if vehicle has been deleted
        this.vehicleSubscriptionDelayActionTimer && clearTimeout(this.vehicleSubscriptionDelayActionTimer);
        // if (this.allowRemoveSelectedVehicleSymbolsForVehicleFromStorage) {
        if (this.serviceInitializedWithAllOperations) {
          this.vehicleSubscriptionDelayActionTimer = setTimeout(() => {
            this.updateStorageSymbolsOnVehicleDelete();
          }, 500);
        }



          // this.setVehicleSymbolsIfVehicleDataYearChanged(this.vehiclesList);
          // this.vehiclesListPreviousState = JSON.parse(JSON.stringify(data));
      });
    })
  }

  public subscribeSelectedQuote():Promise<Quote> {
    return new Promise((resolve, reject) => {
      this.subscriptionQuote = this.storageService.getStorageData('selectedQuote').subscribe(
        res => {
          this.selectedQuote = res;

          // Initial date Assign
          if (this.selectedQuotePreviousEffectiveDate == null) {
            this.selectedQuotePreviousEffectiveDate = this.selectedQuote.effectiveDate;
          }

          // Update previous Effective Date and symbols if Effective Date changed
          if (this.selectedQuotePreviousEffectiveDate != this.selectedQuote.effectiveDate) {
            this.selectedQuotePreviousEffectiveDate = this.selectedQuote.effectiveDate;

            if (this.serviceInitializedWithAllOperations) {
              // console.log('%cEffective Date Changed, Update Symbols', 'color:blue');
              this.setVehiclesSymbols();
            }
          }

          // Resolve Promise just after the correct Quote data has been set;
          if (this.selectedQuote && this.selectedQuote.resourceId) {
            resolve(this.selectedQuote);
          } else {
            console.log('Waiting for Quote Data');
          }
        }
      );
    });
  }

  public subscribeSelectedQuotePlans():Promise<QuotePlan[]> {
    return new Promise((resolve, reject) => {
      this.subscriptionQuotePlans = this.storageService.getStorageData('selectedPlan').subscribe(plans => {
        if (plans && plans.items && plans.items.length) {
          this.selectedQuotePlansList = plans.items[0].items;

          // if (this.allowUpdateSymbolsOnPlanChange) {
          if (this.serviceInitializedWithAllOperations) {
            // console.log('%cPlans Changed, Update Symbols', 'color:green');
            this.setVehiclesSymbols();
          }

          // this.allowUpdateSymbolsOnPlanChange = true;
        }

        if (this.selectedQuotePlansList && this.selectedQuotePlansList.length) {
          resolve(this.selectedQuotePlansList)
        } else {
          console.log('Waiting for Quote Selected Plans');
        }
      });
    });
  }

  private subscribeSelectedVehicleSymbolsForVehicle():Promise<SelectedVehicleSymbolsForVehicle[]> {
    return new Promise((resolve, reject) => {
      this.subscriptionSelectedVehicleSymbolsForVehicle = this.storageService.getStorageData('vehiclesSelectedSymbolsForVehicle')
        .subscribe((res:SelectedVehicleSymbolsForVehicle[]) => {
          this.selectedVehicleSymbolsForVehicle = res;
          resolve(res)
        })
    });
  }

  private updateStorageSymbolsOnVehicleDelete():void {
     if (this.vehiclesList.length && this.selectedVehicleSymbolsForVehicle.length) {
       if (this.vehiclesList.length < this.selectedVehicleSymbolsForVehicle.length) {

         let symbolsToDelete = this.selectedVehicleSymbolsForVehicle.filter((symbol:SelectedVehicleSymbolsForVehicle) => {
           return !this.vehiclesList.some((vehicle:Vehicle) => vehicle.resourceId == symbol.vehicle.resourceId)
         })

         if(symbolsToDelete.length) {
          symbolsToDelete.forEach((symbol:SelectedVehicleSymbolsForVehicle) => {
            this.storageService.deleteVehiclesSelectedSymbolsForVehicleSingleItem(symbol);
          })
         }
       }
     }
  }

  // -----------
  private setVehiclesSymbols():void {
    // For Each Vehicle:
    this.vehiclesList.forEach((vehicle:Vehicle) => {
      if (vehicle.vehicleType == 'Trailer' || vehicle.vehicleType == 'Motorcycle') {
        // skip symbols update
      } else {
        this.performVehicleSymbolsUpdate(vehicle);
      }
    });
  }

  // private setVehicleSymbolsIfVehicleDataYearChanged(vehicles: Vehicle[]): void {
  //   vehicles.forEach((v: Vehicle) => {
  //     const vehiclePreviousState: Vehicle = this.vehiclesListPreviousState.find((psv: Vehicle) => psv.resourceId === v.resourceId);

  //     if (vehiclePreviousState && vehiclePreviousState.year !== v.year) {
  //       if (v.vehicleType == 'Trailer' || v.vehicleType == 'Motorcycle') {
  //         // skip symbols update
  //       } else {
  //         console.log('%c Update vehicle symbols for vehicle: ', 'color:gold', v);
  //         this.performVehicleSymbolsUpdate(v);
  //       }
  //     }
  //   });
  // }

  public performVehicleSymbolsUpdate(vehicle:Vehicle): Promise<void> {
    // console.log('%c performVehicleSymbolsUpdate', 'color: red');
    let tmpVehicleDetails:VehicleGeneralDetails;

    this.symbolsService.setSymbolsUpdatingInProgress(vehicle, true);

    // Cancel all previous requests if there are any
    if (this.subscriptionsGetVehicleDetails.length && this.subscriptionsGetVehicleDetails[vehicle.resourceId]) {
      this.subscriptionsGetVehicleDetails[vehicle.resourceId].unsubscribe();
    }

    if (this.subscriptionsRetrieveVehicleSymbols.length && this.subscriptionsRetrieveVehicleSymbols[vehicle.resourceId]) {
      this.subscriptionsRetrieveVehicleSymbols[vehicle.resourceId].unsubscribe();
    }

    return this.getVehiclesDetails(vehicle)
      .then((vehicleDetails:VehicleGeneralDetails) => {
        tmpVehicleDetails = vehicleDetails;
        return this.retrieveVehicleSymbols(vehicle, vehicleDetails);
      })
      .then((planSymbols:VehiclePlanSymbols[]) => {
        this.symbolsService.setSymbolsUpdatingInProgress(vehicle, false);
        return this.updateVehicleSymbolsAndPriceValue(vehicle, planSymbols);
      })
      .catch(err => {
        // If the vehicle data missing year/make/mode error occurs, but we need to update symbols for vehicle
        this.handleCatchErrorOnPerformVehicleSymbolsUpdate(vehicle);
        this.symbolsService.setSymbolsUpdatingInProgress(vehicle, false);
        console.log('VEHICLES SYMBOLS AUTOMANAGER ERROR:', err);
      });
  }

  private handleCatchErrorOnPerformVehicleSymbolsUpdate(vehicle: Vehicle): void {
    const selectedVehicleSymbolsForVehicle: SelectedVehicleSymbolsForVehicle = this.selectedVehicleSymbolsForVehicle
      .find((data: SelectedVehicleSymbolsForVehicle) => data.vehicle.resourceId === vehicle.resourceId);

    if (selectedVehicleSymbolsForVehicle) {
      // console.log('%c HANDLE ERROR: SET REQUIRED SYMBOLS BASED ON SELECTED PLANS: ', 'background:gold', vehicle);
      selectedVehicleSymbolsForVehicle.selectedVehicleSymbols = this.symbolsService.manageSelectedVehicleSymbolsRequiredFieldsBasedOnSelectedPlans(
        selectedVehicleSymbolsForVehicle.selectedVehicleSymbols,
        this.selectedQuotePlansList,
        vehicle
      );

      // Get required symbols for vehicle
      const requiredSymbols: VehicleSymbol[] = Object.keys(selectedVehicleSymbolsForVehicle.selectedVehicleSymbols)
        .filter((symbolKey: string) => selectedVehicleSymbolsForVehicle.selectedVehicleSymbols[symbolKey].required)
        .map((symbolKey: string) => selectedVehicleSymbolsForVehicle.selectedVehicleSymbols[symbolKey].symbol);

      this.updateVehicleSymbolsAndPriceValue(vehicle, null, requiredSymbols);
    }
  }


  private getVehiclesDetails(vehicle:Vehicle):Promise<any> {
    if (vehicle.vin) {
      vehicle.vin = vehicle.vin.toUpperCase();
    }

    // Skip getting vehicle details (VIN lookup) and symbols update
    if (vehicle.vehicleType && (vehicle.vehicleType == 'Trailer' || vehicle.vehicleType == 'Motorcycle')) {
      return Promise.reject('[vehicle-symbols-automanager] Stopped updating symbols for vehicle type => ' + vehicle.vehicleType);
    }

    // By Vin
    if (vehicle.vin && this.vinIsValid(vehicle.vin)) {
      return new Promise((resolve, reject)=>{

        if (this.subscriptionsGetVehicleDetails[vehicle.resourceId]) {
          this.subscriptionsGetVehicleDetails[vehicle.resourceId].unsubscribe()
        }

        // this.subscriptionsGetVehicleDetails[vehicle.resourceId] && this.subscriptionsGetVehicleDetails[vehicle.resourceId].unsubscribe();
        this.subscriptionsGetVehicleDetails[vehicle.resourceId] = this.lookupsService.getVehicleDataByVin(vehicle.vin).pipe(
          take(1))
          .subscribe(
            (genDetails:VehicleGeneralDetails) => {
              resolve(genDetails);
            },
            err => {
              console.log('VEHICLE GENERAL DETAILS LOOKUP ERROR', err);
              reject(err);
            }
          );
      });
    }
    // By Year/Make/Model
    else if (vehicle.year && vehicle.make && vehicle.model) {
      return new Promise( (resolve, reject) => {
          this.subscriptionsGetVehicleDetails[vehicle.resourceId] && this.subscriptionsGetVehicleDetails[vehicle.resourceId].unsubscribe();
          this.subscriptionsGetVehicleDetails[vehicle.resourceId] = this.lookupsService.getVehicleModelDetails(
          vehicle.year,
          vehicle.make,
          vehicle.model,
          this.selectedQuote.effectiveDate
        ).pipe(take(1))
        .subscribe(
          res => resolve(res),
          err => {
            console.log('VEHICLE GENERAL DETAILS LOOKUP BY Year/Make/Model ERROR: ', err);
            reject(err);
          }
        );
      });
    } else {
      return Promise.reject('[vehicle-symbols-automanager] VEHICLE GENERAL DETAILS LOOKUP ERROR - neither VIN nor Year/Make/Model');
    }
  }


  private retrieveVehicleSymbols(vehicle:Vehicle, genDetails:VehicleGeneralDetails):Promise<VehiclePlanSymbols[]> {
    return new Promise((resolve, reject) => {
      // this.subscriptionVehiclesPlanSymbols = this.symbolsService.retrieveVehiclePlanSymbols(
      this.subscriptionsRetrieveVehicleSymbols[vehicle.resourceId] = this.symbolsService.retrieveVehiclePlanSymbols(
        vehicle,
        genDetails,
        this.selectedQuote.effectiveDate,
        this.selectedQuotePlansList
      ).pipe(first())
       .subscribe(
          res => resolve(res),
          err => {
            console.log('[retrieveVehicleSymbols] ERROR: ', err);
            reject(err);
          }
       );
    });
  }

  private updateVehicleSymbolsAndPriceValue(vehicle:Vehicle, vehiclePlanSymbols:VehiclePlanSymbols[] | null, vehicleSymbolsToUseAsRequiredSymbols?: VehicleSymbol[]):void {
    // 1. Filter unique symbols or required symbols
    let uniqueSymbols:VehicleSymbol[] = [];
    let symbolsToAssignForVehicle: VehicleSymbol[] = [];

    if (vehiclePlanSymbols) {
      // Filter unique symbols
      let allSymbols:VehicleSymbol[] = this.symbolsService.extractSymbolsToArrayFromPlanSymbols(vehiclePlanSymbols);
      uniqueSymbols = this.symbolsService.filterUniqueSymbolsBySymbolDescription(allSymbols);

      // Prepare symbols that should be assigned to vehicle - retain already set values
      symbolsToAssignForVehicle = this.symbolsService.prepareProperSymbolsForTheVehicle(uniqueSymbols, vehicle.symbols);
    } else if (vehiclePlanSymbols === null && vehicleSymbolsToUseAsRequiredSymbols) {
      // Prepare symbols that should be assigned to vehicle - retain already set values
      const currentVehicleSymbols: VehicleSymbol[] = vehicle.symbols || [];
      symbolsToAssignForVehicle = this.symbolsService.prepareProperSymbolsForTheVehicle(vehicleSymbolsToUseAsRequiredSymbols, currentVehicleSymbols);
    } else {
      throw new Error('Neither VehiclePlanSymbols nor vehicleSymbolsToUseAsRequiredSymbols are not available to complete Vehicle symbols update for vehicle');
    }

    // 2. Assign unique symbols with proper values (keep symbols that was already set)
    vehicle.symbols = symbolsToAssignForVehicle;


    // 3. Map symbols to selected symbols, to check required symbols
    let selectedVehicleSymbols:SelectedVehicleSymbols = new SelectedVehicleSymbols();
    selectedVehicleSymbols = this.symbolsService.mapSymbolsArrayToSelectedVehicleSymbolsBySymbolDescription(symbolsToAssignForVehicle, selectedVehicleSymbols);
    selectedVehicleSymbols = this.symbolsService.manageSelectedVehicleSymbolsRequiredFieldsBasedOnVehicleSymbols(symbolsToAssignForVehicle, selectedVehicleSymbols);

    // Set Source Symbols and requirements only if working with VehiclePlanSymbols
    if (vehiclePlanSymbols) {
      selectedVehicleSymbols = this.symbolsService.setSourceSymbolsForSelectedVehicleSymbols(uniqueSymbols, selectedVehicleSymbols);
      selectedVehicleSymbols = this.symbolsService.setRequiredForPlansForSelectedVehicleSymbols(vehiclePlanSymbols, selectedVehicleSymbols);
    }

    // 4. Check if Price/Value field is required
    // https://bostonsoftware.atlassian.net/browse/SPRC-9 (if price no longer required, remove value)
    // let isRequiredPriceValue = this.symbolsService.checkIfVehiclePriceNewValueIsRequired(selectedVehicleSymbols);
    let isRequiredPriceValue = SymbolsService.checkIfVehiclePriceNewValueIsRequired(selectedVehicleSymbols);

    // Commented on 2017-09-07 because of https://bostonsoftware.atlassian.net/browse/SPRC-289
    // if (!isRequiredPriceValue) {
    //   vehicle.priceValue = "";
    // }

    // 5. Update vehicle
    this.updateVehicle(vehicle);

    // 6. Set SelectedVehicleSymbolsForVehicle and save to storage
    let tmpSelectedVehicleSymbolsForVehicle:SelectedVehicleSymbolsForVehicle = new SelectedVehicleSymbolsForVehicle();
    tmpSelectedVehicleSymbolsForVehicle.vehicle = vehicle;
    tmpSelectedVehicleSymbolsForVehicle.selectedVehicleSymbols = selectedVehicleSymbols;
    tmpSelectedVehicleSymbolsForVehicle.priceNewValueIsRequired = isRequiredPriceValue; // Set if the field is editable

    this.storageService.updateVehiclesSelectedSymbolsForVehicleSingleItem(tmpSelectedVehicleSymbolsForVehicle);
  }


  private updateVehicle(vehicle:Vehicle):void {
    this.vehiclesService.updateVehicle(vehicle.parentId, vehicle.resourceId, vehicle).pipe(
      first())
      .subscribe(res => console.log('UPDATED VEHICLE', res));
  }


  // Helpers
  // -----------------------------------------------------------------------
  private vinIsValid(vin:string):boolean {
    let validVin = this.vehiclesService.vinValidate(vin);
    let validPartialVin = (vin && vin.length == 10);

    return validVin || validPartialVin;
  }


  // New Functionality - INITIALIZATION
  // -----------------------------------------------------------------------
  private initVehiclesSymbolsCheckRequirements(): Promise<void> {
    // Get Vehicles
    // Get symbols from Vehicle
    // Convert symbols to SelectedVehicleSymbols
    // Get required symbols for Vehicle From API
    // Manage symbols requirements based on API => SelectedVehicleSymbols
    // Save SelectedVehicleSymbols to storage

    return this.getVehicles()
      .then((vehicles:Vehicle[]) => {
        return this.setSelectedVehicleSymbolsForVehicle(this.vehiclesList)
      })
      .then((symbolsForVehicles:SelectedVehicleSymbolsForVehicle[]) => {
        return this.setRequiredFieldsForSelectedVehicleSymbolsForVehicle(symbolsForVehicles);
      })
      .then((symbolsForVehiclesWithRequirements:SelectedVehicleSymbolsForVehicle[]) => {

        // Update Vehicles Resources (API) and storage with adjusted symbols
        let vehiclesToUpdateInStorage: Vehicle[] = symbolsForVehiclesWithRequirements.map((item: SelectedVehicleSymbolsForVehicle) => {
          // Update vehicle Resource  - API
          this.updateVehicle(item.vehicle);
          return item.vehicle;
        });
        this.storageService.setStorageData('vehiclesList', vehiclesToUpdateInStorage);
        //-------

        this.storageService.setStorageData('vehiclesSelectedSymbolsForVehicle', symbolsForVehiclesWithRequirements);
      })
      // .then(() => {
      //   // this.allowRemoveSelectedVehicleSymbolsForVehicleFromStorage = true;
      //   // console.log('%cSYMBOLS INITIALIZED', 'background:pink')
      // })
      .catch(err => {
        console.log(err)
      })
  }

  private getVehicles():Promise<Vehicle[]> {
    return new Promise((resolve, reject) => {
      if (this.vehiclesList.length) {
        resolve(this.vehiclesList)
      } else {
        this.vehiclesService.getVehiclesList(this.selectedQuote.resourceId).pipe(
          take(1))
          .subscribe(res => {
            this.vehiclesList = res.items;
            resolve(this.vehiclesList);
          })
      }
    })
  }

  private setSelectedVehicleSymbolsForVehicle(vehicles:Vehicle[]):Promise<SelectedVehicleSymbolsForVehicle[]> {
    return new Promise((resolve,reject) => {
      this.selectedVehicleSymbolsForVehicle = [];

      vehicles.forEach((vehicle:Vehicle) => {
        const tmpSelectedVehicleSymbolsForVehicle:SelectedVehicleSymbolsForVehicle = new SelectedVehicleSymbolsForVehicle();
        let selectedVehicleSymbols:SelectedVehicleSymbols = new SelectedVehicleSymbols();

        // Normalize Vehicle Symbols Value
        if (vehicle.symbols && vehicle.symbols.length) {
          vehicle.symbols = vehicle.symbols.map((sym: VehicleSymbol) => {
            sym = sym && this.symbolsService.normalizeSymbolValue(sym);
            return sym;
          })
        }

        selectedVehicleSymbols = this.symbolsService.mapSymbolsArrayToSelectedVehicleSymbolsBySymbolDescription(vehicle.symbols, selectedVehicleSymbols);
        tmpSelectedVehicleSymbolsForVehicle.selectedVehicleSymbols = selectedVehicleSymbols;
        tmpSelectedVehicleSymbolsForVehicle.vehicle = vehicle;
        // tmpSelectedVehicleSymbols.vehicleResourceId = vehicle.resou
        this.selectedVehicleSymbolsForVehicle.push(tmpSelectedVehicleSymbolsForVehicle);
      });

      resolve(this.selectedVehicleSymbolsForVehicle)

    });
  }


  // Set required fields based on API if available, else based on selected plans
  private setRequiredFieldsForSelectedVehicleSymbolsForVehicle(selectedVehicleSymbolsForVehicle:SelectedVehicleSymbolsForVehicle[]):Promise<SelectedVehicleSymbolsForVehicle[]> {
    let promises = [];

    selectedVehicleSymbolsForVehicle.forEach((item:SelectedVehicleSymbolsForVehicle) => {
      let tmpPromise = new Promise((resolve,reject) => {
        let tmpVehicleDetails:VehicleGeneralDetails;
        this.symbolsService.setSymbolsUpdatingInProgress(item.vehicle, true);

        this.getVehiclesDetails(item.vehicle)
          .then((vehicleDetails:VehicleGeneralDetails) => {
            tmpVehicleDetails = vehicleDetails;
            return this.retrieveVehicleSymbols(item.vehicle, vehicleDetails);
          })
          .then((planSymbols:VehiclePlanSymbols[]) => {
            const allSymbols:VehicleSymbol[] = this.symbolsService.extractSymbolsToArrayFromPlanSymbols(planSymbols);
            const uniqueSymbols:VehicleSymbol[] = this.symbolsService.filterUniqueSymbolsBySymbolDescription(allSymbols);

            item.selectedVehicleSymbols = this.symbolsService.manageSelectedVehicleSymbolsRequiredFieldsBasedOnVehicleSymbols(uniqueSymbols, item.selectedVehicleSymbols);
            item.selectedVehicleSymbols = this.symbolsService.setSourceSymbolsForSelectedVehicleSymbols(uniqueSymbols, item.selectedVehicleSymbols);
            item.selectedVehicleSymbols = this.symbolsService.setRequiredForPlansForSelectedVehicleSymbols(planSymbols, item.selectedVehicleSymbols);

            // Adjust Vehicle Symbols, to be able to update Vehicles in storage
            item.selectedVehicleSymbols = this.symbolsService.setSelectedVehicleSymbolsSymbolIfTheValueIsEmptyBasedOnSourceSymbol(item.selectedVehicleSymbols);

            let symbolsForTheVehicle: VehicleSymbol[] = [];
            for (const symbolType in item.selectedVehicleSymbols) {
              if (item.selectedVehicleSymbols.hasOwnProperty(symbolType)) {
                if(item.selectedVehicleSymbols[symbolType].required) {
                  symbolsForTheVehicle.push(item.selectedVehicleSymbols[symbolType].symbol);
                }
              }
            }

            // Updated vehicle with adjusted symbols - required to update vehicles with correct symbols in storage
            item.vehicle.symbols = symbolsForTheVehicle;
            //------------------------------------------------------------------

            // check if Field Price new Value is required and save it in SelectedVehicleSymbolsForVehicle
            // let isRequired = this.symbolsService.checkIfVehiclePriceNewValueIsRequired(item.selectedVehicleSymbols);
            let isRequired = SymbolsService.checkIfVehiclePriceNewValueIsRequired(item.selectedVehicleSymbols);
            item.priceNewValueIsRequired = isRequired;

            this.symbolsService.setSymbolsUpdatingInProgress(item.vehicle, false);

            resolve(item);
          })
          .catch(err => {
            // If Error Occurred, return SelectedVehicleSymbolsForVehicle with setted up required values based on selected plans
            console.log(err);
            this.symbolsService.setSymbolsUpdatingInProgress(item.vehicle, false);
            item.selectedVehicleSymbols = this.symbolsService.manageSelectedVehicleSymbolsRequiredFieldsBasedOnSelectedPlans(item.selectedVehicleSymbols, this.selectedQuotePlansList, item.vehicle);
            resolve(item)
          })
      })

      promises.push(tmpPromise);
    });

    return Promise.all(promises);
  }

  // Helpers
  // ----------------------------------------------------------------------------
  private unsubscribeAllSubscriptionsInArray(subscriptions: ISubscription[]): void {
    if (subscriptions && subscriptions.length) {
      subscriptions.forEach(sub => {
        sub && 'unsubscribe' in sub && sub.unsubscribe();
      });

      subscriptions = [];
    }
  }

  // External methods (Currently not in use)
  // ---------------------------------------------------------------------------
  public requiredVehicleBodyStyle(vehicleResourceId: string): boolean {
    let symbolsForVehicle: SelectedVehicleSymbolsForVehicle;

    if (vehicleResourceId) {
      symbolsForVehicle = this.selectedVehicleSymbolsForVehicle
        .find((item: SelectedVehicleSymbolsForVehicle) => item.vehicle.resourceId === vehicleResourceId);
    }

    if (symbolsForVehicle) {
      return symbolsForVehicle.selectedVehicleSymbols.vrgColl.required
        || symbolsForVehicle.selectedVehicleSymbols.vrgComp.required;
    }

    return false;
  }

}
