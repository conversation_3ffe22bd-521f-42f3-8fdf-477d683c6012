import { ApiResponse } from './_common';

export class LocationDataCollectionApiResponseData extends ApiResponse<LocationData> { }

export class LocationData {
  constructor(
    public meta = {href: <string>'', rel: <any[]>[]},
    public addressType: string = null,
    public address1: string = null,
    public address2: string = null,
    public city: string = null,
    public state: string = null,
    public zip: string = null,
    public fireDistrict: string = null,
    public residencyDate?: string,
    public quoteSessionId: string = null,
    public resourceId: string = null,
    public parentId: string = null,
    public resourceName: string = 'Location'
  ) {}
}
