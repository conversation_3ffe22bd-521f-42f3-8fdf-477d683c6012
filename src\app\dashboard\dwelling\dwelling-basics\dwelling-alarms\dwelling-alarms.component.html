<section class="section section--compact u-spacing--2-5">
  <div class="row">
    <div class="col-xs-12">
      <h1 class="o-heading">Alarms</h1>
    </div>
  </div>

  <div class="row u-spacing--1">
    <div class="col-xs-12">
      <div class="box box--silver">
        <div class="row o-columns">
          <div class="col-xs-6">
            <table class="form-table">

              <tr class="form-table__row">
                <td colspan="2" class="form-table__cell">
                  <label class="o-checkable">
                    <input #refFireLocalInd type="checkbox" name="fireLocalInd" id="fireLocalInd" [(ngModel)]="dwellingProtectionDevices['fireLocalInd']" (ngModelChange)="updateProtectionDevices('fireLocalInd', $event)">
                    <i class="o-btn o-btn--checkbox"></i>
                    Local Fire Alarm or Smoke Detector
                  </label>
                </td>
              </tr>

              <tr class="form-table__row">
                <td colspan="2" class="form-table__cell">
                  <label class="o-checkable">
                    <input [(ngModel)]="dwellingProtectionDevices['burglarLocalInd']" (ngModelChange)="updateProtectionDevices('burglarLocalInd', $event)" #refBurglarLocalInd type="checkbox" name="burglarLocalInd" id="burglarLocalInd">
                    <i class="o-btn o-btn--checkbox"></i>
                    Local Burglar Alarm
                  </label>
                </td>
              </tr>

              <tr class="form-table__row">
                <td colspan="2" class="form-table__cell">
                  <label class="o-checkable">
                    <input [(ngModel)]="dwellingProtectionDevices['deadboltInd']" (ngModelChange)="updateProtectionDevices('deadboltInd', $event)" #refDeadboltInd type="checkbox" name="deadboltInd" id="deadboltInd">
                    <i class="o-btn o-btn--checkbox"></i>
                    Deadbolt
                  </label>
                </td>
              </tr>

              <tr class="form-table__row">
                <td colspan="2" class="form-table__cell">
                  <label class="o-checkable">
                    <input [(ngModel)]="dwellingProtectionDevices['fireExtinguisherInd']" (ngModelChange)="updateProtectionDevices('fireExtinguisherInd', $event)" #refFireExtinguisherInd type="checkbox" name="fireExtinguisherInd" id="fireExtinguisherInd">
                    <i class="o-btn o-btn--checkbox"></i>
                    Fire Extinguisher
                  </label>
                </td>
              </tr>

              <tr class="form-table__row">
                <td colspan="2" class="form-table__cell">
                  <label class="o-checkable">
                    <input [(ngModel)]="dwellingProtectionDevices['burglarCentralStationInd']" (ngModelChange)="updateProtectionDevices('burglarCentralStationInd', $event)" #refburglarCentralStationInd type="checkbox" name="burglarCentralStationInd" id="burglarCentralStationInd">
                    <i class="o-btn o-btn--checkbox"></i>
                    Central Station Reporting Burglar
                  </label>
                </td>
              </tr>

              <tr class="form-table__row">
                <td colspan="2" class="form-table__cell">
                  <label class="o-checkable">
                    <input [(ngModel)]="dwellingProtectionDevices['fireCentralStationInd']" (ngModelChange)="updateProtectionDevices('fireCentralStationInd', $event)" #refFireCentralStationInd type="checkbox" name="fireCentralStationInd" id="fireCentralStationInd">
                    <i class="o-btn o-btn--checkbox"></i>
                    Central Station Reporting Fire
                  </label>
                </td>
              </tr>

            </table>
          </div>
          <div class="col-xs-6">
            <table class="form-table">

              <tr class="form-table__row">
                <td colspan="2" class="form-table__cell">
                  <label class="o-checkable">
                    <input [(ngModel)]="dwellingProtectionDevices['burglarDirectInd']" (ngModelChange)="updateProtectionDevices('burglarDirectInd', $event)" #refburglarDirectInd type="checkbox" name="burglarDirectInd" id="burglarDirectInd">
                    <i class="o-btn o-btn--checkbox"></i>
                    Police Station Reporting Burglar
                  </label>
                </td>
              </tr>

              <tr class="form-table__row">
                <td colspan="2" class="form-table__cell">
                  <label class="o-checkable">
                    <input [(ngModel)]="dwellingProtectionDevices['fireDirectInd']" (ngModelChange)="updateProtectionDevices('fireDirectInd', $event)" #reffireDirectInd type="checkbox" name="fireDirectInd" id="fireDirectInd">
                    <i class="o-btn o-btn--checkbox"></i>
                    Fire Department Reporting Fire
                  </label>
                </td>
              </tr>

              <tr class="form-table__row">
                <td colspan="2" class="form-table__cell">
                  <label class="o-checkable">
                    <input [(ngModel)]="dwellingProtectionDevices['sprinklerAllAreaInd']" (ngModelChange)="updateProtectionDevices('sprinklerAllAreaInd', $event)" #refSprinklerAllAreaInd type="checkbox" name="sprinklerAllAreaInd" id="sprinklerAllAreaInd">
                    <i class="o-btn o-btn--checkbox"></i>
                    Sprinklers in All Area
                  </label>
                </td>
              </tr>

              <tr class="form-table__row">
                <td colspan="2" class="form-table__cell">
                  <label class="o-checkable">
                    <input [(ngModel)]="dwellingProtectionDevices['sprinklerExceptDetectorAreaInd']" (ngModelChange)="updateProtectionDevices('sprinklerExceptDetectorAreaInd', $event)" #refSprinklerExceptDetectorAreaInd type="checkbox" name="sprinklerExceptDetectorAreaInd" id="sprinklerExceptDetectorAreaInd">
                    <i class="o-btn o-btn--checkbox"></i>
                    Sprinklers Except where Fire Detector Present
                  </label>
                </td>
              </tr>

              <tr class="form-table__row">
                <td colspan="2" class="form-table__cell">
                  <label class="o-checkable">
                    <input [(ngModel)]="dwellingProtectionDevices['guardedCommunityInd']" (ngModelChange)="updateProtectionDevices('guardedCommunityInd', $event)" #refGuardedCommunityInd type="checkbox" name="guardedCommunityInd" id="guardedCommunityInd">
                    <i class="o-btn o-btn--checkbox"></i>
                    Guarded Community
                  </label>
                </td>
              </tr>

              <tr class="form-table__row">
                <td colspan="2" class="form-table__cell">
                  <label class="o-checkable">
                    <input [(ngModel)]="dwellingProtectionDevices['voiceDialerInd']" (ngModelChange)="updateProtectionDevices('voiceDialerInd', $event)" #refVoiceDialerInd type="checkbox" name="voiceDialerInd" id="voiceDialerInd">
                    <i class="o-btn o-btn--checkbox"></i>
                    Voice Dialer
                  </label>
                </td>
              </tr>

            </table>

          </div>
        </div>
      </div>
    </div>
  </div>

</section>
