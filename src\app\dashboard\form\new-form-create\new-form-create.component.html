<ng-container *ngIf="showChooseFormQuestion">
  <h2 class="u-t-size--1-5rem u-color-sunset u-align-left">New Form</h2>
  <p class="u-spacing--1-5 u-spacing--bottom-2 u-align-left u-color-sun-juan">
      Do you want to use this quote to prefill the form?
  </p>
  <hr>
  <div class="row u-spacing--2">
    <div class="col-xs-12 u-align-right">
      <button type="button" class="o-btn u-spacing--right-1" (click)="actionChooseFormYes()">Yes</button>
      <button type="button" class="o-btn o-btn--outlined" (click)="actionChooseFormNo()">No</button>
    </div>
  </div>
</ng-container>

<ng-container *ngIf="showQuoteForm">
  <ng-container *ngIf="!allowCreateForm">
    <h2 class="u-t-size--1-5rem u-color-sunset u-align-left">Save Quote?</h2>
    <p class="u-spacing--1-5 u-spacing--bottom-2 u-align-left u-color-sun-juan">
        In order to prefill the form with quote data and to save the form, your
        quote will need to be saved. Do you want to save your quote before proceeding?
    </p>
    <hr>
    <div class="row u-spacing--2">
      <div class="col-xs-12 u-align-right">
        <button type="button" class="o-btn u-spacing--right-1" (click)="actionSaveQuoteYes()">Yes, Save Quote</button>
        <button type="button" class="o-btn o-btn--outlined u-spacing--right-1" (click)="actionSaveQuoteNo()">Don't Save</button>
        <button type="button" class="o-btn o-btn--idle" (click)="actionSaveQuoteCancel()">Cancel</button>
      </div>
    </div>
  </ng-container>

  <!--
  <app-new-form-create-for-quote
    *ngIf="allowCreateForm"
    [verifyIfQuoteIsSaved]="false"
    (createForm)="emitCreateForm($event)"
    (closeForm)="emitCancel($event)"
    (formsLoadStart)="emitFormsLoadStart()"
    (formsLoadEnd)="emitFormsLoadEnd()">
  </app-new-form-create-for-quote>
  -->

  <app-new-form-create-for-quote-with-form-select-dropdown
    *ngIf="allowCreateForm"
    [verifyIfQuoteIsSaved]="false"
    (createForm)="emitCreateForm($event)"
    (closeForm)="emitCancel($event)"
    (formsLoadStart)="emitFormsLoadStart()"
    (formsLoadEnd)="emitFormsLoadEnd()">
  </app-new-form-create-for-quote-with-form-select-dropdown>
</ng-container>

<ng-container *ngIf="showNewForm" >
  <app-new-form-create-new
    class="u-color-sun-juan"
    (cancelForm)="emitCancel($event)"
    (createForm)="emitCreateForm($event)"
    (formsLoadStart)="emitFormsLoadStart()"
    (formsLoadEnd)="emitFormsLoadEnd()">
  </app-new-form-create-new>
</ng-container>
