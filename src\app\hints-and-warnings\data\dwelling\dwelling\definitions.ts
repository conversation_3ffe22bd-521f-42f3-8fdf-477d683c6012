import { AdditionalDataHomeDwelling } from './../../../model/warnings';
import { Quote } from 'app/app-model/quote';
import { WARNING_GROUPS, WarningDefinitionI, AdditionalDataI} from 'app/hints-and-warnings/model/warnings';
import { Dwelling, DwellingLocation } from 'app/app-model/dwelling';
import { Validate} from 'app/hints-and-warnings/validators'
import { differenceInYears, parseISO } from 'date-fns';


function requiredField(value):boolean {
  return Validate.isEmptyValue(value);
}

function validateZipCode(zip): boolean {
  if (zip === '' || zip === null) {
    return false;
  }

  const zipPattern = /^\d{5}(?:-?\d{4})?$/;
  return !zipPattern.test(zip);
}

function generateViewUrl(fullObj:DwellingLocation|Dwelling):string {
  return  '/dashboard/dwelling/quotes/' + fullObj.quoteSessionId + '/dwelling';
}

/**
 * Validation for Dwelling Data
 * For Model: Dwelling Location
 */

const dwellingCity:WarningDefinitionI = {
  id: 'city',
  deepId: 'city',
  viewUri: generateViewUrl,
  viewFieldId: 'dwellingCity',
  warnings: [{
    label: (value, fullObj) => 'Required Dwelling City.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}

const dwellingMFDCarriers: string[] =[] ;
const dwellingFireDistrct: WarningDefinitionI = {
  id: 'fireDistrict',
  deepId: 'fireDistrict',
  viewUri: generateViewUrl,
  viewFieldId: 'fireDistricts',
  warnings: [{
    label: (value, fullObj) => 'Required Fire District to be set.',
    condition: (value, fullObj, additionalData: AdditionalDataHomeDwelling) => {

      const valueToCheck = (value === 'Unassigned') ? null : value;
      const dwellingMFDCarriersValues = additionalData.quoteSelectedPlans.filter(x=>x.ratingFlow.toUpperCase() === "MFD").map(y=>y.ratingPlanId);
      for (var i=0;i<dwellingMFDCarriersValues.length; i++)
      {
        dwellingMFDCarriers.push(dwellingMFDCarriersValues[i]);
      }
      return Validate.isRequiredForSelectedPlansIfEmptyValue(
        valueToCheck,
        dwellingMFDCarriersValues,
        additionalData.quoteSelectedPlansIds
      );
    },
    group: WARNING_GROUPS.carrier,
    carriers: dwellingMFDCarriers
  },
]
};

const dwellingZip:WarningDefinitionI = {
  id: 'zip',
  deepId: 'zip',
  viewUri: generateViewUrl,
  viewFieldId: 'dwellingZip',
  warnings: [{
    label: (value, fullObj) => 'Required Dwelling Zip Code.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  },
  {
    label: (value, fullObj) => 'Zip Code is not Valid.',
    condition: validateZipCode,
    group: WARNING_GROUPS.general,
    carriers: []
  }
]
}

/*******************************************************************************
 * For Model: Dwelling
 ******************************************************************************/


// Rule 1:: (https://bostonsoftware.atlassian.net/browse/SPRC-365) (https://app.smartsheet.com/b/home?lx=wrx37F1hxf1XQrMBnVN7QA)
// For Plans: Barnstable County Mutual  - Preferred [94], Barnstable County Mutual - Protector Plus [69]
// Condition:
// H03 form type - Minimum Year Built accepted >=1987
// H05 form type - Minimum Year Built accepted >=1987
const validateConstructionYearRule1FormTypes = ['HO3', 'HO5'];
const validateConstructionYearRule1Carriers = ['94', '69'];
function validateConstructionYearRule1(dwelling: Dwelling, selectedPlansIds: string[], selectedFormTypes: string []): boolean {
  const requiredForFormTypes = validateConstructionYearRule1FormTypes;
  const requiredForPlans = validateConstructionYearRule1Carriers;
  let isInvalid = false;

  let allowValidationForFormTypes = requiredForFormTypes.some(item => selectedFormTypes.indexOf(item) != -1);
  let allowValidationForSelectedPlans = Validate.isRequiredForSelectedPlans(requiredForPlans, selectedPlansIds);

  if (allowValidationForFormTypes && allowValidationForSelectedPlans) {
    if (dwelling.constructionYear) {
      let tmpConstructionYear = Number(dwelling.constructionYear);
      isInvalid = (!isNaN(tmpConstructionYear) && (tmpConstructionYear < 1987));
      // console.log('The Year Built can not be lower than 1987');
    }
  }

  return isInvalid;
}


// Rule 2:: (https://bostonsoftware.atlassian.net/browse/SPRC-365) (https://app.smartsheet.com/b/home?lx=wrx37F1hxf1XQrMBnVN7QA)
// For Plans: Barnstable County Mutual  - Preferred [94], Barnstable County Mutual - Protector Plus [69]
// Condition:
// Construction Year <=30 Years from Quote effective date
const validateConstructionYearRule2Carriers = ['94', '69'];
function validateConstructionYearRule2(dwelling: Dwelling, selectedPlansIds: string[], selectedFormTypes: string [], quote: Quote): boolean {
  const requiredForPlans = validateConstructionYearRule2Carriers;
  let isInvalid = false;

  let allowValidationForSelectedPlans = Validate.isRequiredForSelectedPlans(requiredForPlans, selectedPlansIds);
  if (allowValidationForSelectedPlans) {
    const tmpDifference = differenceInYears(
  parseISO(this.quote.effectiveDate),
  new Date(dwelling.constructionYear, 0, 1)
);
    if (tmpDifference > 30) {
      isInvalid = true;
      // console.log('The Year Built difference between Quote Effective Date can not be more than 30 years');
    }
  }

  return isInvalid;
}


// Rule 3:: (https://bostonsoftware.atlassian.net/browse/SPRC-365) (https://app.smartsheet.com/b/home?lx=wrx37F1hxf1XQrMBnVN7QA)
// For Plans: Tower National Insurance [77]
// Condition:
// Construction Year > 47 Yrs not accepted
const validateConstructionYearRule3Carriers = ['77'];
function validateConstructionYearRule3(dwelling: Dwelling, selectedPlansIds: string[], selectedFormTypes: string [], quote: Quote): boolean {
  const requiredForPlans = validateConstructionYearRule3Carriers;
  let isInvalid = false;

  let allowValidationForSelectedPlans = Validate.isRequiredForSelectedPlans(requiredForPlans, selectedPlansIds);
  if (allowValidationForSelectedPlans) {
    const tmpDifference = differenceInYears(
  new Date(),
  new Date(dwelling.constructionYear, 0, 1)
);
    if (tmpDifference > 47) {
      isInvalid = true;
      // console.log('The Year Built can not be older than 47 years from now.')
    }
  }

  return isInvalid;
}


// Rule 4:: (https://bostonsoftware.atlassian.net/browse/SPRC-365) (https://app.smartsheet.com/b/home?lx=wrx37F1hxf1XQrMBnVN7QA)
// For Plans: Tower National Preferred [78]
// Condition:
// Construction Year > 47 Yrs not accepted
const validateConstructionYearRule4Carriers = ['78'];
function validateConstructionYearRule4(dwelling: Dwelling, selectedPlansIds: string[], selectedFormTypes: string [], quote: Quote): boolean {
  const requiredForPlans = validateConstructionYearRule4Carriers;
  let isInvalid = false;

  let allowValidationForSelectedPlans = Validate.isRequiredForSelectedPlans(requiredForPlans, selectedPlansIds);
  if (allowValidationForSelectedPlans) {
    const tmpDifference = differenceInYears(
  new Date(),
  new Date(dwelling.constructionYear, 0, 1)
);
    if (tmpDifference > 70) {
      isInvalid = true;
      // console.log('The Year Built can not be older than 70 years from now.')
    }
  }

  return isInvalid;
}


// Rule 5:: (https://bostonsoftware.atlassian.net/browse/SPRC-365) (https://app.smartsheet.com/b/home?lx=wrx37F1hxf1XQrMBnVN7QA)
// For Plans: Yankee Risk solutions - Preferred [99]
// Condition:
// Dwell age >30 years not accepted
const validateConstructionYearRule5Carriers = ['99'];
function validateConstructionYearRule5(dwelling: Dwelling, selectedPlansIds: string[], selectedFormTypes: string [], quote: Quote): boolean {
  const requiredForPlans = validateConstructionYearRule5Carriers;
  let isInvalid = false;

  let allowValidationForSelectedPlans = Validate.isRequiredForSelectedPlans(requiredForPlans, selectedPlansIds);
  if (allowValidationForSelectedPlans) {
    const tmpDifference = differenceInYears(
  new Date(),
  new Date(dwelling.constructionYear, 0, 1)
);
    if (tmpDifference > 30) {
      isInvalid = true;
      // console.log('The Year Built can not be older than 30 years from now.')
    }
  }

  return isInvalid;
}



const dwellingConstructionMaterial:WarningDefinitionI = {
  id: 'constructionMaterialTypeCode',
  deepId: 'constructionMaterialTypeCode',
  viewUri: generateViewUrl,
  viewFieldId: 'constructionMaterial',
  warnings: [{
    label: (value, fullObj) => 'Required Dwelling Construction Material.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const dwellingConstructionYear:WarningDefinitionI = {
  id: 'constructionYear',
  deepId: 'constructionYear',
  viewUri: generateViewUrl,
  viewFieldId: 'constructionYear',
  warnings: [{
    label: (value, fullObj) => 'Required Dwelling Construction Year.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  },
  // {
  //   label: (value, fullObj) => 'Dwelling is too old for this form type.',
  //   condition: (value, fullObj: Dwelling, additionalData: AdditionalDataHomeDwelling) => {
  //     return validateConstructionYearRule1(fullObj, additionalData.quoteSelectedPlansIds, additionalData.quoteFormTypes)
  //   },
  //   group: WARNING_GROUPS.carrier,
  //   carriers: validateConstructionYearRule1Carriers
  // },
  {
    label: (value, fullObj) => 'Dwelling is too old for this form type.',
    condition: (value, fullObj: Dwelling, additionalData: AdditionalDataHomeDwelling) => {
      return validateConstructionYearRule2(fullObj, additionalData.quoteSelectedPlansIds, additionalData.quoteFormTypes, additionalData.quote);
    },
    group: WARNING_GROUPS.carrier,
    carriers: validateConstructionYearRule2Carriers
  },
  {
    label: (value, fullObj) => 'Dwelling is too old for this form type.',
    condition: (value, fullObj: Dwelling, additionalData: AdditionalDataHomeDwelling) => {
      return validateConstructionYearRule3(fullObj, additionalData.quoteSelectedPlansIds, additionalData.quoteFormTypes, additionalData.quote);
    },
    group: WARNING_GROUPS.carrier,
    carriers: validateConstructionYearRule3Carriers
  },
  {
    label: (value, fullObj) => 'Dwelling is too old for this form type.',
    condition: (value, fullObj: Dwelling, additionalData: AdditionalDataHomeDwelling) => {
      return validateConstructionYearRule4(fullObj, additionalData.quoteSelectedPlansIds, additionalData.quoteFormTypes, additionalData.quote);
    },
    group: WARNING_GROUPS.carrier,
    carriers: validateConstructionYearRule4Carriers
  },
  {
    label: (value, fullObj) => 'Dwelling is too old for this form type.',
    condition: (value, fullObj: Dwelling, additionalData: AdditionalDataHomeDwelling) => {
      return validateConstructionYearRule5(fullObj, additionalData.quoteSelectedPlansIds, additionalData.quoteFormTypes, additionalData.quote);
    },
    group: WARNING_GROUPS.carrier,
    carriers: validateConstructionYearRule5Carriers
  }]
};

const dwellingUtilizationTypeCode:WarningDefinitionI = {
  id: 'utilizationTypeCode',
  deepId: 'utilizationTypeCode',
  viewUri: generateViewUrl,
  viewFieldId: 'utilizationTypeCode',
  warnings: [{
    label: (value, fullObj) => 'Required Dwelling Occupancy.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const dwellingFamillies:WarningDefinitionI = {
  id: 'familiesCount',
  deepId: 'familiesCount',
  viewUri: generateViewUrl,
  viewFieldId: 'families',
  warnings: [{
    label: (value, fullObj) => 'Required Dwelling Families.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

export const WARNINGS_DEFINITIONS_DWELLING_DWELLING_LOCATION: WarningDefinitionI[] = [
  dwellingCity,
  dwellingZip
];
export const WARNINGS_DEFINITIONS_DWELLING_DWELLING_FIRE_LOCATION: WarningDefinitionI[] = [

  dwellingFireDistrct

];

export const WARNINGS_DEFINITIONS_DWELLING_DWELLING: WarningDefinitionI[] = [
  dwellingConstructionMaterial,
  dwellingConstructionYear,
  dwellingUtilizationTypeCode,
  dwellingFamillies
];
