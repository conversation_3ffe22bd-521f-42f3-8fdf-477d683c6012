import { CoverageItemParsed, CoverageItem, Coverage, CoverageItemChild } from 'app/app-model/coverage';
import { expect, expectLastConnectionUrl, DataCustomMatchers, expectLastConnectionPayload } from 'testing/helpers/all';
import { setupMockBackend } from 'testing/setups/mock-backend';
import { MockBackend } from 'testing/setups/mock-backend';
import { data as AUTO_COVERAGES } from 'testing/data/quotes/coverages/auto';
import { data as HOME_COVERAGES } from 'testing/data/quotes/coverages/home';
import { DWELLING_COVERAGES } from 'testing/data/quotes/coverages/dwelling';
import { data as HOME_POLICY_GENERAL_PARSED } from 'testing/data/specs/rating-coverages/HOME/policy-options-general-parsed';
import { HOME_GENERAL_POLICY_OPTIONS } from 'testing/data/specs/rating-coverages/HOME/policy-options-general';
import { inject, TestBed, fakeAsync, tick } from '@angular/core/testing';

import { StorageService } from 'app/shared/services/storage-new.service';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';

import { OptionsService } from './options.service';
import { Helpers } from 'app/utils/helpers';

describe('Service: Options', () => {
  let service: OptionsService;
  let mockBackend: MockBackend;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        OptionsService,
        StorageService,
        StorageGlobalService
      ]
    });

    jasmine.addMatchers(DataCustomMatchers);
  });

  beforeEach(inject([OptionsService], (_service: OptionsService) => {
    service = _service;
  }));

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('when working with coverages data', () => {
    it('allows to create coverages by URI', fakeAsync(() => {
      mockBackend = setupMockBackend(AUTO_COVERAGES.items[0]);

      service.createCoverageByUri('/quotes/quote-id/coverages').subscribe();
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/coverages');
    }));

    it('allows to update coverage by URI', fakeAsync(() => {
      mockBackend = setupMockBackend(AUTO_COVERAGES.items[0]);

      service.updatePoliciesByUri('/quotes/quote-id/coverages/coverage-id', AUTO_COVERAGES.items[0]).subscribe();
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/coverages/coverage-id');
    }));

    it('allows to get coverages', fakeAsync(() => {
      mockBackend = setupMockBackend(AUTO_COVERAGES);

      service.getPolicies('quote-id').subscribe();
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/coverages');
    }));
  });

  describe('when working with parsed coverages', () => {
    it('can convert coverages tree to parsed coverages', () => {
      const parsed = service.parsePoliciesItemsToPoliciesItemsParsed(<CoverageItem[]>HOME_GENERAL_POLICY_OPTIONS.items);
      expect(parsed[0]).toHaveSamePropertiesAs(new CoverageItemParsed());
    });

    it('can convert parsed coverages to coverages tree', () => {
      const items = service.convertPolicyItemParsedArrayToPolicyArray(HOME_POLICY_GENERAL_PARSED);
      expect(items[0]).toHaveSamePropertiesAs(new Coverage());
    });

    it('can set coverage status based on parsed coverages', () => {
      const item = service.setPolicyItemStatusBasedOnPolicyCoverages(
        Helpers.deepClone(HOME_POLICY_GENERAL_PARSED[3]),
        HOME_COVERAGES.items[0].coverages
      );

      expect(item.isActive).toBeTruthy();
    });

    it('can sort child coverages in the correct order', () => {
      const sorted = service.sortPolicyItemParsedChildItems(
        Helpers.deepClone(HOME_POLICY_GENERAL_PARSED[1])
      );

      expect(sorted.values).toEqual([
        jasmine.objectContaining({ questionIndex: 1 }),
        jasmine.objectContaining({ questionIndex: 2 }),
        jasmine.objectContaining({ questionIndex: 3 }),
        jasmine.objectContaining({ questionIndex: 4 }),
        jasmine.objectContaining({ questionIndex: 5 }),
        jasmine.objectContaining({ questionIndex: 6 })
      ]);
    });

    it('can set isNewFromAPI status for coverage if it is not in storage', () => {
      const item = Helpers.deepClone(HOME_POLICY_GENERAL_PARSED[0]);

      service.setPolicyItemParsedIsNewFromAPIValue(item, []);
      expect(item.isNewFromAPI).toBeTruthy();

      service.setPolicyItemParsedIsNewFromAPIValue(item, HOME_POLICY_GENERAL_PARSED);
      expect(item.isNewFromAPI).toBeFalsy();
    });

    it('can set default values for new items', () => {
      const item = Helpers.deepClone(HOME_POLICY_GENERAL_PARSED[3]);
      item.isNewFromAPI = true;

      const withDefaults = service.setDefaultValuesForOptions([item], HOME_COVERAGES.items[0].coverages)[0];
      expect(withDefaults.currentValue).toEqual('10000');
      expect((<CoverageItemChild>withDefaults.values[0]).currentValue).toEqual('10000');

      item.isNewFromAPI = false;
      item.currentValue = '20000';
      (<CoverageItemChild>item.values[0]).currentValue = '20000';

      const noDefaults = service.setDefaultValuesForOptions([item], HOME_COVERAGES.items[0].coverages)[0];
      expect(noDefaults.currentValue).toEqual('20000');
      expect((<CoverageItemChild>noDefaults.values[0]).currentValue).toEqual('20000');
    });
  });

  // this should be in separate helper somewhere...
  describe('when using orderObjectsArrayByProperty helper', () => {
    const DATA = [
      {
        numeric: 3,
        big_numeric: 1234,
        with_string: 'some strung 1',
        with_undef: 'B',
        with_null: 'B',
        boolean: false,
        special: '`~!@#$%^&*()+c'
      },
      {
        numeric: -2,
        big_numeric: 11,
        with_string: 'SOME string',
        with_null: null,
        boolean: true,
        special: ' =[{]}|\\\t\'\nB'
      },
      {
        numeric: 1,
        big_numeric: 1e5,
        with_string: 'some STRING 1',
        with_undef: 'a',
        with_null: 'a',
        boolean: false,
        special: '<,.>?/";:a'
      }
    ];

    it('can sort by numeric properties', () => {
      const sorted = service.orderObjectsArrayByProperty(
        Helpers.deepClone(DATA),
        'numeric'
      );

      expect(sorted).toEqual([
        jasmine.objectContaining({ numeric: -2 }),
        jasmine.objectContaining({ numeric: 1 }),
        jasmine.objectContaining({ numeric: 3 })
      ]);
    });

    it('can sort ignoring case by string properties', () => {
      const sorted = service.orderObjectsArrayByProperty(
        Helpers.deepClone(DATA),
        'with_string'
      );

      expect(sorted).toEqual([
        jasmine.objectContaining({ with_string: 'SOME string' }),
        jasmine.objectContaining({ with_string: 'some STRING 1' }),
        jasmine.objectContaining({ with_string: 'some strung 1' })
      ]);
    });

    it('can handle undefined properties', () => {
      const sorted = service.orderObjectsArrayByProperty(
        Helpers.deepClone(DATA),
        'with_undef'
      );

      expect(sorted).toEqual([
        jasmine.objectContaining({ with_undef: 'a' }),
        jasmine.objectContaining({ with_undef: 'B' }),
        jasmine.anything()  // undefined
      ]);
    });

    it('can handle null properties', () => {
      const sorted = service.orderObjectsArrayByProperty(
        Helpers.deepClone(DATA),
        'with_null'
      );

      expect(sorted).toEqual([
        jasmine.objectContaining({ with_null: 'a' }),
        jasmine.objectContaining({ with_null: 'B' }),
        jasmine.objectContaining({ with_null: null })
      ]);
    });

    it('can handle boolean properties', () => {
      const sorted = service.orderObjectsArrayByProperty(
        Helpers.deepClone(DATA),
        'boolean'
      );

      expect(sorted).toEqual([
        jasmine.objectContaining({ boolean: false, numeric: 3 }),
        jasmine.objectContaining({ boolean: false, numeric: 1 }),
        jasmine.objectContaining({ boolean: true })
      ]);
    });

    it('ignores special characters in string properties by default', () => {
      const sorted = service.orderObjectsArrayByProperty(
        Helpers.deepClone(DATA),
        'special'
      );

      expect(sorted).toEqual([
        jasmine.objectContaining({ special: '`~!@#$%^&*()+c' }),
        jasmine.objectContaining({ special: '<,.>?/";:a' }),
        jasmine.objectContaining({ special: ' =[{]}|\\\t\'\nB' }),
        // jasmine.objectContaining({ special: '`~!@#$%^&*()+c' })
      ]);
    });

    it('can be set not to ignore special characters in string properties', () => {
      const sorted = service.orderObjectsArrayByProperty(
        Helpers.deepClone(DATA),
        'special',
        false
      );

      expect(sorted).toEqual([
        jasmine.objectContaining({ special: ' =[{]}|\\\t\'\nB' }),
        jasmine.objectContaining({ special: '<,.>?/";:a' }),
        jasmine.objectContaining({ special: '`~!@#$%^&*()+c' })
      ]);
    });

    it('can handle numeric values of various magnitudes', () => {
      const sorted = service.orderObjectsArrayByProperty(
        Helpers.deepClone(DATA),
        'big_numeric'
      );

      expect(sorted).toEqual([
        jasmine.objectContaining({ big_numeric: 11 }),
        jasmine.objectContaining({ big_numeric: 1234 }),
        jasmine.objectContaining({ big_numeric: 1e5 })
      ]);
    });
  });

  describe('when working with Dwelling coverages', () => {
    // TODO:: Fix test
    xit('allows to retrieve dwelling coverages by URI and save them in storage', inject(
      [StorageService], (storageService: StorageService) => {
        mockBackend = setupMockBackend(DWELLING_COVERAGES);
        spyOn(storageService, 'setStorageData');

        service.getDwellingOptions$('/quotes/quote-id/options').subscribe();

        expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/options');
        expect(storageService.setStorageData).toHaveBeenCalledWith('dwellingQuoteOptions', DWELLING_COVERAGES);
      }));

    it('ensures default coverages cannot be accidentally modified', () => {
      expect(() => {
        service.defaultValuesDataSource.coverages.push(new Coverage());
      }).toThrow();

      expect(() => {
        service.defaultValuesDataSource.coverages[0].values.push({ value: '123' });
      }).toThrow();

      expect(() => {
        service.defaultValuesDataSource.coverages[0].coverageDescription = 'New description';
      }).toThrow();
    });

    it('allows to set default value on default coverages other than one with code FRV', () => {
      const withDefaults = service.setDefaultsToOptionsOtherThanDP1();

      withDefaults.coverages.forEach((item: Coverage) => {
        if (item.coverageCode !== 'FRV') {
          expect(item.values).toEqual([{ value: 'true' }]);
        }
      });
    });

    describe('when creating new coverage with default options by URI', () => {
      beforeEach(() => {
        mockBackend = setupMockBackend(DWELLING_COVERAGES.items[0]);
      });

      it('uses defaultValuesDataSource for unknown form type', () => {
        service.createDwellingOptionsByUriWithDefaultValues('/quotes/quote-id/coverages').subscribe();
        expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/coverages');
        expectLastConnectionPayload(mockBackend).toEqual(service.defaultValuesDataSource);  // unknown form type == do not set defaults
      });

      it('uses default values for form type other than DP1', () => {
        const withDefaults = service.setDefaultsToOptionsOtherThanDP1();

        service.createDwellingOptionsByUriWithDefaultValues('/quotes/quote-id/coverages', 'DP3').subscribe();
        expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/coverages');
        expectLastConnectionPayload(mockBackend).toEqual(withDefaults);
      });

      it('uses defaultValuesDataSource for DP1 form type', () => {
        service.createDwellingOptionsByUriWithDefaultValues('/quotes/quote-id/coverages', 'DP1').subscribe();
        expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/coverages');
        expectLastConnectionPayload(mockBackend).toEqual(service.defaultValuesDataSource);
      });
    });

    it('allows to create coverage data by URI', () => {
      mockBackend = setupMockBackend(DWELLING_COVERAGES.items[0]);

      service.createCoverageByUri('/quotes/quote-id/coverages').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/coverages');
    });

    it('allows to update coverage data by URI', () => {
      mockBackend = setupMockBackend(DWELLING_COVERAGES.items[0]);

      service.updateDwellingOptionByUri('/quotes/quote-id/coverages/coverage-id', DWELLING_COVERAGES.items[0]).subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/coverages/coverage-id');
      expectLastConnectionPayload(mockBackend).toEqual(DWELLING_COVERAGES.items[0]);
    });
  });
});
