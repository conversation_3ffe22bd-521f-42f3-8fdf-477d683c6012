
<mat-horizontal-stepper (selectionChange)="refreshToken();changeOwnerIfTransfer($event)" class="stepper-main">
    <form #form="ngForm" (submit)="submit()">
    <mat-step>
      <h4 class="black">{{splitWords(serviceType.serviceType.transactionType)}}</h4>
      <ng-template matStepLabel>Service Type </ng-template>
      <section class="section section--compact u-spacing--2 5">
           <app-prefill-service-type (ownershipTypeChange)="ownershipTypeChange($event)" (transactionTypeChange)="setTaxExempt($event);setLeaseAndFinance($event)" (vehicleConditionChange)="setCondition($event)" #serviceType></app-prefill-service-type>
           <!-- <a class="skip-form" *ngIf="!serviceType.isDuplicateRegistration() && !isFeatureEnabled('SprApp_SkipToRTA')" (click)="skipToFormWarning.open()">Skip to RTA Form</a> -->
    <div [style.padding-top]="'32px'" class="col-xs-12 u-align-right u-remove-letter-spacing">
      <button class="o-btn o-btn--idle save" name="save" *ngIf="!renewOrReinstate() && !serviceType.isDuplicateRegistration()" (click)="saveData('RmvServices',true,false)">Save</button>
      <button class="o-btn o-btn--idle save" name="saveForLater" *ngIf="!renewOrReinstate() && !serviceType.isDuplicateRegistration()" (click)="saveData('RmvServices',true,true,true,'/dashboard/rmv-services')">Save and Close</button>
        <button type="button" class="o-btn margin-top" [disabled]="!serviceType.serviceType.transactionType" matStepperNext>Next</button>
        </div>
      </section>
    </mat-step>
    <mat-step>
      <h4 class="black">{{splitWords(serviceType.serviceType.transactionType)}}</h4>
      <ng-template matStepLabel> Vehicle </ng-template>
      <section class="section section--compact u-spacing--2 5">
        <app-prefill-vehicle #vehicleSelection [serviceType]="serviceType.serviceType" [requireVinLookup]="requireVinLookup" (plateChange)="isReportPrinted($event)"></app-prefill-vehicle>
        <!-- <a class="skip-form" *ngIf="!serviceType.isDuplicateRegistration() && !isFeatureEnabled('SprApp_SkipToRTA')" (click)="skipToFormWarning.open()">Skip to RTA Form</a> -->
        <div [style.padding-top]="'32px'" class="col-xs-12 u-align-right u-remove-letter-spacing">
          <button class="o-btn o-btn--idle save" *ngIf="!renewOrReinstate() && !serviceType.isDuplicateRegistration()" (click)="saveData('RmvServices',true,false)">Save</button>
            <button class="o-btn o-btn--idle save" *ngIf="!renewOrReinstate() && !serviceType.isDuplicateRegistration()" (click)="saveData('RmvServices',true,true,true,'/dashboard/rmv-services')">Save and Close</button>
            <button type="button" class="o-btn margin-top" matStepperPrevious>Previous</button>
            <button type="button" *ngIf="!renewOrReinstate() && !serviceType.isDuplicateRegistration()" class="o-btn margin-top margin-left" matStepperNext>Next</button>
            <button type="button" *ngIf="vehicleForm && vehicleForm.checkRenewFeature()" class="o-btn margin-top margin-left" matStepperNext>Next</button>
            <button type="button" *ngIf="vehicleForm && !vehicleForm.checkRenewFeature() && renewOrReinstate()" class="o-btn margin-top margin-left" (click)="registrationEasy()"
             [disabled]="!vehicleForm.vehicle.plateNumber || !vehicleForm.vehicle.plateType">Check Eligibility</button>
             <button type="button" *ngIf="serviceType.isDuplicateRegistration()" class="o-btn margin-top margin-left" (click)="duplicateReg()"
             [disabled]="!vehicleForm?.plateLookupDone ||  !vehicleForm.ownerValidation[0]?.isOwnerValidated">Get Reg Details</button>
        </div>
        </section>


    </mat-step>
    <mat-step *ngIf="serviceType.serviceType.transactionType === '' || serviceType.isFieldRequired() || serviceType.isRequiredNewTitle() || serviceType.isRequiredSurvivingSpouseReg() || serviceType.isRequiredTransfer() || serviceType.isRequiredReassign() || serviceType.isRequiredSurvivingSpouse() || vehicleForm && vehicleForm.checkRenewFeature() ">
      <h4 class="black">{{splitWords(serviceType.serviceType.transactionType)}}</h4>
      <ng-template matStepLabel>Owner/Addresses </ng-template>
      <section class="section section--compact u-spacing--2 5">
        <app-prefill-owner #ownerForm [form]="form" [ownership]="serviceType.serviceType.ownership" [transactionType]="serviceType.serviceType.transactionType"></app-prefill-owner>
        <!-- <a class="skip-form" (click)="skipToFormWarning.open()" *ngIf="!isFeatureEnabled('SprApp_SkipToRTA')">Skip to RTA Form</a> -->
        <div class="col-xs-12 u-align-right u-remove-letter-spacing" [style.padding-top]="'32px'">
            <button class="o-btn o-btn--idle margin-top margin-right" name="save" *ngIf="vehicleForm && !vehicleForm.checkRenewFeature()" (click)="saveData('RmvServices',true,false)"  name="save">Save</button>
            <button class="o-btn o-btn--idle margin-top margin-right" name="saveForLater" *ngIf="vehicleForm && !vehicleForm.checkRenewFeature()" (click)="saveData('RmvServices',true,true,true,'/dashboard/rmv-services')"  name="saveForLater">Save and Close</button>
            <button type="button" class="o-btn margin-top" matStepperPrevious>Previous</button>
            <button type="button" *ngIf="!serviceType.isDuplicateRegistration() && vehicleForm && !vehicleForm.checkRenewFeature()" class="o-btn margin-top margin-left" [disabled]="!serviceType.serviceType.transactionType" matStepperNext>Next</button>
            <button type="button" *ngIf="vehicleForm && vehicleForm.checkRenewFeature()" class="o-btn margin-top margin-left" (click)="registrationEasy()"
             [disabled]="!vehicleForm.vehicle.plateNumber || !vehicleForm.vehicle.plateType">Check Eligibility</button>

            </div>
        </section>
    </mat-step>
    <mat-step *ngIf="serviceType.serviceType.transactionType === '' || serviceType.isFieldRequired() || serviceType.serviceType.transactionType === 'PrefillRegistrationTransfer'
    || serviceType.serviceType.transactionType === 'PrefillReassign' || serviceType.isRequiredNewTitle() || serviceType.isRequiredSurvivingSpouseReg() ">
      <h4 class="black">{{splitWords(serviceType.serviceType.transactionType)}}</h4>
      <ng-template matStepLabel>
        Purchase
      </ng-template>
      <section class="section section--compact u-spacing--2 5">
      <app-prefill-purchase #purchaseForm [vehicle]="vehicleForm?.vehicle" [type]="serviceType.serviceType.transactionType" [purchaseType]="serviceType.serviceType.purchaseType"></app-prefill-purchase>
      <!-- <a class="skip-form" (click)="skipToFormWarning.open()" *ngIf="!isFeatureEnabled('SprApp_SkipToRTA')">Skip to RTA Form</a> -->
    <div class="col-xs-12 u-align-right u-remove-letter-spacing" [style.padding-top]="'32px'">
      <button class="o-btn o-btn--idle margin-top margin-right" (click)="saveData('RmvServices',true,false)" name="save">Save</button>
        <button class="o-btn o-btn--idle margin-top margin-right" (click)="saveData('RmvServices',true,true,true,'/dashboard/rmv-services')" name="saveForLater">Save and Close</button>
        <button type="button" class="o-btn margin-top" matStepperPrevious>Previous</button>
        <button type="button" class="o-btn margin-top margin-left" [disabled]="!serviceType.serviceType.transactionType" matStepperNext>Next</button>
        </div>
      </section>
    </mat-step>
    <mat-step *ngIf="!renewOrReinstate() && !serviceType.isDuplicateRegistration()">
      <h4 class="black">{{splitWords(serviceType.serviceType.transactionType)}}</h4>
      <ng-template matStepLabel>
        Insurance
      </ng-template>
      <section class="section section--compact u-spacing--2 5">
      <app-prefill-insurance #insuranceForm [transactionType]="serviceType.serviceType.transactionType"></app-prefill-insurance>
       <a class="skip-form" (click)="skipToFormWarning.open()">Skip to RTA Form</a>
    <div [style.padding-top]="!isGetReady(serviceType.serviceType.transactionType,true) ? '32px': null" class="col-xs-12 u-align-right u-remove-letter-spacing">
      <button class="o-btn o-btn--idle margin-top margin-right" (click)="saveData('RmvServices',true,false)" name="save">Save</button>
      <button class="o-btn o-btn--idle margin-top margin-right" (click)="saveData('RmvServices',true,true,true,'/dashboard/rmv-services')" name="saveForLater">Save and Close</button>
      <button type="button" class="o-btn margin-top" matStepperPrevious>Previous</button>
        <button *ngIf="isGetReady(serviceType.serviceType.transactionType,true)" type="submit" class="o-btn margin-top margin-left" (click)="submit()">Check Rmv Eligibility</button>
        <button *ngIf="!isGetReady(serviceType.serviceType.transactionType,true)" type="button" class="o-btn margin-top margin-left" (click)="skipToForm()">Go To RTA Form</button>
      </div>
      </section>
    </mat-step>
    </form>
  </mat-horizontal-stepper>
  <div class="section section--compact u-spacing--2 5 u-remove-letter-spacing padding-top">
    <button class="o-btn" (click)="canDeactivate(true,true)">RETURN TO RMV DASHBOARD</button>
</div>

<!-- <app-modalbox #registrationSuccess>
  <app-aside-registration-response *ngIf="regData"[data]="regData" [id]="savedId" [vehicle]="vehicleForm.vehicle" [owners]="vehicleForm.owners" [originalVehicle]="vehicleForm.transferVehicleResponse.vehicle" [ownerValidation]="getOwnerValidation()" [transaction]="transactionType" [plateNumber]="vehicleForm.vehicle.plateNumber" [modalbox]="registrationSuccess" [fromRmvs]="true"></app-aside-registration-response>
</app-modalbox> -->


  <app-modalbox #rmvResponseModal>
    <app-rmv-response-modal #response (plateInquiryClicked)="isReportPrinted($event)" [showImage]="showImage" [isGetReadyEligible]="IsGetReadyEligible" [evrLiteMessages]="evrLiteMessages" [isEvrEligible]="IsEvrLiteEligible" [messages]="messages" [notifications]="notifications" [formCreateModel]="createFormModel" [getReadyModel]="getReadyRequest"
      [modalbox]="rmvResponseModal"></app-rmv-response-modal>
      <div class="row u-spacing--1-5">
        <div class="col-xs-12 u-align-right u-remove-letter-spacing">
            <button type="button" class="o-btn" *ngIf="!IsGetReadyEligible && !IsEvrLiteEligible" (click)="response.goToRTA()">GO TO RTA</button>
            <button type="button" *ngIf="checkGetReady()" class="o-btn" name="submitToRmv" id="submitbtn" (click)="submitToRmv()">{{submitText}}</button>
            <button type="button" *ngIf="checkGetReady()" class="o-btn o-btn--idle u-spacing--left-2" (click)="skipToForm()">SKIP TO FORM</button>
            <button type="button" *ngIf="IsEvrLiteEligible && serviceType.serviceType.reassignedPlate !== 'Y'" class="o-btn" name="initiateEvrBtn" [disabled]="isPlateInitiatedNeeded()" (click)="evrLiteProceed.open()">Initiate</button>

          <button type="button" (click)="response.onCancel()" class="o-btn o-btn--idle u-spacing--left-2 margin-left-rem;">Go Back</button>
        </div>
      </div>
  </app-modalbox>
  <app-modalbox #getReadyResponseModal [css]="'u-width-650px'">
    <app-get-ready-success [modalbox]="getReadyResponseModal" [responseData]="getReadyResponse"></app-get-ready-success>
  </app-modalbox>
  <app-modalbox #getReadyErrorModal>
    <img alt="getreadylogo" src="assets/images/common/rmv_getready_logo.png" class="height">
  <br>

              <div class="box box--silver" class="margin-bottom">
                  <p><b>Transaction:</b> {{getReadyResponse?.transactionType}}</p>
                  <p><b>Status:</b>{{getReadyResponse?.resultStatus.status}}</p>
                  <p><b>Please review the following error and try again:</b></p>
                <div *ngFor="let msg of getReadyResponse?.resultStatus?.message" class="padding-bottom">
                  <p [innerHTML]="msg" class="white-space"></p>
                  </div>
                 </div>
                 <div class="col-xs-12 u-align-right u-remove-letter-spacing">
                  <button type="button" class="o-btn" (click)="response.goToRTA()">GO TO RTA</button>
                <button type="button" (click)="getReadyErrorModal.close()" class="o-btn o-btn--idle u-spacing--left-2 margin-left-rem">Go Back</button>
                <input id="atlasTransactionKey" hidden value="pending" />
              </div>

  </app-modalbox>

  <app-modalbox #evrLiteResponse>
    <app-evr-lite-response (goToGetReady)="converToGetReady()" [id]="rmvServicesRequestId" [modalbox]="evrLiteResponse" [evrValidate]="evrValidateData"></app-evr-lite-response>
  </app-modalbox>

  <app-modalbox #evrLiteErrorModal>
  <br>

              <div class="box box--silver margin-bottom">
                  <p><b>Please review the following error and try again:</b></p>
                <div *ngFor="let msg of evrValidateData?.resultStatus?.message" class="padding-bottom">
                  <p [innerHTML]="msg" class="white-space"></p>
                  @if(evrValidateData?.resultStatus?.message[0].includes('exceed the maximum')){
                    <br/>
                    <p style="font-weight: bold;">If proceeding with GetReady, be sure to return the physical plate to your inventory if you have removed it.</p>
                  }
                  </div>
                 </div>
                 <div class="row">
                 <div class="col-xs-4">
                  <span for="">Atlas Key: </span><span id="atlasTransactionKey">{{evrValidateData?.atlasTransactionKey}}</span>

                  </div>
                 <div class="col-xs-8 u-align-right u-remove-letter-spacing">
                  <button type="button" *ngIf="evrValidateData?.isGetReadyEligible" class="o-btn" (click)="submitToRmv();evrLiteErrorModal.close()">Launch GetReady</button>
                <button type="button" (click)="evrLiteErrorModal.close()" class="o-btn o-btn--idle u-spacing--left-2 margin-left-rem;">Go Back</button>
              </div>
              </div>

  </app-modalbox>

  <app-modalbox #evrLiteProceed [css]="'u-width-690px'">
    <br>

                <div class="box box--silver margin-bottom">
                   <p class="padding-bottom-percent">
                    Please be advised the RMV requires all activity to be completed the same day, otherwise the EVR Transaction will be REJECTED. </p>
                    <p class="padding-bottom-percent">
                    Before proceeding:
                   </p>
                   <div class="padding-bottom-percent">
                    <label class="o-checkable">
                      <input type="checkbox" name="rmv-lookup-checkbox" [(ngModel)]="verifyAcknowledgment.documents" >
                      <i class="o-btn o-btn--checkbox"></i>
                      Verify you have all necessary documents available
                    </label>
                  </div>
                  <div class="padding-bottom-percent">
                    <label class="o-checkable">
                      <input type="checkbox" name="rmv-lookup-checkbox" [(ngModel)]="verifyAcknowledgment.payment" >
                      <i class="o-btn o-btn--checkbox"></i>
                      Verify the customer will be able to pay the processing fee TODAY
                    </label>
                  </div>
                  <div class="padding-bottom-percent">
                    <label class="o-checkable">
                      <input type="checkbox" name="verify-complete-checkbox" [(ngModel)]="verifyAcknowledgment.complete" >
                      <i class="o-btn o-btn--checkbox"></i>
                      Verify you will be able to complete the RMV transaction TODAY upon payment receipt
                    </label>
                  </div>
                   </div>
                   <div class="col-xs-12 u-align-right u-remove-letter-spacing">
                  <button type="button" class="o-btn" [disabled]="!verifyAcknowledgment.documents || !verifyAcknowledgment.payment || !verifyAcknowledgment.complete" name="proceedEvrBtn" (click)="initiateEvrLite();evrLiteProceed.close()">Proceed</button>
                  <button type="button" (click)="evrLiteProceed.close()" class="o-btn o-btn--idle u-spacing--left-2  margin-left-rem;">Go Back</button>
                </div>

    </app-modalbox>

    <app-modalbox #skipToFormWarning>
                <div class="box box--silver margin-bottom">
                    <p>Choosing to exit the RTA Prefill and manually complete an RTA will result in the the inability to utilize Get Ready, and if applicable, EVR , both of which are time saving features for you and your customers.  Are you sure you wish to do this?</p>
                   </div>
                   <div class="col-xs-12 u-align-right u-remove-letter-spacing">
                     <button type="button" (click)="skipToForm()" class="o-btn o-btn--idle">Go To RTA</button>
                     <button type="button" class="o-btn margin-left-rem" (click)="skipToFormWarning.close()">Go Back to Prefill</button>
              </div>

    </app-modalbox>

    <app-modalbox #requiredWarningModal>
      <div class="box box--silver margin-bottom">
        <p>The following fields and/or actions are required:</p>
        <br>
        <ul style="list-style: none;">
          <li *ngFor="let item of requiredItems">{{item}}</li>
        </ul>
       </div>
       <div class="col-xs-12 u-align-right u-remove-letter-spacing">
        <button type="button" class="o-btn" (click)="requiredWarningModal.close()">Ok</button>

    </div>
    </app-modalbox>

    <app-modalbox #inventoryNotFoundModal>
      <div class="box box--silver margin-bottom">
        <p>Available plate is not found in your inventory. Transaction cannot be completed.</p>
       </div>
       <div class="col-xs-12 u-align-right u-remove-letter-spacing">
        <button type="button" class="o-btn" (click)="inventoryNotFoundModal.close()">Ok</button>

    </div>
    </app-modalbox>

    <app-modalbox #leaving>
      <app-leave-transaction #leave [modal]="leaving" (save)="saveData('RmvServices',true,true,true,$event)" (isDeactivatedWithoutSave)="redirect($event)"></app-leave-transaction>
    </app-modalbox>

