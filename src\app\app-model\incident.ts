import { ApiResponse, MetaData } from './_common';
import { Driver } from 'selenium-webdriver/safari';

export class Incident {
  constructor(
    public meta: MetaData = {href: '', rel: ['']},
    public surchargeDate: string = null,
    public typeOfIncident: string = null,
    public amountPaid: string = null,
    public descOfIncident: string = null,
    public quoteSessionId: string = null,
    public resourceId: string = null,
    public resourceName: string = null,
    public parentId: string = null
  ) { }
}

export class IncidentForHintsAndWarnings extends Incident {
  constructor(
    public driver: Driver = null,
    public incidentTypes: IncidentType[] = []
  ) { super() }
}

export class IncidentsResponse extends ApiResponse<Incident> { }

export class IncidentTypeDesc {
  constructor(
    public name: string = null
  ){}
}

export class IncidentType {
  constructor(
    public driverIncidentTypeId: string = null,
    public meta: {href: string} = {href: ''},
    public name: string = null,
    public options: IncidentTypeDesc[] = [],
    public resourceName: string = 'DriverIncident'
  ){}
}

export class IncidentTypeResponse extends ApiResponse<IncidentType> {}
