import { Component, OnInit, Input } from '@angular/core';
import { PurchaseAndSalesInformation, Seller, Address } from '../get-ready.model';
import { STATES } from '../../../app-services/state.service';
import { ControlContainer, NgForm } from '@angular/forms';
import { TAX_EXEMPT_TYPES } from '../../../app-services/get-ready-dropdown';

import { format } from 'date-fns';

@Component({
    selector: 'app-purchase-and-sales',
    templateUrl: './purchase-and-sales.component.html',
    viewProviders: [{ provide: ControlContainer, useExisting: NgForm }],
    styleUrls: ['./purchase-and-sales.component.scss'],
    standalone: false
})
export class PurchaseAndSalesComponent implements OnInit {
  @Input() type;
  sellerAddress: Address = { street: '', unitOrApt: '', city: '', state: '', zip: ''};
  seller: Seller = { businessName: '', firstName: '', lastName: '', address: this.sellerAddress };
purchaseAndSalesInfo: PurchaseAndSalesInformation = { purchaseDate: '', purchaseState: '', nonMASalesTaxPreviouslyPaid: '',
proofOfNoTaxRequired: '', maResidentAtTimeOfPurchase: '', maSalesTaxPreviouslyPaid: '',
 seller: this.seller, taxExempt: '', taxExemptType: '', purchaseType: '', dealerFid: '', totalSalePrice: '', auctionSale: ''};
stateOptions = STATES;
taxEmemptOptions = TAX_EXEMPT_TYPES;
  constructor() { }

  ngOnInit() {
  }


resetModel() {
  this.sellerAddress = { street: '', unitOrApt: '', city: '', state: '', zip: ''};
  this.seller = { businessName: '', firstName: '', lastName: '', address: this.sellerAddress };
  this.purchaseAndSalesInfo = { purchaseDate: '', purchaseState: '', nonMASalesTaxPreviouslyPaid: '',
proofOfNoTaxRequired: '', maResidentAtTimeOfPurchase: '', maSalesTaxPreviouslyPaid: '',
 seller: this.seller, taxExempt: '', taxExemptType: '', purchaseType: '', dealerFid: '', totalSalePrice: '', auctionSale: ''};
}

  setDate($ev) {
    this.purchaseAndSalesInfo.purchaseDate = $ev.formatedDate ? format(new Date($ev.formatedDate), 'yyyyMMdd') : '';
  }

  getReadyEligible() {
    return this.type === 'PrefillNewTitleAndRegistration' ||
    this.type === 'PrefillTitleOnly' || this.type === 'PrefillRegistrationOnly'
    || this.type === 'PrefillRegistrationTransfer';
  }

}
