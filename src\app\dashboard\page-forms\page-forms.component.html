<app-lob-tabbed-nav [navigationList]="splitList" *ngIf="PersonalCommercialSeparate && !isQuoteForms()">
</app-lob-tabbed-nav>
<section *ngIf="isQuoteForms()" class="section">
</section>
<section *ngIf="!isQuoteForms()" class="section">
  <app-search-by-name (filterByName)="filterByName($event)" [lob]="clientType" (resetSearch)="resetSearch()">
  </app-search-by-name>
</section>
<section class="section u-padd--bottom-0" style="padding-top: 0;">
  <div class="row">
    <div *ngIf="!isQuoteForms()" class="col-xs-12">
      <app-filter *ngIf="filterFormsOptions?.length" [id]="'filter_forms'" [name]="'Forms'" [hasSearch]="true"
      [selectedOption]="filterFormsSelectedOption" [options]="filterFormsOptions" [label]="filterFormsLabel" (onChange)="onFilterDataChange($event)"></app-filter>

      <app-filter *ngIf="filterAgentsOptions?.length" [id]="'filter_agents'" [name]="'Agents'" [hasSearch]="true"
      [selectedOption]="filterAgentsSelectedOption" [options]="filterAgentsOptions" [label]="filterAgentsLabel" (onChange)="onFilterDataChange($event)">
      </app-filter>

      <app-filter-dates (getByFilter)="getFormsFromFilteredDates()"></app-filter-dates>

      <app-filter *ngIf="filterLocationsOptions?.length" [id]="'filter_locations'" [name]="'Locations'"
        [hasSearch]="true" [options]="filterLocationsOptions" (onChange)="onFilterDataChange($event)"></app-filter>

      <div class="u-float-right">
        <div class="trash trash--select u-float-left">
          <span class="u-t-upper u-color-slate-grey">
            <label class="o-checkable">
              <input type="checkbox" class="o-btn--checkbox u-show-inline" (click)="toggleAll()"
                [checked]="isChecked()" />
              <i class="tooltip__menu-icon tooltip__menu-icon_inner o-btn o-btn--checkbox "></i>
              Select All
            </label>
          </span>
        </div>
        <div class="trash trash--img u-float-right">
          <a class="u-t-upper u-color-slate-grey" (click)="deleteForms()">Delete</a>
        </div>
      </div>

    </div>
    <div class="col-xs-12" *ngIf="isQuoteForms()">
      <div class="col-xs-5 u-padd--left-0">
        <a #refBtnNewForm class="app-button app-button--save-quote app-button--new-file"
          (click)="openNewFormCreateModal()">New Form</a>
      </div>
    </div>
    <div *ngIf="isQuoteForms()" class="col-xs-12">
      <div class="u-float-right">
        <div class="trash trash--select u-float-left">
          <span class="u-t-upper u-color-slate-grey">
            <label class="o-checkable">
              <input type="checkbox" class="o-btn--checkbox u-show-inline" (click)="toggleAll()"
                [checked]="isChecked()" />
              <i class="tooltip__menu-icon tooltip__menu-icon_inner o-btn o-btn--checkbox "></i>
              Select All
            </label>
          </span>
        </div>
        <div class="trash trash--img u-float-right">
          <a class="u-t-upper u-color-slate-grey" (click)="deleteForms()">Delete</a>
        </div>
      </div>
    </div>
  </div>
</section>
<app-modalbox #refModalNewForm [css]="'u-width-450px'">
  <app-new-form-create *ngIf="refModalNewForm.isOpen" [preventFormTypeChooseIfInQuote]="true"
    (createForm)="actionNewFormCreateFormCreated()" (cancel)="actionNewFormCreateCancel()">
  </app-new-form-create>
</app-modalbox>
<section class="section">
  <div class="row">
    <div class="col-xs-12">

      <table class="table table--compact table--fixed table--hoverable-grey">
        <thead class="table__thead">
          <tr class="">
            <th class="table__th u-width-150px">Name</th>
            <th class="table__th u-width-150px">City &amp; State</th>
            <th class="table__th u-width-110px">Form Type</th>
            <th class="table__th u-width-110px">Saved</th>
            <th class="table__th u-width-115px">Agent</th>
            <th class="table__th u-width-115px">Notes</th>
            <th class="table__th u-width-45px"></th>
          </tr>
        </thead>

        <tbody class="table__tbody" *ngIf="arrAgencyFormsFiltered">
          <tr class="table__tr" *ngFor="let row of arrAgencyFormsFiltered">
            <td class="table__td u-color-pelorous">
              <a (click)="openAgencyForm(row)" title="{{row.formName}}"
                [userSubscription]="{code: 'Forms', warningPopup: refSubscriptionFormInfoPopup}"
                (showPopup)="setMsg($event)">
                <span *ngIf="!row.customerName">--</span><span *ngIf="row.customerName">{{row.customerName}}</span>
              </a>


              <app-modalbox #refSubscriptionFormInfoPopup>
                <div class="box box--silver u-spacing--1-5">
                  <p *ngFor="let message of subscriptionMessages">{{message}}</p>
                </div>
                <div class="row u-spacing--2">
                  <div class="col-xs-12 u-align-right">
                    <button (click)="refSubscriptionFormInfoPopup.closeModalbox()" class="o-btn">OK</button>
                  </div>
                </div>
              </app-modalbox>
            </td>
            <td class="table__td">{{row.city}}<span *ngIf="row.state">, {{row.state}}</span></td>
            <td class="table__td">{{row.formName}}</td>
            <td class="table__td">{{parseLastModifiedDate(row.lastModifiedDate)}}</td>
            <td class="table__td">{{row.lastModifyingAgentName}}</td>
            <td class="table__td">{{row.notes}}</td>
            <td class="table__td">
              <label class="o-checkable u-float-right">
                <input type="checkbox" class="o-btn--checkbox" [checked]="isSelected(row.agencyFormId)"
                  (click)="toggleForm(row.agencyFormId)" />
                <i class="o-btn o-btn--checkbox"></i>
              </label>
            </td>
          </tr>
        </tbody>
        <tbody class="table__tbody" *ngIf="!arrAgencyFormsFiltered?.length && isSearchResults()">
          <tr class="table__tr">
            <td [colSpan]="isDashboard ? 6 : 7">
              <p class="u-padd--bottom-1 u-padd--1">There are no results that match your search.</p>
            </td>
          </tr>
        </tbody>
        <tbody class="table__tbody" *ngIf="!arrAgencyFormsFiltered?.length  && !isSearchResults()">
          <tr class="table__tr">
            <td [colSpan]="isDashboard ? 6 : 7">
              <p class="u-padd--bottom-1 u-padd--1">
                <span *ngIf="!formsOnlyUser">Saved Forms for this quote will appear here.</span>
                <span *ngIf="formsOnlyUser">There are no forms.</span>
              </p>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</section>

<section class="section">
  <div class="u-flex u-flex--to-middle">
    <div class="">
      <app-pagination [currentPage]="paginationCurrentPage" [totalRecords]="paginationResultsCount"
        [recordsLimit]="paginationResultLimit" (onPageChange)="paginationPageChange($event)"></app-pagination>
    </div>
    <div class="" style="padding-left: 10rem;">
      <app-results-limiter [description]="'Forms to show'" (onChange)="onResultLimitChange($event)">
      </app-results-limiter>
    </div>
  </div>
</section>
<div *ngIf="isQuoteForms() && rmvList && rmvList.length > 0 && quoteLob === 'AUTOP'">
  <hr class="o-hr o-hr--size-section">
  <br>
  <h6 style="text-align:center">The transactions below have been linked to this quote. Once opened, a new RTA may be
    generated from the transaction.</h6>
  <section class="section">
    <div class="row">
      <div class="col-xs-12">
        <table class="table table--compact table--hoverable-grey">
          <thead class="table__thead">
            <tr class="">
              <th class="table__th">Owner</th>
              <th class="table__th">Agent Name</th>
              <th class="table__th">Transaction Type</th>
              <th class="table__th">Vin/Plate</th>
              <th class="table__th">Last Modified</th>
              <th class="table__th"></th>
            </tr>
          </thead>
          <tbody class="table__tbody" *ngIf="rmvList">
            <tr class="table__tr" *ngFor="let row of rmvList">

              <td class="table__td">
                <a [routerLink]="['/dashboard/rmv-services/rta-prefill']" [queryParams]="{
                  id: row.rmvServicesId,
                  type: row.transactionType
                }" routerLinkActive="router-link-active">{{ row.ownerFirstName || 'No Owner' }} {{ row.ownerLastName ||
                  '' }}</a>
              </td>
              <td class="table__td">
                {{ row.userFirstName }} {{ row.userLastName }}
              </td>
              <td class="table__td">New Registration And Title</td>
              <td class="table__td">
                <span *ngIf="row.vin; else plate">{{ row.vin }}</span>
                <ng-template #plate>
                  <span>{{ row.plate }}</span>
                </ng-template>
              </td>
              <td class="table__td">
                {{ row.lastModifiedDate | date }}
              </td>
              <td class="table__td"><button class="o-btn" (click)="deleteTransactionModalbox.open()">
                  <i class="fa fa-trash"></i> Delete
                </button>
                <app-modalbox #deleteTransactionModalbox>
                  <h2 class="u-t-size--1-5rem u-color-sunset u-align-left">
                    Delete Transaction?
                  </h2>
                  <p class="u-spacing--1-5 u-spacing--bottom-2 u-align-left">
                    Are you sure you want to delete this transaction?
                  </p>

                  <hr />

                  <div class="row u-spacing--2">
                    <div class="col-xs-12 u-align-right">
                      <button (click)="deleteTransaction(row.rmvServicesId)" class="o-btn u-spacing--right-1">
                        Yes, Delete Transaction
                      </button>
                      <button (click)="deleteTransactionModalbox.close()" class="o-btn o-btn--idle">
                        Cancel
                      </button>
                    </div>
                  </div>
                </app-modalbox>
              </td>
            </tr>
          </tbody>
          <tbody class="table__tbody" *ngIf="!rmvList?.length">
            <tr class="table__tr">
              <td colspan="5">
                <p class="u-padd--bottom-1 u-padd--1">
                  There are no results that match your search.
                </p>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </section>

  <section class="section">
    <div class="u-flex u-flex--spread u-flex--to-middle">
      <div class="">
        <app-pagination [currentPage]="rmvPaginationCurrentPage" [totalRecords]="rmvSize"
          (onPageChange)="rmvPaginationPageChange($event)"></app-pagination>
      </div>
      <div class="">
        <app-results-limiter [description]="'Transactions to show'"></app-results-limiter>
      </div>
    </div>
  </section>
</div>
<app-delete-form #deleteFormModal (onDeleteFormsClick)="refreshPage($event)">
</app-delete-form>
<app-leave-quote #leaveQuote *ngIf="!formsOnlyUser"></app-leave-quote>
