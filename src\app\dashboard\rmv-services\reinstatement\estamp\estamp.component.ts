import { Component, OnInit, ViewChild } from '@angular/core';
import { LookupsService } from '../../../app-services/lookups.service';
import { FilterOption } from '../../../../app-model/filter-option';
import { ControlContainer, NgForm } from '@angular/forms';

import { Router } from '@angular/router';
import { format, subDays, parseISO } from 'date-fns';

@Component({
    selector: 'app-estamp',
    templateUrl: './estamp.component.html',
    viewProviders: [{ provide: ControlContainer, useExisting: NgForm }],
    styleUrls: ['./estamp.component.scss'],
    standalone: false
})
export class EstampComponent implements OnInit {
  @ViewChild('refPickerEffectiveDate') effectiveDatePicker;
  @ViewChild('refProvider') providerList;
carrier;
effectiveDate = this.router.url.includes('renewal') ? format(new Date(), 'yyyy-MM-dd') : '';
isRenewal = this.router.url.includes('renewal');
providers = [];
setMinDate = this.router.url.includes('reinstatement') ? 30 : 180;
maxDate = format(new Date(), 'yyyy-MM-dd');
minDate = format(
  subDays(new Date(), this.setMinDate),
  'yyyy-MM-dd'
);

  constructor(private lookupService: LookupsService, private router: Router) { }

  ngOnInit() {

   this.lookupService.getEstampsAsOptions().subscribe(x => this.providers = x);
  }

  setDate($ev) {
    this.effectiveDate = format(parseISO($ev.date), 'yyyy-MM-dd');
  }

}
