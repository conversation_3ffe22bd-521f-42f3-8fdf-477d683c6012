
import {first} from 'rxjs/operators';
import { Component, OnInit, OnDestroy, EventEmitter, Output, Input, ViewChild} from '@angular/core';
import { ModalboxComponent } from 'app/shared/components/modalbox/modalbox.component';
import { SubscriptionLike as ISubscription } from 'rxjs';
import { AgencyUserService, UserData } from 'app/shared/services/agency-user.service';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Quote } from 'app/app-model/quote';
import { exit } from 'process';

interface ISendToBscData {
  rateRequestId: string;
  quoteIndex: number;
  name: string;
  phone: string;
  subject: string;
  message: string;
  carrier: string;
  headerText: string;
}

export type ISendSuccesfull = boolean;

@Component({
    selector: 'app-send-to-bsc',
    templateUrl: './send-to-bsc.component.html',
    styleUrls: ['./send-to-bsc.component.scss'],
    standalone: false
})
export class SendToBscComponent implements OnInit, OnDestroy {


  @Input() carrierName = '';
  @Input() requestId = '';
  @Input() quoteIndex = -1;
  @Input() isMessage: false;

  @Output() sendClick: EventEmitter<void> = new EventEmitter;
  @Output() sendFinished: EventEmitter<ISendSuccesfull> = new EventEmitter;
  @Output() confirmationEmail: EventEmitter<void> = new EventEmitter;
  @Output() cancel: EventEmitter<void> = new EventEmitter;

  @ViewChild('modalCarrierCredentialsValidation') public modalCarrierCredentialsValidation: ModalboxComponent;

  private subscriptionAgencyData: ISubscription;
  private subscriptionQuote: ISubscription;

  public validationWarning = '';
  public formSubmitError = false;
  public showConfirmation = false;
  public showConfirmationForSendMsg = false;

  public agentId;

  public data: ISendToBscData = {
    rateRequestId: '',
    quoteIndex: -1,
    name: '',
    phone: '',
    subject: '',
    message: '',
    carrier: '',
    headerText: 'Send Quote to Boston Software'
  };

  private quote: Quote = null;
  private quoteUrl = '';
  public userData: UserData = new UserData();

  constructor(
    private agencyUserService: AgencyUserService,
    private overlayLoaderService: OverlayLoaderService,
    private apiCommonService: ApiCommonService,
    private storageService: StorageService,
  ) { }

  ngOnInit() {
    this.showConfirmation = false;
    this.subscribeAgencyData();
    this.subscribeQuote();
    this.setUserAgent();

    this.data.carrier = this.carrierName;
    this.data.rateRequestId = this.requestId;
    this.data.quoteIndex = this.quoteIndex;
    if (this.isMessage) {
      this.data.headerText = 'Ask Support a Question';
    }
  }

  ngOnDestroy() {
    this.subscriptionAgencyData && this.subscriptionAgencyData.unsubscribe();
    this.subscriptionQuote && this.subscriptionQuote.unsubscribe();
  }

  private formatPhoneNumber(phoneNumberString) : string {
    let cleaned = ('' + phoneNumberString).replace(/\D/g, '');
    let match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
    if (match) {
      return '(' + match[1] + ') ' + match[2] + '-' + match[3];
    }
    return null;
  }

  private subscribeAgencyData(): void {
    this.subscriptionAgencyData = this.agencyUserService.userData$.subscribe((agency: UserData) => {
      this.userData = agency;
      if (agency && agency.user) {
        this.data.name = agency.user.firstName + ' ' + agency.user.lastName;
      if(agency.user.userPhone!="")
      {
          this.data.phone = this.formatPhoneNumber(agency.user.userPhone);
          if (agency.user.userPhoneExtension != "")
            this.data.phone = this.data.phone+ " ext. "+ agency.user.userPhoneExtension;
      }
      else if (agency.facilityPhone!="")
        this.data.phone = this.formatPhoneNumber(agency.facilityPhone);
      else
        this.data.phone = this.formatPhoneNumber(agency.phone);
      }
    });
  }


  private subscribeQuote(): void {
    this.subscriptionQuote = this.storageService.getStorageData('selectedQuote')
      .subscribe((res: Quote) => {
        this.quote = res;
        if (res && res.meta && res.meta.href) {
          this.quoteUrl = res.meta.href;
        }
      });
  }

  private resetForm(): void {
    this.data.rateRequestId = this.requestId;
    this.data.quoteIndex = this.quoteIndex;
    this.data.name = this.userData.user.firstName + ' ' + this.userData.user.lastName;
    this.data.phone = this.userData.phone;
    this.data.subject = '';
    this.data.message = '';
    this.data.carrier = this.carrierName;
  }

  private setUserAgent():void{

    let userData: UserData;

    this.agencyUserService.userData$.pipe(
      first())
      .subscribe(data => userData = data);

    // Quote agency contact
    if (userData) {
      this.agentId = userData.user.userId;
    }
}

  public onSend(): void {
    this.sendClick.emit();
  }

  public onCancel(ev: Event): void {
    ev.preventDefault();
    this.cancel.emit();
  }

  public onFormSubmit(ev: Event): void {
    ev.preventDefault();


      this.overlayLoaderService.showLoader('Sending...');

      if (this.isMessage) {
          this.quoteUrl = '/quotes/message';
      }

      this.apiCommonService.postByUri(this.quoteUrl + '/sendtobsc', this.data).pipe(first()).subscribe( response => {
        this.overlayLoaderService.hideLoader();
        if (this.isMessage) {
          this.showConfirmationForSendMsg = true;
        } else {
          this.showConfirmation = true;
        }

        this.sendFinished.emit(true);
      }, err => {
        this.overlayLoaderService.hideLoader();
        this.formSubmitError = true;
        this.sendFinished.emit(false);
      });
    }


  public get sendMailData(): string {
    return '<EMAIL>?subject=Send to BSC - '
      + this.userData.name + ' - ' + this.data.subject + '&body=' + this.data.message;
  }

  public sendMail(ev: Event): void {
    ev.preventDefault();

    // Open protocol in new window to avoid 'onBeforeUnload' prompt (Quote not saved)
    const helperWindow = window.open('mailto:' + this.sendMailData, 'mailWindow');

    // Automatically close opened window - we do not need it anymore
    setTimeout(() => {
      helperWindow.close();
      this.confirmationEmail.emit();
    }, 50);

    // document.location.href = 'mailto:'+ this.sendMailData;
  }


}
