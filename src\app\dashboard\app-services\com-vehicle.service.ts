import { Injectable } from '@angular/core';
import { IComVehicle } from 'app/app-model/CommercialVehicle';

@Injectable()
export class ComVehicleService {
  vehicle: IComVehicle = {
    vin: '',
    vehicleType: 'Truck',
    year: 2019,
    make: 'Ford',
    model: 'F550 Super Duty',
    bodyType: 'Cab and Chasis',
    unitDescription: 'Truck',
    originalCostNew: 70165,
    vehicleWeight: 19500,
    plateNumber: 'ABC123',
    plateType: 'CON',
    radius: 'Local Upto 150 miles',
    vehicleUse: 'Service',
    classCode: '',
    garaging: '189 Reservoir Street, Needham MA',
  };

  constructor() { }

  getVehicle(): IComVehicle {
    return this.vehicle;
  }
}
