import { async, ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';

import { VEHICLE_GENERAL_DETAILS } from 'testing/data/lookups/vin';
import { VEHICLES } from 'testing/data/quotes/vehicles';
import { updateProperties } from 'testing/helpers/all';
import { StubBlurDirective } from 'testing/stubs/directives/blur.directive';
import { StubVehiclesServiceProvider } from 'testing/stubs/services/vehicles.service.provider';

import { VehicleGeneralDetails } from 'app/app-model/vehicle';
import { ToggleboxComponent } from 'app/shared/components/togglebox/togglebox.component';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Helpers } from 'app/utils/helpers';

import { VehicleOptionsComponent } from './vehicle-options.component';

describe('Component: VehicleOptions', () => {
  let component: VehicleOptionsComponent;
  let fixture: ComponentFixture<VehicleOptionsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [FormsModule],
      declarations: [ VehicleOptionsComponent, ToggleboxComponent, StubBlurDirective ],
      providers: [
        StubVehiclesServiceProvider
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(VehicleOptionsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should react to selectedVehicleGeneralDetails update', fakeAsync(() => {
    updateProperties(component, {
      selectedVehicle: Helpers.deepClone(VEHICLES.items[0]),
      selectedVehicleGeneralDetails: Helpers.deepClone(VEHICLE_GENERAL_DETAILS)
    });

    fixture.detectChanges();
    tick(50);

    expect(component.selectedVehicle.options.passiveDisabling).toBeTruthy();
  }));

  it('should reset all local component options if empty selectedVehicleGeneralDetails is provided [SPRC-587]', fakeAsync(() => {
    updateProperties(component, {
      selectedVehicle: Helpers.deepClone(VEHICLES.items[0]),
      selectedVehicleGeneralDetails: new VehicleGeneralDetails()
    });

    fixture.detectChanges();
    tick(50);

    expect(component.options.passiveDisabling.selected).toBeFalsy();
  }));

  it('should NOT reset selectedVehicle.options if empty selectedVehicleGeneralDetails is provided [SPRC-587]', fakeAsync(() => {
    updateProperties(component, {
      selectedVehicle: Helpers.deepClone(VEHICLES.items[0]),
      selectedVehicleGeneralDetails: new VehicleGeneralDetails()
    });

    fixture.detectChanges();
    tick(50);

    expect(component.selectedVehicle.options.passiveDisabling).not.toBeFalsy();
  }));
});
