import { Indicator } from '../../../app-model/rmv-services-add.model';
export interface LessorLookupData {
  lessorId: string;
  name: string;
  fid: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  zipCode: string;
}

export interface BusinessOwnerLookupData {
  lessorId: string;
  name: string;
  fid: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  zipCode: string;
}

export interface LienholderLookupData {
  lienholderId: string;
  name: string;
  code: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  postalCode: string;
  county: string;
}

export interface OwnerAddress {
  addressStreet1: string;
  addressStreet2: string;
  addressUnitType: string;
  addressUnit: string;
  addressCity: string;
  addressState: string;
  addressZIP: string;
  addressCountry: string;
}


export interface Owner {
  id: number;
  firstName: string;
  middleName?: string;
  lastName: string;
  nameSuffix?: string;
  dateOfBirth: string;
  license: string;
  licenseState: string;
  ssn?: string;
  email?: string;
  phone?: string;
  phoneType?: string;
  addressChangeIndicator?: string;
  residentialAddress?: OwnerAddress;
  mailAddress?: OwnerAddress;
  garagingAddress?: OwnerAddress;
  atlasEntityKey?: any;
  atlasEntityLocationKey?: any;
  ownershipRole?: string;
  fid?: string;
  newNumberOfSeats?: string;
  newRegisteredWeight?: string;
  entityType?: string;
}

export interface Vehicle {
  id: number;
  lookupType: string;
  usage?: string;
  plateNumber: string;
  vin: string;
  plateType: string;
  odometer?: string;
  odometerCode?: string;
  registrationType?: string;
  reassignedPlate?: string;
  plateReactivateIndicator?: string;
  registrationReason?: string;
  ownership?: string;
  condition?: string;
  primaryColor?: string;
  transmission?: string;
  passengers?: string;
  outOfStateTitleNumber?: string;
  titleState?: string;
  titleIssueDate?: string;
  atlasVehicleIndicator?: string;
  atlasVehicleKey?: string;
  pseudoTrailerVINIndicator?: string;
  bodyStyle?: string;
  vehicleType?: string;
  secondaryColor?: string;
  year?: string;
  make?: string;
  makeDescription?: string;
  model?: string;
  modelNumber?: string;
  cylinders?: string;
  doors?: string;
  numberOfSeats?: string;
  fuelType?: string;
  trim?: string;
  msrp?: string;
  tradeInAllowance?: string;
  grossVehicleWeight?: string;
  registeredWeight?: string;
  trimItems?: TrimItem[];
  titleType?: string;
  titleBrands?: TitleBrands[];
  newNumberOfSeats?: string;
  newRegisteredWeight?: string;
  newPlateType?: string,
  newAssignedPlateNumber?: string,
  plateAssignedIndicator?: string,
  titleStatusDescription?: string
  registrationStatus?: string
}

export interface TitleBrands {
  brandType: string;
}

export interface TrimItem {
  id: number;
  name: string;
  value: string;
  msrp: string;
}

export interface GaragingAddres {
  type: string;
  referenceType: string;
  referenceId: string;
  street: string;
  street2: string;
  unitOrApt: string;
  unitType: string;
  city: string;
  state: string;
  zip: string;
}

export interface Address {
  street: string;
  unitOrApt: string;
  city: string;
  state: string;
  zip: string;
}

export interface Seller {
  businessName: string;
  firstName: string;
  lastName: string;
  address: Address;
}

export interface PurchaseAndSalesInformation {
  purchaseDate: string;
  purchaseState: string;
  seller: Seller;
  taxExempt: string;
  taxExemptType: string;
  purchaseType: string;
  dealerFid: string;
  totalSalePrice: string;
  auctionSale: string;
  maResidentAtTimeOfPurchase: string;
  maSalesTaxPreviouslyPaid: string;
  nonMASalesTaxPreviouslyPaid: string;
  proofOfNoTaxRequired: string;
}

export interface EStampInfo {
  effectiveDate: string;
  writingCompanyName: string;
  companyCode: number;
  agencyName: string;
  signedBy: string;
  dateType: string;
}

export interface Lessor {
  id: number;
  fid: string;
}

export interface Lienholder {
  id: number;
  code: string;
}

export interface BusinessOwner {
  id: number;
  fid: string;
}

export interface GetReadyUserEnteredData {
  owners: Owner[];
  vehicles: Vehicle[];
  garagingAddress: GaragingAddres;
  purchaseAndSalesInformation: PurchaseAndSalesInformation;
  eStampInfo: EStampInfo;
  lessors: Lessor[];
  lienholders: Lienholder[];
  businessowners: BusinessOwner[];
  indicators?: Indicator[];
}

export interface GetReadyRequest {
  transactionType: string;
  ownership: string;
  agencyId: string;
  creationUserId: number;
  rmvLookupData: string;
  lessorLookupData: LessorLookupData[];
  businessOwnerLookupData: BusinessOwnerLookupData[];
  lienholderLookupData: LienholderLookupData[];
  rmvServicesUserEnteredData: GetReadyUserEnteredData;
}
