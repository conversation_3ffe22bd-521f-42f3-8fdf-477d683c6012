<button type="button" class="o-link o-link--blue" style="margin-left:15px" (click)="openModal()">Update</button>
<!-- ISO: {{dwellingProtectionClass?.isoProtectionClass}} -->

<app-modalbox #refModalProtectionOverrides>
  <h1 class="o-heading">User defined ISO protection class</h1>

  <div class="box box--silver u-spacing--1-5">
    <app-loader [loading]="loadingData" [cssClass]="'loader--with-opacity'" [loadingText]="'Checking data, please wait...'"></app-loader>

    <ng-container *ngIf="valuesManuallyEdited">
      <p class="u-color-app-dwelling u-spacing--bottom-1-5">Protection classes in yellow were manually entered.</p>
      <hr>
    </ng-container>


    <p-scrollPanel styleClass="u-height-max-340px">
        <table class="form-table">
          <thead class="table__thead">
            <tr class="form-table__row">
              <th class="table__th table__th--no-borders table__th--no-left-padding u-width-80px">ISO PC</th>
              <th class="table__th table__th--no-borders u-padd--left-2">PLAN</th>
            </tr>
          </thead>
          <tbody>
            <tr class="form-table__row" *ngFor="let plan of modalProtectionClassOverrides">
              <td class="form-table__cell u-width-80px">
                <sm-autocomplete
                  [css]="plan.manuallyEdited ? 'u-warning-object' : ''"
                  [name]="'iso-value' + plan.ratingPlanId"
                  [id]="'iso-value' + plan.ratingPlanId"
                  [options]="optionsIsoValues"
                  [activeOption]="{id: plan.protectionClassCode, text: removeLeadingUnderscore(isoProtectionClass)}"
                  [searchFromBegining]="true"
                  (onSelect)="selectedCustomISO($event, plan)">
                </sm-autocomplete>
              </td>
              <td class="form-table__cell u-padd--left-2">{{ plan.ratingPlanName }}</td>
            </tr>
          </tbody>
        </table>
    </p-scrollPanel>
  </div>

  <div class="row u-spacing--2">
    <div class="col-xs-12 u-align-right">
      <button type="button" class="o-btn u-spacing--right-2" (click)="modalActionSave()">Save</button>
      <button type="button" class="o-btn o-btn--idle" (click)="modalActionCancel()">Cancel</button>
    </div>
  </div>
</app-modalbox>
