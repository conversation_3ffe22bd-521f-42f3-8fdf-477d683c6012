<section class="section section--compact u-spacing--2-5">
  <div class="row">
    <div class="col-xs-12">
      <h1 class="o-heading">Coverage Limits</h1>
    </div>
  </div>

  <div class="row u-spacing--1">
    <div class="col-xs-12">
      <div class="box box--silver">
        <div class="row o-columns">
          <div class="col-xs-6">
            <table class="form-table">
              <tr class="form-table__row">
                <tr class="form-table__row" [ngClass]="{'is-required-field': (!coverageQuoteDwelling && formType !== 'HO6')}" *ngIf="formType !== 'HO4'">
                  <td class="form-table__cell u-width-140px">
                    <label for="">Dwelling:</label>
                  </td>
                  <td class="form-table__cell u-width-160px">
                    <input #refDwelling
                    fieldAutofocus
                      type="text"
                      id="field_DWELL"
                      name="field_DWELL"
                      [required]="formType !== 'HO6'"
                      mask="separator.0" thousandSeparator="," decimalMarker="." [allowNegativeNumbers]="false" prefix="$"
                      [(ngModel)]="coverageQuoteDwelling"
                      (focus)="helperOnInputFocusTextMaskChangeEventIssueWorkaround($event)"
                      (blur)="helperOnInputBlurTextMaskChangeEventIssueWorkaroundUpdateQuoteCoverage($event, 'DWELL', coverageQuoteDwelling)">
                      <!-- textMask issue, change event doesn't work properly:: (change)="updateQuoteCoverage('DWELL', coverageQuoteDwelling)" -->
                  </td>
                  <td class="form-table__cell u-width-70px"></td>
                </tr>

                <tr class="form-table__row" [ngClass]="{'is-required-field': !coverageQuoteOtherStructures}" *ngIf="formType !== 'HO4' && formType !== 'HO6'">
                  <td class="form-table__cell u-width-140px">
                    <label for="">Other Structures:</label>
                  </td>
                  <td class="form-table__cell u-width-160px">
                    <input #refOtherStructures
                      type="text"
                      id="field_OS"
                      name="field_OS"
                      required
                      mask="separator.0" thousandSeparator="," decimalMarker="." [allowNegativeNumbers]="false" prefix="$"
                      [(ngModel)]="coverageQuoteOtherStructures"
                      (focus)="helperOnInputFocusTextMaskChangeEventIssueWorkaround($event)"
                      (blur)="helperOnInputBlurTextMaskChangeEventIssueWorkaroundUpdateQuoteCoverage($event, 'OS', coverageQuoteOtherStructures)">
                      <!-- textMask issue, change event doesn't work properly:: (change)="updateQuoteCoverage('OS', coverageQuoteOtherStructures)" -->
                  </td>
                  <td class="form-table__cell u-width-70px"></td>
                </tr>

                <tr class="form-table__row" [ngClass]="{'is-required-field': false}" *ngIf="formType === 'HO6'">
                  <td class="form-table__cell u-width-140px">
                    <label for="">Special Coverage:</label>
                  </td>
                  <td class="form-table__cell" colspan="2" id="" [class.radio-disabled]="!coverageQuoteDwelling">
                    <label class="o-checkable u-spacing--right-2" [appDetectSystem] [removeRadio]="true">
                      <input type="radio" name="specialCoverages" id="specialCoverageYes" [value]="'Yes'" [disabled]="!coverageQuoteDwelling" [checked]="coverageQuoteSpecialCoverage == 'Yes'" (change)="updateQuoteCoverage('AASPC', 'Yes')">
                      <i class="o-btn o-btn--radio"></i>
                      <span>Yes</span>
                    </label>
                    <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
                      <input type="radio" name="specialCoverages" id="specialCoverageNo" [value]="'No'" [disabled]="!coverageQuoteDwelling" [checked]="coverageQuoteSpecialCoverage !== 'Yes'" (change)="updateQuoteCoverage('AASPC', 'No')">
                      <i class="o-btn o-btn--radio"></i>
                      <span>No</span>
                    </label>
                  </td>
                </tr>

                <tr class="form-table__row" [ngClass]="{'is-required-field': !coverageQuotePersonalProperty}">
                  <td class="form-table__cell u-width-140px">
                    <label for="">Personal Property:</label>
                  </td>
                  <td class="form-table__cell u-width-160px">
                    <input #refPersonalProperty
                      type="text"
                      id="field_PP"
                      name="field_PP"
                      required
                      mask="separator.0" thousandSeparator="," decimalMarker="." [allowNegativeNumbers]="false" prefix="$"
                      [(ngModel)]="coverageQuotePersonalProperty"
                      (focus)="helperOnInputFocusTextMaskChangeEventIssueWorkaround($event)"
                      (blur)="helperOnInputBlurTextMaskChangeEventIssueWorkaroundUpdateQuoteCoverage($event, 'PP', coverageQuotePersonalProperty)">
                      <!-- textMask issue, change event doesn't work properly:: (change)="updateQuoteCoverage('PP', coverageQuotePersonalProperty)" -->
                  </td>
                  <td class="form-table__cell u-width-70px"></td>
                </tr>

                <tr class="form-table__row" [ngClass]="{'is-required-field': !coverageQuoteLossOfUse}">
                  <td class="form-table__cell u-width-140px">
                    <label for="">Loss of Use:</label>
                  </td>
                  <td class="form-table__cell u-width-160px">
                    <input #refLossOfUse
                      type="text"
                      id="field_LOU"
                      name="field_LOU"
                      required
                      mask="separator.0" thousandSeparator="," decimalMarker="." [allowNegativeNumbers]="false" prefix="$"
                      [(ngModel)]="coverageQuoteLossOfUse"
                      (focus)="helperOnInputFocusTextMaskChangeEventIssueWorkaround($event); lossOfUseValidationOnFocus()"
                      (blur)="lossOfUseValidationOnBlur($event, 'LOU', coverageQuoteLossOfUse)">
                      <!-- (blur)="helperOnInputBlurTextMaskChangeEventIssueWorkaroundUpdateQuoteCoverage($event, 'LOU', coverageQuoteLossOfUse);" -->
                      <!-- textMask issue, change event doesn't work properly:: (change)="updateQuoteCoverage('LOU', coverageQuoteLossOfUse)" -->
                      <app-modalbox #refModalLossOfUse [css]="'u-width-420px'" (onStateChange)="actionModalLossOfUseStateChange($event, refModalLossOfUseBtn, refLossOfUse)">
                        <div class="box box--silver u-spacing--1-5">
                          <p>{{lossOfUseModalMessage}}</p>
                        </div>
                        <div class="row u-spacing--2">
                          <div class="col-xs-12 u-align-right">
                            <button #refModalLossOfUseBtn class="o-btn" (click)="actionModalLossOfUseOk($event)">Ok</button>
                          </div>
                        </div>
                      </app-modalbox>
                  </td>
                  <td class="form-table__cell u-width-70px"></td>
                </tr>

                <tr class="form-table__row" [ngClass]="{'is-required-field': !coverageQuotePersonalLiability?.id}">
                  <td class="form-table__cell u-width-140px">
                    <label for="">Personal Liability:</label>
                  </td>
                  <td class="form-table__cell u-width-160px">
                    <sm-autocomplete
                      #refPersonalLiability
                      [options]="coveragePersonalLiabilityOptions"
                      [activeOption]="coverageQuotePersonalLiability"
                      [name]="'field_PL'"
                      [id]="'field_PL'"
                      [disabled]="false"
                      [allowEmptyValue]="false"
                      [required]="true"
                      [allowSearchById]="true"
                      (onSelect)="updateQuoteCoverage('PL', $event.id)">
                    </sm-autocomplete>
                  </td>
                  <td class="form-table__cell u-width-70px"></td>
                </tr>

                <tr class="form-table__row" [ngClass]="{'is-required-field': !coverageQuoteMedicalPayments?.id, 'is-disabled-field-regular': lockMEDPM}">
                  <td class="form-table__cell u-width-140px">
                    <label for="">Medical Payments:</label>
                  </td>
                  <td class="form-table__cell u-width-160px">
                    <sm-autocomplete
                      #refMedicalPayments
                      [options]="coverageMedicalPaymentsOptions"
                      [activeOption]="coverageQuoteMedicalPayments"
                      [name]="'field_MEDPM'"
                      [id]="'field_MEDPM'"
                      [disabled]="lockMEDPM"
                      [searchFromBegining]="false"
                      [allowEmptyValue]="false"
                      [allowCustomText]="false"
                      [required]="true"
                      [allowSearchById]="true"
                      (onSelect)="updateQuoteCoverage('MEDPM', $event.id)">
                    </sm-autocomplete>
                  </td>
                  <td class="form-table__cell u-width-70px"></td>
                </tr>

            </table>
          </div>
          <div class="col-xs-6">
            <table class="form-table">

              <tr class="form-table__row">
                <td class="form-table__cell u-width-180px u-spacing--bottom-1 u-color-slate-grey">REPLACEMENT COST</td>
                <td class="form-table__cell u-width-140px"></td>
              </tr>

              <tr class="form-table__row" *ngIf="formType !== 'HO4' && formType !== 'HO6'">
                <td class="form-table__cell u-width-140px">
                  <label for="">Dwelling:</label>
                </td>
                <td class="form-table__cell u-width-160px">
                <sm-autocomplete
                  #refDwelling
                  [options]="coverageDwellingReplacementCostOptions"
                  [activeOption]="coverageQuoteDwellingReplacementCost"
                  [name]="'coverageDwellingReplacementCostOptions'"
                  [id]="'coverageDwellingReplacementCostOptions'"
                  [disabled]="false"
                  [searchFromBegining]="false"
                  [allowEmptyValue]="true"
                  [allowCustomText]="false"
                  [required]="false"
                  (onSelect)="updateQuoteCoverage('RCDWELL', $event.id)">
                </sm-autocomplete>
                </td>
                <td class="form-table__cell u-width-70px"></td>
              </tr>

              <tr class="form-table__row" [class.is-required-field]="!coverageQuoteContentsReplacementCost || coverageQuoteContentsReplacementCost !== 'No' && coverageQuoteContentsReplacementCost !== 'Yes'">
                <td class="form-table__cell">
                  Contents:
                </td>
                <td class="form-table__cell" colspan="2" id="field_RCCONT">
                  <label class="o-checkable u-spacing--right-2" [appDetectSystem] [removeRadio]="true">
                    <input type="radio" name="contents" id="contentsYes" [value]="'Yes'" [checked]="coverageQuoteContentsReplacementCost === 'Yes'" (change)="updateQuoteCoverage('RCCONT', 'Yes')">
                    <i class="o-btn o-btn--radio"></i>
                    <span>Yes</span>
                  </label>
                  <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
                    <input type="radio" name="contents" id="contentsNo" [value]="'No'" [checked]="coverageQuoteContentsReplacementCost === 'No'" (change)="updateQuoteCoverage('RCCONT', 'No')">
                    <i class="o-btn o-btn--radio"></i>
                    <span>No</span>
                  </label>
                </td>
              </tr>

              <tr class="form-table__row">
                <td class="form-table__cell u-width-180px u-spacing--bottom-1 u-color-slate-grey">DEDUCTIBLES</td>
                <td class="form-table__cell u-width-140px"></td>
              </tr>

               <tr class="form-table__row" [ngClass]="{'is-required-field': !coverageQuoteAllPerils}">
                 <td class="form-table__cell u-width-140px">
                   <label for="">All Perils:</label>
                 </td>
                 <td class="form-table__cell u-width-160px">
                 <sm-autocomplete
                   #refAllPerils
                   [options]="coverageAllPerilsOptions"
                   [activeOption]="coverageQuoteAllPerils"
                   [name]="'field_APDED'"
                   [id]="'field_APDED'"
                   [disabled]="false"
                   [searchFromBegining]="false"
                   [allowEmptyValue]="false"
                   [allowCustomText]="false"
                   [required]="false"
                   (onSelect)="updateQuoteCoverage('APDED', $event.id)">
                 </sm-autocomplete>
                 </td>
                 <td class="form-table__cell u-width-70px"></td>
               </tr>

               <tr class="form-table__row" [class.is-required-field]="windHailFieldIsInvalid()">
                <td class="form-table__cell u-width-140px">
                  <label for="">Wind / Hail:</label>
                </td>
                <td class="form-table__cell u-width-180px">
                 <sm-autocomplete
                   #refWindHail
                   [options]="coverageWindHailOptions"
                   [activeOption]="coverageQuoteWindHail"
                   [name]="'field_WHDED'"
                   [id]="'field_WHDED'"
                   [disabled]="false"
                   [searchFromBegining]="false"
                   [allowEmptyValue]="false"
                   [allowCustomText]="false"
                   [required]="false"
                   (onSelect)="updateQuoteCoverage('WHDED', $event.id)">
                 </sm-autocomplete>
                </td>
                <td class="form-table__cell u-width-20px"></td>
              </tr>

            </table>

          </div>
        </div>
      </div>
    </div>
  </div>
  <app-modalbox #modalDefaultPercentages>
    <h1 class="o-heading o-heading--red">Some coverage limits are not set to default percentages.</h1>
    <p class="u-spacing--1">The following limits were manually changed to custom values:</p>
    <ul class="box u-spacing--1">
      <li *ngFor="let item of coveragesNotDefaultPercentages">
        {{ item.description }}: {{ moneyService.formatToMoney(item.values[0].value) }} (default value would be {{ moneyService.formatToMoney(item.defaultValue) }})
      </li>
    </ul>
    <p class="u-spacing--1">We can leave all values as is, update all coverages to match default percentages, or we can update only unmodified values to match standard defaults.</p>

    <hr class="o-hr u-spacing--2">

    <div class="row u-spacing--2">
      <div class="col-xs-12 u-align-right">
        <button class="o-btn u-spacing--right-2" (click)="decideOnDefaultCoverages('leave')">Leave as is</button>
        <button class="o-btn o-btn--outlined" (click)="decideOnDefaultCoverages('all')">
          <span *ngIf="quote.policyType === 'Homeowner'">Reset all</span>
          <span *ngIf="quote.policyType !== 'Homeowner'">Reset</span>
        </button>
        <button class="o-btn o-btn--outlined u-spacing--left-2" (click)="decideOnDefaultCoverages('unmodified')" *ngIf="quote.policyType === 'Homeowner'">Reset unmodified</button>
      </div>
    </div>
  </app-modalbox>
</section>

<section class="section section--compact u-spacing--2-5">
  <div class="row">
    <div class="col-xs-12">
      <h1 class="o-heading">Insured Discounts</h1>
    </div>
  </div>

  <div class="row u-spacing--1">
    <div class="col-xs-12">
      <div class="box box--silver">
        <div class="row o-columns">
          <div class="col-xs-6">
            <table class="form-table">

              <tr class="form-table__row">
                <td class="form-table__cell u-width-140px">
                  <label for="">Insured DOB / Age:</label>
                </td>
                <td class="form-table__cell u-width-160px">
                  <app-datepicker-input
                      #refInsurerDOB
                      [id]="insurerDOB"
                      [selectDate]="insurerDOB"
                      [returnDateFormat]="'yyyy'"
                      (onDateChange)="selectedDate($event)"
                      >
                    </app-datepicker-input>
                </td>
                <td class="form-table__cell">
                  <input [disabled]="true" class="u-t-align--center u-width-50px" #refInsurerAge="ngModel" type="text" id="insurerAge" name="insurerAge" [(ngModel)]="insurerAge">
                </td>
              </tr>

            </table>
          </div>
          <div class="col-xs-6">
            <table class="form-table">

              <tr class="form-table__row" [ngClass]="{'is-required-field': fieldNonSmokerIsRequired()}">
                <td class="form-table__cell u-width-140px">
                  Non-Smoker:
                </td>
                <td class="form-table__cell" colspan="2">
                  <label class="o-checkable u-spacing--right-2" [appDetectSystem] [removeRadio]="true">
                      <input type="radio" name="nonSmoker" id="nonSmokerYes" [value]="'Yes'" [(ngModel)]="coverageQuoteNonSmoke" [checked]="coverageQuoteNonSmoke == 'Yes'" (change)="updateQuoteCoverage('NONSMOKE', 'Yes')">
                      <i class="o-btn o-btn--radio"></i>
                      <span>Yes</span>
                    </label>
                    <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
                      <input type="radio" name="nonSmoker" id="nonSmokerNo" [value]="'No'" [(ngModel)]="coverageQuoteNonSmoke" [checked]="coverageQuoteNonSmoke == 'No'" (change)="updateQuoteCoverage('NONSMOKE', 'No')">
                      <i class="o-btn o-btn--radio"></i>
                      <span>No</span>
                    </label>
                </td>
              </tr>

            </table>

          </div>
        </div>
      </div>
    </div>
  </div>
</section>
