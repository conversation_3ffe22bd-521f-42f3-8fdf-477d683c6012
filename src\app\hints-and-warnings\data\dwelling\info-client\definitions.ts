import { WARNING_GROUPS, WarningDefinitionI, AdditionalDataI, AdditionalDataHomeClientInfo} from 'app/hints-and-warnings/model/warnings';
import { ClientAddress, ClientContactMethod, ClientDetails } from 'app/app-model/client';
import { Validate } from 'app/hints-and-warnings/validators';
import { Quote } from 'app/app-model/quote';
import { CoverageItemParsed } from '../../../../app-model/coverage';



function requiredField(value):boolean {
  if(value == undefined || value == null) {
    return true;
  }
  value = String(value);

  return value.trim().length <= 0 || !value;
}

function validateZipCode(zip): boolean {
  if (zip === '' || zip === null) {
    return false;
  }

  const zipPattern = /^\d{5}(?:-?\d{4})?$/;
  return !zipPattern.test(zip);
}

function generateViewUrl():string {
  return  '{{current_url}}?overlay=info&type=client';
}

/**
 * Validation for Client Info Data
 * For Model: ClientDetails
 */


const clientInfoFirstName:WarningDefinitionI = {
  id: 'firstName',
  deepId: 'firstName',
  viewUri: generateViewUrl,
  viewFieldId: 'clientInfoPrimaryFirstName',
  warnings: [{
    label: 'Required First Name for client.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}

const clientInfoLastName:WarningDefinitionI = {
  id: 'lastName',
  deepId: 'lastName',
  viewUri: generateViewUrl,
  viewFieldId: 'clientInfoPrimaryLastName',
  warnings: [{
    label: 'Required Last Name for client',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}

const clientInfoDOB:WarningDefinitionI = {
  id: 'dob',
  deepId: 'dob',
  viewUri: generateViewUrl,
  viewFieldId: 'clientInfoPrimaryDOB',
  warnings: [{
    label: 'Required Date of Birth for client.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}

// const clientInfoSecondaryDOB:WarningDefinitionI = {
//   id: 'secondaryDOB',
//   deepId: 'secondaryDOB',
//   viewUri: generateViewUrl,
//   viewFieldId: 'clientInfoSecondaryDOB',
//   warnings: [{
//     label: 'Required Date of Birth for secondary client.',
//     condition: (val, fullObj: ClientDetails, additionalData: AdditionalDataI) => {
//       return (fullObj.secondaryFirstName && fullObj.secondaryLastName && !fullObj.secondaryDOB)
//     },
//     group: WARNING_GROUPS.general,
//     carriers: []
//   }]
// }

export const WARNINGS_DEFINITIONS_INFO_CLIENT_FOR_DWELLING: WarningDefinitionI[] = [
  clientInfoFirstName,
  clientInfoLastName,
  // clientInfoDOB,
  // clientInfoSecondaryDOB
];

// ------------------------------------------------------------------------------

/**
 * Validation for Client Info Addresses Data
 * For Model: ClientAddress
 */

function generateAddressFieldId(addr:ClientAddress, customName:string):string {
  return (addr && addr.addressType) ? customName + '_' + addr.addressType : customName;
}

const clientInfoAddrZip: WarningDefinitionI = {
  id: 'zip',
  deepId: 'zip',
  viewUri: generateViewUrl,
  viewFieldId: (fullObj: ClientAddress) => generateAddressFieldId(fullObj, 'clientInfoAddrZip'),
  warnings: [
  {
    label: 'Client Prior Address Zip code is not Valid',
    condition: (value: any, fullObj: ClientAddress) => {
      if(fullObj.addressType === "PreviousAddress") {
        return validateZipCode(value);
      }
    },
    group: WARNING_GROUPS.general,
    carriers: []
  },
  {
    label: 'Client Current Address Zip code is not Valid',
    condition: (value: any, fullObj: ClientAddress) => {
      if(fullObj.addressType === "StreetAddress") {
        return validateZipCode(value);
      }
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }
]
};

export const WARNINGS_DEFINITIONS_INFO_CLIENT_ADDRESSES_FOR_DWELLING: WarningDefinitionI[] = [
  clientInfoAddrZip
];


// Client Contact Methods
// ------------------------------------------------------------------------------

/**
 * Validation for Client Info Addresses Data
 * For Model: ClientContactMethod
 */

function generateContactMethodViewFieldId(fullObj: ClientContactMethod): string {
  return fullObj.type ? 'cliContact' + fullObj.type : 'no_id';
}

const clientInfoContactMethods:WarningDefinitionI = {
  id: 'value',
  deepId: 'value',
  viewUri: generateViewUrl,
  viewFieldId: generateContactMethodViewFieldId, //'cliContactEmail',
  warnings: [{
    label: (val: string, fullObj: ClientContactMethod) => 'Client\'s Phone number incorrect: ' + fullObj.type,
    condition: (value: string, fullObj: ClientContactMethod) => {

      switch (fullObj.type) {
        case 'MobilePhone':
        case 'HomePhone':
        case 'BusinessPhone':
          if (value && !Validate.isValidPhoneNumber(value)) {
            return true
          }
          break;
        default:
          return false;
      }

      return false;
    },
    group: WARNING_GROUPS.general,
    carriers: [],
  },
  {
    label: (val: string, fullObj: ClientContactMethod) => 'Client\'s Email address incorrect.',
    condition: (value: string, fullObj: ClientContactMethod) => {
      if (fullObj.type === 'Email' && value && !Validate.isValidEmail(value)) {
        return true;
      }

      return false;
    },
    group: WARNING_GROUPS.general,
    carriers: [],
  },
  {
    label: (val: string, fullObj: ClientContactMethod) => 'Client\'s Fax Number incorrect.',
    condition: (value: string, fullObj: ClientContactMethod) => {
      if (fullObj.type === 'Fax' && value && !Validate.isValidPhoneNumber(value)) {
        return true;
      }

      return false;
    },
    group: WARNING_GROUPS.general,
    carriers: [],
  }]
}


export const WARNINGS_DEFINITIONS_INFO_CLIENT_CONTACT_METHODS_FOR_DWELLING: WarningDefinitionI[] = [
  clientInfoContactMethods
];
