import { LookupsService } from '../../../app-services/lookups.service';
import { PlansService } from '../../../app-services/plans.service';
import { SymbolsService } from '../../../app-services/symbols.service';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { RouterTestingModule } from '@angular/router/testing';

import { StubAutocompleteComponent } from 'testing/stubs/components/autocomplete.component';
import { StubModalboxComponent } from 'testing/stubs/components/modalbox.component';

import { CoveragesService } from 'app/dashboard/app-services/coverages.service';
import { LocationsService } from 'app/dashboard/app-services/locations.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { VehiclesService } from 'app/dashboard/app-services/vehicles.service';
import { OverlayModule } from 'app/overlay/overlay.module';
import { BroadcastService } from 'app/shared/services/broadcast.service';
import { CurrentPageService } from 'app/shared/services/current-page.service';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { StorageService } from 'app/shared/services/storage-new.service';

import { CoveragesNavComponent } from './coverages-nav.component';
import { AutoCoveragesCopyCoveragesComponent } from '../auto-coverages-copy-coverages/auto-coverages-copy-coverages.component';
import { OptionsService } from 'app/dashboard/app-services/options.service';


let component: CoveragesNavComponent;
let fixture: ComponentFixture<CoveragesNavComponent>;

describe('CoveragesNavComponent', () => {
  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [
        RouterTestingModule,
        FormsModule,
        OverlayModule
      ],
      declarations: [ CoveragesNavComponent, StubModalboxComponent, StubAutocompleteComponent, AutoCoveragesCopyCoveragesComponent ],
      providers: [
        BroadcastService,
        CurrentPageService,
        LocationsService,
        StorageService,
        VehiclesService,
        CoveragesService,
        SpecsService,
        OverlayLoaderService,
        OptionsService,
        SymbolsService,
        PlansService,
        LookupsService,
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(CoveragesNavComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

});
