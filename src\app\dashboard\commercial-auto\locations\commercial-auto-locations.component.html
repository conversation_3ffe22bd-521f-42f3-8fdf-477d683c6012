<section class="section section--compact u-spacing--0-5">
  <div>
    <div class="row u-spacing--1">
      <div class="col-xs-12">
        <div class="box box--silver">
          <form name="locationsForm" autocomplete="off">
            <table class="form-table">
              <tr class="form-table__row" *ngFor="let location of locations; let index = index; trackBy: trackByFn">
                <td class="form-table__cell  u-width-150px">
                  <input fieldAutofocus type="text" [name]="'address1_'+index" [id]="'address1_'+index" [(ngModel)]="location.address1"
                    placeholder="Street Address" (change)="onLocationDataChangeUpdate(location)">
                </td>
                <td class="form-table__cell  u-width-150px">
                  <input type="text" [name]="'address2_'+index" [id]="'address2_'+index" [(ngModel)]="location.address2"
                    placeholder="Additional Address" (change)="onLocationDataChangeUpdate(location)">
                </td>
                <td class="form-table__cell  u-width-120px">
                  <sm-autocomplete #refVehicleCity [options]="optionsCity" [activeOption]="location.city"
                    [caseSensitiveOptionsIdMatching]="false" [(ngModel)]="location.city" [name]="'vehicleLocationCity_'+index"
                    [id]="'vehicleLocationCity_'+index" [searchFromBegining]="false" [allowEmptyValue]="false"
                    [allowCustomText]="false" [delayedOptions]="true"
                    (onSelect)="onLocationDataChangeSelect(location,$event,'city')" placeholder="City">
                  </sm-autocomplete>
                </td>
                <td class="form-table__cell  u-width-100px">
                  <sm-autocomplete #refVehicleState [options]="optionsState" [activeOption]="location.state"
                    [css]="(refVehicleState.ngModel?.invalid) ? 'error' : ''" [name]="'vehicleLocationState_'+index"
                    [id]="'vehicleLocationState_'+index" [searchFromBegining]="false" [allowEmptyValue]="false"
                    [allowCustomText]="false" [readonly]="fieldLocationStateDisabled" [required]="true"
                    (onSelect)="onLocationDataChangeSelect(location,$event,'state')" placeholder="State">
                  </sm-autocomplete>
                </td>
                <td class="form-table__cell  u-width-80px">
                  <input type="text" [name]="'zip_'+index" [id]="'zip_'+index" [(ngModel)]="location.zip" placeholder="Zipcode"
                    (change)="onLocationDataChangeUpdate(location)">
                </td>
                <td class="form-table__cell">
                  <button *ngIf="index>0 && locations?.length > 1" (click)="selectLocationForDelete(index)" type="button"
                    class="o-btn o-btn--action o-btn--i_cancel u-t-size--1-7rem modal-delete-location" tabindex="-1"></button>
                </td>
              </tr>
            </table>
          </form>
          <div class="u-spacing--1">
            <button (click)="addLocation()" class="o-btn o-btn--action o-btn--i_round-plus" id="add-driver-modal">
              Add Additional Location
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<app-confirmbox [launcher]="'.modal-delete-location'" [question]="'Are you sure you want to delete this location?'"
  [confirmBtnText]="'Yes, delete location'" (onAccept)="confirmDelete()" (onCancel)="confirmCanceled($event)">
</app-confirmbox>
