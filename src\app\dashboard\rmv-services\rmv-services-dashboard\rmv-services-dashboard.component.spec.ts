import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { RmvServicesDashboardComponent } from './rmv-services-dashboard.component';

describe('RmvServicesDashboardComponent', () => {
  let component: RmvServicesDashboardComponent;
  let fixture: ComponentFixture<RmvServicesDashboardComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ RmvServicesDashboardComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(RmvServicesDashboardComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
