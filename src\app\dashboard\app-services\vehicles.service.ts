import {empty as observableEmpty,  Observable, empty, throwError } from 'rxjs';
import {catchError, map} from 'rxjs/operators';
import { RateResponseMessage } from './../../app-model/quote';
import { CommercialVehicle, CommercialClassCodeLookup } from './../../app-model/vehicle';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ApiService } from 'app/shared/services/api.service';
import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Vehicle, VehicleGeneralDetails, VehicleSpecificDetails, VehicleLocationDataForVehicle, VehicleRMVLookupData } from 'app/app-model/vehicle';
import { LocationData } from 'app/app-model/location';
import { LocationsService } from 'app/dashboard/app-services/locations.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { CoverageApiResponseData } from 'app/app-model/coverage';
import { FilterOption } from 'app/app-model/filter-option';
import { CommercialVehicleForm } from '../commercial-auto/vehicles/commercial-auto-vehicles/commercial-auto-vehicles.component';

interface observerDataInterface {
  action: string;
  success: boolean;
  data: any;
}

export interface VehicleTypesI {
  privatePassenger: string;
  pickup: string;
  motorcycle: string;
  trailer: string;
  commercial: string;
}

export const VEHICLE_DETAILS_DESCRIPTION_FIELDS = [
  'bodyDescription',
  'bodyStyleDescription',
  'engineSizeDescription',
  'cylinderNumDescription',
  'engineDescription',
  'modelDescription',
  'otherDescription',
  'restraintDescription',
  'transmissionDescription',
  'vin'
];

export const VEHICLE_TYPES: VehicleTypesI = {
  privatePassenger: 'Private Passenger',
  pickup: 'Pickup',
  motorcycle: 'Motorcycle',
  trailer: 'Trailer',
  commercial: 'Commercial'

};

export const VEHICLE_OPTIONS_MAKE_MOTORCYCLE: FilterOption[] = [
  { id: 'Motorcycle', text: 'Motorcycle'},
  { id: 'BMW', text: 'BMW'},
  { id: 'Ducati', text: 'Ducati'},
  { id: 'Harley Davidson', text: 'Harley Davidson'},
  { id: 'Honda', text: 'Honda'},
  { id: 'Husqvarna', text: 'Husqvarna'},
  { id: 'Kawasaki', text: 'Kawasaki'},
  { id: 'Suzuki', text: 'Suzuki'},
  { id: 'Triumph', text: 'Triumph'},
  { id: 'Yamaha', text: 'Yamaha'}
];

export const VEHICLE_OPTIONS_MAKE_TRAILER: FilterOption[] = [
  { id: 'Trailer', text: 'Trailer'}
];


@Injectable()
export class VehiclesService {

  constructor(
    private http: HttpClient,
    private apiService: ApiService,
    private quotesService: QuotesService,
    private storageService: StorageService,
    private locationsService: LocationsService,
    private apiCommonService: ApiCommonService
  ) { }

  // public selectedRMVvehicle: VehicleRMVLookupData;

  private _selectedRMVvehicle: VehicleRMVLookupData;
  public get selectedRMVvehicle(): VehicleRMVLookupData {return this._selectedRMVvehicle; }
  public set selectedRMVvehicle(value: VehicleRMVLookupData) {
    this._selectedRMVvehicle = Object.freeze(value);
  }

  // Get Vehicles list
  public getVehiclesList(quoteID: string) {
    if (quoteID) {
      const uri = '/quotes/' + quoteID + '/vehicles';
      return this.getVehiclesListByUri(uri);
    } else {
      return empty();
    }
  }

  public getVehiclesListByUri(uri: string) {
    return this.apiCommonService.getByUri(uri);
  }

  // Create Vehicle
  public createVehicle(quoteID: string, vehicleData = {}, vehicleType = 'private', updateStorage = true): Observable<any> {
    let uriResourcePath = 'vehicles';
    if (vehicleType === 'commercial') {
      uriResourcePath = 'comvehicles';
    }

    const uri = '/quotes/' + quoteID + '/' + uriResourcePath;
    return this.createVehicleByUri(uri, vehicleData, updateStorage);
  }

  public createVehicleByUri(uri: string, vehicleData: any= {}, updateStorage = true): Observable<any> {
    return this.http.post(this.apiService.url(uri), vehicleData).pipe(map((res: any) => {
      if (updateStorage) {
        this.storageService.updateVehiclesListSingleItem(res.parentId, res);
      }
      return res;
    }));
  }


  // Get Vehicle by Id
  public getVehicle(quoteID: string, vehicleID: string): Observable<any> {
    const uri = '/quotes/' + quoteID + '/vehicles/' + vehicleID;
    return this.getVehicleByUri(uri);
  }

  public getVehicleByUri(uri: string): Observable<any> {
    return this.apiCommonService.getByUri(uri);
  }


  // Update Vehicle
  public updateVehicle(quoteID: string, vehicleID: string, vehicleData: Vehicle, updateStorage: boolean = true): Observable<any> {
    const uri = '/quotes/' + quoteID + '/vehicles/' + vehicleID;
    return this.updateVehicleByUri(uri, vehicleData, updateStorage);
  }


  // Update Commercial Vehicle
  public updateCommercialVehicle(quoteID: string, vehicleID: string, vehicleData: Vehicle, updateStorage: boolean = true): Observable<any> {
    const uri = '/quotes/' + quoteID + '/comvehicles/' + vehicleID;
    return this.updateVehicleByUri(uri, vehicleData, updateStorage);
  }

  public updateVehicleByUri(uri: string, vehicleData: Vehicle, updateStorage: boolean = true): Observable<any> {
    updateStorage && this.storageService.updateVehiclesListSingleItem(vehicleData.parentId, vehicleData);

    return this.apiCommonService.putByUri(uri, vehicleData).pipe(catchError(err => {
      if (err.status === 400 && err.error.message === 'Validation Failed') {
        alert(err.error.errors[0].message);
        return throwError(null);
      }
      return throwError(err);
    }));

  }

  // Delete vehicle
  public deleteVehicleById(quoteID: string, vehicleID: string): Observable<any> {
    const uri = '/quotes/' + quoteID + '/vehicles/' + vehicleID;

    return this.deleteVehicleByUri(uri);
  }

  public deleteVehicleByUri(uri: string): Observable<any> {
    return this.http.delete(this.apiService.url(uri)).pipe(map((response) => {
      const res = response ;

      const start = uri.indexOf('/quotes/');
      const endVehicle = uri.indexOf('/vehicles/');
      const quoteID = uri.substr(start + 8, endVehicle);
      const vehicleID = uri.substr(endVehicle + 10, 999);

      this.storageService.deleteVehiclesListSingleItem(vehicleID);
      return res;
    }));
  }
  public deleteComVehicleByUri(uri: string): Observable<any> {
    return this.http.delete(this.apiService.url(uri)).pipe(map((response) => {
      const res = response ;

      const start = uri.indexOf('/quotes/');
      const endVehicle = uri.indexOf('/comvehicles/');
      const quoteID = uri.substr(start + 11, endVehicle);
      const vehicleID = uri.substr(endVehicle + 10, 999);

      this.storageService.deleteVehiclesListSingleItem(vehicleID);
      return res;
    }));
  }

  public getVehicleStandardCoverages(uri: string): Observable<CoverageApiResponseData> {
    return this.apiCommonService.getByUri(uri);
  }

  public getVehicleAdditionalCoverages(uri: string): Observable<any> {
    return this.apiCommonService.getByUri(uri);
  }

  public calculateClassCode( vehicleData: CommercialClassCodeLookup): Observable<any> {
    const uri = '/lookups/vehicleclasscode';
    return this.apiCommonService.postByUri(uri, vehicleData);
}

  // Helper Methods
  // -------------------------------------------------------------------------------
  public vehicleNameToDisplay(vehicle: Vehicle): string {
    let name = '';
    name += vehicle.year ? vehicle.year + ' ' : '';
    name += vehicle.make ? vehicle.make + ' ' : '';
    name += vehicle.model ? vehicle.model.split(' ')[0] + ' ' : '';

    return name.trim() || 'New Vehicle';
  }


  // VIN Validation -- Less readable
  // ---------------------------------------------------------------------------
  // private vinTransliterate (c) {
  //   if (c) {
  //     c = c.toUpperCase();
  //   }
  //   return '0123456789.ABCDEFGH..JKLMN.P.R..STUVWXYZ'.indexOf(c) % 10;
  // }

  // private vinGet_check_digit (vin) {
  //     const map = '0123456789X';
  //     const weights = '8765432X098765432';
  //     let sum = 0;
  //     for (let i = 0; i < 17; ++i) {
  //         sum += this.vinTransliterate(vin[i]) * map.indexOf(weights[i]);
  //     }
  //     return map[sum % 11];
  // }

  // public vinValidate (vin) {
  //     if (!vin || vin.length !== 17) { return false; }
  //     return this.vinGet_check_digit(vin) === vin[8];
  // }


  public vinValidate (vin: string): boolean {
    return this.stepByStepVINValidation(vin);
     // ZACCJBBT0FPB65696
  }

  public plateValidate (plate) {
      if (!plate || plate.length < 5) { return false; }
      return true;
  }

  // VIN Validation - https://en.wikibooks.org/wiki/Vehicle_Identification_Numbers_(VIN_codes)/Check_digit
  // ---------------------------------------------------------------------------
  // 1 - Transliteration
  public vinTransliteration(vin: string): number[] {
    let transliteratedVin: number[] = [];
    const letters = {
      '1': 1, '2': 2, '3': 3, '4': 4, '5': 5, '6': 6, '7': 7, '8': 8, '9': 9,
      '0': 0, 'A': 1, 'B': 2, 'C': 3, 'D': 4, 'E': 5, 'F': 6, 'G': 7, 'H': 8,
      'J': 1, 'K': 2, 'L': 3, 'M': 4, 'N': 5, 'P': 7, 'R': 9, 'S': 2, 'T': 3,
      'U': 4, 'V': 5, 'W': 6, 'X': 7, 'Y': 8, 'Z': 9
    };

    transliteratedVin = Array.from(vin).map((l: string) => letters[l]);
    return transliteratedVin;
  }

  public vinComputeWeightedProducts(transliteratedVin: number[]): number[] {
    const weights: number[] = [8, 7, 6, 5, 4, 3, 2, 10, 0, 9, 8, 7, 6, 5, 4, 3, 2];
    let weightedProduct: number[] = [];
    weightedProduct = transliteratedVin.map((n: number, i: number) => n * weights[i]);

    return weightedProduct;
  }

  public vinComputeSumWeight(computedWeights: number[]): number {
    return computedWeights.reduce((prev: number, current: number) => prev + current);
  }

  public vinGetRemainder(computedSumWeight: number): number {
    return computedSumWeight % 11;
  }

  public vinGetChecksum(computedSumWeightRemainder: number): string {
    return computedSumWeightRemainder <= 9 ? computedSumWeightRemainder.toString() : 'X';
  }

  public stepByStepVINValidation(vin: string): boolean {
    if (!vin || vin.length < 17) { return false; }
    if (vin === '11111111111111111') { return false; }
    if (!vin.match('^([0-9a-hj-npr-zA-HJ-NPR-Z]{10,17})+$')) { return false; }

    const vinToCheck = vin.toUpperCase();
    const invalidVinLetters = ['I', 'O', 'Q'];

    if (Array.from(vinToCheck).some((l: string) => invalidVinLetters.indexOf(l) !== -1)) {
      return false;
    }

    // 1. Transliteration
    const transliteratedVin = this.vinTransliteration(vinToCheck);

    // 2. Compute Weighted Products
    const computedWeights: number[] = this.vinComputeWeightedProducts(transliteratedVin);

    // 3. Compute Sum of Weighted Products
    // const sum: number = computedWeights.reduce((prev: number, current: number) => prev + current);
    const sum: number = this.vinComputeSumWeight(computedWeights);

    // 4. Compute Remainder
    // const remainder = sum % 11;
    const remainder = this.vinGetRemainder(sum);

    // 5. Compare Remainder to Check Digit
    // const checkSum = remainder <= 9 ? remainder.toString() : 'X';
    const checkSum = this.vinGetChecksum(remainder);

    return checkSum === vinToCheck.charAt(8);
  }


  // Concatenate Vehicle Detail Description fields
  // ---------------------------------------------------------------------------
  private helperConcatenateFieldsToString(inObject: any, arrPropertiesToCheck: string[], showVin: boolean = true): string {
    let strConatenated = '';
    arrPropertiesToCheck.forEach(val => {
      if (val in inObject && inObject[val]) {
        if (val !== 'vin') {
          strConatenated += inObject[val].trim() + ' ';
        } else {
          if (showVin) { // added logic to not show vin for other scenarios like Vehicle Detail Report
            strConatenated += '(' + inObject[val].trim().replace(/&/g, '&amp;') + ')';
          }
        }
      }
    });

    return strConatenated.trim();
  }

  public concatVehicleDetailDescriptionFields(generalDetail: VehicleGeneralDetails, showVin: boolean = true): string {
    return this.helperConcatenateFieldsToString(generalDetail.detail, VEHICLE_DETAILS_DESCRIPTION_FIELDS, showVin);
  }

  public convertLocationToLocationForVehicle(location: LocationData, vehicle: Vehicle): VehicleLocationDataForVehicle {
    const tmpLocation: VehicleLocationDataForVehicle = new VehicleLocationDataForVehicle();
    tmpLocation.vehicleResourceId = vehicle.resourceId;
    tmpLocation.vehicleQuoteSessionId = vehicle.quoteSessionId;
    tmpLocation.vehicleMeta = Object.assign({}, vehicle.meta);
    tmpLocation.vehicleYear = vehicle.year;
    tmpLocation.vehicleMake = vehicle.make;
    tmpLocation.vehicleModel = vehicle.model;
    tmpLocation.location = Object.assign({}, location);
    return tmpLocation;
  }

}
