export class MetaData {
  public href = '';
  public rel?: string[];
}

export class ApiResponse<T = any> {
  public first: MetaData = null;
  public last: MetaData = null;
  public limit = 0;
  public meta = new MetaData();
  public next: MetaData = null;
  public offset = 0;
  public previous: MetaData = null;
  public resourceName: string = null;
  public size = 0;
  public items: T[] = [];
}

export class Resource {
  public meta: MetaData = new MetaData();
  public quoteSessionId = '';
  public resourceId = '';
  public parentId?;
  public resourceName: string = null;

  constructor() {

  }
}

export class Collection<T = any> extends Resource {
  public items: T[] = [];
}
