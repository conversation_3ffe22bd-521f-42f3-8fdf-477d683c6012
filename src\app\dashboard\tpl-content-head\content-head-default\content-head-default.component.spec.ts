import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';

import { MockCurrentPageServiceProvider } from 'testing/stubs/services/current-page.service.provider';

import { ContentHeadDefaultComponent } from './content-head-default.component';

describe('Component: ContentHeadDefault', () => {
  let component: ContentHeadDefaultComponent;
  let fixture: ComponentFixture<ContentHeadDefaultComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      declarations: [ ContentHeadDefaultComponent ],
      providers: [
        MockCurrentPageServiceProvider
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ContentHeadDefaultComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
