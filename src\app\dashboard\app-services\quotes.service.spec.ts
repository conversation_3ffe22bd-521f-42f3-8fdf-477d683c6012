import { HttpClient } from '@angular/common/http';
import { fakeAsync, inject, TestBed, tick } from '@angular/core/testing';

import { data as QUOTE_GUIDELINE_OVERRIDES } from 'testing/data/quotes/guideline-overrides';
import { data as QUOTE_OUTBOUND } from 'testing/data/quotes/outbound';
import { data as QUOTE_POLICY_HISTORY } from 'testing/data/quotes/policy-history';
import { AUTO_QUOTE as AUTO_QUOTE } from 'testing/data/quotes/quote-auto';
import { DWELLING_QUOTE } from 'testing/data/quotes/quote-dwelling';
import { HOME_QUOTE } from 'testing/data/quotes/quote-home';
import { AUTO_QUOTE_PLAN_LIST as QUOTE_PLANS_LIST } from 'testing/data/quotes/quote-plan-list/auto';
import { data as QUOTES } from 'testing/data/quotes/quotes';
import {
    DataCustomMatchers, expect, expectLastCallArgs, expectLastConnectionPayload,
    expectLastConnectionUrl
} from 'testing/helpers/all';
import { MockBackend, setupMockBackend } from 'testing/setups/mock-backend';

import { Quote, QuoteAuto, QuoteDwelling, QuoteHome } from 'app/app-model/quote';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { ApiService } from 'app/shared/services/api.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Helpers } from 'app/utils/helpers';

import { IMPORTED_QUOTE, QuotesService } from './quotes.service';
import { LeaveQuoteService } from 'app/shared/services/leave-quote.service';
import { OverlayRouteService } from 'app/overlay/services/overlay-route.service';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { RouterTestingModule } from '@angular/router/testing';

describe('Service: Quotes', () => {
  let service: QuotesService;
  let mockBackend: MockBackend;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [ RouterTestingModule ],
      providers: [
        QuotesService,
        StorageService,
        OverlayRouteService,
        OverlayLoaderService,
        LeaveQuoteService
      ]
    });

    jasmine.addMatchers(DataCustomMatchers);
    jasmine.clock().mockDate(new Date(2017, 3, 27));
  });

  beforeEach(inject(
    [QuotesService],
    (_service: QuotesService) => {
      service = _service;
    }));

  it('can instantiate service when inject service', () => {
    expect(service).toBeTruthy();
  });

  it('can retrieve data from any URI', fakeAsync(() => {
    mockBackend = setupMockBackend({
      success: true
    });

    service.getDataByUrl('any-uri').subscribe();
    tick();

    expectLastConnectionUrl(mockBackend).toEndWith('any-uri');
  }));

  describe('when selectedQuote is available in storage', () => {
    beforeEach(fakeAsync(inject([StorageService], (storageService: StorageService) => {
      storageService.setStorageData('selectedQuote', Helpers.deepClone(AUTO_QUOTE));
    })));

    it('should correctly set newQuote property', inject([QuotesService], (_service: QuotesService) => {
      expect(_service.newQuote).toEqual(AUTO_QUOTE);
    }));
  });

  describe('when retrieving quotes list', () => {
    beforeEach(() => {
      mockBackend = setupMockBackend(QUOTES);
    });

    it('works with default params', fakeAsync(() => {
      service.getQuotes().subscribe();
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith(
        '/quotes?offset=0&limit=10&name=&agentId=&lob=&locationId=&dateType=&startDate=&endDate=&clientIdentifier='
      );
    }));

    it('sends correct params to the server', fakeAsync(() => {
      service.getQuotes(
        '20', '20', 'Smith', 'agent-id', 'home', 'location-id', 'date-type', '2015-01-01', '2017-11-30', 'client-id'
      ).subscribe();
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith(
        '/quotes?offset=20&limit=20&name=Smith&agentId=agent-id&lob=home&locationId=location-id&dateType=date-type'
        + '&startDate=2015-01-01&endDate=2017-11-30&clientIdentifier=client-id'
      );
    }));
  });

  describe('when creating a new quote', () => {
    it('correctly sets effective and expiration dates if they are missing', fakeAsync(() => {
      const quote = new Quote();

      mockBackend = setupMockBackend(quote);

      service.createNewQuote(quote).subscribe();
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes');
      expectLastConnectionPayload(mockBackend).toEqual(jasmine.objectContaining({
        effectiveDate: '2017-04-27',
        expirationDate: '2018-04-27'
      }));
    }));

    it('correctly sets the newQuote field', fakeAsync(() => {
      const quote = new Quote();
      const responseQuote = new Quote();
      responseQuote.quoteIdentifier = 'abcde';

      mockBackend = setupMockBackend(responseQuote);

      service.createNewQuote(quote).subscribe();
      mockBackend.run();
      tick();

      expect(service.newQuote).toEqualIgnoringTypes(responseQuote);
    }));

    it('works with QuoteAuto data', fakeAsync(() => {
      const quote = new QuoteAuto();
      quote.effectiveDate = '2017-11-16T00:00:00';
      quote.expirationDate = '2018-11-16T00:00:00';

      mockBackend = setupMockBackend(AUTO_QUOTE);

      service.createNewQuote(quote).subscribe();
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes');
      expectLastConnectionPayload(mockBackend).toEqual(quote);
    }));

    it('works with QuoteHome data', fakeAsync(() => {
      const quote = new QuoteHome();
      quote.effectiveDate = '2017-11-16T00:00:00';
      quote.expirationDate = '2018-11-16T00:00:00';

      mockBackend = setupMockBackend(HOME_QUOTE);

      service.createNewQuote(quote).subscribe();
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes');
      expectLastConnectionPayload(mockBackend).toEqual(quote);
    }));

    it('works with QuoteDwelling data', fakeAsync(() => {
      const quote = new QuoteDwelling();
      quote.effectiveDate = '2017-11-16T00:00:00';
      quote.expirationDate = '2018-11-16T00:00:00';

      mockBackend = setupMockBackend(DWELLING_QUOTE);

      service.createNewQuote(quote).subscribe();
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes');
      expectLastConnectionPayload(mockBackend).toEqual(quote);
    }));
  });

  describe('when updating quote effective/expiration date', () => {
    it('works with QuoteAuto data', fakeAsync(() => {
      const quote: QuoteAuto = Helpers.deepClone(AUTO_QUOTE);

      mockBackend = setupMockBackend(quote);

      service.updateQuoteInfo('quote-id', quote).subscribe();
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/info');
      expectLastConnectionPayload(mockBackend).toEqual(quote);
    }));

    it('works with QuoteHome data', fakeAsync(() => {
      const quote: QuoteHome = Helpers.deepClone(HOME_QUOTE);

      mockBackend = setupMockBackend(quote);

      service.updateQuoteInfo('quote-id', quote).subscribe();
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/info');
      expectLastConnectionPayload(mockBackend).toEqual(quote);
    }));

    it('works with QuoteDwelling data', fakeAsync(() => {
      const quote: QuoteDwelling = Helpers.deepClone(DWELLING_QUOTE);

      mockBackend = setupMockBackend(quote);

      service.updateQuoteInfo('quote-id', quote).subscribe();
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/info');
      expectLastConnectionPayload(mockBackend).toEqual(quote);
    }));
  });

  describe('when saving quote', () => {
    it('works with QuoteAuto data', fakeAsync(() => {
      const quote: QuoteAuto = Helpers.deepClone(AUTO_QUOTE);

      mockBackend = setupMockBackend(quote);

      service.saveQuote('/quotes/quote-id', quote).subscribe();
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id');
      expectLastConnectionPayload(mockBackend).toEqual(quote);
    }));

    it('works with QuoteHome data', fakeAsync(() => {
      const quote: QuoteHome = Helpers.deepClone(HOME_QUOTE);

      mockBackend = setupMockBackend(quote);

      service.saveQuote('/quotes/quote-id', quote).subscribe();
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id');
      expectLastConnectionPayload(mockBackend).toEqual(quote);
    }));

    it('works with QuoteDwelling data', fakeAsync(() => {
      const quote: QuoteDwelling = Helpers.deepClone(DWELLING_QUOTE);

      mockBackend = setupMockBackend(quote);

      service.saveQuote('/quotes/quote-id', quote).subscribe();
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id');
      expectLastConnectionPayload(mockBackend).toEqual(quote);
    }));
  });

  describe('when saving quote copy', () => {
    it('works with QuoteAuto data', fakeAsync(() => {
      const quote: QuoteAuto = Helpers.deepClone(AUTO_QUOTE);

      mockBackend = setupMockBackend(quote);

      service.saveAsQuote('/quotes/quote-id', quote).subscribe();
      tick();

      const quoteCopy = Helpers.deepClone(quote);
      quoteCopy.quoteIdentifier = '';

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/saveas');
      expectLastConnectionPayload(mockBackend).toEqual(quoteCopy);
    }));

    it('works with QuoteHome data', fakeAsync(() => {
      const quote: QuoteHome = Helpers.deepClone(HOME_QUOTE);

      mockBackend = setupMockBackend(quote);

      service.saveAsQuote('/quotes/quote-id', quote).subscribe();
      tick();

      const quoteCopy = Helpers.deepClone(quote);
      quoteCopy.quoteIdentifier = '';

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/saveas');
      expectLastConnectionPayload(mockBackend).toEqual(quoteCopy);
    }));

    it('works with QuoteDwelling data', fakeAsync(() => {
      const quote: QuoteDwelling = Helpers.deepClone(DWELLING_QUOTE);

      mockBackend = setupMockBackend(quote);

      service.saveAsQuote('/quotes/quote-id', quote).subscribe();
      tick();

      const quoteCopy = Helpers.deepClone(quote);
      quoteCopy.quoteIdentifier = '';

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/saveas');
      expectLastConnectionPayload(mockBackend).toEqual(quoteCopy);
    }));
  });

  describe('when working with quote plans', () => {
    it('allows to create quote plans', fakeAsync(() => {
      const plans = Helpers.deepClone(QUOTE_PLANS_LIST);

      mockBackend = setupMockBackend(plans);

      // why is this called like that instead of updateQuotePlanList or sth?
      service.updateQuoteByUrl('/quotes/quote-id/quotePlanList', plans, true).subscribe();
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/quotePlanList');
      expectLastConnectionPayload(mockBackend).toEqual(plans);
    }));

    it('allows to update quote plans', fakeAsync(() => {
      const plans = Helpers.deepClone(QUOTE_PLANS_LIST);

      mockBackend = setupMockBackend(plans);

      service.updateQuoteByUrl('/quotes/quote-id/quotePlanList', plans, false).subscribe();
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/quotePlanList');
      expectLastConnectionPayload(mockBackend).toEqual(plans);
    }));
  });

  describe('when working with policy history', () => {
    beforeEach(() => {
      mockBackend = setupMockBackend(QUOTE_POLICY_HISTORY);
    });

    it('allows to create quote policy history', fakeAsync(() => {
      service.policyHistoryCreateByUri('/quotes/quote-id/policyhistory', QUOTE_POLICY_HISTORY.items[0]).subscribe();
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/policyhistory');
      expectLastConnectionPayload(mockBackend).toEqual(QUOTE_POLICY_HISTORY.items[0]);
    }));

    it('allows to update quote policy history', fakeAsync(() => {
      service.policyHistoryUpdateByUri('/quotes/quote-id/policyhistory/policy-history-id', QUOTE_POLICY_HISTORY.items[0]).subscribe();
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/policyhistory/policy-history-id');
      expectLastConnectionPayload(mockBackend).toEqual(QUOTE_POLICY_HISTORY.items[0]);
    }));

    it('allows to retrieve quote policy history', fakeAsync(() => {
      service.policyHistoryGetListByUri('/quotes/quote-id/policyhistory').subscribe();
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/policyhistory');
    }));
  });

  describe('when working with guideline overrides', () => {
    it('allows to create quote guideline overrides', fakeAsync(() => {
      mockBackend = setupMockBackend(QUOTE_GUIDELINE_OVERRIDES);

      service.createGuidelineOverrides('quote-id', QUOTE_GUIDELINE_OVERRIDES.items).subscribe();
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/overrides');
      expectLastConnectionPayload(mockBackend).toEqual({ items: QUOTE_GUIDELINE_OVERRIDES.items });
    }));

    it('allows to update quote guideline overrides', fakeAsync(() => {
      mockBackend = setupMockBackend(QUOTE_GUIDELINE_OVERRIDES.items[0]);

      service.updateGuidelineOverrides('quote-id', QUOTE_GUIDELINE_OVERRIDES.items[0]).subscribe();
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/overrides/b0b4ce58-98c2-4280-8461-3bc5acc2eec7');
      expectLastConnectionPayload(mockBackend).toEqual(QUOTE_GUIDELINE_OVERRIDES.items[0]);
    }));

    it('allows to retrieve quote guideline overrides', fakeAsync(() => {
      mockBackend = setupMockBackend(QUOTE_GUIDELINE_OVERRIDES.items[0]);

      service.getGuidelineOverrides('quote-id').subscribe();
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/overrides');
    }));

    it('allows to submit quote (?)', fakeAsync(() => {
      mockBackend = setupMockBackend(QUOTE_GUIDELINE_OVERRIDES.items[0]);

      service.quoteSubmit('any-uri').subscribe();
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith('/any-uri');
    }));
  });

  describe('when importing/exporting quotes', () => {
    it('allows to get imported quote', fakeAsync(() => {
      mockBackend = setupMockBackend(AUTO_QUOTE);

      service.getImportQuoteUrl('imported-quote-id').subscribe();
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/imported-quote-id/inbound');
    }));

    it('allows to export quote to selected vendor', fakeAsync(() => {
      mockBackend = setupMockBackend(QUOTE_OUTBOUND);

      service.exportQuote('exported-quote-id', 'vendor-id', 'rate-response-id', 'response-index').subscribe();
      tick();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/exported-quote-id/outbound?vendorId=vendor-id&rateresponseId=rate-response-id&responseIndex=response-index&requestId=');
    }));

    it('allows to set importedQuote flag in session storage', () => {
      service.setIsImportedQuote(true);
      expect(sessionStorage.getItem(IMPORTED_QUOTE)).toEqual('{"isImported":true}');

      service.setIsImportedQuote(false);
      expect(sessionStorage.getItem(IMPORTED_QUOTE)).toEqual('{"isImported":false}');

      service.setIsImportedQuote();
      expect(sessionStorage.getItem(IMPORTED_QUOTE)).toEqual('{"isImported":true}');
    });

    it('allows to monitor importedQuote flag status', fakeAsync(() => {
      const spy = jasmine.createSpy('isImportedSpy');
      service.isImportedQuote$.subscribe(spy);

      service.setIsImportedQuote(true);
      tick();
      expectLastCallArgs(spy).toEqual([{
        isImported: true
      }]);

      service.setIsImportedQuote(false);
      tick();
      expectLastCallArgs(spy).toEqual([{
        isImported: false
      }]);
    }));

    it('correctly handles pre-existing importedQuote flag', fakeAsync(inject(
      [HttpClient, ApiService, StorageService, ApiCommonService],
      (http: HttpClient, apiService: ApiService,
        storageService: StorageService, apiCommonService: ApiCommonService,
        overlayRouteService: OverlayRouteService, overlayLoaderService: OverlayLoaderService,
        leaveQuoteService: LeaveQuoteService) => {
      localStorage.setItem(IMPORTED_QUOTE, '{"isImported":true}');

      const newService = new QuotesService(
        http,
        apiService,
        storageService,
        apiCommonService,
        overlayRouteService,
        overlayLoaderService,
        leaveQuoteService
      );
      const spy = jasmine.createSpy('isImportedSpy');
      newService.isImportedQuote$.subscribe(spy);
      tick();

      expectLastCallArgs(spy).toEqual([{
        isImported: true
      }]);
    })));
  });

  describe('when working with Quote form types', () => {
    it('allows to convert form types string to array', () => {
      expect(service.simplifyFormType('HO3, HO 5')).toEqual(['HO5']);
      expect(service.simplifyFormType('HO 3')).toEqual(['HO3']);
      expect(service.simplifyFormType('ho5')).toEqual(['HO5']);

      expect(service.simplifyFormType('HO 2')).toEqual(['HO2']);
      expect(service.simplifyFormType('ho 4')).toEqual(['HO4']);
      expect(service.simplifyFormType('HO6')).toEqual(['HO6']);

      expect(service.simplifyFormType('DP1')).toEqual(['DP1']);
      expect(service.simplifyFormType('DP2')).toEqual(['DP2']);
      expect(service.simplifyFormType('DP3')).toEqual(['DP3']);

      expect(service.simplifyFormType('Custom')).toEqual(['Custom']);
    });

    it('works with QuoteHome data', () => {
      expect(service.getQuoteSelectedFormTypes(new QuoteHome())).toEqual([]);
      expect(service.getQuoteSelectedFormTypes(HOME_QUOTE)).toEqual(['HO3']);
    });

    it('works with QuoteDwelling data', () => {
      expect(service.getQuoteSelectedFormTypes(new QuoteDwelling())).toEqual([]);
      expect(service.getQuoteSelectedFormTypes(DWELLING_QUOTE)).toEqual(['DP3']);
    });
  });
});
