import { fakeAsync, inject, TestBed } from '@angular/core/testing';

import {
    DataCustomMatchers, expectLastConnectionPayload, expectLastConnectionUrl
} from 'testing/helpers/all';
import { setupMockBackend } from 'testing/setups/mock-backend';

import { DriverRmvToSendWithResourceId, RmvDriverResult } from 'app/app-model/driver';

import { RmvService } from './rmv.service';

describe('Service: Rmv', () => {
  let service: RmvService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        RmvService
      ]
    });

    jasmine.addMatchers(DataCustomMatchers);
  });

  beforeEach(inject(
    [RmvService],
    (_service: RmvService) => {
      service = _service;
    }));

  it('can instantiate service when inject service', () => {
    expect(service).toBeTruthy();
  });

  describe('when using generateRmvReportDriverId helper', () => {
    it('can generate driver id from RmvDriverResult', () => {
      const driver = new RmvDriverResult();
      driver.firstName = 'mike';
      driver.lastName = 'oldfield';
      driver.licenseNumber = 's202020';

      expect(service.generateRmvReportDriverId(driver)).toEqual('mike-oldfield-s202020');
    });

    it('should ignore letter case when generating ID', () => {
      const driver = new RmvDriverResult();
      driver.firstName = 'Mike';
      driver.lastName = 'Oldfield';
      driver.licenseNumber = 'S202020';

      expect(service.generateRmvReportDriverId(driver)).toEqual('mike-oldfield-s202020');
    });
  });

  describe('when using updateDriversByLicense method', () => {
    it('should pass the data to the server', fakeAsync(() => {
      const mockBackend = setupMockBackend(null);

      const driver = new DriverRmvToSendWithResourceId(
        'resource-id', 'Mike', 'Oldfield', '1952-02-23T00:00:00', 'S202020'
      );
      service.updateDriversByLicense('quote-id', '2017-11-15T20:00:00', [ driver ]).subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/lookups/rmv/drivers/licenses');
      expectLastConnectionPayload(mockBackend).toEqual({
        quoteId: 'quote-id',
        policyEffectiveDate: '2017-11-15T20:00:00',
        drivers: [
          driver
        ]
      });
    }));
  });
});
