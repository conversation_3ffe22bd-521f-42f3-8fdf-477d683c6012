import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { CrossApplicationService } from 'app/shared/services/cross-application.service';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';

@Component({
  selector: 'app-admin-redirect',
  templateUrl: './admin-redirect.component.html',
  styleUrl: './admin-redirect.component.scss',
  standalone: false
})
export class AdminRedirectComponent implements OnInit {
   wildcardValue: string;

  constructor(
    private route: ActivatedRoute,
    private crossApplicationService: CrossApplicationService,
    private overlayLoader: OverlayLoaderService
  ) {
    this.wildcardValue = this.route.snapshot.paramMap.get('wildcard') || '';
  }

  ngOnInit() {
    this.overlayLoader.showLoader();
    debugger;
    this.crossApplicationService.openSinglePointSettingsUrl('User/ProfileEdit/' + this.wildcardValue, false);
  }
}
