import { Component, EventEmitter, HostListener, Input, OnInit, Output, ViewChild, ElementRef } from '@angular/core';

import { CalendarComponent, ICalendarViewDateSelected } from 'app/shared/components/calendar/calendar.component';
import { Datepicker } from './../datepicker/datepicker';
import { NgModel } from '@angular/forms';
import { format } from 'date-fns';

export interface IDatePickerInputDataEvent {
  date: Date;
  dateIso: string;
  day: number;
  formatedDate: string;
  month: number;
  selectByClick: boolean | undefined;
  selectedManually: boolean;
  timestamp: number;
  year: number;
}


const PLACEHOLDER_CHAR = '-'; // '\u2000'
const PLACEHOLDER_CHAR_REGEXP = new RegExp(PLACEHOLDER_CHAR, 'g');

const KEY_SLASH = 191;
const KEY_SLASH_NUM = 111;
const KEY_ENTER = 13;
const KEY_BACKSPACE = 8;
const KEY_ESC = 27;
const KEY_ARROW_LEFT = 37;
const KEY_ARROW_RIGHT = 39;

const KEY_A = 65;
const KEY_C = 67;

@Component({
    selector: 'app-datepicker-input',
    templateUrl: './datepicker-input.component.html',
    styleUrls: ['./datepicker-input.component.scss'],
    standalone: false
})
export class DatepickerInputComponent extends Datepicker implements OnInit {
private get selectedDateFormatedView(): string | null {
    return this.refCalendar.selectedDate ? format(new Date(this.refCalendar.selectedDate), this.viewDateFormat) : null;
}
  @Input() placeholder = '';
  @Input() name = '';
  @Input() id = '';
  @Input() required = false;
  @Input() disabled = false;

  @Output() onDateChange: EventEmitter<IDatePickerInputDataEvent> = new EventEmitter<IDatePickerInputDataEvent>();

  @ViewChild('refInputModel') public refInputModel: NgModel;
  @ViewChild('refModalInfo') public refModalInfo;
  @ViewChild('refTooltip') public refTooltip;
  @ViewChild('refCalendar') public refCalendar: CalendarComponent;
  @ViewChild('refInputPicker') public refInputPicker: ElementRef;
  public position: any;
  public selectedDateEditable = ''; // TODO::
  public errorInfoSelectedDate = '';

  private viewDateFormat = 'MM/dd/yyyy';

  public ngModel: NgModel;

  private timerModalboxOpen;
  private dateProvidedManually = false; // Indicates if the date has been provided manually by user (by keyboard or click) or it was set automatically
  public calendarPreviewDateFormat = 'MMM d, yyyy';


  // NEW INPUT MASK CONFIGURATION
  public inputMaskConfig = {
    mask: ['\d', '\d', '/', '\d', '\d', '/', '\d', '\d', '\d', '\d'],
    placeholder: 'MM/dd/yyyy'
  };
  ngOnInit() {
    this.initDatepicker();
    this.setIdAndNameOfDatepicker();
    setTimeout(() => {this.ngModel = this.refInputModel; });
  }

  private setIdAndNameOfDatepicker(): void {
    const strIdName = 'datepicker_' + this.generateUUID();

    this.id = this.id || strIdName;
    this.name = this.name || strIdName;
  }

  public onDateSelect($ev, refTooltip): void {
    this.rememberSavedDate();
    this.selectedDateEditable = this.selectedDateFormatedView || '';
    // console.log('%cDATE SELECT', 'background:pink', this.id,  this.selectedDate);

    this.registerDateChange($ev);

    // Do not close tooltip if date set by public method (which is called by navigation 'Today' button)
    if ($ev && $ev.additionalData && $ev.additionalData === 'setTodayDatePublicMethod') {
      return;
    }

    if (refTooltip && 'close' in refTooltip && $ev.selectByClick) {
      refTooltip.close();
    }
  }


  public registerDateChange($ev?): void {
    const updatedDate: IDatePickerInputDataEvent = {
      date: this.selectedDate,
      formatedDate: this.selectedDateFormated,
      dateIso: this.selectedDateIso,
      timestamp: this.selectedDateTimestamp,
      year: this.selectedYear,
      month: this.selectedMonth,
      day: this.selectedDay,
      selectByClick: ($ev) ? $ev.selectByClick : undefined,
      selectedManually: this.dateProvidedManually
    };
    // console.log('Date Has changed: ', updatedDate);
    this.onDateChange.emit(updatedDate);

    // new
    // this.dateProvidedManually = false;
  }

  public registerEmptyDateChange(): void {
    const updatedDate: IDatePickerInputDataEvent = {
      date: undefined,
      formatedDate: '',
      dateIso: '',
      timestamp: undefined,
      year: undefined,
      month: undefined,
      day: undefined,
      selectByClick: undefined,
      selectedManually: this.dateProvidedManually
    };

    this.onDateChange.emit(updatedDate);
    // new
    // this.dateProvidedManually = false;
  }

  public onInputMaskFieldBlur(maskIsComplete: boolean): void {
    // this.dateProvidedManually = true;
    this.checkEditableDateAndSaveIfValidOrEmpty(true);
    // setTimeout(() => {
    //   this.dateProvidedManually = false;
    // }, 10);
  }

  public onInputMaskKeyUp(maskIsComplete: boolean): void {
    this.dateProvidedManually = true;
    this.checkEditableDateAndSaveIfValidOrEmpty();

    setTimeout(() => {
      this.dateProvidedManually = false;
    }, 10);
  }

  private checkEditableDateAndSaveIfValidOrEmpty(showWarningOnBlur: boolean = false): void {
    const [month, day, year] = this.selectedDateEditable.replace(PLACEHOLDER_CHAR_REGEXP, '').split('/');
    const trimedDate = [month, day, year].join('/');

    if (trimedDate.length === 10) {
      const monthNo: number = parseInt(month, 10) - 1;
      const dayNo: number = parseInt(day, 10);
      const yearNo: number = parseInt(year, 10);

      const tmpDate = new Date(yearNo, monthNo, dayNo, 0, 0, 0);
      const tmpMonth = tmpDate.getMonth() + 1;

      // let dateZoneOffset = new Date().getTimezoneOffset();
      // let tmpDate;
      // let tmpMonth;

      // if(dateZoneOffset <= 0) {
      //   tmpDate = new Date(Date.UTC(yearNo, monthNo, dayNo, 0, 0, 0));
      //   tmpMonth = tmpDate.getUTCMonth() + 1;
      // } else {
      //   tmpDate = new Date(yearNo, monthNo, dayNo, 0, 0, 0);
      //   tmpMonth = tmpDate.getMonth() + 1;
      // }

      const tmpMonthStr: string = (tmpMonth < 10) ? '0' + tmpMonth : tmpMonth.toString();
      const tmpDay = tmpDate.getDate();
      const tmpDayStr = (tmpDay < 10) ? '0' + tmpDay : tmpDay.toString();
      const tmpStringDate = tmpMonthStr + '/' + tmpDayStr + '/' + tmpDate.getFullYear();

      if (tmpStringDate === trimedDate) {
        this.selectDate = tmpDate;
        // TODO:: providedManually
      } else {
        this.refInputModel.control.setErrors({
          'invalid-date': 'Inputted date is invalid'
        });

        if (showWarningOnBlur ) {
          this.errorInfoSelectedDate = trimedDate;
          this.timerModalboxOpen && clearTimeout(this.timerModalboxOpen);
          this.timerModalboxOpen = setTimeout(() => {
            if (!this.refTooltip.isOpen) {
              this.refModalInfo.openModalbox();
            }
          }, 100);
        }
      }
    } else {
      this.selectDate = null;
      this.registerEmptyDateChange();
    }
  }

  public actionOnModalboxErrorClose(ev, refInput): void {
    if (!ev.isOpen) {
              this.selectedDateEditable = '';
      this.timerModalboxOpen && clearTimeout(this.timerModalboxOpen);
      refInput.focus();
    }
  }

  public actionOnDatepickerTooltipClose(ev, refInput): void {
    if (!ev.isOpen) {
     refInput.focus();
    }
  }


  public focus(openCalendar: boolean = false): void {
    if (this.refInputPicker) {
      // this.refInputPicker.nativeElement.focus();
      setTimeout(() => this.refInputPicker.nativeElement.focus());

      if (openCalendar && this.refTooltip && 'open' in this.refTooltip) {
        this.refTooltip.open();
      }
    }
  }


  private generateUUID(): string {
    return Math.floor((1 + Math.random()) * 0x10000000000).toString(16);
  }

  // Calendar Navigation
  // ---------------------------------------------------------------------------
  public showSelectedDate(): void {
    if (this.refCalendar) {
      const result: ICalendarViewDateSelected = this.refCalendar.showSelectedDate();
      this.selectedYearString = result.year ? String(result.year) : '';
      this.selectedMonthNumber = result.month;
    }
  }

  public showCurrentMonth(): void {
    const result: ICalendarViewDateSelected = this.refCalendar.showCurrentMonth();
    this.selectedYearString = result.year ? String(result.year) : '';
    this.selectedMonthNumber = result.month;
  }

  public showPreviousMonth(): void {
    const result: ICalendarViewDateSelected = this.refCalendar.prevMonth();
    this.selectedYearString = result.year ? String(result.year) : '';
    this.selectedMonthNumber = result.month;
  }

  public showNextMonth(): void {
    const result: ICalendarViewDateSelected = this.refCalendar.nextMonth();
    this.selectedYearString = result.year ? String(result.year) : '';
    this.selectedMonthNumber = result.month;
  }

  public showPreviousYear(): void {
    const result: ICalendarViewDateSelected = this.refCalendar.showPrevYear();
    this.selectedYearString = result.year ? String(result.year) : '';
    this.selectedMonthNumber = result.month;
  }

  public showNextYear(): void {
    const result: ICalendarViewDateSelected = this.refCalendar.showNextYear();
    this.selectedYearString = result.year ? String(result.year) : '';
    this.selectedMonthNumber = result.month;
  }

  public selectTodayDate(): void {
    const result: ICalendarViewDateSelected = this.refCalendar.setTodayDate();
    this.selectedYearString = result.year ? String(result.year) : '';
    this.selectedMonthNumber = result.month;
  }

}
