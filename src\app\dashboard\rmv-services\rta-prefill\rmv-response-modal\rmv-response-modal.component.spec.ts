import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { RmvResponseModalComponent } from './rmv-response-modal.component';

describe('RmvResponseModalComponent', () => {
  let component: RmvResponseModalComponent;
  let fixture: ComponentFixture<RmvResponseModalComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ RmvResponseModalComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(RmvResponseModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
