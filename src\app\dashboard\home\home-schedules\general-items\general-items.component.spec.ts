import { async, ComponentFixture, fakeAsync, TestBed } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';

import { changeTextInputValue } from 'testing/helpers/all';
import { StubDwellingServiceProvider } from 'testing/stubs/services/dwelling.service.provider';
import {
    StubOverlayLoaderServiceProvider
} from 'testing/stubs/services/overlay-loader.service.provider';

import { StorageService } from 'app/shared/services/storage-new.service';

import { GeneralItemsComponent } from './general-items.component';

describe('Component: GeneralItems', () => {
  let component: GeneralItemsComponent;
  let fixture: ComponentFixture<GeneralItemsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [FormsModule],
      declarations: [GeneralItemsComponent],
      providers: [
        StorageService,
        StubDwellingServiceProvider,
        StubOverlayLoaderServiceProvider
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(GeneralItemsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });

  it('should destroy without errors', () => {
    expect(() => {
      fixture.destroy();
    }).not.toThrow();
  });

  it('should be able to handle cameras amount change', fakeAsync(() => {
    changeTextInputValue(
      fixture.debugElement.query(By.css('#cameras')).nativeElement,
      3,
      fixture
    );

    expect(component.propCameras.itemValueAmount).toEqual(3);
  }));

  it('should be able to handle fine arts amount change', fakeAsync(() => {
    changeTextInputValue(
      fixture.debugElement.query(By.css('#FineArts')).nativeElement,
      1,
      fixture
    );

    expect(component.propFineArts.itemValueAmount).toEqual(1);
  }));

  it('should be able to handle furs amount change', fakeAsync(() => {
    changeTextInputValue(
      fixture.debugElement.query(By.css('#furs')).nativeElement,
      2,
      fixture
    );

    expect(component.propFurs.itemValueAmount).toEqual(2);
  }));

  it('should be able to handle golf equipment amount change', fakeAsync(() => {
    changeTextInputValue(
      fixture.debugElement.query(By.css('#golfEquipment')).nativeElement,
      1,
      fixture
    );

    expect(component.propGolfEquipment.itemValueAmount).toEqual(1);
  }));

  it('should be able to handle guns amount change', fakeAsync(() => {
    changeTextInputValue(
      fixture.debugElement.query(By.css('#guns')).nativeElement,
      1,
      fixture
    );

    expect(component.propGuns.itemValueAmount).toEqual(1);
  }));

  it('should be able to handle musical instruments amount change', fakeAsync(() => {
    changeTextInputValue(
      fixture.debugElement.query(By.css('#musicalInstruments')).nativeElement,
      2,
      fixture
    );

    expect(component.propMusicalInstruments.itemValueAmount).toEqual(2);
  }));

  it('should be able to handle silverware amount change', fakeAsync(() => {
    changeTextInputValue(
      fixture.debugElement.query(By.css('#silverware')).nativeElement,
      12,
      fixture
    );

    expect(component.propSilverware.itemValueAmount).toEqual(12);
  }));

  it('should be able to handle stamps amount change', fakeAsync(() => {
    changeTextInputValue(
      fixture.debugElement.query(By.css('#stamps')).nativeElement,
      18,
      fixture
    );

    expect(component.propStamps.itemValueAmount).toEqual(18);
  }));
});
