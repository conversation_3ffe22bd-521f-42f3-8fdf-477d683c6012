import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ApiService } from 'app/shared/services/api.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';

import { EstampRequest } from 'app/app-model/estamp-Request';
import { map } from 'rxjs/operators';




@Injectable()
export class EstampService {

  constructor(
    private http: HttpClient,
    private apiService: ApiService,
    private apiCommonService: ApiCommonService
  ) { }


  public getEstampRequests(params?): Observable<any> {
   let uri = '/stamprequests';
   if (params) {
   Object.keys(params).forEach((key, index) => {
    if (index === 0) {
      uri += `?${key}=${params[key]}`;
    } else {
      uri += `&${key}=${params[key]}`;
    }
  });
}
   return this.apiCommonService.getByUri(uri);
  }

  getEstampCount() {
    return this.getEstampRequests().pipe(
      map(res => {
        const stampCount = res.items.filter(estamp => estamp.status === 'Requested' || estamp.status === 'RequestSent');
        return stampCount.length;
      })
    );
  }

  public deleteEstampRequests(estampRequestsToDelete: string[]): Observable<any>  {

    return this.apiCommonService.putByUri('/stamprequests/delete', estampRequestsToDelete);
  }

  validateStampRequest(id, orgId) {
    const uri = `/stamprequests/${id}/${orgId}/validation`;
    return this.apiCommonService.getByUri(uri);
  }

  getStampDetails(id) {
    const uri = `/stamprequests/${id}`;
    return this.apiCommonService.getByUri(uri);
  }

  postDealerStampRequest(id, stamp) {
    const uri = `/stamprequests/${id}`;
    return this.apiCommonService.putByUri(uri, stamp);
  }

  getAgencies(offset = 0, limit = 10, name) {
    const uri = `/agencies?offset=${offset}&limit=${limit}&searchTerm=${name}`;
    return this.apiCommonService.getByUri(uri);

  }

  changeAgency(stampId, agency) {
    const uri = `/stamprequests/${stampId}/changeagency`;
    return this.apiCommonService.putByUri(uri, agency);

  }
}
