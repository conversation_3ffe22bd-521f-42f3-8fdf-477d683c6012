import { StubAutocompleteComponent } from 'testing/stubs/components/autocomplete.component';
import { async, ComponentFixture, TestBed, fakeAsync, inject } from '@angular/core/testing';

import { UmbrellaPrimaryPolicyComponent } from './umbrella-primary-policy.component';
import { StorageService } from 'app/shared/services/storage-new.service';
import { StubSpecsServiceProvider, StubSpecsService } from 'testing/stubs/services/specs.service.provider';
import { StubSubsServiceProvider } from 'testing/stubs/services/subs.service.provider';
import { StubUmbrellaServiceProvider } from 'testing/stubs/services/umbrella.service.provider';
import { StubOverlayLoaderServiceProvider } from 'testing/stubs/services/overlay-loader.service.provider';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { Helpers } from 'app/utils/helpers';
import { MA_TOWNS } from 'testing/data/specs/rating-states/ma-towns';
import { StubPremiumsServiceProvider } from 'testing/stubs/services/premiums.service.provider';

describe('UmbrellaPrimaryPolicyComponent', () => {
  let component: UmbrellaPrimaryPolicyComponent;
  let fixture: ComponentFixture<UmbrellaPrimaryPolicyComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [
        UmbrellaPrimaryPolicyComponent,
        StubAutocompleteComponent
      ],
      providers: [
        StorageService,
        StubSpecsServiceProvider,
        StubSubsServiceProvider,
        StubUmbrellaServiceProvider,
        StubOverlayLoaderServiceProvider,
        StubPremiumsServiceProvider
      ]
    })
    .compileComponents();
  }));

  beforeEach(fakeAsync(inject([SpecsService], (specsService: StubSpecsService) => {
    specsService.townsList = Helpers.deepClone(MA_TOWNS);

    fixture = TestBed.createComponent(UmbrellaPrimaryPolicyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  })));

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
