import { Vehicle, VehicleGeneralDetails, VehiclePlanSymbols, VehicleSymbol} from 'app/app-model/vehicle';

export const VEHICLE_PLAN_SYMBOLS:VehiclePlanSymbols[] = [
  {
      "meta": null,
      "ratingPlanId": "1",
      "symbols": [
        {
          "symbolType": "ISO75",
          "symbolValue": "36",
          "codeType": "Comp",
          "conversionType": "NA",
          "codeSource": "VinMaster",
          "description": "ISO 75 Comprehensive"
        },
        {
          "symbolType": "ISO75",
          "symbolValue": "48",
          "codeType": "Coll",
          "conversionType": "NA",
          "codeSource": "VinMaster",
          "description": "ISO 75 Collision"
        }
      ],
      "resourceName": "PlanSymbols"
    },
    {
      "meta": null,
      "ratingPlanId": "2",
      "symbols": [],
      "resourceName": "PlanSymbols"
    },
    {
      "meta": null,
      "ratingPlanId": "3",
      "symbols": [
        {
          "symbolType": "ISO75",
          "symbolValue": "36",
          "codeType": "Comp",
          "conversionType": "NA",
          "codeSource": "VinMaster",
          "description": "ISO 75 Comprehensive"
        },
        {
          "symbolType": "ISO75",
          "symbolValue": "48",
          "codeType": "Coll",
          "conversionType": "NA",
          "codeSource": "VinMaster",
          "description": "ISO 75 Collision"
        }
      ],
      "resourceName": "PlanSymbols"
    },
    {
      "meta": null,
      "ratingPlanId": "4",
      "symbols": [],
      "resourceName": "PlanSymbols"
    },
    {
      "meta": null,
      "ratingPlanId": "6",
      "symbols": [
        {
          "symbolType": "ISO75",
          "symbolValue": "36",
          "codeType": "Comp",
          "conversionType": "NA",
          "codeSource": "VinMaster",
          "description": "ISO 75 Comprehensive"
        },
        {
          "symbolType": "ISO75",
          "symbolValue": "48",
          "codeType": "Coll",
          "conversionType": "NA",
          "codeSource": "VinMaster",
          "description": "ISO 75 Collision"
        }
      ],
      "resourceName": "PlanSymbols"
    },
    {
      "meta": null,
      "ratingPlanId": "10",
      "symbols": [
        {
          "symbolType": "ISO27",
          "symbolValue": "22",
          "codeType": "NA",
          "conversionType": "BSC",
          "codeSource": "VinMaster",
          "description": "ISO 1-27 (BSC Conversion)"
        }
      ],
      "resourceName": "PlanSymbols"
    },
    {
      "meta": null,
      "ratingPlanId": "12",
      "symbols": [
        {
          "symbolType": "ISO75",
          "symbolValue": "36",
          "codeType": "Comp",
          "conversionType": "NA",
          "codeSource": "VinMaster",
          "description": "ISO 75 Comprehensive"
        },
        {
          "symbolType": "ISO75",
          "symbolValue": "48",
          "codeType": "Coll",
          "conversionType": "NA",
          "codeSource": "VinMaster",
          "description": "ISO 75 Collision"
        }
      ],
      "resourceName": "PlanSymbols"
    },
    {
      "meta": null,
      "ratingPlanId": "22",
      "symbols": [
        {
          "symbolType": "ISO75",
          "symbolValue": "36",
          "codeType": "Comp",
          "conversionType": "NA",
          "codeSource": "VinMaster",
          "description": "ISO 75 Comprehensive"
        },
        {
          "symbolType": "ISO75",
          "symbolValue": "48",
          "codeType": "Coll",
          "conversionType": "NA",
          "codeSource": "VinMaster",
          "description": "ISO 75 Collision"
        }
      ],
      "resourceName": "PlanSymbols"
    }
]