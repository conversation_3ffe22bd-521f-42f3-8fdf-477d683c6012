
import {map, first} from 'rxjs/operators';
import { Component, OnInit } from '@angular/core';
import { GENERAL_WARNINGS, WARNINGS } from 'app/app-mock/warnings';
import { NavigationExtras, Router, NavigationEnd } from '@angular/router';
import { WarningDefinitionI, WarningDataI, WarningItem, WARNING_GROUPS} from 'app/hints-and-warnings/model/warnings';

import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { PremiumsService } from 'app/dashboard/auto/premiums/premiums.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { RateService } from 'app/shared/services/rate.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { HintsAndWarningsService } from 'app/hints-and-warnings/services/hints-and-warnings.service';

import { Observable ,  forkJoin ,  SubscriptionLike as ISubscription } from 'rxjs';
import { AgencyUserService, UserData } from 'app/shared/services/agency-user.service';
import { SubsService } from 'app/dashboard/app-services/subs.service';

const BIG_FAKE_PREMIUM = 999999; // Deprecated

type TSortType = 'Carrier' | 'Rate';
type TSortOrder = 'ASC' | 'DESC';
const STORAGE_SORT_TYPE_NAME: string  = 'currentPremiumsSortType';
const STORAGE_SORT_ORDER_NAME: string  = 'currentPremiumsSortOrder';

//Rating plan ids to show authentication error help
const idsToShowAuthErrorHelp: string[] = ['91', '290','319','64','92','113'];

@Component({
    selector: 'app-home-premiums-table',
    templateUrl: './home-premiums-table.component.html',
    styleUrls: ['./home-premiums-table.component.scss'],
    standalone: false
})
export class HomePremiumsTableComponent implements OnInit {
  private quote;
  private subscription: ISubscription;
  private rateSubscription: ISubscription;
  private currentPlansubscription: ISubscription;
  private subscriptionChanges: ISubscription;
  public quotePlanObject;
  public loadingAll:boolean = false;

  public ratedPlans:any = [];
  public generalWarnings;
  private carrierWarnings;
  private currentRatesSubscription: ISubscription;
  private routerSubscription: ISubscription;
  private currentPlan;
  public agentId;
  public ratingPlanId;


  private subscriptionRateAll: ISubscription;
  private planSummaryRequestSubscription: ISubscription;

  constructor(
    private hintsAndWarningsService:HintsAndWarningsService,
    private overlayLoaderService: OverlayLoaderService,
    private rateService: RateService,
    private storageService: StorageService,
    private router: Router,
    public premiumsService: PremiumsService,
    public apiCommonService: ApiCommonService,
    private agencyUserService: AgencyUserService,
     private subsService:SubsService
  ) { }

  ngOnInit() {
    this.setUserAgent();
    this.rateService.getRates();
    this.premiumsService.getRates();

    this.processListSorting();

    this.getCurrentPlan();
    this.checkIfRequirementsMet();
    if (!this.premiumsService.loadingDataInProgress) {
      this.premiumsService.rerateGeneral();
    }
    this.routerCheck();

    this.getPlanSummaryRequest();
  }

  ngOnDestroy() {
    this.rateService.unsubscribeGetRates();
    this.subscription && this.subscription.unsubscribe();
    this.rateSubscription && this.rateSubscription.unsubscribe();
    this.currentPlansubscription && this.currentPlansubscription.unsubscribe();
    this.subscriptionChanges && this.subscriptionChanges.unsubscribe();
    this.subscriptionWarnings && this.subscriptionWarnings.unsubscribe();
    this.currentRatesSubscription && this.currentRatesSubscription.unsubscribe();
    this.routerSubscription && this.routerSubscription.unsubscribe();
    this.planSummaryRequestSubscription && this.planSummaryRequestSubscription.unsubscribe();
    this.subscriptionRateAll && this.subscriptionRateAll.unsubscribe();
    this.userSubscription && this.userSubscription.unsubscribe();
  }

  private userSubscription;

  private setUserAgent():void{

      let userData: UserData;

      this.agencyUserService.userData$.pipe(
        first())
        .subscribe(data => userData = data);

      // Quote agency contact
      if (userData) {
        this.agentId = userData.user.userId;
      }
  }

  private subscriptionWarnings;
  private delayTimer;
  private allWarnings:WarningItem[] = [];
  private checkIfRequirementsMet():void {
    this.subscriptionWarnings = this.hintsAndWarningsService.allWarnings$.subscribe(warnings => {
      this.delayTimer && clearTimeout(this.delayTimer);

      this.delayTimer = setTimeout(() => {
        this.allWarnings = warnings; // .filter(item => item.showWarning);
        this.generalWarnings = warnings.filter(warning => warning.group === WARNING_GROUPS.general && warning.showWarning);
        this.carrierWarnings = warnings.filter(warning => warning.group === WARNING_GROUPS.carrier && warning.showWarning);

        // console.log('%c >>> this.carrierWarnings', 'color:red', this.carrierWarnings);

        if (this.generalWarnings && this.generalWarnings.length) {
          if (this.quotePlanObject && this.quotePlanObject.items && this.quotePlanObject.items.length) {
            this.quotePlanObject.items.forEach( plan => {
              plan.review = true;
            })
          }
        } else {
          if (this.quotePlanObject && this.quotePlanObject.items && this.quotePlanObject.items.length) {
            this.quotePlanObject.items.forEach( plan => {
              plan.review = false;
            })
          }
        }
        if (this.carrierWarnings && this.carrierWarnings.length) {
          this.carrierWarnings.forEach(element => {

            if ( element.viewFieldId === 'field_DWELL' || element.viewFieldId === 'field_PP' || element.viewFieldId === 'field_OS' || element.viewFieldId === 'field_LOU')
            {
              this.quotePlanObject.items.forEach( plan => {
                plan.review = true;
              })

            }

            element.carrier.forEach(carrierId => {
              if (this.quotePlanObject && this.quotePlanObject.items && this.quotePlanObject.items.length) {
                this.quotePlanObject.items.forEach(plan => {
                  if (plan.ratingPlanId === carrierId) {
                    plan.review = true;
                  }
                })
              }
            })
          })
        }
      })
    })
  }

  private getCurrentPlan(): void {
    this.currentPlansubscription = this.storageService.getStorageData('selectedPlan').subscribe( res => {
      if (res && res.items && res.items.length) {
        this.currentPlan = JSON.parse(JSON.stringify(res));
        this.quotePlanObject = JSON.parse(JSON.stringify(res.items[0]));
        this.clearPlans();
        this.getCurrentRates()
          .then(() => {
            this.processListSorting();
          });
      }
    })
  }

  private clearPlans(force: boolean = false) {
    if (this.quotePlanObject && this.quotePlanObject.items && this.quotePlanObject.items.length) {
      this.quotePlanObject.items.forEach( plan => {
        if (!plan.rerate || force) {
          plan.error = null;
          plan.rate = null;
          plan.rerate = null;
          plan.premium = null;
        }
      })
    }
  }

  private getCurrentRates(): Promise<void>  {
    return new Promise((resolve, reject) => {
      this.currentRatesSubscription = this.storageService.getStorageData('rates').subscribe( res => {
        if (res && res.length) {
          this.ratedPlans = res;
          if (this.quotePlanObject && this.quotePlanObject.items && this.quotePlanObject.items.length) {
            this.quotePlanObject.items = this.quotePlanObject.items.map( (plan, index) => {
              this.ratedPlans.forEach(ratedPlan => {
                // console.log('ratedPlan >>>', ratedPlan);
                if (ratedPlan && ratedPlan.items && ratedPlan.items.length) {
                  // console.log('ratedPlan.items[0].rerate', ratedPlan.items[0].rerate);
                  if (!ratedPlan.items[0].rerate) {
                    if (ratedPlan.items[0].ratingPlanId === Number(plan.ratingPlanId) ) {
                      plan.rate = JSON.parse(JSON.stringify(ratedPlan));
                      if (!ratedPlan.loading) {
                        plan.loading = false
                      }

                      plan.premium = null;
                      if (plan.rate && plan.rate.items && plan.rate.items.length && plan.rate.items[0].premium !== '' && plan.rate.items[0].premium !== 0 && plan.rate.items[0].premium !== '0') {
                        plan.premium = plan.rate.items[0].premium;
                      } else {
                        // force for ordering
                        plan.premium = BIG_FAKE_PREMIUM // Deprecated
                      }
                      plan.rerate = null;
                      if (ratedPlan.items[0].msgStatusCd === 'Error' || ratedPlan.items[0].msgStatusCd === 'DataError' || ratedPlan.items[0].msgStatusCd === 'Unknown') {
                        plan.error = ratedPlan.items[0].msgStatusDesc
                        if (!plan.error && ratedPlan.items[0].systemMessages && ratedPlan.items[0].systemMessages.length) {
                          plan.error = ratedPlan.items[0].systemMessages[0].message
                        }
                      }

                      if (plan.rate && plan.rate.items && plan.rate.items.length && plan.rate.items[0].planName !== '' ) {
                        plan.carrier = plan.name;
                        plan.name = plan.rate.items[0].planName;
                      }

                      if (ratedPlan.items[0].msgStatusCd === 'Rejected' || ratedPlan.items[0].msgStatusCd === 'Declined') {
                        plan.declined = ratedPlan.items[0].msgStatusDesc
                        if (!plan.declined && ratedPlan.items[0].systemMessages && ratedPlan.items[0].systemMessages.length) {
                          plan.declined = ratedPlan.items[0].systemMessages[0].message
                        }
                      }
                    }
                  } else {
                    if (ratedPlan.items[0].ratingPlanId === Number(plan.ratingPlanId)) {
                      if (plan.carrier && plan.carrier.length) {
                        plan.name = plan.carrier;
                      }

                      // console.log('%c Im IN 1', 'color:red');
                      plan.rerate = true;
                      plan.rate = null;
                      plan.error = null;
                      plan.premium = null;
                    }
                  }
                }
              });

              if (this.premiumsService.tmpQuoteData && this.premiumsService.tmpQuoteData.items && this.premiumsService.tmpQuoteData.items.length) {
                if (plan.items && plan.items.length) {
                  plan.items.forEach(planItem => {
                    if (this.premiumsService.tmpQuoteData.items[index] && this.premiumsService.tmpQuoteData.items[index].items && this.premiumsService.tmpQuoteData.items[index].items.length) {
                      this.premiumsService.tmpQuoteData.items[index].items.forEach(oldPlanItem => {
                        if (planItem.coverageCode === oldPlanItem.coverageCode) {
                          if (planItem.currentValue !== oldPlanItem.currentValue && !planItem.rate) {
                            // console.log('%c Im IN 2', 'color:red');
                            this.quotePlanObject.items[index].rerate = true;
                            this.quotePlanObject.items[index].error = null;
                            this.quotePlanObject.items[index].rate = null;
                            this.quotePlanObject.items[index].premiums = null;
                          }
                        }
                      });
                    }
                  });
                }
              }
              return plan;
            })
            this.premiumsService.tmpQuoteData = JSON.parse(JSON.stringify(this.quotePlanObject));
          }
        }
        resolve();
      });
    });
  }

  private removeRate(plan) {
    const plans = [];
    this.ratedPlans.forEach( ratedPlan => {
      if (ratedPlan && ratedPlan.items && ratedPlan.items.length && ratedPlan.items[0].carrier !== plan.name) {
        plans.push(ratedPlan)
      }
    })
    this.storageService.setStorageData('rates', plans);
  }




  private mapRatedPlans(newRate) {
    let inRatedPlans = false;
    this.ratedPlans = this.ratedPlans.map(ratedPlan => {
      if (ratedPlan && ratedPlan.items && ratedPlan.items.length) {
        // if ((newRate && newRate.items && newRate.items.length) && ratedPlan && ratedPlan.items && ratedPlan.items.length && ratedPlan.items[0].planName === newRate.items[0].planName) {
        // if ((newRate && newRate.items && newRate.items.length) && ratedPlan && ratedPlan.items && ratedPlan.items.length && ratedPlan.items[0].carrier === newRate.items[0].carrier) {
        if ((newRate && newRate.items && newRate.items.length) && ratedPlan && ratedPlan.items && ratedPlan.items.length && ratedPlan.items[0].ratingPlanId === newRate.items[0].ratingPlanId) {
          ratedPlan = newRate;
          inRatedPlans = true;
        }
      }
      return ratedPlan
    });
    if (!inRatedPlans) {
      this.ratedPlans.push(newRate)
    };

    // console.log('this.ratedPlans', this.ratedPlans);
    this.storageService.setStorageData('rates', this.ratedPlans);
  }

  public rate(uri: string, plan): Promise<any> {
    return new Promise<void>( (resolve, reject) => {
      if (!plan.review) {
        plan.loading = true;
        plan.error = null;
        plan.declined = null;
        plan.rerate = null;
        plan.rate = null;
        plan.premium = null;
        return this.rateSubscription = this.apiCommonService.getByUri(uri).subscribe( response => {
          plan.loading = false;
          if (this.quotePlanObject && this.quotePlanObject.items && this.quotePlanObject.items.length) {
            this.quotePlanObject.items[this.quotePlanObject.items.indexOf(plan)].rate = response;
          };
          if (response.items[0].msgStatusCd === 'Error' || response.items[0].msgStatusCd === 'DataError' || response.items[0].msgStatusCd === 'Unknown') {
            plan.error = response.items[0].msgStatusDesc
            if (!plan.error && response.items[0].systemMessages && response.items[0].systemMessages.length) {
              plan.error = response.items[0].systemMessages[0].message
            }
           // plan.rateRequestId = response.items[0].rateRequestId;
            // response.items[0].rateRequestId = null;
          }

          if (response.items[0].msgStatusCd === 'Rejected' || response.items[0].msgStatusCd === 'Declined') {
            plan.declined = response.items[0].msgStatusDesc
            if (!plan.declined && response.items[0].systemMessages && response.items[0].systemMessages.length) {
              plan.declined = response.items[0].systemMessages[0].message
            }
            if (!plan.declined) {
              plan.declined = true
            }
           // plan.rateRequestId = response.items[0].rateRequestId;
            // response.items[0].rateRequestId = null;
          }

          this.mapRatedPlans(response);
          this.processListSorting();
          resolve();
        }, err => {
          plan.error = true;
          plan.loading = false;
          resolve()});
      } else {
        plan.premium = BIG_FAKE_PREMIUM; // Deprecated
        resolve();
      }
    })
  }

  public rateAll(continueRating: boolean = false): void {
    if (!continueRating) {
      this.ratedPlans = [];
      this.storageService.setStorageData('rates', []);
    }

    if (this.quotePlanObject && this.quotePlanObject.items && this.quotePlanObject.items.length) {
      this.loadingAll = true;
      let plansRating = [];
      this.quotePlanObject.items = this.quotePlanObject.items.map( (plan, index) => {
        if (!plan.review) {
          if (!continueRating) {
            plan.loading = true;
            const uri: string = this.quotePlanObject.meta.href + '/rateResponses/' + plan.ratingPlanId;
            plansRating.push(this.rateAllSingleFn(uri))
          } else {
            if (plan.loading) {
              const uri: string = this.quotePlanObject.meta.href + '/rateResponses/' + plan.ratingPlanId;
              plansRating.push(this.rateAllSingleFn(uri))
            }
          }
        }
        return plan
      });

      if (plansRating.length) {
        this.subscriptionRateAll = forkJoin(plansRating).subscribe(results => {
          console.log('done rate all')
          this.loadingAll = false;
        }, err => console.log('err', err));
      } else {
        this.loadingAll = false
      }
    }
  }

  private rateAllSingleFn(uri): Observable<any> {
    return this.rateService.getRate(uri).pipe(map(res => {
      this.processListSorting();
      return res;
    }));
  }

  private getPlanSummaryRequest() {
    this.planSummaryRequestSubscription = this.rateService.openSummary$.subscribe( data => {
      if (data) {
        this.stopAndRestartRateAll();
        this.rateService.openSummary(false);
      }
    })
  }

  private stopAndRestartRateAll() {
    this.subscriptionRateAll && this.subscriptionRateAll.unsubscribe();
    setTimeout( () => this.rateAll(true), 2500 );
  }

  // Sorting
  public filterOptions:Array<string> = ['Carrier', 'Rate'];
  public currentSortTypeOption: TSortType = 'Rate';
  public currentSortOrderOption: TSortOrder = 'ASC';


  public onFilterChange($ev) {
    if ($ev && $ev.filter) {
      this.currentSortTypeOption = $ev.filter;

      if (this.currentSortTypeOption === $ev.filter) {
        this.currentSortOrderOption = (this.currentSortOrderOption === 'ASC') ? 'DESC' : 'ASC';
      } else {
        this.currentSortOrderOption = 'ASC';
      }

      this.sortList(this.currentSortTypeOption, this.currentSortOrderOption);

      // Save Sort Order Option to local storage
      window.sessionStorage.setItem(STORAGE_SORT_TYPE_NAME, this.currentSortTypeOption);
      window.sessionStorage.setItem(STORAGE_SORT_ORDER_NAME, this.currentSortOrderOption);
    }
  }

  private processListSorting(): void {
    const sortTypeCurrOption: TSortType = <TSortType>window.sessionStorage.getItem(STORAGE_SORT_TYPE_NAME);
    const sortOrderCurrOption: TSortOrder = <TSortOrder>window.sessionStorage.getItem(STORAGE_SORT_ORDER_NAME);
    this.currentSortTypeOption = sortTypeCurrOption || 'Rate';
    this.currentSortOrderOption = sortOrderCurrOption || 'ASC';

    this.sortList(this.currentSortTypeOption, this.currentSortOrderOption);
  }

  // Sorting Premiums list
  // ---------------------------------------------------------------------------
  private sortList(sortType: TSortType, sortOrder?: TSortOrder): void {
    switch (sortType.toLowerCase()) {
      case 'rate': {
        // First sort by Carrier name - in case if there are no premiums yet;
        this.sortQuotePlanObjectItemsByCarrierName('ASC');
        this.sortQuotePlanObjectItemsByCarrierPremium(sortOrder);
        break;
      }
      case 'carrier': {
        this.sortQuotePlanObjectItemsByCarrierName(sortOrder);
        break;
      }
    }
  }

  private sortQuotePlanObjectItemsByCarrierPremium(sortOrder: TSortOrder): void {
    if (this.quotePlanObject && this.quotePlanObject.items && this.quotePlanObject.items.length) {
      this.quotePlanObject.items.sort((planA, planB) => {
        let valA: number;
        let valB: number;

        // Plans with status 'Declined' or 'Error' should be always in the end of the list.
        if (sortOrder === 'ASC') {
          valA = (this.planStatusIsError(planA) || this.planStatusIsDeclined(planA)) ? 9999999999 : (planA.premium && typeof planA.premium === 'number') ? planA.premium : -1;
          valB = (this.planStatusIsError(planB) || this.planStatusIsDeclined(planB)) ? 9999999999 : (planB.premium && typeof planB.premium === 'number') ? planB.premium : -1;
        } else {
          valA = (this.planStatusIsError(planA) || this.planStatusIsDeclined(planA)) ? -2 : (planA.premium && typeof planA.premium === 'number') ? planA.premium : -1;
          valB = (this.planStatusIsError(planB) || this.planStatusIsDeclined(planB)) ? -2 : (planB.premium && typeof planB.premium === 'number') ? planB.premium : -1;
        }

        if (planA.rate && planA.rate.items && planA.rate.items.length && planA.rate.items.length > 1) {
          const aTmpPremiums: number[] = planA.rate.items.map(p => p.premium);
          const minValue = Math.min.apply(null, aTmpPremiums);
          valA = minValue;
        }

        if (planB.rate && planB.rate.items && planB.rate.items.length && planB.rate.items.length > 1) {
          const aTmpPremiums: number[] = planB.rate.items.map(p => p.premium);
          const minValue = Math.min.apply(null, aTmpPremiums);
          valB = minValue;
        }

        if (sortOrder === 'ASC') {
          return (valA < valB) ? -1 : (valA > valB) ? 1 : 0;
        } else {
          return (valA < valB) ? 1 : (valA > valB) ? -1 : 0;
        }
      });
    }
  }

  private sortQuotePlanObjectItemsByCarrierName(sortOrder: TSortOrder): void {
    if (this.quotePlanObject && this.quotePlanObject.items && this.quotePlanObject.items.length) {

      this.quotePlanObject.items.sort((planA, planB) => {
        const valA: string = planA.name;
        const valB: string = planB.name;

        if (sortOrder === 'ASC') {
          return (valA < valB) ? -1 : (valA > valB) ? 1 : 0;
        } else {
          return (valA < valB) ? 1 : (valA > valB) ? -1 : 0;
        }
      });
    }
  }
  // Sorting Premiums list - END


  public goToSummary(plan) {
    let rateRequestId;
    if (plan.rate.items && plan.rate.items && plan.rate.items.length) {
      rateRequestId = plan.rate.items[0].rateRequestId;
    }

    let isError;
    isError = (this.planStatusIsError(plan) || this.planStatusIsDeclined(plan));

    const uri = '/' + plan.rate.meta.href.split('rateResponses')[0] + 'planSummaries/' + rateRequestId;

    this.overlayLoaderService.showLoader();
    if (!isError) {
      this.overlayLoaderService.hideLoader();
      this.apiCommonService.getByUri(uri).toPromise().then( planSummary => {
        this.rateService.setPlanSummary(planSummary);
        console.log('%c SET PLAN SUMMARY 1: ', 'color:red', planSummary);
        this.storageService.setStorageData('planSummary', planSummary);


        const params: NavigationExtras = {
          queryParams: {overlay: 'plan-summary', type: 'home'}
        };
        this.router.navigate([], params);

      }, err => {
        this.rateService.setPlanSummary({
          manualError: err
        });
        this.overlayLoaderService.hideLoader();

        this.storageService.setStorageData('planSummary', this.rateService.getPlanSummary());

        const params: NavigationExtras = {
          queryParams: {overlay: 'plan-summary', type: 'home'}
        };
        this.router.navigate([], params);
      });
    } else {
      this.storageService.setStorageData('planSummary', plan);
      this.overlayLoaderService.hideLoader();

      const params: NavigationExtras = {
        queryParams: {overlay: 'plan-summary', type: 'home'}
      };
      this.router.navigate([], params);

    }

    this.stopAndRestartRateAll();
  }

  public reviewingPlan;
  public showWarnings(plan) {
    this.reviewingPlan = plan;
    this.reviewingPlan.carrierWarnings = [];
    this.carrierWarnings.forEach(element => {
      if (element.carrier.indexOf(plan.ratingPlanId) > -1) {
        this.reviewingPlan.carrierWarnings.push(element);
      }
    });
    this.checkIfRequirementsMet();
  }

  private closeModalbox(refModal): void {
    refModal.closeModalbox();
  }

  private openModalbox(refModal): void {
    refModal.openModalbox();
  }

  public focusWarnElement(elementID: string, refModal = null) {
    if (refModal) {
      this.closeModalbox(refModal);
    }
    setTimeout( () => {
      const warnElement = document.getElementById(elementID);
      // this.warningService.focusWarnElement(warnElement);
    });
  }

  public resolveWarnings(generalWarnings: WarningItem[], reviewingPlan, refModal = null) {
    if (refModal) {
      this.closeModalbox(refModal);
    }

    if (generalWarnings && generalWarnings.length) {
      this.hintsAndWarningsService.goTo(generalWarnings[0]);
    } else if (reviewingPlan && reviewingPlan.carrierWarnings && reviewingPlan.carrierWarnings.length) {
      this.hintsAndWarningsService.goTo(reviewingPlan.carrierWarnings[0]);
    }
  }

  private routerCheck() {
    if (this.router && this.router.events) {
      this.routerSubscription = this.router.events.subscribe( val => {
        if (val instanceof NavigationEnd) {
          this.premiumsService.rerateGeneral(val.url);
        }
      });
    }
  }

  public planStatusIsError(plan): boolean {
    return plan.error
        || (plan.rate && plan.rate.items && plan.rate.items.length && plan.rate.items[0].msgStatusCd === 'Error')
        || (plan.rate && plan.rate.items && plan.rate.items.length && plan.rate.items[0].msgStatusCd === 'DataError');
  }

  public planStatusIsDeclined(plan): boolean {
    return plan.declined
        || (plan.rate && plan.rate.items && plan.rate.items.length && (plan.rate.items[0].msgStatusCd === 'Declined'
        || plan.rate.items[0].msgStatusCd === 'Rejected'));
  }

  public planStatusIsAuthError(plan): boolean {
    return plan?.error && plan?.error.includes('Authentication Error');
  }



  public showBtnViewPlanSummary(plan): boolean {
    return plan.rate && plan.rate.items && plan.rate.items.length && !plan.loading;
  }

  public showBtnPlanAuthError(plan): boolean {
    let ret = idsToShowAuthErrorHelp.includes(plan.ratingPlanId);
    return ret;
  }
  public PlanAuthErrorHelpLink(plan) {
    let ret = "";
    let tagLink ="";

    switch ( plan.ratingPlanId) {
      case '290':
      case '319':
      {
        ret = "https://bostonsoftware.happyfox.com/kb/article/113-arbella-authentication-errors/";
        tagLink="#Arbella_help";
        break;
      }
        case '91':
             {
        ret = "https://bostonsoftware.happyfox.com/kb/article/104-how-to-fix-a-safety-authentication-error/";
        tagLink="#Safety_help";
        break;
      }
      case '64':
           {
        ret = "https://bostonsoftware.happyfox.com/kb/article/132-concord-group-authentication-errors/";
        tagLink="#Concord_help";
        break;
      }
      case '92':
         {
        ret = "https://bostonsoftware.happyfox.com/kb/article/171-quincy-mutual-authentication-errors/";
        tagLink="#Quincy_help";
        break;
      }
      case '113':
        {
        ret = "https://bostonsoftware.happyfox.com/kb/article/129-plymouth-rock-authentication-errors/";
        tagLink="#Plymounth_help";
        break;
      }

    }
    this.logData("#authentication-errors", ret, tagLink)

     window.open(ret, "_blank");
  }

  logData(tag, url, tagLink) {
    const {user, facilityId} = JSON.parse(localStorage.getItem('userData')) || {} as any;


    const log = {
      message: '/'+facilityId + '/'+ user.userId + '/#HomePremiums/'+ tagLink +'/'+url,
      statusText:'OK',
      status:'200',
      url:'',
      name:'Plan Summary',
      tag: tag,
      userId:user.userId,
      facilityId

  };
  this.subsService.log(log).subscribe()

  }


}
