<section class="section">
  <div class="row">
    <div class="col-xs-12 u-align-center" *ngIf="!quoteLoadingError">
      <p>Loading Quote... <span *ngIf="getProgressPercentage(loadingStatus) > 0">{{getProgressPercentage(loadingStatus)}}%</span></p>
    </div>
    <div class="col-xs-12 u-align-center" *ngIf="quoteLoadingError">
      <br>
      <p>Sorry, Error occurred during Quote loading.</p>
      <p *ngIf="quoteLoadingErrorMessage" class="u-spacing--1-5">{{quoteLoadingErrorMessage}}</p>
      <br>
      <p>
        Please
        <a class="o-link o-link--blue" (click)="$event.preventDefault(); initQuoteData(quoteId)">try again</a>
        or load <a [routerLink]="'/dashboard'" class="o-link o-link--blue">other Quote </a>.
      </p>
    </div>
  </div>
</section>

<div *ngIf="quote && quote.lob"
  [userSubscription]="{code: getSubscriptionName(quote), warningPopup: refSubscriptionInfoPopup, createFormContainer: null, startAtInit: true, isMaipArcQuote: isMaipArcQuote}"
  (showPopup)="userSubscriptionStatus($event)"></div>

    <app-modalbox
    #refSubscriptionInfoPopup
    smToOverlay
    [mainContainerCss]="'force-to-front'">
    <div class="box box--silver u-spacing--1-5">
      <p *ngFor="let message of subscriptionMessages">{{message}}</p>
    </div>
    <div class="row u-spacing--2">
      <div class="col-xs-12 u-align-right">
        <button (click)="actionOnUserSubscriptionModalClose(refSubscriptionInfoPopup)" class="o-btn">OK</button>
      </div>
    </div>
  </app-modalbox>
