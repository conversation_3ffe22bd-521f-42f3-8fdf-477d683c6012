import { Garaging<PERSON>dd<PERSON>, PurchaseAndSalesInformation, EStampInfo } from 'app/dashboard/rmv-services/rta-prefill/get-ready.model';
import { Indicator } from './rmv-services-add.model';

export interface LessorLookupData {
  lessorId: string;
  name: string;
  fid: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  zipCode: string;
  subType: string;
  locationType: string;
  locationTypeDescription: string;
  atlasEntityKey: string;
  atlasEntityLocationKey: string;
}

export interface BusinessOwnerLookupData {
  lessorId: string;
  name: string;
  fid: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  zipCode: string;
}

export interface LienholderLookupData {
  lienholderId: string;
  name: string;
  code: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  postalCode: string;
  county: string;
  atlasEntityKey: string;
}

export interface Owner {
  id: number;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  license: string;
}

export interface Vehicle {
  id: number;
  lookupType: string;
  usage?: string;
  checked?: boolean;
  plateNumber: string;
  vin: string;
  plateType: string;
}

export interface Lessor {
  id: number;
  fid: string;
  atlasEntityKey: string;
  atlasEntityLocationKey: string;
}

export interface Lienholder {
  id: number;
  code: string;
  name?: string;
  city?: string;
}

export interface BusinessOwner {
  id: number;
  fid: string;
}

export interface RmvServicesUserEnteredData {
  owners: Owner[];
  vehicles: Vehicle[];
  tradeInVehicles?: Vehicle[];
  businessowners: BusinessOwner[];
  garagingAddress: GaragingAddres;
  purchaseAndSalesInformation: PurchaseAndSalesInformation;
  eStampInfo: EStampInfo;
  lessors: Lessor[];
  lienholders: Lienholder[];
  indicators?: any[];
}



export interface RMVCreateForm {
  transactionType: string;
  ownership: string;
  agencyId: string;
  creationUserId: number;
  number: string;
  clientId: string;
  rmvLookupData: string;
  lessorLookupData?: LessorLookupData[];
  businessOwnerLookupData?: BusinessOwnerLookupData[];
  lienholderLookupData?: LienholderLookupData[];
  rmvServicesUserEnteredData: RmvServicesUserEnteredData;
}

export interface Driver {
  id: number;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  license: string;
}

export interface PrefillRmv {
  transactionType: string;
  ownership: string;
  vehicles: Vehicle[];
  drivers: Driver[];
  lessors?: Lessor[];
  lienholders?: Lienholder[];
  businessowners?: BusinessOwner[];
  indicators?: Indicator[];
}

export interface LessorLookupData {
  lessorId: string;
  name: string;
  fid: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  zipCode: string;
}

export interface BusinessOwnerLookupData {
  lessorId: string;
  name: string;
  fid: string;
  addressLine1: string;
  addressLine2: string;
  city: string;
  state: string;
  zipCode: string;
}
