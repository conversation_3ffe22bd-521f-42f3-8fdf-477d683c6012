import { async, ComponentFixture, fakeAsync, inject, TestBed, tick } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { RouterTestingModule } from '@angular/router/testing';

import { HOME_QUOTE } from 'testing/data/quotes/quote-home';
import { HOME_QUOTE_PLAN_LIST } from 'testing/data/quotes/quote-plan-list/home';
import { data as RATING_PLANS_SUBS } from 'testing/data/subs/rating-plans';
import { expectLastCallArgs } from 'testing/helpers/data-expect';
import { StubDatepickerModalComponent } from 'testing/stubs/components/datepicker-modal.component';
import { StubPlansSelectorComponent } from 'testing/stubs/components/plans-selector.component';
import { StubSelectComponent } from 'testing/stubs/components/select.component';
import { StubdateFormatPipe } from 'testing/stubs/pipes/am-date-format.pipe';
import {
    StubOverlayLoaderServiceProvider
} from 'testing/stubs/services/overlay-loader.service.provider';
import { StubPremiumsServiceProvider } from 'testing/stubs/services/premiums.service.provider';
import {
    StubQuotesService, StubQuotesServiceProvider
} from 'testing/stubs/services/quotes.service.provider';

import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Helpers } from 'app/utils/helpers';

import { AsideHomeQuoteComponent } from './aside-home-quote.component';

describe('Component: AsideHomeQuote', () => {
  let component: AsideHomeQuoteComponent;
  let fixture: ComponentFixture<AsideHomeQuoteComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [ RouterTestingModule ],
      declarations: [
        AsideHomeQuoteComponent,
        StubSelectComponent,
        StubDatepickerModalComponent,
        StubPlansSelectorComponent,
        StubdateFormatPipe
      ],
      providers: [
        StorageService,
        StubQuotesServiceProvider,
        StubOverlayLoaderServiceProvider,
        StubPremiumsServiceProvider,
        StorageGlobalService
      ]
    })
    .compileComponents();
  }));

  describe('when selectedPlan is available in storage', () => {
    beforeEach(fakeAsync(inject([StorageService, QuotesService, StorageGlobalService],
      (storageService: StorageService, quotesService: StubQuotesService, storageGlobalService: StorageGlobalService) => {
        storageService.setStorageData('selectedQuote', Helpers.deepClone(HOME_QUOTE));
        storageService.setStorageData('selectedPlan', Helpers.deepClone(HOME_QUOTE_PLAN_LIST));
        storageGlobalService.setSubs('plans', Helpers.deepClone(RATING_PLANS_SUBS.items));
        quotesService.formType = ['HO3'];

        fixture = TestBed.createComponent(AsideHomeQuoteComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
        tick(200);
    })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });

    it('should react to effective date change', fakeAsync(inject([QuotesService, StorageService],
      (quotesService: StubQuotesService, storageService: StorageService) => {
        spyOn(quotesService, 'updateQuoteInfo').and.callThrough();
        spyOn(storageService, 'setStorageData').and.callThrough();

        const datepicker = <StubDatepickerModalComponent>fixture.debugElement.query(
          By.directive(StubDatepickerModalComponent)).componentInstance;
        datepicker.onSave.emit({
          date: new Date(2017, 3, 27)
        });

        fixture.detectChanges();
        tick(200);

        expect(component.quote.effectiveDate).toEqual('2017-04-27');
        expect(component.quote.expirationDate).toEqual('2018-04-27');
        expect(quotesService.updateQuoteInfo).toHaveBeenCalled();
        expect(storageService.setStorageData).toHaveBeenCalledWith(
          'selectedQuote',
          jasmine.anything()
        );
      })));

    it('should react to form type change', fakeAsync(inject([QuotesService, StorageService],
      (quotesService: StubQuotesService, storageService: StorageService) => {
        spyOn(quotesService, 'updateQuoteInfo').and.callThrough();
        spyOn(storageService, 'setStorageData').and.callThrough();

        const select = <StubSelectComponent>fixture.debugElement.query(By.directive(StubSelectComponent)).componentInstance;
        select.onSelect.emit({
          selectedOption: {
            id: 'Special (HO 3)'
          }
        });

        fixture.detectChanges();
        tick(200);

        expect(component.quote.formType).toEqual('Special (HO 3)');
        expect(quotesService.updateQuoteInfo).toHaveBeenCalled();
        expect(storageService.setStorageData).toHaveBeenCalledWith(
          'selectedQuote',
          jasmine.anything()
        );
      })));

    it('should react to selected plan change', fakeAsync(inject([QuotesService],
      (quotesService: StubQuotesService) => {
        spyOn(quotesService, 'updateQuoteByUrl').and.callThrough();

        const plansSelector = fixture.debugElement.query(By.directive(StubPlansSelectorComponent)).componentInstance;
        plansSelector.onSelect.emit({
          selectedOption: [{
            text: 'name',
            id: 'id',
            data: {
              name: 'name'
            }
          }]
        });

        fixture.detectChanges();
        tick(200);

        expectLastCallArgs(quotesService.updateQuoteByUrl).toEqual([
          jasmine.any(String),
          jasmine.objectContaining({
            items: [
              jasmine.objectContaining({
                name: 'name'
              })
            ]
          }),
          jasmine.any(Boolean)
        ]);
      })));
  });

  describe('when selectedPlan is not available in storage', () => {
    beforeEach(fakeAsync(inject([StorageService, StorageGlobalService],
      (storageService: StorageService, storageGlobalService: StorageGlobalService) => {
      storageService.setStorageData('selectedQuote', Helpers.deepClone(HOME_QUOTE));
      storageGlobalService.setSubs('plans', Helpers.deepClone(RATING_PLANS_SUBS.items));

      fixture = TestBed.createComponent(AsideHomeQuoteComponent);
      component = fixture.componentInstance;

      fixture.detectChanges();
      tick(200);
    })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('when first selectedPlan has no items', () => {
    beforeEach(fakeAsync(inject([StorageService, StorageGlobalService],
      (storageService: StorageService, storageGlobalService: StorageGlobalService) => {
      storageService.setStorageData('selectedQuote', Helpers.deepClone(HOME_QUOTE));
      const modifiedSelectedPlan = Helpers.deepClone(HOME_QUOTE_PLAN_LIST);
      modifiedSelectedPlan.items[0].items = [];
      storageService.setStorageData('selectedPlan', modifiedSelectedPlan);
      storageGlobalService.setSubs('plans', Helpers.deepClone(RATING_PLANS_SUBS.items));

      fixture = TestBed.createComponent(AsideHomeQuoteComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
      tick(200);
    })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('when there are no HOME plans available in subs', () => {
    beforeEach(fakeAsync(inject([StorageService], (storageService: StorageService) => {
      storageService.setStorageData('selectedQuote', Helpers.deepClone(HOME_QUOTE));
      storageService.setStorageData('selectedPlan', Helpers.deepClone(HOME_QUOTE_PLAN_LIST));

      fixture = TestBed.createComponent(AsideHomeQuoteComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
      tick(200);
    })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('when selectedQuote has no policyType set', () => {
    beforeEach(fakeAsync(inject([StorageService, StorageGlobalService],
      (storageService: StorageService, storageGlobalService: StorageGlobalService) => {
      const modifiedSelectedQuote = Helpers.deepClone(HOME_QUOTE);
      modifiedSelectedQuote.policyType = undefined;
      storageService.setStorageData('selectedQuote', modifiedSelectedQuote);
      storageService.setStorageData('selectedPlan', Helpers.deepClone(HOME_QUOTE_PLAN_LIST));
      storageGlobalService.setSubs('plans', Helpers.deepClone(RATING_PLANS_SUBS.items));

      fixture = TestBed.createComponent(AsideHomeQuoteComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
      tick(200);
    })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });
  });
});
