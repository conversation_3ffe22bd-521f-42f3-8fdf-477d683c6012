import { QuotePlan, QuotePlanRequired } from 'app/app-model/quote';

import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

@Injectable()
export class PlansService {

  constructor() { }

  public checkIfSelectedPlansAreInRequiredPlans(selectedPlans: QuotePlan[], requiredPlans: QuotePlanRequired[]): boolean {
    let isInRequiredPlans = false;

    for (const plan of requiredPlans) {
      const exists = selectedPlans.findIndex(selectedPlan => selectedPlan.ratingPlanId === plan.ratingPlanId);
      if (exists > -1) {
        isInRequiredPlans = true;
        break;
      }
    }

    return isInRequiredPlans;
  }


  public getPlansIdsListFromQuotePlansList(plans: QuotePlan[]): string[] {
    return plans.map(plan => plan.ratingPlanId);
  }

}

export const PLANS_LIST: QuotePlanRequired[] = [
  { ratingPlanId: '1', name: 'ACE Bankers Standard'},
  { ratingPlanId: '10', name: 'AIG'},
  { ratingPlanId: '3', name: '<PERSON><PERSON>'},
  { ratingPlanId: '13', name: 'Arbella'},
  { ratingPlanId: '4', name: 'Commerce'},
  { ratingPlanId: '6', name: 'Farm Family'},
  { ratingPlanId: '7', name: 'Firemans Fund'},
  { ratingPlanId: '21', name: 'Green Mountain'},
  { ratingPlanId: '20', name: 'Hanover'},
  { ratingPlanId: '16', name: 'Harleysville'},
  { ratingPlanId: '8', name: 'Liberty'},
  { ratingPlanId: '2', name: 'MAIP (CAR)'},
  { ratingPlanId: '24', name: 'MetLife'},
  { ratingPlanId: '22', name: 'NGM Insurance'},
  { ratingPlanId: '172', name: 'National General'},
  { ratingPlanId: '14', name: 'Norfolk and Dedham'},
  { ratingPlanId: '17', name: 'Occidental'},
  { ratingPlanId: '28', name: 'Plymouth Rock'},
  { ratingPlanId: '19', name: 'Preferred'},
  { ratingPlanId: '29', name: 'Preferred New'},
  { ratingPlanId: '26', name: 'Progressive'},
  { ratingPlanId: '18', name: 'Quincy Mutual Group'},
  { ratingPlanId: '25', name: 'Safeco'},
  { ratingPlanId: '11', name: 'Safety'},
  { ratingPlanId: '12', name: 'State Farm'},
  { ratingPlanId: '27', name: 'Travelers'},
  { ratingPlanId: '15', name: 'Vermont Mutual'}
];

export const REQUIRED_PLANS_SYMBOLS_ISO75: QuotePlanRequired[] = [
  { ratingPlanId: '12', name: 'State Farm'},
  { ratingPlanId: '20', name: 'Hanover'},
  { ratingPlanId: '21', name: 'Green Mountain'},
  { ratingPlanId: '3', name: 'Amica'},
  { ratingPlanId: '13', name: 'Arbella'},
  { ratingPlanId: '27', name: 'Travelers'},
  { ratingPlanId: '5', name: 'Encompass'},
  { ratingPlanId: '1', name: 'ACE Bankers Standard'},
  { ratingPlanId: '19', name: 'Preferred'},
  { ratingPlanId: '29', name: 'Preferred New'},
  { ratingPlanId: '14', name: 'Norfolk and Dedham'},
  { ratingPlanId: '22', name: 'NGM Insurance'},
  { ratingPlanId: '6', name: 'Farm Family'},
  { ratingPlanId: '11', name: 'Safety'},
  { ratingPlanId: '15', name: 'Vermont Mutual'},
];

export const REQUIRED_PLANS_SYMBOLS_ISO27: QuotePlanRequired[] = [
  { ratingPlanId: '10', name: 'AIG'},
  { ratingPlanId: '16', name: 'Harleysville'},
  { ratingPlanId: '8', name: 'Liberty'},
  { ratingPlanId: '24', name: 'MetLife'},
  { ratingPlanId: '172', name: 'National General'},
  { ratingPlanId: '28', name: 'Plymouth Rock'},
  { ratingPlanId: '26', name: 'Progressive'},
  { ratingPlanId: '25', name: 'Safeco'},
];

export const REQUIRED_PLANS_SYMBOLS_ISO27_SAFETY: QuotePlanRequired[] = [
  { ratingPlanId: '11', name: 'Safety'},
];

export const REQUIRED_PLANS_SYMBOLS_VRG: QuotePlanRequired[] = [
  { ratingPlanId: '4', name: 'Commerce'},
  { ratingPlanId: '18', name: 'Quincy Mutual Group'},
  { ratingPlanId: '2', name: 'MAIP (CAR)'},
];
