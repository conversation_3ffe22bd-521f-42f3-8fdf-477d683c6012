<section class="section" >
  <div class="row u-spacing--1">
    <div class="col-xs-12">
      <div class="inbox" *ngIf="newQuotesSize" [ngClass]="{'is-open': quoteInboxVisible}">
        <div class="inbox__header inbox__header--clickable" (click)="toggleQuoteInbox()">
          <div class="inbox__header-left">
            You have <strong>{{leadsText}}</strong> in your Leads Inbox
          </div>
          <div class="inbox__header-right">
            <a [routerLink]="['/dashboard/quote-inbox']" class="o-link o-link--upper inbox__more-link">View Leads Inbox</a>
          </div>
        </div>
        <div class="inbox__content">
          <table class="table table--compact table--no-edge-borders table--hoverable-grey">
            <tbody class="table__tbody">
              <tr class="table__tr" *ngFor="let row of quoteInbox">
                <td class="table__td u-width-50px">
                  <i class="o-icon o-icon--md o-icon--i-{{row.lob}} table__td-icon"></i>
                </td>
                <td class="table__td u-width-150px u-color-pelorous">
                  <a (click)="setShowLead(row)" class="modal-launcher-quote-inbox">{{row.insuredName}}</a>
                </td>
                <td class="table__td u-width-150px">{{row.state}}</td>
                <td class="table__td u-width-150px">{{row.receiveDate | date:'mediumDate'}}</td>
                <td class="table__td u-width-150px">{{row.source}}</td>
                <td class="table__td u-width-150px">{{row.viewStatus}}</td>
                <td class="table__td u-width-50px">
                  <span *ngIf="row.isNew" class="o-tag o-tag--green o-tag--mini">NEW</span>
                </td>
                <td class="table__td u-width-120px">{{row.status}}</td>
                <td class="table__td u-width-50px u-align-right">
                  <a class="o-link o-link--blue" (click)="rate(row)">Open</a>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div> <!-- / .inbox -->

      <app-modalbox #refModalQuoteInbox [launcher]="'.modal-launcher-quote-inbox'">

          <app-loader [loading]="modalboxLoadingData" [cssClass]="''" [loadingText]="'Loading Quote Details...'"></app-loader>

        <h1 class="o-heading o-heading--red">{{selectedLeadDetail.contact.name}}</h1>
<hr>
            <mat-tab-group *ngIf="selectedLeadDetail.contact"  @.disabled disableRipple mat-stretch-tabs #tabGroup>
                <mat-tab @.disabled label="Contact" aria-selected="true" #firsttab>
                <div class="box box--silver u-spacing--1-5">

                    <table class="table table--compact table--flex table--no-edge-borders">
                        <tbody class="table__tbody">
                          <tr class="table__tr">
                            <td class="table__td  u-width-150px">Name:</td>
                            <td *ngIf="selectedLeadDetail.contact.name" class="table__td u-color-charcoal">{{selectedLeadDetail.contact.name}}</td>
                          </tr>
                          <tr  class="table__tr">
                            <td class="table__td u-width-150px">City:</td>
                            <td *ngIf="selectedLeadDetail.contact.city" class="table__td u-color-charcoal">{{selectedLeadDetail.contact.city}}</td>
                          </tr>
                          <tr class="table__tr">
                            <td class="table__td u-width-150px">Phone:</td>
                            <td *ngIf="selectedLeadDetail.contact.leadPhone"class="table__td u-color-charcoal">{{selectedLeadDetail.contact.leadPhone.phone}}</td>
                          </tr>
                          <tr class="table__tr">
                            <td class="table__td u-width-150px">Email:</td>
                            <td *ngIf="selectedLeadDetail.contact.leadEmail" class="table__td u-color-charcoal">{{selectedLeadDetail.contact.leadEmail.email}}</td>
                          </tr>
                          <!--<tr class="table__tr">
                            <td class="table__td u-width-150px">Primary Residence:</td>
                            <td class="table__td u-color-charcoal">{{selectedLeadDetail.contact.primaryResidence}}</td>
                          </tr>-->
                        </tbody>
                      </table>
                    </div>
                </mat-tab>
                <mat-tab *ngIf="selectedLeadDetail.drivers" label="Driver">
                <div *ngFor="let driver of selectedLeadDetail.drivers;let idx = index" class="box box--silver u-spacing--1-5">
                  <table  *ngIf="driver"  class="table table--compact table--flex table--no-edge-borders">
                      <tr class="table__tr" style="background-color: #1989C9">
                          <td class="table__td  u-width-150px" style="color: white" >  Driver: {{idx+1}}</td>
                        </tr>
                      <tr class="table__tr">
                        <td class="table__td  u-width-150px">FirIstname:</td>
                        <td class="table__td u-color-charcoal">{{driver.firstName}}</td>
                      </tr>
                      <tr class="table__tr">
                        <td class="table__td u-width-150px">Lastname:</td>
                        <td class="table__td u-color-charcoal">{{driver.lastName}}</td>
                      </tr>
                      <tr class="table__tr">
                        <td class="table__td u-width-150px">Date Of Birth:</td>
                        <td class="table__td u-color-charcoal">{{driver.dateOfBirth}}</td>
                      </tr>
                      <tr class="table__tr">
                        <td class="table__td u-width-150px">Merit:</td>
                        <td class="table__td u-color-charcoal">{{driver.meritRatingPoints}}</td>
                      </tr>
                  </table>

                  </div>
                </mat-tab>
                <mat-tab *ngIf="selectedLeadDetail.vehicles" label="Vehicle">
                <div *ngFor="let vehicle of selectedLeadDetail.vehicles;let idx = index" class="box box--silver u-spacing--1-5">
                  <table class="table table--compact table--flex table--no-edge-borders">
                    <tbody *ngIf="vehicle" class="table__tbody">
                        <tr class="table__tr" style="background-color: #1989C9">
                            <td class="table__td  u-width-150px" style="color: white" >Vehicle: {{idx+1}}</td>
                        </tr>
                      <tr class="table__tr">
                        <td class="table__td  u-width-150px">Year:</td>
                        <td class="table__td u-color-charcoal">{{vehicle.year}}</td>
                      </tr>
                      <tr class="table__tr">
                        <td class="table__td u-width-150px">Make:</td>
                        <td class="table__td u-color-charcoal">{{vehicle.make}}</td>
                      </tr>
                      <tr class="table__tr">
                        <td class="table__td u-width-150px">Primary Use:</td>
                        <td class="table__td u-color-charcoal">{{vehicle.primaryUse}}</td>
                      </tr>
                      <tr class="table__tr">
                        <td class="table__td u-width-150px">Optional BI:</td>
                        <td class="table__td u-color-charcoal">{{vehicle.coverageRequested}}</td>
                      </tr>
                      <tr class="table__tr">
                        <td class="table__td u-width-150px">Collision Deductible:</td>
                        <td class="table__td u-color-charcoal">{{vehicle.collisionDeductible}}</td>
                      </tr>
                      <tr class="table__tr">
                          <td class="table__td u-width-150px">Comprehensive Deductible:</td>
                          <td class="table__td u-color-charcoal">{{vehicle.comprehensiveDeductible}}</td>
                        </tr>
                        <tr class="table__tr">
                            <td class="table__td u-width-150px">Property Damage:</td>
                            <td class="table__td u-color-charcoal">{{vehicle.propertyDamage}}</td>
                          </tr>
                    </tbody>
                  </table>
                  </div>
                </mat-tab>
                <mat-tab *ngIf="selectedLeadDetail.home" label="Home">
                    <div class="box box--silver u-spacing--1-5">
                        <table  class="table table--compact table--flex table--no-edge-borders">
                            <tbody class="table__tbody">
                              <tr class="table__tr">
                                <td class="table__td  u-width-150px">Insurance Type</td>
                                <td class="table__td u-color-charcoal">{{selectedLeadDetail.home.insuranceType}}</td>
                              </tr>
                              <tr class="table__tr">
                                  <td class="table__td  u-width-150px">Year Built</td>
                                  <td class="table__td u-color-charcoal">{{selectedLeadDetail.home.yearBuilt}}</td>
                                </tr>
                                <tr class="table__tr">
                                    <td class="table__td  u-width-150px">Residence</td>
                                    <td class="table__td u-color-charcoal">{{selectedLeadDetail.home.residence}}</td>
                                  </tr>
                                  <tr class="table__tr">
                                      <td class="table__td  u-width-150px">Current Coverage</td>
                                      <td class="table__td u-color-charcoal">{{selectedLeadDetail.home.currentCoverage}}</td>
                                    </tr>
                        </tbody>
                      </table>
                      </div>
                    </mat-tab>
                    <mat-tab *ngIf="selectedLeadDetail.coverage" label="Coverage">
                        <div class="box box--silver u-spacing--1-5">

                            <table  class="table table--compact table--flex table--no-edge-borders">
                                <tbody class="table__tbody">
                                  <tr class="table__tr">
                                    <td class="table__td  u-width-150px">Liability</td>
                                    <td class="table__td u-color-charcoal">{{selectedLeadDetail.coverage.liability}}</td>
                                  </tr>
                                  <tr class="table__tr">
                                      <td class="table__td  u-width-150px">Medical Payments</td>
                                      <td class="table__td u-color-charcoal">{{selectedLeadDetail.coverage.medicalPayments}}</td>
                                    </tr>
                                    <tr class="table__tr">
                                        <td class="table__td  u-width-150px">Dwelling Replacement Cost</td>
                                        <td class="table__td u-color-charcoal">{{selectedLeadDetail.coverage.dwellingReplacementCost}}</td>
                                      </tr>
                                      <tr class="table__tr">
                                          <td class="table__td  u-width-150px">Content Replacement Cost</td>
                                          <td class="table__td u-color-charcoal">{{selectedLeadDetail.coverage.contentReplacementCost}}</td>
                                        </tr>
                            </tbody>
                          </table>
                          </div>
                        </mat-tab>
                <mat-tab *ngIf="selectedLeadDetail.quotes" label="Quotes">
                <div class="box box--silver u-spacing--1-5">
                     <table  class="table table--compact table--flex table--no-edge-borders">
                        <tbody class="table__tbody">
                          <tr *ngFor="let quote of selectedLeadDetail.quotes" class="table__tr">
                            <td *ngIf="quote" class="table__td  u-width-150px">{{quote.name}}</td>
                            <td *ngIf="quote" class="table__td u-color-charcoal">{{quote.premium}}</td>
                          </tr>
                    </tbody>
                  </table>
                  </div>
                </mat-tab>
                <mat-tab *ngIf="selectedLeadDetail.discounts" label="Discounts">
                <div class="box box--silver u-spacing--1-5">

                    <table  class="table table--compact table--flex table--no-edge-borders">
                        <tbody class="table__tbody">
                          <tr class="table__tr">
                            <td class="table__td  u-width-150px">Payment Method</td>
                            <td class="table__td u-color-charcoal">{{selectedLeadDetail.discounts.paymentMethod}}</td>
                          </tr>
                          <tr class="table__tr">
                              <td class="table__td  u-width-150px">Motor Club</td>
                              <td class="table__td u-color-charcoal">{{selectedLeadDetail.discounts.motorClub}}</td>
                            </tr>
                    </tbody>
                  </table>
                  </div>
                </mat-tab>

              </mat-tab-group>



        <div class="row u-spacing--2 u-align-right">
          <div class="col-xs-12">
            <button class="o-btn u-spacing--right-2" (click)="rate(showLead)">Open</button>
            <button class="o-btn o-btn--outlined u-spacing--right-2" id="delete-lead-modal">Delete</button>
            <button class="o-btn o-btn--idle" (click)="closeModalBox()">Cancel</button>

            <app-confirmbox
            [launcher]="'#delete-lead-modal'"
            [question]="'Are you sure you want to delete this Lead?'"
            [askAbout]="selectedLeadDetail.contact.name"
            [confirmBtnText]="'Yes, delete lead'"
            (onAccept)="deleteQuote(showLead, refModalQuoteInbox)"
            (onCancel)="confirmCanceled($event)">
            </app-confirmbox>

          </div>
        </div>
      </app-modalbox>

    </div>
  </div>
</section>
