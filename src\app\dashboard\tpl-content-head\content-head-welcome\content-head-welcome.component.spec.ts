import { async, ComponentFixture, inject, TestBed } from '@angular/core/testing';

import { data as AGENCY_USER } from 'testing/data/agencies/user';
import { StubAgencyUserService } from 'testing/stubs/services/agency-user.service.provider';
import { StubAgencyUserServiceProvider } from 'testing/stubs/services/agency-user.service.provider';

import { AgencyUserService } from 'app/shared/services/agency-user.service';
import { Helpers } from 'app/utils/helpers';

import { ContentHeadWelcomeComponent } from './content-head-welcome.component';

describe('Component: ContentHeadWelcome', () => {
  let component: ContentHeadWelcomeComponent;
  let fixture: ComponentFixture<ContentHeadWelcomeComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [  ],
      declarations: [ ContentHeadWelcomeComponent ],
      providers: [
        StubAgencyUserServiceProvider
      ]
    })
    .compileComponents();
  }));

  beforeEach(inject([AgencyUserService], (agencyUserService: StubAgencyUserService) => {
    agencyUserService.userData = Helpers.deepClone(AGENCY_USER);

    fixture = TestBed.createComponent(ContentHeadWelcomeComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should destroy without errors', () => {
    expect(() => {
      fixture.destroy();
    }).not.toThrow();
  });
});
