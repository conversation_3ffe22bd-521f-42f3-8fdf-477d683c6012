import {
    generateCarriers,
    generateLabels,
    generateViewFieldIds,
    generateViewURIs,
    runConditions,
} from 'testing/helpers/warning-definitions';

import {
    WARNINGS_DEFINITIONS_AUTO_OPTIONS_CARRIER_OPTIONS,
    WARNINGS_DEFINITIONS_AUTO_OPTIONS_POLICY_HISTORY,
} from 'app/hints-and-warnings/data';

import { CoverageItemParsed } from 'app/app-model/coverage';
import { QuotePolicyHistory } from 'app/app-model/quote';
import { AdditionalDataI } from '../../../model/warnings';


describe('Definitions: Options', () => {
    describe('when auto options policy history validator is used', () => {
        let definitions: any[];
        let additionalData: AdditionalDataI;
        let policyHistory: QuotePolicyHistory;

        beforeEach(() => {
            definitions = WARNINGS_DEFINITIONS_AUTO_OPTIONS_POLICY_HISTORY;
            additionalData = {
                quoteSelectedPlans: [
                    {
                        ratingPlanId: '24',
                        name: 'MetLife',
                        lob: '',
                        meta: {
                            href: '',
                            rel: []
                        },
                        naic: '',
                        resourceName: '',
                        state: '',
                        bscCompanyPolicyID: '',
                        bscCompanyQuoteNumber: '',
                        allowedFormTypesCode: '',
                        sharedRatingPlanId: null,
                        sppAgreedValueInd: null,
                        submissionAvailableInd: null
                    },
                    {
                        ratingPlanId: '20',
                        name: 'Hanover',
                        lob: '',
                        meta: {
                            href: '',
                            rel: []
                        },
                        naic: '',
                        resourceName: '',
                        state: '',
                        bscCompanyPolicyID: '',
                        bscCompanyQuoteNumber: '',
                        allowedFormTypesCode: '',
                        sharedRatingPlanId: null,
                        sppAgreedValueInd: null,
                        submissionAvailableInd: null
                    }
                ],
                quoteSelectedPlansIds: ['20', '24'],
                specLobTowns: [],
            };
            policyHistory = {
                meta: {
                    href: '',
                    rel: []
                },
                priorPolicyPolicyNumber: '',
                priorPolicyExpirationDate: '',
                priorBodilyInjurylimits: '',
                priorOrRenewingCarrier: '',
                yearsWithCurrentAgency: '',
                yearsWithThisCarrier: '',
                quoteSessionId: '',
                quoteThisCarrierAs: '',
                lapsedDaysLast12Months: '',
                resourceId: '',
                resourceName: '',
                parentId: ''
            };
        });

        it('allows to check if required fields are provided', () => {
            let errors = runConditions(definitions, policyHistory, additionalData);

            expect(errors).toEqual([
                'priorOrRenewingCarrier', 'yearsWithCurrentAgency'
            ]);

            policyHistory.priorOrRenewingCarrier = 'Metropolitan';

            errors = runConditions(definitions, policyHistory, additionalData);

            expect(errors).toEqual(jasmine.arrayContaining([
                'priorPolicyExpirationDate', 'yearsWithThisCarrier', 'quoteThisCarrierAs',
                'lapsedDaysLast12Months', 'priorBodilyInjurylimits'
            ]));

            policyHistory.quoteThisCarrierAs = 'Renewal';

            errors = runConditions(definitions, policyHistory, additionalData);

            expect(errors).toEqual(jasmine.arrayContaining([
                'priorPolicyPolicyNumber'
            ]));
        });

        describe('generate without errors', () => {
            beforeEach(() => {
                policyHistory.priorOrRenewingCarrier = 'Metropolitan';
                policyHistory.quoteThisCarrierAs = 'Renewal';
            });

            it('viewFieldIds', () => {
                expect(() => {
                    generateViewFieldIds(definitions, policyHistory);
                }).not.toThrow();
            });

            it('viewURIs', () => {
                expect(() => {
                    generateViewURIs(definitions, policyHistory);
                }).not.toThrow();
            });

            it('labels', () => {
                expect(() => {
                    generateLabels(definitions, policyHistory);
                }).not.toThrow();
            });

            it('carriers', () => {
                expect(() => {
                    generateCarriers(definitions, policyHistory);
                }).not.toThrow();
            });
        });
    });

    describe('when auto options carrier validator is used', () => {
        let definitions: any[];
        let policyItem: CoverageItemParsed;

        beforeEach(() => {
            definitions = WARNINGS_DEFINITIONS_AUTO_OPTIONS_CARRIER_OPTIONS;
            policyItem = Object.assign(new CoverageItemParsed(), {
                isRequired: true,
                isActive: false,
                isDisabled: false,
                currentValue: '',
                alreadySelected: false,
                endpointUrl: '',
                isNewFromAPI: false,
                carrierNotes: '',
                coverageCode: '',
                coverageGroup: '',
                defaultValue: '',
                description: '',
                inputType: '',
                meta: {
                    href: ''
                },
                ratingPlanId: '',
                ratingPlanName: '',
                resourceName: '',
                values: [],
                quoteResourceId: '',
                ratingCarrierId: 0
            });
            // policyItem = {
            //     isRequired: true,
            //     isActive: false,
            //     isDisabled: false,
            //     currentValue: '',
            //     alreadySelected: false,
            //     endpointUrl: '',
            //     isNewFromAPI: false,
            //     carrierNotes: '',
            //     coverageCode: '',
            //     coverageGroup: '',
            //     defaultValue: '',
            //     description: '',
            //     inputType: '',
            //     meta: {
            //         href: ''
            //     },
            //     ratingPlanId: '',
            //     ratingPlanName: '',
            //     resourceName: '',
            //     values: [],
            //     quoteResourceId: '',
            //     ratingCarrierId: 0
            // };
        });

        it('allows to check if required fields are provided', () => {
            const errors = runConditions(definitions, policyItem, {});

            expect(errors).toEqual([
                'currentValue'
            ]);
        });

        it('generates viewFieldIds without errors', () => {
            expect(() => {
                generateViewFieldIds(definitions, policyItem);
            }).not.toThrow();
        });

        it('generates viewURIs without errors', () => {
            expect(() => {
                generateViewURIs(definitions, policyItem);
            }).not.toThrow();
        });

        it('generates labels without errors', () => {
            expect(() => {
                generateLabels(definitions, policyItem);
            }).not.toThrow();
        });

        it('generates carriers without errors', () => {
            expect(() => {
                generateCarriers(definitions, policyItem);
            }).not.toThrow();
        });
    });
});
