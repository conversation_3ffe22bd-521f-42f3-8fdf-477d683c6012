
import {first} from 'rxjs/operators';
import { request } from 'http';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';

// Services
import { DwellingService } from 'app/dashboard/app-services/dwelling.service';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { MoneyService } from 'app/shared/services/money.service';
import createNumberMask from 'text-mask-addons/dist/createNumberMask';

// Models
import { SubscriptionLike as ISubscription } from 'rxjs';
import {
  LossHistoryRequirements,
  LossHistory,
  LossHistoryItem
} from 'app/app-model/dwelling';
import { QuotePlan, Quote } from 'app/app-model/quote';

// Libraries
import * as _ from 'underscore';
import { 
  format,
  parse,
  parseISO,
  addDays,
  subYears,
  isAfter,
  isBefore,
  isSameDay,
  isValid
} from 'date-fns';

const DISPLAY_FORMAT = 'MM/dd/yyyy';
const MDY_FORMAT = 'M/d/yyyy';
const ISO_FORMAT = 'yyyy-MM-dd';

@Component({
    selector: 'app-loss-history',
    templateUrl: './loss-history.component.html',
    styleUrls: ['./loss-history.component.scss'],
    standalone: false
})
export class LossHistoryComponent implements OnInit {
  private selectedPlans: QuotePlan[] = []; // Selected Plans
  private quote: Quote;
  private quoteId = '';
  private resourceId = '';

  public lossFreeYearsRequired = false;
  public lossCountRequired = false;
  public lossDetailsRequired = false;
  public lossCountNumberRequired = 0;

  public totalNumberOfLossFreeYears: any = [
    { id: null, text: 'Unspecified' },
    { id: '0', text: '0' },
    { id: '1', text: '1' },
    { id: '2', text: '2' },
    { id: '3', text: '3' },
    { id: '4', text: '4' },
    { id: '5', text: '5' },
    { id: '6', text: '6' },
    { id: '7', text: '7' },
    { id: '8', text: '8' },
    { id: '9', text: '9' },
    { id: '10', text: '10' },
    { id: '11', text: '11' }
  ];
  public selectedTotalNumberOfLossFreeYears: any = this
    .totalNumberOfLossFreeYears[0];

  public lossesOptions: any = [
    { id: '_0', text: '0' },
    { id: '_1', text: '1' },
    { id: '_2', text: '2' },
    { id: '_3', text: '3' },
    { id: '_4', text: '4' },
    { id: '_5', text: '5' },
    { id: '_6', text: '6' },
    { id: '_7', text: '7' },
    { id: '_8', text: '8' },
    { id: '_9', text: '9' }
  ];

  public lossesTypes: any[] = [
    { id: 'AllOtherDamages', text: 'All Other Damages' },
    { id: 'BodilyInjury', text: 'Bodily Injury' },
    { id: 'Contamination', text: 'Contamination' },
    { id: 'CollisionSubmergedObject', text: 'Collision - Submerged Object' },
    { id: 'CreditCardForgery', text: 'Credit Card Forgery' },
    { id: 'DamageToPropertyOfOthers', text: 'Damage to Property of Others' },
    { id: 'EarthquakeDamage', text: 'Earthquake Damage' },
    { id: 'ExtendedCovPerils', text: 'Extended Cov Perils' },
    { id: 'FireOrRemoval', text: 'Fire or Removal' },
    { id: 'Flood', text: 'Flood' },
    { id: 'Freezing', text: 'Freezing' },
    { id: 'Hail', text: 'Hail' },
    { id: 'IMPersonalArticlesLoss', text: 'IM Personal Articles Loss' },
    { id: 'IntakeOfForeignObjs', text: 'Intake of Foreign Obs' },
    { id: 'LandMudSlide', text: 'Land/Mud Slide' },
    {
      id: 'LiabAssaultBatteryMolestationBi',
      text: 'Liab Assault/Battery/Molestation BI'
    },
    {
      id: 'LiabBusinessRelatedProfessional',
      text: 'Liab Business Related/Professional'
    },
    { id: 'LiabCauseOfLossNotlisted', text: 'Liab Cause of Loss not Listed' },
    { id: 'LiabDogBite', text: 'Liab Dog Bite' },
    { id: 'LiabFire', text: 'Liab Fire' },
    { id: 'LiabFirearms', text: 'Liab Firearms' },
    { id: 'LiabFuelOilPollution', text: 'Liab Fuel/Oil Pollution' },
    { id: 'LiabLeadPaintPoisoning', text: 'Liab Lead Paint Poisoning' },
    {
      id: 'LiabLibelSlanderDefamPersInjury',
      text: 'Liab Libel/Slander/Defam Pers Injury'
    },
    { id: 'LiabMoldBodilyInjury', text: 'Liab Mold Bodily Injury' },
    { id: 'LiabMoldPropertyDamage', text: 'Liab Mold Property Damage' },
    { id: 'LiabSlipAndFall', text: 'Liab Slip and Fall' },
    { id: 'LiabSwimmingPool', text: 'Liab Swimming Pool' },
    { id: 'LiabTrampoline', text: 'Liab Trampoline' },
    { id: 'LiabVehRecreationalEquip', text: 'Liab Veh/Recreational Equip' },
    { id: 'LiabWaterLeakOverflow', text: 'Liab Water Leak/Overflow' },
    { id: 'Lightning', text: 'Lightning' },
    { id: 'Livestock', text: 'Livestock' },
    { id: 'LossAdjustmentExpense', text: 'Loss Adjustment Expense' },
    { id: 'MedicalPayments', text: 'Medical Payments' },
    { id: 'MotorizedGolfCartCollision', text: 'Motorized Golf Cart Collision' },
    {
      id: 'MotorizedGolfCartOtherThanCollision',
      text: 'Motorized Golf Cart Other than Collision'
    },
    { id: 'MysteriousDisappearance', text: 'Mysterious Disappearance' },
    {
      id: 'OtherPropertyLossNonWeatherRelated',
      text: 'Other Property Loss (Non-Weather Related)'
    },
    {
      id: 'OtherPropertyLossWeatherRelated',
      text: 'Other Property Loss (Weather Related)'
    },
    { id: 'PersonalInjury', text: 'Personal Injury' },
    { id: 'PropertyDamage', text: 'Property Damage' },
    { id: 'ResidentEmployeeWC', text: 'Resident Employee WC' },
    { id: 'SinkholeCollapse', text: 'Sinkhole Collapse' },
    { id: 'SmokeDamage', text: 'Smoke Damage' },
    { id: 'TheftOffPremises', text: 'Theft (off premises)' },
    { id: 'TheftOnPremises', text: 'Theft (on premises)' },
    { id: 'TheftScheduledProperty', text: 'Theft Scheduled Property' },
    {
      id: 'VandalismAndMaliciousMischief',
      text: 'Vandalism and Malicious Mischief'
    },
    { id: 'Watercraft', text: 'Watercraft' },
    {
      id: 'WaterDamageMajorSystemAppliance',
      text: 'Water Damage - Major System Appliance'
    },
    { id: 'WaterDamageNonWeather', text: 'Water Damage - Non-Weather' },
    { id: 'WaterDamageSewerBackup', text: 'Water Damage - Sewer Backup' },
    { id: 'WaterDamageWeather', text: 'Water Damage - Weather' },
    { id: 'Windstorm', text: 'Windstorm' }
  ];

  public lossHistoryRequirements: LossHistoryRequirements[] = [];
  public lossHistory: LossHistory = new LossHistory();
  public requirementYears = 0;
  public effectiveDate: any;
  public displayDate: any;
  private lossHistoryRequirementsLoaded = false;

  public rowsQty: any;

  private subscriptionGetLossHistoryRequirements: ISubscription;
  private subscriptionGetQuote: ISubscription;
  private subscriptionGetSelectedPlans: ISubscription;
  private subscriptionlossHistory: ISubscription;
  private routerSubscription: ISubscription;
  private getLossHistoryFromAPISubscription: ISubscription;

  constructor(
    private storageService: StorageService,
    private dwellingService: DwellingService,
    private overlayLoaderService: OverlayLoaderService,
    private specsService: SpecsService,
    public moneyService: MoneyService,
    private route: ActivatedRoute
  ) {}

  public moneyMask = createNumberMask({allowDecimal: true});

  ngOnInit() {
    this.getQuoteId();
    this.getSelectedPlans();
    this.getLossHistoryRequirements();
  }

  ngAfterViewInit() {
    this.getRouterParams();
  }

  ngOnDestroy() {
    this.subscriptionGetSelectedPlans &&
      this.subscriptionGetSelectedPlans.unsubscribe();
    this.subscriptionGetLossHistoryRequirements &&
      this.subscriptionGetLossHistoryRequirements.unsubscribe();
    this.subscriptionGetQuote && this.subscriptionGetQuote.unsubscribe();
    this.subscriptionlossHistory && this.subscriptionlossHistory.unsubscribe();
    this.routerSubscription && this.routerSubscription.unsubscribe();
    this.getLossHistoryFromAPISubscription && this.getLossHistoryFromAPISubscription.unsubscribe();
    this.createLossHistoryCollectionSubscription && this.createLossHistoryCollectionSubscription.unsubscribe();
  }

  private getQuoteId() {
  this.subscriptionGetQuote && this.subscriptionGetQuote.unsubscribe();
  this.subscriptionGetQuote = this.storageService
    .getStorageData('selectedQuote')
    .subscribe(quote => {
      if (quote) {
        this.quote = quote;
        this.quoteId = quote.resourceId;
        this.effectiveDate = parseISO(quote.effectiveDate);
        this.rowsQty = this.updateRowsQty(
          this.selectedTotalNumberOfLossFreeYears,
          this.requirementYears
        );
        this.displayDate = format(this.effectiveDate, DISPLAY_FORMAT);
        this.getLossHistoryCollection(this.quoteId);
      }
    });
}

  private getLossHistoryRequirements() {
    this.subscriptionGetLossHistoryRequirements && this.subscriptionGetLossHistoryRequirements.unsubscribe();
    this.subscriptionGetLossHistoryRequirements = this.storageService.getStorageData('dwellingLossHistoryRequirementsNew')
      .subscribe(res => {
        this.lossHistoryRequirements = res;

        if (
          this.lossHistoryRequirements &&
          this.lossHistoryRequirements.length
        ) {
          this.checkIfLossFreeYearsRequired(this.lossHistoryRequirements);

          // Clean up loss history if requirements changed.
          if (this.requirementYears !== null) {
            let isClean = true;
            for (let i = 5; i > this.requirementYears; i--) {
              if (
                this.lossHistory.items &&
                this.lossHistory.items.length > 0
              ) {
                this.lossHistory.items = this.lossHistory.items.filter(
                  item => {
                    if (item.lossesYear === 'lossesYear' + i) {
                      isClean = false;
                      return false;
                    }
                    return true;
                  }
                );
              }
              this.lossHistory['lossesYear' + i] = null;
            }
            if (!isClean) {
              this.updateLossHistoryCollection(this.lossHistory);
            }
          }
          this.rowsQty = this.updateRowsQty(
            this.selectedTotalNumberOfLossFreeYears,
            this.requirementYears
          );
        }
      });
  }

  private checkIfLossFreeYearsRequired(lhrs: LossHistoryRequirements[]) {
    this.lossFreeYearsRequired = false;
    this.lossCountRequired = false;
    this.lossDetailsRequired = false;
    this.requirementYears = 0;
    lhrs.forEach(lhr => {
      if (lhr.requirementType === 'LossFreeYears') {
        this.lossFreeYearsRequired = true;
      }
      if (lhr.requirementType === 'LossCount') {
        this.lossFreeYearsRequired = true;
        this.lossCountRequired = true;
      }
      if (lhr.requirementType === 'LossDetail') {
        this.lossFreeYearsRequired = true;
        this.lossDetailsRequired = true;
      }
      const newLossCountNumberRequired = +lhr.requirementYears.substr(1, 999);
      if (this.requirementYears < newLossCountNumberRequired) {
        this.requirementYears = newLossCountNumberRequired;
      }
    });
  }

  private getSelectedPlans() {
    this.selectedPlans = [];
    this.subscriptionGetSelectedPlans && this.subscriptionGetSelectedPlans.unsubscribe();
    this.subscriptionGetSelectedPlans = this.storageService
      .getStorageData('selectedPlan')
      .subscribe(response => {
        if (response && response.items && response.items.length) {
          this.selectedPlans = JSON.parse(
            JSON.stringify(response.items[0].items)
          );
          // Clear existing loss requirements rows
          this.lossFreeYearsRequired = false;
          this.lossCountRequired = false;
          this.lossDetailsRequired = false;
          this.requirementYears = 0;
          this.rowsQty = this.updateRowsQty(
            this.selectedTotalNumberOfLossFreeYears,
            this.requirementYears
          );
          // this.getLossHistoryRequirements(); //TEST
        }
      });
  }

  private parseSelectedYearsValue(str: string): any {
    if (!str || !str.length) {
      return 'Unspecified';
    }

    const _strToArr = str.split('');
    let _filteredString = '';

    _strToArr.forEach(el => {
      if (!isNaN(parseInt(el))) {
        _filteredString += el;
      }
    });
    return parseInt(_filteredString);
  }

private updateRowsQty(selectedYears: number | any, requiredYears: number) {
  if (isNaN(selectedYears)) {
    this.selectedTotalNumberOfLossFreeYears = this.totalNumberOfLossFreeYears[0];
  }
  let numberOfLossYears = Math.abs(selectedYears - requiredYears);
  if (selectedYears > requiredYears) {
    numberOfLossYears = 0;
  }
  const rows = [];
  for (let i = 0; i < numberOfLossYears; i++) {
    const fromDate = subYears(this.effectiveDate, i + 1 + selectedYears);
    const toDate = subYears(this.effectiveDate, i + selectedYears);
    rows.push({
      from: format(addDays(fromDate, 1), MDY_FORMAT),
      to: format(toDate, MDY_FORMAT),
      lossesYear: 'lossesYear' + (i + 1 + selectedYears),
      items: []
    });
  }
  this.parseLossHistoryItems(this.lossHistory, rows);
  return rows;
}
  public getSelectedTotalNumberOfLossFreeYears() {
    return this.selectedTotalNumberOfLossFreeYears;
  }

  public updateTotalNumberOfLossFreeYears($event, input, validationMsgModel) {
    let lossFreeYears = $event.id;
    if (lossFreeYears && lossFreeYears !== 'null') {
      const validLosses = this.checkLossesValid(lossFreeYears);
      if (!validLosses) {
        validationMsgModel.openModalbox();
        input.activeOption = this.selectedTotalNumberOfLossFreeYears;
        return false;
      }
      lossFreeYears = '_' + lossFreeYears;
    } else {
      lossFreeYears = null;
    }

    this.selectedTotalNumberOfLossFreeYears = $event.id;
    this.displayDate = this.updateDisplayDate(
      this.selectedTotalNumberOfLossFreeYears
    );

    this.lossHistory.lossFreeYears = lossFreeYears;
    this.updateLossHistoryCollection(this.lossHistory);
    this.rowsQty = this.updateRowsQty(
      this.selectedTotalNumberOfLossFreeYears,
      this.requirementYears
    );
  }

  public closeModal(element) {
    element.closeModalbox();
  }

  private checkLossesValid(lossFreeYears) {
    if (this.lossHistory && lossFreeYears) {
      for (let i = 1; i <= lossFreeYears; i++) {
        if (
          this.lossHistory['lossesYear' + i] &&
          this.lossHistory['lossesYear' + i] !== '_0'
        ) {
          return false;
        }
      }
    } else {
      return false;
    }
    return true;
  }

public updateDisplayDate(extractValue: number) {
  if (extractValue > 0) {
    return format(
      subYears(this.effectiveDate, this.selectedTotalNumberOfLossFreeYears),
      DISPLAY_FORMAT
    );
  }
  return format(this.effectiveDate, DISPLAY_FORMAT);
}

  private getLossHistoryCollection(quoteId: string) {
    this.subscriptionlossHistory && this.subscriptionlossHistory.unsubscribe();
    this.subscriptionlossHistory = this.storageService
      .getStorageData('dwellingLossHistory')
      .subscribe(lossHistoryData => {
        console.log('lossHistoryData >>> ', lossHistoryData);
        // update view
        if (
          lossHistoryData &&
          lossHistoryData.meta &&
          lossHistoryData.meta.href
        ) {
          this.resourceId = lossHistoryData.meta.href;
          this.lossHistory = lossHistoryData;
          this.selectedTotalNumberOfLossFreeYears = this.parseSelectedYearsValue(
            this.lossHistory.lossFreeYears
          );
          this.displayDate = this.updateDisplayDate(
            this.selectedTotalNumberOfLossFreeYears
          );
          this.rowsQty = this.updateRowsQty(
            this.selectedTotalNumberOfLossFreeYears,
            this.requirementYears
          );
        } else {
          this.getLossHistoryFromAPI(quoteId);
        }
      });
  }


  private getLossHistoryFromAPI(quoteId: string) {
    this.overlayLoaderService.showLoader();
    this.getLossHistoryFromAPISubscription && this.getLossHistoryFromAPISubscription.unsubscribe();
    this.getLossHistoryFromAPISubscription = this.dwellingService
      .getLossHistory(quoteId).pipe(
      first())
      .subscribe(response => {
        if (response.items[0] === undefined) {
          this.createLossHistoryCollection(quoteId);
        } else {
          this.resourceId = response.items[0].meta.href;
          this.lossHistory = response.items[0];
          this.selectedTotalNumberOfLossFreeYears = this.parseSelectedYearsValue(
            this.lossHistory.lossFreeYears
          );
          this.displayDate = this.updateDisplayDate(
            this.selectedTotalNumberOfLossFreeYears
          );
          this.storageService.setStorageData(
            'dwellingLossHistory',
            this.lossHistory
          );
          this.overlayLoaderService.hideLoader();
        }
      });
  }

  private createLossHistoryCollectionSubscription: ISubscription;
  private createLossHistoryCollection(quoteId: string) {
    this.createLossHistoryCollectionSubscription && this.createLossHistoryCollectionSubscription.unsubscribe();
    this.createLossHistoryCollectionSubscription = this.dwellingService
      .createLossHistory(quoteId).pipe(
      first())
      .subscribe(
        response => {
          this.resourceId = response.meta.href;
          // NEW F
          response.items[0] = this.dwellingService.helpUpdateLossHistoryForHintsAndWarnings(response.items[0], this.quote);
          this.storageService.setStorageData('dwellingLossHistory', response);
          this.overlayLoaderService.hideLoader();
        },
        reject => {
          this.overlayLoaderService.hideLoader();
        }
      );
  }

  private updateLossHistoryCollectionSubscription: ISubscription;
  private updateLossHistoryCollection(data: LossHistory) {
    this.overlayLoaderService.showLoader();
    this.updateLossHistoryCollectionSubscription && this.updateLossHistoryCollectionSubscription.unsubscribe();
    this.updateLossHistoryCollectionSubscription = this.dwellingService
      .updateLossHistory(this.resourceId, data).pipe(
      first())
      .subscribe(
        response => {
          // NEW F
          response = this.dwellingService.helpUpdateLossHistoryForHintsAndWarnings(response, this.quote);
          this.storageService.setStorageData('dwellingLossHistory', response);
          this.overlayLoaderService.hideLoader();
        },
        reject => {
          this.overlayLoaderService.hideLoader();
        }
      );
  }

  public deleteLossHistoryCollection() {
    const emptyLossHistory: LossHistory = new LossHistory();
    this.selectedTotalNumberOfLossFreeYears = this.totalNumberOfLossFreeYears[0];
    return this.updateLossHistoryCollection(emptyLossHistory);
  }

private parseLossHistoryItems(lossHistory: LossHistory, rows) {
  if (lossHistory?.items?.length) {
    lossHistory.items.forEach(item => {
      let lossDate: Date;
      
      // Safely parse the date
      try {
        lossDate = typeof item.lossDate === 'string' 
          ? parseISO(item.lossDate)
          : item.lossDate as Date;
      } catch {
        return;
      }

      if (!isValid(lossDate)) { return; }

      item.descriptionParsed = this.getLossTypeDescription(item.description);
      rows.forEach(row => {
        // Parse the from/to dates safely
        const fromDate = parse(row.from, MDY_FORMAT, new Date());
        const toDate = parse(row.to, MDY_FORMAT, new Date());

        if (
          (isAfter(lossDate, fromDate) && isBefore(lossDate, toDate)) ||
          isSameDay(lossDate, fromDate) ||
          isSameDay(lossDate, toDate)
        ) {
          // Always store in ISO format
          item.lossDate = format(lossDate, ISO_FORMAT);
          item.lossesYear = row.lossesYear;
          row.items.push(item);
        }
      });
    });
  }
}

  public isAnyCatastrophic(items): boolean {
    let isCatastrophic = false;
    if (items && items.length) {
      items.forEach(item => {
        if (item.catastrophicLossInd) {
          isCatastrophic = true;
        }
      });
    }
    return isCatastrophic;
  }

  public modalAddLossHistoryData: any;
  public lossHistoryDataRows: Array<any> = [];
  public addLossHistory(modalRef, row) {
    modalRef.openModalbox();

    this.modalAddLossHistoryData = row;
    let lossHistoryDataRowsCount = this.lossHistory[row.lossesYear];
    lossHistoryDataRowsCount = parseInt(lossHistoryDataRowsCount.substring(1));
    this.lossHistoryDataRows = [];
    let currentLossHistoryDataRowsCount = 0;
    if (row.items && row.items.length) {
      currentLossHistoryDataRowsCount = row.items.length;
    }
    for (
      let i = 0;
      i < lossHistoryDataRowsCount - currentLossHistoryDataRowsCount;
      i++
    ) {
      this.lossHistoryDataRows.push(null);
    }
    this.lossHistoryDataRows = this.lossHistoryDataRows.map(item => {
      if (!item) {
        return new LossHistoryItem();
      }
      return item;
    });
    let maxNewRowsAvailable = 3;
    if (this.lossHistory && this.lossHistory.items) {
      maxNewRowsAvailable = 3 - this.lossHistory.items.length;
    }
    if (
      this.lossHistoryDataRows &&
      this.lossHistoryDataRows.length > maxNewRowsAvailable
    ) {
      this.lossHistoryDataRows = this.lossHistoryDataRows.slice(
        0,
        maxNewRowsAvailable
      );
    }
  }

  public addNewLossHistoryItems(lossHistoryDataRows) {
    const newLosses = [];
    if (lossHistoryDataRows.length) {
      lossHistoryDataRows.forEach(loss => {
        if (loss.lossDate && loss.description && (loss.lossAmount || loss.lossAmount === 0 )) {
          newLosses.push(loss);
        }
      });
    }
    if (
      this.lossHistory &&
      (!this.lossHistory.items ||
        (this.lossHistory.items && !this.lossHistory.items.length))
    ) {
      this.lossHistory.items = newLosses;
    } else {
      this.lossHistory.items = this.lossHistory.items.concat(newLosses);
    }

    this.updateLossHistoryCollection(this.lossHistory);
  }

public updateSingleLoss($event: any, index: any, field: string) {
  if (!Number.isInteger(index)) {
    if (field === 'lossDate') {
      // Handle both date object and string cases
      const date = $event.date instanceof Date ? $event.date : parseISO($event.date);
      if (isValid(date)) {
        this.lossItemToEdit.lossDate = format(date, ISO_FORMAT);
      }
    } else if (field === 'lossAmount') {
      if (typeof $event === 'string' && $event.indexOf('$') > -1) {
        $event = parseFloat($event.replace('$', '').replace(/,/g, ''));
      }
      if ($event >= 0) {
        this.lossItemToEdit[field] = $event;
      }
    } else {
      this.lossItemToEdit[field] = $event;
    }
  } else {
    if (field === 'lossDate') {
      // Handle both date object and string cases
      const date = $event.date instanceof Date ? $event.date : parseISO($event.date);
      if (isValid(date)) {
        this.lossHistoryDataRows[index].lossDate = format(date, ISO_FORMAT);
      }
    } else if (field === 'lossAmount') {
      if (typeof $event === 'string' && $event.indexOf('$') > -1) {
        $event = parseFloat($event.replace('$', '').replace(/,/g, ''));
      }
      this.lossHistoryDataRows[index][field] = $event;
    } else {
      this.lossHistoryDataRows[index][field] = $event;
    }
  }
  this.checkAllLossItemDateInRange();
}


public checkLossItemDateInRange(date: string | null): boolean {
  if (!date) { return false; }

  if (this.modalAddLossHistoryData?.from && this.modalAddLossHistoryData?.to) {
    try {
      // Parse dates safely
      const fromDate = parse(this.modalAddLossHistoryData.from, MDY_FORMAT, new Date());
      const toDate = parse(this.modalAddLossHistoryData.to, MDY_FORMAT, new Date());
      const checkDate = typeof date === 'string' ? parseISO(date) : date;

      if (!isValid(fromDate) || !isValid(toDate) || !isValid(checkDate)) {
        return false;
      }

      return (
        (isSameDay(checkDate, fromDate) || isAfter(checkDate, fromDate)) &&
        (isSameDay(checkDate, toDate) || isBefore(checkDate, toDate))
      );
    } catch {
      return false;
    }
  }
  return false;
}


  public isEditModelInvalid(lossItemToEdit) {
    if (!lossItemToEdit) {
      return true;
    }
    if (!lossItemToEdit.lossDate || !this.checkLossItemDateInRange(lossItemToEdit.lossDate)) {
      return true;
    }
    if (!lossItemToEdit.description ) {
      return true;
    }
    if (lossItemToEdit.lossAmount == null || lossItemToEdit.lossAmount < 0) {
      return true;
    }
    return false;
  }


  public invalidLossDetailModal = true;

  private checkAllLossItemDateInRange() {
    let keepgoing = true;
    this.invalidLossDetailModal = true;
    if (this.lossHistoryDataRows && this.lossHistoryDataRows.length) {
      this.lossHistoryDataRows.forEach(item => {
        if (keepgoing) {
          if (
            item.lossDate &&
            this.checkLossItemDateInRange(item.lossDate) &&
            (item.lossAmount || item.lossAmount === 0) &&
            item.lossAmount !== '' &&
            item.description &&
            item.description !== ''
          ) {
            this.invalidLossDetailModal = false;
          } else {
            this.invalidLossDetailModal = true;
            keepgoing = false;
          }
        }
      });
    }
  }

  public lossItemToEdit: LossHistoryItem;
  public lossItemToEditIndex: number;
  public editSingleLossHistory(loss, modalEditSingleLossHistory, row) {
    modalEditSingleLossHistory.openModalbox();
    this.modalAddLossHistoryData = row;

    this.lossItemToEdit = JSON.parse(JSON.stringify(loss));
    this.lossItemToEditIndex = null;
    this.lossHistory.items.forEach((item, index) => {
      if (
        item.description === this.lossItemToEdit.description &&
        item.lossDate === this.lossItemToEdit.lossDate &&
        item.lossAmount === this.lossItemToEdit.lossAmount
      ) {
        this.lossItemToEditIndex = index;
      }
    });
  }

public updateSingleLossHistoryCount(ev, year, refLossesFrom, modalLossDetailsValidationMsg, from) {
  if (this.lossHistory?.items?.length > 0 && ev.id) {
    let prevSelection = this.lossHistory[year]?.slice(1, 2);
    let currSelection = ev.id?.slice(1, 2);

    let numDetails = 0;
    let invalidLossDetail = false;
    
    if (currSelection && prevSelection && currSelection < prevSelection) {
      try {
        // Parse the from date using the correct format
        const fromDateObj = parse(from, MDY_FORMAT, new Date());
        if (!isValid(fromDateObj)) {
          console.error('Invalid from date:', from);
          return;
        }

        this.lossHistory.items.forEach(detail => {
          if (detail.lossesYear) {
            const detailDate = parseISO(detail.lossDate);
            if (isValid(detailDate) && !isBefore(detailDate, fromDateObj)) {
              const detailYear = detail.lossesYear.slice(10, 11);
              if ('lossesYear' + detailYear === year) {
                numDetails = numDetails + 1;
              }
            }
          }
        });

        if (numDetails > currSelection) {
          invalidLossDetail = true;
        }
      } catch (error) {
        console.error('Error processing dates:', error, 'from:', from);
        return;
      }
    }

    if (modalLossDetailsValidationMsg && invalidLossDetail) {
      modalLossDetailsValidationMsg.openModalbox();
      refLossesFrom.activeOption = this.lossHistory[year];
      return false;
    }
  }

  this.lossHistory[year] = ev.id;
  this.updateLossHistoryCollection(this.lossHistory);
}
  public updateSingleLossHistory() {
    if (this.lossHistory && this.lossHistory.items) {
      if (this.lossHistory.items.length) {
        this.lossHistory.items[this.lossItemToEditIndex] = JSON.parse(
          JSON.stringify(this.lossItemToEdit)
        );
      } else {
        this.lossHistory.items.push(this.lossItemToEdit);
      }

      this.updateLossHistoryCollection(this.lossHistory);
    }
  }

  public lossItemToDelete: LossHistoryItem;
  public deleteSingleLossHistory(loss) {
    this.lossItemToDelete = loss;
  }

  public confirmDeleteLoss() {
    if (
      this.lossHistory &&
      this.lossHistory.items &&
      this.lossHistory.items.length
    ) {
      let indexToRemove;
      this.lossHistory.items.forEach((item, index) => {
        if (
          item.description === this.lossItemToDelete.description &&
          item.lossDate === this.lossItemToDelete.lossDate &&
          item.lossAmount === this.lossItemToDelete.lossAmount
        ) {
          indexToRemove = index;
        }
      });
      this.lossHistory.items.splice(indexToRemove, 1);
      this.updateLossHistoryCollection(this.lossHistory);
    }
  }

  public getLossTypeDescription(id: string): string {
    let type = '';
    this.lossesTypes.forEach(lossType => {
      if (lossType.id === id) {
        type = lossType.text;
      }
    });
    return type;
  }
  public getLossesOptions(id: number) {
    // if (id === 0) {
    //   const customLossesOptions = this.lossesOptions.slice();
    //   customLossesOptions.splice(0, 1);
    //   return customLossesOptions;

    // }
    return this.lossesOptions;
  }

  private getRouterParams() {
    this.routerSubscription = this.route.fragment.subscribe(
      (fragment: string) => {
        if (fragment && fragment.startsWith('modal-details-')) {
          const element = document.getElementById(fragment);
          if (element) {
            element.click();
          }
        }
      }
    );
  }

  // HELPER
  // ----------------------------------------------------------------------------
 public helpDisplayLossDate(date: string | Date): string {
  if (!date) { return ''; }
  try {
    const parsedDate = date instanceof Date ? date : parseISO(date);
    return isValid(parsedDate) ? format(parsedDate, DISPLAY_FORMAT) : '';
  } catch {
    return '';
  }
}
}
