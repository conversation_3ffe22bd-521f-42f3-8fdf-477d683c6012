import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { Component, OnInit, Input, OnChanges } from '@angular/core';
import { FilterOption } from 'app/app-model/filter-option';
import { ControlContainer, NgForm } from '@angular/forms';
import { PLATE_TYPES, REGISTRATION_TYPES, REGISTRATION_REASONS, COLORS, TRANSMISSIONS } from '../../../app-services/get-ready-dropdown';
import { STATES } from '../../../app-services/state.service';
import { Vehicle } from '../get-ready.model';

import { format } from 'date-fns';


@Component({
    selector: 'app-vehicle-selection',
    templateUrl: './vehicle-selection.component.html',
    viewProviders: [{ provide: ControlContainer, useExisting: NgForm }],
    styleUrls: ['./vehicle-selection.component.scss'],
    standalone: false
})


export class VehicleSelectionComponent implements OnInit, OnChanges {
  @Input() type;
  plateTypeOptions: FilterOption[] = PLATE_TYPES;
  registrationTypeOptions = REGISTRATION_TYPES;
  registrationReasonOptions = REGISTRATION_REASONS;
  primaryColorOptions = COLORS;
  transmissionOptions = TRANSMISSIONS;
  stateOptions = STATES;

  plate;
  vin;
  plateType;
  transferPlate;
  transferType;
  constructor(private specsService: SpecsService) {}
  ownershipType = [];
  ownership;
  vehicle: Vehicle = {id: 1, lookupType: '', usage: '',
                      ownership: '', condition: '', primaryColor: '', transmission: '',
                      passengers: '', outOfStateTitleNumber: '', titleIssueDate: '', titleState: '',
                      plateNumber: '', vin: '', plateType: '', odometer: '',
                      registrationReason: '', reassignedPlate: '', registrationType: '', };
  ngOnInit() {
    this.resetForm();
    this.specsService
      .getOwnershipsAsOptions()
      // remove BusinessOwned until lookup is implemented
      .subscribe(x => {this.ownershipType = x; });
  }

  ngOnChanges() {
    this.resetForm();
  }

  resetForm() {
    this.vehicle = {id: 1, lookupType: '', usage: '',
                      ownership: '', condition: '', primaryColor: '', transmission: '',
                      passengers: '', outOfStateTitleNumber: '', titleIssueDate: '', titleState: '',
                      plateNumber: '', vin: '', plateType: '', odometer: '',
                      registrationReason: '', reassignedPlate: '', registrationType: '', };
    // this.ownershipType = [];
  }

  selectRMVlookupPlateType(ev, model) {
    model = ev.id;
  }

  checkIfTitleOnly() {
    return this.type === 'PrefillTitleOnly' || this.type === 'PrefillApplyForSalvageTitle' ||
     this.type === 'PrefillRegisterPreviouslyTitledVehicle' ? this.type : '';
  }

  checkIfReinstateOnly() {
    return this.type === 'PrefillRenewRegistration' ||
            this.type === 'PrefillReinstateRegistration' ||
            this.type === 'PrefillTitlePreviouslyRegisteredVehicle' ||
            this.type === 'PrefillAmendRegistration' ? this.type : '';
  }

  checkIfPlateToTransfer() {
if (this.type === 'PrefillRegistrationTransfer' || this.type === 'PrefillTransferPlateBetweenTwoVehicles'
 || this.type === 'PrefillChangePlateOnExistingVehicle' || this.type === 'PrefillTransferVehicleToSurvivingSpouse') {
   this.transferPlate = true;
  return this.type;
 } else {
   this.transferPlate = false;
 }
  }

  setDate($ev, vehicle) {
    vehicle.titleIssueDate = $ev.formatedDate ? format(new Date($ev.formatedDate), 'yyyyMMdd') : '';
  }
}
