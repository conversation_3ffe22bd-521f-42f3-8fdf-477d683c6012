import { WARNING_GROUPS, WarningDefinitionI, AdditionalDataI, AdditionalDataAutoClientInfo} from 'app/hints-and-warnings/model/warnings';
import { ClientAddress, ClientContactMethod, ClientDetails } from 'app/app-model/client';
import { Validate } from 'app/hints-and-warnings/validators';



function requiredField(value):boolean {
  if(value == undefined || value == null) {
    return true;
  }
  value = String(value);

  return value.trim().length <= 0 || !value;
}

function generateViewUrl(clientDetails: ClientDetails):string {
  return  '/dashboard/clients/' + clientDetails.resourceId + '/clientInfo';

}

/**
 * Validation for Client Info Data
 * For Model: ClientDetails
 */


const clientDetailsFirstName:WarningDefinitionI = {
  id: 'fNameDetails',
  deepId: 'fNameDetails',
  viewUri: generateViewUrl,
  viewFieldId: 'clientDetailsPrimaryFirstName',
  warnings: [{
    label: 'Required First Name for client.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}

const clientDetailsLastName:WarningDefinitionI = {
  id: 'lNamDetails',
  deepId: 'lNamDetails',
  viewUri: generateViewUrl,
  viewFieldId: 'clientDetailsPrimaryLastName',
  warnings: [{
    label: 'Required Last Name for client',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}

const clientDetailsDOB:WarningDefinitionI = {
  id: 'dobDetails',
  deepId: 'dobDetails',
  viewUri: generateViewUrl,
  viewFieldId: 'clientDetailsPrimaryDOB',
  warnings: [{
    label: 'Required Date of Birth for client.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}

const clientDetailsBusinessName:WarningDefinitionI = {
  id: 'businessName',
  deepId: 'businessName',
  viewUri: generateViewUrl,
  viewFieldId: 'clientDetailsBusinessName',
  warnings: [{
    label: 'Required Business Name for client.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}

const clientDetailsLegalEntity:WarningDefinitionI = {
  id: 'legalEntity',
  deepId: 'legalEntity',
  viewUri: generateViewUrl,
  viewFieldId: 'clientDetailsBusinessLegalEntity',
  warnings: [{
    label: 'Required Legal Entity for client.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}


export const WARNINGS_DEFINITIONS_INFO_CLIENT: WarningDefinitionI[] = [
  clientDetailsFirstName,
  clientDetailsLastName,
  //clientDetailsDOB,
  clientDetailsBusinessName,
  clientDetailsLegalEntity
];
