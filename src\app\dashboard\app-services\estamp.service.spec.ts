import { inject, TestBed } from '@angular/core/testing';

TestBed.configureTestingModule({
  providers: [EstampService]
});

import { DataCustomMatchers, expect } from 'testing/helpers/all';

import { EstampService } from './estamp.service';

describe('Service: Forms', () => {
  let service: EstampService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        EstampService
      ]
    });

    jasmine.addMatchers(DataCustomMatchers);
  });

  beforeEach(inject(
    [EstampService],
    (_service: EstampService) => {
      service = _service;
    }));

  it('can instantiate service when inject service', () => {
    expect(service).toBeTruthy();
  });

});
