import { inject, TestBed } from '@angular/core/testing';

import { Quote<PERSON>lan, QuotePlanRequired } from 'app/app-model/quote';

import { PlansService } from './plans.service';

describe('Service: Plans', () => {
  let service: PlansService;
  const PLAN_1: QuotePlan = new QuotePlan();
  const PLAN_2: QuotePlan = new QuotePlan();

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [PlansService]
    });
  });

  beforeEach(inject([PlansService], (_service: PlansService) => {
    service = _service;

    PLAN_1.meta = { href: '/specs/ratingplans/1', rel: ['SpecRatingPlan'] };
    PLAN_1.ratingPlanId = '1';
    PLAN_1.state = 'MA';
    PLAN_1.lob = 'AUTOP';
    PLAN_1.carrier = 'ACE Bankers Standard';
    PLAN_1.naic = '18279';
    PLAN_1.name = 'ACE Bankers Standard';
    PLAN_1.resourceName = null;

    PLAN_2.meta = { href: '/specs/ratingplans/2', rel: ['SpecRatingPlan'] };
    PLAN_2.ratingPlanId = '2';
    PLAN_2.state = 'MA';
    PLAN_2.lob = 'AUTOP';
    PLAN_2.carrier = 'MAIP(CAR)';
    PLAN_2.naic = '22222';
    PLAN_2.name = 'MAIP(CAR)';
    PLAN_2.resourceName = null;
  }));

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Method: checkIfSelectedPlansAreInRequiredPlans', () => {
    it('should return "false" if plans are not in required plans', () => {
      const selectedPlans: QuotePlan[] = [PLAN_1, PLAN_2];
      const requiredPlans: QuotePlanRequired[] = [
        { ratingPlanId: '10', name: 'AIG'},
        { ratingPlanId: '16', name: 'Harleysville'},
        { ratingPlanId: '8', name: 'Liberty'},
      ];

      const result = service.checkIfSelectedPlansAreInRequiredPlans(selectedPlans, requiredPlans);

      expect(result).toBe(false);
    });

    it('should return "true" if at least one plan is in required plans', () => {
      const selectedPlans: QuotePlan[] = [PLAN_1, PLAN_2];
      const requiredPlans: QuotePlanRequired[] = [
        { ratingPlanId: '10', name: 'AIG'},
        { ratingPlanId: '16', name: 'Harleysville'},
        { ratingPlanId: '8', name: 'Liberty'},
        { ratingPlanId: '2', name: 'MAIP (CAR)'},
      ];

      const result = service.checkIfSelectedPlansAreInRequiredPlans(selectedPlans, requiredPlans);

      expect(result).toBe(true);
    });
  });


  describe('Method: getPlansIdsListFromQuotePlansList', () => {
    it('should return array with ids of QuotePlans', () => {
      const selectedPlans: QuotePlan[] = [PLAN_1, PLAN_2];

      const result = service.getPlansIdsListFromQuotePlansList(selectedPlans);

      expect(result).toEqual([PLAN_1.ratingPlanId, PLAN_2.ratingPlanId]);
    });
  });
});
