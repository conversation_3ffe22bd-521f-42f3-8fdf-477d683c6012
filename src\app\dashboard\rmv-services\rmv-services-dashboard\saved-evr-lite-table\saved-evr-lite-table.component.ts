import { Component, OnInit, ViewChild } from '@angular/core';
import { NavigationExtras, Router } from '@angular/router';
import { FilterOption } from 'app/app-model/filter-option';
import { RmvService } from 'app/dashboard/app-services/rmv.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { ModalboxComponent } from 'app/shared/components/modalbox/modalbox.component';
import { take } from 'rxjs/operators';
import { AgencyUserService } from '../../../../shared/services/agency-user.service';
import { DateRange } from 'app/app-model/date';
import { FilterDatesComponent } from 'app/shared/components/filter-dates/filter-dates.component';
import { saveAs } from 'file-saver';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { ToastrService } from 'ngx-toastr';
import { format } from 'date-fns';

@Component({
    selector: 'app-saved-evr-lite-table',
    templateUrl: './saved-evr-lite-table.component.html',
    styleUrls: ['./saved-evr-lite-table.component.scss'],
    standalone: false
})
export class SavedEvrLiteTableComponent implements OnInit {
  @ViewChild('deleteModalbox') public deleteModalbox: ModalboxComponent;
  @ViewChild('expired') public expiredModalbox: ModalboxComponent;
  @ViewChild(FilterDatesComponent) filterDates;

  list = [];
  criteria;
  searchQuery;
  dataToChange;
  size = 0;
  limit = 10;
  paginationResultShowFrom = 0;
  userId;
  agencyId;
  dataInitiated = false;
  searchType = 'vin';
  typeOptions;
  expiredId;
  expiredTransactionType;
  expireMessage = '';
  isDatesFilterViewEnabled = false;
  paginationCurrentPage = 1;

  public filterStatusOptions: FilterOption[] = [
    { id: '0', text: 'All' },
    { id: '1', text: 'Open' },
    { id: '2', text: 'Closed' }
  ];

  public filterRequesterOptions: FilterOption[] = [
    { id: '0', text: 'Myself' },
    { id: '1', text: 'All' },
  ];

  filterStatusSelectedOption: FilterOption = this.filterStatusOptions[0];
  filterLocationsSelectedOption: FilterOption = this.filterRequesterOptions[1];

  public isAllSelected = false;
  public selectedItems: string[] = [];
  public dateRange: DateRange = new DateRange();

  constructor(
    private rmvService: RmvService,
    private specsService: SpecsService,
    private router: Router,
    private userService: AgencyUserService,
    private overlayLoaderService: OverlayLoaderService,
    private toastService: ToastrService
  ) { }

  ngOnInit(): void {
    setTimeout(() => this.getTransactions(true));
    this.specsService.getTransactionsAsOptions().subscribe(x => this.typeOptions = x);
    this.userService.userData$.subscribe(x => { this.userId = x.user.userId; this.agencyId = x.agencyId; });
    this.isDatesFilterViewEnabled = JSON.parse(localStorage.getItem('features')).find(x => x.name === 'SprApp_DatesFilterView');
  }

  getDisplayName(transactionType) {
    return this.typeOptions.find(x => x.id === transactionType).text;
  }

  filterByName(event) {
    this.filterDates.searchQuery = this.searchQuery;
    if ((event.key === 'Enter' && this.searchQuery.length >= 3) || (event.target.tagName === 'BUTTON' && this.searchQuery.length >= 3)) {
      this.filterDates.filterDatesLabel = 'All Dates';
      this.getTransactions();
    }
  }

  public paginationPageChange(data) {
    if (data.startAt < 0 || Object.is(NaN, data.startAt)) {
      data.startAt = 0;
      data.pageNumber = 1;
    }
    if (this.dataInitiated && this.paginationResultShowFrom !== data.startAt) {
      this.paginationCurrentPage = data.pageNumber;
      this.paginationResultShowFrom = data.startAt;
      this.getTransactions();
    }


  }

  private paginationSetResultLimit(intLimit: any) {
    this.limit = parseInt(intLimit, 10);
  }

  public onResultLimitChange($ev): void {
    if (this.limit !== $ev.limit) {
      setTimeout(() => {
        this.paginationSetResultLimit($ev.limit);
        this.getTransactions();
      }
      );
    };
  }

  private getTransactions(showResultsFromFirstPage = false) {
    if (showResultsFromFirstPage) {
      this.paginationResultShowFrom = 0;
      this.paginationCurrentPage = 1;
    }

    const location = this.filterLocationsSelectedOption.text === 'All' ? this.agencyId : '';
    const userId = this.filterLocationsSelectedOption.text === 'Myself' ? this.userId : 0;
    const status = this.filterStatusSelectedOption.text === 'All' ? 'None' : this.filterStatusSelectedOption.text;

    let startDate, endDate;

    let criteria = this.searchQuery !== '' && this.searchQuery !== undefined ? `?${this.searchType}=${this.searchQuery}` : '';
    if (criteria == '') {
      startDate = this.dateRange.start ? format(new Date(this.dateRange.start), 'yyyy-MM-dd') : '';
      endDate = this.dateRange.end ? format(new Date(this.dateRange.end), 'yyyy-MM-dd') : '';
    }
    criteria !== '' ? criteria += `&location=${location}&status=${status}&workflowType=EvrLite&userId=${userId}` :
      criteria = `?location=${location}&status=${status}&workflowType=EvrLite&userId=${userId}`;

    if (startDate != undefined && endDate != undefined)
      criteria = criteria + `&startDate=` + startDate + '&endDate=' + endDate;

    // criteria = `?offset=${this.paginationResultShowFrom}`;
    this.paginationResultShowFrom = isNaN(this.paginationResultShowFrom) ? 0 : this.paginationResultShowFrom;

    this.rmvService.getSavedRmvList(criteria, this.paginationResultShowFrom, this.limit).pipe(take(1)).subscribe(x => {
      this.dataInitiated = true;
      this.list = x.items; this.size = x.size;
    });

    if (this.searchQuery === '') {
      this.searchType = 'vin';
    }

  }

  resetSearch() {
    this.filterDates.prepareResetSearch();
    this.dateRange.start = null;
    this.dateRange.end = null;
    this.getTransactions();
  }

  public onFilterDataChange($ev): void {
    this.filterDataChangeUpdate($ev);
    // this.filterResults();
  }

  private onStatusChange(option: FilterOption): void {
    this.filterStatusSelectedOption = option;
  }

  private onLocationChange(option: FilterOption): void {
    this.filterLocationsSelectedOption = option;
  }

  private filterDataChangeUpdate($ev): void {
    if (this.dataToChange) {
      this.dataToChange = $ev;
      switch ($ev.filterId) {
        case 'filter_Status':
          this.onStatusChange($ev.selectedOption);
          break;
        case 'filter_locations':
          this.onLocationChange($ev.selectedOption);
          break;
        case 'filter_requester':
          this.onLocationChange($ev.selectedOption);
          break;
      }
      this.getTransactions(true);
    } else {
      this.dataToChange = $ev;
    }
  }

  public isSelected(Identifier: string, status = ''): boolean {
    return (this.selectedItems.indexOf(Identifier) > -1 || this.isAllSelected && !this.checkStatus(status));
  }

  public toggleSelected(Identifier: string): void {
    const itemIndex = this.selectedItems.indexOf(Identifier);
    const isPresent = (itemIndex > -1);

    if (isPresent) {
      this.selectedItems.splice(itemIndex, 1);
    } else {
      this.selectedItems.push(Identifier.toString());
    }
  }

  public isChecked(): boolean {
    return this.isAllSelected;
  }

  public deleteSelectedRequest() {
    const deleteArray = [];
    this.selectedItems.forEach(x => { deleteArray.push(x); });
    this.rmvService.deleteRmvTransactions(deleteArray).subscribe(x => {
      this.selectedItems = [];
      this.deleteModalbox.close();
      this.getTransactions();
    });
  }

  public toggleAll(): void {
    this.isAllSelected = !this.isAllSelected;

    if (this.isAllSelected) {
      this.list.forEach(item => {
        if (this.selectedItems.indexOf(item.rmvServicesId) === -1 && !item.status.includes('Payment')) {
          this.selectedItems.push(item.rmvServicesId.toString());
        }
      });
    } else {
      this.selectedItems = [];
    }
  }

  public toggleTransactions(stampIdentifier: string): void {
    const itemIndex = this.selectedItems.indexOf(stampIdentifier);
    const isPresent = (itemIndex > -1);

    if (isPresent) {
      this.selectedItems.splice(itemIndex, 1);
    } else {
      this.selectedItems.push(stampIdentifier.toString());
    }
  }

  redirectToEvrLite(id, status, type): void {
    const params: NavigationExtras = {
      queryParams: { id: id }
    };
    if (status === 'Expired') {
      this.expiredId = id;
      this.expiredTransactionType = type;
      this.expireMessage = `You are attempting to open a transaction that has expired.  This transaction must be re-initiated with the RMV before it can be processed.

    SinglePoint can rebuild this transaction for you with the data you previously entered.  Would you like to proceed?

    Note: DO NOT proceed if you have sent the Payment Request to your customer AND it has been PAID.  Cancel and contact Boston Software if this has occurred.  `;
      this.expiredModalbox.open();

    } else {
      this.router.navigate(['/dashboard/rmv-services/evr-lite'], params);
    }

  }

  splitWords(word) {
    return word.replace(/([A-Z])/g, ' $1').replace('Prefill', '').trim();
  }

  goToRmvServices() {
    this.router.navigate([`/dashboard/rmv-services/rta-prefill`], { queryParams: { id: this.expiredId, 'type': this.expiredTransactionType } });

  }

  checkStatus(status) {
    switch (status) {
      case 'PaymentRequested':
      case 'PaymentReceived':
      case 'Complete':
        return true;
      default:
        return false;
    }
  }

  public showHideDiv(): boolean {
    let result = true;
    if (this.isDatesFilterViewEnabled)
      result = this.searchQuery && this.searchQuery.length > 0;
    return result;
  }

  public IsDatesFilterViewEnabled(): boolean {
    return this.isDatesFilterViewEnabled;
  }

  getTransactionsFromFilteredDates() {

    this.dateRange = this.filterDates.dateRange;

    var allowFiltering = this.filterDates.isFilteringAllowed();

    if (allowFiltering) {
      this.getTransactions();
    }
    this.filterDates.tooltip.close();

  }
  downloadDocuments(row) {
    this.overlayLoaderService.showLoader();
    this.rmvService.downloadDocuments(row.rmvServicesId).subscribe(x => {
      const blob = new Blob([x], {
        type: 'application/zip'
      });
      saveAs(blob,`${row.ownerFirstName}_${row.ownerLastName}_RmvDocs` );
      this.overlayLoaderService.hideLoader();
    }, err => {this.overlayLoaderService.hideLoader()
      this.toastService.warning('Sorry, Download error, please try again.')
      })


  }


}
