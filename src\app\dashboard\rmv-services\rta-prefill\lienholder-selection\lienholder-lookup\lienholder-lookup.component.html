<section class="section">
  <div class="row">
    <div class="col-xs-9">
      <div class="u-flex u-flex--to-middle">
        <h1 class="o-heading u-spacing--right-2-5">Select Lienholder</h1>
        <div class="search">

          <input #searchInput [id]="'filter_criteria'" [(ngModel)]="searchQuery" (keydown.enter)="filter()"
            class="search__input search__input--md" type="text" placeholder="Search By Name or Code"
            changeDetectionDelay />
            <button *ngIf="searchQuery && searchQuery.length > 0"
              (click)="reset($event)"
              name="cancel"
              class="reset__button"
            ></button>
          <button (click)="filter()" type="button" name="searchbtn" class="search__button">
          </button>
        </div>
      </div>

    </div>
    <div class="col-xs-3">
      <div class="u-spacing--left-2 u-align-right">
        <app-pagination [totalRecords]="paginationResultsCount" [recordsLimit]="8"
          (onPageChange)="paginationPageChange($event)"></app-pagination>
      </div>
    </div>

  </div>
  <span style="color: red" *ngIf="searchQuery && searchQuery.length < 3">Minimum search length is 3</span>
</section>

<div class="row">
  <app-loader [loading]="loadingLienholders"></app-loader>
  <div class="col-xs-12">
    <table class="table table--compact table--hoverable form-table">
      <thead class="table__thead">
        <tr class="">
          <th class="table__th u-width-40px">
            <!-- Checkbox -->
          </th>
          <th class="table__th">Lienholder Name</th>
          <th class="table__th">Address</th>
          <th class="table__th">Code</th>

        </tr>
      </thead>

      <tbody class="table__tbody" *ngIf="results">
        <tr class="table__tr" *ngFor="let row of results" (click)="setSelectedLienholder(row)" (dblclick)="setSelectedLienholder(row);saveSelectedLienholder()">
          <td class="table__td">
            <div class="checklist__btns">
              <label class="o-checkable">
                <input type="radio" name="lienholder-group" [checked]="isLienholderSelected(row)" />
                <i class="o-btn o-btn--radio"></i>
              </label>
            </div>
          </td>
          <td class="table__td u-color-pelorous">
            {{ row.name }}

          </td>
          <td class="table__td">{{row.addressLine1}} {{row.city}} {{row.state}} {{row.postalCode}}
          </td>

          <td class="table__td">
            {{row.code }}
          </td>
        </tr>
      </tbody>
      <tbody class="table__tbody" *ngIf="!results?.length">
        <tr class="table__tr">
          <td colspan="6">
            <p class="u-padd--bottom-1 u-padd--1">There are no results that match your search.</p>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</div>
<div class="row u-spacing--2">
  <div class="col-xs-12 u-align-right">
    <button (click)="saveSelectedLienholder()" class="o-btn u-spacing--left-2" [disabled]="!selectedLienholder">Select Lienholder</button>
    <button (click)="closeModal()" class="o-btn o-btn--idle u-spacing--left-2">Cancel</button>
  </div>
</div>

