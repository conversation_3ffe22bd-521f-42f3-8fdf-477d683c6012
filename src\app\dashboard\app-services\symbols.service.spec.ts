import { inject, TestBed } from '@angular/core/testing';

import { VEHICLE_GENERAL_DETAILS } from 'testing/data/lookups/vin';
import { data as VEHICLE_DATA } from 'testing/data/quotes/vehicle';
import { expect, expectLastCallArgs } from 'testing/helpers/all';
import { DataCustomMatchers } from 'testing/helpers/data-custom-matchers';
import {
    StubLookupsService, StubLookupsServiceProvider
} from 'testing/stubs/services/lookups.service.provider';

import { QuotePlan } from 'app/app-model/quote';
import { SelectedVehicleSymbols } from 'app/app-model/symbols';
import {
    Vehicle, VehicleGeneralDetails, VehiclePlanSymbols, VehicleSymbol
} from 'app/app-model/vehicle';
import { LookupsService } from 'app/dashboard/app-services/lookups.service';
import { PlansService } from 'app/dashboard/app-services/plans.service';
import { Helpers } from 'app/utils/helpers';

import { SymbolsService } from './symbols.service';

const VRG_COLL_SYMBOL: VehicleSymbol = new VehicleSymbol();
VRG_COLL_SYMBOL.symbolType = 'POLK';
VRG_COLL_SYMBOL.symbolValue = '11';
VRG_COLL_SYMBOL.codeType = 'Coll';
VRG_COLL_SYMBOL.conversionType = 'NA';
VRG_COLL_SYMBOL.codeSource = 'VinMaster';
VRG_COLL_SYMBOL.description = 'VRG Collision';

const VRG_COMP_SYMBOL: VehicleSymbol = new VehicleSymbol();
VRG_COMP_SYMBOL.symbolType = 'POLK';
VRG_COMP_SYMBOL.symbolValue = '3';
VRG_COMP_SYMBOL.codeType = 'Comp';
VRG_COMP_SYMBOL.conversionType = 'NA';
VRG_COMP_SYMBOL.codeSource = 'VinMaster';
VRG_COMP_SYMBOL.description = 'VRG Comprehensive';

const QUOTE_EFFECTIVE_DATE = '2017-06-13';

const VEHICLE_YEAR = '2015';
const VEHICLE_MAKE = 'BMW';
const VEHICLE_PRICE = '1928374';
const VEHICLE_SYMBOLS: VehicleSymbol[] = [VRG_COLL_SYMBOL, VRG_COMP_SYMBOL];

// Plan symbols
const PLAN_SYMBOL_1: VehiclePlanSymbols = new VehiclePlanSymbols();
const PLAN_SYMBOL_2: VehiclePlanSymbols = new VehiclePlanSymbols();
const PLAN_SYMBOL_3: VehiclePlanSymbols = new VehiclePlanSymbols();

const SYMBOL_1: VehicleSymbol = new VehicleSymbol();
const SYMBOL_2: VehicleSymbol = new VehicleSymbol();
const SYMBOL_3: VehicleSymbol = new VehicleSymbol();
const SYMBOL_4: VehicleSymbol = new VehicleSymbol();
const SYMBOL_5: VehicleSymbol = new VehicleSymbol();

SYMBOL_1.symbolType = 'ISO75';
SYMBOL_1.symbolValue = '36';
SYMBOL_1.codeType = 'Comp';
SYMBOL_1.conversionType = 'NA';
SYMBOL_1.codeSource = 'VinMaster';
SYMBOL_1.description = 'ISO 75 Comprehensive';

SYMBOL_2.symbolType = 'ISO75';
SYMBOL_2.symbolValue = '48';
SYMBOL_2.codeType = 'Coll';
SYMBOL_2.conversionType = 'NA';
SYMBOL_2.codeSource = 'VinMaster';
SYMBOL_2.description = 'ISO 75 Collision';

SYMBOL_3.symbolType = 'ISO27';
SYMBOL_3.symbolValue = '22';
SYMBOL_3.codeType = 'NA';
SYMBOL_3.conversionType = 'BSC';
SYMBOL_3.codeSource = 'VinMaster';
SYMBOL_3.description = 'ISO 1-27 (BSC Conversion)';

SYMBOL_4.symbolType = 'ISO75';
SYMBOL_4.symbolValue = '49';
SYMBOL_4.codeType = 'Comp';
SYMBOL_4.conversionType = 'NA';
SYMBOL_4.codeSource = 'VinMaster';
SYMBOL_4.description = 'ISO 75 Comprehensive';

SYMBOL_5.symbolType = 'ISO75';
SYMBOL_5.symbolValue = '50';
SYMBOL_5.codeType = 'Coll';
SYMBOL_5.conversionType = 'NA';
SYMBOL_5.codeSource = 'VinMaster';
SYMBOL_5.description = 'ISO 75 Collision';


PLAN_SYMBOL_1.ratingPlanId = '1';
PLAN_SYMBOL_1.symbols = [SYMBOL_1, SYMBOL_2];

PLAN_SYMBOL_2.ratingPlanId = '2';
PLAN_SYMBOL_2.symbols = [SYMBOL_3];

PLAN_SYMBOL_3.ratingPlanId = '2';
PLAN_SYMBOL_3.symbols = [SYMBOL_4, SYMBOL_5];

// Selected Vehicle
const SELECTED_VEHICLE = new Vehicle();
      SELECTED_VEHICLE.year = VEHICLE_YEAR;
      SELECTED_VEHICLE.make = VEHICLE_MAKE;
      SELECTED_VEHICLE.priceValue = VEHICLE_PRICE;
      SELECTED_VEHICLE.symbols = VEHICLE_SYMBOLS;

const SELECTED_VEHICLE_GENERAL_DETAILS: VehicleGeneralDetails = new VehicleGeneralDetails();
      SELECTED_VEHICLE_GENERAL_DETAILS.collisionSymbol = '50';
      SELECTED_VEHICLE_GENERAL_DETAILS.comprehensiveSymbol = '38';
      SELECTED_VEHICLE_GENERAL_DETAILS.massAntiTheftCode = '3';
      SELECTED_VEHICLE_GENERAL_DETAILS.massBodyStyleGroup = 'All Other PP Types';
      SELECTED_VEHICLE_GENERAL_DETAILS.massHighTheftCode = '0';
      SELECTED_VEHICLE_GENERAL_DETAILS.polkRestraintCode = 'E';
      SELECTED_VEHICLE_GENERAL_DETAILS.resourceName = 'VehicleLookup';
      SELECTED_VEHICLE_GENERAL_DETAILS.safetyCustomSymbol = '';
      SELECTED_VEHICLE_GENERAL_DETAILS.seriesName = 'A3 1.8T PREMIUM PLUS';
      SELECTED_VEHICLE_GENERAL_DETAILS.symbol = '23';
      SELECTED_VEHICLE_GENERAL_DETAILS.vin = 'WAUCCHFF&G';
      SELECTED_VEHICLE_GENERAL_DETAILS.vrgDetail = null;
      SELECTED_VEHICLE_GENERAL_DETAILS.year = '2016';


// Quote Plans
const PLAN_1: QuotePlan = new QuotePlan();
const PLAN_2: QuotePlan = new QuotePlan();

PLAN_1.meta = {href: '/specs/ratingplans/1', rel: ['SpecRatingPlan']};
PLAN_1.ratingPlanId = '1';
PLAN_1.state = 'MA';
PLAN_1.ratingFlow = 'MFD';
PLAN_1.productCode = 'YA';
PLAN_1.lob = 'AUTOP';
PLAN_1.carrier = 'ACE Bankers Standard';
PLAN_1.naic = '18279';
PLAN_1.name = 'ACE Bankers Standard';
PLAN_1.resourceName = null;

PLAN_2.meta = {href: '/specs/ratingplans/2', rel: ['SpecRatingPlan']};
PLAN_2.ratingPlanId = '2';
PLAN_2.state = 'MA';
PLAN_2.lob = 'AUTOP';
PLAN_2.carrier = 'MAIP(CAR)';
PLAN_2.naic = '22222';
PLAN_2.name = 'MAIP(CAR)';
PLAN_2.resourceName = null;

const QUOTE_PLAN_LIST: QuotePlan[] = [PLAN_1, PLAN_2];


describe('Service: Symbols', () => {
  let service: SymbolsService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        SymbolsService,
        PlansService,
        StubLookupsServiceProvider
      ]
    });

    jasmine.addMatchers(DataCustomMatchers);
  });

  beforeEach(inject([SymbolsService], (_service: SymbolsService) => {
    service = _service;
  }));

  it('should be created', () => {
    expect(service).toBeTruthy();
  });


  describe('Method: sortSymbols', () => {
    it('should sort symbols by symbols description', () => {
      const arrSymbolsNotSorted = [SYMBOL_1, SYMBOL_3, SYMBOL_2];
      const arrSymbolsPredictedOrder = [SYMBOL_3, SYMBOL_2, SYMBOL_1];

      const result = service.sortSymbols(arrSymbolsNotSorted);

      expect(result).not.toEqual(arrSymbolsNotSorted);
      expect(result).toEqual(arrSymbolsPredictedOrder);
    });
  });


  describe('Method: extractSymbolsToArrayFromPlanSymbols',  () => {
    it('extractSymbolsToArrayFromPlanSymbols: should extract Symbols to array from PlanSymbols array', () => {
      const arrPlanSymbols: VehiclePlanSymbols[] = [PLAN_SYMBOL_1, PLAN_SYMBOL_2, PLAN_SYMBOL_3];
      const predictedResult = [SYMBOL_1, SYMBOL_2, SYMBOL_3, SYMBOL_4, SYMBOL_5];

      const result = service.extractSymbolsToArrayFromPlanSymbols(arrPlanSymbols);

      expect(result).toEqual(predictedResult);
    });
  });

  describe('Method: mapSymbolsArrayToSelectedVehicleSymbolsBySymbolDescription', () => {
    it('should map Symbols to SelectedVehicleSymbols', () => {
      const selectedSymbols: SelectedVehicleSymbols = new SelectedVehicleSymbols();
      const arrSymbols: VehicleSymbol[] = [SYMBOL_1, SYMBOL_2, SYMBOL_3, VRG_COLL_SYMBOL, VRG_COMP_SYMBOL];

      service.mapSymbolsArrayToSelectedVehicleSymbolsBySymbolDescription(arrSymbols, selectedSymbols);

      expect(selectedSymbols.iso27.symbol).toEqual(SYMBOL_3);
      expect(selectedSymbols.iso75Coll.symbol).toEqual(SYMBOL_2);
      expect(selectedSymbols.iso75Comp.symbol).toEqual(SYMBOL_1);
      expect(selectedSymbols.vrgColl.symbol).toEqual(VRG_COLL_SYMBOL);
      expect(selectedSymbols.vrgComp.symbol).toEqual(VRG_COMP_SYMBOL);
      expect(selectedSymbols.iso27Safety.symbol.symbolValue).toBeFalsy();
    });
  });

  describe('Method: manageSelectedVehicleSymbolsRequiredFieldsBasedOnSelectedPlans',  () => {
    it('should set required symbols based on selected plans - should override values', () => {
      const selectedSymbols: SelectedVehicleSymbols = new SelectedVehicleSymbols();
      const selectedPlans: QuotePlan[] = [PLAN_1, PLAN_2];
      const vehicle = SELECTED_VEHICLE;

      // PLAN_1 = ISO75
      // PLAN_2 = VRG

      selectedSymbols.iso27Safety.required = true;

      const result = service.manageSelectedVehicleSymbolsRequiredFieldsBasedOnSelectedPlans(selectedSymbols, selectedPlans, vehicle, false);

      expect(result.iso75Coll.required).toBe(true);
      expect(result.iso75Comp.required).toBe(true);
      expect(result.vrgColl.required).toBe(true);
      expect(result.vrgComp.required).toBe(true);
      expect(result.iso27.required).toBe(false);
      expect(result.iso27Safety.required).toBe(false);
    });


    it('should set required symbols based on selected plans - should not override values if already set to true', () => {
      const selectedSymbols: SelectedVehicleSymbols = new SelectedVehicleSymbols();
      const selectedPlans: QuotePlan[] = [PLAN_1];
      const vehicle = SELECTED_VEHICLE;

      // PLAN_1 = ISO75
      // PLAN_2 = VRG

      selectedSymbols.iso27Safety.required = true;

      const result = service.manageSelectedVehicleSymbolsRequiredFieldsBasedOnSelectedPlans(selectedSymbols, selectedPlans, vehicle, true);

      expect(result.iso75Coll.required).toBe(true);
      expect(result.iso75Comp.required).toBe(true);
      expect(result.vrgColl.required).toBe(false);
      expect(result.vrgComp.required).toBe(false);
      expect(result.iso27.required).toBe(false);
      expect(result.iso27Safety.required).toBe(true);
    });
  });


  describe('Method: manageSelectedVehicleSymbolsRequiredFieldsBasedOnVehicleSymbols', () => {
    it('should set required symbols based on VehicleSymbols - only for all symbols passed as a first argument', () => {
      const selectedSymbols: SelectedVehicleSymbols = new SelectedVehicleSymbols();
      const arrSymbols: VehicleSymbol[] = [SYMBOL_1, SYMBOL_3];

      selectedSymbols.iso27Safety.required = true;

      const result = service.manageSelectedVehicleSymbolsRequiredFieldsBasedOnVehicleSymbols(arrSymbols, selectedSymbols);

      expect(result.iso27.required).toBe(true);
      expect(result.iso27Safety.required).toBe(false);
      expect(result.vrgColl.required).toBe(false);
      expect(result.vrgComp.required).toBe(false);
      expect(result.iso75Coll.required).toBe(false);
      expect(result.iso75Comp.required).toBe(true);
    });
  });

  /*
  describe('Method: checkIfVehiclePriceNewValueIsRequiredForHintsAndWarnings',  () => {
    it('should return "false" if all required SelectedVehicleSymbols have values', () => {
      const selectedSymbols: SelectedVehicleSymbols = new SelectedVehicleSymbols();
      selectedSymbols.iso75Coll.symbol = SYMBOL_2;
      selectedSymbols.iso75Coll.required = true;
      selectedSymbols.iso75Comp.symbol = SYMBOL_1;
      selectedSymbols.iso75Comp.required = true;

      const result = service.checkIfVehiclePriceNewValueIsRequiredForHintsAndWarnings(selectedSymbols);

      expect(result).toBe(false);
    });

    it('should return "true" if any of the SelectedVehicleSymbols is required and has no value', () => {
      const selectedSymbols: SelectedVehicleSymbols = new SelectedVehicleSymbols();
      selectedSymbols.iso75Coll.symbol = SYMBOL_2;
      selectedSymbols.iso75Coll.required = true;
      selectedSymbols.iso75Comp.symbol = SYMBOL_1;
      selectedSymbols.iso75Comp.required = true;

      selectedSymbols.iso27Safety.required = true;

      const result = service.checkIfVehiclePriceNewValueIsRequiredForHintsAndWarnings(selectedSymbols);

      expect(result).toBe(true);
    });

    it('should return "true" if iso27 of SelectedVehicleSymbols is required and has value equal to "27"', () => {
      const selectedSymbols: SelectedVehicleSymbols = new SelectedVehicleSymbols();
      selectedSymbols.iso27.required = true;
      selectedSymbols.iso27.symbol = SYMBOL_3;
      selectedSymbols.iso27.symbol.symbolValue = '27';

      const result = service.checkIfVehiclePriceNewValueIsRequiredForHintsAndWarnings(selectedSymbols);

      expect(result).toBe(true);
    });

    it('should return "true" if iso75Coll of SelectedVehicleSymbols is required and has value equal to "98"', () => {
      const selectedSymbols: SelectedVehicleSymbols = new SelectedVehicleSymbols();
      selectedSymbols.iso75Coll.required = true;
      selectedSymbols.iso75Coll.symbol = SYMBOL_2;
      selectedSymbols.iso75Coll.symbol.symbolValue = '98';

      const result = service.checkIfVehiclePriceNewValueIsRequiredForHintsAndWarnings(selectedSymbols);

      expect(result).toBe(true);
    });

    it('should return "true" if iso75Comp of SelectedVehicleSymbols is required and has value equal to "98"', () => {
      const selectedSymbols: SelectedVehicleSymbols = new SelectedVehicleSymbols();
      selectedSymbols.iso75Comp.required = true;
      selectedSymbols.iso75Comp.symbol = SYMBOL_2;
      selectedSymbols.iso75Comp.symbol.symbolValue = '98';

      const result = service.checkIfVehiclePriceNewValueIsRequiredForHintsAndWarnings(selectedSymbols);

      expect(result).toBe(true);
    });
  });
  */


  describe('Method: checkIfVehiclePriceNewValueIsRequired',  () => {
    it('should return "true" even if all required SelectedVehicleSymbols have values', () => {
      const selectedSymbols: SelectedVehicleSymbols = new SelectedVehicleSymbols();
      selectedSymbols.iso75Coll.symbol = SYMBOL_2;
      selectedSymbols.iso75Coll.required = true;
      selectedSymbols.iso75Comp.symbol = SYMBOL_1;
      selectedSymbols.iso75Comp.required = true;

      // const result = service.checkIfVehiclePriceNewValueIsRequired(selectedSymbols);
      const result = SymbolsService.checkIfVehiclePriceNewValueIsRequired(selectedSymbols);

      expect(result).toBe(true);
    });

    it('should return "true" if any of the SelectedVehicleSymbols is required and has no value', () => {
      const selectedSymbols: SelectedVehicleSymbols = new SelectedVehicleSymbols();
      selectedSymbols.iso75Coll.symbol = SYMBOL_2;
      selectedSymbols.iso75Coll.required = true;
      selectedSymbols.iso75Comp.symbol = SYMBOL_1;
      selectedSymbols.iso75Comp.required = true;

      selectedSymbols.iso27Safety.required = true;

      // const result = service.checkIfVehiclePriceNewValueIsRequired(selectedSymbols);
      const result = SymbolsService.checkIfVehiclePriceNewValueIsRequired(selectedSymbols);

      expect(result).toBe(true);
    });

    it('should return "true" if iso27 of SelectedVehicleSymbols is required and has value equal to "27"', () => {
      const selectedSymbols: SelectedVehicleSymbols = new SelectedVehicleSymbols();
      selectedSymbols.iso27.required = true;
      selectedSymbols.iso27.symbol = SYMBOL_3;
      selectedSymbols.iso27.symbol.symbolValue = '27';

      // const result = service.checkIfVehiclePriceNewValueIsRequired(selectedSymbols);
      const result = SymbolsService.checkIfVehiclePriceNewValueIsRequired(selectedSymbols);

      expect(result).toBe(true);
    });

    it('should return "true" if iso75Coll of SelectedVehicleSymbols is required and has value equal to "98"', () => {
      const selectedSymbols: SelectedVehicleSymbols = new SelectedVehicleSymbols();
      selectedSymbols.iso75Coll.required = true;
      selectedSymbols.iso75Coll.symbol = SYMBOL_2;
      selectedSymbols.iso75Coll.symbol.symbolValue = '98';

      // const result = service.checkIfVehiclePriceNewValueIsRequired(selectedSymbols);
      const result = SymbolsService.checkIfVehiclePriceNewValueIsRequired(selectedSymbols);

      expect(result).toBe(true);
    });

    it('should return "true" if iso75Comp of SelectedVehicleSymbols is required and has value equal to "98"', () => {
      const selectedSymbols: SelectedVehicleSymbols = new SelectedVehicleSymbols();
      selectedSymbols.iso75Comp.required = true;
      selectedSymbols.iso75Comp.symbol = SYMBOL_2;
      selectedSymbols.iso75Comp.symbol.symbolValue = '98';

      // const result = service.checkIfVehiclePriceNewValueIsRequired(selectedSymbols);
      const result = SymbolsService.checkIfVehiclePriceNewValueIsRequired(selectedSymbols);

      expect(result).toBe(true);
    });
  });


  describe('Method: retrieveVehiclePlanSymbols', () => {

    it('should call function', () => {

      const spy = spyOn(service, 'retrieveVehiclePlanSymbols');

      service.retrieveVehiclePlanSymbols(
        SELECTED_VEHICLE,
        SELECTED_VEHICLE_GENERAL_DETAILS,
        QUOTE_EFFECTIVE_DATE,
        QUOTE_PLAN_LIST
      );

      expect(spy).toHaveBeenCalledTimes(1);

    });

    // it('should return Observabel<Array<VehiclePlanSymbols>>', () => {
    //   let spy = spyOn(service, 'retrieveVehiclePlanSymbols').and.callThrough();

    //   let result = service.retrieveVehiclePlanSymbols(
    //     SELECTED_VEHICLE,
    //     SELECTED_VEHICLE_GENERAL_DETAILS,
    //     QUOTE_EFFECTIVE_DATE,
    //     QUOTE_PLAN_LIST
    //   ).subscribe();

    //   // expect(result).to
    // });

  });

  describe('when symbols are updated', () => {
    let vehicle: Vehicle;

    beforeEach(() => {
      vehicle = Helpers.deepClone(VEHICLE_DATA);
    });

    it('should allow to set updating state to in progress', () => {
      const spy = jasmine.createSpy('spy');

      service.getSymbolsUpdatingInProgress$.subscribe(spy);

      service.setSymbolsUpdatingInProgress(vehicle, true);

      expectLastCallArgs(spy).toEqual([
        { 'f68fafc0-bb08-4fd5-8987-c3cde5f9c9be': true }
      ]);
    });

    it('should allow to reset updating state', () => {
      const spy = jasmine.createSpy('spy');

      service.getSymbolsUpdatingInProgress$.subscribe(spy);

      service.setSymbolsUpdatingInProgress(vehicle, false);

      expectLastCallArgs(spy).toEqual([
        { 'f68fafc0-bb08-4fd5-8987-c3cde5f9c9be': false }
      ]);
    });
  });

  describe('when using filterUniqueSymbolsBySymbolDescription helper', () => {
    it('should be able to return array containing only unique symbols', () => {
      const symbols = [
        SYMBOL_1,
        SYMBOL_2,
        SYMBOL_3,
        SYMBOL_4,
        SYMBOL_5
      ];

      const uniqueSymbols = service.filterUniqueSymbolsBySymbolDescription(symbols);

      expect(uniqueSymbols).toEqual([
        SYMBOL_1,
        SYMBOL_2,
        SYMBOL_3
      ]);
    });

    it('should be able to work with misshapen symbols descriptions', () => {
      const symbols = [
        SYMBOL_1,
        SYMBOL_2,
        new VehicleSymbol('VinMaster', 'Comp', 'NA', 'ISO 75  Comprehensive', 'ISO75', '36'),
        new VehicleSymbol('VinMaster', 'Comp', 'NA', 'ISO 75 Comprehensive ', 'ISO75', '36')
      ];

      const uniqueSymbols = service.filterUniqueSymbolsBySymbolDescription(symbols);

      expect(uniqueSymbols).toEqual([
        SYMBOL_1,
        SYMBOL_2
      ]);
    });
  });

  describe('when working with setSourceSymbolsForSelectedVehicleSymbols helper', () => {
    it('should be able to set sourceSymbol fields', () => {
      const selectedSymbols: SelectedVehicleSymbols = new SelectedVehicleSymbols();
      const sourceSymbols = [SYMBOL_1, SYMBOL_2, SYMBOL_3, VRG_COLL_SYMBOL, VRG_COMP_SYMBOL];

      service.setSourceSymbolsForSelectedVehicleSymbols(sourceSymbols, selectedSymbols);

      expect(selectedSymbols.iso27.sourceSymbol).toEqualIgnoringTypes(SYMBOL_3);
      expect(selectedSymbols.iso75Coll.sourceSymbol).toEqualIgnoringTypes(SYMBOL_2);
      expect(selectedSymbols.iso75Comp.sourceSymbol).toEqualIgnoringTypes(SYMBOL_1);
      expect(selectedSymbols.vrgColl.sourceSymbol).toEqualIgnoringTypes(VRG_COLL_SYMBOL);
      expect(selectedSymbols.vrgComp.sourceSymbol).toEqualIgnoringTypes(VRG_COMP_SYMBOL);
      expect(selectedSymbols.iso27Safety.sourceSymbol).toEqualIgnoringTypes(new VehicleSymbol());
    });

    it('should return selectedSymbols as well as update them in place', () => {
      const selectedSymbols = new SelectedVehicleSymbols();
      const sourceSymbols = [SYMBOL_1, SYMBOL_2, SYMBOL_3, VRG_COLL_SYMBOL, VRG_COMP_SYMBOL];

      const result = service.setSourceSymbolsForSelectedVehicleSymbols(sourceSymbols, selectedSymbols);

      expect(result).toBe(selectedSymbols);
    });
  });

  describe('when working with setSelectedVehicleSymbolsSymbolIfTheValueIsEmptyBasedOnSourceSymbol helper', () => {
    it('should update missing symbol fields based on source symbols', () => {
      const selectedSymbols = new SelectedVehicleSymbols();
      const sourceSymbols = [SYMBOL_1, SYMBOL_2, SYMBOL_3, VRG_COLL_SYMBOL, VRG_COMP_SYMBOL];

      service.setSourceSymbolsForSelectedVehicleSymbols(sourceSymbols, selectedSymbols);

      const resultSymbols = service.setSelectedVehicleSymbolsSymbolIfTheValueIsEmptyBasedOnSourceSymbol(selectedSymbols);

      expect(resultSymbols.iso27.symbol).toEqualIgnoringTypes(SYMBOL_3);
      expect(resultSymbols.iso75Coll.symbol).toEqualIgnoringTypes(SYMBOL_2);
      expect(resultSymbols.iso75Comp.symbol).toEqualIgnoringTypes(SYMBOL_1);
      expect(resultSymbols.vrgColl.symbol).toEqualIgnoringTypes(VRG_COLL_SYMBOL);
      expect(resultSymbols.vrgComp.symbol).toEqualIgnoringTypes(VRG_COMP_SYMBOL);
    });
  });

  /*
  describe('when working with checkIfVehiclePriceValueFieldRequiredBasedOnVehicleSymbolsAndSelectedPlans helper', () => {
    it('should correctly mark price value field as required', () => {
      const selectedSymbols = [SYMBOL_1, SYMBOL_2, SYMBOL_3];
      const selectedPlans = [PLAN_1, PLAN_2];
      const vehicle = Helpers.deepClone(VEHICLE_DATA);

      const required = service.checkIfVehiclePriceValueFieldRequiredBasedOnVehicleSymbolsAndSelectedPlans(
        selectedSymbols, vehicle, selectedPlans
      );

      expect(required).toBeTruthy();
    });
  });
  */

  describe('when using orderVehiclesSymbols helper', () => {
    it('should allow to sort vehicle symbols by description', () => {
      const vehicle = Helpers.deepClone(VEHICLE_DATA);

      const ordered = service.orderVehiclesSymbols([vehicle])[0].symbols;

      expect(ordered[0].description).toEqual('ISO 1-27  (BSC Conversion)');
      expect(ordered[1].description).toEqual('ISO 75 Collision ');
      expect(ordered[2].description).toEqual('ISO 75 Comprehensive ');
    });
  });

  describe('when using retrieveVehiclePlanSymbols method', () => {
    it('should pass the request for symbols to lookups service', inject([LookupsService], (lookupsService: StubLookupsService) => {
      spyOn(lookupsService, 'postVehiclePlanSymbols').and.callThrough();

      const vehicle = Helpers.deepClone(VEHICLE_DATA);
      const generalDetails = Helpers.deepClone(VEHICLE_GENERAL_DETAILS);

      service.retrieveVehiclePlanSymbols(vehicle, generalDetails, '2017-11-27', [PLAN_1, PLAN_2]);

      expectLastCallArgs(lookupsService.postVehiclePlanSymbols).toEqual([
        '2017', {
          price: null,
          policyEffectiveDt: '2017-11-27',
          ratingPlans: '1,2',
          vrgDetailComprehensive: '23',
          vrgDetailCollision: '35',
          symbol: '19',
          collisionSymbol: '38',
          comprehensiveSymbol: '25',
          safetyCustomSymbol: '',
          massBodyStyleGroup: 'All Other PP Types'
          // bodyStyleGroup: 'All Other PP Types'
        }
      ]);
    }));
  });
});
