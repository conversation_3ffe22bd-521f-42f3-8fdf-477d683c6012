import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { StubLeaveQuoteComponent } from 'testing/stubs/components/leave-quote.component';

import { HomeUmbrellaComponent } from './home-umbrella.component';

describe('UmbrellaComponent', () => {
  let component: HomeUmbrellaComponent;
  let fixture: ComponentFixture<HomeUmbrellaComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ HomeUmbrellaComponent, StubLeaveQuoteComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(HomeUmbrellaComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });
});
