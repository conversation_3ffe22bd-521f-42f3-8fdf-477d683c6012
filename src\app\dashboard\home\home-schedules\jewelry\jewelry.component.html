<section class="section section--compact u-spacing--2-5">
  <div class="row">
    <div class="col-xs-12">
      <h1 class="o-heading">Jewelry</h1>
    </div>
  </div>

  <div class="row u-spacing--1">
    <div class="col-xs-12">
      <div class="box box--silver">
        <div class="row o-columns">
          <div class="col-xs-12">
            <table class="form-table">
              <tr class="form-table__row">

                <tr class="form-table__row" [ngClass]="{'is-required-field': false}">
                  <td class="form-table__cell u-width-130px">
                    <label for="">Items Vaulted:</label>
                  </td>
                  <td class="form-table__cell u-width-150px">
                    <input #refJewelryVaulted type="number" id="jewelryVaulted" name="JewelryVaulted" [(ngModel)]="propJewelryVaulted.itemValueAmount" (change)="handleChange($event)">
                  </td>
                  <td class="form-table__cell u-width-70px"></td>
                </tr>

                <tr class="form-table__row" [ngClass]="{'is-required-field': false}">
                  <td class="form-table__cell u-width-130px">
                    <label for="">Items Not Vaulted:</label>
                  </td>
                  <td class="form-table__cell u-width-150px">
                    <input #refJewelryNotVaulted type="number" id="jewelryNotVaulted" name="JewelryNotVaulted" [(ngModel)]="propJewelryNotVaulted.itemValueAmount" (change)="handleChange($event)">
                  </td>
                  <td class="form-table__cell u-width-70px"></td>
                </tr>

                <tr class="form-table__row">
                  <td class="form-table__cell" [ngClass]="{'is-disabled-field': propJewelryNotVaulted.itemValueAmount < num1}">
                    Home Safe in Use:
                  </td>
                  <td class="form-table__cell" colspan="2" [ngClass]="{'is-disabled-field': propJewelryNotVaulted.itemValueAmount < num1}">
                    <label class="o-checkable u-spacing--right-2" [appDetectSystem] [removeRadio]="true">
                      <input [checked]="homeSafeInUse" type="radio" [disabled]='propJewelryNotVaulted.itemValueAmount < num1' name="safeinUse" id="isHomeSafeInUseYes" (change)="handleRadio($event)">
                      <i class="o-btn o-btn--radio"></i>
                      <span>Yes</span>
                    </label>
                    <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
                      <input [checked]="!homeSafeInUse" type="radio" [disabled]='propJewelryNotVaulted.itemValueAmount < num1' name="safeinUse" id="isHomeSafeInUseNo" (change)="handleRadio($event)">
                      <i class="o-btn o-btn--radio"></i>
                      <span>No</span>
                    </label>
                  </td>

                  <!--    <td class="form-table__cell" colspan="2" [ngClass]="{'is-disabled-field': propJewelryNotVaulted.itemValueAmount < 1}">
                                      <label class="o-checkable u-spacing--right-2">
                   <input [(ngModel)]="homeSafeInUse" [value]='homeSafeInUse' type="radio" [disabled]='propJewelryNotVaulted.itemValueAmount < 1' [checked]="homeSafeInUse && propJewelryNotVaulted.itemValueAmount > 1 ? true : false" name="safeinUse" id="isHomeSafeinUseYes" (change)="handleRadio($event)">
                   <i class="o-btn o-btn--radio"></i>
                   Yes
                                      </label>
                                      <label class="o-checkable">
                   <input [(ngModel)]="homeSafeInUse" [value]='!homeSafeInUse' type="radio" [disabled]='propJewelryNotVaulted.itemValueAmount < 1' [checked]="!homeSafeInUse && propJewelryNotVaulted.itemValueAmount > 1 ? true : false" name="safeinUse" id="isHomeSafeinUseNo" (change)="handleRadio($event)">
                   <i class="o-btn o-btn--radio"></i>
                   No
                                      </label>
                                    </td> -->
                </tr>
            </table>
          </div>

        </div>
      </div>
    </div>
  </div>

</section>
