import { StubLeaveQuoteComponent } from '../../../../testing/stubs/components/leave-quote.component';
import { RouterTestingModule } from '@angular/router/testing';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { DwellingOptionsComponent } from './dwelling-options.component';

describe('DwellingOptionsComponent', () => {
  let component: DwellingOptionsComponent;
  let fixture: ComponentFixture<DwellingOptionsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [ RouterTestingModule ],
      declarations: [
        DwellingOptionsComponent,
        StubLeaveQuoteComponent
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DwellingOptionsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });
});
