import { DriverRmvToSend } from 'app/app-model/driver';
import { DataCustomMatchers } from 'testing/helpers/data-custom-matchers';
import { expect } from './../../../testing/helpers/expect';
import { expectLastConnectionUrl, expectLastCallArgs, expectLastConnectionPayload } from 'testing/helpers/all';
import { setupMockBackend } from 'testing/setups/mock-backend';
import { MockBackend } from 'testing/setups/mock-backend';
import { TestBed, inject } from '@angular/core/testing';

import { LookupsService } from './lookups.service';

import { data as VEHICLE_MAKES } from 'testing/data/lookups/makes';
import { data as VEHICLE_MODELS } from 'testing/data/lookups/models';
import { data as VEHICLE_MODEL_DETAILS } from 'testing/data/lookups/model-details';
import { PLAN_SYMBOLS as PLAN_SYMBOLS } from 'testing/data/lookups/plan-symbols';
import { VEHICLE_GENERAL_DETAILS } from 'testing/data/lookups/vin';
import { data as QUOTE_NEW_RMV } from 'testing/data/lookups/newquote';
import { data as VEHICLE_PLATES_LOOKUP } from 'testing/data/rmv-vehicle-plates';

import { VehicleSymbolRequestData } from 'app/app-model/vehicle';

describe('Service: Lookups', () => {
  let service: LookupsService;
  let mockBackend: MockBackend;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        LookupsService
      ]
    });

    jasmine.addMatchers(DataCustomMatchers);
  });

  beforeEach(inject([LookupsService], (_service: LookupsService) => {
    service = _service;
  }));

  it('can instantiate service when inject service', () => {
    expect(service).toBeTruthy();
  });

  it('allows to retrieve makes for particular year', () => {
    mockBackend = setupMockBackend(VEHICLE_MAKES);

    service.getVehicleMakes('2002').subscribe();

    expectLastConnectionUrl(mockBackend).toEndWith('/lookups/vehicles/modelYears/2002/makes');
  });

  it('allows to retrieve makes for particular year and convert them to options', () => {
    mockBackend = setupMockBackend(VEHICLE_MAKES);
    const spy = jasmine.createSpy('makes');

    service.getVehicleMakesAsOptions('2002').subscribe(spy);

    expectLastConnectionUrl(mockBackend).toEndWith('/lookups/vehicles/modelYears/2002/makes');
    expectLastCallArgs(spy).toEqual([ jasmine.arrayContaining([
      { id: 'Toyota', text: 'Toyota' }
    ]) ]);
  });

  it('allows to retrieve models for particular year and make', () => {
    mockBackend = setupMockBackend(VEHICLE_MODELS);

    service.getVehicleModels('2002', 'Toyota').subscribe();

    expectLastConnectionUrl(mockBackend).toEndWith('/lookups/vehicles/modelYears/2002/makes/Toyota/models');
  });

  it('allows to retrieve models for particular year and make and convert them to options', () => {
    mockBackend = setupMockBackend(VEHICLE_MODELS);
    const spy = jasmine.createSpy('models');

    service.getVehicleModelsAsOptions('2002', 'Toyota').subscribe(spy);

    expectLastConnectionUrl(mockBackend).toEndWith('/lookups/vehicles/modelYears/2002/makes/Toyota/models');
    expectLastCallArgs(spy).toEqual([jasmine.arrayContaining([
      { id: 'AVALON XL/AVALON XLS', text: 'AVALON XL/AVALON XLS' }
    ])]);
  });

  it('allows to retrieve vehicle details for particular year, make and model', () => {
    mockBackend = setupMockBackend(VEHICLE_MODEL_DETAILS);

    service.getVehicleModelDetails('2002', 'Toyota', 'AVALON XL/AVALON XLS').subscribe();

    expectLastConnectionUrl(mockBackend).toEndWith(
      '/lookups/vehicles/modelYears/2002/makes/Toyota/models/AVALON%2520XL%252FAVALON%2520XLS'
    );
  });

  it('allows to retrieve vehicle details using vin', () => {
    mockBackend = setupMockBackend(VEHICLE_GENERAL_DETAILS);

    service.getVehicleDataByVin('vin-number').subscribe();

    expectLastConnectionUrl(mockBackend).toEndWith(
      '/lookups/vehicles/vins/VIN-NUMBER'
    );
  });

  it('allows to create new vehicle using plate', () => {
    mockBackend = setupMockBackend(VEHICLE_PLATES_LOOKUP);

    const data = {
      quoteId: 'quote-id',
      policyEffectiveDate: '2017-11-27',
      plateNumber: 'plate-number',
      plateType: 'plate-type'
    };

    service.postVehicleDataByPlate(data).subscribe();

    expectLastConnectionUrl(mockBackend).toEndWith(
      '/lookups/rmv/vehicles/plates'
    );
    expectLastConnectionPayload(mockBackend).toEqual(data);
  });

  it('allows to create new vehicle using vin', () => {
    mockBackend = setupMockBackend(VEHICLE_GENERAL_DETAILS);

    const data = {
      quoteId: 'quote-id',
      policyEffectiveDate: '2017-11-27',
      vin: 'vin-number'
    };

    service.postVehicleDataByVin(data).subscribe();

    expectLastConnectionUrl(mockBackend).toEndWith(
      '/lookups/rmv/vehicles/vins'
    );
    expectLastConnectionPayload(mockBackend).toEqual(data);
  });

  it('allows to create plan symbols for particular year', () => {
    mockBackend = setupMockBackend(PLAN_SYMBOLS);

    const data: VehicleSymbolRequestData = {
      'price': null,
      'policyEffectiveDt': '2017-11-27',
      'ratingPlans': '13,4,28,11',
      'vrgDetailComprehensive': '26',
      'vrgDetailCollision': '31',
      'symbol': '13',
      'collisionSymbol': '',
      'comprehensiveSymbol': '',
      'safetyCustomSymbol': '',
      'massBodyStyleGroup': 'All Other PP Types'
      // 'bodyStyleGroup': 'All Other PP Types'
    };

    service.postVehiclePlanSymbols('2002', data).subscribe();

    expectLastConnectionUrl(mockBackend).toEndWith(
      '/lookups/vehicles/modelYears/2002/planSymbols'
    );
    expectLastConnectionPayload(mockBackend).toEqual(data);
  });

  it('allows to create new quote from RMV lookup', () => {
    mockBackend = setupMockBackend(QUOTE_NEW_RMV);

    const data = {
      policyEffectiveDate: '2017-12-20',
      drivers: <DriverRmvToSend[]>[{
          'multiples are possible': ''
        },
        new DriverRmvToSend('John', 'Doe', '1983-02-13', 'license-number')
      ]
    };

    service.newRmvQuote(data).subscribe();

    expectLastConnectionUrl(mockBackend).toEndWith('/lookups/rmv/drivers/newquote');
    expectLastConnectionPayload(mockBackend).toEqual(data);
  });

  it('allows to store and retrieve latest inputted client info', () => {
    const spy = jasmine.createSpy('client');

    service.getNewRmvClient$.subscribe(spy);

    const info = {
      firstName: 'John',
      lastName: 'Doe',
      dob: '1983-02-13'
    };
    service.updateNewRmvClient(info);
    expectLastCallArgs(spy).toEqual([info]);
  });
});
