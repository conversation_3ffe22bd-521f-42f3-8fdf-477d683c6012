import { Input, ViewChild, OnDestroy } from '@angular/core';
import { Component, OnInit } from '@angular/core';
import { FeatureService } from 'app/shared/services/feature.service';
import { ModalboxComponent } from '../../../shared/components/modalbox/modalbox.component';
import { RmvService } from '../../app-services/rmv.service';
import { OverlayLoaderService } from '../../../shared/services/overlay-loader.service';
import { BroadcastService } from 'app/shared/services/broadcast.service';
import {
  Router,
  RouterEvent,
  NavigationStart,
  NavigationEnd,
  NavigationError,
  ActivatedRoute,
} from '@angular/router';
import { SpecsService } from '../../app-services/specs.service';
import { filter } from 'rxjs/operators';
import { AgencyUserService } from '../../../shared/services/agency-user.service';
import { addDays, format, isBefore, parseISO, startOfDay } from 'date-fns';

@Component({
    selector: 'app-rmv-services-dashboard',
    templateUrl: './rmv-services-dashboard.component.html',
    styleUrls: ['../../../../styles/skeleton.css', './rmv-services-dashboard.component.scss'],
    standalone: false
})
export class RmvServicesDashboardComponent implements OnInit, OnDestroy {
  @ViewChild('ListSavedRTA') savedlist: ModalboxComponent;
  documentDetailSubscription: any;
  documentSubscription: any;
  eligibilitySubscription: any;
  transactionSubscription;
  isDuplicateEnabled: boolean;
  isDownloadDocuments;

  constructor(
    private agencyUserService: AgencyUserService,
    private rmvService: RmvService,
    private overlayLoader: OverlayLoaderService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    private specService: SpecsService,
    private broadcastService: BroadcastService,
  ) {
    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe((event: NavigationEnd) => { this.navigationInterceptor(event); });
    this.broadcastService.broadcast('notifications.update');
  }


  @ViewChild('missingPhone') missingPhone: ModalboxComponent;
  list;
  documentsToDestroy = false;
  evrEligible;
  isReassign;
  ngOnInit(): void {
    this.checkEvrEligibility();
    this.checkListOfTransactions();
    this.isDownloadDocuments = JSON.parse(localStorage.getItem('features')).find(x => x.name === 'SprApp_DownloadDocuments');

  }
  ngOnDestroy(): void {
    this.documentDetailSubscription && this.documentDetailSubscription.unsubscribe();
    this.documentSubscription && this.documentSubscription.unsubscribe();
    this.eligibilitySubscription && this.eligibilitySubscription.unsubscribe();
    this.transactionSubscription && this.transactionSubscription.unsubscribe();

  }

  openModal() {
    this.savedlist.openModalbox();
  }

  navigationInterceptor(e: RouterEvent) {
    if (e instanceof NavigationStart) {
      if (e.url === '/dashboard/rmv-services') {
        this.overlayLoader.showLoader();
      }

    }
    if (e instanceof NavigationEnd) {
      this.overlayLoader.hideLoader();
    }
    if (e instanceof NavigationError) {
      this.overlayLoader.hideLoader();
    }
  }

  checkEvrEligibility() {
  this.eligibilitySubscription =  this.rmvService.checkEvrLiteEligibility().subscribe((x) => {
      this.evrEligible = x.isEvrLiteEligible;
      this.getEvrLitePermitStatus();

      if (this.evrEligible) {
        this.getDocumentDestructionDetails();
      }
    });
  }

  private getEvrLitePermitStatus(): void {
    this.agencyUserService.getEvrLitePermitStatus().subscribe(x => {
      if (x.isEvrUser && x.isExpired) {
        this.evrEligible = false;
      }
    });

  }

  checkListOfTransactions() {
    this.transactionSubscription = this.specService.getTransactionTypes().subscribe(x => {
      this.isReassign = x.items.find(t => t.type === 'PrefillReassign') ? true : false;
      this.isDuplicateEnabled = x.items.find(t => t.type === 'DuplicateRegistration') ? true : false;
    });
  }

  routeToInventory() {
    const {
      user: { userPhone },
    } = JSON.parse(localStorage.getItem('userData'));
    if (userPhone) {
      this.router.navigate(['dashboard/rmv-services/inventory-order']);
    } else {
      this.missingPhone.open();
    }
  }

async getDocumentDestructionDetails() {
  this.documentSubscription = this.rmvService.GetDocumentDestructionDetails().subscribe((x) => {
    const params: any = {};
    const {
      documentDestructionDetails: {
        eligibleToDestroyThroughDate,
        destroyedThroughDate,
        lastDestroyedDate,
      },
    } = x;

       params.endDate = eligibleToDestroyThroughDate
      ? format(parseISO(eligibleToDestroyThroughDate), 'yyyy-MM-dd')
      : format(new Date(), 'yyyy-MM-dd');
    params.startDate = destroyedThroughDate
      ? format(addDays(parseISO(destroyedThroughDate), 1), 'yyyy-MM-dd')
      : format(new Date(), 'yyyy-MM-dd');

    if (!destroyedThroughDate || isBefore(
      startOfDay(parseISO(destroyedThroughDate)),
      startOfDay(new Date())
    )) {
      this.getDocumentList(params);
    }
  });
}

  getDocumentList(params) {
    params.limit = 1;
    params.offset = 0;
  this.documentDetailSubscription =  this.rmvService.GetDocumentsToDestroy(params).subscribe(
      (x) => {
        this.documentsToDestroy = x.items.length > 0 ? true : false;
      },
      (err) => {
        this.overlayLoader.hideLoader();
      }
    );
  }
}
