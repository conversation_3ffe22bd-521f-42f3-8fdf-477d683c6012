<br>
<i style="font-style: italic; padding-left:10px">Use the following to perform an RMV lookup for Drivers or Vehicles.</i>
<form #reinstatmentForm="ngForm" (ngSubmit)="lookup(reinstatmentForm)">
<app-inquiry-type></app-inquiry-type>
<app-driver-inquiry *ngIf="inquiryType.type === 'driver' || inquiryType.type === 'driverAndVehicle'"></app-driver-inquiry>
<app-vehicle-inquiry *ngIf="inquiryType.type === 'vehicle'"></app-vehicle-inquiry>
<div class="row u-spacing--1-5">
    <div class="col-xs-12 u-remove-letter-spacing" style="padding-left: 40px;">
      <button class="o-btn" type="submit" [disabled]="reinstatmentForm.invalid">Lookup</button>     
    </div>
 
</div>
 <div class="section section--compact u-spacing--2 5 u-remove-letter-spacing" style="padding-top:20px;">
    <button class="o-btn" [routerLink]="'/dashboard/rmv-services'">RETURN TO RMV DASHBOARD</button>
</div>

</form>
