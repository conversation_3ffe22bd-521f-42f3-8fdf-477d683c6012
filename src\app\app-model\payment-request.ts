export interface Payer {
  firstName: string;
  lastName: string;
  dob?: string;
  businessName: string;
  email: string;
}

export interface Requester {
  email: string;
  ccOnEmail: boolean;
}

export interface LineItem {
  amount: number;
  description: string;
  type: string;
}

export interface Payment {
  total: number;
  rmvFee: number;
  bankTransferTotal?: number;
  useBankTransferOption?:boolean;
  useAgencyBill?: boolean;
  agencyBillTotal?: number;
  paymentMethod?: PaymentMethodType;
  lineItems: LineItem[];
}

export enum PaymentMethodType
{
    None,
    CreditCard,
    BankTransfer,
    AgencyBill
}

export interface PaymentDefinition {
  category?: string;
  type: string;
  payment?: Payment;
}

export interface Attribute {
  name: string;
  value: string;
}

export interface TransactionData {
  transaction: string;
  attributes: Attribute[];
  datastreams?: DataStream;
}

export interface DataStream {
  type: string;
  description: string;
  data: string;
}

export interface PaymentCreateRequest {
  payer: Payer;
  requester: Requester;
  paymentDefinition: PaymentDefinition;
  transactionData: TransactionData;
}

export interface PaymentCreateRequestEasy {
  payer: Payer;
  owner: RegistrationOwner;
  vehicle: any;
  requester: Requester;
  paymentDefinition: PaymentDefinition;
  transactionData: TransactionData;
}

export interface RegistrationOwner {
  firstName: string;
  lastName: string;
  dob: string;
  email: string;
  fid: string;
}
