import { StorageGlobalService } from './../../../../shared/services/storage-global.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { StubDriversServiceProvider } from 'testing/stubs/services/drivers.service.provider';
import { StubSubsServiceProvider } from 'testing/stubs/services/subs.service.provider';
import { StubOptionsServiceProvider } from 'testing/stubs/services/options.service.provider';
import { StubSpecsServiceProvider } from 'testing/stubs/services/specs.service.provider';
import { StubAgencyUserServiceProvider } from 'testing/stubs/services/agency-user.service.provider';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { DriversOptionsAutomanagerComponent } from './drivers-options-automanager.component';

describe('DriversOptionsAutomanagerComponent', () => {
  let component: DriversOptionsAutomanagerComponent;
  let fixture: ComponentFixture<DriversOptionsAutomanagerComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [DriversOptionsAutomanagerComponent],
      providers: [
        StubAgencyUserServiceProvider,
        StubSpecsServiceProvider,
        StubOptionsServiceProvider,
        StubSubsServiceProvider,
        StubDriversServiceProvider,
        StorageGlobalService,
        StorageService
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DriversOptionsAutomanagerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });
});
