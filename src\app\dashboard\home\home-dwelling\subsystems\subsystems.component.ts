
import { map, first, take, filter } from 'rxjs/operators';
import { SubscriptionLike as ISubscription } from 'rxjs';
import { Component, OnInit } from '@angular/core';

import { FilterOption } from 'app/app-model/filter-option';
import { Dwelling, DwellingsSubsystemsAPIResponse, DwellingSubsystems } from 'app/app-model/dwelling';

import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { SubsService } from 'app/dashboard/app-services/subs.service';
import { DwellingService } from 'app/dashboard/app-services/dwelling.service';
import { OptionsService } from 'app/dashboard/app-services/options.service';
import { QuotePlan, QuotePlanListAPIResponse } from 'app/app-model/quote';
import { Validate } from 'app/hints-and-warnings/validators';
import {  CoverageItemParsed, Coverage} from 'app/app-model/coverage';
import { DwellingOptionsValidationService } from 'app/shared/services/dwelling-options-validation.service';

import { getYear } from 'date-fns';

@Component({
    selector: 'app-subsystems',
    templateUrl: './subsystems.component.html',
    styleUrls: ['./subsystems.component.scss'],
    standalone: false
})
export class SubsystemsComponent implements OnInit {
  homeGeneralItems: CoverageItemParsed[];

  constructor(
    private storageService: StorageService,
    private subsService: SubsService,
    private specsService: SpecsService,
    private dwellingService: DwellingService,
    private optionsService: OptionsService,
    private dwellingOptionValidation: DwellingOptionsValidationService
  ) { }
  private subscriptionSelectedQuoteTypes: ISubscription;

  public heatingType: Object[] = [
   { id: 'None', text: 'None'},
   { id: 'CoalNonProfessionallyInstalled', text: 'Coal Non-Professionally Installed'},
   { id: 'CoalProfessionallyInstalled', text: 'Coal Professionally Installed'},
   { id: 'Electric', text: 'Electric'},
   { id: 'ElectricPortableHeater', text: 'Electric Portable Heater'},
   { id: 'Kerosene', text: 'Kerosene'},
   { id: 'NaturalGas', text: 'Natural Gas'},
   { id: 'Oil', text: 'Oil'},
   { id: 'OutdoorWoodBoiler', text: 'Outdoor Wood Boiler'},
   { id: 'PelletStove', text: 'Pellet Stove'},
   { id: 'Propane', text: 'Propane'},
   { id: 'PropaneMonitor', text: 'Propane Monitor'},
   { id: 'WoodProfessionallyInstalled', text: 'Wood Professionally Installed'},
   { id: 'Other', text: 'Other'}
  ];
  public selectedHeatingPrimaryType: Object;
  public heatingLastUpdatedYear: number;
  public selectedHeatingSecondaryType: Object;

  public plumbingMaterialType: Object[] = [
   { id: 'CastIron', text: 'Cast Iron'},
   { id: 'Copper', text: 'Copper'},
   { id: 'CopperAndGalvanized', text: 'Copper and Galvanized'},
   { id: 'GalvanizedAll', text: 'Galvanized – All'},
   { id: 'Pvc', text: 'PVC'},
   { id: 'PvcAndCopper', text: 'PVC and Copper'},
   { id: 'PvcAndGalvanized', text: 'PVC and Galvanized'},
   { id: 'PvcAndCopperAndGalvanized', text: 'PVC, Copper and Galvanized'},
   { id: 'MoreThanOneOfTheAbove', text: 'More than one of the above'},
   { id: 'Other', text: 'Other'},
  ];
  public selectedPlumbingMaterialType: Object;
  public plumbingLastUpdatedYear: number;

  public electricalWiringType: Object[] = [
   { id: 'Aluminum', text: 'Aluminum'},
   { id: 'BX', text: 'BX'},
   { id: 'Copper', text: 'Copper'},
   { id: 'KnobAndTube', text: 'Knob and Tube'},
   { id: 'Romex', text: 'Romex'},
   { id: 'MoreThanOne', text: 'More than one'},
   { id: 'Other', text: 'Other'}
  ];
  public selectedElectricalWiringType: Object;
  public electricalLastUpdatedYear: number;

  public roofMaterialType: Object[] = [
    { id: 'AluminumShingles', text: 'Aluminum Shingles'},
    { id: 'ArchitecturalShingles', text: 'Architectural Shingles'},
    { id: 'Asbestos', text: 'Asbestos'},
    { id: 'AsphaltShingle', text: 'Asphalt Shingle'},
    { id: 'ClayTileOrSlate', text: 'Clay Tile or Slate'},
    { id: 'ConcreteTile', text: 'Concrete Tile'},
    { id: 'Composition', text: 'Composition'},
    { id: 'Copper', text: 'Copper'},
    { id: 'CorrugatedSteel', text: 'Corrugated Steel'},
    { id: 'Foam', text: 'Foam'},
    { id: 'RolledRoofing', text: 'Rolled Roofing'},
    { id: 'RubberRoof', text: 'Rubber Roof'},
    { id: 'SinglePlyMembrane', text: 'Single Ply Membrane'},
    { id: 'SpanishTile', text: 'Spanish Tile'},
    { id: 'TarAndGravel', text: 'Tar and Gravel'},
    { id: 'TarAndGravelBuiltUp', text: 'Tar and Gravel (Built-up)'},
    { id: 'Tile', text: 'Tile'},
    { id: 'Tin', text: 'Tin'},
    { id: 'WoodFiberShingle', text: 'Wood Fiber Shingle'},
    { id: 'Other', text: 'Other'}
  ];
  public selectedRoofMaterialType: Object;
  public roofLastUpdatedYear: number;

  public oilStorageTankLocation: Object[] = [
    { id: 'None', text: 'None'},
    { id: 'IndoorCompletelyAboveGroundOnMasonFloor', text: 'Indoor completely above ground on mason floor'},
    { id: 'IndoorCompletelyAboveGroundNotOnMasonFloor', text: 'Indoor completely above ground not on mason floor'},
    { id: 'OutdoorsCompletelyAboveGround', text: 'Outdoors completely above ground'},
    { id: 'Underground', text: 'Underground'},
    { id: 'Other', text: 'Other'}
  ];
  public selectedOilStorageTankLocation: Object;
  public subsystemItems: DwellingSubsystems = new DwellingSubsystems();

  private subscriptionDwelling: ISubscription;
  private subscriptionSubsystems: ISubscription;
  private subscriptionQuotePlans: ISubscription;

  private dwelling: Dwelling = new Dwelling();
  private quoteId: string;
  private subsystems: DwellingsSubsystemsAPIResponse = new DwellingsSubsystemsAPIResponse();
  private selectedPlans: QuotePlan[] = [];
  private selectedPlansIds: string[] = [];
  private quoteFormTypes: string[] = [];

  private properties: string[] = [
    'electricalLastUpdatedYear',
    'heatingPrimaryLastUpdatedYear',
    'plumbingLastUpdatedYear',
    'roofLastUpdatedYear'
  ];

  public yearOfUpdateModalVisible: boolean;
  public futureYearOfUpdateModalVisible: boolean;

  private allowDwellingDataUpdate = true;
  public homeCoverageItems;

  ngOnInit() {
    this.subscribeQuoteSelectedPlans();
    this.getDwelling();
    this.getSubsystems();
    this.subscribeSelectedQuoteFormTypes();

    this.storageService.getStorageData('homeCarrierOptionsParsed').subscribe((items: CoverageItemParsed[]) => {
      this.homeCoverageItems = items;
    });
     this.storageService.getStorageData('homeGeneralOptionsParsed').subscribe((items: CoverageItemParsed[]) => {
      this.homeGeneralItems = items.filter(x => x.isActive === true);
    });
  }

  ngOnDestroy() {
    this.subscriptionDwelling && this.subscriptionDwelling.unsubscribe();
    this.subscriptionSubsystems && this.subscriptionSubsystems.unsubscribe();
    this.subscriptionQuotePlans && this.subscriptionQuotePlans.unsubscribe();
    this.subscriptionSelectedQuoteTypes && this.subscriptionSelectedQuoteTypes.unsubscribe();
  }

  private subscribeQuoteSelectedPlans(): void {
    this.subscriptionQuotePlans = this.storageService.getStorageData('selectedPlan')
      .subscribe((res: QuotePlanListAPIResponse) => {
        if (res && res.items && res.items.length) {
          this.selectedPlans = JSON.parse(JSON.stringify(res.items[0].items));
          this.selectedPlansIds = this.selectedPlans.map(plan => plan.ratingPlanId);
        }
      });
  }

  private subscribeSelectedQuoteFormTypes(): Promise<string[]> {
    return new Promise((resolve, reject) => {
      this.subscriptionSelectedQuoteTypes = this.storageService.getStorageData('selectedQuoteFormTypes')
        .subscribe((res: string[]) => {
          this.quoteFormTypes = res;
          resolve(this.quoteFormTypes);
        });
    });
  }

  private getDwelling() {
    this.subscriptionDwelling = this.storageService.getStorageData('dwelling').subscribe(data => {
      if (data) {
        this.dwelling = JSON.parse(JSON.stringify(data));
        this.quoteId = data.quoteSessionId;
      }
    });
  }

  private getSubsystems() {
    this.subscriptionSubsystems = this.storageService.getStorageData('dwellingSubsystems').subscribe(data => {
      // console.log('%c dwellingSubsystems', 'color:red', data);
      if (data && data.meta && data.meta.href) {
        if (this.dwelling && this.dwelling.subsystems && this.dwelling.subsystems.href && this.dwelling.subsystems.href === data.meta.href) {
          this.subsystems = JSON.parse(JSON.stringify(data));
          if (this.subsystems && this.subsystems.items && this.subsystems.items.length) {
            this.subsystemItems = this.subsystems.items[0];
          }
        } else {
          // console.log('getSubsystemsFromAPI 1:', data);
          this.getSubsystemsFromAPI();
        }
      } else {
        // console.log('getSubsystemsFromAPI 2:', data);
        this.getSubsystemsFromAPI();
      }
    });
  }

  private getSubsystemsFromAPI() {
    if (this.dwelling && this.dwelling.subsystems && this.dwelling.subsystems.href) {
      this.dwellingService.getDwellingSubsystem(this.dwelling.subsystems.href).pipe(take(1)).subscribe( data => {
        if (data && data.items && !data.items.length) {
          this.createSubsystemsItems();
        }
      });
    }
  }

  private createSubsystemsItems() {
    if (this.subsystems && this.subsystems.meta && this.subsystems.meta.href) {
      this.dwellingService.createDwellingSubsystem(this.subsystems.meta.href).pipe(take(1)).subscribe(res => console.log('CREATED SUBSYSTEMS', res));
    }
  }

  public selected($event, data, field, update = true) {
    if ($event && field) {
      data[field] = $event.id;
    }
    if (update) {
      this.saveDwellingSubsystemData(data);
    }
    if (field === 'heatingSecondaryTypeCode') {
      this.validateCarrierOptionForTravelersPlan($event.id);
    }
  }

  public validateCarrierOptionForTravelersPlan(data) {
    const policyOption = this.homeCoverageItems.find(item => item.coverageCode === 'BSC-HOME-25052');

    if (!policyOption) {
        return;
    }

    const secondaryHeat = ['CoalProfessionallyInstalled', 'CoalNonProfessionallyInstalled', 'PelletStove', 'WoodProfessionallyInstalled'].includes(data);

    // Create a copy of the policy option to ensure change detection
    const updatedPolicyOption = {...policyOption};

    updatedPolicyOption.isActive = false;
    updatedPolicyOption.isRequired = secondaryHeat;
    updatedPolicyOption.isDisabled = !secondaryHeat;
    updatedPolicyOption.currentValue = '';
    updatedPolicyOption.currentValueData.values = [];

    // Update the original policy option
    Object.assign(policyOption, updatedPolicyOption);

    if (policyOption.isDisabled === true) {
        const coverageItem = this.homeCoverageItems.find(item => item.coverageCode === 'BSC-HOME-25052');

        if (coverageItem) {
            for (const value of coverageItem.values) {
                value.currentValue = null;
            }

            const arrayOfCoveragesToUpdate: CoverageItemParsed[] = [coverageItem];
            // update api data
            this.updateApiCoverageData(arrayOfCoveragesToUpdate);
        }
    }

    // Update storage
    this.storageService.setStorageData('homeCarrierOptionsParsed', this.homeCoverageItems);

    // Trigger validation immediately after storage update
    this.dwellingOptionValidation.triggerChange(this.homeCoverageItems);
}

  public validateCarrierOptionForSafecoPlan(data, update = true) {
    if (update) {
      this.saveDwellingSubsystemData(data);
    }

    const policyOption = this.homeCoverageItems.find(item => item.coverageCode === 'BSC-HOME-023224');

    if (!policyOption) {
      return;
    }

    const isRoofLastUpdatedYearValid = ![null, 0, undefined].includes(data);

    policyOption.isActive = false;
    policyOption.isRequired = isRoofLastUpdatedYearValid;
    policyOption.isDisabled = !isRoofLastUpdatedYearValid;

    policyOption.currentValue = '';
    policyOption.currentValueData.values = [];

    if (policyOption.isDisabled === true) {
      const coverageItem = this.homeCoverageItems.find(item => item.coverageCode === 'BSC-HOME-023224');

      if (coverageItem) {
        for (const value of coverageItem.values) {
          value.currentValue = null;
        }

        const arrayOfCoveragesToUpdate: CoverageItemParsed[] = [coverageItem];
        // update api data
        this.updateApiCoverageData(arrayOfCoveragesToUpdate);
        // update storage
        this.storageService.setStorageData('homeCarrierOptionsParsed', this.homeCoverageItems);

      }
    }

    this.dwellingOptionValidation.triggerChange(this.homeCoverageItems);
  }

  private updateApiCoverageData(homeCoverages) {
    // Debug setPolicyItemStatusBasedOnPolicyCoverages
     const homeCoverageToUpdate  = this.homeCoverageItems.filter(item => item.isActive);
    homeCoverages = [...homeCoverageToUpdate, ...this.homeGeneralItems];
    const readyAllPoliciesToUpdate: Coverage[] = this.optionsService.convertPolicyItemParsedArrayToPolicyArray(
      homeCoverages
    );

    const newCoverageData = {
      coverages: readyAllPoliciesToUpdate
    };

    this.optionsService
      .updatePoliciesByUri(
        homeCoverages[0].endpointUrl,
        newCoverageData
      ).pipe(
        take(1))
        .subscribe(
          resp => console.log('Updated Coverages'),
          err => console.log('Update Error: ', err)
        );
  }


  public validateAndSaveSubsystems(ev) {
    this.validateSubsystems().pipe(first()).subscribe(isValid => {
      if (!isValid) {
        this.correctData();
      }

      this.saveDwellingSubsystemData(this.subsystemItems);
    });
  }

  public getConstructionYear() {
    let constructionYear;
    this.storageService.getStorageData('dwelling').pipe(first()).subscribe(info => {
      constructionYear = info.constructionYear;
    });
    return constructionYear;
  }

  private validateSubsystems() {
    return this.storageService.getStorageData('dwelling').pipe(first(), map(info => {
      let isEverythingValid = true;
      const constructionYear = info.constructionYear;
      const data = this.subsystemItems;
      this.properties.forEach(prop => {
        if (!this.isPropertyValid(data[prop], constructionYear)) {
          isEverythingValid = false;
        }
      });
      return isEverythingValid;
    }), );
  }

  private isPropertyValid(property, constructionYear) {
    const currentYear = getYear(new Date());
    if (property) {
      if (property > currentYear) {
        this.futureYearOfUpdateModalVisible = true;
        return false;
      }
      if (constructionYear && (property < constructionYear)) {
        this.yearOfUpdateModalVisible = true;
        return false;
      }
    }

    return true;
  }

  public correctData() {
    const data = this.subsystemItems;
    const constructionYear = this.getConstructionYear();
    this.properties.forEach(item => {
      if (!this.isPropertyValid(data[item], constructionYear)) {
        this.subsystemItems[item] = constructionYear;
      }
    });
  }
  public saveDwellingSubsystemData(data) {
    if (data) {
      if (data.electricalLastUpdatedYear) {
        data.electricalLastUpdatedYear = parseInt(data.electricalLastUpdatedYear);
      }
      if (data.heatingPrimaryLastUpdatedYear) {
        data.heatingPrimaryLastUpdatedYear = parseInt(data.heatingPrimaryLastUpdatedYear);
      }
      if (data.plumbingLastUpdatedYear) {
        data.plumbingLastUpdatedYear = parseInt(data.plumbingLastUpdatedYear);
      }
      if (data.roofLastUpdatedYear) {
        data.roofLastUpdatedYear = parseInt(data.roofLastUpdatedYear);
      }
    }
    if (this.allowDwellingDataUpdate) {
      this.allowDwellingDataUpdate = false;
      setTimeout( () => {
        this.dwellingService.updateDwellingSubsystem(data).subscribe( res => {
            this.allowDwellingDataUpdate = true;
          },
          err => this.allowDwellingDataUpdate = true,
          () => this.allowDwellingDataUpdate = true,
        );
      }, 300);
    }
  }

  // Validation
  // ----------------------------------------------------------------------------
  public fieldRoofTypeIsRequired(): boolean {
    const valueToCheck = (this.subsystemItems.roofMaterialTypeCode === 'Unassigned')
      ? null
      : this.subsystemItems.roofMaterialTypeCode;

    // https://bostonsoftware.atlassian.net/browse/SPR-2540
    // https://bostonsoftware.atlassian.net/browse/SPR-3015
    // Hanover New (Plan ID 282), Travelers New (Plan ID 283)
    const requiredForPlansDependingFormType = ['282', '283', '303', '292', '306', '119', '316', '332', '304'];
    let isRequiredForPlansDependingFormType = false;
    if (this.anyElementIsInArray(['HO3', 'HO5'], this.quoteFormTypes)) {
      isRequiredForPlansDependingFormType = Validate.isRequiredForSelectedPlansIfEmptyValue(
        valueToCheck,
        requiredForPlansDependingFormType,
        this.selectedPlansIds
      );
    }

    // ASI (105)
    const requiredForPlansDependingFormType2 = ['105'];
    let isRequiredForPlansDependingFormType2 = false;
    if (this.anyElementIsInArray(['HO3', 'HO5', 'HO4'], this.quoteFormTypes)) {
      isRequiredForPlansDependingFormType2 = Validate.isRequiredForSelectedPlansIfEmptyValue(
        valueToCheck,
        requiredForPlansDependingFormType2,
        this.selectedPlansIds
      );
    }

    const requiredForPlansDependingFormType3 = ['314'];
    let isRequiredForPlansDependingFormType3 = false;
    if (this.anyElementIsInArray(['HO3', 'HO5', 'HO6'], this.quoteFormTypes)) {
      isRequiredForPlansDependingFormType3 = Validate.isRequiredForSelectedPlansIfEmptyValue(
        valueToCheck,
        requiredForPlansDependingFormType3,
        this.selectedPlansIds
      );
    }


    // Required for: MAS/NGM Insurance, National General, Providence Mutual
    // Travelers
    const requiredForPlans = ['204', '97', '95'];
    let isRequiredForPlans = false;
    isRequiredForPlans = Validate.isRequiredForSelectedPlansIfEmptyValue(
      valueToCheck,
      requiredForPlans,
      this.selectedPlansIds
    );

    return isRequiredForPlansDependingFormType || isRequiredForPlansDependingFormType2 || isRequiredForPlans
     || isRequiredForPlansDependingFormType3;
  }

  public fieldPlumbingIsRequired(): boolean {
    const valueToCheck = (this.subsystemItems.plumbingMaterialTypeCode === 'Unassigned')
      ? null
      : this.subsystemItems.plumbingMaterialTypeCode;

    // Required for:  MSA/NGM Insurance, National General
    const requiredForPlans = ['204', '306', '93'];

    const requiredForPlansDependingFormType = ['119'];
    let isRequiredForPlansDependingFormType = false;
    if (this.anyElementIsInArray(['HO3', 'HO5'], this.quoteFormTypes)) {
      isRequiredForPlansDependingFormType = Validate.isRequiredForSelectedPlansIfEmptyValue(
        valueToCheck,
        requiredForPlansDependingFormType,
        this.selectedPlansIds
      );
    }

    return Validate.isRequiredForSelectedPlansIfEmptyValue(
      valueToCheck,
      requiredForPlans,
      this.selectedPlansIds
    ) || isRequiredForPlansDependingFormType;
  }

  public fieldPrimaryHeatIsRequired(): boolean {
    const valueToCheck = (this.subsystemItems.heatingPrimaryTypeCode === 'Unassigned')
      ? null
      : this.subsystemItems.heatingPrimaryTypeCode;

    // Required for: MGM/NGM Insurance, National General, Providence Mutual, Travelers,
    // Union Mutual of Vermont, Hanover New (Plan ID 282), Travelers New (Plan ID 283)
    const requiredForPlans = ['305', '204', '119', '97', '95', '282', '283', '292', '303', '316', '332', '304'];
    const requiredForPlansDependingFormType = ['93'];
    let isRequiredForPlansDependingFormType = false;
    if (this.anyElementIsInArray(['HO3', 'HO5'], this.quoteFormTypes)) {
      isRequiredForPlansDependingFormType = Validate.isRequiredForSelectedPlansIfEmptyValue(
        valueToCheck,
        requiredForPlansDependingFormType,
        this.selectedPlansIds
      );
    }


    return Validate.isRequiredForSelectedPlansIfEmptyValue(
      valueToCheck,
      requiredForPlans,
      this.selectedPlansIds
    ) || isRequiredForPlansDependingFormType;
  }

  public fieldSecondaryHeatIsRequired(): boolean {
    const valueToCheck = (this.subsystemItems.heatingSecondaryTypeCode === 'Unassigned')
      ? null
      : this.subsystemItems.heatingSecondaryTypeCode;

    // Required for: National General, Providence Mutual, Union Mutual of Vermont
    const requiredForPlans = ['119', '97', '304'];
    const requiredForPlansDependingFormType = ['93'];
    let isRequiredForPlansDependingFormType = false;
    if (this.anyElementIsInArray(['HO3', 'HO5'], this.quoteFormTypes)) {
      isRequiredForPlansDependingFormType = Validate.isRequiredForSelectedPlansIfEmptyValue(
        valueToCheck,
        requiredForPlansDependingFormType,
        this.selectedPlansIds
      );
    }
    return Validate.isRequiredForSelectedPlansIfEmptyValue(
      valueToCheck,
      requiredForPlans,
      this.selectedPlansIds
    ) || isRequiredForPlansDependingFormType;
  }

  public fieldWiringTypeIsRequired(): boolean {
    const valueToCheck = (this.subsystemItems.electricalWiringTypeCode === 'Unassigned')
      ? null
      : this.subsystemItems.electricalWiringTypeCode;

    // Required for:
    const requiredForPlans = ['292'];
   return this.anyElementIsInArray(['HO3', 'HO5'], this.quoteFormTypes) &&
     Validate.isRequiredForSelectedPlansIfEmptyValue(
      valueToCheck,
      requiredForPlans,
      this.selectedPlansIds
    );
  }

  public fieldOilTankLocationIsRequired(): boolean {
    const valueToCheck = (this.subsystemItems.oilStorageTankLocation === 'Unassigned')
      ? null
      : this.subsystemItems.oilStorageTankLocation;

    let validateOilTankRequirements = false;

    if (this.subsystemItems.heatingPrimaryTypeCode === 'Oil' || this.subsystemItems.heatingSecondaryTypeCode === 'Oil') {
      validateOilTankRequirements = true;
    }

    // Required for:
    // Bunker Hill (PlanId 113), National General (PlanId 119), Travelers (PlanId 95)
    // Travelers New (Plan ID 283), NLC RTR
    const requiredForPlans = ['113', '119', '95', '283', '317'];

    return validateOilTankRequirements && Validate.isRequiredForSelectedPlansIfEmptyValue(
      valueToCheck,
      requiredForPlans,
      this.selectedPlansIds
    );
  }

  // HELPERS
  // ----------------------------------------------------------------------------
  // Check if any element of an array (anyElementArr) is in another array (arrayToCheckIfIsIn)
  private anyElementIsInArray(anyElementArr: string[], arrayToCheckIfIsIn: string[]): boolean {
    return anyElementArr.some(el => arrayToCheckIfIsIn.indexOf(el) !== -1);
  }
}
