<section class="section section--compact u-spacing--2-5">
  <div class="row">
    <div class="col-xs-12">
      <h1 class="o-heading">Coverage Limits</h1>
    </div>
  </div>

  <div class="row u-spacing--1">
    <div class="col-xs-12">
      <div class="box box--silver">
        <div class="row o-columns">
          <div class="col-xs-6">
            <table class="form-table">
              <tr class="form-table__row">
                <td class="form-table__cell u-width-140px">
                  <label for="">Dwelling:</label>
                </td>
                <td class="form-table__cell u-width-160px" [ngClass]="{'is-required-field': !coverageQuoteDwelling && quote.formType !== 'DP2'}">
                  <input #refDwelling
                    type="text"
                    id="field_DWELL"
                    name="field_DWELL"
                    required
                    mask="separator.0" thousandSeparator="," decimalMarker="." [allowNegativeNumbers]="false" prefix="$"
                    [(ngModel)]="coverageQuoteDwelling"
                    (focus)="helperOnInputFocusTextMaskChangeEventIssueWorkaround($event)"
                    (blur)="helperOnInputBlurTextMaskChangeEventIssueWorkaroundUpdateQuoteCoverage($event, 'DWELL', coverageQuoteDwelling)">
                    <!-- textMask issue, change event doesn't work properly:: (change)="updateQuoteCoverage('DWELL', coverageQuoteDwelling)" -->
                </td>
                <td class="form-table__cell u-width-220px" [ngClass]="{'disabled-general': dp1FormType.indexOf(quote.formType) === -1}">
                  <sm-autocomplete
                    [options]="coverageExtendedOptions"
                    [activeOption]="optionQuoteBSCEC"
                    [disabled]="dp1FormType.indexOf(quote.formType) === -1"
                    [allowEmptyValue]="true"
                    (onSelect)="updateQuoteOption('BSC_EC', $event.id, true)">
                  </sm-autocomplete>
                </td>
              </tr>

              <tr class="form-table__row">
                <td class="form-table__cell u-width-140px">
                  <label for="">Other Structures:</label>
                </td>
                <td class="form-table__cell u-width-160px">
                  <input #refOtherStructures
                    type="text"
                    id="field_OS"
                    name="field_OS"
                    mask="separator.0" thousandSeparator="," decimalMarker="." [allowNegativeNumbers]="false" prefix="$"
                    [(ngModel)]="coverageQuoteOtherStructures"
                    (focus)="helperOnInputFocusTextMaskChangeEventIssueWorkaround($event)"
                    (blur)="helperOnInputBlurTextMaskChangeEventIssueWorkaroundUpdateQuoteCoverage($event, 'OS', coverageQuoteOtherStructures)">
                    <!-- textMask issue, change event doesn't work properly:: (change)="updateQuoteCoverage('OS', coverageQuoteOtherStructures)" -->
                </td>
                <td class="form-table__cell u-width-220px" [ngClass]="{'disabled-general': dp1FormType.indexOf(quote.formType) === -1}">
                  <sm-autocomplete
                    [options]="coverageExtendedOptions"
                    [activeOption]="optionQuoteBSCOSEC"
                    [disabled]="dp1FormType.indexOf(quote.formType) === -1"
                    [allowEmptyValue]="true"
                    (onSelect)="updateQuoteOption('BSC_OSEC', $event.id, true)">
                  </sm-autocomplete>
                </td>
              </tr>

              <tr class="form-table__row">
                <td class="form-table__cell u-width-140px">
                  <label for="">Personal Property:</label>
                </td>
                <td class="form-table__cell u-width-160px">
                  <input #refPersonalProperty
                    type="text"
                    id="field_PP"
                    name="field_PP"
                    mask="separator.0" thousandSeparator="," decimalMarker="." [allowNegativeNumbers]="false" prefix="$"
                    [(ngModel)]="coverageQuotePersonalProperty"
                    (focus)="helperOnInputFocusTextMaskChangeEventIssueWorkaround($event)"
                    (blur)="helperOnInputBlurTextMaskChangeEventIssueWorkaroundUpdateQuoteCoverage($event, 'PP', coverageQuotePersonalProperty)">
                    <!-- textMask issue, change event doesn't work properly:: (change)="updateQuoteCoverage('PP', coverageQuotePersonalProperty)" -->
                </td>
                <td class="form-table__cell u-width-220px" [ngClass]="{'disabled-general': dp1FormType.indexOf(quote.formType) === -1}">
                  <sm-autocomplete
                    [options]="coverageExtendedOptions"
                    [activeOption]="optionQuoteBSCPPEC"
                    [disabled]="dp1FormType.indexOf(quote.formType) === -1"
                    [allowEmptyValue]="true"
                    (onSelect)="updateQuoteOption('BSC_PPEC', $event.id, true)">
                  </sm-autocomplete>
                </td>
              </tr>

              <tr class="form-table__row">
                <td class="form-table__cell u-width-140px">
                  <label for="">Fair Rental Value:</label>
                </td>
                <td class="form-table__cell u-width-160px">
                  <input #refFairRentalValue
                    type="text"
                    id="field_FRV"
                    name="field_FRV"
                    mask="separator.0" thousandSeparator="," decimalMarker="." [allowNegativeNumbers]="false" prefix="$"
                    [(ngModel)]="coverageQuoteFairRentalValue"
                    (focus)="helperOnInputFocusTextMaskChangeEventIssueWorkaround($event)"
                    (blur)="helperOnInputBlurTextMaskChangeEventIssueWorkaroundUpdateQuoteCoverage($event, 'FRV', coverageQuoteFairRentalValue)">
                    <!-- textMask issue, change event doesn't work properly:: (change)="updateQuoteCoverage('FRV', coverageQuoteFairRentalValue)" -->
                </td>
                <td class="form-table__cell u-width-220px" [ngClass]="{'disabled-general': dp1FormType.indexOf(quote.formType) === -1}">
                  <sm-autocomplete
                    [options]="coverageExtendedOptions"
                    [activeOption]="optionQuoteBSCINFRV"
                    [disabled]="dp1FormType.indexOf(quote.formType) === -1"
                    [allowEmptyValue]="true"
                    (onSelect)="updateQuoteOption('BSC_INFRV', $event.id, true)">
                  </sm-autocomplete>
                </td>
              </tr>

              <tr class="form-table__row">
                <td class="form-table__cell u-width-140px">
                  <label for="">Loss of Use:</label>
                </td>
                <td class="form-table__cell u-width-160px">
                  <input #refLossOfUse
                    type="text"
                    id="field_LOU"
                    name="field_LOU"
                    mask="separator.0" thousandSeparator="," decimalMarker="." [allowNegativeNumbers]="false" prefix="$"
                    [(ngModel)]="coverageQuoteLossOfUse"
                    (focus)="helperOnInputFocusTextMaskChangeEventIssueWorkaround($event); lossOfUseValidationOnFocus()"
                    (blur)="helperOnInputBlurTextMaskChangeEventIssueWorkaroundUpdateQuoteCoverage($event, 'LOU', coverageQuoteLossOfUse)">

                    <!-- (blur)="lossOfUseValidationOnBlur($event, 'LOU', coverageQuoteLossOfUse)"> -->
                    <!-- (blur)="helperOnInputBlurTextMaskChangeEventIssueWorkaroundUpdateQuoteCoverage($event, 'LOU', coverageQuoteLossOfUse)"> -->
                    <!-- textMask issue, change event doesn't work properly:: (change)="updateQuoteCoverage('LOU', coverageQuoteLossOfUse)" -->

                    <app-modalbox #refModalLossOfUse [css]="'u-width-420px'" (onStateChange)="actionModalLossOfUseStateChange($event, refModalLossOfUseBtn, refLossOfUse)">
                      <div class="box box--silver u-spacing--1-5">
                        <p>{{lossOfUseModalMessage}}</p>
                      </div>
                      <div class="row u-spacing--2">
                        <div class="col-xs-12 u-align-right">
                          <button #refModalLossOfUseBtn class="o-btn" (click)="actionModalLossOfUseOk($event)">Ok</button>
                        </div>
                      </div>
                    </app-modalbox>
                </td>
                <td class="form-table__cell u-width-220px" [ngClass]="{'disabled-general': dp1FormType.indexOf(quote.formType) === -1}">
                  <sm-autocomplete
                    [options]="coverageExtendedOptions"
                    [activeOption]="optionQuoteBSCLOUEC"
                    [disabled]="dp1FormType.indexOf(quote.formType) === -1"
                    [allowEmptyValue]="true"
                    (onSelect)="updateQuoteOption('BSC_LOUEC', $event.id, true)">
                  </sm-autocomplete>
                </td>
              </tr>
            </table>
          </div>
          <div class="col-xs-6">
            <table class="form-table">

              <tr class="form-table__row">
                <td class="form-table__cell u-width-140px">
                  <label for="">Personal Liability:</label>
                </td>
                <td class="form-table__cell u-width-160px">
                  <sm-autocomplete
                    #refPersonalLiability
                    [options]="coveragePersonalLiabilityOptions"
                    [activeOption]="coverageQuotePersonalLiability"
                    [name]="'field_PL'"
                    [id]="'field_PL'"
                    [disabled]="false"
                    [allowEmptyValue]="false"
                    [required]="true"
                    [allowSearchById]="true"
                    (onSelect)="updateQuoteCoverage('PL', $event.id)">
                  </sm-autocomplete>
                </td>
                <td class="form-table__cell u-width-70px"></td>
              </tr>

              <tr class="form-table__row" [ngClass]="{'is-disabled-field-regular': lockMEDPM}">
                <td class="form-table__cell u-width-140px">
                  <label for="">Medical Payments:</label>
                </td>
                <td class="form-table__cell u-width-160px">
                  <sm-autocomplete
                    #refMedicalPayments
                    [options]="coverageMedicalPaymentsOptions"
                    [activeOption]="coverageQuoteMedicalPayments"
                    [name]="'field_MEDPM'"
                    [id]="'field_MEDPM'"
                    [disabled]="lockMEDPM"
                    [searchFromBegining]="false"
                    [allowEmptyValue]="false"
                    [allowCustomText]="false"
                    [allowSearchById]="true"
                    (onSelect)="updateQuoteCoverage('MEDPM', $event.id)">
                  </sm-autocomplete>
                </td>
                <td class="form-table__cell u-width-70px"></td>
              </tr>

              <tr class="form-table__row">
                <td class="form-table__cell u-width-180px u-spacing--bottom-1 u-color-slate-grey">DEDUCTIBLES</td>
                <td class="form-table__cell u-width-140px"></td>
              </tr>

               <tr class="form-table__row">
                 <td class="form-table__cell u-width-140px">
                   <label for="">All Perils:</label>
                 </td>
                 <td class="form-table__cell u-width-160px">
                 <sm-autocomplete
                   [options]="coverageAllPerilsOptions"
                   [activeOption]="coverageQuoteAllPerils"
                   (onSelect)="updateQuoteCoverage('APDED', $event.id)">
                 </sm-autocomplete>
                 </td>
                 <td class="form-table__cell u-width-70px"></td>
               </tr>

               <tr class="form-table__row" [class.is-required-field]="windHailFieldIsInvalid()">
                <td class="form-table__cell u-width-140px">
                  <label for="">Wind / Hail:</label>
                </td>
                <td class="form-table__cell u-width-180px">
                 <sm-autocomplete
                   #refWindHail
                   [options]="coverageWindHailOptions"
                   [activeOption]="coverageQuoteWindHail"
                   [name]="'field_WHDED'"
                   [id]="'field_WHDED'"
                   [disabled]="false"
                   [searchFromBegining]="false"
                   [allowEmptyValue]="true"
                   (onSelect)="updateQuoteCoverage('WHDED', $event.id)">
                 </sm-autocomplete>
                </td>
                <td class="form-table__cell u-width-20px"></td>
              </tr>

            </table>

          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<!-- <section class="section section--compact u-spacing--2-5">
  <div class="row">
    <div class="col-xs-12">
      <h1 class="o-heading">Insured Discounts</h1>
    </div>
  </div>

  <div class="row u-spacing--1">
    <div class="col-xs-12">
      <div class="box box--silver">
        <div class="row o-columns">
          <div class="col-xs-6">
            <table class="form-table">

              <tr class="form-table__row">
                <td class="form-table__cell u-width-140px">
                  <label for="">Inflation %:</label>
                </td>
                <td class="form-table__cell u-width-160px">
                  <sm-autocomplete
                    #refWindHail
                    [options]="coverageInflationOptions"
                    [activeOption]="optionInflation"
                    [name]="'inflation'"
                    [id]="'inflation'"
                    [disabled]="false"
                    [searchFromBegining]="false"
                    [allowEmptyValue]="false"
                    [allowCustomText]="false"
                    [required]="false"
                    (onSelect)="updateQuoteOption('BSC-DFIRE-000243', $event.id)">
                  </sm-autocomplete>
                </td>
              </tr>

            </table>
          </div>
          <div class="col-xs-6">
            <table class="form-table">

              <tr class="form-table__row">
                <td class="form-table__cell u-width-140px">
                  Loss Free Years:
                </td>
                <td class="form-table__cell" colspan="2">
                  <sm-autocomplete
                    #refWindHail
                    [options]="coverageLossFreeYearsOptions"
                    [activeOption]="optionLossFreeYears"
                    [name]="'lossFreeYears'"
                    [id]="'lossFreeYears'"
                    [disabled]="false"
                    [searchFromBegining]="false"
                    [allowEmptyValue]="false"
                    [allowCustomText]="false"
                    [required]="false"
                    (onSelect)="updateQuoteCoverage('BSC_LOSSFREEYRS', $event.id)">
                  </sm-autocomplete>
                </td>
              </tr>

            </table>

          </div>
        </div>
      </div>
    </div>
  </div>
</section>-->
