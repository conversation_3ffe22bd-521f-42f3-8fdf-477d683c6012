


<div class="container padding_4-4 container-padding">
    <div class="row">
        <div class="twelve columns rmv-svcs border">
            <img src="assets/images/common/rmv_services-wheel-squared-gray.png" width="100%" class="img-max img-style" />
            <h2>SinglePoint RMV Services</h2>
        </div>
    </div>
</div>

<div class="fullwidth padding">

    <div class="container">

        <div class="row">
​
          <div class="four columns rmv-svcs">

              <div class="rmv-svcs_action-box" [routerLink]="'rta-prefill'" [queryParams]="{'type':'PrefillNewTitleAndRegistration'}">

                <!--<div style="float: right; height: 100%; padding: 8px 0;">
                  <img src="https://bostonsoftware.com/2017/wp-content/uploads/blue-arrowhead.png" style="max-width: 20px;">
                  </div>-->
                  <p>Register and Title a new vehicle</p>
                  <span *ngIf="evrEligible" class="green">EVR Enabled</span>
              </div>

              <div class="rmv-svcs_action-box" [routerLink]="'rta-prefill'" [queryParams]="{'type':'PrefillRegistrationTransfer'}">
                <p>Transfer plate to a new vehicle</p>
                <span *ngIf="evrEligible" class="green">EVR Enabled</span>

              </div>
              <div class="rmv-svcs_action-box" [routerLink]="'rta-prefill'" [queryParams]="{'type':'PrefillReassign'}" *ngIf="isReassign">
                <p>Reassign plate</p>
                <span *ngIf="evrEligible" class="green">EVR Enabled</span>

              </div>
              <div *ngIf="evrEligible" class="rmv-svcs_action-box" [routerLink]="'rta-prefill'" [queryParams]="{'type':'DuplicateRegistration'}">
                <p>Duplicate Registration</p>
                <span *ngIf="evrEligible" class="green">EVR Enabled</span>
              </div>
              <div class="rmv-svcs_action-box" [routerLink]="'rta-prefill'">
                <p>Create RTA for all other transactions</p>
              </div>
​
          </div>
​
          <div class="four columns rmv-svcs">
              <div class="rmv-svcs_action-box greenarrows" [routerLink]="'rta-prefill'" [queryParams]="{'type':'PrefillRenewRegistration'}">
                  <!--<img class="bolt" src="https://bostonsoftware.com/2017/wp-content/uploads/bolt_greenbox.png">-->
                  <p>Renew vehicle Registration</p>
              </div>

              <div class="rmv-svcs_action-box greenarrows" [routerLink]="'rta-prefill'" [queryParams]="{'type':'PrefillReinstateRegistration'}">
                  <!--<img class="bolt" src="https://bostonsoftware.com/2017/wp-content/uploads/bolt_greenbox.png">-->
                  <p>Reinstate vehicle Registration</p>
              </div>
@if(isDownloadDocuments) {
              <div class="rmv-svcs_action-box" [routerLink]="'download-documents'">
                <p>Download Documents for Completed Transactions</p>
              </div>
            }

              <div class="margin">
                  <img src="assets/images/common/green-arrowheads.png" class="img-display">
                  <p class="font-size">= &nbsp;Immediate processing</p>
              </div>
          </div>
​
          <div class="four columns rmv-svcs">
              <div class="rmv-svcs_action-box" [routerLink]="'inquiry'">
                <p>Run inquiry for driver or vehicle</p>
              </div>
              <div class="rmv-svcs_action-box" [routerLink]="'saved-rmv-services'">
                <p>Open Saved RMV Transaction

              </div>
              <div *ngIf="evrEligible" class="rmv-svcs_action-box" [routerLink]="'saved-evr-lite'">
                <p> Open Saved EVR Transaction</p>
                <span *ngIf="evrEligible" class="green">EVR Enabled</span>

              </div>
              <div *ngIf="evrEligible" class="rmv-svcs_action-box position" [routerLink]="'evr-document-destruction'">
                <i *ngIf="documentsToDestroy" class="fas fa-exclamation-circle fa-2x doc-icon"></i>
                <p> EVR Document Destruction</p>
                <span *ngIf="evrEligible" class="green">EVR Enabled</span>


              </div>
              <div *ngIf="evrEligible" class="rmv-svcs_action-box">
                <p (click)="routeToInventory()"> Inventory Management</p>
                <span *ngIf="evrEligible" class="green">EVR Enabled</span>

              </div>
          </div>
    </div>
​

</div><!--end container-->

</div><!--end fullwidth-->
<app-modalbox #ListSavedRTA [css]="'u-width-780px horizontal-centered'" (click)="$event.stopPropagation()">
    <app-saved-rmv-services-table></app-saved-rmv-services-table>
  </app-modalbox>
  <app-modalbox #missingPhone>
    <div class="box box--silver">
      <p>Ordering Inventory requires a phone number to be on file with your SinglePoint account. Go to SinglePoint Settings and save a phone number under your User Profile.  Then logout and log back in before attempting to order Inventory again. </p>
    </div>

  </app-modalbox>
