<div class="sidebox">
  <h2 class="sidebox__title">Auto Quote</h2>
  <div class="sidebox__content">

    <div class="aboutbox">
      <h3 class="aboutbox__title">Client:</h3>
      <a [routerLink]='[]' [queryParams]="{overlay: 'info', type: 'client'}" class="aboutbox__link o-link">
        {{displayClientFullname(client)}}
      </a>
    </div> <!-- / .aboutbox -->

    <div class="aboutbox">
      <h3 class="aboutbox__title">Plans:</h3>
      <app-plans-selector [plans]="plansOnQuotes" [activePlans]="newRmvQuote?.plans" (onSelect)="updateQuotePlan($event)" [css]="'aboutbox__link o-link o-link--expand sidebox__link-dropdown-toggle'"></app-plans-selector>
    </div> <!-- / .aboutbox -->

    <div class="aboutbox">
      <h3 class="aboutbox__title">Effective Date:</h3>
      <button class="aboutbox__link o-link o-link--expand" id="aside-effective-date">{{ newRmvQuote?.effectiveDate | dateFormat: 'MMM d, yyyy' }}</button>
      <app-datepicker-modal
        #datepickerEffectiveDate
        [launcher]="'#aside-effective-date'"
        [title]="'Effective date'"
        [selectDate]="newRmvQuote?.effectiveDate"
        [returnDateFormat]="'MMM d, yyyy'"
        (onSave)="updateEffectiveDate($event)">
      </app-datepicker-modal>
    </div> <!-- / .aboutbox -->

  </div>
</div> <!-- / .sidebox -->

<div class="sidebox__separator"></div>

<!--<app-aside-quick-links-info></app-aside-quick-links-info>-->
