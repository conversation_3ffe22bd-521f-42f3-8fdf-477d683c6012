import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';

import { AsideQuickLinksInfoComponent } from './aside-quick-links-info.component';

describe('Component: AsideQuickLinksInfo', () => {
  let component: AsideQuickLinksInfoComponent;
  let fixture: ComponentFixture<AsideQuickLinksInfoComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      declarations: [ AsideQuickLinksInfoComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AsideQuickLinksInfoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
