<section class="section section--compact u-spacing--2-5">
  <div class="row">
    <h1 class="o-heading" style="color:#0b71ac; padding-left: 20px;">Lienholder</h1>
  </div>
  <div class="row u-spacing--1">
    <div class="col-xs-12">
      <div class="box box--silver">
        <table class="form-table">
          <tr class="form-table__row">
            <td class="form-table__cell u-width-80px">
              <label for="">Company:</label>
            </td>
            <td class="form-table__cell u-width-200px">
              <input [(ngModel)]="name" name="name" placeholder="Name" required disabled (keyup.enter)="onChangeName()"
                (keydown.tab)="onChangeName()">
            </td>
            <td class="form-table__cell u-width-10px">or</td>
            <td class="form-table__cell u-width-160px">
              <input [(ngModel)]="code" name="code" placeholder="Lienholder Code" required disabled (keyup.enter)="onChangeCode()"
                (keydown.tab)="onChangeCode()">
            </td>
            <td class="form-table__cell u-width-160px" style="color: #1989C9;"><a id="lienholderLookup-modal"> Look up
                Lienholder</a></td>
          </tr>
        </table>
      </div>
    </div>
  </div>
</section>
<app-modalbox #modalLienHolderLookup [launcher]="'#lienholderLookup-modal'" [css]="'u-width-850px modal-margin'">
  <app-lienholder-lookup [modalBox]="modalLienHolderLookup" (onSelectedLienholderClick)="setSelectedLienholder($event)">
  </app-lienholder-lookup>
</app-modalbox>
