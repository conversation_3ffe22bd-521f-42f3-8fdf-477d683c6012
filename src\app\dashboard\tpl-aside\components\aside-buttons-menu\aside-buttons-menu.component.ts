import {take} from 'rxjs/operators';
import { FeatureResponse, Feature } from 'app/shared/services/feature.service';
import { Component, OnInit, OnDestroy, ViewChild, Input } from '@angular/core';
import { TooltipComponent } from 'app/shared/modules/sm-popups/components/tooltip/tooltip.component';
import { UserSubscriptionRatingEmitDataI } from 'app/shared/directives/user-subscription-rating.directive';
import {
  AgencyUserService,
  UserEvrLitePermitStatusI,
  UserSubscriptionInformationAPIResopnseI
} from 'app/shared/services/agency-user.service';
import { SubscriptionLike as ISubscription } from 'rxjs';
import { FeatureService } from 'app/shared/services/feature.service';

import { RmvService } from '../../../app-services/rmv.service';
import { OverlayLoaderService } from '../../../../shared/services/overlay-loader.service';
import { Router, RoutesRecognized } from '@angular/router';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { AsideRegistrationResponseComponent } from './aside-registration-response/aside-registration-response.component';
import { filter, pairwise } from 'rxjs/operators';

@Component({
    selector: 'app-aside-buttons-menu',
    templateUrl: './aside-buttons-menu.component.html',
    styleUrls: ['./aside-buttons-menu.component.scss'],
    standalone: false
})
export class AsideButtonsMenuComponent implements OnInit, OnDestroy {
  @Input() isDashboard = true;

  private FeatureInfoSubscription: ISubscription;
  private featureResponse: FeatureResponse;
  private subscriptionSubscriptionInformation: ISubscription;
  public subscriptionMessages: string[] = [];
  public proceed = true;
  public formsOnlyUser = false;
  features: any;
  public activeFeatures: Feature[];
 plateNumber;
 regData;
 transactionType;
 rmvEnabled;
 rmvExpired;
 formsExpired;
 ref: DynamicDialogRef | undefined

  @ViewChild('refTooltipNewAuto') refTooltipNewAuto: TooltipComponent;
  @ViewChild('refTooltipNewHome') refTooltipNewHome: TooltipComponent;
  @ViewChild('refTooltipNewDwelling') refTooltipNewDwelling: TooltipComponent;
  @ViewChild('refTooltipNewUmbrella') refTooltipNewUmbrella: TooltipComponent;
  @ViewChild('refTooltipNewForm') refTooltipNewForm: TooltipComponent;
  @ViewChild('registrationSuccess') regModal;
  @ViewChild('rmvNotEnabled') rmvNotEnabledModal;


  public rmvNotEnabledMessage = 'RMV access not enabled for the current user';





  constructor(
    private agencyUserService: AgencyUserService,
    private featureService: FeatureService,
    private rmvService: RmvService,
    private overlayLoaderService: OverlayLoaderService,
    private router: Router,
    public dialogService:DialogService
  ) {
    this.formsOnlyUser = false;
  }

  ngOnInit() {
    var previousUrl ="";
    this.subscribeSubscriptionInformation();
    this.subscribeFeatureInformation();
    this.CheckRmvAccessInformation();

  }

  ngOnDestroy() {
    this.subscriptionSubscriptionInformation &&
      this.subscriptionSubscriptionInformation.unsubscribe();
    this.FeatureInfoSubscription &&
      this.FeatureInfoSubscription.unsubscribe();
    this.closeTooltips();
  }

  showNewIndicator() {
    const date = new Date();
    const expiration = 9;
   return date.getMonth() < expiration ? true : false;
  }

  private closeTooltips() {
    this.refTooltipNewAuto && this.refTooltipNewAuto.close();
    this.refTooltipNewHome && this.refTooltipNewHome.close();
    this.refTooltipNewDwelling && this.refTooltipNewDwelling.close();
    this.refTooltipNewUmbrella && this.refTooltipNewUmbrella.close();
    this.refTooltipNewForm && this.refTooltipNewForm.close();
  }

  // https://bostonsoftware.atlassian.net/browse/SPR-2778
  private subscribeSubscriptionInformation(): void {
    this.subscriptionSubscriptionInformation = this.agencyUserService.getSubscriptionInformation$.subscribe(
      (res: UserSubscriptionInformationAPIResopnseI) => {
       const autoExpired = res?.items?.find(x => x.code === 'MAAuto').expirationDate;
       const homeExpired = res?.items?.find(x => x.code === 'MAHome').expirationDate;
       const RIdwellingExpired = res?.items?.find(x => x.code === 'RIDwelling').expirationDate;
       const RIhomeExpired = res?.items?.find(x => x.code === 'RIHome').expirationDate;

       const DwellingExpired = res?.items?.find(x => x.code === 'Dwelling').expirationDate;
       const umbrellaExpired = res?.items?.find(x => x.code === 'Umbrella').expirationDate;

       this.formsOnlyUser = autoExpired || homeExpired || DwellingExpired || umbrellaExpired || RIdwellingExpired || RIhomeExpired ? false : true;

        this.rmvExpired = res?.items?.find(x => x.code === 'RMV').expired;
        this.formsExpired = res?.items?.find(x => x.code === 'Forms').expired;

      }
    );
  }

  private subscribeFeatureInformation(): void {
    this.featureService.activeFeatures.subscribe(x => this.activeFeatures = x);
  //  this.FeatureInfoSubscription = this.featureService.getFeaturesFromAPI().pipe(take(1)).subscribe(
  //    (data: FeatureResponse) => {
  //      this.featureResponse = data;
  //      if (this.featureResponse && this.featureResponse.items ) {
   //       this.activeFeatures = this.featureResponse.items.filter(f => f.enabled);
   //     }
   //   }
  //  );
  }

  private CheckRmvAccessInformation(): void {

  this.rmvEnabled = false;
  this.rmvService.CheckRmvAccess().subscribe((x) => {
      this.rmvEnabled = x.rmvAccessEnabled;
    });
}


  public isFeatureEnabled(featureName): boolean {
    if (this.activeFeatures !== undefined && this.activeFeatures.length > 0) {
      const enabledFeatures = this.activeFeatures.filter(ef => ef.name === featureName);
      return (enabledFeatures !== undefined && enabledFeatures != null && enabledFeatures.length > 0);
    } else {
      return false;
    }
  }

  public createManuallyClick($ev, refTooltip): void {
    refTooltip.close();
  }

  public cancelClick($ev, refTooltip): void {
    refTooltip.close();
  }

  public setMsg(data: UserSubscriptionRatingEmitDataI): void {
    if (data && data.messages) {
      this.subscriptionMessages = data.messages;
    this.proceed = data.proceed;

    }
  }

  public closeSubscriptionWarning(
    proceed: boolean = true,
    refTooltip: TooltipComponent
  ): void {
    if (proceed && refTooltip) {
      refTooltip.open();
    }
  }

  public createFormClick(data, refTooltip: TooltipComponent): void {
    refTooltip.close();
  }

  public adjustTooltipPosition(refTooltip: TooltipComponent): void {
    if (refTooltip) {
      setTimeout(() => {
        refTooltip.adjustPosition();
      }, 50);
    }
  }

  registrationValidation(transaction) {
   if (!this.rmvExpired) {
    this.transactionType = transaction === 'RegistrationReinstatementDataValidationEasy'
    ? 'RegistrationReinstatement' : 'RegistrationRenewal';
   const data = {
      transaction: {type: transaction},
      vehicle: {
        plateNumber: this.plateNumber.trim()
      }
    };
    if (this.rmvEnabled) {
      this.overlayLoaderService.showLoader();
      this.rmvService.Validation(data).subscribe(
        x => {
               this.overlayLoaderService.hideLoader();
               this.regData = x;
               this.ref = this.dialogService.open(AsideRegistrationResponseComponent, {
                width: '60%',
                data: {
                  plateNumber: this.plateNumber,
                  regData: this.regData,
                  transaction: this.transactionType
                }
               })
               // this.regModal.open();
              }, err => {
                  this.overlayLoaderService.hideLoader();
                 }
        );
    } else {
      this.rmvNotEnabledModal.open();

      this.overlayLoaderService.hideLoader();
    }
  }
   // this.regModal.open();
  }

  navigateToRmvService() {
    if (!this.formsExpired) {
      this.router.navigate(['/dashboard/rmv-services']);
    }
  }
}
