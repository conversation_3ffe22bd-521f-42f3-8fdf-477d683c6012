// local-storage.service.ts

import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root'
})
export class LocalStorageService {
  private openTabsKey = 'openTabsCount';

  constructor() { }

  setOpenTabsCount(count: number) {
    localStorage.setItem(this.openTabsKey, count.toString());
  }

  getOpenTabsCount(): number {
    const count = localStorage.getItem(this.openTabsKey);
    return count ? parseInt(count, 10) : 0;
  }

  decreaseOpenTabsCount() {
    const count = this.getOpenTabsCount();
    if (count > 0) {
      this.setOpenTabsCount(count - 1);
    }
  }

  increaseOpenTabsCount() {
    const count = this.getOpenTabsCount();
    this.setOpenTabsCount(count + 1);
  }

  clearLocalStorage() {
    localStorage.clear();
  }
}
