import { saveAs } from 'file-saver';
import { Component, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { DateRange } from 'app/app-model/date';
import { FilterOption } from 'app/app-model/filter-option';
import { RmvService } from 'app/dashboard/app-services/rmv.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { FilterDatesComponent } from 'app/shared/components/filter-dates/filter-dates.component';
import { AgencyUserService } from 'app/shared/services/agency-user.service';
import { take } from 'rxjs/operators';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { ToastrService } from 'ngx-toastr';
import { format } from 'date-fns';

@Component({
    selector: 'app-download-completed-docs',
    templateUrl: './download-completed-docs.component.html',
    styleUrl: './download-completed-docs.component.scss',
    standalone: false
})
export class DownloadCompletedDocsComponent {
  constructor(private rmvService: RmvService, private specsService: SpecsService, private router: Router,
    private userService: AgencyUserService, private overlayLoaderService: OverlayLoaderService,private toastService: ToastrService) { }
  @ViewChild(FilterDatesComponent) filterDates;
  list = [];
  criteria;
  searchQuery;
  dataToChange;
  size = 0;
  limit = 10;
  paginationResultShowFrom = 0;
  userId;
  agencyId;
  dataInitiated = false;
  searchType = 'vin';
  typeOptions;
  expiredId;
  expiredTransactionType;
  expireMessage = '';
  isDatesFilterViewEnabled = false;
  paginationCurrentPage = 1;
  public dateRange: DateRange = new DateRange();


  public filterRequesterOptions: FilterOption[] = [
    { id: '0', text: 'Myself' },
    { id: '1', text: 'All' },
  ];
  public filterTransactionTypeOptions: FilterOption[] = [
    { id: 'All', text: 'All' },
    { id: 'DuplicateRegistration', text: 'Duplicate Registration' },
    { id: 'PrefillNewTitleAndRegistration', text: 'New Registration & Title' },
    { id: 'PrefillRenewRegistration', text: 'Registration Renewal' },
    { id: 'PrefillReinstateRegistration', text: 'Registration Reinstate' },
    { id: 'PrefillRegistrationTransfer', text: 'Transfer Registration' },

  ]

  filterLocationsSelectedOption: FilterOption = this.filterRequesterOptions[1];
  filterTransactionTypeSelectedOption: FilterOption = this.filterTransactionTypeOptions[0]
  public isAllSelected = false;
  public selectedItems: string[] = [];

  ngOnInit(): void {
    setTimeout(() => this.getTransactions(true));
    this.specsService.getTransactionsAsOptions().subscribe(x => this.typeOptions = x);
    this.userService.userData$.subscribe(x => { this.userId = x.user.userId; this.agencyId = x.agencyId; });
    this.isDatesFilterViewEnabled = JSON.parse(localStorage.getItem('features')).find(x => x.name === 'SprApp_DatesFilterView');
  }

  getTransactions(showResultsFromFirstPage = false) {
    if (showResultsFromFirstPage) {
      this.paginationResultShowFrom = 0;
      this.paginationCurrentPage = 1;
    }

    const location = this.filterLocationsSelectedOption.text === 'All' ? this.agencyId : '';
    const userId = this.filterLocationsSelectedOption.text === 'Myself' ? this.userId : 0;
    const transactionType = this.filterTransactionTypeSelectedOption.id === 'All' ? '' : this.filterTransactionTypeSelectedOption.id;
    let startDate, endDate;

    let criteria = this.searchQuery !== '' && this.searchQuery !== undefined ? `?${this.searchType}=${this.searchQuery}` : '';
    if (criteria == '') {
      startDate = this.dateRange.start ? format(new Date(this.dateRange.start), 'yyyy-MM-dd') : '';
      endDate = this.dateRange.end ? format(new Date(this.dateRange.end), 'yyyy-MM-dd') : '';
    }
    criteria !== '' ? criteria += `&location=${location}&status=Complete&userId=${userId}&transactionType=${transactionType}` :
      criteria = `?location=${location}&userId=${userId}&status=Complete&transactionType=${transactionType}`;

    if (startDate != undefined && endDate != undefined) {
      criteria = criteria + `&startDate=` + startDate + '&endDate=' + endDate;
    }

    // criteria = `?offset=${this.paginationResultShowFrom}`;
    this.paginationResultShowFrom = isNaN(this.paginationResultShowFrom) ? 0 : this.paginationResultShowFrom;

    this.rmvService.getSavedRmvList(criteria, this.paginationResultShowFrom, this.limit).pipe(take(1)).subscribe(x => {
      this.dataInitiated = true;
      this.list = x.items; this.size = x.size;
    });

    if (this.searchQuery === '') {
      this.searchType = 'vin';
    }

  }
  splitWords(word) {
    return word.replace(/([A-Z])/g, ' $1').replace('Prefill', '').trim();
  }

  goToRmvServices() {
    this.router.navigate([`/dashboard/rmv-services/rta-prefill`], { queryParams: { id: this.expiredId, 'type': this.expiredTransactionType } });

  }

  public onFilterDataChange($ev): void {
    this.filterDataChangeUpdate($ev);
    // this.filterResults();
  }



  private onLocationChange(option: FilterOption): void {
    this.filterLocationsSelectedOption = option;
  }
  private onTransactionTypeChange(option: FilterOption): void {
    this.filterTransactionTypeSelectedOption = option;
  }

  private filterDataChangeUpdate($ev): void {
    if (this.dataToChange) {
      this.dataToChange = $ev;
      switch ($ev.filterId) {
        case 'filter_locations':
          this.onLocationChange($ev.selectedOption);
          break;
        case 'filter_requester':
          this.onLocationChange($ev.selectedOption);
          break;
        case 'filter_transactionType':
          this.onTransactionTypeChange($ev.selectedOption);
          break;
      }
      this.getTransactions(true);
    } else {
      this.dataToChange = $ev;
    }
  }

  filterByName(event) {
    this.filterDates.searchQuery = this.searchQuery;
    if ((event.key === 'Enter' && this.searchQuery.length >= 3) || (event.target.tagName === 'BUTTON' && this.searchQuery.length >= 3)) {
      this.filterDates.filterDatesLabel = 'All Dates';
      this.getTransactions();
    }
  }


  getTransactionsFromFilteredDates() {
    this.dateRange = this.filterDates.dateRange;
    const allowFiltering = this.filterDates.isFilteringAllowed();

    if (allowFiltering) {
      this.getTransactions();
    }
    this.filterDates.tooltip.close();

  }
  resetSearch() {
    this.filterDates.prepareResetSearch();
    this.dateRange.start = null;
    this.dateRange.end = null;
    this.getTransactions();
  }

  downloadDocuments(row) {
    this.overlayLoaderService.showLoader();

      this.rmvService.downloadDocuments(row.rmvServicesId).subscribe(x => {
        const blob = new Blob([x], {
          type: 'application/zip'
        })
        saveAs(blob,`${row.ownerFirstName}_${row.ownerLastName}_RmvDocs` );
        this.overlayLoaderService.hideLoader();
      }, err => {this.overlayLoaderService.hideLoader()
        this.toastService.warning('Sorry, Download error, please try again.')
        });



  }

  public paginationPageChange(data) {
    if (data.startAt < 0 || Object.is(NaN, data.startAt)) {
      data.startAt = 0;
      data.pageNumber = 1;
    }
    if (this.dataInitiated && this.paginationResultShowFrom !== data.startAt) {
      this.paginationCurrentPage = data.pageNumber;
      this.paginationResultShowFrom = data.startAt;
      this.getTransactions();
    }

  }
  getDisplayName(transactionType) {
    return this.typeOptions.find(x => x.id === transactionType).text;
  }

  private paginationSetResultLimit(intLimit: any) {
    this.limit = parseInt(intLimit, 10);
  }

  public onResultLimitChange($ev): void {
    if (this.limit !== $ev.limit) {
      setTimeout(() => {
        this.paginationSetResultLimit($ev.limit);
        this.getTransactions();
      }
      );
    }
  }


}
