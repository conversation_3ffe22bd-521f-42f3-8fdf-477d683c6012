import { async, ComponentFixture, fakeAsync, inject, TestBed, tick } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';

import { DWELLING_SCHEDULED_PROPERTY } from 'testing/data/dwellings/scheduled-property';
import { HOME_QUOTE } from 'testing/data/quotes/quote-home';
import { StubDetectSystemDirective } from 'testing/stubs/directives/detect-system.directive';
import { StubDwellingServiceProvider } from 'testing/stubs/services/dwelling.service.provider';
import {
    StubOverlayLoaderServiceProvider
} from 'testing/stubs/services/overlay-loader.service.provider';

import { StorageService } from 'app/shared/services/storage-new.service';
import { Helpers } from 'app/utils/helpers';

import { JewelryComponent } from './jewelry.component';
import { changeTextInputValue, selectCheckbox } from 'testing/helpers/all';

describe('Component: Jewelry', () => {
  let component: JewelryComponent;
  let fixture: ComponentFixture<JewelryComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [FormsModule],
      declarations: [
        JewelryComponent,
        StubDetectSystemDirective
      ],
      providers: [
        StorageService,
        StubDwellingServiceProvider,
        StubOverlayLoaderServiceProvider
      ]
    })
    .compileComponents();
  }));

  beforeEach(fakeAsync(inject([StorageService], (storageService: StorageService) => {
    storageService.setStorageData('selectedQuote', Helpers.deepClone(HOME_QUOTE));
    storageService.setStorageData('dwellingScheduledProperty', Helpers.deepClone(DWELLING_SCHEDULED_PROPERTY.items[0].items));

    fixture = TestBed.createComponent(JewelryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  })));

  it('should be created', () => {
    expect(component).toBeTruthy();
  });

  it('should destroy without errors', () => {
    expect(() => {
      fixture.destroy();
    }).not.toThrow();
  });

  it('should enable Home Safe radio if amount of not vaulted items is greater than 0', () => {
    const inputs = fixture.debugElement.queryAll(By.css('input[name="safeinUse"]'));

    inputs.forEach(input => expect(input.nativeElement.disabled).toBeFalsy());
  });

  it('should disable Home Safe radio if amount of not vaulted items is 0', fakeAsync(() => {
    component.propJewelryNotVaulted.itemValueAmount = 0;
    fixture.detectChanges();
    tick(50);

    const inputs = fixture.debugElement.queryAll(By.css('input[name="safeinUse"]'));

    inputs.forEach(input => expect(input.nativeElement.disabled).toBeTruthy());
  }));

  it('should be able to handle not vaulted items amount change', fakeAsync(() => {
    changeTextInputValue('#jewelryNotVaulted', 3, fixture);

    expect(component.propJewelryNotVaulted.itemValueAmount).toEqual(3);
  }));

  it('should be able to handle vaulted items amount change', fakeAsync(() => {
    changeTextInputValue('#jewelryVaulted', 3, fixture);

    expect(component.propJewelryVaulted.itemValueAmount).toEqual(3);
  }));

  it('should be able to handle home safe use change', fakeAsync(() => {
    selectCheckbox('#isHomeSafeInUseYes', fixture);

    expect(component.propJewelryNotVaulted.storageTypeCode).toEqual('Safe');
  }));
});
