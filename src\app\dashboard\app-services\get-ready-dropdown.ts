export const PLATE_TYPES = [
    { id: 'RCCMPN', text: 'RCCMPN - Camper Normal (AHN)' },
{ id: 'RSCAMP', text: 'RSCAMP - Camper Reserved (AHR)' },
{ id: 'RCCMPV', text: 'RCCMPV - Camper Vanity (AHV)' },
{ id: 'EMAMBL', text: 'EMAMBL - Ambulance (AMN)' },
{ id: 'EMAMBA', text: 'EMAMBA - Animal Ambulance (AMN)' },
{ id: 'RSAMBL', text: 'RSAMBL - Ambulance Reserved (AMR)' },
{ id: 'IRVNOM', text: 'IRVNOM - Apportioned Normal (APN)' },
{ id: 'DCIRVR', text: 'DCIRVR - Apportioned Reserved (APR)' },
{ id: 'DCIRVV', text: 'DCIRVV - Apportioned Vanity (APV)' },
{ id: 'TAATRG', text: 'TAATRG - Attleboro Transit Authority (ATN)' },
{ id: 'TABKRG', text: 'TABKRG - Berkshire Transit Authority (ATN)' },
{ id: 'TABRRG', text: 'TABRRG - Brockton Transit Authority (ATN)' },
{ id: 'TACATA', text: 'TACATA - Cape Ann Transit Authority (ATN)' },
{ id: 'TACCTR', text: 'TACCTR - Cape Cod Transit Authority (ATN)' },
{ id: 'TAFRRT', text: 'TAFRRT - Franklin Transit Authority (ATN)' },
{ id: 'TAGFMG', text: 'TAGFMG - Greenfield Montague Transit Authority (ATN)' },
{ id: 'TALWTA', text: 'TALWTA - Lowell Transit Authority (ATN)' },
{ id: 'TAMV_2', text: 'TAMV_2 - Martha’s Vineyard SE Transit Authority (MVTA) (ATN)' },
{ id: 'TAMV_1', text: 'TAMV_1 - Martha’s Vineyard SE Transit Authority (VTA) (ATN)' },
{ id: 'TAMBTA', text: 'TAMBTA - Mass Bay Transit Authority (ATN)' },
{ id: 'TAMTPA', text: 'TAMTPA - Mass Turnpike Transit Authority (ATN)' },
{ id: 'TAMMVT', text: 'TAMMVT - Merrimac Valley Transit Authority (ATN)' },
{ id: 'TAMWRT', text: 'TAMWRT - MetroWest Transit Authority (ATN)' },
{ id: 'TAMTRG', text: 'TAMTRG - Montachusett Transit Authority (ATN)' },
{ id: 'TANKRE', text: 'TANKRE - Nantucket Transit Authority (ATN)' },
{ id: 'TAPVLT', text: 'TAPVLT - Pioneer Valley Transit Authority (ATN)' },
{ id: 'TASERG', text: 'TASERG - Southeastern Regional Transit Authority (ATN)' },
{ id: 'TATRPO', text: 'TATRPO - Transit Police (ATN)' },
{ id: 'TAWTRE', text: 'TAWTRE - Water Resources Transit Authority (ATN)' },
{ id: 'TAWCRT', text: 'TAWCRT - Worcester Transit Authority (ATN)' },
{ id: 'TATRPM', text: 'TATRPM - Transit Police Motorcycle (AXN)' },
{ id: 'S5BDNL', text: 'S5BDNL - Boat Dealer Normal (BDN)' },
{ id: 'BUSNML', text: 'BUSNML - Bus Normal (BUN)' },
{ id: 'RSBUSR', text: 'RSBUSR - Bus Reserved (BUR)' },
{ id: 'BUSVNY', text: 'BUSVNY - Bus Vanity (BUV)' },
{ id: 'CMCOMM', text: 'CMCOMM - Commercial (CON)' },
{ id: 'CMCETC', text: 'CMCETC - Commercial Electric (CON)' },
{ id: 'DCCFRM', text: 'DCCFRM - Commercial Farm Tractor (CON)' },
{ id: 'CMCLTD', text: 'CMCLTD - Commercial Limited Use (CON)' },
{ id: 'CMCHSE', text: 'CMCHSE - Hearse (CON)' },
{ id: 'CMSWRV', text: 'CMSWRV - Snow Removal (CON)' },
{ id: 'RSCOMM', text: 'RSCOMM - Commercial Reserved (COR)' },
{ id: 'RSCHSE', text: 'RSCHSE - Hearse Reserved (COR)' },
{ id: 'CMCVNY', text: 'CMCVNY - Commercial Vanity (COV)' },
{ id: 'S5NMDL', text: 'S5NMDL - Dealer Normal (DLN)' },
{ id: 'S5VYDL', text: 'S5VYDL - Dealer Vanity (DLV)' },
{ id: 'S5MDNL', text: 'S5MDNL - Dealer Motorcycle Normal (DMN)' },
{ id: 'S5FMDL', text: 'S5FMDL - Farm Normal (FAN)' },
{ id: 'LVLUPL', text: 'LVLUPL - Livery Limited Use (LNV)' },
{ id: 'LVNMPL', text: 'LVNMPL - Livery Normal (LVN)' },
{ id: 'LVRSPL', text: 'LVRSPL - Livery Reserved (LVR)' },
{ id: 'LVVNTY', text: 'LVVNTY - Livery Vanity (LVV)' },
{ id: 'MCMCYL', text: 'MCMCYL - Motorcycle Normal (MCN)' },
{ id: 'MCANTQ', text: 'MCANTQ - Antique Motorcycle (MCR)' },
{ id: 'MCBLKN', text: 'MCBLKN - Blue Knight (MCR)' },
{ id: 'RSMCMY', text: 'RSMCMY - Motorcycle Reserved (MCR)' },
{ id: 'MCRDKN', text: 'MCRDKN - Red Knight (MCR)' },
{ id: 'MCMSBS', text: 'MCMSBS - Motorcycle Bronze Star (MCS)' },
{ id: 'MCSPHC', text: 'MCSPHC - Motorcycle Disabled (MCS)' },
{ id: 'MCMSDF', text: 'MCMSDF - Motorcycle Distinguished Flying Cross (MCS)' },
{ id: 'MCMSEP', text: 'MCMSEP - Motorcycle Ex-POW (MCS)' },
{ id: 'MCMSGF', text: 'MCMSGF - Motorcycle Gold Star Family (MCS)' },
{ id: 'MCGFVY', text: 'MCGFVY - Motorcycle Gold Star Family Vanity (MCS)' },
{ id: 'MCMSLV', text: 'MCMSLV - Motorcycle Legion of Valor (MCS)' },
{ id: 'MCMSLU', text: 'MCMSLU - Motorcycle Limited Use (MCS)' },
{ id: 'MCMSMH', text: 'MCMSMH - Motorcycle Medal of Honor (MCS)' },
{ id: 'MCMSPR', text: 'MCMSPR - Motorcycle Pearl Harbor (MCS)' },
{ id: 'MCMSPH', text: 'MCMSPH - Motorcycle Purple Heart (MCS)' },
{ id: 'MCMSSS', text: 'MCMSSS - Motorcycle Silver Star (MCS)' },
{ id: 'MCMSVT', text: 'MCMSVT - Motorcycle Veteran (MCS)' },
{ id: 'MCVNTY', text: 'MCVNTY - Motorcycle Vanity (MCV)' },
{ id: 'MOPED', text: 'MOPED - Moped (MPN)' },
{ id: 'MPMNCP', text: 'MPMNCP - Municipal (MVN)' },
{ id: 'MPBOST', text: 'MPBOST - Municipal Boston (MVN)' },
{ id: 'MPHDCP', text: 'MPHDCP - Municipal Disabled (MVN)' },
{ id: 'MPFIRE', text: 'MPFIRE - Municipal Fire (MVN)' },
{ id: 'MPPLCE', text: 'MPPLCE - Municipal Police (MVN)' },
{ id: 'MCMNCP', text: 'MCMNCP - Motorcycle Municipal (MXN)' },
{ id: 'S5ORNL', text: 'S5ORNL - Owner/Contractor Normal (OCN)' },
{ id: 'RNPSNG', text: 'RNPSNG - Passenger Normal Green (PAN)' },
{ id: 'PANPL', text: 'PANPL - Passenger Normal Red (PAN)' },
{ id: 'PASLTY', text: 'PASLTY - Passenger Lottery (PAR)' },
{ id: 'RSPAPL', text: 'RSPAPL - Passenger Reserved (PAR)' },
{ id: 'CBAOPL', text: 'CBAOPL - ALS One (PAS)' },
{ id: 'SPANTQ', text: 'SPANTQ - Antique (PAS)' },
{ id: 'SPATQV', text: 'SPATQV - Antique Vanity (PAS)' },
{ id: 'CBHFPL', text: 'CBHFPL - Basketball Hall of Fame (PAS)' },
{ id: 'CBBVPL', text: 'CBBVPL - Blackstone Valley (PAS)' },
{ id: 'VTBSPL', text: 'VTBSPL - Bronze Star (PAS)' },
{ id: 'CBBUPL', text: 'CBBUPL - Bruins (PAS)' },
{ id: 'RNBRNS', text: 'RNBRNS - Bruins (Renew Only) (PAS)' },
{ id: 'CBCIPL', text: 'CBCIPL - Cape and Island (PAS)' },
{ id: 'CBCAPL', text: 'CBCAPL - Cape Ann (PAS)' },
{ id: 'CBCLPL', text: 'CBCLPL - Celtics (PAS)' },
{ id: 'RNBSCL', text: 'RNBSCL - Celtics (Renew Only) (PAS)' },
{ id: 'CBCHPL', text: 'CBCHPL - Choose Life (PAS)' },
{ id: 'VTCMOH', text: 'VTCMOH - Congressional Medal of Honor (PAS)' },
{ id: 'CBCCPL', text: 'CBCCPL - Conquer Cancer (PAS)' },
{ id: 'CBBRPL', text: 'CBBRPL - Cure Breast Cancer (PAS)' },
{ id: 'HCRGPL', text: 'HCRGPL - Disabled Parking (PAS)' },
{ id: 'RNHVTY', text: 'RNHVTY - Disabled Vanity (PAS)' },
{ id: 'VTDSPL', text: 'VTDSPL - Disabled Veteran (PAS)' },
{ id: 'VTDFPL', text: 'VTDFPL - Distinguished Flying Cross (PAS)' },
{ id: 'SPETPL', text: 'SPETPL - Electric Vehicle (PAS)' },
{ id: 'EMAMBV', text: 'EMAMBV - Emergency Vehicle (PAS)' },
{ id: 'VTXPPL', text: 'VTXPPL - Ex-POW (PAS)' },
{ id: 'CBFHPL', text: 'CBFHPL - Fallen Heroes (PAS)' },
{ id: 'DCFENW', text: 'DCFENW - Fenway Park (PAS)' },
{ id: 'DCFRFT', text: 'DCFRFT - Fire Fighter (PAS)' },
{ id: 'CBFFPL', text: 'CBFFPL - Fire Fighters Memorial (PAS)' },
{ id: 'CBFWPL', text: 'CBFWPL - Fish and Wildlife (PAS)' },
{ id: 'SPFORG', text: 'SPFORG - Foreign Organization (PAS)' },
{ id: 'CBFMPL', text: 'CBFMPL - Free Masons (PAS)' },
{ id: 'CBFLPL', text: 'CBFLPL - Fresh and Local (PAS)' },
{ id: 'VTGSPL', text: 'VTGSPL - Gold Star Family (PAS)' },
{ id: 'VTGSFV', text: 'VTGSFV - Gold Star Family Vanity Plate (PAS)' },
{ id: 'GVGOVC', text: 'GVGOVC - Governors Council (PAS)' },
{ id: 'CBHHPL', text: 'CBHHPL - Habitat and Heritage (PAS)' },
{ id: 'SPHAMO', text: 'SPHAMO - Ham Radio Operator (PAS)' },
{ id: 'GVCONC', text: 'GVCONC - Honorary Consular Corps (PAS)' },
{ id: 'GVHOUS', text: 'GVHOUS - House (PAS)' },
{ id: 'CBICPL', text: 'CBICPL - Invest in Children (PAS)' },
{ id: 'VTLGOV', text: 'VTLGOV - Legion of Valor (PAS)' },
{ id: 'SPLUPL', text: 'SPLUPL - Limited Use (PAS)' },
{ id: 'SPLWPL', text: 'SPLWPL - Low Speed (PAS)' },
{ id: 'CBMVPL', text: 'CBMVPL - Martha’s Vineyard (PAS)' },
{ id: 'CBACPL', text: 'CBACPL - Mass Animal Coalition (PAS)' },
{ id: 'DCMAMW', text: 'DCMAMW - Mass House Majority Whip (PAS)' },
{ id: 'DCMAHS', text: 'DCMAHS - Mass House Speaker (PAS)' },
{ id: 'SPMDPL', text: 'SPMDPL - Medical Doctor (PAS)' },
{ id: 'CBNTPL', text: 'CBNTPL - Nantucket Island (PAS)' },
{ id: 'MLNGPL', text: 'MLNGPL - National Guard (PAS)' },
{ id: 'CBNEPL', text: 'CBNEPL - New England Patriots (PAS)' },
{ id: 'SPNWSP', text: 'SPNWSP - News Photographer (PAS)' },
{ id: 'CBOYPL', text: 'CBOYPL - Olympic Spirit (PAS)' },
{ id: 'CBPMPL', text: 'CBPMPL - Pan Mass Challenge (PAS)' },
{ id: 'VTPLPL', text: 'VTPLPL - Pearl Harbor Survivor (PAS)' },
{ id: 'CBPLPL', text: 'CBPLPL - Plymouth 400 (PAS)' },
{ id: 'CBSPPL', text: 'CBSPPL - Protect and Serve (PAS)' },
{ id: 'VTHTPL', text: 'VTHTPL - Purple Heart (PAS)' },
{ id: 'CBRXPL', text: 'CBRXPL - Red Sox (PAS)' },
{ id: 'CBRWPL', text: 'CBRWPL - Right Whale (PAS)' },
{ id: 'GVMASN', text: 'GVMASN - Senate (PAS)' },
{ id: 'DCSNPR', text: 'DCSNPR - Senate President (PAS)' },
{ id: 'VTSSPL', text: 'VTSSPL - Silver Star (PAS)' },
{ id: 'TEMPLT', text: 'TEMPLT - Temporary Plate (PAS)' },
{ id: 'GVHORP', text: 'GVHORP - U.S. Congress (PAS)' },
{ id: 'CBUMPL', text: 'CBUMPL - UMass (PAS)' },
{ id: 'CBUSPL', text: 'CBUSPL - United We Stand (PAS)' },
{ id: 'GVUSSN', text: 'GVUSSN - US Senate (PAS)' },
{ id: 'RNUSOL', text: 'RNUSOL - USA Olympic team (PAS)' },
{ id: 'VTVFPL', text: 'VTVFPL - Veteran Flag Plate (PAS)' },
{ id: 'VTVTPL', text: 'VTVTPL - Veteran Plate (PAS)' },
{ id: 'CBRLPL', text: 'CBRLPL - Welcome Home (PAS)' },
{ id: 'CBWTPL', text: 'CBWTPL - White Shark (PAS)' },
{ id: 'PAVNTY', text: 'PAVNTY - Passenger Vanity (PAV)' },
{ id: 'YMYROM', text: 'YMYROM - Year of Manufacture (PAY)' },
{ id: 'MCYRMF', text: 'MCYRMF - Year of Manufacture (Motorcycle) (PAY)' },
{ id: 'S5ORDC', text: 'S5ORDC - Repair Normal (RPN)' },
{ id: 'S5RPVY', text: 'S5RPVY - Repair Vanity (RPV)' },
{ id: 'BSSBNL', text: 'BSSBNL - School Bus Normal (SBN)' },
{ id: 'RSSBUS', text: 'RSSBUS - School Bus Reserved (SBR)' },
{ id: 'STSEMI', text: 'STSEMI - Semi-Trailer (SMN)' },
{ id: 'RSSTRL', text: 'RSSTRL - Semi-Trailer Reserved (SMR)' },
{ id: 'HCSPHC', text: 'HCSPHC - School Pupil Disabled (SPN)' },
{ id: '7DPUPL', text: '7DPUPL - School Pupil Normal (SPN)' },
{ id: 'DCCAPO', text: 'DCCAPO - Capitol Police (STN)' },
{ id: 'POENPL', text: 'POENPL - Environmental Police (STN)' },
{ id: 'DCFRMS', text: 'DCFRMS - Fire Marshall (STN)' },
{ id: 'DCMDCM', text: 'DCMDCM - Metro District Commercial (STN)' },
{ id: 'DCRMVN', text: 'DCRMVN - Registry (STN)' },
{ id: 'HCSTHC', text: 'HCSTHC - State Disabled (STN)' },
{ id: 'POSTPO', text: 'POSTPO - State Police (STN)' },
{ id: 'GVSTVH', text: 'GVSTVH - State Vehicle (STN)' },
{ id: 'DCCPMY', text: 'DCCPMY - Capitol Police Motorcycle (SXN)' },
{ id: 'POMCEN', text: 'POMCEN - Environmental Police Motorcycle (SXN)' },
{ id: 'POMSTP', text: 'POMSTP - Motorcycle State Police (SXN)' },
{ id: 'DCRMVM', text: 'DCRMVM - Registry Motorcycle (SXN)' },
{ id: 'MCSTAT', text: 'MCSTAT - State Motorcycle (SXN)' },
{ id: 'TXLTDU', text: 'TXLTDU - Taxi Limited Use (TAN)' },
{ id: 'TXNRML', text: 'TXNRML - Taxi Normal (TAN)' },
{ id: 'RSTAXI', text: 'RSTAXI - Taxi Reserved (TAR)' },
{ id: 'S5TPNL', text: 'S5TPNL - Transporter Normal (TPN)' },
{ id: 'TRTRLR', text: 'TRTRLR - Trailer (TRN)' },
{ id: 'RSTRLR', text: 'RSTRLR - Trailer Reserved (TRR)' },
{ id: 'TAVPNL', text: 'TAVPNL - Van Pool (VPN)' },
{ id: 'ONLPL',  text: 'ONLPL - Registered Nurse (PAS)'},
{ id: 'ETSBPL', text: 'ETSBPL - Striped Bass (PAS)'},
{ id: 'CBDRPL', text: 'CBDRPL - Dr. Seuss - Legacy Type (PAS)' },
{ id: 'CBZNPL', text: 'CBZNPL - Zoo New England - Legacy Type (PAS)' },
{ id: 'CBODPL', text: 'CBODPL - Overdose Awareness - Legacy Type (PAS)' },
{ id: 'SPMAPL', text: 'SPMAPL - 250 Years of Independence - Legacy Type (PAS)' },
  ];

  export const REGISTRATION_TYPES = [
      { id: 'BUS', text: 'Bus' },
  { id: 'CAMPER', text: 'Camper' },
  { id: 'COM', text: 'Commercial' },
  { id: 'LIV', text: 'Livery' },
  { id: 'MTRCYC', text: 'Motorcycle' },
  { id: 'OTHER', text: 'Other' },
  { id: 'PRS', text: 'Passenger' },
  { id: 'SMTRLR', text: 'Semi-Trailer' },
  { id: 'TAXI', text: 'Taxi' },
  { id: 'TRLR', text: 'Trailer' }


];

export const REGISTRATION_REASONS = [{ id: 'DualReg', text: 'Dual Registration' },
{ id: 'GovLoan', text: 'US Government Loaned' },
{ id: 'Military', text: 'Military Exemption' },
{ id: 'Moped', text: 'Moped' },
{ id: 'OffRoad', text: 'Converted from Off Road' },
{ id: 'OOSLease', text: 'OOS Leased Vehicle' },
{ id: 'TrlWeight', text: 'Trailer Weight <= 3000 lbs' }

];

export const COLORS = [{ id: 'BLK', text: 'Black' },
{ id: 'BLU', text: 'Blue' },
{ id: 'BRO', text: 'Brown' },
{ id: 'GLD', text: 'Gold' },
{ id: 'GRN', text: 'Green' },
{ id: 'GRY', text: 'Gray' },
{ id: 'ONG', text: 'Orange' },
{ id: 'PLE', text: 'Purple' },
{ id: 'RED', text: 'Red' },
{ id: 'SIL', text: 'Silver' },
{ id: 'WHI', text: 'White' },
{ id: 'YEL', text: 'Yellow' }

];

export const TRANSMISSIONS = [
    { id: 'AUTOMA', text: 'Automatic Transmission Type' },
    { id: 'B', text: 'Both Automatic and Manual' },
    { id: 'CVT', text: 'Continuously Variable' },
    { id: 'MANUAL', text: 'Manual Transmission Type' }

];

export const GARAGING_ADDRESS = [
  {id: '', text: ''},
    { id: 'Owner1Residential', text: 'Owner 1 Residential Address' },
{ id: 'Owner1Mailing', text: 'Owner 1 Mailing Address' },
{ id: 'Owner2Residential', text: 'Owner 2 Residential Address' },
{ id: 'Owner2Mailing', text: 'Owner 2 Mailing Address' },
{ id: 'BusinessOwner', text: 'Business Owner Address' },
{ id: 'Vehicle', text: 'Vehicle Garaging Address' },
{ id: 'New', text: 'New Address'}
];

export const TAX_EXEMPT_TYPES  = [
    { id: 'A', text: 'Casualty Acquisition' },
{ id: 'B', text: 'Repossession (MVU-30)' },
{ id: 'C', text: 'Corporate Reorganization (MVU-25)' },
{ id: 'D', text: 'Disabled Veteran (MVU-33)' },
{ id: 'E', text: 'Exempt Organization (ST-2)' },
{ id: 'F', text: 'Family Transfer (MVU-26)' },
{ id: 'FE', text: 'Fire Engine/Ambulance (MVU-22)' },
{ id: 'G', text: 'Gift (MVU-24)' },
{ id: 'I', text: 'Inheritance (MVU-27)' },
{ id: 'K', text: 'Intra-City Busing' },
{ id: 'L', text: 'Leasing Company' },
{ id: 'O', text: 'Even Trade' },
{ id: 'P', text: 'Disabled (MVU-33)' },
{ id: 'Q', text: 'Tax Previously Paid to MA (ST-7R)' },
{ id: 'R', text: 'For Resale' },
{ id: 'S', text: 'Tax Paid to Other State (MVU-29)' },
{ id: 'SS', text: 'Surviving Spouse' },
{ id: 'V', text: 'Short Term Lease' },
{ id: 'W', text: 'Contest Winner (MVU-21)' }
];


export const ODOMETER_OPTIONS = [
    { id: 'ACTU', text: 'Actual Mileage' },
    { id: 'HOURS', text: 'Listed in Hours' },
    { id: 'KILO', text: 'Kilometers' },
    { id: 'OALT', text: 'Odometer may be Altered' },
    { id: 'ODIS', text: 'Odometer Discrepancy' },
    { id: 'ORPL', text: 'Odometer Replaced' }
];

export const BODYSTYLE_OPTIONS = [
  {id: 'Auto-Home', text: 'Auto-Home'},
  {id: 'Trailer', text: 'Trailer'}
];

export const VEHICLE_BODYSTYLE_OPTIONS = [
  {id: 'AUTHM', text: 'Auto Home'},
  {id: 'AMBUL', text: 'Ambulance'},
  {id: 'ARMOR', text: 'Armored Truck'},
  {id: 'BACKH', text: 'Backhoe'},
  {id: 'BOX', text: 'Box Truck'},
  {id: 'BUCKT', text: 'Bucket Truck'},
  {id: 'BUS', text: 'Bus'},
  {id: 'CAMP', text: 'Camper'},
  {id: 'CONST', text: 'Construction Vehicle'},
  {id: 'CONVT', text: 'Convertible'},
  {id: 'COUPE', text: 'Coupe'},
  {id: 'CRANE', text: 'Crane'},
  {id: 'DUMP', text: 'Dump Truck'},
  {id: 'FIRE', text: 'Fire Truck'},
  {id: 'FLAT', text: 'Flatbed Truck'},
  {id: 'FRMTR', text: 'Farm Tractor'},
  {id: 'GARBA', text: 'Garbage Truck'},
  {id: 'HARDT', text: 'Hardtop'},
  {id: 'HATCH', text: 'Hatchback'},
  {id: 'HERSE', text: 'Hearse'},
  {id: 'LIMO', text: 'Limousine'},
  {id: 'LOADR', text: 'Loader'},
  {id: 'LSV', text: 'Low Speed Vehicle'},
  {id: 'MIXER', text: 'Mixer Truck'},
  {id: 'MOPED', text: 'Moped'},
  {id: 'MOTCY', text: 'Motorcycle'},
  {id: 'OTHER', text: 'Other'},
  {id: 'PANEL', text: 'Panel Truck'},
  {id: 'PU', text: 'Pickup Truck'},
  {id: 'RAMP', text: 'Ramp Tow Truck'},
  {id: 'ROLL', text: 'Roll-off Truck'},
  {id: 'SCBUS', text: 'School Bus'},
  {id: 'SEDAN', text: 'Sedan'},
  {id: 'STWAG', text: 'Station Wagon'},
  {id: 'SUV', text: 'Sports Utility Vehicle'},
  {id: 'TANK', text: 'Tanker Truck'},
  {id: 'TOW', text: 'Tow Truck'},
  {id: 'TRACT', text: 'Tractor'},
  {id: 'TRAIL', text: 'Trailer'},
  {id: 'UTIL', text: 'Utility Truck'},
  {id: 'VAN', text: 'Van'},
  {id: 'WELL', text: 'Well Drilling Truck'},


];

export const VEHICLE_TYPE_OPTIONS = [
{id: 'AH', text: 'Auto-Home'},
{id: 'AM', text: 'Ambulance'},
{id: 'BU', text: 'Bus'},
{id: 'CM', text: 'Camper'},
{id: 'EQ', text: 'Equipment'},
{id: 'FT', text: 'Fire Truck'},
{id: 'LS', text: 'Low Speed'},
{id: 'MC', text: 'Motorcycle'},
{id: 'MP', text: 'Moped'},
{id: 'PS', text: 'Passenger'},
{id: 'ST', text: 'Semi-Trailer'},
{id: 'TK', text: 'Truck'},
{id: 'TL', text: 'Trailer'}

];


export const FUEL_TYPE_OPTIONS = [
  {id: 'B', text: 'Electric and Gas Hybrid'},
{id: 'C', text: 'Convertible'},
{id: 'D', text: 'Diesel'},
{id: 'E', text: 'Electric'},
{id: 'F', text: 'Flexible'},
{id: 'G', text: 'Gas'},
{id: 'H', text: 'Ethanol'},
{id: 'M', text: 'Methanol'},
{id: 'N', text: 'Compressed Natural Gas'},
{id: 'O', text: 'Other'},
{id: 'P', text: 'Propane'},
{id: 'R', text: 'Hydrogen Fuel Cell'},
{id: 'Y', text: 'Electric and Diesel Hybrid'}

];

export const PHONE_TYPES = [
  {id: '', text: ''},
  {id: 'BSN', text: 'Business Phone'},
  {id: 'BSNFAX', text: 'Fax - Business'},
  {id: 'CELL', text: 'Cell Phone'},
  {id: 'HOM', text: 'Home Phone'},

];

export const UNIT_TYPES = [
  {id: '', text: ''},
  {id: '#', text: '#'},
  {id: 'APT', text: 'Apartment'},
  {id: 'BLDG', text: 'Building'},
  {id: 'BSMT', text: 'Basement'},
  {id: 'DEPT', text: 'Department'},
  {id: 'FL', text: 'Floor'},
  {id: 'FRNT', text: 'Front'},
  {id: 'HNGR', text: 'Hanger'},
  {id: 'LBBY', text: 'Lobby'},
  {id: 'LOT', text: 'Lot'},
  {id: 'LOWR', text: 'Lower'},
  {id: 'NUM', text: 'Number'},
  {id: 'OFC', text: 'Office'},
  {id: 'PH', text: 'Penthouse'},
  {id: 'PIER', text: 'Pier'},
  {id: 'REAR', text: 'REAR'},
  {id: 'RM', text: 'Room'},
  {id: 'SIDE', text: 'Side'},
  {id: 'SLIP', text: 'Slip'},
  {id: 'SPC', text: 'Space'},
  {id: 'STE', text: 'Suite'},
  {id: 'STOP', text: 'Stop'},
  {id: 'TRLR', text: 'Trailer'},
  {id: 'UNIT', text: 'Unit'},
  {id: 'UPPR', text: 'Upper'},

];

export const PLATE_STATUS = [
  {id: 'MISSNG', text: 'Missing'},
  {id: 'DAMGED', text: 'Damaged'}
];

