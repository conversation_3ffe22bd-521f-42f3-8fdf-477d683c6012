export interface Transaction {
	transactionAccepted: string;
	transactionTimestamp: string;
	atlasTransactionKey: string;
}

export interface Header {
	requestID: string;
	transaction: Transaction;
}

export interface Entity {
	atlasEntityKey: string;
	atlasEntityLocationKey: string;
	entityType: string;
	entityTypeDescription: string;
	entitySubType: string;
	entitySubTypeDescription: string;
	entityLocationType: string;
	entityLocationTypeDescription: string;
}

export interface LocationAddress {
	addressStreet1: string;
	addressStreet2: string;
	addressUnitType: string;
	addressUnit: string;
	addressCity: string;
	addressState: string;
	addressZIP: string;
	addressCountry: string;
}

export interface BusinessSummary {
	entity: Entity;
	federalID: string;
	businessName: string;
	businessDBAName: string;
	locationAddress: LocationAddress;
	lessorLongTermIndicator: string;
	lessorShortTermIndicator: string;
}

export interface BusinessList {
	header: Header;
	businessSummaryItems: BusinessSummary[];
}
