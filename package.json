{"name": "singlepoint-ng5", "version": "0.0.3", "license": "MIT", "angular-cli": {}, "scripts": {"ng": "ng", "start": "ng serve -o --output-hashing=all", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "build": "node --max_old_space_size=8192 ./node_modules/.bin/ng build --output-hashing=all --configuration=qat", "build-prod": "node --max_old_space_size=8192 ./node_modules/@angular/cli/bin/ng build --configuration production --configuration=prod --output-hashing=all", "compodoc": "./node_modules/.bin/compodoc -p tsconfig.json"}, "private": true, "dependencies": {"@angular/animations": "^19.2.8", "@angular/cdk": "^19.2.3", "@angular/common": "^19.2.8", "@angular/compiler": "^19.2.8", "@angular/core": "^19.2.8", "@angular/forms": "^19.2.8", "@angular/material": "^19.2.3", "@angular/platform-browser": "^19.2.8", "@angular/platform-browser-dynamic": "^19.2.8", "@angular/router": "^19.2.8", "@microsoft/signalr": "^8.0.7", "@primeng/themes": "^19.1.0", "@types/underscore": "^1.9.4", "@zxcvbn-ts/core": "^2.1.0", "@zxcvbn-ts/language-common": "^2.0.1", "@zxcvbn-ts/language-en": "^2.1.0", "angular2-ie9-shims": "0.0.2", "angulartics2": "~6.0.0", "bootstrap": "^4.0.0-alpha.5", "core-js": "^2.6.11", "custom-event": "^1.0.1", "date-fns": "^4.1.0", "file-saver": "^1.3.8", "ng-recaptcha": "^13.2.1", "ng2-cookies": "^1.0.12", "ngx-cookie-service": "^19.1.2", "ngx-device-detector": "^8.0.0", "ngx-mask": "^15.1.5", "ngx-pipes": "^3.2.2", "ngx-toastr": "^19.0.0", "normalize-scss": "^7.0.0", "npm": "^6.14.2", "primeicons": "^6.0.1", "primeng": "^19.1.0", "rxjs": "7.8", "rxjs-compat": "^6.0.0-rc.0", "sha1": "^1.1.1", "stream": "0.0.2", "text-mask-addons": "^3.8.0", "timers": "^0.1.1", "ts-helpers": "^1.1.1", "tslib": "^2.4.1", "uuid": "^9.0.0", "zone.js": "~0.15.0"}, "devDependencies": {"@angular-devkit/build-angular": "^19.2.9", "@angular/cli": "^19.2.9", "@angular/compiler-cli": "^19.2.8", "@angular/language-service": "^19.2.8", "@fortawesome/fontawesome-free": "^5.12.0", "@types/jasmine": "^2.8.16", "@types/jasminewd2": "^2.0.8", "@types/node": "^12.11.1", "codelyzer": "^6.0.1", "jasmine-core": "~3.5.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.3.2", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "protractor": "~7.0.0", "sass": "^1.54.4", "ts-node": "~4.1.0", "tslint": "~6.1.0", "typescript": "~5.8.3", "underscore": "^1.9.2", "webpack": "^4.44.1"}, "overrides": {"ng-recaptcha": {"@angular/core": "^19.0.0", "@angular/common": "^19.0.0", "@angular/forms": "^19.0.0"}}}