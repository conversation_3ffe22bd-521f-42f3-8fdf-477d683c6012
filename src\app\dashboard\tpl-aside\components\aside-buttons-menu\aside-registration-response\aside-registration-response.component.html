<div *ngIf="!proceedClicked; then RmvResults else emailSent"></div>
<ng-template #RmvResults>
  <a style="float: right;color: #1989C9; font-size:12px;" (click)="agencyChangeEventHandler()" *ngIf="fromEstamp">Not
    your customer? Click here to send to a different agency</a>

  <h1 class="o-heading o-heading--red">RMV Results </h1>

  <br>
  <p-accordion [multiple]="true">
    <p-accordionTab header="The RMV returned the following information for the data you entered:" [selected]="true"
      *ngIf="data">
      <div class="box box--silver">
        <div *ngFor="let msg of data.messages" style="padding-bottom:.4rem">
          <p *ngIf="data.transaction.success && msg.name!='Fees' && msg.name != 'Payment Method Fees'">{{msg.name}}: {{msg.value}}</p>
          <p *ngIf="!data.transaction.success && msg.name === 'Failure' && !isMultipleRecords()">{{msg.value}}</p>
        </div>

        <p *ngIf="isMultipleRecords()">The plate number you’ve provided appears to apply to multiple different plate
          types within Atlas. Please provide the specific Plate Type for this transaction</p>

        <div class="row"
          *ngIf="this.isFeatureEnabled('AllowAgentFee') && this.data.transaction.eligible && this.AgentFeeOptions && this.AgentFeeOptions.length>0">
          <div class="col-md-4"><label for="">Agent Service Fee:</label></div>
          <div class="col-md-6">
            <sm-autocomplete #refFee [activeOption]="'AgentFee'" [options]="this.AgentFeeOptions" [required]="true"
              [searchFromBegining]="true" [(ngModel)]="AgentFee">
            </sm-autocomplete>
          </div>
        </div>
      </div>
    </p-accordionTab>
    <p-accordionTab header="Notifications:" [selected]="true" *ngIf="data && data.notifications.length > 0">
      <div *ngFor="let notif of data.notifications">
        <p class="box box--silver">
          {{notif.value}}
        </p>
      </div>
    </p-accordionTab>
    @if(isAgencyBill) {
    <p-accordionTab [selected]="true" header="Payment Method">
      <div class="box box--silver">
        <form #paymentMethodForm>
        <div  >
          <div>
            <label class="o-checkable" [appDetectSystem] [removeRadio]="true" [ngClass]="{'is-required-field':  useAgencyBill === undefined }">
              <input type="radio" name="payOnline" [(ngModel)]="useAgencyBill" [value]="false" required />

              <i class="o-btn o-btn--radio"></i>
              <span style="margin-right: 10px"><b>Online Payment Request - {{data.paymentDefinition.payment?.total | currency}} </b></span>
            </label>
            <span style="color:darkred;">- SinglePoint sends email to insured who pays with Credit or Debit</span>
            <br>
            <span style="margin-left: 50px;"> {{data.paymentDefinition.payment?.rmvFee | currency}} RMV Fees,  {{this.bscCreditCardFee | currency}} Service Charge, {{this.creditCardFee| currency}} Payment Processing Fee</span>

          </div>

          <div style="margin-top: 10px;">
            <label class="o-checkable" [appDetectSystem] [removeRadio]="true" [ngClass]="{'is-required-field':  useAgencyBill === undefined }">
              <input type="radio" name="agencyBill" [(ngModel)]="useAgencyBill" [value]="true" required />

              <i class="o-btn o-btn--radio"></i>
              <span style="margin-right: 10px"><b>Agency Bill - {{data.paymentDefinition.payment?.agencyBillTotal | currency}}</b></span>
            </label>
            <span style="color:darkred;">- You collect payment directly from insured</span>
            <br>
            <span style="margin-left: 50px;"> {{data.paymentDefinition.payment?.rmvFee | currency}} RMV Fees,  {{this.bscAgencyBillFee | currency}} Service Charge</span>

          </div>


        </div>
        </form>
      </div>
    </p-accordionTab>
  }
  @if(!isAgencyBill) {
    <p-accordionTab [selected]="true" header="Payment Method">
      <div class="box box--silver">
        <form #paymentMethodForm>
        <div>
          <div>


              <span style="margin-right: 10px">Online Payment Request - {{data.paymentDefinition.payment?.total | currency}} - SinglePoint sends email to insured who pays with Credit or Debit</span>
           <br>
            <span style="margin-left: 50px;"> {{data.paymentDefinition.payment?.rmvFee | currency}} RMV Fees,  {{this.bscCreditCardFee | currency}} Service Charge, {{this.creditCardFee| currency}} Payment Processing Fee</span>

          </div>

        </div>
        </form>
      </div>
    </p-accordionTab>
  }


    <p-accordionTab *ngIf="data.requestAdditionalData.length > 0 && this.data.transaction.eligible"
      [header]="getHeader()" [selected]="true">
      <div class="box box--silver">
        <div *ngFor="let item of data.requestAdditionalData">
          <div class="row">
            <div class="col-md-4"><label for="">{{item.value}}:</label></div>
            <div class="col-md-8" [ngSwitch]="item.name">
              <span *ngSwitchCase="'ownerEmail'" [ngClass]="{'is-required-field': !validEmail()}">
                <input name="ownerEmail" type="email" #email="ngModel" [(ngModel)]="ownerEmail" required>
                <span *ngIf="email.dirty && !validEmail()" class="is-required-field">Please enter a valid email
                  address</span>
              </span>
              <span *ngSwitchCase="'ownerFid'">
                <input name="ownerFid" [(ngModel)]="ownerFid" required>
              </span>
              <span *ngSwitchCase="'ownerDob'">
                <app-datepicker-input #refPickerDateOfBirth [placeholder]="'MM/dd/yyyy'" [(ngModel)]="ownerDob"
                  name="dateOfBirth" ngDefaultControl [selectDate]="ownerDob" (onDateChange)="setDate($event)">
                </app-datepicker-input>
              </span>
            </div>
          </div>
        </div>
        <div>
          <div class="row">
            <div class="col-md-4"><label for="">Writing Company:</label></div>
            <div class="col-md-8">
              <sm-autocomplete #refProvider placeholder="Carrier" [activeOption]="writingCompany" [options]="providers"
                [required]="true" [searchFromBegining]="true" [(ngModel)]="writingCompany" changeDetectionDelay
                (keydown)="$event.stopPropagation()">
              </sm-autocomplete>
            </div>
          </div>
          <div class="row" *ngIf="writingCompany === '000'">
            <div class="col-md-4"><label for="">Assigned Carrier:</label></div>
            <div class="col-md-8">
              <sm-autocomplete #refProvider placeholder="Carrier" [activeOption]="assignedCarrier"
                [options]="arcOptions | orderBy: ['text']" [required]="writingCompany === '000'"
                [searchFromBegining]="true" [(ngModel)]="assignedCarrier" changeDetectionDelay
                (onSelect)="ARCconfirm.open()">
              </sm-autocomplete>
            </div>

          </div>
          <div class="row" *ngIf="transaction !=='RegistrationRenewal' && transaction !== 'DuplicateRegistration'"
            [ngClass]="{'is-required-field':  effectiveDate > maxDate || effectiveDate < minDate}">
            <div class="col-md-4"><label for="">Effective Date of Change:</label></div>
            <div class="col-md-8">
              <app-datepicker-input #refPickerEffectiveDate ngDefaultControl [(ngModel)]="effectiveDate"
                [dateRangeStart]="minDate" [dateRangeEnd]="maxDate" [maxDate]="maxDate" [minDate]="minDate"
                name="effectiveDate" [required]="true" [selectDate]="effectiveDate" [placeholder]="'MM/dd/yyyy'"
                [returnDateFormat]="'yyyy-MM-dd'" (onDateChange)="setEffectiveDate($event)">
              </app-datepicker-input>
              <span *ngIf="dateError">{{dateError}}</span>
            </div>
          </div>
        </div>
      </div>


    </p-accordionTab>
    <p-accordionTab header="Please Select Plate Type" [selected]="true" *ngIf="isMultipleRecords() || isNoRecords()">
      <sm-autocomplete style="width: 100px" name="plateType" [searchFromBegining]="false" [allowSearchById]="true"
        [(ngModel)]="plateType" [allowEmptyValue]="true" placeholder="Plate Type" [activeOption]="plateType"
        [options]="plateTypeOptions">
      </sm-autocomplete>
      <div class="row">
        <div class="col-md-3" style="margin-top:10px">
          <a href="https://bostonsoftware.com/resource-center/plate-type-conversion-list/" target="_blank">Plate Type
            Help</a>
        </div>
      </div>
    </p-accordionTab>

  </p-accordion>


  <div class="row u-spacing--1-5">
    <div class="col-xs-12 u-align-right u-remove-letter-spacing" style="padding-top: 2rem;">

      <button *ngIf="data?.transaction.eligible && data?.transaction.success && transaction !== 'DuplicateRegistration'"
        type="button" class="o-btn o-btn--idle u-spacing--left-2" (click)="skipToForm()">Go To RTA</button>
      <button
        *ngIf="!data?.transaction.eligible && !data?.transaction.success && !isMultipleRecords() && !isNoRecords()"
        type="button" class="o-btn" (click)="redirectToServices()">Go To RMV Services</button>
      <button (click)="registrationValidation()" class="o-btn"
        *ngIf="!data?.transaction.eligible && !data?.transaction.success && isMultipleRecords() || isNoRecords()">Retry</button>
      <button
        *ngIf="!data?.transaction.eligible && data?.transaction.success && transaction !== 'DuplicateRegistration'"
        type="button" class="o-btn" (click)="skipToForm()">Go To RTA</button>
      <button type="button" (click)="onCancel()" class="o-btn o-btn--idle u-spacing--left-2"
        style="margin-left:1.8rem;">Go Back</button>
      @if(useAgencyBill) {
      <button *ngIf="data?.transaction.eligible && data?.transaction.success" type="button"
        class="o-btn u-spacing--left-2" [disabled]="validateForm() || validateDob() || !validEmail() || dateError !== '' || validateAgencyBill()"
        (click)="agencyBillConfirm.isOpen = true">PROCEED</button>
      }@else {
      <button *ngIf="data?.transaction.eligible && data?.transaction.success" type="button"
        class="o-btn u-spacing--left-2" [disabled]="validateDob() || validateForm() || !validEmail() || dateError !== '' || validateAgencyBill()"
        (click)="proceed(useAgencyBill)">PROCEED</button>
      }

    </div>
  </div>
  <app-modalbox #ARCconfirm [css]="'u-width-440px'">
    <h1 class="o-heading o-heading--red">Confirm the following</h1>

    <div class="box box--silver margin-bottom">
      <div class="padding-bottom">
        <ul>
          <li>This is not a voluntary policy.</li>

          <li>
            This policy had been assigned to {{getCompanyName()}} via MAIP.
          </li>
        </ul>
      </div>
    </div>
    <div class="row">
      <div class="col-md-12 u-align-right u-remove-letter-spacing" style="padding-top: 10px;">
        <button type="button" (click)="acceptARC();ARCconfirm.close()" class="o-btn">Ok</button>
        <button type="button" class="o-btn o-btn--idle u-spacing--left-2 margin-left-rem;"
          (click)="undoARC();ARCconfirm.close()">Cancel</button>

      </div>
    </div>

  </app-modalbox>
</ng-template>
<ng-template #emailSent>

  @if(failureMessage && failureMessage.name === 'Failure') {
  <div class="box box--silver u-spacing--1-5">
    <p style="white-space: pre-wrap;">{{failureMessage.value}}</p>
  </div>
  <div class="row u-spacing--1-5">
    <div class="col-xs-12 u-align-right u-remove-letter-spacing">
      <button (click)="registrationValidation()" class="o-btn" *ngIf="ownerRetry">Retry</button>
      <button type="button" class="o-btn" [ngClass]="{'o-btn--idle u-spacing--left-2':ownerRetry}"
        (click)="onCancel()">CLOSE</button>
    </div>
  </div>
  }@else if (!useAgencyBill) {
      <h1 class="o-heading o-heading--red">PAYMENT REQUEST</h1>
      <br>
      @if(proceedResponse && proceedResponse.messages){
      <div class="box box--silver u-spacing--1-5">
        <p style="white-space: pre-wrap;">{{proceedResponse.messages[0]}}</p>
        <hr>
        <p style="white-space: pre-wrap;padding-bottom: 10px;" [innerHTML]="proceedResponse.messages[1]"></p>
        <button class="o-btn o-btn--idle" [cdkCopyToClipboard]="proceedResponse.paymentUrl">Copy link to Clipboard</button>
        <button class="o-btn u-remove-letter-spacing" style="left: 25rem;" (click)="onCancel()">Close</button>
      </div>
      }

  }@else {
  <div class="box box--silver">
    <p>
      @switch(transaction) {
      @case('RegistrationRenewal') {
      <div [innerHTML]="renewMessage"></div>
      }
      @case('RegistrationReinstatement') {
      The registration has been successfully reinstated. Click the button below to download the receipt from the RMV.
      }
      @case('DuplicateRegistration') {
      Click the button below to download the Certificate of Registration and receipt from the RMV.
      }
      }
    </p>
        <button class="o-btn" style="margin: 20px;" (click)="downloadDocuments()">Download Documents</button>

        @if (transaction === 'RegistrationRenewal' && isEvrFull) {
          <button type="button" style="margin: 20px;" (click)="goToInventory()" class="o-btn">Order More</button>
        }

  </div>
  }
</ng-template>

<app-confirmbox #agencyBillConfirm [css]="'width-430px'" [question]="getConfirmMessage()" [confirmBtnText]="'Ok'"
  (onAccept)="proceed(useAgencyBill)" (onCancel)="confirmCanceled($event)" [templateVersion]="3">

</app-confirmbox>


