import { Collection } from 'app/app-model/_common';
import { ApiResponse, MetaData } from './_common';

export class Dwelling {
  constructor(
    public meta = { href: <string>'' },
    public constructionMaterialTypeCode: string = '',
    public constructionYear: number = 0,
    public constructionAge: number = 0,
    public livingSpaceArea: number = 0,
    public utilizationTypeCode: string = '',
    public familiesCount: number = null,
    public storiesCount: string = 'One',
    public designStyleTownhouseInd: boolean = false,
    public townhouseUnitsCount: number = 0,
    public poolOnPremisesInd: boolean = null,
    public mortgageeCount: number = null,
    public dwellingLocation = { href: <string>'' },
    public protectionClass: any = '',
    public protectionDevices: any = '',
    public subsystems: any = '',
    public quoteSessionId: string = '',
    public resourceId: string = '',
    public parentId: string = '',
    public resourceName: string = 'Dwelling',
    public protectionClassOverrides?: MetaData, // = new MetaData(),
    public coverages?, // Is it still in the model ?
    public vacancyInd?: boolean,
    public nonOwnerOccupancyInd?: boolean,
    public underConstructionInd?: boolean,
    public mobileHomeInd?: boolean
  ) {}
}

export class DwellingsListAPIResponse extends ApiResponse<Dwelling> { }

export class Town {
  constructor(
    public state: string = '',
    public lob: string = 'HOME',
    public id: string = '',
    public townName: string = '',
    public townCode: string = '',
    public countyName: string = '',
    public countyId: string = ''
   ) {}
}

export class FireDistrict {
  constructor(
    public state: string = '',
    public lob: string = 'HOME',
    public fireDistrictId: string = '',
    public fireDistrictName: string = ''
   ) {}
}
export class DwellingProtectionClass {
  constructor(
    public meta = {
      href: <string>'',
      rel: [
        'ProtectionClass'
      ]
    },
    public town: Town = new Town(),
    public fireDistrictsTown: Town = new Town(),
    public distanceToFireStation: string = 'LessThan5Miles',
    public distanceToFireHydrant: string = 'LessThan1000Feet',
    public distanceToBodyWater: string = 'Over20Miles',
    public suburbanRatingOption: string = 'Auto',
    public isoProtectionClass: any = null,
    public parentId: string = '',
    public quoteSessionId: string = '',
    public resourceId: string = '',
    public resourceName: string = 'ProtectionClass'
  ) {}
}


export class DwellingProtectionClassApiResponse extends ApiResponse<DwellingProtectionClass> {
  constructor (...args) {
    super();
    this.meta.rel = args[3] && args[3].rel ? args[3].rel : ['ProtectionClassCollection'];
  }
}

export class DwellingProtectionClassItem {
  constructor (
    public protectionClassCode: string = '',
    public ratingPlanId: string = '',
    public ratingPlanName: string = ''
  ) {}
}

export class DwellingProtectionClassOverridesApiResponse extends ApiResponse<DwellingProtectionClassOverrides> {}

export class DwellingProtectionClassOverrides extends Collection<DwellingProtectionClassItem> {
  public meta: MetaData = {
    href: '',
    rel: ['ProtectionClassOverrides']
  };
}

export class DwellingLocation {
  constructor(
    public meta = {
      href: <string>'',
      rel: <string[]>[]
    },
    public address1: string = '',
    public address2: string = '',
    public city: string = '',
    public state: string = '',
    public zip: string = '',
    public fireDistrict: string = '',
    public quoteSessionId: string = '',
    public resourceId: string = '',
    public parentId: string = '',
    public resourceName: string = 'ProtectionDevices'
  ) {}
}

export class DwellingProtectionDevices {
  constructor(
    public meta = {
      href: <string>'',
      rel: <string[]>[]
    },
    public burglarLocalInd: boolean = false,
    public burglarCentralStationInd: boolean = false,
    public burglarDirectInd: boolean = false,
    public fireLocalInd: boolean = false,
    public fireCentralStationInd: boolean = false,
    public fireDirectInd: boolean = false,
    public deadboltInd: boolean = false,
    public fireExtinguisherInd: boolean = false,
    public sprinklerAllAreaInd: boolean = false,
    public sprinklerExceptDetectorAreaInd: boolean = false,
    public guardedCommunityInd: boolean = false,
    public voiceDialerInd: boolean = false,
    public quoteSessionId: string = '',
    public resourceId: string = '',
    public parentId: string = '',
    public resourceName: string = 'ProtectionDevices'
  ) {}
}

export class DwellingsSubsystemsAPIResponse extends ApiResponse<DwellingSubsystems> { }

export class DwellingSubsystems {
  constructor(
    public electricalWiringTypeCode: string = '',
    public electricalLastUpdatedYear: number = null,
    public heatingPrimaryTypeCode: string = '',
    public heatingPrimaryLastUpdatedYear: number = null,
    public heatingSecondaryTypeCode: string = '',
    public plumbingMaterialTypeCode: string = '',
    public plumbingLastUpdatedYear: number = null,
    public roofMaterialTypeCode: string = '',
    public roofLastUpdatedYear: number = null,
    public oilStorageTankLocation: string = ''
  ) {}
}

export class ScheduledPropertyCollection extends Collection<ScheduledPropertyItem> {
  public showAgreedValueInd: boolean = null;
}

export class ScheduledPropertyApiResponse extends ApiResponse<ScheduledPropertyCollection> {}

export class ScheduledPropertyItem {
  constructor(
    public itemValueAmount: Number = 0,
    public riskTypeCode: string = '',
    public storageTypeCode: string = '',
    public summaryIndicator: boolean = true,
    public valuationTypeCode: string = '',
    public agreedValueSupplementCode: string = ''
  ) {}
}

export class LossHistoryRequirements {
  constructor(
    public meta: string = '',
    public ratingPlanId: string = '',
    public ratingPlanName: string = '',
    public requirementType: string = '',
    public requirementYears: string = '',
    public resourceName: string = ''
  ) {}
}

export class LossHistory extends Collection<LossHistoryItem> {
  public lossFreeYears = 'Unspecified';
  public lossesYear1 = '';
  public lossesYear2 = '';
  public lossesYear3 = '';
  public lossesYear4 = '';
  public lossesYear5 = '';
  public lossesYear? // Custom field for validation purposes
}

export class LossHistoryItem {
  constructor(
    public lossDate: string = '',
    public description: string = '',
    public descriptionParsed: string = '',
    public catastrophicLossInd: boolean = false,
    public lossAmount: number = null,
    public lossesYear: string = null
  ) {}
}
