
import {take, first} from 'rxjs/operators';
import { Client<PERSON>ddress, ClientContactMethod, ClientDetails } from 'app/app-model/client';
import { Component, EventEmitter, OnDestroy, OnInit, Output, ViewChild, Input } from '@angular/core';
import { Observable } from 'rxjs';

import { AgencyUserService, UserData } from 'app/shared/services/agency-user.service';
import { ClientsService, CLIENT_TYPES } from 'app/dashboard/app-services/clients.service';
import { Driver } from 'app/app-model/driver';
import { DriversService }  from 'app/dashboard/app-services/drivers.service';
import { FilterOption } from 'app/app-model/filter-option';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { Quote, QuoteAuto, QuotePolicyHistory, QuoteNewRMVData, QuotePlan } from 'app/app-model/quote';
import { QuotesService }  from 'app/dashboard/app-services/quotes.service';
import { RouteService } from 'app/shared/services/route.service';
import { Router } from '@angular/router';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { StorageService }  from 'app/shared/services/storage-new.service';
import { SubsService }  from 'app/dashboard/app-services/subs.service';
import { PlansSelectorComponent } from 'app/shared/components/plans-selector/plans-selector.component';
import { LeaveQuoteComponent } from 'app/shared/components/leave-quote/leave-quote.component';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { VehiclesService } from 'app/dashboard/app-services/vehicles.service';
import { CoveragesService } from 'app/dashboard/app-services/coverages.service';
import { Vehicle } from 'app/app-model/vehicle';
import { LeaveQuoteService } from 'app/shared/services/leave-quote.service';
import { IncidentTypeResponse } from 'app/app-model/incident';
import { addYears, format, isBefore, parseISO, startOfDay } from 'date-fns';


interface eventToEmitInterface {
  event: Event;
}

export interface IEventDataImportAutoQuote {
  effectiveDate: string;
  selectedPlans: QuotePlan[];
}

@Component({
    selector: 'app-new-auto-create-form',
    templateUrl: './new-auto-create-form.component.html',
    styleUrls: ['./new-auto-create-form.component.scss'],
    standalone: false
})
export class NewAutoCreateFormComponent implements OnInit, OnDestroy {
  // @Input() public effectiveDate: string = null;
  @Input()
  public get effectiveDate(): string {return this.modelEffectiveDate; }
  public set effectiveDate(val: string) {
    if (val) {
      this.modelEffectiveDate = val;
    } else {
      this.modelEffectiveDate = null;
    }
  }

  constructor(
    private router: Router,
    private agencyUserService: AgencyUserService,
    private subsService: SubsService,
    private quotesService: QuotesService,
    private driversService: DriversService,
    private storageService: StorageService,
    private clientsService: ClientsService,
    private overlayLoaderService: OverlayLoaderService,
    private specsService: SpecsService,
    private routeService: RouteService,
    private storageGlobalService: StorageGlobalService,
    private apiCommonService: ApiCommonService,
    private vehicleService: VehiclesService,
    private coveragesService: CoveragesService,
    private leaveQuoteService: LeaveQuoteService,
) { }

  public get showWarningInThePast(): boolean {
    return this.modelEffectiveDate && this.dateIsInThePast();
  }

  public modelEffectiveDate: string = new Date().toISOString();
  public selectedPlan: any; // QuotePlan
  public plansOnQuotes: FilterOption[] = [];
  public plansCount = 0;
  private subscription;
  private resetPlansSubscribtion;
  public invalidPlanQuoteField = false;
  private agencyInfo;

  private createdDriver: Driver;
  private createdClientDetails: ClientDetails;
  private createdClientContactMethod: ClientContactMethod;
  private createdClientAddress: ClientAddress;

  @Input() public useToImportQuote = false;

  @Output() public onCreateManuallyClick: EventEmitter<eventToEmitInterface> = new EventEmitter();
  @Output() public onCancelClick: EventEmitter<eventToEmitInterface> = new EventEmitter();
  @Output() public importQuoteClick: EventEmitter<IEventDataImportAutoQuote> = new EventEmitter();

  @ViewChild('leaveQuote') leaveQuote: LeaveQuoteComponent;
  @ViewChild('refPlansSelector', {static: true}) refPlansSelector: PlansSelectorComponent;

  private selectedPlansOnQuote = [];

  ngOnInit() {
    // this.modelEffectiveDate = new Date().toISOString(); // "2017-02-22T14:51:39+01:00";
    this.agencyUserService.userData$.subscribe(agent => {
    this.agencyInfo = agent;
    });

    this.processPlans();
  }

  ngOnDestroy() {
    this.subscription && this.subscription.unsubscribe();
    this.resetPlansSubscribtion && this.resetPlansSubscribtion.unsubscribe();
  }

  canDeactivate(): Observable<boolean> | boolean {
    this.leaveQuote.detectConfirmation();
    return this.leaveQuote.detectConfirmationObservable.asObservable().pipe(first());
  }

  private processPlans() {
    const plans = JSON.parse(JSON.stringify(this.storageGlobalService.takeSubs('plans')));
    const plansFilteredByLob = this.filterByLob(plans, 'AUTOP');

    if (plansFilteredByLob.length) {
      this.setPlansOnQuotes(plansFilteredByLob);
    } else {
      this.resetPlans();
    }
  }

  private filterByLob(plans, lob) {
    const filteredPlans = [];

    plans.forEach(plan => {
      if (plan.lob === lob) {
        filteredPlans.push(plan);
      }
    });

    return filteredPlans;
  }

  private resetPlans() {
    this.overlayLoaderService.showLoader('Loading Plans...');
    this.agencyUserService.userData$.subscribe(agent => {
      if (agent) {
        this.resetPlansSubscribtion = this.subsService.getRatingPlans(agent.agencyId).subscribe( response => {
          this.overlayLoaderService.hideLoader();

          this.setPlansOnQuotes(this.filterByLob(response.items, 'AUTOP'));
          this.refPlansSelector.init();
        });
      }
    });
  }

  private setPlansOnQuotes(plans) {
    this.plansOnQuotes = [];
    plans.forEach(item => {
      this.plansOnQuotes.push({text: item.name, id: item.ratingPlanId, data: item});
    });
    this.plansOnQuotes.sort((a, b) => {
      if (a.text < b.text) { return -1; } else if (a.text > b.text) { return 1; } else { return 0; }
    });

    this.plansCount = this.plansOnQuotes.length;

    if (this.plansCount) {
      this.selectedPlan = [];
      this.selectedPlansOnQuote = [];
      this.plansOnQuotes.forEach( (plan, index) => {
        this.selectedPlan.push(plan);
        this.selectedPlansOnQuote.push(plan.data);
      });
    }
  }
  public refreshValue($event) {
    this.selectedPlansOnQuote = [];
    for (const option of $event.selectedOption) {
      this.selectedPlansOnQuote.push(option.data);
    }

    this.selectedPlan = $event.selectedOption;
    this.invalidPlanQuoteField = false;
  }

public onDateChange($ev: { date: string | Date | null }): void {
  try {
    if ($ev && $ev.date) {
      // Handle both string and Date objects
      const dateValue = typeof $ev.date === 'string' ? $ev.date : $ev.date.toISOString();
      this.modelEffectiveDate = format(parseISO(dateValue), 'yyyy-MM-dd');
    } else {
      this.modelEffectiveDate = null;
    }
  } catch (error) {
    console.error('Error formatting date:', error);
    this.modelEffectiveDate = null;
  }
}

  public createQuoteManually() {
    if (!this.selectedPlan) {
      this.invalidPlanQuoteField = true;
      return;
    }

    this.invalidPlanQuoteField = false;

    let letCreateQuote = true;
    if (this.routeService.isImported) {
      letCreateQuote = window.confirm(this.routeService.text);
    }

    if (letCreateQuote) {
      this.quotesService.setIsImportedQuote(false);
      this.quotesService.setIsMaipArcQuote(false);
      let quoteData;
      if (this.selectedPlansOnQuote.length) {
        quoteData = {
          lob: this.selectedPlansOnQuote[0].lob,
          state: this.selectedPlansOnQuote[0].state
        };
      }

      this.overlayLoaderService.showLoader();
      this.quotesService.createNewQuote({
        lob: quoteData.lob,
        state: quoteData.state,
        effectiveDate: this.modelEffectiveDate,
        expirationDate: format(addYears(parseISO(this.modelEffectiveDate), 1), 'yyyy-MM-dd'),
        policyPeriod: 12
      }).subscribe(
        data => {
          if (data) {

            let srcQuoteSessionId = null;
                  this.storageService
                    .getStorageData('srcQuoteSessionId')
                    .subscribe(sessionId => {
                      if (sessionId) {
                        srcQuoteSessionId = sessionId;
                      }
                    });

            // if new quote created manually
            this.storageService.clearStorage();
            this.storageService.clearQuoteStoredData();

            this.storageService.setStorageData('isNewQuote', true);

            let quote = data;
            let subsSubscriptionStates;
            let subsSubscriptionPlans;
            let subsSubscriptionLobs;

            // Set Quote Info
            quote = this.setQuoteInfo(quote);

            this.storageService.setStorageData('selectedQuote', quote);
            subsSubscriptionStates = this.subsService.getRatingStates(this.agencyInfo.agencyId, quoteData.lob).subscribe(() => subsSubscriptionStates.unsubscribe());
            subsSubscriptionLobs = this.subsService.getRatingLobs(this.agencyInfo.agencyId).subscribe(() => subsSubscriptionLobs.unsubscribe());

            const quotePlanList = {
              items: this.selectedPlansOnQuote
            };
            if (!this.selectedPlansOnQuote.length) {
              quotePlanList.items = [this.selectedPlan.data];
            }

            this.quotesService.updateQuoteByUrl(quote.quotePlanList.href, quotePlanList).pipe(take(1)).subscribe( updatedQuote => {
              // Reset data
              this.createdDriver = null;
              this.createdClientDetails = null;
              this.createdClientContactMethod = null;
              this.createdClientAddress = null;

              // Create Driver and Client for the Quote
              this.createDriver(quote.resourceId)
                .then(driver => {
                  this.createdDriver = driver;
                  this.storageService.setStorageData('driversList',  [driver]); // manually cast to array - only 1 new driver
                  return driver;
                })
                .then(driver => {
                  this.driversService.setDriverOptions(driver, {}).subscribe();
                  return driver;
                })
                .then(driver => {
                  return this.getAutoIncidentsTypesAndDescs();
                })
                .then(() => {
                  return this.createClient(quote.resourceId);
                })
                .then(clientDetails => {
                  this.createdClientDetails = clientDetails;
                  return this.createClientData(clientDetails);
                })
                .then(() => this.getQuoteClients(quote.resourceId))
                .then(() => this.getPolicyHistoryOrCreateIfNotExists(quote)) // TEST
                .then(() => this.createNewVehicle(quote.resourceId))
                .then(() => {
                  subsSubscriptionStates && subsSubscriptionStates.unsubscribe();
                  subsSubscriptionPlans && subsSubscriptionPlans.unsubscribe();
                  subsSubscriptionLobs && subsSubscriptionLobs.unsubscribe();
                  // Assign created client ID to Quote
                  quote.client.resourceId = this.createdClientDetails.resourceId;
                  this.storageService.setStorageData('selectedQuote',  quote);

                  // 05.30.2018 - SA: New quote created from dashboard will not have a srcQuoteSessionId, hence check if value is not null or empty.
                  if (srcQuoteSessionId) {
                    this.quotesService.copyQuoteInfo(quote.quoteSessionId, {quotesessionid: srcQuoteSessionId}).pipe(first()).subscribe(res => {
                      if (res) {
                        this.routeService.showSaveConfirmation = false;
                        this.leaveQuote.showSaveConfirmation = false;
                        this.leaveQuote.forceConfirmation = false;
                        // this.router.navigateByUrl('/dashboard/quotes/' + quote.quoteSessionId);
                        this.router.navigate(
                          ['/dashboard/quotes/', quote.quoteSessionId],
                          { queryParams: {newQuote: true}}
                        );
                      } else {
                        this.router.navigateByUrl('/dashboard/auto' + this.createdDriver.meta.href);
                      }

                    });
                  } else {
                    this.router.navigateByUrl('/dashboard/auto' + this.createdDriver.meta.href);
                  }

                  this.leaveQuoteService.saveStorageQuoteDataSnapshot();
                  this.overlayLoaderService.hideLoader();
                })
                .catch(err => {
                  console.log('CREATE QUOTE ERR: ', err);
                  this.overlayLoaderService.hideLoader();
                });
            });
          }
        },
        error => {
          console.log(error);
        });
    }
  }

  private createDriver(quoteId: string): Promise<Driver> {
    return new Promise((resolve, reject) => {
      this.driversService.createDriver(quoteId).pipe(
        take(1))
        .subscribe(
          (driver: Driver) => resolve(driver),
          err => reject(err)
        );
    });
  }

  private getAutoIncidentsTypesAndDescs(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.specsService.getIncidentsSpecsTypes()
        .subscribe(
          (res: IncidentTypeResponse) => {
            this.storageService.setStorageData('autoDriverIncidentsTypesAndDesc', res);
            resolve();
          },
          err => reject(err)
        );
    });
  }

  private createClient(quoteID: string): Promise<ClientDetails> {
    return new Promise((resolve, reject) => {
      this.clientsService.createQuoteClient(quoteID).pipe(
        take(1))
        .subscribe(
          (clientDetails: ClientDetails) => resolve(clientDetails),
          err => reject(err)
        );
    });
  }

  private createClientData(client: ClientDetails): Promise<any> {
    const promiseAddresses = this.clientsService.getClientAddressesAndCreateNotExisting(client)
      .then((res: ClientAddress[]) => {
        this.storageService.setStorageData('clientAddresses', JSON.parse(JSON.stringify(res)));
      })
      .catch(err => console.log(err));

    const promiseContactMethods = this.clientsService.getClientContactMethodsAndCreateNotExisting(client)
      .then((res: ClientContactMethod[]) => {
        this.storageService.setStorageData('clientContactMethods', JSON.parse(JSON.stringify(res)));
      })
      .catch(err => console.log(err));

    return Promise.all([promiseAddresses, promiseContactMethods]);
  }


  private getQuoteClients(quoteId: string): Promise<any> {
    return this.clientsService.getClientsList(quoteId)
      .toPromise()
      .then(res => {
        if (res.items[0] && !res.items[0].type) {
          res.items[0].type = CLIENT_TYPES.personal;
        }
        this.storageService.setStorageData('selectedClient', res.items[0]);
        this.storageService.setStorageData('clients', res.items);
      })
      .catch(err => console.log(err => console.log('CLIENTS ERR:', err)));
  }

  private updateClientDetails(clientDetailsData: ClientDetails): Promise<ClientDetails> {
    return this.apiCommonService.putByUri(clientDetailsData.meta.href, clientDetailsData)
      .toPromise()
      .then(res => res)
      .catch(err => console.log(err));
  }

  private setQuoteInfo(quote: Quote): Quote {
    let userData: UserData;

    this.agencyUserService.userData$.pipe(
      first())
      .subscribe(data => userData = data);

    // Quote agency contact
    if (!quote.agent && userData) {
      quote.agent = userData.user.userId;
    }

    return quote;
  }

  // -----CREATE VEHICLE
  private createNewVehicle(quoteId: string): Promise<Vehicle> {
    return new Promise((resolve, reject) => {
      this.vehicleService.createVehicle(quoteId)
      .subscribe(
        res => {
          this.coveragesService.createAutoStandardAndAdditionalCoveragesForVehicle(res)
            .then(data => {
              resolve(res);
            })
            .catch(err => {
              reject(err);
            });
        },
        err => reject(err)
      );
    });
  }

  // TODO:: To remove
  private getDefaultVehicleCoverages(): Promise<any> {
    let defaultCoverageValues;
    // let defaultCoverageValues = this.storageGlobalService.takeSubs('defaultStandardCoverages');
    // console.log(this.defaultCoverageValues)
    if (defaultCoverageValues && defaultCoverageValues.Coverages && defaultCoverageValues.Coverages.length) {
      return Promise.resolve(defaultCoverageValues);
    } else {
      let agencyId;
      let lobs;
      const storedLobs = this.storageGlobalService.takeSubs('lobs');
      if (storedLobs.length) {
        lobs = storedLobs[0].code;
      }
      const agencyUserSubscription = this.agencyUserService.userData$.subscribe(agent => {
        agencyId = agent.agencyId;
        agencyUserSubscription && agencyUserSubscription.unsubscribe();
      });

      if (defaultCoverageValues && defaultCoverageValues.Coverages && defaultCoverageValues.Coverages.length) {
        return Promise.resolve(defaultCoverageValues);
      } else {
        return this.apiCommonService.getByUri('/subs/' + agencyId + '/quoteCoverageLevels/lobs/' + lobs + '/levels?expand=').toPromise().then( res => {
          if (res && res.items && res.items.length) {
            defaultCoverageValues = res.items.filter( item => {
              return item.name === 'Default';
            });
            defaultCoverageValues = defaultCoverageValues[0];
            defaultCoverageValues = defaultCoverageValues.items.map( item => {
              return {
                coverageCode: item.coverageCode,
                values: [{
                  value: item.value
                }]
              };
            });
            defaultCoverageValues = {
              Coverages: defaultCoverageValues
            };
            // this.storageGlobalService.setSubs('defaultStandardCoverages', defaultCoverageValues);
          }
        }).then( () => {
          // console.log(defaultCoverageValues);
          return Promise.resolve(defaultCoverageValues);
        });
      }
    }
  }
  // -----CREATE VEHICLE


  public handleRmvClick($ev): void {
    const data: QuoteNewRMVData = {
      effectiveDate: this.modelEffectiveDate,
      plans: this.selectedPlan
    };
    this.storageService.setStorageData('newRmvQuoteData', data);
    this.storageService.setStorageData('newRmvDriverRmvToSend', []);
    this.router.navigate(['/dashboard/auto/new-rmv-quote']);
    this.onCancelClick.emit({
      event: $ev
    });
  }

  public handleCreateManuallyClick($ev): void {
    this.leaveQuote.forceConfirmation = true;
    this.leaveQuote.confirm().then( answer => {
      if (answer) {
        this.redirectToCreateQuoteView()
          .then(() => {
            this.createQuoteManually();
          });

        if (this.invalidPlanQuoteField) {
          return;
        }

        this.onCreateManuallyClick.emit({
          event: $ev
        });
      }
    });
  }

  public handleCancelClick($ev): void {
    this.onCancelClick.emit({
      event: $ev
    });
  }

  private redirectToCreateQuoteView(): Promise<any> {
    return this.router.navigateByUrl('/dashboard/quotes/new');
  }

  private getPolicyHistoryOrCreateIfNotExists(quote: QuoteAuto): Promise<QuotePolicyHistory|boolean> {
    return new Promise((resolve, reject) => {
      if (quote && quote.policyHistory && quote.policyHistory.href) {
        this.quotesService.policyHistoryGetListByUri(quote.policyHistory.href).pipe(
          take(1))
          .subscribe(
            policies => {
              if (policies && policies.items && policies.items.length) {
                const policyHistoryData = this.formatPolicyHistory(policies.items[0]);
                this.storageService.setStorageData('policyHistory', policyHistoryData);
                resolve(policyHistoryData);
              } else {
                // Create policy history
                this.quotesService.policyHistoryCreateByUri(quote.policyHistory.href, {}).pipe(
                  take(1))
                  .subscribe(data => {
                    const policyHistoryData = this.formatPolicyHistory(data);
                    this.storageService.setStorageData('policyHistory', policyHistoryData);
                    resolve(policyHistoryData);
                  });
              }
            },
            err => reject(err)
          );
      } else {
        console.log('Quote policyHistory.href missing');
        resolve(false);
      }
    });
  }

  // Import Quote
  // ---------------------------------------------------------------------------
  public handleImportQuote(): void {
    // this.leaveQuote.forceConfirmation = true;
    if (this.invalidPlanQuoteField) {
      return;
    }

    this.importQuoteClick.next({
      effectiveDate: this.modelEffectiveDate,
      selectedPlans: this.selectedPlansOnQuote,
    });
  }

  // Helpers
  // ----------------------------------------------------------------------------
  private formatPolicyHistory(data): QuotePolicyHistory {
    const formattedData = new QuotePolicyHistory;

    for (const prop in data) {
      if (data[prop] === 'Unknown') {
        data[prop] = null;
      }
      formattedData[prop] = data[prop];
    }

    return formattedData;
  }

  private dateIsInThePast(): boolean {
    return this.modelEffectiveDate && isBefore(startOfDay(parseISO(this.modelEffectiveDate)), startOfDay(new Date()));
  }

}
