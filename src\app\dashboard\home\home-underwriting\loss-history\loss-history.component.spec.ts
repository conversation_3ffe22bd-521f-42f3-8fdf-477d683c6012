import { expect, selectCheckbox, changeTextInputValue } from 'testing/helpers/all';
import { async, ComponentFixture, fakeAsync, inject, TestBed, tick } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';

import { MockActivatedRouteProvider } from 'testing/stubs/activated-route.provider';
import { data as LOSS_HISTORY_DATA } from 'testing/data/quotes/loss-history';
import { HOME_QUOTE } from 'testing/data/quotes/quote-home';
import { HOME_QUOTE_PLAN_LIST } from 'testing/data/quotes/quote-plan-list/home';
import { data as LOSS_HISTORY_REQUIREMENTS_DATA } from 'testing/data/specs/loss-history-requirements';

import { StubAutocompleteComponent } from 'testing/stubs/components/autocomplete.component';
import { StubConfirmboxComponent } from 'testing/stubs/components/confirmbox.component';
import { StubDatepickerInputComponent } from 'testing/stubs/components/datepicker-input.component';
import { StubLeaveQuoteComponent } from 'testing/stubs/components/leave-quote.component';
import { StubModalboxComponent } from 'testing/stubs/components/modalbox.component';
import { StubDwellingServiceProvider } from 'testing/stubs/services/dwelling.service.provider';
import { StubDwellingService } from 'testing/stubs/services/dwelling.service.provider';
import { StubMoneyServiceProvider } from 'testing/stubs/services/money.service.provider';
import { StubOverlayLoaderServiceProvider } from 'testing/stubs/services/overlay-loader.service.provider';
import { StubSpecsService, StubSpecsServiceProvider } from 'testing/stubs/services/specs.service.provider';

import { TextMaskModule } from 'angular2-text-mask/dist/angular2TextMask';
import { DwellingService } from 'app/dashboard/app-services/dwelling.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Helpers } from 'app/utils/helpers';
import { Observable } from 'rxjs';

import { LossHistoryComponent } from './loss-history.component';
import { ToNumberPipe } from 'app/shared/pipes/convert-to-number.pipe';

describe('Component: LossHistory', () => {
  let component: LossHistoryComponent;
  let fixture: ComponentFixture<LossHistoryComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [ TextMaskModule, FormsModule ],
      declarations: [
        LossHistoryComponent,
        StubAutocompleteComponent,
        StubModalboxComponent,
        StubLeaveQuoteComponent,
        StubConfirmboxComponent,
        StubDatepickerInputComponent,
        ToNumberPipe
      ],
      providers: [
        StorageService,
        StubDwellingServiceProvider,
        StubSpecsServiceProvider,
        StubOverlayLoaderServiceProvider,
        StubMoneyServiceProvider,
        MockActivatedRouteProvider
      ]
    })
      .compileComponents();

    jasmine.clock().mockDate(new Date(2017, 10, 27));
  }));

  describe('when all data is available in storage', () => {
    beforeEach(fakeAsync(inject(
      [StorageService],
      (storageService: StorageService) => {
        storageService.setStorageData('selectedPlan', Helpers.deepClone(HOME_QUOTE_PLAN_LIST));
        storageService.setStorageData('selectedQuote', Helpers.deepClone(HOME_QUOTE));
        // storageService.setStorageData('dwellingLossHistoryRequirements', [Helpers.deepClone(LOSS_HISTORY_REQUIREMENTS_DATA.items), null]);
        storageService.setStorageData('dwellingLossHistory', Helpers.deepClone(LOSS_HISTORY_DATA.items[0]));

        fixture = TestBed.createComponent(LossHistoryComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
        tick();
      })));

    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should destroy without errors', () => {
      expect(() => {
        fixture.destroy();
      }).not.toThrow();
    });

    it('should react to loss free years update', fakeAsync(() => {
      const select = <StubAutocompleteComponent>fixture.debugElement.query(By.css('#total-number-loss-free-years')).componentInstance;

      select.activeOption = component.totalNumberOfLossFreeYears[3];
      select.onSelect.emit({
        id: component.totalNumberOfLossFreeYears[3].id,
        text: component.totalNumberOfLossFreeYears[3].text,
        selectedOption: component.totalNumberOfLossFreeYears[3]
      });
      component.selectedTotalNumberOfLossFreeYears = component.totalNumberOfLossFreeYears[3];

      fixture.detectChanges();
      tick();

      fixture.detectChanges();
      tick();

      expect(component.rowsQty.length).toEqual(3);
    }));

    it('should react to losses amount in a year update', fakeAsync(() => {
      const select = <StubAutocompleteComponent>fixture.debugElement.query(By.css('#total-number-loss-free-years')).componentInstance;

      select.activeOption = component.totalNumberOfLossFreeYears[3];  // 2 years
      select.onSelect.emit({
        id: component.totalNumberOfLossFreeYears[3].id,
        text: component.totalNumberOfLossFreeYears[3].text,
        selectedOption: component.totalNumberOfLossFreeYears[3]
      });
      component.selectedTotalNumberOfLossFreeYears = component.totalNumberOfLossFreeYears[3];

      fixture.detectChanges();
      tick();

      fixture.detectChanges();
      tick();

      const amountSelect = <StubAutocompleteComponent>fixture.debugElement.query(By.css('#lossesYear3')).componentInstance;
      amountSelect.activeOption = component.lossesOptions[1];
      amountSelect.onSelect.emit({
        id: component.lossesOptions[1].id,
        text: component.lossesOptions[1].text,
        selectedOption: component.lossesOptions[1]
      });

      fixture.detectChanges();
      tick();

      expect(component.lossHistory['lossesYear3']).toEqual('_1');
    }));
  });

  describe('when all data is available in storage and loss history years are specified', () => {
    beforeEach(fakeAsync(inject(
      [StorageService],
      (storageService: StorageService) => {
        storageService.setStorageData('selectedPlan', Helpers.deepClone(HOME_QUOTE_PLAN_LIST));
        storageService.setStorageData('selectedQuote', Helpers.deepClone(HOME_QUOTE));
        // storageService.setStorageData('dwellingLossHistoryRequirements', [ Helpers.deepClone(LOSS_HISTORY_REQUIREMENTS_DATA.items), null ]);
        const modifiedLossHistory = Helpers.deepClone(LOSS_HISTORY_DATA.items[0]);
        modifiedLossHistory.lossFreeYears = '_2';
        modifiedLossHistory.lossesYear1 = '_0';
        modifiedLossHistory.lossesYear2 = '_0';
        modifiedLossHistory.lossesYear3 = '_2';
        modifiedLossHistory.lossesYear4 = '_1';
        modifiedLossHistory.lossesYear5 = '_0';
        modifiedLossHistory.items = [
          {
            lossDate: '2015-09-19',
            description: 'DamageToPropertyOfOthers',
            lossAmount: 1300,
            catastrophicLossInd: false,
            descriptionParsed: 'Damage To Property Of Others',
            lossesYear: '_3'
          },
          {
            lossDate: '2014-11-10',
            description: 'Windstorm',
            lossAmount: 12000,
            catastrophicLossInd: true,
            descriptionParsed: 'Windstorm',
            lossesYear: '_3'
          },
          {
            lossDate: '2014-10-13',
            description: 'VandalismAndMaliciousMischief',
            lossAmount: 120,
            catastrophicLossInd: false,
            descriptionParsed: 'Vandalism And Malicious Mischief',
            lossesYear: '_4'
          }
        ];
        storageService.setStorageData('dwellingLossHistory', modifiedLossHistory);

        fixture = TestBed.createComponent(LossHistoryComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
        tick();
      })));

    it('should create losses rows', () => {
      expect(component.rowsQty.length).toEqual(3);
      expect(component.rowsQty[0].items.length).toEqual(2);
      expect(component.rowsQty[1].items.length).toEqual(1);
    });

    it('should allow to delete all loss data', fakeAsync(inject(
      [StorageService],
      (storageService: StorageService) => {
        spyOn(storageService, 'setStorageData');

        // open modal
        const btn = fixture.debugElement.query(By.css('#btn-delate-all-loss-data')).nativeElement;
        btn.click();
        fixture.detectChanges();
        tick();

        // confirm
        const modalBox = fixture.debugElement.query(By.directive(StubModalboxComponent));
        const confirmBtn = modalBox.query(By.css('button')).nativeElement;
        confirmBtn.click();
        fixture.detectChanges();
        tick();

        expect(component.selectedTotalNumberOfLossFreeYears).toBeNull();
        expect(storageService.setStorageData).toHaveBeenCalledWith('dwellingLossHistory', jasmine.objectContaining({
          items: [],
          lossFreeYears: 'Unspecified',
          lossesYear1: '',
          lossesYear2: '',
          lossesYear3: '',
          lossesYear4: '',
          lossesYear5: ''
        }));
      })));

    it('should allow to update single loss details', fakeAsync(inject([StorageService],
      (storageService: StorageService) => {
        spyOn(storageService, 'setStorageData');

        // open modal
        const btn = fixture.debugElement.query(By.css('.delete-single-loss')).parent.query(By.css('button')).nativeElement;
        btn.click();
        fixture.detectChanges();
        tick();

        const modalBox = fixture.debugElement.queryAll(By.directive(StubModalboxComponent))[4];
        const datepicker = modalBox.query(By.directive(StubDatepickerInputComponent)).componentInstance;
        const autocomplete = modalBox.query(By.directive(StubAutocompleteComponent)).componentInstance;
        const input = modalBox.query(By.css('input[type="text"]')).nativeElement;
        const checkbox = modalBox.query(By.css('input[type="checkbox"]')).nativeElement;

        datepicker.onDateChange.emit({
          date: new Date(2015, 10, 1),
          selectByClick: true
        });
        fixture.detectChanges();
        tick();

        expect(component.lossItemToEdit.lossDate).toEqual('2015-11-01');

        autocomplete.onSelect.emit({
          id: 'EarthquakeDamage',
          text: 'Earthquake Damage'
        });
        fixture.detectChanges();
        tick();

        expect(component.lossItemToEdit.description).toEqual('EarthquakeDamage');

        changeTextInputValue(input, '80000', fixture);
        expect(component.lossItemToEdit.lossAmount).toEqual('80000');

        selectCheckbox(checkbox, fixture);
        expect(component.lossItemToEdit.catastrophicLossInd).toBeTruthy();

        // click save
        const saveBtn = modalBox.query(By.css('button'));
        saveBtn.triggerEventHandler('click', {});
        fixture.detectChanges();
        tick();

        expect(component.lossHistory.items[0].lossDate).toEqual('2015-11-01');
        expect(component.lossHistory.items[0].description).toEqual('EarthquakeDamage');
        expect(component.lossHistory.items[0].lossAmount).toEqual('80000');
        expect(component.lossHistory.items[0].catastrophicLossInd).toBeTruthy();
        expect(storageService.setStorageData).toHaveBeenCalledWith('dwellingLossHistory', jasmine.objectContaining({
          items: jasmine.arrayContaining([
            jasmine.objectContaining({
              lossDate: '2015-11-01',
              description: 'EarthquakeDamage',
              lossAmount: '80000',
              catastrophicLossInd: true
            })
          ])
        }));
      })));

    it('should allow to delete single loss details', fakeAsync(inject([StorageService],
      (storageService: StorageService) => {
        spyOn(storageService, 'setStorageData');

        // open confirmBox
        const btn = fixture.debugElement.query(By.css('.delete-single-loss')).nativeElement;
        btn.click();
        fixture.detectChanges();
        tick();

        const confirmBox = fixture.debugElement.query(By.directive(StubConfirmboxComponent)).componentInstance;
        confirmBox.onAccept.emit({});
        fixture.detectChanges();
        tick();

        expect(component.lossHistory.items).not.toEqual(jasmine.arrayContaining([
          jasmine.objectContaining({
            lossDate: '2015-09-19',
            description: 'DamageToPropertyOfOthers',
            lossAmount: 1300,
            catastrophicLossInd: false,
            lossesYear: 'lossesYear3'
          })
        ]));
      })));
  });

  describe('when only some data is available in storage', () => {
    beforeEach(fakeAsync(inject(
      [StorageService, SpecsService, DwellingService],
      (storageService: StorageService, specsService: StubSpecsService,
       dwellingService: StubDwellingService) => {
        storageService.setStorageData('selectedPlan', Helpers.deepClone(HOME_QUOTE_PLAN_LIST));
        storageService.setStorageData('selectedQuote', Helpers.deepClone(HOME_QUOTE));
        // storageService.setStorageData('dwellingLossHistoryRequirements', [Helpers.deepClone(LOSS_HISTORY_REQUIREMENTS_DATA.items), null]);
        const modifiedLossHistory = Helpers.deepClone(LOSS_HISTORY_DATA);
        modifiedLossHistory.items[0].lossesYear4 = '_2';
        dwellingService.lossHistory = modifiedLossHistory;

        fixture = TestBed.createComponent(LossHistoryComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
        tick();
      })));

    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should allow to add loss details', fakeAsync(inject([StorageService],
      (storageService: StorageService) => {
        spyOn(storageService, 'setStorageData');

        // open modal
        const btn = fixture.debugElement.query(By.css('#modal-details-lossesYear4')).nativeElement;
        btn.click();
        fixture.detectChanges();
        tick();

        const modalBox = fixture.debugElement.queryAll(By.directive(StubModalboxComponent))[3];
        const datepicker = modalBox.query(By.directive(StubDatepickerInputComponent)).componentInstance;
        const autocomplete = modalBox.query(By.directive(StubAutocompleteComponent)).componentInstance;
        const input = modalBox.query(By.css('input[type="text"]')).nativeElement;
        const checkbox = modalBox.query(By.css('input[type="checkbox"]')).nativeElement;

        datepicker.onDateChange.emit({
          date: new Date(2015, 10, 1),
          selectByClick: true
        });
        fixture.detectChanges();
        tick();

        expect(component.lossHistoryDataRows[0].lossDate).toEqual('2015-11-01');

        autocomplete.onSelect.emit({
          id: 'EarthquakeDamage',
          text: 'Earthquake Damage'
        });
        fixture.detectChanges();
        tick();

        expect(component.lossHistoryDataRows[0].description).toEqual('EarthquakeDamage');

        changeTextInputValue(input, '80000', fixture);
        expect(component.lossHistoryDataRows[0].lossAmount).toEqual(80000);

        selectCheckbox(checkbox, fixture);
        expect(component.lossHistoryDataRows[0].catastrophicLossInd).toBeTruthy();

        // click save
        const saveBtn = modalBox.query(By.css('button'));
        saveBtn.triggerEventHandler('click', {});
        fixture.detectChanges();
        tick();

        expect(component.lossHistory.items[0].lossDate).toEqual('2015-11-01');
        expect(component.lossHistory.items[0].description).toEqual('EarthquakeDamage');
        expect(component.lossHistory.items[0].lossAmount).toEqual(80000);
        expect(component.lossHistory.items[0].catastrophicLossInd).toBeTruthy();
        expect(storageService.setStorageData).toHaveBeenCalledWith('dwellingLossHistory', jasmine.objectContaining({
          items: jasmine.arrayContaining([
            jasmine.objectContaining({
              lossDate: '2015-11-01',
              description: 'EarthquakeDamage',
              lossAmount: 80000,
              catastrophicLossInd: true
            })
          ])
        }));
      })));
  });

  describe('when only some data is available in storage and there is no loss history in service', () => {
    beforeEach(fakeAsync(inject(
      [StorageService, SpecsService, DwellingService],
      (storageService: StorageService, specsService: StubSpecsService,
        dwellingService: StubDwellingService) => {
        storageService.setStorageData('selectedPlan', Helpers.deepClone(HOME_QUOTE_PLAN_LIST));
        storageService.setStorageData('selectedQuote', Helpers.deepClone(HOME_QUOTE));
        specsService.lossHistoryRequirements = Helpers.deepClone(LOSS_HISTORY_REQUIREMENTS_DATA);

        const modifiedLossHistory = Helpers.deepClone(LOSS_HISTORY_DATA);
        modifiedLossHistory.items = [];
        dwellingService.lossHistory = modifiedLossHistory;

        spyOn(dwellingService, 'createLossHistory').and.returnValue(Observable.of(Helpers.deepClone(LOSS_HISTORY_DATA)));

        fixture = TestBed.createComponent(LossHistoryComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
        tick();
      })));

    it('should create', () => {
      expect(component).toBeTruthy();
    });
  });
});
