<h1 class="o-heading o-heading--red">RMV Results</h1>
<br>
      <p-accordion [multiple]="true">
        <p-accordionTab header="The RMV returned the following information for the data you entered:" [selected]="true" >
            <div class="box box--silver">
              <div *ngFor="let msg of messages" style="padding-bottom:.4rem">
                <p>{{msg.message}}</p>
                </div>
               </div>
               </p-accordionTab>
                 <p-accordionTab header="Notifications:" [selected]="true" *ngIf="notifications && notifications.length > 0 && !evrLiteMessages ">
                   <div class="box box--silver">
                    <div *ngFor="let notif of notifications">
                     <p>
                       {{notif.message}}
                     </p>
                   </div>


                  </div>
                 </p-accordionTab>
                 <p-accordionTab header="Eligibility Options:" [selected]="true" *ngIf="evrLiteMessages && evrLiteMessages.length > 0 && !isEvrLitePermitExpired">
                   <div class="box box--silver">
                     <div class="col-md-3" *ngIf="showImage">
                       <img src="assets/images/common/green-arrowheads.png" alt="">
                     </div>
                      <div *ngFor="let message of evrLiteMessages">
                     <p>
                       {{message.message}}
                     </p>
                     <p style="color: blue; padding-top:8px"><a *ngIf="getReadyModel.transactionType === 'RegistrationTransfer' || getReadyModel.transactionType === 'Reassign'" (click)="downloadVehicleReport()">You must review Plate Inquiry here to initiate</a></p>
                  </div>
                   </div>
                 </p-accordionTab>

                 <p-accordionTab header="Get Ready" [selected]="true" *ngIf="!isEvrEligible && isGetReadyEligible">
                  <div class="box box--silver">
                    <div *ngIf="isEvrLitePermitExpired">
                      <p style="text-align: justify;">
                        Your RMV EVR Permit has expired.  You will not be able to process any EVR transactions in SinglePoint until you renew your permit.  Please contact your RMV Compliance Officer for assistance.
                      </p>
                    </div>
                    <hr>
                    <div class="row" style="text-align:center" *ngIf="showImage">
                      <img style="width:200px" src="assets/images/common/rmv_getready_logo.png" alt="">
                    </div>
                    <p style="text-align: justify;">
                      This RTA transaction is eligible for Get Ready pre-approval. The RMV ATLAS system will review the data to verify that all requirements are met.  If so, SinglePoint will generate a QR coded RTA that will be accepted at the RMV branch. If not, it will tell you what fields need to be corrected. Click "Go to Get Ready" button below.
                    </p>
                  </div>
                </p-accordionTab>
</p-accordion>

