import { fakeAsync, inject, TestBed, tick } from '@angular/core/testing';

import { data as AGENCY_LOCATIONS } from 'testing/data/agencies/locations';
import { data as AGENCY_CODES } from 'testing/data/subs/agency-codes';
import { data as AGENCY_USERS } from 'testing/data/subs/agency-users';
import {
    data as DEFAULT_AGENCY_AUTO_POLICY
} from 'testing/data/subs/coverage-defaults/auto-policy';
import { HOME_COVERAGE_DEFAULTS } from 'testing/data/subs/coverage-defaults/home-policy';
import { data as RATING_LOBS } from 'testing/data/subs/rating-lobs';
import { data as RATING_PLANS } from 'testing/data/subs/rating-plans';
import { data as RATING_STATES } from 'testing/data/subs/rating-states';
import { DataCustomMatchers, expectLastConnectionUrl } from 'testing/helpers/all';
import { setupMockBackend } from 'testing/setups/mock-backend';

import { StorageGlobalService } from 'app/shared/services/storage-global.service';

import { SubsService } from './subs.service';

describe('Service: Subs', () => {
  const AGENCY_ID = 'agency-id';

  let service: SubsService;
  let storageGlobalService: StorageGlobalService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        SubsService,
        StorageGlobalService
      ]
    });

    jasmine.addMatchers(DataCustomMatchers);
  });

  beforeEach(inject(
    [SubsService, StorageGlobalService],
    (_service: SubsService, _storageGlobalService: StorageGlobalService) => {
    service = _service;
    storageGlobalService = _storageGlobalService;

    spyOn(storageGlobalService, 'setSubs');
  }));

  it('can instantiate service when inject service', () => {
    expect(service).toBeTruthy();
  });

  it('provides a way to retrieve all rating plans and save them in storage', fakeAsync(() => {
    const mockBackend = setupMockBackend(RATING_PLANS);

    service.getRatingPlans(AGENCY_ID).subscribe();
    tick();

    expect(mockBackend.connectionsArray.length).toEqual(1);
    expectLastConnectionUrl(mockBackend).toEndWith('/subs/agency-id/ratingplans?lobs=');
    expect(storageGlobalService.setSubs).toHaveBeenCalledTimes(1);
  }));

  it('provides a way to retrieve all rating states and save them in storage', fakeAsync(() => {
    const mockBackend = setupMockBackend(RATING_STATES);

    service.getRatingStates(AGENCY_ID).subscribe();
    tick();

    expect(mockBackend.connectionsArray.length).toEqual(1);
    expectLastConnectionUrl(mockBackend).toEndWith('/subs/agency-id/ratingstates?lobs=');
    expect(storageGlobalService.setSubs).toHaveBeenCalledTimes(1);
  }));

  it('provides a way to retrieve all rating lobs and save them in storage', fakeAsync(() => {
    const mockBackend = setupMockBackend(RATING_LOBS);

    service.getRatingLobs(AGENCY_ID).subscribe();
    tick();

    expect(mockBackend.connectionsArray.length).toEqual(1);
    expectLastConnectionUrl(mockBackend).toEndWith('/subs/agency-id/ratinglobs');
    expect(storageGlobalService.setSubs).toHaveBeenCalledTimes(1);
  }));

  it('provides a way to retrieve all agency users and save them in storage', fakeAsync(() => {
    const mockBackend = setupMockBackend(AGENCY_USERS);

    service.getAgencyUsers(AGENCY_ID).subscribe();
    tick();

    expect(mockBackend.connectionsArray.length).toEqual(1);
    expectLastConnectionUrl(mockBackend).toEndWith('/subs/agency-id/users');
    expect(storageGlobalService.setSubs).toHaveBeenCalledTimes(1);
  }));

  it('provides a way to retrieve agency locations', fakeAsync(() => {
    const mockBackend = setupMockBackend(AGENCY_LOCATIONS);

    service.getAgencyLocations(AGENCY_ID).subscribe();
    tick();

    expect(mockBackend.connectionsArray.length).toEqual(1);
    expectLastConnectionUrl(mockBackend).toEndWith('/agencies/agency-id');
  }));

  it('provides a way to retrieve agency codes for rating plans', fakeAsync(() => {
    const mockBackend = setupMockBackend(AGENCY_CODES);

    service.getAgencyCodesForRatingPlan(AGENCY_ID, '4').subscribe();
    tick();

    expect(mockBackend.connectionsArray.length).toEqual(1);
    expectLastConnectionUrl(mockBackend).toEndWith('/subs/agency-id/agencyCodes?ratingPlanId=4&lob=');
  }));

  it('provides a way to retrieve default agency AUTO policy', fakeAsync(() => {
    const mockBackend = setupMockBackend(DEFAULT_AGENCY_AUTO_POLICY);

    service.getDefaultAutoCarrierOptions(AGENCY_ID).subscribe();
    tick();

    expect(mockBackend.connectionsArray.length).toEqual(1);
    expectLastConnectionUrl(mockBackend).toEndWith('/subs/agency-id/coverageDefaults/autoPolicy');
  }));

  it('provides a way to retrieve default agency HOME policy', fakeAsync(() => {
    const mockBackend = setupMockBackend(HOME_COVERAGE_DEFAULTS);

    service.getDefaultHomeCarrierOptions(AGENCY_ID,'').subscribe();
    tick();

    expect(mockBackend.connectionsArray.length).toEqual(1);
    expectLastConnectionUrl(mockBackend).toEndWith('/subs/agency-id/coverageDefaults/homePolicy');
  }));
});
