
import {first, take} from 'rxjs/operators';
import { IntegrationNotes, ModalIntegrationNote } from './../../app-model/quote';
import { Helpers } from './../../utils/helpers';
import { ChangeDetectorRef, Component, OnInit, ViewChild } from '@angular/core';
import { Router, ActivatedRoute, NavigationExtras, Params } from '@angular/router';

import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { SubscriptionLike as ISubscription } from 'rxjs';
import { ModalboxComponent } from 'app/shared/components/modalbox/modalbox.component';
import { IEventDataImportAutoQuote } from '../auto/new-auto-create-form/new-auto-create-form.component';
import { Quote, QuotePlanListAPIResponse, QuotePlan, IntegrationNote } from 'app/app-model/quote';
import { IEventDataImportHomeQuote } from '../home/<USER>/new-home-create-form.component';
import { IEventDataImportDwellingQuote } from '../dwelling/new-dwelling-create-form/new-dwelling-create-form.component';
import { IEventDataImportUmbrellaQuote } from '../umbrella/new-umbrella-create-form/new-umbrella-create-form.component';
import { StorageService } from 'app/shared/services/storage-new.service';

import { RmvService } from '../app-services/rmv.service';
import { AgencyUserService } from 'app/shared/services/agency-user.service';
import { addYears, format, parseISO } from 'date-fns';

type TImportData = IEventDataImportAutoQuote | IEventDataImportHomeQuote | IEventDataImportDwellingQuote | IEventDataImportUmbrellaQuote;

@Component({
    selector: 'app-quote-importer',
    templateUrl: './quote-importer.component.html',
    styleUrls: ['./quote-importer.component.scss'],
    standalone: false
})
export class QuoteImporterComponent implements OnInit {
  private routeSubscription: ISubscription;
  private getImportQuoteUrlSubscription: ISubscription;
  private getIntegrationNotesSubscription: ISubscription;
  public errorMessage;

  @ViewChild('refModalIntegrationNotes') public refModalIntegrationNotes: ModalboxComponent;
  @ViewChild('refModalNewAuto') public refModalNewAuto: ModalboxComponent;
  @ViewChild('refModalNewHome') public refModalNewHome: ModalboxComponent;
  @ViewChild('refModalNewDwelling') public refModalNewDwelling: ModalboxComponent;
  @ViewChild('refModalNewUmbrella') public refModalNewUmbrella: ModalboxComponent;

  public readyToImport = false;
  public quoteToImport: Quote;
  public quoteToImportEffectiveDate: string = null;
  private quoteToImportSelectedPlans: QuotePlan[] = null;
  public quoteToImportSelectedState: string = null;
  public quoteToImportPolicyType: string = null;
  public quoteToImportFormType: string = null;
  public integrationNotes: IntegrationNote[] = null;
  public modalIntegrationNotes: ModalIntegrationNote[] = [];
  drivers = [];
  vehicles = [];
  userData
  selectedVehicle: any;
  selectedOwner1
  selectedOwner2

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private quotesService: QuotesService,
    private overlayLoaderService: OverlayLoaderService,
    private storageService: StorageService,
    private rmvService: RmvService,
    private agencyUserService:AgencyUserService,
  ) { }

  ngOnInit() {
    // this.getQuoteUrl()
    this.getQuoteToImportAllData()
      .then(()=> this.getUser())
      .then(() => this.actionOpenIntegrationNotes())
      .then(() => this.getDriversAndVehicles())
      .then(() => {
        setTimeout(() => {this.readyToImport = true; }, 500);
      })
      .catch(err => console.log('ERROR: ', err));
  }

  private getUser() {
    this.agencyUserService.userData$.pipe(
      first())
      .subscribe(data => this.userData = data);
  }

  ngOnDestroy() {
    this.routeSubscription && this.routeSubscription.unsubscribe();
    this.getImportQuoteUrlSubscription && this.getImportQuoteUrlSubscription.unsubscribe();
    this.getIntegrationNotesSubscription && this.getIntegrationNotesSubscription.unsubscribe();
  }

  public getQuoteToImportAllData(): Promise<void> {
    this.overlayLoaderService.showLoader('Loading...');
    return new Promise((resolve, reject) => {
      this.getQuoteBaseDataToImport()
        .then(() => this.getIntegrationNotes(this.quoteToImport))
        .then(() => this.getQuoteToImportSelectedPlans(this.quoteToImport))
        .then(() => {
          this.overlayLoaderService.hideLoader();
          resolve();
        })
        .catch(err => {
          this.overlayLoaderService.hideLoader();
          reject(err);
        });
    });
  }


  getDriversAndVehicles() {
    this.quotesService.getDataByUrl(this.quoteToImport.drivers.href).subscribe(x => {
      this.drivers = x.items.map(driver => ({
        id: driver.resourceId,
        text: `${driver.firstName} ${driver.lastName}`
      }));
    });

    this.quotesService.getDataByUrl(this.quoteToImport.vehicles.href).subscribe(x => {
      this.vehicles = x.items.map(vehicle => ({
        id: vehicle.resourceId,
        text: `${vehicle.year} ${vehicle.make} ${vehicle.model}`
      }));
    });
  }
  getIntegrationNotes(quoteToImport: Quote): any {
    if (!quoteToImport.resourceId) { return; }

    return new Promise<void>((resolve, reject) => {

        this.getIntegrationNotesSubscription = this.quotesService.getIntegrationNotes(quoteToImport.resourceId).subscribe(
          (data: IntegrationNotes) => {
            if (data) {
              this.integrationNotes = data.notes;
              resolve();
            } else {
              const error = {
                status: 'No Data',
                statusText: 'There is no available Quote data for this request.'
              };
              this.errorMessage = error;
              reject();
            }
          },
          err => {
            this.errorMessage = err;
            reject(err);
          });
    });
  }


  public getQuoteBaseDataToImport(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.routeSubscription = this.route.params.subscribe((params: Params) => {
        this.getImportQuoteUrlSubscription && this.getImportQuoteUrlSubscription.unsubscribe();
        this.getImportQuoteUrlSubscription = this.quotesService.getImportQuoteUrl(params['id']).subscribe(
          (data: Quote) => {
            console.log('IMPORT QUOTE: ', data);
            if (data && data.meta && data.meta.href) {
              this.quoteToImport = data;
              this.quoteToImportEffectiveDate = this.quoteToImport.effectiveDate;
              this.quoteToImportSelectedState = this.quoteToImport.state;
              this.quoteToImportPolicyType = this.quoteToImport.policyType;
              this.quoteToImportFormType = this.quoteToImport.formType;

              resolve();
            } else {
              const error = {
                status: 'No Data',
                statusText: 'There is no available Quote data for this request.'
              };
              this.errorMessage = error;
              reject();
            }
          },
          err => {
            this.errorMessage = err;
            reject(err);
          });
      });
    });
  }

  private getQuoteToImportSelectedPlans(quote: Quote): Promise<void> {
    return new Promise((resolve, reject) => {
      this.quotesService
        .getDataByUrl(quote.quotePlanList.href).pipe(
        take(1))
        .subscribe((data: QuotePlanListAPIResponse) => {
          if (data && data.items && data.items.length) {
            this.quoteToImportSelectedPlans = (data.items[0].items && data.items[0].items.length && data.items[0].items.length > 0)
              ? JSON.parse(JSON.stringify(data.items[0].items))
              : null;
          } else {
            this.quoteToImportSelectedPlans = null;
          }
          resolve();
        });
    });
  }

  public actionOpenIntegrationNotes() {
    this.populateNotesModal();
    this.refModalIntegrationNotes && this.refModalIntegrationNotes.open();


  }

  public populateNotesModal() {

    if (this.integrationNotes && this.integrationNotes.length < 1) { return; }
    this.modalIntegrationNotes = [];
    this.integrationNotes.forEach(intNote => {
        const existingNotes = this.modalIntegrationNotes.filter(n => n.label === intNote.displayLabel);
        if (existingNotes && existingNotes.length > 0) {
          existingNotes[0].messages.push(intNote.message);
        } else {
          this.modalIntegrationNotes.push(
            new ModalIntegrationNote(intNote.displayLabel, [intNote.message])
              );
        }
    });
    this.quotesService.setIntegrationNotesStorage( this.modalIntegrationNotes);
  }


  public  actionGotoImport() {
    this.refModalIntegrationNotes && this.refModalIntegrationNotes.close();
    this.openProperModalForm();
  }

  public  openProperModalForm(): void {
    // console.log('Quote to import:', quote);

    switch (this.quoteToImport.lob) {
      case 'AUTOP':
        this.refModalNewAuto && this.refModalNewAuto.open();
        break;
      case 'HOME':
        this.refModalNewHome && this.refModalNewHome.open();
        break;
      case 'DFIRE':
        this.refModalNewDwelling && this.refModalNewDwelling.open();
        break;
      case 'PUMBR':
        this.refModalNewUmbrella && this.refModalNewUmbrella.open();
        break;
    }
  }

  // View actions
  // ---------------------------------------------------------------------------
  public actionImportButtonClick(): void {
      this.actionOpenIntegrationNotes();
  }

  public actionModalImportQuote(data: TImportData): void {
    console.log('Import:', data);
    switch (this.quoteToImport.lob) {
      case 'AUTOP':
        this.importAutoQuote(data as IEventDataImportAutoQuote);
        break;
      case 'HOME':
        this.importHomeQuote(data as IEventDataImportHomeQuote);
        break;
      case 'DFIRE':
        this.importDwellingQuote(data as IEventDataImportDwellingQuote);
        break;
      case 'PUMBR':
        this.importUmbrellaQuote(data as IEventDataImportUmbrellaQuote);
        break;
    }
  }

  public actionModalCancel(modal: ModalboxComponent): void {
    modal && modal.close();
    this.router.navigate(['/dashboard']);
  }


  // Specified Quote LOBs imports
  // ---------------------------------------------------------------------------
  private importAutoQuote(data: IEventDataImportAutoQuote): void {
    const tmpQuoteToImport: Quote = JSON.parse(JSON.stringify(this.quoteToImport));
    tmpQuoteToImport.effectiveDate = data.effectiveDate;
    tmpQuoteToImport.expirationDate = format(addYears(parseISO(data.effectiveDate), 1), 'yyyy-MM-dd');

    this.overlayLoaderService.showLoader();
    this.updateQuoteSelectedPlans(tmpQuoteToImport, data.selectedPlans)
      .then(() => this.updateQuoteInfo(tmpQuoteToImport))
      .then(() => {
        this.router.navigate(['/dashboard' + tmpQuoteToImport.meta.href, {imported: true}]);
        this.overlayLoaderService.hideLoader();
      })
      .catch(err => {
        console.log(err);
        this.overlayLoaderService.hideLoader();
      });
  }

  private importHomeQuote(data: IEventDataImportHomeQuote): void {
    const tmpQuoteToImport: Quote = JSON.parse(JSON.stringify(this.quoteToImport));
    tmpQuoteToImport.effectiveDate = data.effectiveDate;
    tmpQuoteToImport.expirationDate = format(addYears(parseISO(data.effectiveDate), 1), 'yyyy-MM-dd');
    tmpQuoteToImport.state = data.state.id || 'MA';
    tmpQuoteToImport.policyType = data.policyType;
    tmpQuoteToImport.formType = data.formType;

    this.overlayLoaderService.showLoader();
    this.updateQuoteSelectedPlans(tmpQuoteToImport, data.selectedPlans)
      .then(() => this.updateQuoteInfo(tmpQuoteToImport))
      .then(() => {
        this.router.navigate(['/dashboard' + tmpQuoteToImport.meta.href, {imported: true}]);
        this.overlayLoaderService.hideLoader();
      })
      .catch(err => {
        console.log(err);
        this.overlayLoaderService.hideLoader();
      });
  }

  private importDwellingQuote(data: IEventDataImportDwellingQuote): void {
    const tmpQuoteToImport: Quote = JSON.parse(JSON.stringify(this.quoteToImport));
    tmpQuoteToImport.effectiveDate = data.effectiveDate;
    tmpQuoteToImport.expirationDate = format(addYears(parseISO(data.effectiveDate), 1), 'yyyy-MM-dd');
    tmpQuoteToImport.state = data.state.id || 'MA';
    tmpQuoteToImport.formType = data.formType;

    this.overlayLoaderService.showLoader();
    this.updateQuoteSelectedPlans(tmpQuoteToImport, data.selectedPlans)
      .then(() => this.updateQuoteInfo(tmpQuoteToImport))
      .then(() => {
        this.router.navigate(['/dashboard' + tmpQuoteToImport.meta.href, {imported: true}]);
        this.overlayLoaderService.hideLoader();
      })
      .catch(err => {
        console.log(err);
        this.overlayLoaderService.hideLoader();
      });
  }

  private importUmbrellaQuote(data: IEventDataImportUmbrellaQuote): void {
    const tmpQuoteToImport: Quote = JSON.parse(JSON.stringify(this.quoteToImport));
    tmpQuoteToImport.effectiveDate = data.effectiveDate;
    tmpQuoteToImport.expirationDate = format(addYears(parseISO(data.effectiveDate), 1), 'yyyy-MM-dd');

    this.overlayLoaderService.showLoader();
    this.updateQuoteSelectedPlans(tmpQuoteToImport, data.selectedPlans)
      .then(() => this.updateQuoteInfo(tmpQuoteToImport))
      .then(() => {
        this.router.navigate(['/dashboard' + tmpQuoteToImport.meta.href, {imported: true}]);
        this.overlayLoaderService.hideLoader();
      })
      .catch(err => {
        console.log(err);
        this.overlayLoaderService.hideLoader();
      });
  }


  // Update helpers
  // ---------------------------------------------------------------------------
  private updateQuoteSelectedPlans(quote: Quote, plans: QuotePlan[]): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!plans || !plans.length) {
        resolve();
      }

      this.quotesService.updateQuoteByUrl(quote.quotePlanList.href, { items: plans }).pipe(
        take(1))
        .subscribe(
          () => resolve(),
          err => reject(err)
        );
    });
  }

  private updateQuoteInfo(quote: Quote): Promise<void> {
    return new Promise((resolve, reject) => {
      this.quotesService.updateQuoteInfo(quote.resourceId, quote).pipe(
        take(1))
        .subscribe(() => {
          resolve();
        });
    });
  }

  handleRmvServicesRedirect($ev) {
    const data = {
      transactionType: 'PrefillNewTitleAndRegistration',
      transactionTypeDesc: 'Register and title a new vehicle',
      workflowType: 'RmvServices',
      agencyId: this.userData.agencyId,
      userId: this.userData.user.userId,
    };
  const quoteSessionId = this.quoteToImport.quoteSessionId;
  const owner1 = this.drivers.findIndex(x => x.id === this.selectedOwner1)
  const owner2 = this.drivers.findIndex(x => x.id === this.selectedOwner2)
  const vehicleIndex = this.vehicles.findIndex(x => x.id === this.selectedVehicle) + 1
  const driverArray = [owner1+1,owner2+1].filter(i => i !== 0);

  if (this.overlayLoaderService.isOpen) {this.overlayLoaderService.hideLoader(); }
  this.overlayLoaderService.showLoader();
  this.rmvService.createRmvTransactionFromQuote(data, quoteSessionId, driverArray.join(','),
   vehicleIndex, this.quoteToImport.quoteIdentifier).subscribe(
    x => {
      this.overlayLoaderService.hideLoader();
      this.router.navigate(['/dashboard/rmv-services/rta-prefill'], {queryParams: {id: x.rmvServicesRequestInfo.id, type: 'PrefillNewTitleAndRegistration'}});
    }, err => this.overlayLoaderService.hideLoader()
  );
  }


}
