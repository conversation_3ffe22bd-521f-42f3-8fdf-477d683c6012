import { async, ComponentFixture, inject, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';

import { data as RMV_QUOTE_DATA } from 'testing/data/rmv-quote';
import { MockCurrentPageServiceProvider } from 'testing/stubs/services/current-page.service.provider';

import { StorageService } from 'app/shared/services/storage-new.service';

import { ContentHeadGoToQuoteComponent } from './content-head-go-to-quote.component';

describe('Component: ContentHeadGoToQuote', () => {
  let component: ContentHeadGoToQuoteComponent;
  let fixture: ComponentFixture<ContentHeadGoToQuoteComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      declarations: [ ContentHeadGoToQuoteComponent ],
      providers: [
        MockCurrentPageServiceProvider,
        StorageService
      ]
    })
    .compileComponents();
  }));

  beforeEach(inject([StorageService], (storageService: StorageService) => {
    storageService.setStorageData('responseNewRmvQuoteResult', RMV_QUOTE_DATA);

    fixture = TestBed.createComponent(ContentHeadGoToQuoteComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  }));

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should destroy without errors', () => {
    expect(() => {
      fixture.destroy();
    }).not.toThrow();
  });
});
