import { TestBed, inject } from '@angular/core/testing';

import { AutoAdditionalCoveragesAutomanagerService } from './auto-additional-coverages-automanager.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { OptionsService } from 'app/dashboard/app-services/options.service';
import { SubsService } from 'app/dashboard/app-services/subs.service';
import { VehiclesService } from 'app/dashboard/app-services/vehicles.service';
import { AgencyUserService } from 'app/shared/services/agency-user.service';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { OverlayRouteService } from 'app/overlay/services/overlay-route.service';
import { RouterTestingModule } from '@angular/router/testing';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { LeaveQuoteService } from 'app/shared/services/leave-quote.service';
import { LocationsService } from 'app/dashboard/app-services/locations.service';


describe('AutoAdditionalCoveragesAutomanagerService', () => {
  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [ RouterTestingModule ],
      providers: [
        AutoAdditionalCoveragesAutomanagerService,
        StorageService,
        SpecsService,
        OptionsService,
        SubsService,
        VehiclesService,
        AgencyUserService,
        StorageGlobalService,
        QuotesService,
        OverlayRouteService,
        OverlayLoaderService,
        LeaveQuoteService,
        LocationsService
      ]
    });
  });

  it('should be created', inject([AutoAdditionalCoveragesAutomanagerService], (service: AutoAdditionalCoveragesAutomanagerService) => {
    expect(service).toBeTruthy();
  }));
});
