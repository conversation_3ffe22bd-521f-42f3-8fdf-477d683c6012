<section *ngIf="errorMessage" class="section">
  <p>{{ errorMessage.status }}</p>
  <p>{{ errorMessage.statusText }}</p>
</section>


<section class="section">
  <div class="col-12 u-align-center" *ngIf="readyToImport">
    <button type="button" class="o-btn" (click)="actionImportButtonClick()">Import Quote</button>
  </div>
</section>

<app-modalbox #refModalIntegrationNotes [css]="quoteToImport?.lob === 'AUTOP' ? 'modal-width-850px' : 'width-550px'">
    <h2 class="o-heading o-heading--red u-spacing--bottom-1-5">INBOUND INTEGRATION NOTES</h2>

<div class="box box--silver u-spacing--1-5" style="height: 400px; overflow: auto;" >
    <div *ngFor="let note of modalIntegrationNotes"  >
    <h3 class="u-t-size--1-5rem u-align-left">  {{note.label}}:</h3>
    <div *ngFor="let msg of note.messages">
  <p  class="u-spacing--left-4 u-spacing--1-5 u-spacing--bottom-2 u-align-left">
     - {{msg}}
  </p>
</div>
</div>
</div>

 <div class="row u-spacing--2">
  @if(this.quoteToImport?.lob !== 'AUTOP'){
  <div class="col-xs-12 u-align-right">
    <button class="o-btn" (click)="actionGotoImport()">
      Close
    </button>
  </div>
}@else {
  <div class="col-xs-6">
      <h2 class="o-heading">Rmv Services</h2>

      <div class="box box--silver u-spacing--1-5">
        <div class="row">
          <div class="col-xs-12">
            <table class="form-table">
              <tbody>
                <tr class="form-table__row" [ngClass]="{
                  'is-required-field': !selectedVehicle
                }">
                  <td class="form-table__cell u-width-150px">Vehicle:</td>
                  <td class="form-table__cell">
                    <sm-autocomplete [options]="vehicles" [name]="vehicle" [(ngModel)]="selectedVehicle"></sm-autocomplete>
                  </td>
                </tr>
                <tr class="form-table__row"
                [ngClass]="{
                  'is-required-field': !selectedOwner1
                }"
                >
                  <td class="form-table__cell">Owner 1:</td>
                  <td class="form-table__cell">
                    <sm-autocomplete [options]="drivers" name="owner1" [(ngModel)]="selectedOwner1"></sm-autocomplete>
                  </td>
                </tr>
                <tr class="form-table__row">
                  <td class="form-table__cell">Owner 2:</td>
                  <td class="form-table__cell">
                    <sm-autocomplete [options]="drivers" [name]="owner2" [(ngModel)]="selectedOwner2"></sm-autocomplete>
                  </td>
                </tr>
              </tbody>
            </table>

          </div>
        </div>
      </div>

      <div class="row u-spacing--1-5">
        <div class="col-xs-12 u-align-right u-remove-letter-spacing">
          <ng-container>
            <button type="button" (click)="handleRmvServicesRedirect($event)" [disabled]="!selectedVehicle || !selectedOwner1" class="o-btn u-spacing--left-2" style="margin-left:1.8rem;">Go To Rmv Services</button>
          </ng-container>
    </div>
</div>
  </div>
  <div class="col-xs-6">

     <app-new-auto-create-form
    [useToImportQuote]="true"
    [effectiveDate]="quoteToImportEffectiveDate"
    (importQuoteClick)="actionModalImportQuote($event)"
    (onCancelClick)="actionModalCancel(refModalNewAuto)">
  </app-new-auto-create-form>
  </div>

}
</div>
</app-modalbox>

<app-modalbox #refModalNewAuto [css]="'u-width-430px'">
  <app-new-auto-create-form *ngIf="refModalNewAuto.isOpen"
    [useToImportQuote]="true"
    [effectiveDate]="quoteToImportEffectiveDate"
    (importQuoteClick)="actionModalImportQuote($event)"
    (onCancelClick)="actionModalCancel(refModalNewAuto)">
  </app-new-auto-create-form>
</app-modalbox>

<app-modalbox #refModalNewHome [css]="'u-width-430px'">
  <app-new-home-create-form *ngIf="refModalNewHome.isOpen"
    [useToImportQuote]="true"
    [effectiveDate]="quoteToImportEffectiveDate"
    [quoteState]="quoteToImportSelectedState"
    [quotePolicyType]="quoteToImportPolicyType"
    [quoteFormType]="quoteToImportFormType"
    (importQuoteClick)="actionModalImportQuote($event)"
    (onCancelClick)="actionModalCancel(refModalNewHome)">
  </app-new-home-create-form>
</app-modalbox>

<app-modalbox #refModalNewDwelling [css]="'u-width-430px'">
  <app-new-dwelling-create-form *ngIf="refModalNewDwelling.isOpen"
    [useToImportQuote]="true"
    [effectiveDate]="quoteToImportEffectiveDate"
    [quoteState]="quoteToImportSelectedState"
    [quoteFormType]="quoteToImportFormType"
    (importQuoteClick)="actionModalImportQuote($event)"
    (onCancelClick)="actionModalCancel(refModalNewDwelling)">
  </app-new-dwelling-create-form>
</app-modalbox>

<app-modalbox #refModalNewUmbrella [css]="'u-width-430px'">
  <app-new-umbrella-create-form *ngIf="refModalNewUmbrella.isOpen"
    [useToImportQuote]="true"
    [effectiveDate]="quoteToImportEffectiveDate"
    (importQuoteClick)="actionModalImportQuote($event)"
    (onCancelClick)="actionModalCancel(refModalNewUmbrella)">
  </app-new-umbrella-create-form>
</app-modalbox>
