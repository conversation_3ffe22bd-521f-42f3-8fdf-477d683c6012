{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "typescript.tsc.autoDetect": "off",
    "grunt.autoDetect": "off",
    "jake.autoDetect": "off",
    "gulp.autoDetect": "off",
    "npm.autoDetect": "off",
    "tasks": [
        {
            "label": "coverage",
            "command": "ng test -cc -sr",
            "type": "shell",
            "group": "test",
            "problemMatcher": [
                "$tsc"
            ]
        },
        {
            "label": "test",
            "command": "ng test",
            "type": "shell",
            "group": {
                "kind": "test",
                "isDefault": true
            },
            "presentation": {
                "echo": true,
                "reveal": "silent",
                "focus": false,
                "panel": "dedicated"
            },
            "problemMatcher": [
                "$tsc-watch"
            ]
        },
        {
            "label": "serve",
            "command": "ng serve",
            "type": "shell",
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "presentation": {
                "echo": true,
                "reveal": "silent",
                "focus": false,
                "panel": "dedicated"
            },
            "problemMatcher": [
                "$tsc-watch"
            ]
        },
        {
            "label": "prod",
            "command": "ng build --env=prod",
            "type": "shell",
            "group": "build",
            "problemMatcher": [
                "$tsc"
            ]
        }
    ]
}
