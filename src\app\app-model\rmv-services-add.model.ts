import { ResponseDetail } from './evr-validate';

export interface Owner {
  id: number;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  license: string;
  licenseState: string;
}

export interface Vehicle {
  id: number;
  lookupType: string;
  usage?: string;
  plateNumber: string;
  vin: string;
  plateType: string;
  odometer?: string;
  odometerCode?: string;
  registrationType?: string;
  reassignedPlate?: string;
  plateReactivateIndicator?: string;
  registrationReason?: string;
  ownership?: string;
  condition?: string;
  primaryColor?: string;
  transmission?: string;
  passengers?: string;
  outOfStateTitleNumber?: string;
  titleState?: string;
  titleIssueDate?: string;
  atlasVehicleIndicator?: string;
  atlasVehicleKey?: string;
  pseudoTrailerVINIndicator?: string;
  bodyStyle?: string;
  vehicleType?: string;
  secondaryColor?: string;
  year?: string;
  make?: string;
  model?: string;
  modelNumber?: string;
  cylinders?: string;
  doors?: string;
  numberOfSeats?: string;
  fuelType?: string;
  trim?: string;
  msrp?: string;
  grossVehicleWeight?: string;
  registeredWeight?: string;
  trimItems?: TrimItem[];
  titleType?: string;
  titleBrands?: TitleBrands[];
}

export interface TitleBrands {
  brandType: string;
}

export interface TrimItem {
  id: number;
  name: string;
  value: string;
  msrp: string;
}

export interface GaragingAddress {
  type: string;
  referenceType: string;
  referenceId: string;
  street: string;
  street2: string;
  unitOrApt: string;
  unitType: string;
  city: string;
  state: string;
  zip: string;
}

export interface Address {
  street: string;
  unitOrApt: string;
  city: string;
  state: string;
  zip: string;
}

export interface Seller {
  businessName: string;
  firstName: string;
  lastName: string;
  address: Address;
}

export interface PurchaseAndSalesInformation {
  purchaseDate: string;
  purchaseState: string;
  seller: Seller;
  taxExempt: string;
  taxExemptType: string;
  purchaseType: string;
  dealerFid: string;
  totalSalePrice: string;
  auctionSale: string;
  maResidentAtTimeOfPurchase: string;
  maSalesTaxPreviouslyPaid: string;
  proofOfNoTaxRequired: string;
}

export interface EStampInfo {
  effectiveDate: string;
  writingCompanyName: string;
  companyCode: number;
  agencyName: string;
  signedBy: string;
}

export interface Lessor {
  id: number;
  fid: string;
  atlasEntityKey?: string;
  atlasEntityLocationKey?: string;
}

export interface Lienholder {
  id: number;
  code: string;
  atlasEntityKey?: any;
}

export interface BusinessOwner {
  id: number;
  fid: string;
}

export interface RmvServicesUserEnteredData {
  owners: Owner[];
  vehicles: Vehicle[];
  garagingAddress: GaragingAddress;
  purchaseAndSalesInformation: PurchaseAndSalesInformation;
  eStampInfo: EStampInfo;
  // needed for get details difference
  estampInfo?: EStampInfo;
  lessors: Lessor[];
  lienholders: Lienholder[];
  indicators: Indicator[];
  businessOwners: BusinessOwner[];
}

export interface Indicator {
  name: string;
  value: string;
}

export interface RmvServicesSaveState {
  transactionType: string;
  ownership: string;
  ownerFirstName: string;
  ownerLastName: string;
  agencyId: string;
  userId: number;
  email?: string;
  quoteId?: number;
  assignedPlate?: string;
  atlasValidatedTransactionKey?: string;
  atlasRegistrationKey?: string;
  atlasTransactionKey?: string;
  status?: string;
  validateDetails?: ResponseDetail;
  workflowType: string;
  rmvServicesUserEnteredData: RmvServicesUserEnteredData;
}
