<section class="section section--compact u-spacing--2-5">
  <div class="row">
    <div class="col-xs-12">
      <h1 class="o-heading">Dwelling Information</h1>
    </div>
  </div>

  <div class="row u-spacing--1">
    <div class="col-xs-12">
      <div class="box box--silver">
        <div class="row o-columns">
          <div class="col-xs-6">
            <table class="form-table">

              <tr class="form-table__row" [ngClass]="{'is-required-field': refDwellingCity.ngModel?.invalid}">
                <td class="form-table__cell u-width-120px">
                  <label for="">City:</label>
                </td>
                <td class="form-table__cell u-width-180px">
                  <sm-autocomplete
                    fieldAutofocus
                    #refDwellingCity
                    [options]="cities"
                    [name]="'dwellingCity'"
                    [id]="'dwellingCity'"
                    [caseSensitiveOptionsIdMatching]="false"
                    [activeOption]="getSelectedCityOption()"
                    [readonly]="cities.length ? false : true"
                    [required]="true"
                    [allowEmptyValue]="false"
                    (onSelect)="updateDwellingDetails($event, 'city')">
                  </sm-autocomplete>
                </td>
                <td class="form-table__cell u-width-70px"></td>
              </tr>
              <tr class="form-table__row" [ngClass]="{'is-required-field':fieldFireDistrictIsRequired() }">
                <td class="form-table__cell u-width-120px">
                  <label for="">Fire District:</label>
                </td>
                <td class="form-table__cell u-width-180px">
                  <sm-autocomplete
                    [options]="fireDistricts"
                    [name]="'fireDistricts'"
                    [id]="'fireDistricts'"
                    [activeOption]="getSelectedFireDistrictOption()"
                    [caseSensitiveOptionsIdMatching]="false"
                    [allowEmptyValue]="true"
                    (onSelect)="updateDwellingDetails($event, 'fireDistrict')"

                    >
                  </sm-autocomplete>
                  <!-- getSelectedCityOption() -->
                </td>
                <td class="form-table__cell u-width-70px"></td>
              </tr>
              <tr class="form-table__row">
                <td class="form-table__cell u-width-120px">
                  <label for="">County:</label>
                </td>
                <td class="form-table__cell u-width-180px">
                  <input
                    #refDwellingAddress="ngModel"
                    readonly
                    type="text"
                    id="dwellingCounty"
                    name="dwellingCounty"
                    [(ngModel)]="dwellingProtectionClass.town.countyName">
                </td>
                <td class="form-table__cell u-width-70px"></td>
              </tr>

              <tr class="form-table__row">
                <td class="form-table__cell form-table__cell--label u-width-120px">
                  <label for="dwellingState">Zipcode / ISO PC:</label>
                </td>
                <td class="form-table__cell u-width-220px" colspan="2">
                  <div class="u-show-iblock u-width-75px u-spacing--right-1" [class.is-required-field]="refZipCodeModel.invalid">
                    <input
                      #refZipCodeModel="ngModel"
                      type="text"
                      [(ngModel)]="dwellingLocation.zip"
                      style="width: 12ch; margin-right:15px"
                      pattern="^[0-9]{5}(?:-[0-9]{4})?$|^[0-9]{9}$"
                      name="dwellingZip"
                      id="dwellingZip"
                      required
                      (change)="updateDwellingDetails($event.target.value, 'townCode')"
                      [mask]="'00000-0000||00000'"
                      >
                  </div>
                  <div class="u-show-iblock u-width-60px">
                    <input
                      readonly
                      type="text"
                      name="isopc"
                      style="margin-left:15px"
                      id="isopc"
                      [value]="removeLeadingUnderscore(dwellingProtectionClass.isoProtectionClass)">
                  </div>
                  <div class="u-show-iblock u-padd--left-0-5">
                    <app-protection-class-overrides></app-protection-class-overrides>
                  </div>
                </td>
              </tr>

              <tr class="form-table__row" [ngClass]="{'is-required-field': dwelling.constructionMaterialTypeCode ? false : true}">
                <td class="form-table__cell u-width-120px">
                  <label for="">Construction:</label>
                </td>
                <td class="form-table__cell u-width-180px">
                  <sm-autocomplete
                    [options]="construction"
                    [activeOption]="dwelling.constructionMaterialTypeCode"
                    [required]="dwelling.constructionMaterialTypeCode ? false : true"
                    [id]="'constructionMaterial'"
                    [name]="'constructionMaterial'"
                    [allowEmptyValue]="false"
                    (onSelect)="selected($event, dwelling, 'constructionMaterialTypeCode')">
                  </sm-autocomplete>
                </td>
                <td class="form-table__cell u-width-70px"></td>
              </tr>

              <tr class="form-table__row">
                <td class="form-table__cell u-width-120px" [ngClass]="{'is-required-field': fieldYearBuiltIsInvalid()}">
                  <label for="">Year Built / Age:</label>
                </td>
                <td colspan="2" class="form-table__cell u-width-220px">
                  <div class="u-show-iblock u-width-50px u-spacing--right-1" [ngClass]="{'is-required-field': fieldYearBuiltIsInvalid()}">
                    <input
                      id="constructionYear"
                      name="constructionYear"
                      type="text"
                      required
                      #dwellingYear="ngModel"
                      #constructionYear
                      pattern="\d*"
                      (keydown)="numbersOnly($event)"
                      [(ngModel)]="dwelling.constructionYear"
                      (focus)="setLastValidConstructionYear(constructionYear.value)"
                      (change)="actionOnConstructionYearChange()">
                  </div>

                  <div class="u-show-iblock u-width-25px u-color-slate-grey">
                    or
                  </div>
                  <div class="u-show-iblock u-width-50px">
                    <input
                      id="dwellingAge"
                      name="dwellingAge"
                      type="text"
                      pattern="\d*"
                      (keydown)="numbersOnly($event)"
                      [(ngModel)]="dwelling.constructionAge"
                      (change)="actionOnConstructionAgeChange()">

                    <app-modalbox #refWarningModalbox [css]="'u-width-380px tight'" (onStateChange)="actionOnModalboxClose($event)">
                      <p class="u-spacing--1">
                        {{errorMessage}}
                      </p>

                      <div class="row u-spacing--2">
                        <div class="col-xs-12 u-align-right">
                          <button class="o-btn" (click)="actionOnModalboxBtnClose($event)">
                            Close
                          </button>
                        </div>
                      </div>
                    </app-modalbox>

                  </div>
                </td>
              </tr>

              <tr class="form-table__row" [ngClass]="{'is-required-field': dwelling.utilizationTypeCode ? false : true}">
                <td class="form-table__cell u-width-120px">
                  <label for="">Occupancy:</label>
                </td>
                <td class="form-table__cell u-width-180px">
                  <sm-autocomplete
                    [options]="occupancy"
                    [activeOption]="dwelling.utilizationTypeCode"
                    [required]="true"
                    [allowEmptyValue]="false"
                    [id]="'utilizationTypeCode'"
                    [name]="'utilizationTypeCode'"
                    (onSelect)="selected($event, dwelling, 'utilizationTypeCode')">
                  </sm-autocomplete>
                </td>
                <td class="form-table__cell u-width-70px"></td>
              </tr>

              <tr class="form-table__row" [ngClass]="{'is-required-field': refFamilies.ngModel?.invalid}">
                <td class="form-table__cell u-width-120px">
                  <label for="">Families:</label>
                </td>
                <td colspan="2" class="form-table__cell u-width-220px">
                  <div class="u-show-iblock u-width-50px">
                    <sm-autocomplete
                      #refFamilies
                      [options]="families"
                      [activeOption]="dwelling.familiesCount"
                      [required]="true"
                      [id]="'families'"
                      [name]="'families'"
                      [allowEmptyValue]="false"
                      (onSelect)="selected($event, dwelling, 'familiesCount')">
                    </sm-autocomplete>
                  </div>
                </td>
              </tr>

              <tr class="form-table__row">
                <td class="form-table__cell u-width-120px">
                  <label for="">Units:</label>
                </td>

                <td class="form-table__cell u-width-60px">
                  <sm-autocomplete
                    #refUnits
                    [id]="'townhouseUnitsCount'"
                    [options]="townhouseUnits"
                    [disabled]="townhouseUnitsFieldIsDisabled()"
                    [activeOption]="dwelling.townhouseUnitsCount"
                    [allowEmptyValue]="false"
                    [required]="townhouseUnitsFieldIsRequired()"
                    (onSelect)="selected($event, dwelling, 'townhouseUnitsCount')">
                  </sm-autocomplete>
                </td>
              </tr>
            </table>
          </div>
          <div class="col-xs-6">
            <table class="form-table">
              <tr class="form-table__row">
                <td class="form-table__cell">
                  <div class="u-show-iblock">
                    <label class="o-checkable">
                      <input type="checkbox" name="isVacant" id="isVacant" [(ngModel)]="dwelling.vacancyInd" (change)="selected({id: dwelling.vacancyInd}, dwelling, 'vacancyInd')">
                      <i class="o-btn o-btn--checkbox"></i>
                      <span>Vacant</span>
                    </label>
                  </div>
                </td>
              </tr>
              <tr class="form-table__row">
                <td class="form-table__cell">
                  <div class="u-show-iblock">
                    <label class="o-checkable">
                      <input type="checkbox" name="isNonOwnerOccupancy" id="isNonOwnerOccupancy" [(ngModel)]="dwelling.nonOwnerOccupancyInd" (change)="selected({id: dwelling.nonOwnerOccupancyInd}, dwelling, 'nonOwnerOccupancyInd')">
                      <i class="o-btn o-btn--checkbox"></i>
                      <span>Non Owner Occupancy</span>
                    </label>
                  </div>
                </td>
              </tr>
              <tr class="form-table__row">
                <td class="form-table__cell">
                  <div class="u-show-iblock">
                    <label class="o-checkable">
                      <input type="checkbox" name="isTownhouse" id="isTownhouse" [(ngModel)]="dwelling.designStyleTownhouseInd" (change)="selected({id: dwelling.designStyleTownhouseInd}, dwelling, 'designStyleTownhouseInd')">
                      <i class="o-btn o-btn--checkbox"></i>
                      <span>Townhouse</span>
                    </label>
                  </div>
                </td>
              </tr>
              <tr class="form-table__row">
                <td class="form-table__cell">
                  <div class="u-show-iblock">
                    <label class="o-checkable">
                      <input type="checkbox" name="isUnderConstruction" id="isUnderConstruction" [(ngModel)]="dwelling.underConstructionInd" (change)="selected({id: dwelling.underConstructionInd}, dwelling, 'underConstructionInd')">
                      <i class="o-btn o-btn--checkbox"></i>
                      <span>Under Construction</span>
                    </label>
                  </div>
                </td>
              </tr>
              <tr class="form-table__row">
                <td class="form-table__cell">
                  <div class="u-show-iblock">
                    <label class="o-checkable">
                      <input type="checkbox" name="isMobileHome" id="isMobileHome" [(ngModel)]="dwelling.mobileHomeInd" (change)="selected({id: dwelling.mobileHomeInd}, dwelling, 'mobileHomeInd')">
                      <i class="o-btn o-btn--checkbox"></i>
                      <span>Mobile Home</span>
                    </label>
                  </div>
                </td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
