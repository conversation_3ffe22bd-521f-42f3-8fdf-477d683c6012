<div class="row">
  <div class="col-xs-12">
    <div class="u-flex u-flex--to-middle">
      <div class="search">
        <input
          #searchInput
          [id]="'filter_NAME'"
          [(ngModel)]="searchQuery"
          (keydown.enter)="filter($event)"
          class="search__input search__input--md"
          type="text"
          placeholder="Search By Name"
          changeDetectionDelay
        />
        <button *ngIf="searchQuery && searchQuery.length > 0"
          (click)="reset($event)"
          name="cancel"
          class="reset__button"
        ></button> <button
        (click)="filter($event)"
        name="searchbtn"
        class="search__button"
      >
      </button>
      </div>

      <div *ngIf="lob !== 'AUTOB' && lob !== 'Commercial'">
        <label class="o-checkable u-spacing--left-2" [appDetectSystem] [removeRadio]="true">
            <input type="radio" name="nameType" value="Last"
              [(ngModel)]="nameType">
            <i class="o-btn o-btn--radio"></i>
            <span>Last Name</span>
          </label>
          <label class="o-checkable u-spacing--left-2" [appDetectSystem] [removeRadio]="true">
              <input type="radio" name="nameType" value="First"
                [(ngModel)]="nameType">
              <i class="o-btn o-btn--radio"></i>
              <span>First Name</span>
            </label>
     </div>

    </div>
  </div>
  </div>
  <span style="color: red" *ngIf="searchQuery && searchQuery.length < 3">Minimum search length is 3</span>
