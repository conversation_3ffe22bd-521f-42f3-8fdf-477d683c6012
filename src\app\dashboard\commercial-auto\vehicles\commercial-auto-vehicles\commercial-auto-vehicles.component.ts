
import {map, take} from 'rxjs/operators';
import { element } from 'protractor';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { Observable } from 'rxjs';
import { Component, OnDestroy, OnInit, ViewChild, ElementRef } from '@angular/core';
import { Quote } from 'app/app-model/quote';
import { Vehicle, CommercialType, CommercialVehicle, CommercialClassCodeLookup } from 'app/app-model/vehicle';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { VehiclesService } from 'app/dashboard/app-services/vehicles.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { FilterOption } from 'app/app-model/filter-option';
import { LocationData } from 'app/app-model/location';
import { LocationsService } from 'app/dashboard/app-services/locations.service';
import { ModalboxComponent } from 'app/shared/components/modalbox/modalbox.component';
import { RouteService } from '../../../../shared/services/route.service';
import { Town } from 'app/app-model/dwelling';
import { PremiumsService } from 'app/dashboard/auto/premiums/premiums.service';
import { BroadcastService } from '../../../../shared/services/broadcast.service';
import { getYear } from 'date-fns';

const CITY_OPTION_OUT_OF_STATE = 'Out of State';
export class CommercialVehicleForm {
  constructor(
    public id = '',
    public formType = '',
    public vehicleType = '',
    public vehicleTypeOptions = ['Truck', 'Trailer', 'Private Passenger', 'Public Vehicle', 'Special Vehicle'],
    public vin = '',
    public year = '',
    public yearOptions: string[] = [],
    public make = '',
    public model = '',
    public bodyType: FilterOption = new FilterOption(),
    public bodyTypeOptions: FilterOption[] = [],
    public unitDescription = '',
    public originalCostNew = '',
    public vehicleWeight = '',
    public plateNumber = '',
    public plateTypeOptions: FilterOption[] = [],
    public plateType: FilterOption = new FilterOption(),
    public radius: FilterOption = new FilterOption(),
    public radiusOptions = [
      new FilterOption('50', 'Local up to 50 miles'),
      new FilterOption('200', 'Intermediate 51-200 miles'),
      new FilterOption('201', 'Long Distance > 200 miles')
    ],
    public vehicleUse: FilterOption = new FilterOption(),
    public vehicleUseOptions: FilterOption[] = [],
    public vehicleSecondaryType = '',
    public vehicleSecondaryTypeOptions: FilterOption[] = [],
    public vehicleSecondaryUse = '',
    public vehicleSecondaryUseOptions: FilterOption[] = [],
    public classCode = '',
    public garaging: FilterOption = new FilterOption(),
    public garagingOptions: FilterOption[] = [],
    public classCodeErrorMsg= ''
  ) {
  }
}

@Component({
    selector: 'app-commercial-auto-vehicles',
    templateUrl: './commercial-auto-vehicles.component.html',
    styleUrls: ['./commercial-auto-vehicles.component.scss'],
    standalone: false
})
export class CommercialAutoVehiclesComponent implements OnInit, OnDestroy {
  @ViewChild('modalAddVehicle') public refModalAddVehicle: ModalboxComponent;
  @ViewChild('originalCostNew') originalCostDiv: ElementRef;
  // @ViewChild('leaveQuote') leaveQuote: LeaveQuoteComponent;

  // Business Modal
  selectedQuote: Quote;
  public vehicles: CommercialVehicle[] = [];
  quoteId: string;
  selectedQuotePreviousEffectiveDate: string;

  // Subscriptions
  subscriptionCommercialVehicles: any;
  subscriptionBodyTypes: any;
  subscriptionPlateTypes: any;
  subscriptionVehicleUseTypes: any;
  subscriptionVehicleSecondaryTypes: any;
  quoteSubscription: any;
  userDataSubscription: any;
  subscriptionQuoteLocations: any;

  // UI Modal
  public vehicleForm: CommercialVehicleForm = new CommercialVehicleForm();
  public classCodeLookup: CommercialClassCodeLookup = new CommercialClassCodeLookup();
  bodyTypes: CommercialType[] = [];
  public bodyTypeOptions: FilterOption[];
  plateTypes: CommercialType[] = [];
  public plateTypeOptions: FilterOption[];
  VehicleUseTypes: CommercialType[] = [];
  VehicleSecondaryTypes: any[] = [];
  public VehicleUseOptions: FilterOption[];
  public VehicleSecondaryOptions: FilterOption[];

  locations: LocationData[] = [];
  public LocationOptions: FilterOption[];
  public yearOptions: string[] = [];
  public makeOptions: string[] = [];
  newLocationData: LocationData = new LocationData();
  unitDescOptions: string[] = [];
  newLocation = false;
  optionsState;
  optionsCity;
  newLocationResId;
  constructor(
    private specsService: SpecsService,
    private storageService: StorageService,
    private commercialVehicleService: VehiclesService,
    private locationsService: LocationsService,
    private overlayLoaderService: OverlayLoaderService,
    private routeService: RouteService,
    private premiumsService: PremiumsService,
    private broadcastService: BroadcastService

  ) { }

  ngOnInit() {

    this.initialReset()
      .then(() => this.subscribeSelectedQuote())
      .then(() => this.initializeVehicleForm())
      .then(() => this.initStaticOptions())
      .then(() => {this.subscribeCommercialVehicles(); this.fromHintsAndWarnings(); })
      .then(() => this.initializeStaticData());
      this.subscribeRMVReportsBroadcast();
  }

  fromHintsAndWarnings() {
    if (location.href.includes('interact')) {
      const link = location.href;
     const index = link.indexOf('edit-') + 5;
     this.formAddVehicleEdit(link.substr(index));
    }
  }

  ngOnDestroy(): void {
    this.userDataSubscription && this.userDataSubscription.unsubscribe();
    this.quoteSubscription && this.quoteSubscription.unsubscribe();
    this.subscriptionCommercialVehicles && this.subscriptionCommercialVehicles.unsubscribe();
    this.subscriptionBodyTypes && this.subscriptionBodyTypes.unsubscribe();
    this.subscriptionPlateTypes && this.subscriptionPlateTypes.unsubscribe();
    this.subscriptionVehicleUseTypes && this.subscriptionVehicleUseTypes.unsubscribe();
    this.subscriptionVehicleSecondaryTypes && this.subscriptionVehicleSecondaryTypes.unsubscribe();
    this.subscriptionQuoteLocations && this.subscriptionQuoteLocations.unsubscribe();
  }

  private initialReset(): Promise<void> {
    return new Promise(resolve => {
      this.vehicles = [];
      resolve();
    });
  }

  private subscribeSelectedQuote(): void {
    this.quoteSubscription = this.storageService.getStorageData('selectedQuote').subscribe(
      res => {
        this.selectedQuote = res;
        this.quoteId = res.resourceId;
      }
    );
  }

  private subscribeRMVReportsBroadcast() {
     this.broadcastService.on('overlay.rmv.commercial.reports.applychanges')
      .subscribe(() => {
        this.subscribeCommercialVehicles();
      });
  }

  private subscribeCommercialVehicles(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.subscriptionCommercialVehicles && this.subscriptionCommercialVehicles.unsubscribe();

      this.subscriptionCommercialVehicles = this.storageService.getStorageData('comVehicles').subscribe((comVehicles: CommercialVehicle[]) => {
        if (comVehicles && comVehicles.length > 0) {
          this.vehicles = comVehicles;
          this.vehicles.forEach(veh => this.initCalculateClassCode(veh));
          resolve();
        } else {
          this.getComVehiclesFromAPI()
            .then(() => resolve()).then(() => { this.vehicles.forEach(veh => this.initCalculateClassCode(veh));
            });
        }
      });


    });
  }

private checkForNullLocation() {
    this.locations = this.locations.filter(val => {
       return val.city !== null;
    });
}

  private subscribeQuoteLocations(): void {
    this.subscriptionQuoteLocations = this.storageService.getStorageData('quoteLocations').subscribe((locations: LocationData[]) => {
      if (locations && locations.length > 0) {

        this.locations = locations;
        this.checkForNullLocation();
      } else {
        this.getLocationsFromAPI();
      }
    });
  }

  private getLocationsFromAPI() {

    if (this.locations && this.locations.length === 0) {
      this.locationsService.getLocationByUri('/quotes/' + this.quoteId + '/locations').pipe(take(1)).subscribe(res => {

        if (res && res.items && res.items.length > 0) {
          this.locations = res.items;
          this.checkForNullLocation();
          this.storageService.setStorageData('quoteLocations', this.locations);
        }
      });
    }
  }

  private initializeVehicleForm() {
    this.vehicleForm = new CommercialVehicleForm();
    this.initializeYearOptions();
    this.vehicleForm.yearOptions = this.yearOptions;
    this.vehicleForm.formType = 'ADD';
    this.newLocation = false;
    this.newLocationData = new LocationData();
    if (!this.bodyTypeOptions || this.bodyTypeOptions.length < 1) {
      this.loadBodyTypes();
      this.bodyTypeOptions = this.mapTypesToOptions(this.bodyTypes);
    }

    this.vehicleForm.bodyTypeOptions = this.bodyTypeOptions;

    if (!this.plateTypeOptions || this.plateTypeOptions.length < 1) {
      this.loadPlateTypes();
      this.plateTypeOptions = this.mapTypesToOptions(this.plateTypes);
    }

    this.vehicleForm.plateTypeOptions = this.plateTypeOptions;

    if (!this.VehicleUseOptions || this.VehicleUseOptions.length < 1) {
      this.loadVehicleUseTypes();
      this.VehicleUseOptions = this.mapTypesToOptions(this.VehicleUseTypes);

    }
      this.vehicleForm.vehicleUseOptions = this.VehicleUseOptions;

      if (!this.VehicleSecondaryOptions || this.VehicleSecondaryOptions.length < 1) {
        this.loadVehicleSecondaryTypes();
         this.VehicleSecondaryOptions = this.mapSecondaryTypesToOptions(this.VehicleSecondaryTypes);
      }
      this.vehicleForm.vehicleSecondaryTypeOptions = this.VehicleSecondaryOptions;

      this.subscribeQuoteLocations();
      this.LocationOptions = this.mapLocationsToOptions(this.locations);
      this.LocationOptions.push(new FilterOption ('NL', 'New Location'));

    this.vehicleForm.garagingOptions = this.LocationOptions;
  }
  private initializeStaticData() {
    this.initializeYearOptions();

    this.makeOptions = ['Acura', 'Alfa Romeo', 'AM General', 'American Coleman', 'American Iron Horse', 'American LaFrance', 'American Motors', 'Aprilia', 'Aro', 'Aston Martin', 'Asuna', 'ATK', 'Audi', 'Austin', 'AutoCar', 'Autocar Company', 'AutoCar LLC', 'Available', 'Avanti', 'AZURE DYNAMICS', 'Bailey', 'BASHAN', 'Bentley', 'Bering', 'Bering Trucks', 'Bertone', 'Big Bear Choppers', 'Big Dog', 'Blue Bird', 'BMW', 'Brockway', 'Brown', 'Buell', 'Bugatti', 'Buick', 'Bus & Coach INTL', 'Cadillac', 'Cagiva', 'Canada', 'CAN-AM', 'Capacity of Texas', 'CATERPILLAR', 'Chance', 'Chevrolet', 'Chrysler', 'Cline', 'Colet', 'Corbitt', 'Country Coach', 'CPI Motor Company', 'Crane Carrier', 'Crane Carrier Corporation', 'Crown Coach', 'Daewoo', 'Daihatsu', 'Dart', 'Datsun', 'DCX Sprinter', 'De Lorean', 'Derbi', 'DeSoto', 'Diamond Reo', 'Diamond T', 'Dina', 'Dodge', 'Ducati', 'Duplex', 'Eagle', 'Eagle Transit', 'ElDorado', 'Electric Vehicle Tech', 'Emergency One', 'E-One', 'ETON', 'Evobus', 'Excalibur', 'Fageol', 'Featherlite Mfg Inc', 'Federal', 'Federal Motors', 'Ferrari', 'Fiat', 'FISKER', 'Flexible', 'Flextruc', 'Ford', 'ForeTravel', 'Freeman', 'Freightliner', 'Freightliner Trucks', 'FWD', 'FWD Auto Company', 'GEM', 'General Motors Canada', 'General Vehicle', 'Genesis', 'Geo', 'Gersix', 'Giant', 'Gillig', 'GM', 'GMC', 'Gotfredson', 'Greenkraft Inc', 'Harley-Davidson', 'Hayes Truck', 'Hendrickson', 'Hino', 'Honda', 'Hug', 'Hummer', 'Husaberg', 'Husqvarna', 'Hyosung', 'Hyundai', 'Ibex', 'IC Corporation', 'Indian', 'Infiniti', 'International', 'Isuzu', 'Iveco', 'Jaguar', 'Jarrett', 'Jeep', 'Jeffedry Quad', 'John Deere', 'Kalmar Industries', 'Kawasaki', 'Kenworth', 'Kia', 'Knox', 'KTM', 'KYMCO', 'Lada', 'LaForza', 'Lamborghini', 'Lancia', 'Land Rover', 'Lexus', 'Liberty', 'Lincoln', 'Lotus', 'Mack', 'Mack Trucks', 'Magirus', 'Mahindra', 'MARMON', 'Marmon-Herrington', 'Maserati', 'Maybach', 'Mazda', 'MCI (Les Auto Bus)', 'MCLAREN', 'Mercedes Benz', 'Mercury', 'Merkur', 'Mini', 'Mitsubishi', 'Mitsubishi Fuso', 'Moreland', 'Moto Guzzi', 'Motor Coach Mnd Inc', 'Motorrad Zwieradwerk', 'MV AGUSTA', 'NeoPlan', 'New Flyer', 'Nissan', 'Nissan Diesel', 'Nissan/Datsun', 'North American Bus', 'Nova', 'Oldsmobile', 'Ontario', 'Orange EV', 'Orion', 'OshKosh', 'Other British', 'Paccar', 'Passport', 'Paymaster', 'PeterBilt', 'Peugeot', 'Piaggio', 'Pierce', 'Pinifarina', 'Plymouth', 'Polaris', 'Pontiac', 'Porsche', 'Prevost', 'RAM', 'Ramirez', 'Rapid', 'Relay', 'Renault', 'Reo', 'Republic', 'Riker', 'Roadmaster Rail', 'Rolls Royce', 'Rolls-Royce', 'Rover', 'Saab', 'Saturn', 'Scania', 'Scimitar', 'Scion', 'Scot', 'Seagraves', 'Skoda', 'Smart Cars', 'SMITH', 'Spangler', 'Spartan', 'SSI', 'Sterling', 'Sterling Trucks', 'Stewart', 'Stewart-Stevenson', 'Studebaker', 'Stutz', 'Subaru', 'Suzuki', 'SYM', 'Tesla', 'Tesla Motors', 'Thomas', 'TMC', 'Toyota', 'Traffic', 'Triumph', 'TVR', 'UD Trucks', 'UNIMOG', 'UtiliMaster', 'Van Hool', 'Vector', 'Vespa', 'Victory', 'Volkswagen', 'Volvo', 'Volvo Trucks', 'Walter', 'Western RV', 'Western Star', 'Western Star Trucks', 'White', 'WhiteGMC', 'Winnebago', 'WorkHorse', 'Yamaha', 'YIBEN', 'Yugo', 'ZELIGSON', 'ZERO MOTORCYCLES INC', 'Zongshen'];
  }

  private initializeYearOptions() {
    const currentYear: number = getYear(new Date());
    for (let i = currentYear - 20; i < currentYear + 3; i++) {
      this.yearOptions.push(i.toString());
    }
  }

  private getComVehiclesFromAPI(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.vehicles && this.vehicles.length === 0) {
        this.commercialVehicleService.getVehiclesListByUri('/quotes/' + this.quoteId + '/comVehicles').pipe(take(1)).subscribe(res => {
          if (res.items && res.items.length === 0) {
            this.vehicles = [];
          } else {
            this.vehicles = res.items;
            this.populateLocationData();
            this.storageService.setStorageData('comVehicles', this.vehicles);
          }
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  private populateLocationData() {
    if (this.vehicles && this.vehicles.length > 0) {
      this.vehicles.map(veh => {
        if (veh && veh.garagingAddress && veh.garagingAddress !== '' && veh.garagingAddress.meta && veh.garagingAddress.meta.href) {
          if (this.locations) {
            const filterLocation = this.locations.filter(l => l.meta.href === veh.garagingAddress.meta.href);
            veh.garagingAddress = filterLocation[0];
          }
        }
      });
    }
  }

  private addVehicleResource(): Observable<any> {
    return this.commercialVehicleService.createVehicle(this.quoteId, {}, 'commercial', false).pipe(map(data => {
      this.vehicles.push(data);
      this.storageService.setStorageData('comVehicles', this.vehicles);
      return data;

    }
    ));
  }

  private updateVehicleResource(veh): Observable<any> {
    return this.commercialVehicleService.updateCommercialVehicle(this.quoteId, veh.resourceId, veh, false).pipe(map(data => {
      this.storageService.updateOrAddDataItem('comVehicles', veh);
      this.premiumsService.rerateAll();
      return data;
    }
    ));
  }

  initCalculateClassCode(vehicle: CommercialVehicle) {
    this.mapVehicleFormToClassCodeLookupInit(vehicle);
    if (vehicle.radius && vehicle.usage && vehicle.vehicleType) {
     this.commercialVehicleService.calculateClassCode(this.classCodeLookup).pipe(take(1)).subscribe((response: any) => {
      vehicle.classCode = response.computedClassCode;
    }, error => {
        this.vehicleForm.classCodeErrorMsg = 'Error calculating class code';
    });
  }
}

  public calculateClassCode() {
    this.vehicleForm.classCodeErrorMsg = '';
    this.mapVehicleFormToClassCodeLookup();
    if (this.vehicleForm.radius && this.vehicleForm.vehicleUse && this.vehicleForm.vehicleType) {
    return this.commercialVehicleService.calculateClassCode(this.classCodeLookup).pipe(take(1)).subscribe((response: any) => {
      this.vehicleForm.classCode = response.computedClassCode;
    }, error => {
        this.vehicleForm.classCodeErrorMsg = 'Error calculating class code';
    });
  }
  }
  mapVehicleFormToClassCodeLookup() {
    this.classCodeLookup.vehicleType = this.vehicleForm.vehicleType;
    this.classCodeLookup.unitDescription = this.vehicleForm.unitDescription;
    this.classCodeLookup.vehicleWeight = this.vehicleForm.vehicleWeight;
    this.classCodeLookup.radius = this.vehicleForm.radius.id;
    this.classCodeLookup.usage = this.vehicleForm.vehicleUse.id;
    this.classCodeLookup.secondaryClassCd = this.vehicleForm.vehicleSecondaryType;
    this.classCodeLookup.secondaryClassUsage = this.vehicleForm.vehicleSecondaryUse;
  }

  mapVehicleFormToClassCodeLookupInit(veh: CommercialVehicle) {
    this.classCodeLookup.vehicleType = veh.vehicleType;
    this.classCodeLookup.unitDescription = veh.unitDescription;
    this.classCodeLookup.vehicleWeight = veh.vehicleWeight;
    this.classCodeLookup.radius = veh.radius;
    this.classCodeLookup.usage = veh.usage;
    this.classCodeLookup.secondaryClassCd = veh.secondaryClassCd;
    this.classCodeLookup.secondaryClassUsage = veh.secondaryClassUsage;
  }

  public numbersOnly(event) {
    const digits = /^[0-9]{1}$/;
    const multiKey = event.ctrlKey || event.metaKey;
    const keyNormalized = event.key.toLocaleLowerCase();
    this.originalCostDiv.nativeElement.classList.contains('has-warning-focus')
     && parseInt(this.vehicleForm.originalCostNew) > 1 ? this.originalCostDiv.nativeElement.classList.remove('has-warning-focus') : '';
    if (!(
      keyNormalized === 'backspace' ||
      keyNormalized === 'delete' ||
      keyNormalized === 'tab' ||
      keyNormalized === 'arrowleft' ||
      keyNormalized === 'left' ||
      keyNormalized === 'arrowright' ||
      keyNormalized === 'right' ||
      keyNormalized === 'a' && multiKey ||
      keyNormalized === 'z' && multiKey ||
      keyNormalized === 'c' && multiKey ||
      keyNormalized === 'v' && multiKey ||
      digits.test(event.key)
    )) { event.preventDefault(); }
  }

  public showAddVehicle(modalAddVehicle) {
      this.initializeVehicleForm();
    this.refModalAddVehicle.openModalbox();

  }



  public selectVehicleType($event, edit = false): void {
    this.vehicleForm.vehicleType = $event.text;
    this.updateUnitDescOptions($event.text);
    this.calculateClassCode();
  }

  public selectVehicleUse($event, edit = false): void {
    this.vehicleForm.vehicleUse = $event.id;
    this.calculateClassCode();
  }

  public selectSecondaryType($event, edit = false): void {
    this.vehicleForm.vehicleSecondaryType = $event.id;
    this.updateSecondaryUse($event.id);
    this.calculateClassCode();

  }

  public selectSecondaryUse($event, edit = false): void {
    this.vehicleForm.vehicleSecondaryUse = $event.id;
    this.calculateClassCode();
  }

  public selectVehicleYear($event, edit = false): void {
    this.vehicleForm.year = $event.id;
  }

  public selectVehicleMake($event, edit = false): void {
    this.vehicleForm.make = $event.id;
  }

  private updateUnitDescOptions(text: any) {
    this.unitDescOptions = this.getUnitDescOptions(text);
  }

  private updateSecondaryUse(text) {
     this.getSecondaryUse(text);
  }



  private loadBodyTypes() {
    if (this.bodyTypes && this.bodyTypes.length > 0) {
      return;
    }
    this.subscriptionBodyTypes = this.storageService.getStorageData('commercialAutoBodyTypes').subscribe((bodyTypes: CommercialType[]) => {
      if (bodyTypes && bodyTypes.length > 0) {
        this.bodyTypes = bodyTypes;
      } else {
        this.getBodyTypesFromAPI();
      }
    });
  }

  private loadPlateTypes() {
    if (this.plateTypes && this.plateTypes.length > 0) {
      return;
    }
    this.subscriptionPlateTypes = this.storageService.getStorageData('commercialAutoPlateTypes').subscribe((plateTypes: CommercialType[]) => {
      if (plateTypes && plateTypes.length > 0) {
        this.plateTypes = plateTypes;
      } else {
        this.getPlateTypesFromAPI();
      }
    });
  }

  private loadVehicleUseTypes() {
    if (this.VehicleUseTypes && this.VehicleUseTypes.length > 0) {
      return;
    }
    this.subscriptionVehicleUseTypes = this.storageService.getStorageData('commercialVehicleUseTypes').subscribe((vehicleUseTypes: CommercialType[]) => {
      if (vehicleUseTypes && vehicleUseTypes.length > 0) {
        this.VehicleUseTypes = vehicleUseTypes;
      } else {
        this.getVehicleUseTypesFromAPI();
      }
    });
  }

  private getVehicleUseTypesFromAPI() {
    this.specsService.getVehicleUseTypes('AUTOB').pipe(take(1)).subscribe(res => {

      if (res.items && res.items.length === 0) {
      } else {
        this.VehicleUseTypes = res.items;
        this.storageService.setStorageData('commercialVehicleUseTypes', this.VehicleUseTypes);
      }
    });
  }

  private loadVehicleSecondaryTypes() {
    if (this.VehicleSecondaryTypes && this.VehicleSecondaryTypes.length > 0) {
      return;
    }
    this.subscriptionVehicleUseTypes = this.storageService.getStorageData('commercialVehicleSecondaryTypes').subscribe((vehicleSecondType) => {
      if (vehicleSecondType && vehicleSecondType.length > 0) {
        this.VehicleSecondaryTypes = vehicleSecondType;
      } else {
        this.getVehicleSecondaryTypesFromAPI();
      }
    });
  }

  private getVehicleSecondaryTypesFromAPI() {
    this.specsService.getVehicleSecondaryTypes('AUTOB').pipe(take(1)).subscribe(res => {

      if (res.items && res.items.length === 0) {
      } else {

        this.VehicleSecondaryTypes = res.items;
        this.storageService.setStorageData('commercialVehicleSecondaryTypes', this.VehicleSecondaryTypes);
      }
    });
  }

  private getBodyTypesFromAPI() {
    this.specsService.getBodyTypes('AUTOB').pipe(take(1)).subscribe(res => {

      if (res.items && res.items.length === 0) {
      } else {
        this.bodyTypes = res.items;
        this.storageService.setStorageData('commercialAutoBodyTypes', this.bodyTypes);
      }
    });
  }

  private getPlateTypesFromAPI() {
    this.specsService.getPlateTypes('AUTOB').pipe(take(1)).subscribe(res => {

      if (res.items && res.items.length === 0) {
      } else {
        this.plateTypes = res.items;
        this.storageService.setStorageData('commercialAutoPlateTypes', this.plateTypes);
      }
    });
  }

  private mapTypesToOptions(types: CommercialType[]): FilterOption[] {

    if (!types) {
      return;
    }

    return types.map(item => ({
      id: item.value,
      text: item.name
    }));
  }

  private mapSecondaryTypesToOptions(types): FilterOption[] {
    if (!types) {
      return;
    }
    return types.map(item => ( {
      id: item.type.value,
      text: item.type.name
    }));
  }
  private mapLocationsToOptions(locations: LocationData[]): FilterOption[] {
    if (!locations) {
      return;
    }

    return locations.map(item => ({
      id: item.resourceId,
      text: item.address1 + ', ' + item.city,
      data: item
    }));
  }

  public getUnitDescOptions(vehType): string[] {
    if (this.vehicleForm.vehicleType && this.vehicleForm.vehicleType !== '') {
      switch (this.vehicleForm.vehicleType) {
        case 'Truck':
          return ['Truck', 'Truck Tractor'];
        case 'Trailer':
          return ['Semitrailers', 'Trailers (>2,000 lbs)', 'Utility Trailers (2,000 lbs or less)'];
        case 'Private Passenger':
          return ['Private Passenger'];
        case 'Public Vehicle':
          return ['Taxi', 'Limousine', 'Car Services', 'Bus', 'Van Pools'];
        case 'Special Vehicle':
          return ['Ambulance',
            'Antiques',
            'Driver Training',
            'Fire Department',
            'Funeral Director',
            'Law Enforcement',
            'Leasing Concerns',
            'Mobile Home/Motor Home',
            'Motorcycles',
            'Special or Mobile Equipment',
            'Golfmobiles',
            'Snowmobiles'];
        default:
          return [''];
      }
    } else {
      return [''];
    }
  }

  public getSecondaryUse(type) {
     const value = this.VehicleSecondaryTypes.find(x => x.type.value === type);
     const array = [];
     if (value) {
       value.usage.forEach(element => {
       array.push(new FilterOption(element.value, element.name));
     });
     }

     this.vehicleForm.vehicleSecondaryUseOptions = array;
  }

  public selectBodyType($event, edit = false): void {
    this.vehicleForm.bodyType = new FilterOption($event.id, $event.text);
  }

  public selectUnitDesc($event, edit = false): void {
    console.log($event);
    this.vehicleForm.unitDescription = $event.text;
    this.calculateClassCode();
  }

  public selectPlateType($event, edit = false): void {
    this.vehicleForm.plateType = new FilterOption($event.id, $event.text);
  }

  public selectRadiusOption($event, edit = false): void {
    this.vehicleForm.radius = new FilterOption($event.id, $event.text);
    this.calculateClassCode();
  }
  public selectVehicleUseOption($event, edit = false): void {
    this.vehicleForm.vehicleUse = new FilterOption($event.id, $event.text);
  }

  public selectGaragingOption($event, edit = false): void {
    if ($event.id !== 'NL') {
    this.vehicleForm.garaging = new FilterOption($event.id, $event.text, $event.selectedOption.data);
    this.newLocation = false;
    } else {this.newLocation = true;
      this.locationsService.createLocation(this.quoteId).subscribe(data => {
        this.locations.push(data);
        this.newLocationData = data;
        this.storageService.setStorageData('quoteLocations', this.locations);
      });
    }
  }

  public formAddVehicle(addAnother: boolean) {
    if (this.vehicleForm) {
      this.overlayLoaderService.showLoader();
      this.addVehicleResource()
        .toPromise()
        .then((veh: CommercialVehicle) => {
          this.mapFormToVehicle(veh);
          return this.updateVehicleResource(veh).toPromise();
        })
        .then((addedVeh: CommercialVehicle) => {
          return this.subscribeCommercialVehicles();
        })
        .then(() => {
          if (addAnother) {
            this.initializeVehicleForm();
          } else {
            this.refModalAddVehicle.closeModalbox();
          }
          this.overlayLoaderService.hideLoader();
        })
        .catch(err => {
          console.log('ERROR: ', err);
          this.overlayLoaderService.hideLoader();
        });
    }
  }


  public formAddVehicleEdit(id: string) {
    if (id && id !== '') {
      const filterVeh = this.vehicles.find(v => v.resourceId === id);
      if (filterVeh) {
        this.mapVehicleToForm(filterVeh);
        this.vehicleForm.formType = 'UPDATE';
        this.refModalAddVehicle.openModalbox();
      }
    }
  }

  public formUpdateVehicle() {
    const filterVeh = this.vehicles.find(v => v.resourceId === this.vehicleForm.id);
    this.mapFormToVehicle(filterVeh);
    this.updateVehicleResource(filterVeh).subscribe(vehadded => {
      this.subscribeCommercialVehicles();
      this.refModalAddVehicle.closeModalbox();
    });
  }

  public formAddVehicleDelete(id: string) {
    const filterVeh = this.vehicles.find(v => v.resourceId === id);

    this.overlayLoaderService.showLoader();
    this.commercialVehicleService.deleteComVehicleByUri(filterVeh.meta.href).subscribe(
      data => {
        let vehicleToRemove;
        let vehicleToRemoveIndex;
        if (data) {
          vehicleToRemove = this.vehicles.find((element, index) => {
            if (element.resourceId === filterVeh.resourceId) {
              vehicleToRemoveIndex = index;
              return true;
            }
          });

          this.vehicles.splice(vehicleToRemoveIndex, 1);
          this.storageService.setStorageData('comVehicles', this.vehicles);
          this.overlayLoaderService.hideLoader();
        }
      });
  }

  private mapFormToVehicle(veh: CommercialVehicle) {
    if (this.vehicleForm && veh) {
      veh.resourceId = (this.vehicleForm.id && this.vehicleForm.id != null) ? this.vehicleForm.id : veh.resourceId;
      veh.vehicleType = this.vehicleForm.vehicleType;
      veh.vin = this.vehicleForm.vin;
      veh.year = this.vehicleForm.year;
      veh.make = this.vehicleForm.make;
      veh.model = this.vehicleForm.model;
      veh.bodyTypeCode = this.vehicleForm.bodyType.id;
      veh.unitDescription = this.vehicleForm.unitDescription;
      veh.priceValue = this.vehicleForm.originalCostNew;
      veh.vehicleWeight = this.vehicleForm.vehicleWeight;
      veh.licensePlate = this.vehicleForm.plateNumber;
      veh.plateType = this.vehicleForm.plateType.id;
      veh.radius = this.vehicleForm.radius.id;
      veh.usage = this.vehicleForm.vehicleUse.id;
      veh.classCode = this.vehicleForm.classCode;
      this.newLocation ? veh.garagingAddress = this.newLocationData : veh.garagingAddress = this.vehicleForm.garaging.data;
      veh.secondaryClassUsage = this.vehicleForm.vehicleSecondaryUse;
      veh.secondaryClassCd = this.vehicleForm.vehicleSecondaryType;

    }
  }

  public mapVehicleToForm(veh: CommercialVehicle) {
    this.initializeVehicleForm();
    if (this.vehicleForm && veh) {
      this.vehicleForm.id = veh.resourceId;
      this.vehicleForm.vehicleType = veh.vehicleType;
      this.vehicleForm.vin = veh.vin;
      this.vehicleForm.year = veh.year;
      this.vehicleForm.make = veh.make;
      this.vehicleForm.model = veh.model;
      this.vehicleForm.bodyType.id = veh.bodyTypeCode;
      this.vehicleForm.unitDescription = veh.unitDescription;
      this.vehicleForm.originalCostNew = veh.priceValue;
      this.vehicleForm.vehicleWeight = veh.vehicleWeight;
      this.vehicleForm.plateNumber = veh.licensePlate;
      this.vehicleForm.plateType.id = veh.plateType;
      this.vehicleForm.radius.id = veh.radius;
      this.vehicleForm.vehicleUse.id = veh.usage;
      this.vehicleForm.classCode = veh.classCode;
      this.vehicleForm.garaging = this.getGaragingOption(veh.garagingAddress);
      this.unitDescOptions = this.getUnitDescOptions(veh.vehicleType);
      this.vehicleForm.vehicleSecondaryType = veh.secondaryClassCd;
      this.vehicleForm.vehicleSecondaryUse = veh.secondaryClassUsage;
      this.updateSecondaryUse(veh.secondaryClassCd);
    }
  }

  getGaragingOption(address: any): FilterOption {
    if (this.locations && address) {
      const filterLocation = this.locations.filter(l => l.meta.href === address.meta.href);
      if (filterLocation && filterLocation.length > 0) {
        return new FilterOption(filterLocation[0].resourceId, filterLocation[0].city + ',' + filterLocation[0].state, filterLocation[0]);
      }
    }
    return new FilterOption();
  }

  private initStaticOptions(): Promise<void[]> {
    return Promise.all([
      this.setOptionsLocationStates(),
      this.setOptionsLocationCity(this.selectedQuote.state)
    ]);
  }

  private setOptionsLocationCity(state: string): Promise<void> {
    this.optionsCity = [new FilterOption(CITY_OPTION_OUT_OF_STATE, CITY_OPTION_OUT_OF_STATE)];
    const tmpOptions = [new FilterOption(CITY_OPTION_OUT_OF_STATE, CITY_OPTION_OUT_OF_STATE)];

    return new Promise((resolve, reject) => {
      this.specsService.getTownsList(state, '', true).pipe(
        take(1))
        .subscribe(
          res => {
            Array.prototype.push.apply(tmpOptions, this.helperTownsToCitiesOptions(res.items));
            this.optionsCity = tmpOptions;
            resolve();
          },
          err => reject(err)
        );
    });
  }

  private setOptionsLocationStates(): Promise<void> {
    return new Promise((resolve => {
      // tslint:disable-next-line:max-line-length
      this.optionsState = ['AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DC', 'DE', 'FL', 'GA', 'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MD', 'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ', 'NM', 'NY', 'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC', 'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY'];
      resolve();
    }));
  }

  public onAddressDataChange(location, $event) {
    this.onLocationDataChangeUpdate(this.newLocationData);
  }

  public onCityDataChange(location, $event) {
    this.newLocationData.city = $event.text;
    location.city = $event.text;
    this.onLocationDataChangeUpdate(this.newLocationData);

   // this.onLocationDataChangeUpdate(location);
  }

  public onStateDataChange(location, $event) {
    this.newLocationData.state = $event.text;
    this.onLocationDataChangeUpdate(this.newLocationData);
  }

  public onZipDataChange(location, $event) {
    this.onLocationDataChangeUpdate(this.newLocationData);
  }


  private onLocationDataChangeUpdate(location?: LocationData): Promise<void> {
    return new Promise((resolve, reject) => {
      this.locationsService.updateLocation(this.quoteId, this.newLocationData.resourceId, this.newLocationData).subscribe(data => {
        const updatedItem = this.locations.find(x => x.resourceId === this.newLocationData.resourceId);
        const index = this.locations.indexOf(updatedItem);
        this.locations[index] = data;
        this.storageService.setStorageData('quoteLocations', this.locations);
        resolve();
      }, err => reject(err));
    });
  }

    // Convert Towns objects to Cities options
    private helperTownsToCitiesOptions(townsList: Town[]): FilterOption[] {
      let options: FilterOption[] = [];

      options = townsList.map(town => {
        const option = new FilterOption();
        option.id = town.townName;
        option.text = town.townName;
        option.data = town;

        return option;
      });

      return options;
    }

    formValid() {
     let invalidForm = false;
     let validateGarage;
     let validateType;
     let validateUse;
    const validateVin = this.vinValidate(this.vehicleForm.vin);
    validateGarage = this.garageValidate();
    validateType = this.vehicleTypeValidate();
    validateUse = this.vehicleUseValidate();
     validateGarage && validateType && validateVin && validateUse
     && this.costValidate(this.vehicleForm.originalCostNew) ? invalidForm = false :  invalidForm = true;
      return invalidForm;

    }


    garageValidate() {
      let validateGarage;
      if (this.newLocation) {
        this.newLocationData.address1 === '' || this.newLocationData.address1 === null
        ? validateGarage = false : validateGarage = true;
      } else {
        this.vehicleForm.garaging.text === null ||
         this.vehicleForm.garaging.text === '' ? validateGarage = false : validateGarage = true;
      }
      return validateGarage;

    }

    vehicleTypeValidate() {
     let validateType;
      this.vehicleForm.vehicleType === null || this.vehicleForm.vehicleType === '' ? validateType = false : validateType = true;
      return validateType;
    }

    vehicleUseValidate() {
      let validateUse;
       this.vehicleForm.vehicleUse.id === null || this.vehicleForm.vehicleUse.id === ''  ? validateUse = false : validateUse = true;
      return validateUse;
     }

    public vinValidate(vin) {
      const validVin = this.commercialVehicleService.vinValidate(vin);
      const validPartialVin = (vin && vin.length === 10);
      return validVin;
    }

    costValidate(originalCost) {
      return originalCost > 0;
    }



}
