import { WARNING_GROUPS, WarningDefinitionI, AdditionalDataQuoteInfo} from 'app/hints-and-warnings/model/warnings';
import { Quote } from 'app/app-model/quote';
import { Validate } from 'app/hints-and-warnings/validators';

import { differenceInDays } from 'date-fns';

function requiredField(value):boolean {
  if(value == undefined || value == null) {
    return true;
  }
  value = String(value);

  return value.trim().length <= 0 || !value;
}

function requiredFieldExcludeCommercial(value,fullObj,additional):boolean {
  if(fullObj.lob !== 'AUTOB') {
    if(value == undefined || value == null) {
    return true;
  }
  value = String(value);

  return value.trim().length <= 0 || !value;
  }

}

function requiredDateField(value):boolean {
  return requiredField(value) || value == 'Invalid date';
}

function generateViewUrl():string {
  return  '{{current_url}}?overlay=info&type=quote';
}

/**
 * Validation for Quote data
 * For Model: Quote
 */

const quoteInfoSaveData:WarningDefinitionI = {
  id: 'lastModifiedDate',
  deepId: 'lastModifiedDate',
  viewUri: generateViewUrl,
  viewFieldId: 'quoteInfoSaveData',
  warnings: [{
    label: (value, fullObj) => 'Required Quote Save Date.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const quoteInfoLob:WarningDefinitionI = {
  id: 'lob',
  deepId: 'lob',
  viewUri: generateViewUrl,
  viewFieldId: 'quoteInfoLob',
  warnings: [{
    label: (value, fullObj) => 'Required Quote LOB.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const quoteInfoRatingState:WarningDefinitionI = {
  id: 'state',
  deepId: 'state',
  viewUri: generateViewUrl,
  viewFieldId: 'quoteInfoRatingState',
  warnings: [{
    label: (value, fullObj) => 'Required Quote Rating State.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const quoteInfoAgencyContact:WarningDefinitionI = {
  id: 'agent',
  deepId: 'agent',
  viewUri: generateViewUrl,
  viewFieldId: 'quoteInfoAgencyContact',
  warnings: [{
    label: (value, fullObj) => 'Required Quote Agency Contact.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const quoteInfoPolicyPeriod:WarningDefinitionI = {
  id: 'policyPeriod',
  deepId: 'policyPeriod',
  viewUri: generateViewUrl,
  viewFieldId: 'quoteInfoPolicyPeriod',
  warnings: [{
    label: (value, fullObj) => 'Required Quote Policy Period.',
    condition: requiredFieldExcludeCommercial,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

// Arbella
const quoteInfoEffectiveDateCarriers = ['13'];
const quoteInfoEffectiveDate:WarningDefinitionI = {
  id: 'effectiveDate',
  deepId: 'effectiveDate',
  viewUri: generateViewUrl,
  viewFieldId: 'quoteInfoEffectiveDate',
  warnings: [{
    label: (value, fullObj) => 'Required Quote Effective Date.',
    condition: requiredDateField,
    group: WARNING_GROUPS.general,
    carriers: []
  },
  {
    label: (value, fullObj) => 'Effective Date cannot be more than 30 days in the past.',
    condition: (value, fullObj:Quote, additionalData: AdditionalDataQuoteInfo) => {
      // https://bostonsoftware.atlassian.net/browse/SPRC-469
      if (value && fullObj.lob === 'AUTOP') {
        const difference = differenceInDays(new Date(), new Date(value));
        // https://bostonsoftware.atlassian.net/browse/SPRC-606
        const isNotRenewal: boolean = (additionalData.policyHistory && additionalData.policyHistory.quoteThisCarrierAs !== 'Renewal');

        return isNotRenewal
          && Validate.isRequiredForSelectedPlans(quoteInfoEffectiveDateCarriers, additionalData.quoteSelectedPlansIds)
          && difference > 30;
      }

      return false;
    },
    group: WARNING_GROUPS.carrier,
    carriers: quoteInfoEffectiveDateCarriers // Arbella
  }]
};

const quoteInfoExpirationDate: WarningDefinitionI = {
  id: 'expirationDate',
  deepId: 'expirationDate',
  viewUri: generateViewUrl,
  viewFieldId: 'quoteInfoExpirationDate',
  warnings: [{
    label: (value, fullObj) => 'Required Quote Expiration Date.',
    condition: requiredDateField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};



export const WARNINGS_DEFINITIONS_INFO_QUOTE: WarningDefinitionI[] = [
  // quoteInfoSaveData,
  quoteInfoLob,
  quoteInfoRatingState,
  quoteInfoAgencyContact,
  quoteInfoPolicyPeriod,
  quoteInfoEffectiveDate,
  quoteInfoExpirationDate
];
