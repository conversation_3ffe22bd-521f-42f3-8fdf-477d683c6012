import { async, ComponentFixture, inject, TestBed } from '@angular/core/testing';

import {
    HOME_GENERAL_POLICY_OPTIONS
} from 'testing/data/specs/rating-coverages/HOME/policy-options-general';
import { StubOptionsViewComponent } from 'testing/stubs/components/options-view.component';

import { CoverageItemParsed } from 'app/app-model/coverage';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Helpers } from 'app/utils/helpers';

import { DwellingGeneralOptionsComponent } from './dwelling-general-options.component';

describe('DwellingGeneralOptionsComponent', () => {
  let component: DwellingGeneralOptionsComponent;
  let fixture: ComponentFixture<DwellingGeneralOptionsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [
        DwellingGeneralOptionsComponent,
        StubOptionsViewComponent
      ],
      providers: [
        StorageService
      ]
    })
    .compileComponents();
  }));

  beforeEach(inject(
    [StorageService],
    (storageService: StorageService) => {
      const parsed = Helpers.deepClone(HOME_GENERAL_POLICY_OPTIONS.items).map((item) => {
        const p = new CoverageItemParsed();
        Object.assign(p, item);
        return p;
      });
      storageService.setStorageData('dwellingGeneralOptionsParsed', parsed);

      fixture = TestBed.createComponent(DwellingGeneralOptionsComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
  }));

  it('should be created', () => {
    expect(component).toBeTruthy();
  });
});
