<section class="section section--compact u-spacing--2-5">
    <div class="row">
        <h1 class="o-heading" style="color: #0b71ac; padding-left: 20px;">Vehicles</h1>
      </div>
      <div class="row u-spacing--1">
        <div class="col-xs-12">
          <div class="box box--silver">
      <div class="row" *ngFor="let vehicle of vehicleList;let index = index" style="padding-bottom:1rem">
          <div class="col-lg-1 col-md-2">Vehicle {{index + 1}}</div>
          <div class="col-lg-2 col-md-2">
             <sm-autocomplete [(ngModel)]="vehicle.lookupType" (ngModelChange)="clearOtherField(vehicle)" [options]="lookupOptions" id="lookup{{index}}" name="lookupType{{vehicle.uuid}}" [activeOption]="vehicle.lookupType"></sm-autocomplete>
          </div>
          <div class="col-lg-3 col-md-2" *ngIf="vehicle.lookupType === 'plate'; then plateTemplate else vinTemplate"></div>
          <ng-template #plateTemplate>
            <div class="col-lg-2 col-md-2">
            <input required [(ngModel)]="vehicle.plateNumber" id="plateNum_{{index}}" name="plateNum{{vehicle.uuid}}" placeholder="Plate Number">
            </div>
            <div class="col-lg-3 col-md-3">
                <sm-autocomplete
      [options]="plateTypeOptions"
      [activeOption]="'PAN'"
      [searchFromBegining]="true"
      name="plateType{{vehicle.uuid}}"
      [(ngModel)]="vehicle.plateType"
      >
    </sm-autocomplete>
            </div>
            <div class="col-lg-1">
              <button *ngIf="index>0 && vehicleList?.length > 1" type="button" (click)="deleteVehicle(index)" class="o-btn o-btn--action o-btn--i_cancel u-t-size--1-7rem modal-delete-driver" tabindex="-1"></button>
            </div>
          </ng-template>
      <ng-template #vinTemplate>
        <div class="col-lg-5">
            <input type="text" id="vin_{{index}}" name="rmvLookUpVin{{vehicle.uuid}}"
            placeholder="Vin"
            required
        #refInputRmvVin
        [(ngModel)]="vehicle.vin"
        >
        </div>
        <div class="col-lg-1">
          <button *ngIf="index>0 && vehicleList?.length > 1" type="button" (click)="deleteVehicle(index)" class="o-btn o-btn--action o-btn--i_cancel u-t-size--1-7rem modal-delete-driver" tabindex="-1"></button>
        </div>
      </ng-template>
      </div>
      <div class="row">
        <div class="offset-md-3">
          <button  (click)="addVehicle();" type="button" class="o-btn o-btn--action o-btn--i_round-plus u-color-pelorous">Add Vehicle</button>
        </div>
    </div>
      </div>
      </div>
      </div>
</section>
