import { inject, TestBed } from '@angular/core/testing';

import { expect } from 'testing/helpers/all';

import { FilterOption } from 'app/app-model/filter-option';

import { DatesService } from './dates.service';

describe('Service: Dates', () => {
  let service: DatesService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [DatesService]
    });

    jasmine.clock().mockDate(new Date(2017, 10, 27, 15, 44, 23, 237));
  });

  beforeEach(inject([DatesService], (_service: DatesService) => {
    service = _service;
  }));

  it('can instantiate service when inject service', () => {
    expect(service).toBeTruthy();
  });

  it('allows to get available date types as FilterOptions', () => {
    const options: FilterOption[] = service.getDatesTypesOptions();

    expect(options).toEqual([
      jasmine.objectContaining({ id: 'saved' }),
      jasmine.objectContaining({ id: 'effective' }),
      jasmine.objectContaining({ id: 'expiration' })
    ]);
  });

  it('allows to get available date range types as FilterOptions', () => {
    const options: FilterOption[] = service.getDateRangesOptions();

    expect(options).toEqual([
      jasmine.objectContaining({ id: 'all' }),
      jasmine.objectContaining({ id: 'custom' }),
      jasmine.objectContaining({ id: 'today' }),
      jasmine.objectContaining({ id: 'yesterday' }),
      jasmine.objectContaining({ id: 'thisweek' }),
      jasmine.objectContaining({ id: 'thismonth' }),
      jasmine.objectContaining({ id: 'thisyear' }),
      jasmine.objectContaining({ id: 'lastweek' }),
      jasmine.objectContaining({ id: 'lastmonth' }),
      jasmine.objectContaining({ id: 'lastyear' }),
      jasmine.objectContaining({ id: 'nextweek' }),
      jasmine.objectContaining({ id: 'nextmonth' }),
      jasmine.objectContaining({ id: 'nextyear' }),
    ]);
  });

  it('allows to convert any date to range start (0:00:00.000)', () => {
    expect(service.getDateStartRangeFromDate(new Date())).toEqual(new Date(2017, 10, 27, 0, 0, 0, 0));
  });

  it('allows to convert any date to range end (23:59:59.999)', () => {
    expect(service.getDateEndRangeFromDate(new Date())).toEqual(new Date(2017, 10, 27, 23, 59, 59, 999));
  });

  it('allows to obtain specific UTC date', () => {
    const utc = new Date('2004-02-29Z0:0:00+00:00');
    expect(service.getUTCDate(2004, 1, 29)).toEqual(utc);
  });

  it('allows to normalize date', () => {
    const utc = new Date('2017-11-27Z0:0:00+00:00');
    expect(service.normalizeDate(new Date('2017-11-27Z5:38:00+06:00'))).toEqual(utc);
    expect(service.normalizeDate(new Date('2017-11-26Z20:38:00-04:00'))).toEqual(utc);
  });

  it('allows to construct range from two dates', () => {
    const range = service.getRangeFromDates(new Date(2018, 11, 1), new Date(2018, 11, 31));
    expect(range.start).toEqual(new Date(2018, 11, 1));
    expect(range.end).toEqual(new Date(2018, 11, 31, 23, 59, 59, 999));
  });

  it('allows to construct range from two ISO date strings', () => {
    const range = service.getRangeFromISO(
      '2018-12-1Z13:38:24-04:00',
      '2018-12-31Z13:38:24-04:00'
    );

    expect(range.start).toEqual(new Date(2018, 11, 1));
    expect(range.end).toEqual(new Date(2018, 11, 31, 23, 59, 59, 999));
  });

  it('correctly returns today range', () => {
    const range = service.getRangeToday();

    expect(range.start).toEqual(new Date(2017, 10, 27, 0, 0, 0, 0));
    expect(range.end).toEqual(new Date(2017, 10, 27, 23, 59, 59, 999));
  });

  it('correctly returns yesterday range', () => {
    const range = service.getRangeYesterday();

    expect(range.start).toEqual(new Date(2017, 10, 26, 0, 0, 0, 0));
    expect(range.end).toEqual(new Date(2017, 10, 26, 23, 59, 59, 999));
  });

  it('correctly returns this week range', () => {
    const range = service.getRangeThisWeek();

    expect(range.start).toEqual(new Date(2017, 10, 26, 0, 0, 0, 0));  // Sunday
    expect(range.end).toEqual(new Date(2017, 11, 2, 23, 59, 59, 999));  // Monday
  });

  it('correctly returns this month range', () => {
    const range = service.getRangeThisMonth();

    expect(range.start).toEqual(new Date(2017, 10, 1, 0, 0, 0, 0));
    expect(range.end).toEqual(new Date(2017, 10, 30, 23, 59, 59, 999));
  });

  it('correctly returns this year range', () => {
    const range = service.getRangeThisYear();

    expect(range.start).toEqual(new Date(2017, 0, 1, 0, 0, 0, 0));
    expect(range.end).toEqual(new Date(2017, 11, 31, 23, 59, 59, 999));
  });

  it('correctly returns last week range', () => {
    const range = service.getRangeLastWeek();  // last 8 days with today

    expect(range.start).toEqual(new Date(2017, 10, 20, 0, 0, 0, 0));
    expect(range.end).toEqual(new Date(2017, 10, 27, 23, 59, 59, 999));
  });

  it('correctly returns last month range', () => {
    const range = service.getRangeLastMonth();  // last 28/29/30/31 days

    expect(range.start).toEqual(new Date(2017, 9, 27, 0, 0, 0, 0));
    expect(range.end).toEqual(new Date(2017, 10, 27, 23, 59, 59, 999));
  });

  it('correctly returns last year range', () => {
    const range = service.getRangeLastYear();  // last 12 months

    expect(range.start).toEqual(new Date(2016, 10, 27, 0, 0, 0, 0));
    expect(range.end).toEqual(new Date(2017, 10, 27, 23, 59, 59, 999));
  });

  it('correctly returns next week range', () => {
    const range = service.getRangeNextWeek();  // next 8 days with today

    expect(range.start).toEqual(new Date(2017, 10, 27, 0, 0, 0, 0));
    expect(range.end).toEqual(new Date(2017, 11, 4, 23, 59, 59, 999));
  });

  it('correctly returns next month range', () => {
    const range = service.getRangeNextMonth();  // next 28/29/30/31 days

    expect(range.start).toEqual(new Date(2017, 10, 27, 0, 0, 0, 0));
    expect(range.end).toEqual(new Date(2017, 11, 27, 23, 59, 59, 999));
  });

  it('correctly returns next year range', () => {
    const range = service.getRangeNextYear();  // next 12 months

    expect(range.start).toEqual(new Date(2017, 10, 27, 0, 0, 0, 0));
    expect(range.end).toEqual(new Date(2018, 10, 27, 23, 59, 59, 999));
  });
});
