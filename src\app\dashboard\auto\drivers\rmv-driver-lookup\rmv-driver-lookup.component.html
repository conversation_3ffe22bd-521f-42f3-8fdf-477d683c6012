<p class="u-spacing--bottom-1-5 u-color-sunset" *ngIf="showListErrorInfo">First and Last Name, License #, and D.O.B. are required to perform a lookup.</p>
<p class="u-spacing--bottom-1-5 u-color-sunset" *ngIf="duplicateLicense || notUniqueLicense">You have entered the same License Number more than once. Please remove duplicate license requests.</p>

<div *ngIf="driversList.length">
  <table class="form-table u-width-100" >
    <tr class="form-table__row" *ngFor="let driver of driversList" [class.is-required-field]="isInvalidDriver(driver)">
      <td class="form-table__cell u-width-20px">
        <button class="o-btn o-btn--checkbox u-spacing--right-0" [class.is-active]="driver.checked" (click)="toggleCheckStatus($event, driver)"></button>
      </td>
      <td class="form-table__cell u-width-160px">
        {{driver.firstName}} {{driver.lastName}}
      </td>
      <td class="form-table__cell u-width-160px">
        DOB: {{driver.dob | dateFormat:'MM/dd/yyyy'}}
      </td>
      <td class="form-table__cell">
        License #: {{driver.license}}
      </td>
      <td class="form-table__cell u-width-25px">
        <ng-template [ngIf]="isInvalidDriver(driver)">
          <a #refLink [routerLink]="[generateDriverViewLink(driver)]" (click)="goToDriverView($event, driver, refTooltip)">
            <i class="icon-info"></i>
          </a>

          <sm-tooltip
            #refTooltip
            [launcher]="refLink"
            [css]="'tooltip-new tooltip-new--badge tooltip-new--link'"
            [position]="'my-center-at-bottom-center'"
            [positionLauncher]="refLink"
            [onHover]='true'>
              <p class="u-spacing--bottom-0-5">Please update following data for this driver:</p>
              <ul class="list u-padd--left-0-5">
                <li class="list__item" *ngIf="!driver.firstName"> - First name</li>
                <li class="list__item" *ngIf="!driver.lastName"> - Last Name</li>
                <li class="list__item" *ngIf="!driver.dob"> - Date of Birth</li>
                <li class="list__item" *ngIf="!driver.license"> - License</li>
              </ul>
          </sm-tooltip>
        </ng-template>
      </td>
    </tr>
  </table>
  <hr *ngIf="!isCommercial">
</div>


<!-- FORM -->
<p class="u-spacing--bottom-1-5 u-color-sunset" *ngIf="showFormErrorInfo">First and Last Name, License #, and D.O.B. are required to perform a lookup.</p>
<p class="u-spacing--bottom-1-5 u-color-sunset" *ngIf="duplicateLicense">You have entered the same License Number more than once. Please remove duplicate license requests.</p>
<form *ngIf="!isCommercial" #refFormRmvDriver="ngForm" name="formRmvDriverLookup" id="formRmvDriverLookup">
    <div class="u-flex">
      <div class="u-width-20px u-flex u-flex--to-middle">
        <div>
          <label class="o-checkable">
            <input type="checkbox" name="rmv-lookup-checkbox" [(ngModel)]="tmpNewDriver.checked" (change)="onFormCheckboxChange()" >
            <i class="o-btn o-btn--checkbox u-spacing--right-0"></i>
          </label>
        </div>
      </div>
      <!-- <div class="u-width-130px u-spacing--left-1" [class.is-required-field]="!refDriverFirstName && refFormRmvDriver.submitted"> -->
      <div class="u-width-115px u-spacing--left-1" [class.is-required-field]="validateFormDataAndCheckIfFieldIsRequired('firstName')">
        <input #refDriverFirstName="ngModel" type="text" name="firstName" required placeholder="First Name" [(ngModel)]="tmpNewDriver.firstName">
      </div>
      <!--
      <div class="u-width-50px u-spacing--left-1">
        <input
          #refDriverMiddleName="ngModel"
          name="middleName"
          type="text"
          placeholder="M."
          pattern="[A-Za-z]{1}"
          maxlength="1"
          (keydown)="lettersOnly($event)"
          [(ngModel)]="tmpNewDriver.middleName">
      </div>
      -->
      <div class="u-width-115px u-spacing--left-1" [class.is-required-field]="validateFormDataAndCheckIfFieldIsRequired('lastName')">
        <input #refDriverLastName="ngModel" name="lastName" type="text" required placeholder="Last Name" [(ngModel)]="tmpNewDriver.lastName">
      </div>

      <!-- ****** -->

      <div class="u-width-115px u-spacing--left-1" [class.is-required-field]="validateFormDataAndCheckIfFieldIsRequired('license')">
        <input #refDriverLicenseName="ngModel" name="license" type="text" required placeholder="License #" (change)="duplicateLicense = checkForDuplicateDriverLicense(tmpNewDriver.license)" [(ngModel)]="tmpNewDriver.license">
      </div>

      <div class="u-width-115px u-spacing--left-1" [class.is-required-field]="validateFormDataAndCheckIfFieldIsRequired('dob')">
        <app-datepicker-input
          #refPickerDateOfBirth
          [id]="'dob'"
          [name]="'dob'"
          [required]="true"
          [placeholder]="'D.O.B.'"
          [selectDate]="tmpNewDriver.dob"
          (onDateChange)="updateDateObjectProperty($event, tmpNewDriver, 'dob')">
        </app-datepicker-input>
      </div>

    </div>

    <!--
    <div class="u-flex u-spacing--1">
      <div class="u-width-130px u-spacing--left-3" [class.is-required-field]="validateFormDataAndCheckIfFieldIsRequired('license')">
        <input #refDriverLicenseName="ngModel" name="license" type="text" required placeholder="License #" [(ngModel)]="tmpNewDriver.license">
      </div>
      <div class="u-width-130px u-spacing--left-1" [class.is-required-field]="validateFormDataAndCheckIfFieldIsRequired('dob')">
        <app-datepicker-input
          #refPickerDateOfBirth
          [id]="'dob'"
          [name]="'dob'"
          [required]="true"
          [placeholder]="'D.O.B.'"
          [selectDate]="tmpNewDriver.dob"
          (onDateChange)="updateDateObjectProperty($event, tmpNewDriver, 'dob')">
        </app-datepicker-input>
      </div>
    </div>
  -->
</form>
