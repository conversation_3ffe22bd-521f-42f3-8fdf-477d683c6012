<div class="sidebox">
  <h2 class="sidebox__title">Dwelling Fire Quote</h2>
  <div class="sidebox__content">

    <div class="aboutbox">
      <h3 class="aboutbox__title">Form Type:</h3>
      <button class="aboutbox__link o-link o-link--expand sidebox__link-dropdown-toggle" (click)="refDropdownFormTypes.openDropdown()">{{ quote.formType }}</button>
      <sm-select #refDropdownFormTypes [options]="formTypes" [activeOption]="quote.formType" [hasSearch]="false" [multiselect]="false"
        (onSelect)="updateQuoteFormType($event)" class="sidebox__link-dropdown">
      </sm-select>
    </div>
    <!-- / .aboutbox -->

    <div class="aboutbox">
      <h3 class="aboutbox__title">Client:</h3>
      <a [routerLink]='[]' [queryParams]="{overlay: 'info', type: 'client'}" class="aboutbox__link o-link">
        {{clientNameToDisplay}}
      </a>
    </div>
    <!-- / .aboutbox -->

    <div class="aboutbox">
      <h3 class="aboutbox__title">Plans:</h3>
      <app-plans-selector #refPlansSelector [plans]="plansOnQuotes" [activePlans]="selectedPlan" (onSelect)="updateQuotePlan($event)"
        [css]="'aboutbox__link o-link o-link--expand sidebox__link-dropdown-toggle'"></app-plans-selector>
    </div>
    <!-- / .aboutbox -->

    <div class="aboutbox">
      <h3 class="aboutbox__title">Effective Date:</h3>
      <button class="aboutbox__link o-link o-link--expand" id="aside-effective-date">{{ effectiveDate | dateFormat: 'MMM d, yyyy' }}</button>
      <app-datepicker-modal #datepickerEffectiveDate [launcher]="'#aside-effective-date'" [title]="'Effective date'" [selectDate]="quote.effectiveDate"
        [returnDateFormat]="'MMM d, yyyy'" (onSave)="updateEffectiveDate($event)" [warningTimeInPastShow]="true">
      </app-datepicker-modal>
    </div>
    <!-- / .aboutbox -->
  </div>
</div>
<!-- / .sidebox -->
