
import {map} from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { Vehicle, VehicleGeneralDetails, VehiclePlanSymbols, VehicleSymbol, VehicleSymbolRequestData} from 'app/app-model/vehicle';
import { SYMBOL_DESCRIPTIONS, LocalSymbolData, SelectedVehicleSymbols } from 'app/app-model/symbols';
import { QuotePlan, QuotePlanRequired } from 'app/app-model/quote';
import {
  PlansService,
  REQUIRED_PLANS_SYMBOLS_ISO75,
  REQUIRED_PLANS_SYMBOLS_ISO27,
  REQUIRED_PLANS_SYMBOLS_ISO27_SAFETY,
  REQUIRED_PLANS_SYMBOLS_VRG
} from 'app/dashboard/app-services/plans.service';
import { LookupsService } from 'app/dashboard/app-services/lookups.service';

export interface SymbolsUpdateInProgressI {
  [vehicleResourceId: string]: boolean
}

@Injectable()
export class SymbolsService {

  constructor(
    private plansService:PlansService,
    private lookupsService?:LookupsService
  ) { }

  // Info about vehicles symbols updating state
  public symbolsUpdatingInProgress: SymbolsUpdateInProgressI = {};
  private _symbolsUpdatingInProgress:BehaviorSubject<SymbolsUpdateInProgressI> = new BehaviorSubject(this.symbolsUpdatingInProgress);

  public setSymbolsUpdatingInProgress(vehicle: Vehicle, state: boolean = false): void {
    this.symbolsUpdatingInProgress[vehicle.resourceId] = state;
    // delete this.symbolsUpdatingInProgress[vehicle.resourceId];
    this._symbolsUpdatingInProgress.next(this.symbolsUpdatingInProgress);
  }

  public get getSymbolsUpdatingInProgress$(): Observable<SymbolsUpdateInProgressI> {
    return this._symbolsUpdatingInProgress.asObservable();
  }


  public sortSymbols(arrSymbols:VehicleSymbol[]):VehicleSymbol[] {
    // return [...arrSymbols].sort((a, b) => {
    //   if (a.description < b.description)
    //     return -1;
    //   if (a.description > b.description)
    //     return 1;
    //   return 0;
    // });

    return [...arrSymbols].sort((a: VehicleSymbol, b: VehicleSymbol) => {
      let aVal = a.description + '_' + a.codeType;
      let bVal = b.description + '_' + b.codeType;

      if (aVal < bVal) {
        return -1;
      } else if (aVal > bVal) {
        return 1;
      } else {
        return 0;
      }
    });
  }

  public extractSymbolsToArrayFromPlanSymbols(arrPlanSymbols:VehiclePlanSymbols[]):VehicleSymbol[] {
    let arrSymbols = [];

    arrPlanSymbols.forEach((planSymbol:VehiclePlanSymbols)=> {
      planSymbol.symbols.forEach(symbol => arrSymbols.push(symbol))
    });
    return arrSymbols
  }

  public filterUniqueSymbolsBySymbolDescription(symbols:VehicleSymbol[], normalizeSymbolsValues: boolean = true):VehicleSymbol[] {
    let arrUqSymbols = [];

    symbols.forEach((symbol:VehicleSymbol) => {
      let exists = arrUqSymbols.findIndex((el:VehicleSymbol) => {
        return this.helpNormalizeDescription(el.description) == this.helpNormalizeDescription(symbol.description);
      });

      if (exists < 0) {
        if (normalizeSymbolsValues) {
          symbol = this.normalizeSymbolValue(symbol);
        }

        arrUqSymbols.push(symbol);
      }
    });
    // console.log('%cUnique Symbols:', 'color:red', arrUqSymbols);
    return arrUqSymbols;
  }


  public normalizeSymbolValue(symbol:VehicleSymbol): VehicleSymbol {
    let normalizedSymbol = symbol;
    // Remove leading Zeros
    if (normalizedSymbol && normalizedSymbol.symbolValue && typeof normalizedSymbol.symbolValue == 'string') {
      normalizedSymbol.symbolValue = normalizedSymbol.symbolValue.replace(/^0*/i, '');
    }

    return normalizedSymbol;
  }


  /**
   *
   * @param arrSymbols
   * @param selectedVehicleSymbols
   */
  public mapSymbolsArrayToSelectedVehicleSymbolsBySymbolDescription(arrSymbols:VehicleSymbol[], selectedVehicleSymbols:SelectedVehicleSymbols):SelectedVehicleSymbols {
    if (!arrSymbols) return selectedVehicleSymbols;

    arrSymbols.forEach(symbol => {
      let symbolDescription = this.helpNormalizeDescription(symbol.description);

      switch (symbolDescription) {
        case SYMBOL_DESCRIPTIONS.iso75Comp: //'ISO 75 Comprehensive':
          selectedVehicleSymbols.iso75Comp.symbol = symbol;
          // selectedVehicleSymbols.iso75Comp.sourceSymbol = this.cloneSymbol(symbol);
        break;
        case SYMBOL_DESCRIPTIONS.iso75Coll: // 'ISO 75 Collision':
          selectedVehicleSymbols.iso75Coll.symbol = symbol;
          // selectedVehicleSymbols.iso75Coll.sourceSymbol = this.cloneSymbol(symbol);
        break;
        case SYMBOL_DESCRIPTIONS.vrgComp: // 'VRG Comprehensive':
          selectedVehicleSymbols.vrgComp.symbol = symbol;
          // selectedVehicleSymbols.vrgComp.sourceSymbol = this.cloneSymbol(symbol);
        break;
        case SYMBOL_DESCRIPTIONS.vrgColl: // 'VRG Collision':
          selectedVehicleSymbols.vrgColl.symbol = symbol;
          // selectedVehicleSymbols.vrgColl.sourceSymbol = this.cloneSymbol(symbol);
        break;
        case SYMBOL_DESCRIPTIONS.iso27: // 'ISO 1-27':
        case SYMBOL_DESCRIPTIONS.iso27Bsc: // 'ISO 1-27 (BSC Conversion)':
          selectedVehicleSymbols.iso27.symbol = symbol;
          // selectedVehicleSymbols.iso27.sourceSymbol = this.cloneSymbol(symbol);
        break;
        case SYMBOL_DESCRIPTIONS.iso27Safety: // 'ISO 1-27 (Safety Conversion)':
          selectedVehicleSymbols.iso27Safety.symbol = symbol;
          // selectedVehicleSymbols.iso27Safety.sourceSymbol = this.cloneSymbol(symbol);
        break;
      }
    });

    return selectedVehicleSymbols;
  }

  // From API
  public setSourceSymbolsForSelectedVehicleSymbols(arrSourceSymbols:VehicleSymbol[], selectedVehicleSymbols: SelectedVehicleSymbols): SelectedVehicleSymbols {
    if (!arrSourceSymbols) return selectedVehicleSymbols;

    arrSourceSymbols.forEach(symbol => {
      let symbolDescription = this.helpNormalizeDescription(symbol.description);

      switch (symbolDescription) {
        case SYMBOL_DESCRIPTIONS.iso75Comp: //'ISO 75 Comprehensive':
          selectedVehicleSymbols.iso75Comp.sourceSymbol = this.cloneSymbol(symbol);
        break;
        case SYMBOL_DESCRIPTIONS.iso75Coll: // 'ISO 75 Collision':
          selectedVehicleSymbols.iso75Coll.sourceSymbol = this.cloneSymbol(symbol);
        break;
        case SYMBOL_DESCRIPTIONS.vrgComp: // 'VRG Comprehensive':
          selectedVehicleSymbols.vrgComp.sourceSymbol = this.cloneSymbol(symbol);
        break;
        case SYMBOL_DESCRIPTIONS.vrgColl: // 'VRG Collision':
          selectedVehicleSymbols.vrgColl.sourceSymbol = this.cloneSymbol(symbol);
        break;
        case SYMBOL_DESCRIPTIONS.iso27: // 'ISO 1-27':
        case SYMBOL_DESCRIPTIONS.iso27Bsc: // 'ISO 1-27 (BSC Conversion)':
          selectedVehicleSymbols.iso27.sourceSymbol = this.cloneSymbol(symbol);
        break;
        case SYMBOL_DESCRIPTIONS.iso27Safety: // 'ISO 1-27 (Safety Conversion)':
          selectedVehicleSymbols.iso27Safety.sourceSymbol = this.cloneSymbol(symbol);
        break;
      }
    });

    return selectedVehicleSymbols;
  }


  public setRequiredForPlansForSelectedVehicleSymbols(vehiclePlanSymbols:VehiclePlanSymbols[], selectedVehicleSymbols: SelectedVehicleSymbols): SelectedVehicleSymbols {
    if (!vehiclePlanSymbols) return selectedVehicleSymbols;

    vehiclePlanSymbols.forEach(vehPlanSymbols => {
      let tmpSymbolsTypes = this.getSymbolsIdsFromVehiclePlanSymbols(vehPlanSymbols);

      tmpSymbolsTypes.forEach(symbolType => {
        if(selectedVehicleSymbols[symbolType]) {
          selectedVehicleSymbols[symbolType].requiredForPlans.push(vehPlanSymbols.ratingPlanId);
        } else {
          console.log('There is no such symbolType: ' + symbolType + ' in SelectedVehicleSymbols')
        }
      });
    })

    return selectedVehicleSymbols;
  }


  public setSelectedVehicleSymbolsSymbolIfTheValueIsEmptyBasedOnSourceSymbol(selectedVehicleSymbolsWithSourceSymbolsSettedUp: SelectedVehicleSymbols): SelectedVehicleSymbols {
    if (!selectedVehicleSymbolsWithSourceSymbolsSettedUp) console.error('missing SelectedVehicleSymbols ');
    let selectedVehicleSymbols = JSON.parse(JSON.stringify(selectedVehicleSymbolsWithSourceSymbolsSettedUp))

    selectedVehicleSymbols.iso27.symbol.symbolValue
    selectedVehicleSymbols.iso27.sourceSymbol.symbolValue

    for (const symbol in selectedVehicleSymbols) {
      if (selectedVehicleSymbols.hasOwnProperty(symbol)) {
        if (!selectedVehicleSymbols[symbol].symbol.symbolValue && selectedVehicleSymbols[symbol].sourceSymbol) {
          if (selectedVehicleSymbols[symbol].sourceSymbol.symbolValue != undefined){
            selectedVehicleSymbols[symbol].symbol = JSON.parse(JSON.stringify(selectedVehicleSymbols[symbol].sourceSymbol));
          }
        }
      }
    }

    return selectedVehicleSymbols;
  }



  // https://bostonsoftware.atlassian.net/wiki/display/CON/Vehicle+Symbols
  public manageSelectedVehicleSymbolsRequiredFieldsBasedOnSelectedPlans(selectedVehicleSymbols:SelectedVehicleSymbols, selectedPlans:QuotePlan[], vehicle:Vehicle, preventOverrideAlreadyRequiredFields:boolean = false):SelectedVehicleSymbols {
    // ISO 27
    let requiredISO27 = this.plansService.checkIfSelectedPlansAreInRequiredPlans(selectedPlans, REQUIRED_PLANS_SYMBOLS_ISO27);
    let requiredISO27ByYear = this.helperRequiredUpToYear(vehicle, 2011);

    // ISO 27 Safety
    let requiredISO27_Safety = this.plansService.checkIfSelectedPlansAreInRequiredPlans(selectedPlans, REQUIRED_PLANS_SYMBOLS_ISO27_SAFETY);
    let requiredISO27_SafetyByYear = this.helperRequiredByYear(vehicle, 2011);

    // ISO 75
    let requiredISO75 = this.plansService.checkIfSelectedPlansAreInRequiredPlans(selectedPlans, REQUIRED_PLANS_SYMBOLS_ISO75);
    let requiredISO75ByYear = this.helperRequiredForYearOrNewerThanYear(vehicle, 2011);

    // VRG
    let requiredVRG = this.plansService.checkIfSelectedPlansAreInRequiredPlans(selectedPlans, REQUIRED_PLANS_SYMBOLS_VRG);


    if (preventOverrideAlreadyRequiredFields) {
      selectedVehicleSymbols.iso27.required = (selectedVehicleSymbols.iso27.required) ? true : requiredISO27 || requiredISO27ByYear;
      selectedVehicleSymbols.iso27Safety.required = (selectedVehicleSymbols.iso27Safety.required) ? true : (requiredISO27_Safety && requiredISO27_SafetyByYear);
      selectedVehicleSymbols.iso75Coll.required = (selectedVehicleSymbols.iso75Coll.required) ? true : requiredISO75 && requiredISO75ByYear;
      selectedVehicleSymbols.iso75Comp.required = (selectedVehicleSymbols.iso75Comp.required) ? true : requiredISO75 && requiredISO75ByYear;
      selectedVehicleSymbols.vrgColl.required = (selectedVehicleSymbols.vrgColl.required) ? true : requiredVRG;
      selectedVehicleSymbols.vrgComp.required = (selectedVehicleSymbols.vrgComp.required) ? true : requiredVRG;
    } else {
      selectedVehicleSymbols.iso27.required = requiredISO27 || requiredISO27ByYear;
      selectedVehicleSymbols.iso27Safety.required = (requiredISO27_Safety && requiredISO27_SafetyByYear);
      selectedVehicleSymbols.iso75Coll.required = requiredISO75 && requiredISO75ByYear;
      selectedVehicleSymbols.iso75Comp.required = requiredISO75 && requiredISO75ByYear;
      selectedVehicleSymbols.vrgColl.required = requiredVRG;
      selectedVehicleSymbols.vrgComp.required = requiredVRG;
    }

    return selectedVehicleSymbols;
  }


  // https://bostonsoftware.atlassian.net/browse/SPRC-2 (The symbol pop up screen should display only the symbols returned by this call)
  public manageSelectedVehicleSymbolsRequiredFieldsBasedOnVehicleSymbols(arrSymbols:VehicleSymbol[], selectedVehicleSymbols:SelectedVehicleSymbols):SelectedVehicleSymbols {
    // Reset
    for (let symbol in selectedVehicleSymbols) {
      selectedVehicleSymbols[symbol].required = false;
    }

    arrSymbols.forEach(symbol => {
      let symbolDescription = this.helpNormalizeDescription(symbol.description);

      switch (symbolDescription) {
        case SYMBOL_DESCRIPTIONS.iso75Comp: //'ISO 75 Comprehensive':
          selectedVehicleSymbols.iso75Comp.required = true;
        break;
        case SYMBOL_DESCRIPTIONS.iso75Coll: // 'ISO 75 Collision':
          selectedVehicleSymbols.iso75Coll.required = true;
        break;
        case SYMBOL_DESCRIPTIONS.vrgComp: // 'VRG Comprehensive':
          selectedVehicleSymbols.vrgComp.required = true;
        break;
        case SYMBOL_DESCRIPTIONS.vrgColl: // 'VRG Collision':
          selectedVehicleSymbols.vrgColl.required = true;
        break;
        case SYMBOL_DESCRIPTIONS.iso27: // 'ISO 1-27':
        case SYMBOL_DESCRIPTIONS.iso27Bsc: // 'ISO 1-27 (BSC Conversion)':
          selectedVehicleSymbols.iso27.required = true;
        break;
        case SYMBOL_DESCRIPTIONS.iso27Safety: // 'ISO 1-27 (Safety Conversion)':
          selectedVehicleSymbols.iso27Safety.required = true;
        break;
      }
    });

    return selectedVehicleSymbols;
  }

  /**
   * @param selectedVehicleSymbols
   * @param property - Field with symbol value based on witch the validation should be perfomed ['symbol', 'sourceSymbol']
   * @param checkCodeSource - define if during validation should be checked source of the symbol - used for validation for view needs (if the field should be editable)
   */
  private static checkIfVehiclePriceValueFieldRequiredBasedOnSelectedVehicleSymbolsAndSpecifiedSymbol(selectedVehicleSymbols:SelectedVehicleSymbols, property: string = 'symbol', checkCodeSource: boolean = false, checkOnlyIfTheValuesAreSet: boolean = false): boolean {
    let isRequired = false;

    // If a required symbol type does not return a value, the Price New/Value field will be required
    for (let symbol in selectedVehicleSymbols) {
      let tmpSymbolData:LocalSymbolData = selectedVehicleSymbols[symbol];

      if (checkCodeSource) {
        if ( tmpSymbolData.required
          && (tmpSymbolData.sourceSymbol.symbolValue === null
          || tmpSymbolData.sourceSymbol.symbolValue === ''
          || tmpSymbolData.sourceSymbol.symbolValue === undefined)
          || tmpSymbolData.sourceSymbol.codeSource === 'Price' ) {
           isRequired = true;
           break;
          }

      } else {
        if ( tmpSymbolData.required
          && (tmpSymbolData[property].symbolValue === null
          || tmpSymbolData[property].symbolValue === ''
          || tmpSymbolData[property].symbolValue === undefined) ) {
          isRequired = true;
          break;
          }
      }

    }

    if (!checkOnlyIfTheValuesAreSet) {
      // If an ISO-27 symbol type is required and the value is 27 the Price New/Value field will be required
      if (selectedVehicleSymbols.iso27.required && selectedVehicleSymbols.iso27[property].symbolValue == '27') {
        isRequired = true;
      }

      // If an ISO-75 symbol type is required and the value is 98 the Price New/Value field will be required
      if ( selectedVehicleSymbols.iso75Coll.required && selectedVehicleSymbols.iso75Coll[property].symbolValue == '98'
          || selectedVehicleSymbols.iso75Comp.required && selectedVehicleSymbols.iso75Comp[property].symbolValue == '98') {
          isRequired = true;
      }

      // https://bostonsoftware.atlassian.net/browse/SPR-2818
      // If an VRG symbol type is required and the value is 50 the Price New/Value field will be required
      if ( selectedVehicleSymbols.vrgColl.required && selectedVehicleSymbols.vrgColl[property].symbolValue == '50'
          || selectedVehicleSymbols.vrgComp.required && selectedVehicleSymbols.vrgComp[property].symbolValue == '50') {
          isRequired = true;
      }
    }

    // // If an ISO-27 symbol type is required and the value is 27 the Price New/Value field will be required
    // if (selectedVehicleSymbols.iso27.required && selectedVehicleSymbols.iso27[property].symbolValue == '27') {
    //   isRequired = true;
    // }

    // // If an ISO-75 symbol type is required and the value is 98 the Price New/Value field will be required
    // if ( selectedVehicleSymbols.iso75Coll.required && selectedVehicleSymbols.iso75Coll[property].symbolValue == '98'
    //     || selectedVehicleSymbols.iso75Comp.required && selectedVehicleSymbols.iso75Comp[property].symbolValue == '98') {
    //     isRequired = true;
    // }

    return isRequired;
  }


  /**
   * @description Check if Price/Value field is Required for view // (validation based on API response symbols)
   * @param selectedVehicleSymbols
   */
  public static checkIfVehiclePriceNewValueIsRequired(selectedVehicleSymbols:SelectedVehicleSymbols):boolean {
    // return SymbolsService.checkIfVehiclePriceValueFieldRequiredBasedOnSelectedVehicleSymbolsAndSpecifiedSymbol(selectedVehicleSymbols, 'sourceSymbol', true);
    return SymbolsService.checkIfVehiclePriceValueFieldRequiredBasedOnSelectedVehicleSymbolsAndSpecifiedSymbol(selectedVehicleSymbols, 'symbol', true);
  }

  // For H&W
  // public static checkIfVehiclePriceNewValueOrSymbolsMissingSomeValue(selectedVehicleSymbols:SelectedVehicleSymbols):boolean {
  //   return SymbolsService.checkIfVehiclePriceValueFieldRequiredBasedOnSelectedVehicleSymbolsAndSpecifiedSymbol(selectedVehicleSymbols, 'symbol', false);
  // }

  public static checkIfVehicleSymbolsMissingSomeValue(selectedVehicleSymbols:SelectedVehicleSymbols):boolean {
    return SymbolsService.checkIfVehiclePriceValueFieldRequiredBasedOnSelectedVehicleSymbolsAndSpecifiedSymbol(selectedVehicleSymbols, 'symbol', false, true);
  }

  public retrieveVehiclePlanSymbols(vehicle:Vehicle, genDetails:VehicleGeneralDetails, quoteEffectiveDate:string, quotePlansList:QuotePlan[]):Observable<VehiclePlanSymbols[]> {
    let vehicleYearGenDetails = genDetails?.year ? genDetails.year : ''
    let vehicleYear = (vehicle?.year) ? vehicle.year : vehicleYearGenDetails;
    let data:VehicleSymbolRequestData = {
      price: vehicle.priceValue,
      policyEffectiveDt: quoteEffectiveDate,
      ratingPlans: this.plansService.getPlansIdsListFromQuotePlansList(quotePlansList).join(','),
      vrgDetailComprehensive: (genDetails?.vrgDetail?.comp_Sym) ? genDetails.vrgDetail.comp_Sym : '',
      vrgDetailCollision: (genDetails?.vrgDetail?.coll_Sym) ? genDetails.vrgDetail.coll_Sym : '',
      symbol: genDetails ? genDetails.symbol : '',
      collisionSymbol: genDetails ? genDetails.collisionSymbol : '',
      comprehensiveSymbol: genDetails ? genDetails.comprehensiveSymbol : '',
      safetyCustomSymbol: genDetails ? genDetails.safetyCustomSymbol: '',
      massBodyStyleGroup: (vehicle.bodyStyle) ? vehicle.bodyStyle : genDetails ? genDetails.massBodyStyleGroup : '' // https://bostonsoftware.atlassian.net/browse/SPR-3031
    }

     return this.lookupsService.postVehiclePlanSymbols(vehicleYear, data).pipe(
        map(res => res.items));
  }

  public orderVehiclesSymbols(vehicles) {
    if (vehicles && vehicles.length) {
      vehicles.forEach( vehicle => {
        if (vehicle && vehicle.symbols && vehicle.symbols.length) {
          vehicle.symbols = this.sortSymbols(vehicle.symbols);
        }
      })
    }
    return vehicles
  }

  public getSymbolIdBasedOnSymbolDescription(symbol: VehicleSymbol): string {
    let symbolId = '';

    let symbolDescription = this.helpNormalizeDescription(symbol.description);

    switch (symbolDescription) {
      case SYMBOL_DESCRIPTIONS.iso75Comp: //'ISO 75 Comprehensive':
        symbolId = 'iso75Comp';
      break;
      case SYMBOL_DESCRIPTIONS.iso75Coll: // 'ISO 75 Collision':
        symbolId = 'iso75Coll';
      break;
      case SYMBOL_DESCRIPTIONS.vrgComp: // 'VRG Comprehensive':
        symbolId = 'vrgComp';
      break;
      case SYMBOL_DESCRIPTIONS.vrgColl: // 'VRG Collision':
        symbolId = 'vrgColl';
      break;
      case SYMBOL_DESCRIPTIONS.iso27: // 'ISO 1-27':
      case SYMBOL_DESCRIPTIONS.iso27Bsc: // 'ISO 1-27 (BSC Conversion)':
        symbolId = 'iso27';
      break;
      case SYMBOL_DESCRIPTIONS.iso27Safety: // 'ISO 1-27 (Safety Conversion)':
        symbolId = 'iso27Safety';
      break;
    }

    return symbolId;
  }

  public getSymbolsIdsFromVehiclePlanSymbols(vehiclePlanSymbols: VehiclePlanSymbols): string [] {
    let ids: string[] = [];

    if (vehiclePlanSymbols && vehiclePlanSymbols.symbols && vehiclePlanSymbols.symbols.length) {
      ids = vehiclePlanSymbols.symbols.map(item => this.getSymbolIdBasedOnSymbolDescription(item));
    }

    return ids;
  }

  /**
   * Return all required symbols, and keep already set symbols values
   *
   * @param newUniqueSymbolsFromApi
   * @param currentVehicleSymbols
   *
   */
  public prepareProperSymbolsForTheVehicle(newUniqueSymbolsFromApi: VehicleSymbol[], currentVehicleSymbols: VehicleSymbol[]): VehicleSymbol[] {
    let currentVehicleSymbolsCloned: VehicleSymbol[] = (currentVehicleSymbols && currentVehicleSymbols.length) ? [...currentVehicleSymbols] : [];
    let symbolsToAssignForVehicle: VehicleSymbol[] = [];

    symbolsToAssignForVehicle = newUniqueSymbolsFromApi.map(apiSymbol => {
      //check if symbol already is set for vehicle and has value
      let tmpVehicleSymbol = currentVehicleSymbolsCloned
        .find(symbol => symbol.symbolType == apiSymbol.symbolType && symbol.codeType == apiSymbol.codeType && symbol.symbolValue != null && symbol.symbolValue != undefined  && String(symbol.symbolValue).trim() != '');

      return (tmpVehicleSymbol) ? tmpVehicleSymbol : apiSymbol;
    });

    // Sort Symbols
    symbolsToAssignForVehicle = this.sortSymbols(symbolsToAssignForVehicle);

    return symbolsToAssignForVehicle
  }

  private helperRequiredUpToYear(vehicle:Vehicle, year:number=2011):boolean {
    let required = false;
    if(vehicle.year) {
      let tmpYear = parseInt(vehicle.year, 10);
      required = (!isNaN(tmpYear) && tmpYear < year) ? true : false;
    }

    return required;
  }

  private helperRequiredByYear(vehicle:Vehicle, year:number=2011):boolean {
    let required = false;
    if(vehicle.year) {
      let tmpYear = parseInt(vehicle.year, 10);
      required = (!isNaN(tmpYear) && tmpYear == year) ? true : false;
    }

    return required;
  }

  private helperRequiredForYearOrNewerThanYear(vehicle:Vehicle, year:number=2011):boolean {
    let required = false;
    if(vehicle.year) {
      let tmpYear = parseInt(vehicle.year, 10);
      required = (!isNaN(tmpYear) && tmpYear >= year) ? true : false;
    }

    return required;
  }

  private helpNormalizeDescription(description):string {
    let normalizedDescription = description.trim();
    normalizedDescription = normalizedDescription.replace(/(\s+)/ig, ' ');
    return normalizedDescription;
  }

  private cloneSymbol(symbol:VehicleSymbol):VehicleSymbol {
    return JSON.parse(JSON.stringify(symbol));
  }

}
