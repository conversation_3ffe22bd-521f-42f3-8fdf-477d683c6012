import { inject, TestBed } from '@angular/core/testing';

import { DWELLING_PROTECTION_CLASS } from 'testing/data/dwellings/protection-class';
import {
    DWELLING_PROTECTION_CLASS_OVERRIDES
} from 'testing/data/dwellings/protection-class-overrides';
import { DWELLING_PROTECTION_DEVICES } from 'testing/data/dwellings/protection-devices';
import { DWELLING_SCHEDULED_PROPERTY } from 'testing/data/dwellings/scheduled-property';
import { DWELLING_SUBSYSTEMS } from 'testing/data/dwellings/subsystems';
import { DWELLINGS } from 'testing/data/quotes/dwellings';
import { DWELLING_LOCATION } from 'testing/data/quotes/locations/dwelling';
import { data as LOSS_HISTORY } from 'testing/data/quotes/loss-history';
import {
    DataCustomMatchers, expectLastCallArgs, expectLastConnectionPayload, expectLastConnectionUrl
} from 'testing/helpers/all';
import { MockBackend, setupMockBackend } from 'testing/setups/mock-backend';

import {
    DwellingProtectionClass, DwellingProtectionClassOverrides, DwellingProtectionDevices,
    DwellingSubsystems, LossHistory
} from 'app/app-model/dwelling';
import { LocationData } from 'app/app-model/location';
import { StorageService } from 'app/shared/services/storage-new.service';

import { DwellingService } from './dwelling.service';
import { QuotesService } from './quotes.service';
import { SpecsService } from './specs.service';

describe('Service: Dwelling', () => {
  let service: DwellingService;
  let mockBackend: MockBackend;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        DwellingService,
        { provide: QuotesService, useValue: {} },  // unused
        StorageService,
        { provide: SpecsService, useValue: {} },  // unused
      ]
    });

    jasmine.addMatchers(DataCustomMatchers);
  });

  beforeEach(inject([DwellingService], (_service: DwellingService) => {
    service = _service;
  }));

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('when working with dwellings', () => {
    it('allows to retrieve dwellings list by URI', () => {
      mockBackend = setupMockBackend(DWELLINGS);

      service.getDwellingsListByUri('/quotes/quote-id/dwellings').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/dwellings');
    });

    it('allows to create a dwelling by URI', () => {
      mockBackend = setupMockBackend(DWELLINGS.items[0]);

      service.createDwellingByUri('/quotes/quote-id/dwellings', DWELLINGS.items[0]).subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/dwellings');
      expectLastConnectionPayload(mockBackend).toEqual(DWELLINGS.items[0]);
    });

    it('allows to update a dwelling by URI', () => {
      mockBackend = setupMockBackend(DWELLINGS.items[0]);

      service.updateDwellingByUri('/quotes/quote-id/dwellings/dwelling-id', DWELLINGS.items[0]).subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/dwellings/dwelling-id');
      expectLastConnectionPayload(mockBackend).toEqual(DWELLINGS.items[0]);
    });

    it('allows to update a dwelling by data and save it in storage', inject(
      [StorageService], (storageService: StorageService) => {
        mockBackend = setupMockBackend(DWELLINGS.items[0]);
        spyOn(storageService, 'setStorageData');

        service.updateDwelling(DWELLINGS.items[0]).subscribe();  // it gets URI from meta

        expectLastConnectionUrl(mockBackend).toEndWith(
          '/quotes/2d9c9c4c-0fca-4aa8-8b41-4c79103765c4/dwellings/a89bbb96-dd0b-4c9b-8b56-4b8ff06cf283'
        );
        expectLastConnectionPayload(mockBackend).toEqual(DWELLINGS.items[0]);
        expect(storageService.setStorageData).toHaveBeenCalledWith('dwelling', DWELLINGS.items[0]);
    }));
  });

  describe('when working with dwelling locations', () => {
    it('allows to retrieve dwelling location by URI and save it in storage', inject(
      [StorageService], (storageService: StorageService) => {
        mockBackend = setupMockBackend(DWELLING_LOCATION);
        spyOn(storageService, 'setStorageData');

        service.getDwellingLocation('/quotes/quote-id/locations').subscribe();

        expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/locations');
        expect(storageService.setStorageData).toHaveBeenCalledWith('dwellingLocation', DWELLING_LOCATION.items[0]);
    }));

    it('allows to retrieve dwelling location by URI and save empty in storage if none retrieved', inject(
      [StorageService], (storageService: StorageService) => {
        mockBackend = setupMockBackend({ items: [] });
        spyOn(storageService, 'setStorageData');

        service.getDwellingLocation('/quotes/quote-id/locations').subscribe();

        expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/locations');
        expect(storageService.setStorageData).toHaveBeenCalledWith('dwellingLocation', new LocationData());
    }));

    it('allows to update dwelling location by data and not update it in storage', inject(
      [StorageService], (storageService: StorageService) => {
        mockBackend = setupMockBackend(DWELLING_LOCATION.items[0]);
        spyOn(storageService, 'setStorageData');

        service.updateDwellingLocation(DWELLING_LOCATION.items[0], false).subscribe();  // it gets URI from meta
        expectLastConnectionUrl(mockBackend).toEndWith(
          '/quotes/3295e9ae-cfa5-4b41-b972-4e6121690105/locations/7197514f-e9e7-4a15-8555-1624a17dfce6'
        );
        expect(storageService.setStorageData).not.toHaveBeenCalled();
      }));

    it('allows to update dwelling location by data and update it in storage', inject(
      [StorageService], (storageService: StorageService) => {
        mockBackend = setupMockBackend(DWELLING_LOCATION.items[0]);
        spyOn(storageService, 'setStorageData');

        service.updateDwellingLocation(DWELLING_LOCATION.items[0]).subscribe();
        expectLastConnectionUrl(mockBackend).toEndWith(
          '/quotes/3295e9ae-cfa5-4b41-b972-4e6121690105/locations/7197514f-e9e7-4a15-8555-1624a17dfce6'
        );
        expect(storageService.setStorageData).toHaveBeenCalledWith('dwellingLocation', DWELLING_LOCATION.items[0]);
      }));
  });

  describe('when working with protection devices', () => {
    it('allows to retrieve protection devices list by URI', () => {
      mockBackend = setupMockBackend(DWELLING_PROTECTION_DEVICES);

      service.getDwellingProtectionDevices('/dwellings/dwelling-id/protectionDevices').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/dwellings/dwelling-id/protectionDevices');
    });

    it('allows to create protection devices by URI', () => {
      mockBackend = setupMockBackend(new DwellingProtectionDevices());

      service.createDwellingProtectionDevices('/dwellings/dwelling-id/protectionDevices').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/dwellings/dwelling-id/protectionDevices');
      expectLastConnectionPayload(mockBackend).toEqual({});  // it sends empty object as data
    });

    it('allows to update protection devices by data', () => {
      mockBackend = setupMockBackend(DWELLING_PROTECTION_DEVICES.items[0]);

      service.updateDwellingProtectionDevices(DWELLING_PROTECTION_DEVICES.items[0]).subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith(
        '/dwellings/2358f8c0-5979-4115-87f4-246e009c7ffd/protectionDevices/d1e7d916-ac6c-49ad-85bb-98e86dcde592'
      );
      expectLastConnectionPayload(mockBackend).toEqual(DWELLING_PROTECTION_DEVICES.items[0]);
    });
  });

  describe('when working with protection class', () => {
    it('can retrieve protection class by URI', () => {
      mockBackend = setupMockBackend(DWELLING_PROTECTION_CLASS);

      service.getDwellingProtectionClass('/dwellings/dwelling-id/protectionClass').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/dwellings/dwelling-id/protectionClass');
    });

    it('can create protection class by URI', () => {
      mockBackend = setupMockBackend(new DwellingProtectionClass());

      service.createDwellingProtectionClass('/dwellings/dwelling-id/protectionClass').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/dwellings/dwelling-id/protectionClass');
      expectLastConnectionPayload(mockBackend).toEqual(new DwellingProtectionClass());
    });

    it('can update protection class by URI', () => {
      mockBackend = setupMockBackend(DWELLING_PROTECTION_CLASS.items[0]);

      service.updateDwellingProtectionClass(
        '/dwellings/dwelling-id/protectionClass/protection-class-id',
        DWELLING_PROTECTION_CLASS.items[0]
      ).subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/dwellings/dwelling-id/protectionClass/protection-class-id');
      expectLastConnectionPayload(mockBackend).toEqual(DWELLING_PROTECTION_CLASS.items[0]);
    });
  });

  describe('when working with protection class overrides', () => {
    it('can retrieve protection class overrides by URI', () => {
      mockBackend = setupMockBackend(DWELLING_PROTECTION_CLASS_OVERRIDES);

      service.getDwellingProtectionClassOverrides('/dwellings/dwelling-id/protectionClassOverrides').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/dwellings/dwelling-id/protectionClassOverrides');
    });

    it('can create protection class overrides by URI', () => {
      mockBackend = setupMockBackend(new DwellingProtectionClassOverrides());

      service.createDwellingProtectionClassOverrides('/dwellings/dwelling-id/protectionClassOverrides').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/dwellings/dwelling-id/protectionClassOverrides');
      expectLastConnectionPayload(mockBackend).toEqual({ items: [] });
    });

    it('can update protection class overrides by URI', () => {
      mockBackend = setupMockBackend(DWELLING_PROTECTION_CLASS_OVERRIDES.items[0]);

      service.updateDwellingProtectionClassOverrides(
        '/dwellings/dwelling-id/protectionClassOverrides/protection-class-overrides-id',
        DWELLING_PROTECTION_CLASS_OVERRIDES.items[0]
      ).subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/dwellings/dwelling-id/protectionClassOverrides/protection-class-overrides-id');
      expectLastConnectionPayload(mockBackend).toEqual(DWELLING_PROTECTION_CLASS_OVERRIDES.items[0]);
    });
  });

  describe('when working with scheduled property', () => {
    it('can retrieve scheduled property list by ID', () => {
      mockBackend = setupMockBackend(DWELLING_SCHEDULED_PROPERTY);

      service.getScheduledPropertyList('quote-id').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/scheduledProperty');
    });

    it('can retrieve scheduled property by ID', () => {
      mockBackend = setupMockBackend(DWELLING_SCHEDULED_PROPERTY.items[0]);

      service.getScheduledProperty('quote-id', 'scheduled-property-id').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/scheduledProperty/scheduled-property-id');
    });

    it('can create scheduled property by URI', () => {
      mockBackend = setupMockBackend(new DwellingProtectionClassOverrides());

      service.createScheduledProperty('/dwellings/dwelling-id/scheduledProperty').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/dwellings/dwelling-id/scheduledProperty');
      expectLastConnectionPayload(mockBackend).toEqual({ items: [] });
    });

    it('can update scheduled property by URI', () => {
      mockBackend = setupMockBackend(DWELLING_SCHEDULED_PROPERTY.items[0]);

      service.updateScheduledProperty(
        '/quotes/quote-id/scheduledProperty/scheduled-property-id',
        DWELLING_SCHEDULED_PROPERTY.items[0]
      ).subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/scheduledProperty/scheduled-property-id');
      expectLastConnectionPayload(mockBackend).toEqual(DWELLING_SCHEDULED_PROPERTY.items[0]);
    });
  });

  describe('when working with dwelling subsystems', () => {
    it('can retrieve dwelling subsystems by URI and save it in storage', inject(
      [StorageService], (storageService: StorageService) => {
        mockBackend = setupMockBackend(DWELLING_SUBSYSTEMS);
        spyOn(storageService, 'setStorageData');

        service.getDwellingSubsystem('/dwellings/dwelling-id/subsystems').subscribe();

        expectLastConnectionUrl(mockBackend).toEndWith('/dwellings/dwelling-id/subsystems');
        expect(storageService.setStorageData).toHaveBeenCalledWith('dwellingSubsystems', DWELLING_SUBSYSTEMS);
    }));

    it('can create dwelling subsystems by URI and save it in storage', inject(
      [StorageService], (storageService: StorageService) => {
        mockBackend = setupMockBackend(new DwellingSubsystems());
        spyOn(storageService, 'updateDwellingSubsystems');

        service.createDwellingSubsystem('/dwellings/dwelling-id/subsystems').subscribe();

        expectLastConnectionUrl(mockBackend).toEndWith('/dwellings/dwelling-id/subsystems');
        expectLastConnectionPayload(mockBackend).toEqual({});
        expectLastCallArgs(storageService.updateDwellingSubsystems).toEqualIgnoringTypes([
          new DwellingSubsystems()
        ]);
    }));

    it('can update dwelling subsystems by data', inject(
      [StorageService], (storageService: StorageService) => {
        mockBackend = setupMockBackend(DWELLING_SUBSYSTEMS.items[0]);
        spyOn(storageService, 'updateDwellingSubsystems');

        service.updateDwellingSubsystem(
          DWELLING_SUBSYSTEMS.items[0]
        ).subscribe();

        expectLastConnectionUrl(mockBackend).toEndWith(
          '/dwellings/a89bbb96-dd0b-4c9b-8b56-4b8ff06cf283/subsystems/4cd53d24-6ea4-4e06-bab3-0e11cf7ac5ff'
        );
        expectLastConnectionPayload(mockBackend).toEqual(DWELLING_SUBSYSTEMS.items[0]);
        expect(storageService.updateDwellingSubsystems).toHaveBeenCalledWith(DWELLING_SUBSYSTEMS.items[0]);
    }));
  });

  describe('when working with loss history', () => {
    it('can retrieve loss history by ID', () => {
      mockBackend = setupMockBackend(LOSS_HISTORY);

      service.getLossHistory('quote-id').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/lossHistory');
    });

    it('can create loss history by ID', () => {
      mockBackend = setupMockBackend(new LossHistory());

      service.createLossHistory('quote-id').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/lossHistory');
      expectLastConnectionPayload(mockBackend).toEqual({});
    });

    it('can update loss history by URI', () => {
      mockBackend = setupMockBackend(LOSS_HISTORY.items[0]);

      service.updateLossHistory(
        '/quotes/quote-id/losshistory/loss-history-id',
        LOSS_HISTORY.items[0]
      ).subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/losshistory/loss-history-id');
      expectLastConnectionPayload(mockBackend).toEqual(LOSS_HISTORY.items[0]);
    });
  });
});
