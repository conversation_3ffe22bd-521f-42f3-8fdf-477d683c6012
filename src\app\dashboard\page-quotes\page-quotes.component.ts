import { take } from 'rxjs/operators';
import { FeatureService } from 'app/shared/services/feature.service';
import { SearchByNameComponent } from './../search-by-name/search-by-name.component';
import { FilterComponent } from './../../shared/components/filter/filter.component';
import { BroadcastService } from 'app/shared/services/broadcast.service';
import * as _ from 'underscore';

import { ActivatedRoute, Router } from '@angular/router';
import { Component, OnInit, ViewChild, Input, Output, AfterViewInit } from '@angular/core';
import { AgencyUserService } from 'app/shared/services/agency-user.service';
import { DateRange } from 'app/app-model/date';
import { DatesService } from '../app-services/dates.service';
import { FilterOption } from 'app/app-model/filter-option';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { Quote } from 'app/app-model/quote';
import { QuotesService } from '../app-services/quotes.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { SubsService } from '../app-services/subs.service';
import { RouteService } from 'app/shared/services/route.service';
import { SubscriptionLike as ISubscription } from 'rxjs';
import { PremiumsService } from 'app/dashboard/auto/premiums/premiums.service';
import { DeleteQuoteModalComponent } from 'app/shared/components/delete-quote/delete-quote.component';
import { format, parseISO } from 'date-fns';

const MostRecentText = 'Most Recent';
const Last7DaysText = 'Last 7 Days';

@Component({
    selector: 'app-page-quotes',
    templateUrl: './page-quotes.component.html',
    styleUrls: ['./page-quotes.component.scss'],
    standalone: false
})
export class PageQuotesComponent implements OnInit, AfterViewInit {

  constructor(
    private router: Router,
    private quotesService: QuotesService,
    private subsService: SubsService,
    private specsService: SpecsService,
    private storageService: StorageService,
    private datesService: DatesService,
    private agencyUserService: AgencyUserService,
    private overlayLoaderService: OverlayLoaderService,
    private activatedRoute: ActivatedRoute,
    private storageGlobalService: StorageGlobalService,
    private premiumsService: PremiumsService,
    private routeService: RouteService,
    private broadcastService: BroadcastService,
    private route: ActivatedRoute,
    private featureService: FeatureService,
  ) {

  }
  @Input() isDashboard: boolean | null = null;
  @ViewChild(SearchByNameComponent) searchProperty;
  @ViewChild('deleteQuoteModal')
  public deleteQuoteModal: DeleteQuoteModalComponent;
  @ViewChild('refFilterDates') public FilterDates: FilterComponent;
  public paginationResultsCount = 0;
  public paginationResultLimit = 10;
  public paginationResultShowFrom = 0;
  public paginationCurrentPage = 1;

  public arrQuotesAll: Quote[];
  public arrQuotesFiltered: Quote[];

  private dataInited = false;

  public searchQuery: string;
  lob;
  public isImported = false;
  public priorSelectedText = MostRecentText;

  public filterLobOptions: FilterOption[] = [
    { id: 'all', text: 'All' },
    { id: 'AUTOP', text: 'Personal Auto' },
    { id: 'DFIRE', text: 'Dwelling Fire' },
    { id: 'HOME', text: 'Homeowners' },
    { id: 'PUMBR', text: 'Umbrella' }
  ];

  naviagationList = [
    { text: 'Personal Quotes', link: 'quotes' },
    { text: 'Commercial Quotes', link: 'quotes', type: 'commercial' }
  ];

  private filterLobSelectedOption: FilterOption = this.filterLobOptions[0];
  public filterLobLabel: string;

  public filterAgentsOptions: FilterOption[] = [{ id: 'all', text: 'All' }];
  private filterAgentsSelectedOption: FilterOption = this
    .filterAgentsOptions[0];
  public filterAgentsLabel: string;

  public filterLocationsOptions: FilterOption[] = [{ id: 'all', text: 'All' }];
  private filterLocationsSelectedOption: FilterOption = this
    .filterLocationsOptions[0];
  private filterLocationLabel: string;

  public filterDatesLabel: string;
  public RecentDatesLabel:string;
  public filterDatesTypesOptions: FilterOption[] = this.datesService.getDatesTypesOptions();

  public filterDatesTypesSelectedOption: FilterOption = this
    .filterDatesTypesOptions[0];

  public filterDatesRangeOptions: FilterOption[] = this.datesService.getDateRangesOptions_Quotes(this.filterDatesTypesOptions[0]);
  public filterDatesRangeSelectedOption: FilterOption = this
    .filterDatesRangeOptions[0];

  public dateRange: DateRange = new DateRange();
  public dateRangeCustom: DateRange = new DateRange();

  private agencyId: string;

  public selectedQuotes: string[] = [];

  public isAllSelected = false;
  PersonalCommercialSeparate;
  private isRefreshRequest = false;
  nameType;
  quotesLoading = false;
  isPageLoad = false;

  private getAgencyUsersSubscription;
  private agencyUserServiceSubscription;
  private getRatingLobsServiceSubscription;
  private getRatingLocationsServiceSubscription;

  private subscriptionRoute: ISubscription;
  private recallQuoteInfo: any = null;
  private subscriptionRecallQuoteInfo: ISubscription;

  private importedSubscription: ISubscription;

  private getQuotesSubscription: ISubscription;
  private getQuotesRequestDataString = '';

  private isForceGetQuotes = false;

  private dataToChange: any;

  ngAfterViewInit() {
    setTimeout(() => {
      this.PersonalCommercialSeparate =  this.featureService.isCommercialSplit();
       }, 100);
       this.isPageLoad = false;
  }

  ngOnInit() {
    this.isPageLoad = true;
    this.setFilterAgentsOptions();
    this.setFilterLobOptions();
    this.setFilterLocationsOptions();
    this.setFilterDatesTypesOptions();
    this.setFilterDatesRangesOptions();
    this.setIsImportedValue();
    this.setRecentDatesLabel();
    this.route.queryParams.subscribe(p => {
      p['lob'] === 'commercial' ? (this.lob = 'AUTOB') : (this.lob = '');
      this.lob === 'AUTOB'
        ? (this.filterLobOptions = [{ id: 'AUTOB', text: 'Commercial' }])
        : (this.filterLobOptions = [
            { id: 'all', text: 'All' },
            { id: 'AUTOP', text: 'Personal Auto' },
            { id: 'DFIRE', text: 'Dwelling Fire' },
            { id: 'HOME', text: 'Homeowners' },
            { id: 'PUMBR', text: 'Umbrella' }
          ]);

          this.setFilterLobOptions();
    });

    this.premiumsService.getRatesAndRerate();

    this.subscriptionRoute = this.activatedRoute.params.subscribe(params => {
      if (params && params['imported']) {
        this.quotesService.setIsImportedQuote();
      } else {
        this.quotesService.setIsImportedQuote(false);
      }

      this.subscriptionRecallQuoteInfo?.unsubscribe();
      this.subscriptionRecallQuoteInfo = this.storageService
        .getStorageData('recallQuoteInfo')
        .subscribe(data => {
          this.recallQuoteInfo = data;

          if (data && (data.firstName || data.lastName) && this.isImported) {
            const firstname = data.firstName;
            const lastname = data.lastName;
            this.searchQuery = firstname + ' ' + lastname;
            this.filterByParams();
          } else {
            this.getQuotes(false, true);
          }
        });
    });

  }

  ngOnDestroy() {
    this.getAgencyUsersSubscription?.unsubscribe();
    this.agencyUserServiceSubscription?.unsubscribe();
    this.getRatingLobsServiceSubscription?.unsubscribe();
    this.getRatingLocationsServiceSubscription?.unsubscribe();
    this.subscriptionRoute?.unsubscribe();
    this.importedSubscription?.unsubscribe();
    this.subscriptionRecallQuoteInfo?.unsubscribe();
  }

  private setIsImportedValue() {
    this.importedSubscription = this.quotesService.isImportedQuote$.subscribe(
      imported => {
        this.isImported = imported.isImported;
      }
    );
  }

  public filterByName(event) {
    this.searchQuery = this.searchProperty.searchQuery;
    this.nameType = this.searchProperty.nameType;
    if (this.searchQuery.length >= 3) {
      this.setTo7Days(this.searchQuery)
      this.getQuotes(false, true);
    }
  }

  public filterByParams() {
    if (this.searchQuery.length) {
      this.getQuotes(true, true);
    }
  }

  // Toggles invidual quote checkbox
  public toggleQuote(quoteIdentifier: string): void {
    const itemIndex = this.selectedQuotes.indexOf(quoteIdentifier);
    const isPresent = itemIndex > -1;

    if (isPresent) {
      this.selectedQuotes.splice(itemIndex, 1);
    } else {
      this.selectedQuotes.push(quoteIdentifier);
    }

    this.storageService.setStorageData(
      'quoteListToDelete',
      this.selectedQuotes
    );
  }

  // to check/uncheck based on the selected quote list
  public isSelected(quoteIdentifier: string): boolean {
    return (
      this.selectedQuotes.indexOf(quoteIdentifier) > -1 || this.isAllSelected
    );
  }

  // Toggles all the quotes with a single select.
  public toggleAll(): void {
    this.isAllSelected = !this.isAllSelected;

    if (this.isAllSelected) {
      this.arrQuotesAll.forEach(quote => {
        if (this.selectedQuotes.indexOf(quote.quoteIdentifier) === -1) {
          this.selectedQuotes.push(quote.quoteIdentifier);
        }
      });
      this.storageService.setStorageData(
        'quoteListToDelete',
        this.selectedQuotes
      );
    } else { this.selectedQuotes = []; }
  }

  // to check/uncheck using a property
  public isChecked(): boolean {
    return this.isAllSelected;
  }

  disableDateType() {
    return this.filterDatesRangeSelectedOption.text === MostRecentText
  }

  // This method only updates the quote status to deleted
  // But will not physically remove the quote from the database.
  // Physical deletion is done at a later stage through automation
  public deleteQuotes(): void {
    this.storageService.setStorageData(
      'quoteListToDelete',
      this.selectedQuotes
    );
    this.deleteQuoteModal.modal.open();
  }

  // This is triggered once a delete quotes method is called from Delete-Quote.component
  public refreshPage(event) {
    // this property is set to indicate to the getquotes method to refresh the current page.
    this.isRefreshRequest = true;
    this.getQuotes();
  }
  public getQuotes(
    usesImportedData?: boolean,
    showResultsFromFirstPage: boolean = false
  ): void {
    this.isAllSelected = false;
    this.selectedQuotes = [];
    if (showResultsFromFirstPage) {
      this.paginationResultShowFrom = 0;
      this.paginationCurrentPage = 1;
    }

    let agentId, locationId, dateType, startDate, endDate, name;

    name = this.searchProperty?.searchQuery ? this.searchProperty?.searchQuery : '';
    if(name) { this.searchQuery = name}
    if (this.lob !== 'AUTOB') { this.lob = this.filterLobSelectedOption.id; }
    if (this.lob === 'all' || this.lob === 'All') {
      this.lob = '';
    }

    agentId = this.filterAgentsSelectedOption.id;
    if (agentId === 'all' || agentId === 'All') {
      agentId = '';
    }

    locationId = this.filterLocationsSelectedOption.id;
    if (locationId === 'all' || locationId === 'All') {
      locationId = '';
    }

    dateType = this.filterDatesTypesSelectedOption.id
      ? this.filterDatesTypesSelectedOption.id.replace(/\b\w/g, l =>
          l.toUpperCase()
        )
      : '';
    if (dateType === 'all' || dateType === 'All') {
      dateType = '';
    }

    startDate = this.dateRange.start
      ? format(new Date(this.dateRange.start), 'yyyy-MM-dd')  : '';
    endDate = this.dateRange.end
      ? format(new Date(this.dateRange.end), 'yyyy-MM-dd') : '';

    // If request data hasn't changed, do not send request
    const currentRequestDataString =
      this.paginationResultShowFrom.toString() +
      this.paginationResultLimit.toString() +
      name +
      agentId +
      this.lob +
      locationId +
      dateType +
      startDate +
      endDate +
      this.nameType;
    if (
      this.getQuotesRequestDataString === currentRequestDataString &&
      !this.isRefreshRequest && !this.isForceGetQuotes
    ) {
      return;
    }
    // --------------

    this.isRefreshRequest = false;
    this.getQuotesRequestDataString = currentRequestDataString;

    if (this.getQuotesSubscription) {
      this.getQuotesSubscription.unsubscribe();
      this.overlayLoaderService.hideLoader();
    }

    let loadingMessage = 'Loading Results...';
    if (usesImportedData) {
      loadingMessage = 'Searching For ' + this.searchQuery + '...';
    }
  this.overlayLoaderService.hideLoader();
  this.quotesLoading = true;

    // this.overlayLoaderService.showLoader(loadingMessage);


   let mostRecentEndpoint = this.filterDatesRangeSelectedOption.text == MostRecentText && name == '' ? true : false

    this.getQuotesSubscription = this.quotesService
      .getQuotes(
        this.paginationResultShowFrom.toString(),
        this.paginationResultLimit.toString(),
        name, // name
        agentId,
        this.lob,
        locationId,
        dateType,
        startDate,
        endDate,
        '', // clientId
        this.nameType,
        mostRecentEndpoint
      )
      .pipe(take(1))
      .subscribe(
        quotes => {
          if (this.filterAgentsOptions.length < 2) {
            this.getAgents();
          }
          // if( this.filterLobOptions.length < 2) { this.getLobs(); }
          if (this.filterLocationsOptions.length < 2) {
            this.getLocations();
          }
          this.dataInited = true;
          this.arrQuotesAll = quotes.items;
          this.arrQuotesFiltered = this.arrQuotesAll;
          this.paginationResultsCount = quotes.size;
          this.overlayLoaderService.hideLoader();
          this.quotesLoading = false;
        },
        reject => {
          this.overlayLoaderService.hideLoader();
          this.quotesLoading = false;

        }
      );
  }

  private getLocations() {
    const locations = ''; // todo: this.storageGlobalService.takeSubs('locations')
    if (locations.length) {
      return this.parseLocationsData(locations);
    } else {
      if (this.agencyId) {
        this.getRatingLocationsServiceSubscription = this.subsService
          .getAgencyLocations(this.agencyId)
          .subscribe(data => {
            this.parseLocationsData(data.locations);
          });
      } else {
        this.agencyUserServiceSubscription = this.agencyUserService.userData$.subscribe(
          agent => {
            this.agencyId = agent.agencyId;
            this.getRatingLobsServiceSubscription = this.subsService
              .getAgencyLocations(this.agencyId)
              .subscribe(data => {
                this.parseLocationsData(data.locations);
              });
          }
        );
      }
    }
  }

  private parseLocationsData(locations) {
    let id, address, city, state;
    const tmpArr: FilterOption[] = [{ id: 'all', text: 'All' }];
    locations.map(location => {
      id = location.id ? location.id : false;
      address = location.address1 ? location.address1 + ', ' : '';
      city = location.city ? location.city + ', ' : '';
      state = location.state ? location.state : '';

      if (id) {
        tmpArr.push({ id: location.id, text: address + city + state });
      }
    });
    this.filterLocationsOptions = tmpArr;
    return tmpArr;
  }

  // not in use - LOBS hardcoded. Uncomment to retrieve list of LOBS from the API
  /*private getLobs(){
    const lobs = this.storageGlobalService.takeSubs('lobs');
    if(lobs.length) {
      return this.parseLobs(lobs);
    } else {
      if(this.agencyId){
        this.getRatingLobsServiceSubscription = this.subsService.getRatingLobs(this.agencyId).subscribe(lobs => {
          this.parseLobs(lobs)
        });
      } else {
        this.agencyUserServiceSubscription = this.agencyUserService.userData$.subscribe(agent => {
            this.agencyId = agent.agencyId;
            this.getRatingLobsServiceSubscription = this.subsService.getRatingLobs(this.agencyId).subscribe(lobs => {
              this.parseLobs(lobs)
            });
        });
      }
    }
  }

  private parseLobs(lobs) {
    let tmpArr:FilterOption[] = [{ id: 'all', text: 'All'}];
    lobs.map(lob =>{
      tmpArr.push({"id": lob.code, "text": lob.name});
    })
    this.filterLobOptions = tmpArr;
    return tmpArr;
  }
*/

  private getAgents(): FilterOption[] {
    const agents = this.storageGlobalService.takeSubs('users');
    if (agents.length) {
      return this.parseAgentsData(agents);
    } else {
      this.agencyUserServiceSubscription = this.agencyUserService.userData$.subscribe(
        agent => {
          this.agencyId = agent.agencyId;

          this.getAgencyUsersSubscription = this.subsService
            .getAgencyUsers(agent.agencyId)
            .subscribe(agents => {
              this.storageGlobalService.setSubs('users', agents.items);
              return this.parseAgentsData(agents.items);
            });
        }
      );
    }
  }

  private parseAgentsData(agents) {
    const tmpArr: FilterOption[] = [{ id: 'all', text: 'All Agents' }];
    agents.map(agent => {
      tmpArr.push({
        id: agent.userId,
        text: agent.firstName + ' ' + agent.lastName
      });
    });
    this.filterAgentsOptions = tmpArr;
    return tmpArr;
  }

  private parseLastModifiedDate(date: string): string {
  const parsedDate = parseISO(date);
  return format(parsedDate, 'MMM d, yyyy');
}

  private setFilterAgentsOptions(): void {
    this.filterAgentsSelectedOption = this.filterAgentsOptions[0];
  }

  private setFilterLobOptions(): void {
    this.filterLobSelectedOption = this.filterLobOptions[0];
  }

  private setFilterLocationsOptions(): void {
    this.filterLocationsSelectedOption = this.filterLocationsOptions[0];
  }

  private setFilterDatesTypesOptions(): void {
    this.filterDatesTypesSelectedOption = this.filterDatesTypesOptions[0];
    this.filterDatesLabel = this.formatFilterLabelDateTypes();
  }

  private setRecentDatesLabel(): void {
    this.RecentDatesLabel = '';
    if (this.filterDatesRangeSelectedOption.text === MostRecentText) {
      this.RecentDatesLabel = 'Up to 50 of the most recently saved quotes from the last 7 days';
    }
  }

  private setFilterDatesRangesOptions(): void {
    this.filterDatesRangeSelectedOption = this.filterDatesRangeOptions[0];
  }

  // Pagination
  // ------------------------------------------------------------------------------

  public paginationPageChange(data) {

    if (this.dataInited && this.paginationResultShowFrom !== data.startAt) {
      this.paginationCurrentPage = data.pageNumber;
      this.paginationResultShowFrom = data.startAt;
      this.getQuotes();
    }
  }

  private paginationSetResultLimit(intLimit: any) {
    if (this.filterDatesRangeSelectedOption.text === MostRecentText ) {
      this.paginationResultLimit = parseInt(intLimit, 50)
    }
    else {
      this.paginationResultLimit = parseInt(intLimit, 10);
    }
  }

  public onResultLimitChange($ev): void {
   if(this.paginationResultLimit !== $ev.limit) {
    setTimeout(() => {
      this.paginationSetResultLimit($ev.limit);
      this.getQuotes();
    });
  }
  }


  private updateRangeDateProperty($ev, fieldName: string): void {
    let tmpDate = $ev.date;
    console.log($ev)
    switch (fieldName) {
      case 'start':
        tmpDate = this.datesService.getDateStartRangeFromDate($ev.date);
        this.dateRangeCustom.start = $ev.selectByClick
          ? tmpDate
          : this.dateRangeCustom.start;
         if($ev.selectedManually) { this.filterDatesRangeSelectedOption =this.filterDatesRangeOptions.find(x => x.id === 'custom') }
         break;
      case 'end':
        tmpDate = this.datesService.getDateEndRangeFromDate($ev.date);
        this.dateRangeCustom.end = $ev.selectByClick
          ? tmpDate
          : this.dateRangeCustom.end;
         if($ev.selectedManually) { this.filterDatesRangeSelectedOption =this.filterDatesRangeOptions.find(x => x.id === 'custom') }
        break;
    }

    this.dateRange[fieldName] = tmpDate;
  }

  // Filters on the Page
  // ------------------------------------------------------------------------------
 private setTo7Days(filterText) {
  if(this.filterDatesRangeSelectedOption.text === MostRecentText && filterText !== 'all') {
    this.filterDatesRangeSelectedOption = this.filterDatesRangeOptions[1]
    this.updateFilterDatesLabel()
    this.setRecentDatesLabel()

  }
 }

  private onLobChange(option: FilterOption): void {
    this.filterLobSelectedOption = option;

  }

  private onAgentChange(option: FilterOption): void {
    this.filterAgentsSelectedOption = option;

  }

  private onLocationChange(option: FilterOption): void {
    this.filterLocationsSelectedOption = option;
  }
  private filterDataChangeUpdate($ev): void {
    this.searchQuery = this.searchProperty?.searchQuery;
    this.nameType = this.searchProperty?.nameType;
    if (
      this.dataToChange &&
      ($ev.selectedOption.id !== this.dataToChange.selectedOption.id ||
        $ev.selectedOption.id === 'all')
    ) {
      this.dataToChange = $ev;

      switch ($ev.filterId) {
        case 'filter_LOB':
          this.onLobChange($ev.selectedOption);
          break;
        case 'filter_agents':
          this.onAgentChange($ev.selectedOption);
          break;
        case 'filter_locations':
          this.onLocationChange($ev.selectedOption);
          break;
      }
      this.setTo7Days($ev.selectedOption.id)

      if ((this.searchQuery === undefined) ||(this.searchQuery === '') || (this.searchQuery.length >= 3)) {
        this.getQuotes(false, true);
      }
    } else {
      this.dataToChange = $ev;
    }
  }

  private onDateTypeChange(option: FilterOption): void {

    this.filterDatesRangeOptions = this.datesService.getDateRangesOptions_Quotes(option.text)

    let found=false;
    this.filterDatesRangeOptions.forEach(option => {
      if (option.id == this.filterDatesRangeSelectedOption.id) {
        found=true;
      }
      })
    if (!found)
    {
      this.setFilterDatesRangesOptions();

      this.dateRange.start = null;
      this.dateRange.end = null;
    }

    this.filterDatesTypesOptions.forEach(filterOption => {
      if (filterOption.text == option.text) {
        this.filterDatesTypesSelectedOption=filterOption;
      }
      })

    this.setRecentDatesLabel();

  }

  private onDateRangeChange($ev: FilterOption): void {
    this.dateRange = this.getDateRangeFromSelectedOption(
      $ev,
      this.dateRangeCustom
    );
    this.filterDatesRangeSelectedOption = $ev;

    if ((this.priorSelectedText==MostRecentText && $ev.text == Last7DaysText) ||(this.priorSelectedText==Last7DaysText && $ev.text == MostRecentText) || this.dateRange.recent) {
      this.isForceGetQuotes = true;
    }
    this.priorSelectedText = $ev.text
    if(this.dateRange.recent) {
      this.filterLobSelectedOption = this.filterLobOptions[0]
      this.filterAgentsSelectedOption = this.filterAgentsOptions[0]
      this.filterLocationsSelectedOption = this.filterLocationsOptions[0]
      this.updateFiltersLabels()
    }

    this.setRecentDatesLabel();


  }

  public onFilterDataChange($ev): void {
    if (!this.isPageLoad)
    {
      this.filterDataChangeUpdate($ev);
    }
  }

  private updateDateRangeOptionIfCustomRange(): void {
    this.filterDatesRangeSelectedOption = this.filterDatesRangeOptions.filter(
      opt => opt.id === 'custom'
    )[0];
  }

  public onDatesParamsChangeHandler($ev: any, updateField: string): void {
    // https://bostonsoftware.atlassian.net/browse/SPRC-560
    // When Date Range selection is Custom, filtering shouldn't begin until Ending date is entered
    let allowFiltering = true;
    switch (updateField) {
      case 'dateType':
        this.onDateTypeChange($ev);
        break;
      case 'dateRange':
        this.onDateRangeChange($ev);
        break;
      case 'dateRangeStart':
        this.updateRangeDateProperty($ev, 'start');
        $ev.selectByClick && this.updateDateRangeOptionIfCustomRange();

        if (!this.dateRange.start || !this.dateRange.end) {
          allowFiltering = false;
        }
        break;
      case 'dateRangeEnd':
        this.updateRangeDateProperty($ev, 'end');
        $ev.selectByClick && this.updateDateRangeOptionIfCustomRange();
        if (!this.dateRange.start || !this.dateRange.end) {
          allowFiltering = false;
        }
        break;
    }

    this.updateFilterDatesLabel();
     this.setRecentDatesLabel()
  }

  getQuotesFromFilteredDates() {
    let allowFiltering = true;
    this.searchQuery = this.searchProperty.searchQuery;
    if ( (this.filterDatesRangeSelectedOption.id !== 'all'  &&  this.filterDatesRangeSelectedOption.id !== 'mostRecent' ) && (!this.dateRange.start || !this.dateRange.end) ) {
      allowFiltering = false;
    }

      if(this.filterDatesRangeSelectedOption.id === 'custom') {
        if(!this.dateRange.start) {
          this.dateRange.start = this.dateRange.end
          allowFiltering = true
        }
        if(!this.dateRange.end) {
          this.dateRange.end = this.dateRange.start
          allowFiltering = true
        }
      }

    if ((allowFiltering) && ((this.searchQuery === undefined) || (this.searchQuery === '') || (this.searchQuery.length >= 3))) {
      this.getQuotes(false, true);
    }

    this.FilterDates.tooltip.close();
  }

  resetSearch() {
    this.filterDatesRangeSelectedOption = this.filterDatesRangeOptions[0];
    this.dateRange.start = null;
    this.dateRange.end = null;
    this.searchQuery = null;
    this.filterLobSelectedOption = this.filterLobOptions[0]
    this.filterAgentsSelectedOption = this.filterAgentsOptions[0]
    this.filterLocationsSelectedOption = this.filterLocationsOptions[0]
    this.updateFiltersLabels()
    this.updateFilterDatesLabel()
    this.setRecentDatesLabel()
    this.getQuotes(false, true);
  }

  private updateFilterDatesLabel(): void {
    this.filterDatesLabel = this.formatFilterLabelDateTypes();
  }


  // Helpers
  // ------------------------------------------------------------------------------
  private updateFiltersLabels(): void {
    this.filterLobLabel = this.filterLobSelectedOption.text;
    this.filterAgentsLabel = this.filterAgentsSelectedOption.text;
    this.filterLocationLabel = this.filterLocationsSelectedOption.text;
    this.filterDatesLabel = this.formatFilterLabelDateTypes();
  }

  // 08.30.18 - SA - Just changing the label and leaving the id which is used for processing.
  private formatFilterLabelDateTypes(): string {

    let formatedLabel = 'Last 7 Days';
    if (this.filterDatesRangeSelectedOption.text === MostRecentText) {
       formatedLabel = 'Most Recent';
    }

    const startDate = this.dateRange.start
      ? format(new Date(this.dateRange.start), 'MMM d, yyyy')
      : null;
    const endDate = this.dateRange.end
      ? format(new Date(this.dateRange.end), 'MMM d, yyyy')
      : null;

    if (this.dateRange.start && this.dateRange.end) {
      formatedLabel =
        startDate === endDate ? `${startDate}` : `${startDate} - ${endDate}`;
    } else if (this.dateRange.start && !this.dateRange.end) {
      formatedLabel = `From ${startDate}`;
    } else if (!this.dateRange.start && this.dateRange.end) {
      formatedLabel = `To ${endDate}`;
    }

    if (this.filterDatesTypesSelectedOption.id !== 'all') {
      formatedLabel += ` (${this.filterDatesTypesSelectedOption.text})`;
    }

    return formatedLabel;
  }

  private getDateRangeFromSelectedOption(
    $ev: FilterOption,
    customDateRange: DateRange
  ): DateRange {

    switch ($ev.id) {
      case 'mostRecent':
        return new DateRange(null, null,true);
      case 'all':
        return new DateRange(null, null);
      case 'custom':
        return customDateRange;
      case 'today':
        return this.datesService.getRangeToday();
      case 'yesterday':
        return this.datesService.getRangeYesterday();
      case 'thisweek':
        return this.datesService.getRangeThisWeek();
      case 'thismonth':
        return this.datesService.getRangeThisMonth();
      case 'thisyear':
        return this.datesService.getRangeThisYear();
      case 'lastweek':
        return this.datesService.getRangeLastWeek();
      case 'lastmonth':
        return this.datesService.getRangeLastMonth();
      case 'last30days':
        return this.datesService.getRangeLast30days();
      case 'lastyear':
        return this.datesService.getRangeLastYear();
      case 'nextweek':
        return this.datesService.getRangeNextWeek();
      case 'nextmonth':
        return this.datesService.getRangeNextMonth();
      case 'nextyear':
        return this.datesService.getRangeNextYear();
      default:
        return new DateRange();
    }
  }

  public generateRouterLinkValue(quote: Quote): any[] {
    let routerLink = ['/dashboard/quotes', quote.resourceId];

    if (this.isImported) {
      routerLink = [
        '/dashboard/quotes',
        quote.resourceId,
        { imported: true }
      ] as any[];
    }

    return routerLink;
  }
}
