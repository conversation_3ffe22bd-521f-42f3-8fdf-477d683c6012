<section class="section section--thick">
  <div class="u-flex u-flex--spread">
    <div class="">
      <h1 class="o-heading">Drivers</h1>
    </div>
    <div class="">
    </div>
  </div>

  <form #refEnterNewDriver="ngForm" name="enterNewDriverForm" novalidate (ngSubmit)="newRmvQuote(refEnterNewDriver)">
    <div class="row u-spacing--1-5">
      <div class="col-xs-12">
        <div class="box box--silver">

          <div class="u-spacing--bottom-1-5 u-color-sunset" *ngIf="showErrorInfo() || errorMessage">{{errorMessage}}</div>

          <table class="form-table">
            <tbody>
              <tr class="form-table__row" *ngFor="let driver of driverList; let index = index;">
                
                  <td class="form-table__cell u-width-80px"><div class="u-t-nowrap-only">Driver {{index + 1}}:</div></td>
                  <td class="form-table__cell" [ngClass]="{'is-required-field': !refDriverFirstName.valid && invalidForm}">
                    <input #refDriverFirstName="ngModel" fieldAutofocus type="text" id="firstName{{index}}" name="firstName{{driver.uuid}}" required placeholder="First Name" [(ngModel)]="driver.firstName" (change)="updateClient(index, driver); onInputDataChange()">
                  </td>
                  <td class="form-table__cell" [ngClass]="{'is-required-field': !refDriverLastName.valid && invalidForm}">
                    <input #refDriverLastName="ngModel" id="lastName{{index}}" name="lastName{{driver.uuid}}" type="text" required placeholder="Last Name" [(ngModel)]="driver.lastName" (change)="updateClient(index, driver); onInputDataChange()">
                  </td>
                  <td class="form-table__cell" [ngClass]="{'is-required-field': !refDriverLicenseName.valid && invalidForm}">
                    <input #refDriverLicenseName="ngModel" id="licenseNumber{{index}}" name="licenseNumber{{driver.uuid}}" type="text" required placeholder="License #" [(ngModel)]="driver.license" (change)="onInputDataChange()">
                  </td>
                  <td class="form-table__cell" [ngClass]="{'is-required-field': refDriverDob.ngModel?.invalid && invalidForm}">
                    <app-datepicker-input
                      #refDriverDob
                      [required]="true"
                      [selectDate]="driver.dateOfBirth"
                      (onDateChange)="saveDate($event, driver, index); onInputDataChange()"
                      [placeholder]="'D.O.B.'">
                    </app-datepicker-input>
                  </td>
                  <td class="form-table__cell">
                    <button *ngIf="driverList?.length > 1" (click)="selectDriverForDelete(index)" type="button" class="o-btn o-btn--action o-btn--i_cancel u-t-size--1-7rem modal-delete-driver"></button>
                  </td>
              </tr>
              <tr class="form-table__row">
                <td class="form-table__cell u-width-80px"></td>
                <td class="form-table__cell" colspan="6">
                  <button (click)="addDriver($event, refEnterNewDriver);" type="button" class="o-btn o-btn--action o-btn--i_round-plus u-color-pelorous">Add Driver</button>
                </td>
              </tr>
            </tbody>
          </table>

        </div>
      </div>
    </div>

    <div class="row u-spacing--2">
      <div class="col-xs-12">
        <button type="submit" class="o-btn" [disabled]="invalidForm || duplicateLicense">RMV Lookup</button>
        <a routerLink="/dashboard" class="o-btn o-btn--idle u-spacing--left-2">Cancel</a>
      </div>
    </div>
  </form>
</section>

<app-confirmbox
  [launcher]="'.modal-delete-driver'"
  [question]="'Are you sure you want to delete this driver?'"
  [askAbout]="driverList[selectedDriverIndex]?.firstName + ' ' + driverList[selectedDriverIndex]?.lastName"
  [itemData]="selectedDriverToDelete"
  [confirmBtnText]="'Yes, delete driver'"
  (onAccept)="confirmDelete()"
  (onCancel)="confirmCanceled($event)">
</app-confirmbox>
