import { WARNING_GROUPS, WarningDefinitionI, AdditionalDataI } from 'app/hints-and-warnings/model/warnings';
import { Validate } from 'app/hints-and-warnings/validators';
import { CoverageItemParsed } from 'app/app-model/coverage';
import { BasicPolicyInfo } from 'app/app-model/umbrella';

function validPolicyItemParsed(value, fullObj:CoverageItemParsed):boolean {
  // return fullObj.isRequired && !fullObj.isActive && !fullObj.isDisabled;
  return fullObj.isRequired && !fullObj.isDisabled && Validate.isEmptyValue(fullObj.currentValue);
}

function generateUqOptionViewId(policy:CoverageItemParsed):string {
  return policy.viewUqId;
}

// Umbrella Basics Tab - General Options,
//------------------------------------------------------------------------------
/**
 * Validation for Home General Options Data
 * For Model: CoverageItemParsed
 */

function generateViewUrlGeneralOptions(fullObj: CoverageItemParsed):string {
  return '/dashboard/umbrella/quotes/' + fullObj.quoteResourceId + '/basics';
}

const policyItemUmbrellaParsedGeneralOption:WarningDefinitionI = {
  id: 'currentValue',
  deepId: 'currentValue',
  viewUri: generateViewUrlGeneralOptions,
  viewFieldId: generateUqOptionViewId,
  warnings: [{
    label: (val, fullObj:CoverageItemParsed) => 'Required ' + fullObj.description  + '.',
    condition: validPolicyItemParsed,
    group: WARNING_GROUPS.general,
    carriers: (fullObj: CoverageItemParsed) => {
      return fullObj.ratingPlanId ? fullObj.ratingPlanId.split(',') : [];
    }
  }]
};

export const WARNINGS_DEFINITIONS_UMBRELLA_OPTIONS_GENERAL_OPTIONS: WarningDefinitionI[] = [
  policyItemUmbrellaParsedGeneralOption
];


// Umbrella Basics Tab - PRIMARY POLICY INFORMATION,
//------------------------------------------------------------------------------
function generateViewUrl(fullObj:BasicPolicyInfo):string {
  return  '/dashboard/umbrella/quotes/' + fullObj.quoteSessionId + '/basics';
}

function requiredField(value):boolean {
  return Validate.isEmptyValue(value);
}


/**
 * Validation for PRIMARY POLICY INFORMATION Data
 * For Model: BasicPolicyInfo
 */
const primaryPolicyInformationLocation:WarningDefinitionI = {
  id: 'location',
  deepId: 'location',
  viewUri: generateViewUrl,
  viewFieldId: 'primaryPolicyInformationLocation',
  warnings: [{
    label: (value, fullObj) => 'Required Primary Residence Location.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}

const primaryPolicyInformationUnderlyingLimits:WarningDefinitionI = {
  id: 'underlyingLimits',
  deepId: 'underlyingLimits',
  viewUri: generateViewUrl,
  viewFieldId: 'primaryPolicyInformationUnderlyingLimits',
  warnings: [{
    label: (value, fullObj) => 'Required Underlying Limits.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}

const primaryPolicyInformationPolicyLimits:WarningDefinitionI = {
  id: 'policyLimits',
  deepId: 'policyLimits',
  viewUri: generateViewUrl,
  viewFieldId: 'primaryPolicyInformationPolicyLimits',
  warnings: [{
    label: (value, fullObj) => 'Required Policy Limits.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}

export const WARNINGS_DEFINITIONS_UMBRELLA_PRIMARY_POLICY_INFORMATION: WarningDefinitionI[] = [
  primaryPolicyInformationLocation,
  primaryPolicyInformationUnderlyingLimits,
  primaryPolicyInformationPolicyLimits
];