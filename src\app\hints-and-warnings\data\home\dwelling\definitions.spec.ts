import {
    generateCarriers,
    generateLabels,
    generateViewFieldIds,
    generateViewURIs,
    runConditions,
} from 'testing/helpers/warning-definitions';

import { Dwelling, DwellingLocation, DwellingSubsystems } from 'app/app-model/dwelling';
import {
    WARNINGS_DEFINITIONS_HOME_DWELLING_LOCATION,
    WARNINGS_DEFINITIONS_HOME_DWELLINGS,
    WARNINGS_DEFINITIONS_HOME_DWELLINGS_SUBSYSTEMS
} from './definitions';


describe('Definitions: HomeDwelling', () => {
    describe('when home dwelling location validator is used', () => {
        let definitions: any[];
        let dwellingLocation: DwellingLocation;

        beforeEach(() => {
            definitions = WARNINGS_DEFINITIONS_HOME_DWELLING_LOCATION;
            dwellingLocation = {
                meta: {
                    href: '',
                    rel: []
                },
                address1: '',
                address2: '',
                city: '',
                state: '',
                zip: '',
                quoteSessionId: '',
                resourceId: '',
                resourceName: '',
                parentId: ''
            };
        });

        it('allows to check if required fields are provided', () => {
            const errors = runConditions(definitions, dwellingLocation, {});

            expect(errors).toEqual([
                'address1', 'city', 'state', 'zip'
            ]);
        });

        it('generates viewFieldIds without errors', () => {
            expect(() => {
                generateViewFieldIds(definitions, dwellingLocation);
            }).not.toThrow();
        });

        it('generates viewURIs without errors', () => {
            expect(() => {
                generateViewURIs(definitions, dwellingLocation);
            }).not.toThrow();
        });

        it('generates labels without errors', () => {
            expect(() => {
                generateLabels(definitions, dwellingLocation);
            }).not.toThrow();
        });

        it('generates carriers without errors', () => {
            expect(() => {
                generateCarriers(definitions, dwellingLocation);
            }).not.toThrow();
        });
    });

    describe('when home dwelling validator is used', () => {
        let definitions: any[];
        let dwelling: Dwelling;

        beforeEach(() => {
            definitions = WARNINGS_DEFINITIONS_HOME_DWELLINGS;
            dwelling = {
                meta: {
                    href: ''
                },
                constructionMaterialTypeCode: '',
                constructionYear: undefined,
                constructionAge: undefined,
                livingSpaceArea: undefined,
                utilizationTypeCode: '',
                familiesCount: undefined,
                storiesCount: '',
                designStyleTownhouseInd: false,
                townhouseUnitsCount: undefined,
                poolOnPremisesInd: false,
                mortgageeCount: undefined,
                dwellingLocation: {
                    href: ''
                },
                coverages: {
                    href: ''
                },
                protectionClass: {
                    href: ''
                },
                protectionDevices: {
                    href: ''
                },
                subsystems: {
                    href: ''
                },
                quoteSessionId: '',
                resourceId: '',
                resourceName: '',
                parentId: ''
            };
        });

        it('allows to check if required fields are provided', () => {
            const errors = runConditions(definitions, dwelling, {
                quoteFormTypes: '',
                quoteSelectedPlansIds: [
                    '119', '92'
                ],
                quote: {
                    effectiveDate: '2017-11-14'
                }
            });

            expect(errors).toEqual([
                'constructionMaterialTypeCode', 'constructionYear', 'livingSpaceArea',
                'utilizationTypeCode', 'storiesCount', 'mortgageeCount', 'familiesCount'
            ]);
        });

        it('allows to check if constructionYear is within accepted range', () => {
            dwelling.constructionYear = 1879;
            dwelling.constructionMaterialTypeCode = 'a';
            dwelling.utilizationTypeCode = 'a';
            dwelling.familiesCount = 1;

            const errors = runConditions(definitions, dwelling, {
                quoteFormTypes: 'HO3',
                quoteSelectedPlansIds: [
                    '69', '77', '78', '99'
                ],
                quote: {
                    effectiveDate: '2017-11-14'
                }
            });

            expect(errors).toEqual([
                'constructionYear', 'constructionYear',
                'constructionYear', 'constructionYear'
            ]);
        });

        it('generates viewFieldIds without errors', () => {
            expect(() => {
                generateViewFieldIds(definitions, dwelling);
            }).not.toThrow();
        });

        it('generates viewURIs without errors', () => {
            expect(() => {
                generateViewURIs(definitions, dwelling);
            }).not.toThrow();
        });

        it('generates labels without errors', () => {
            expect(() => {
                generateLabels(definitions, dwelling);
            }).not.toThrow();
        });

        it('generates carriers without errors', () => {
            expect(() => {
                generateCarriers(definitions, dwelling);
            }).not.toThrow();
        });
    });

    describe('when home dwelling subsystems validator is used', () => {
        let definitions: any[];
        let dwellingSubsystems: DwellingSubsystems;

        beforeEach(() => {
            definitions = WARNINGS_DEFINITIONS_HOME_DWELLINGS_SUBSYSTEMS;
            dwellingSubsystems = {
                electricalWiringTypeCode: '',
                electricalLastUpdatedYear: undefined,
                heatingPrimaryTypeCode: '',
                heatingPrimaryLastUpdatedYear: undefined,
                heatingSecondaryTypeCode: '',
                plumbingMaterialTypeCode: '',
                plumbingLastUpdatedYear: undefined,
                roofMaterialTypeCode: '',
                roofLastUpdatedYear: undefined,
                oilStorageTankLocation: ''
            };
        });

        it('allows to check if required fields are provided', () => {
            const errors = runConditions(definitions, dwellingSubsystems, {
                quoteSelectedPlansIds: [
                    '119', '97'
                ],
                quote: {
                    effectiveDate: '2017-11-14'
                }
            });

            expect(errors).toEqual([
                'heatingPrimaryTypeCode', 'heatingSecondaryTypeCode', 'plumbingMaterialTypeCode',
                'electricalWiringTypeCode', 'roofMaterialTypeCode'
            ]);
        });

        it('generates viewFieldIds without errors', () => {
            expect(() => {
                generateViewFieldIds(definitions, dwellingSubsystems);
            }).not.toThrow();
        });

        it('generates viewURIs without errors', () => {
            expect(() => {
                generateViewURIs(definitions, dwellingSubsystems);
            }).not.toThrow();
        });

        it('generates labels without errors', () => {
            expect(() => {
                generateLabels(definitions, dwellingSubsystems);
            }).not.toThrow();
        });

        it('generates carriers without errors', () => {
            expect(() => {
                generateCarriers(definitions, dwellingSubsystems);
            }).not.toThrow();
        });
    });
});
