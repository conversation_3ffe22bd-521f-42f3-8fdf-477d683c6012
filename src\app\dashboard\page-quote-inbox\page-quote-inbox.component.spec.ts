import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { RouterTestingModule } from '@angular/router/testing';

import { StubConfirmboxComponent } from 'testing/stubs/components/confirmbox.component';
import { StubFilterComponent } from 'testing/stubs/components/filter.component';
import { StubModalboxComponent } from 'testing/stubs/components/modalbox.component';
import { StubPaginationComponent } from 'testing/stubs/components/pagination.component';
import { StubResultsLimiterComponent } from 'testing/stubs/components/results-limiter.component';

import { PageQuoteInboxComponent } from './page-quote-inbox.component';

describe('Component: PageQuoteInbox', () => {
  let component: PageQuoteInboxComponent;
  let fixture: ComponentFixture<PageQuoteInboxComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      declarations: [
        PageQuoteInboxComponent,
        StubFilterComponent,
        StubModalboxComponent,
        StubPaginationComponent,
        StubResultsLimiterComponent,
        StubConfirmboxComponent
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PageQuoteInboxComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  /*
  it('should allow to select single item', () => {
    const checkbox = fixture.debugElement.query(By.css('.o-btn--checkbox'));
    checkbox.triggerEventHandler('click', {});

    fixture.detectChanges();

    expect(component.quoteInboxVisibleItems[0].isSelected).toBeTruthy();
  });

  it('should allow to select all items', () => {
    const link = fixture.debugElement.query(By.css('.section:nth-child(4) .o-link'));
    link.triggerEventHandler('click', {});

    fixture.detectChanges();

    component.recordsToDisplay.forEach((record) => {
      expect(record.isSelected).toBeTruthy();
    });
  });

  it('should delete selected items', () => {
    const record = component.recordsToDisplay[0];

    const confirmBox = fixture.debugElement.query(By.directive(StubConfirmboxComponent)).componentInstance;
    confirmBox.onAccept.emit({
      itemData: [ record ]
    });

    fixture.detectChanges();

    expect(component.recordsToDisplay).not.toContain(record);
  });

  it('should allow to delete selected item from details modal', () => {
    const record = component.recordsToDisplay[0];

    const link = fixture.debugElement.query(By.css('.modal-launcher-quote-inbox'));
    link.triggerEventHandler('click', {});

    fixture.detectChanges();

    const btn = fixture.debugElement.query(By.directive(StubModalboxComponent)).query(By.css('.o-btn--outlined'));
    btn.triggerEventHandler('click', {});

    fixture.detectChanges();

    expect(component.recordsToDisplay).not.toContain(record);
  });
  */
});
