
import {take} from 'rxjs/operators';
import { ActivatedRoute } from '@angular/router';
import { LobTabbedNavComponent } from './../lob-tabbed-nav/lob-tabbed-nav.component';
import { SearchByNameComponent } from './../search-by-name/search-by-name.component';
import { FeatureService } from 'app/shared/services/feature.service';
import { Component, OnInit, ViewChild, Input, AfterViewInit } from '@angular/core';
import { BroadcastService } from 'app/shared/services/broadcast.service';

import { FilterOption } from 'app/app-model/filter-option';
import {
  ClientListItemApiResponse,
  ClientListItemDetails
} from 'app/app-model/client';
import { ClientsService } from '../app-services/clients.service';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { SubscriptionLike as ISubscription } from 'rxjs';
import { Feature } from '../../shared/services/feature.service';
import { format, parseISO } from 'date-fns';

@Component({
    selector: 'app-page-clients',
    templateUrl: './page-clients.component.html',
    styleUrls: ['./page-clients.component.scss'],
    standalone: false
})
export class PageClientsComponent implements OnInit {
  @ViewChild(SearchByNameComponent) searchProperties;
  @ViewChild(LobTabbedNavComponent) tabbedNav;
  public paginationResultsCount = 0;
  public paginationResultLimit = 10;
  public paginationResultShowFrom = 0;
  public paginationCurrentPage = 1;

  public arrClientsAll: ClientListItemDetails[];
  public arrClientsFiltered: ClientListItemDetails[];
  private dataInitiated = false;
  public searchQuery: string;
   nameType;
   clientType;
splitList = [
  {text: 'Personal Clients', link: 'clients'  },
  {text: 'Commercial Clients', link: 'clients', type: 'commercial'}
];
PersonalCommercialSeparate;
resetQuery;

  private getClientsSubscription: ISubscription;
  private getClientsRequestDataString = '';

  constructor(
    private clientsService: ClientsService,
    private overlayLoaderService: OverlayLoaderService,
    private featureService: FeatureService,
    private route: ActivatedRoute
  ) {}

  ngOnInit() {
this.route.queryParams.subscribe(params => {
      params['lob'] ? this.clientType = 'Commercial' : this.clientType = '';
   //   this.getClients(true);
    });
    setTimeout(() => {
   this.PersonalCommercialSeparate =  this.featureService.isCommercialSplit();
    }, 100);
  //  this.featureService.getFeaturesFromAPI().subscribe(x =>  {
  //    split = x.items.find(d => d.name === 'PersonalCommercialSeparate');  /// change name once feature flag is enabled
  //    this.PersonalCommercialSeparate = split.enabled;
  //  });
  }
  ngOnDestroy() {
    this.getClientsSubscription && this.getClientsSubscription.unsubscribe();
  }
  public filterByName(event) {
    this.searchQuery = this.searchProperties.searchQuery;
    this.nameType = this.searchProperties.nameType;
    if (event.key === 'Enter' && this.searchQuery.length >= 3) {
      this.getClients(true);
    } else if (event.target.tagName === 'BUTTON' && this.searchQuery.length >= 3) {
      this.getClients(true);
    }
  }

  clearSearch(clientType) {
    if (this.clientType !== clientType) {
      this.arrClientsAll = [];
      this.searchQuery = null;
      this.paginationResultsCount = 0;
    }
  }

  private getClients(showResultsFromFirstPage: boolean = false): void {
    let lob, agentId, locationId, dateType, startDate, endDate, name, clientId;

    if (showResultsFromFirstPage) {
      this.paginationResultShowFrom = 0;
      this.paginationCurrentPage = 1;
    }

    name = this.searchQuery ? this.searchQuery : '';
    lob = '';
    agentId = '';
    locationId = '';
    dateType = '';
    startDate = '';
    endDate = '';
    clientId = '';
    // If request data hasn't changed, do not send request
    const currentRequestDataString =
      this.paginationResultShowFrom.toString() +
      this.paginationResultLimit.toString() +
      name + agentId + lob + locationId + dateType + startDate + endDate + clientId + this.nameType + this.clientType;

    if (this.getClientsRequestDataString === currentRequestDataString) {
      return;
    }

    this.getClientsRequestDataString = currentRequestDataString;

    if (this.getClientsSubscription) {
      this.overlayLoaderService.hideLoader();
      this.getClientsSubscription.unsubscribe();
    }

    this.overlayLoaderService.showLoader('Loading Results...');
   const params = {
    offset: this.paginationResultShowFrom,
    limit: this.paginationResultLimit,
    name,
    agentId,
    lob,
    locationId,
    dateType,
    startDate,
    endDate,
    clientId,
    clientType: this.clientType,
    nameType: this.nameType
    };

    this.getClientsSubscription = this.clientsService
      .getClients(params).pipe(
      take(1))
      .subscribe(
        (agencyClients: ClientListItemApiResponse) => {
          this.dataInitiated = true;
          this.arrClientsAll = agencyClients.items;
          this.arrClientsFiltered = this.arrClientsAll;
          this.paginationResultsCount = agencyClients.size;
          this.overlayLoaderService.hideLoader();
        },
        reject => {
          this.overlayLoaderService.hideLoader();
        }
      );
  }

  public getPreferredContactValue(arrContactMethods: any): string {
    if (
      arrContactMethods == null ||
      arrContactMethods.items == null ||
      typeof arrContactMethods.items === 'undefined'
    ) {
      return '';
    }
    let retString = '';
    arrContactMethods.items.forEach(item => {
      if (item != null && item.preferredMethod === 'True') {
        retString = this.formatPhoneNumber(item.value);
        if (item.type) {
          retString = retString + '     ' + item.type;
        }
        return retString;
      }
    });
    return retString;
  }

  public formatPhoneNumber(phoneNumberString) {
    const cleaned = ('' + phoneNumberString).replace(/\D/g, '');
    const match = cleaned.match(/^(1|)?(\d{3})(\d{3})(\d{4})$/);
    if (match) {
      const intlCode = match[1] ? '+1 ' : '';
      return [intlCode, '(', match[2], ') ', match[3], '-', match[4]].join('');
    }
    return phoneNumberString;
  }

  public getPreferredContactMethod(arrContactMethods: any): string {
    if (
      arrContactMethods == null ||
      arrContactMethods.items == null ||
      typeof arrContactMethods.items === 'undefined'
    ) {
      return '';
    }

    arrContactMethods.items.forEach(item => {
      if (item != null && item.preferredMethod === 'True') {
        return item.type;
      }
    });
    return '';
  }

  // Pagination
  // ------------------------------------------------------------------------------

  public paginationPageChange(data) {
    if (this.dataInitiated && this.paginationResultShowFrom !== data.startAt) {
      this.paginationCurrentPage = data.pageNumber;
      this.paginationResultShowFrom = data.startAt;
      this.getClients();
    }
  }

public parseLastModifiedDate(date: string): string {
  return date ? format(new Date(date), 'MMM d, yyyy') : '';
}

  private paginationSetResultLimit(intLimit: any) {
    this.paginationResultLimit = parseInt(intLimit, 10);
  }

  public onResultLimitChange($ev): void {
    setTimeout(() => {
      if (this.paginationResultLimit !== $ev.limit) {
        this.paginationSetResultLimit($ev.limit);
        if (this.searchQuery && this.searchQuery.length > 3) {
          this.getClients();
        }

      }
    });
  }

  resetSearch() {
    this.searchQuery = null;
    // this.getClients();
  }
}
