
import {take} from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { BehaviorSubject ,  SubscriptionLike as ISubscription } from 'rxjs';

// Services
import { OptionsService } from 'app/dashboard/app-services/options.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { AgencyUserService } from 'app/shared/services/agency-user.service';
import { VehiclesService } from 'app/dashboard/app-services/vehicles.service';
import { CoveragesService } from 'app/dashboard/app-services/coverages.service';


// Models
import { Vehicle, VehicleCoverages } from 'app/app-model/vehicle';
import { QuoteAuto, QuotePlan, QuotePlanListAPIResponse } from 'app/app-model/quote';
import {
  Coverage,
  PolicyCoveragesData,
  CoverageItem,
  CoverageItemParsed,
  CoverageApiResponseData,
  CoveragesAutoDefaultStandardCoverages,
  CoverageItemValue,
  ICoverageItemParsedAdditionalData,
} from 'app/app-model/coverage';



interface TriggerSourceI {
  triggerBy?: 'Vehicles' | 'QuoteEffectiveDate';
  vehiclesResourceId?: string[]; // if defined, will be processed only for specific vehicles
  forceDefaultValues?: boolean;
}

interface PromiseDataAfterSettingDefaultValuesNew {
  policiesToUpdate: CoverageItemParsed[];
  policyItemsParsed: CoverageItemParsed[];
}

interface PromiseDataAfterSavingToApi {
  status: string;
  policyItemsParsed: CoverageItemParsed[];
}

class CoverageItemParsedAdditionalData implements ICoverageItemParsedAdditionalData {
  public errorMessage = '';
  public hasError = false;
  public forVehicle: Vehicle;
}

export interface IValidateVehiclesStandardCoveragesOptionResult {
  coverageItemParsed: CoverageItemParsed;
  errors: string[];
}

@Injectable()
export class AutoStandardCoveragesAutomanagerService {
  effectiveDateChanged: boolean;

  constructor(
    private storageService: StorageService,
    private agencyUserService: AgencyUserService,
    private specsService: SpecsService,
    private optionsService: OptionsService,
    private vehiclesService: VehiclesService,
    private coveragesService: CoveragesService
  ) { }
  private serviceIsInitialized = false;

  private subscriptionQuote: ISubscription;
  private subscriptionSelectedPlans: ISubscription;
  private subscriptionParserTrigger: ISubscription;
  private subscriptionVehicles: ISubscription;

  private _parserTrigger: BehaviorSubject<TriggerSourceI> = new BehaviorSubject({});

  private quote: QuoteAuto;
  private quoteId = '';
  private quoteLob = '';
  private quoteBeforeChange: QuoteAuto = null;
  private selectedPlans: QuotePlan[] = [];
  private selectedPlansIds: string[] = [];
  private vehicles: Vehicle[] = [];
  private initializedVehicles: string[] = []; // required to force default values (only for initialized vehicles);

  private defaultCoverageValues: Coverage[] = [];

  // TEST Set Validation For options each time, the options has been updated
  private subscriptionAutoCoveragesStandardForVehicles: ISubscription;
  private vehiclesStandardCoveragesToCompare: VehicleCoverages[] = [];

  public initialize(): Promise<void> {
    if (this.serviceIsInitialized) {
      return Promise.resolve();
    }

    this.serviceIsInitialized = true;
    console.log('Initialize Standard Coverages');

    return Promise.all([
      this.subscribeQuote(),
      this.subscribeSelectedPlans(),
      this.subscribeVehicles()
    ]).then(() => {
      this.getDefaultCoveragesValues();
    }).then(() => {
      return this.initPolicyItemsParser();
    }).then(() => {
      return this.subscribeAutoCoveragesStandardForVehicles();
    }).catch(err => {
      console.log(err);
    });
  }

  public destroy() {
    this.serviceIsInitialized = false;
    // Remove vehicles recourceIds from 'initializedVehicles' to avoid issues with setting default values after the user
    // has been logout and restored session
    this.initializedVehicles = [];

    this.subscriptionQuote && this.subscriptionQuote.unsubscribe();
    this.subscriptionSelectedPlans && this.subscriptionSelectedPlans.unsubscribe();
    this.subscriptionParserTrigger && this.subscriptionParserTrigger.unsubscribe();
    this.subscriptionVehicles && this.subscriptionVehicles.unsubscribe();
    this.subscriptionAutoCoveragesStandardForVehicles && this.subscriptionAutoCoveragesStandardForVehicles.unsubscribe();
  }

  private subscribeQuote(): Promise<QuoteAuto> {
    return new Promise((resolve, reject) => {
      this.subscriptionQuote = this.storageService.getStorageData('selectedQuote')
        .subscribe((quote: QuoteAuto) => {
          this.quote = JSON.parse(JSON.stringify(quote));
          this.quoteId = quote.resourceId;
          this.quoteLob = quote.lob;

          resolve(this.quote);

          if (!this.quoteBeforeChange) {
            this.quoteBeforeChange = JSON.parse(JSON.stringify(quote));
          } else if (
            this.quoteBeforeChange.effectiveDate !== this.quote.effectiveDate
          ) {
            this.quoteBeforeChange = JSON.parse(JSON.stringify(quote));

            // INIT POLICY ITEMS PARSING After Quote Effective date change
            // Emit Observable to Parse Items
            this._parserTrigger.next({ triggerBy: 'QuoteEffectiveDate' });
          }
        });
    });
  }

  private subscribeSelectedPlans(): Promise<QuotePlan[]> {
    return new Promise((resolve, reject) => {
      this.subscriptionSelectedPlans = this.storageService.getStorageData('selectedPlan')
        .subscribe((res: QuotePlanListAPIResponse) => {
          if (res && res.items && res.items.length) {
            this.selectedPlans = JSON.parse(JSON.stringify(res.items[0].items));
            this.selectedPlansIds = this.selectedPlans.map(plan => plan.ratingPlanId);
            resolve(this.selectedPlans);
          }
        });
    });
  }
  private subscribeAutoCoveragesStandardForVehicles(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.subscriptionAutoCoveragesStandardForVehicles = this.storageService.getStorageData('autoCoveragesStandardForVehicles')
        .subscribe((res: VehicleCoverages[]) => {
          const vehiclesStandardCoverages = res;

          // console.log('TO VALIDATE', res, JSON.stringify(this.vehiclesStandardCoveragesToCompare) !== JSON.stringify(vehiclesStandardCoverages));

          if (JSON.stringify(this.vehiclesStandardCoveragesToCompare) !== JSON.stringify(vehiclesStandardCoverages)) {
            const vehiclesStandardCoveragesAfterValidation = this.validateVehiclesStandardCoverages(vehiclesStandardCoverages);

            this.vehiclesStandardCoveragesToCompare = JSON.parse(JSON.stringify(vehiclesStandardCoveragesAfterValidation));
            this.storageService.setStorageData('autoCoveragesStandardForVehicles', vehiclesStandardCoveragesAfterValidation);
          } else {
            // console.log('>>> DO NOT VALIDATE: vehiclesStandardCoverages');
          }

        });
      resolve();
    });
  }
  // TEST

  private subscribeVehicles(): Promise<Vehicle[]> {
    let vehiclesCount = 0;
    let vehicles: Vehicle[] = [];

    return new Promise((resolve, reject) => {
      this.subscriptionVehicles = this.storageService.getStorageData('vehiclesList')
        .subscribe((res: Vehicle[]) => {
          this.vehicles = JSON.parse(JSON.stringify(res));
          resolve(this.vehicles);

          if (vehiclesCount !== this.vehicles.length) {
            vehiclesCount = this.vehicles.length;

            // Keep only coverages data for the existing vehicles
            this.storageService.getStorageData('autoCoveragesStandardForVehicles').pipe(
              take(1))
              .subscribe(res => {
                const toKeep = res.filter(el => {
                  const exists = this.vehicles.find(veh => el.vehicleResourceId === veh.resourceId);
                  return exists ? true : false;
                });
                this.storageService.setStorageData('autoCoveragesStandardForVehicles', toKeep);
              });
          }

          // Update vehicle Data each time the vehicle has been updated
          this.storageService.getStorageData('autoCoveragesStandardForVehicles').pipe(
            take(1))
            .subscribe((res: VehicleCoverages[]) => {
              this.vehicles.forEach((vehicle: Vehicle) => {
                const vehicleCoveragesToUpdate = res.find((item: VehicleCoverages) => {
                  return item.vehicleResourceId === vehicle.resourceId;
                });

                if (vehicleCoveragesToUpdate) {
                  vehicleCoveragesToUpdate.vehicle = vehicle;
                }
              });
              this.storageService.setStorageData('autoCoveragesStandardForVehicles', res);
            });


          // Emmit Observable to Parse Items only on vehicles count change or vehicle type change
          const vehiclesCoveragesToRefresh = this.vehicles.filter(veh => {
            const vehicleToCheck = vehicles.find(item => item.resourceId === veh.resourceId);

            if (veh.resourceId && !vehicleToCheck) {
              return true; // New vehicle, require update for vehicle
            } else if (vehicleToCheck.vehicleType === veh.vehicleType) {
              return false; // Vehicle type hasn't change
            } else {
              return true; // Vehicle type has changed, require update for vehicle
            }
          });

          // Save old data about vehicles
          vehicles = JSON.parse(JSON.stringify(this.vehicles));

          // If new vehicle or vehicleType has change for some vehicle, Emit Observable
          if (vehiclesCoveragesToRefresh && vehiclesCoveragesToRefresh.length) {
            const vehiclesIds = vehiclesCoveragesToRefresh.map(el => el.resourceId);
            this._parserTrigger.next({triggerBy: 'Vehicles', vehiclesResourceId: vehiclesIds});
          }
        });
    });
  }

  private updateInitializedVehicles(vehicleResourceId: string, dataTest: any): void {
    const alreadyInitialized = this.initializedVehicles.find(el => el === vehicleResourceId);

    if (!alreadyInitialized) {
      this.initializedVehicles.push(vehicleResourceId);
    }
  }


  // TODO:: Add parameter with vehicle Type
  private initPolicyItemsParser(): Promise<void> {

    return new Promise((resolve, reject) => {
      this.subscriptionParserTrigger = this._parserTrigger.asObservable().subscribe((res: TriggerSourceI) => {
        let vehiclesToParseFor: Vehicle[] = [];

        if (res.vehiclesResourceId && res.vehiclesResourceId.length) {
          vehiclesToParseFor = this.vehicles.filter(veh => res.vehiclesResourceId.indexOf(veh.resourceId) !== -1);
        } else {
          vehiclesToParseFor = this.vehicles;
        }
        this.effectiveDateChanged = res.triggerBy === 'QuoteEffectiveDate';


        this.getOptionsPoliciesListAndParseDataForVehicles(vehiclesToParseFor)
          .then(() => resolve())
          .catch(err => reject(err));
      });
    });
  }

  private getcoverageGroupForRequest(vehicle: Vehicle): string {
    switch (vehicle.vehicleType) {
      case 'Trailer':
        return 'vehicleStandardTrailer';
      case 'Motorcycle':
        return ' vehicleStandardMotorcycle';
      default:
        return 'vehicleStandard';
    }
  }

  private getOptionsPoliciesListAndParseDataForVehicles(vehicles: Vehicle[]): Promise<void> {
    if (!vehicles || !vehicles.length) {
      return ;
    }

    const selectedPlansIdsString = this.selectedPlansIds.join(',');
    const states = 'MA';
    const quoteEffectiveDate = (this.quote && this.quote.effectiveDate) ? this.quote.effectiveDate : '';

    let agencyId;
    this.agencyUserService.userData$.pipe(take(1)).subscribe(agent => agencyId = agent.agencyId);

    const promises: Promise<void>[] = [];

    vehicles.forEach((vehicle: Vehicle) => {
      // Where from get rating coverages
      const coverageGroup = this.getcoverageGroupForRequest(vehicle);

      const tmpPromise: Promise<void> = new Promise((resolve, reject) => {
        this.specsService.getRatingCoverages(states, this.quoteLob, coverageGroup, selectedPlansIdsString, '', quoteEffectiveDate).pipe(
          take(1))
          .subscribe(res => {
            let policiesItems: CoverageItem[] = [];
            if (res && res.items && res.items.length) {
              policiesItems = [...res.items];
            }

            if (policiesItems && this.quoteId) {
              let vehicleAlreadyInitialized = false;
              vehicleAlreadyInitialized = this.initializedVehicles.indexOf(vehicle.resourceId) !== -1;
              // If vehicle already initialized, force setting default options on Vehicle changes (when Vehicle Type change);

              this.processPoliciesItemsParsingForVehicle(
                policiesItems,
                this.selectedPlans,
                this.quoteId,
                vehicle,
                vehicleAlreadyInitialized
              )
              .then((res: VehicleCoverages) => {
                // Update initialized vehicles to be able to force default options next time
                this.updateInitializedVehicles(res.vehicleResourceId, res);
                this.storageService.updateAutoCoveragesStandardForVehiclesSingleItem(res);

                // Update coverages To API
                this.saveCoveragesForVehicleToAPI(res);
              })
              // TODO:: set Default value for options that has only one available option to chose
              .then(() => resolve())
              .catch(err => {
                console.log(err);
                reject(err);
              });
            }
          },
          err => {
            console.log('Fetching Rating Coverages for: ' + coverageGroup + ' ERROR Occurred: ', err);
            reject(err);
          }
        );
      });

      promises.push(tmpPromise);

    });

    return Promise.all(promises).then(() => void 0);
  }



  private adjustCoverageValueBasedOnDefaultValuesAndAvailableOptions(policy: CoverageItemParsed, defaultValues: Coverage[], forceDefaultValues: boolean = false): CoverageItemParsed {
    let setFirstAvailableOptionsAsSelected = false;
    if (policy.currentValue) {
      const currentValueInAvailableOptions = policy.values.find((option: CoverageItemValue) => policy.currentValue === option.value);

      // If already value set and default value is not forced
      if (currentValueInAvailableOptions && !forceDefaultValues) {
        // console.log('RETURN UNMODIFIED POLICY', currentValueInAvailableOptions)
        return policy;
      }

      // Check if there is expected value in default values for policy
      const expectedDefaultValue = defaultValues.find(coverage => coverage.coverageCode === policy.coverageCode);

      if (expectedDefaultValue && expectedDefaultValue.values && expectedDefaultValue.values[0]) {
        // console.log('%c There is expected Value: ', 'background:green', expectedDefaultValue);

        // check up if expected value in available options
        const expectedValueInAvailableOptions = policy.values.find((option: CoverageItemValue) => expectedDefaultValue.values[0].value === option.value);

        if (expectedValueInAvailableOptions && !this.effectiveDateChanged) {
          policy.currentValue = expectedDefaultValue.values[0].value;
        } else {
          setFirstAvailableOptionsAsSelected = true;
        }
      } else {
        setFirstAvailableOptionsAsSelected = true;
      }

    } else {
      setFirstAvailableOptionsAsSelected = true;
    }

    if (setFirstAvailableOptionsAsSelected) {
      let valueToSet: CoverageItemValue = policy.values && policy.values[0] ? <CoverageItemValue>policy.values[0] : null;
      if (policy.currentValue.includes('20/')) {
        valueToSet =  <CoverageItemValue>policy.values.find((option: CoverageItemValue) => '25/50' === option.value);
      }
      if (policy.currentValue === '45/45') {
        valueToSet = <CoverageItemValue>policy.values.find((option: CoverageItemValue) => '50/50' === option.value);
      }
      policy.currentValue = valueToSet ? valueToSet.value : policy.currentValue;
      if (policy.coverageGroup === 'VehicleStandardTrailer') {
        policy.currentValue = policy.defaultValue;
      }
    }

    return policy;
  }

  private setPolicyItemParsedDefaultValueProperty(policy: CoverageItemParsed, defaultValues: Coverage[]): CoverageItemParsed {
    let setFirstAvailableOptionsAsSelected = false;

    // Check if there is expected value in default values for policy
    const expectedDefaultValue = defaultValues.find(coverage => coverage.coverageCode === policy.coverageCode);

    if (expectedDefaultValue && expectedDefaultValue.values && expectedDefaultValue.values[0]) {
      // console.log('%c There is expected Value: ', 'background:green', expectedDefaultValue);

      // check up if expected value in available options
      const expectedValueInAvailableOptions = policy.values.find((option: CoverageItemValue) => expectedDefaultValue.values[0].value === option.value);

      if (expectedValueInAvailableOptions) {
        policy.defaultValue = expectedDefaultValue.values[0].value;
      } else {
        setFirstAvailableOptionsAsSelected = true;
      }
    } else {
      setFirstAvailableOptionsAsSelected = true;
    }


    if (setFirstAvailableOptionsAsSelected) {
      const valueToSet: CoverageItemValue = policy.values && policy.values[0] ? <CoverageItemValue>policy.values[0] : null;
      policy.defaultValue = valueToSet ? valueToSet.value : '';
    }

    return policy;
  }



  // public processPoliciesItemsParsingForVehicle(policies:CoverageItem[], selectedPlans:QuotePlan[], quoteId:string, vehicle: Vehicle):Promise<VehicleCoverages[]> {
  public processPoliciesItemsParsingForVehicle(policies: CoverageItem[], selectedPlans: QuotePlan[], quoteId: string, vehicle: Vehicle, forceDefaultValues: boolean = false): Promise<VehicleCoverages> {
    let arrPoliciesItemsParsed = this.optionsService.parsePoliciesItemsToPoliciesItemsParsed(policies, vehicle.resourceId);
    arrPoliciesItemsParsed = this.optionsService.orderObjectsArrayByProperty(arrPoliciesItemsParsed, 'description', true, true);

    return this.loadStandardCoveragesForVehicle(vehicle)
      .then((data: PolicyCoveragesData) => {
        let tmpClonedArrPoliciesItemsParsed = JSON.parse(JSON.stringify(arrPoliciesItemsParsed));

        tmpClonedArrPoliciesItemsParsed = tmpClonedArrPoliciesItemsParsed.map(item => {
          item = this.optionsService.sortPolicyItemParsedChildItems(item);
          item = this.optionsService.setPolicyItemStatusBasedOnPolicyCoverages(item, data.coverages);
          item = this.setPolicyItemAdditionalData(item, vehicle);
          // item = this.setPolicyItemAdditionalDataAndValidate(item, data.coverages);
          // item = this.setPolicyItemStatusBasedOnRequirements(item, quoteSelectedFormTypes);

          item.endpointUrl = data.endpointURL;
          // item = this.optionsService.setPolicyItemParsedIsNewFromAPIValue(item, policiesItemsFromStorage);
          item.quoteResourceId = quoteId;

          if (item.inputType !== 'Dropdown') {
            item.values = this.optionsService.orderObjectsArrayByProperty(item.values, 'value');
          }

          // Set Default Value Property
          item = this.setPolicyItemParsedDefaultValueProperty(item, this.defaultCoverageValues);

          // Adjust selected values
          item = this.adjustCoverageValueBasedOnDefaultValuesAndAvailableOptions(item, this.defaultCoverageValues, this.effectiveDateChanged ? false : forceDefaultValues);

          return item;
        });

        const tmpVehicleCoverages: VehicleCoverages = new VehicleCoverages();
        tmpVehicleCoverages.vehicle = vehicle;
        tmpVehicleCoverages.vehicleResourceId = vehicle.resourceId;
        tmpVehicleCoverages.options = tmpClonedArrPoliciesItemsParsed;
        if (this.effectiveDateChanged) {
          this.storageService.getStorageData('autoCoveragesStandardForVehicles').pipe(
            take(1))
            .subscribe((res: VehicleCoverages[]) => {
              tmpVehicleCoverages.defaultOption = res.find(el => el.vehicleResourceId === vehicle.resourceId).defaultOption;
        }); }

        return tmpVehicleCoverages;
      });
  }



  private loadStandardCoveragesForVehicle(vehicle: Vehicle): Promise<PolicyCoveragesData> {
    const policyCoverageData: PolicyCoveragesData = new PolicyCoveragesData();

    return new Promise((resolve, reject) => {
      if (vehicle && vehicle.standardCoverages && vehicle.standardCoverages.meta && vehicle.standardCoverages.meta.href) {

        this.vehiclesService.getVehicleStandardCoverages(vehicle.standardCoverages.meta.href)
          .subscribe((res: CoverageApiResponseData) => {
            if (res && res.items[0]) {
              policyCoverageData.coverages = res.items[0].coverages;

              if (res.items[0].meta && res.items[0].meta.href) {
                policyCoverageData.endpointURL = res.items[0].meta.href;
              } else if (res.items[0]) {
                policyCoverageData.endpointURL = res.meta.href + '/' + res.items[0].resourceId;
              }
            } else {
              policyCoverageData.endpointURL = 'api_does_not_returned_data_to_create_uri';
            }

            resolve(policyCoverageData);
          });
      } else {
        reject('Error Load Policy Coverages, vehicle.standardCoverages.meta.href Not defined');
      }
    });
  }


  private getDefaultCoveragesValues(): void {
    this.coveragesService.getAutoDefaultStandardCoverages('')
      .then((res: CoveragesAutoDefaultStandardCoverages) => {
        let agencyId;
    this.agencyUserService.userData$.pipe(take(1)).subscribe(agent => agencyId = agent.agencyId);

        this.coveragesService.getCoverageLevels(agencyId).subscribe(x => localStorage.setItem('autoDefault', x.items[0].name));
        this.defaultCoverageValues =  res.Coverages.map(el => Object.assign(new Coverage(), el)); // res.Coverages;
      });
  }

  private saveCoveragesForVehicleToAPI(data: VehicleCoverages): void {
    const endpointUrl = data.options[0].endpointUrl;
    const readyAllPoliciesToUpdate: Coverage[] = this.optionsService.convertPolicyItemParsedArrayToPolicyArray(data.options);

    // Update remote data
    const newCoverageData = {
      coverages: readyAllPoliciesToUpdate
    };

    this.optionsService.updatePoliciesByUri(endpointUrl, newCoverageData).pipe(
      take(1))
      .subscribe(
        res => console.log('Updated Coverages'),
        err => console.log('Update Error: ', err)
      );
  }

  private setPolicyItemAdditionalData(item: CoverageItemParsed, vehicle: Vehicle): CoverageItemParsed {
    item.additionalData = new CoverageItemParsedAdditionalData();
    item.additionalData.forVehicle = vehicle;
    return item;
  }

  // Validation
  // ----------------------------------------------------------------------------


  // HELPERS
  // ---------------------------------------------------------------------------
  private helpGetCoverageByCoverageCode(parsedCoverages: CoverageItemParsed[], coverageCode: string): CoverageItemParsed {
    const coverage = parsedCoverages.find(opt => opt.coverageCode === coverageCode);
    return coverage;
  }

  private helpGetCoverageValueAsNumber(coverage: CoverageItemParsed): number {
    if (coverage.currentValue === 'Omit') {
      return -1;
    } else {
      const coverageValueStringToConvert = coverage.currentValue.replace('/', '');
      return parseInt(coverageValueStringToConvert, 10);
    }
  }


  // ----------------------------------------------------------------------------
  private validateVehiclesStandardCoverages(vehiclesStandardCoverages: VehicleCoverages[]): VehicleCoverages[] {
    return vehiclesStandardCoverages.map((item: VehicleCoverages) => {
      const options = item.options.map(opt => {
        const result: IValidateVehiclesStandardCoveragesOptionResult = this.validateVehiclesStandardCoveragesOption(opt, item.options);
        return result.coverageItemParsed;
      });
      item.options = options;
      return item;
    });
  }

  public validateVehiclesStandardCoveragesOption(parsedItem: CoverageItemParsed, relatedParsedItems: CoverageItemParsed[]): IValidateVehiclesStandardCoveragesOptionResult {
    const errorMsg: string[] = [];
    let result: IValidateVehiclesStandardCoveragesOptionResult;

    // parsedItem.additionalData = new CoverageItemParsedAdditionalData();
    parsedItem.additionalData.hasError = false; // reset error state

    const currentValueNumber: number = this.helpGetCoverageValueAsNumber(parsedItem);

    const optionalBodilyInjuryCoverage: CoverageItemParsed = this.helpGetCoverageByCoverageCode(relatedParsedItems, 'OPTBI');
    const optionalBodilyInjuryCoverageNumber: number = this.helpGetCoverageValueAsNumber(optionalBodilyInjuryCoverage);

    switch (parsedItem.coverageCode) {

      /* Uninsured */
      case 'UM':
        if (optionalBodilyInjuryCoverage) {

          if (!isNaN(currentValueNumber)) {
            if ((currentValueNumber === 2040 || currentValueNumber === 2550) &&  optionalBodilyInjuryCoverageNumber === -1) {
              // MA requires Uninsured of 20/40 so when OPTBI is set to Omit we need to factor that in (this combination is permissible).
            } else if (!isNaN(optionalBodilyInjuryCoverageNumber) && currentValueNumber > optionalBodilyInjuryCoverageNumber) {
              // console.log('%c ERRROR', 'color:red', optionalBodilyInjuryCoverageNumber);
              parsedItem.additionalData.hasError = true;
              parsedItem.additionalData.errorMessage = 'Limits may not exceed limits for Optional Bodily Injury.';
              errorMsg.push(parsedItem.description + ': ' + parsedItem.additionalData.errorMessage);
            }
          }
        }
        break;

      /* Optional Bodily */
      case 'OPTBI':
        const uninsuredCoverage = this.helpGetCoverageByCoverageCode(relatedParsedItems, 'UM');
        const underinsiuredCoverage = this.helpGetCoverageByCoverageCode(relatedParsedItems, 'UNDUM');

        if (uninsuredCoverage) {
          const uninsuredCoverageValue = this.helpGetCoverageValueAsNumber(uninsuredCoverage);
          const underinsiuredCoverageValue = this.helpGetCoverageValueAsNumber(underinsiuredCoverage);

          // https://bostonsoftware.atlassian.net/browse/SPRC-605
          if (!isNaN(currentValueNumber)) {
            let showMessage = false;
            let uninsuredPartMsg = '';
            let underinsuredPartMsg = '';
            let connector = '';

            if (!isNaN(uninsuredCoverageValue) && (uninsuredCoverageValue > currentValueNumber)) {
              if ((uninsuredCoverageValue === 2040 || uninsuredCoverageValue === 2550) && currentValueNumber === -1) {
                // MA requires Uninsured of 20/40 so when OPTBI is set to Omit we need to factor that in (this combination is permissible).
              } else {
                showMessage = true;
                uninsuredPartMsg = 'Uninsured';
              }
            }

            if (!isNaN(underinsiuredCoverageValue) && (underinsiuredCoverageValue > currentValueNumber)) {
              if ((underinsiuredCoverageValue === 2040 || underinsiuredCoverageValue === 2550) && currentValueNumber === -1) {
                // MA requires Uninsured of 20/40 so when OPTBI is set to Omit we need to factor that in (this combination is permissible).
              } else {
              showMessage = true;
              underinsuredPartMsg = 'Underinsured';
              }
            }

            if (uninsuredPartMsg && underinsuredPartMsg) {
              connector = ' & ';
            }

            if (showMessage) {
              const msg = `Limits for ${uninsuredPartMsg}${connector}${underinsuredPartMsg} may not exceed Optional Bodily Injury.`;
              // this.errorMsg.push(updatedPolicyParsed.description + ': ' + msg);

              parsedItem.additionalData.hasError = true;
              parsedItem.additionalData.errorMessage = msg;
              errorMsg.push(parsedItem.description + ': ' + parsedItem.additionalData.errorMessage);
            }
          }

        }
        break;

      // /* Collision */
      // case "COLL":
      //   let limitedCollisionCoverage = this.helpGetCoverageByCoverageCode(vehiclesCoverages, 'LCOLL');

      //   if (limitedCollisionCoverage && limitedCollisionCoverage.currentValue !== 'Omit') {
      //     this.errorMsg.push(updatedPolicyParsed.description + ': Limits cannot be set for both Collision and Limited. Limited is set to Omit.');
      //     limitedCollisionCoverage.currentValue = 'Omit';
      //   }

      //   break;

      // /* Limited */
      // case "LCOLL":
      //   let collisionCoverage = this.helpGetCoverageByCoverageCode(vehiclesCoverages, 'COLL');

      //   if (collisionCoverage && collisionCoverage.currentValue !== 'Omit') {
      //     this.errorMsg.push(updatedPolicyParsed.description + ': Limits cannot be set for both Collision and Limited. Collision is set to Omit.');
      //     collisionCoverage.currentValue = 'Omit';
      //   }

      //   break;

      /* Underinsured */
      case 'UNDUM':
        if (optionalBodilyInjuryCoverage) {
          if (!isNaN(currentValueNumber) && !isNaN(optionalBodilyInjuryCoverageNumber) && currentValueNumber > optionalBodilyInjuryCoverageNumber) {
            if ((currentValueNumber === 2040 || currentValueNumber === 2550) &&  optionalBodilyInjuryCoverageNumber === -1) {
              // MA requires Uninsured of 20/40 so when OPTBI is set to Omit we need to factor that in (this combination is permissible).
            } else if (!isNaN(optionalBodilyInjuryCoverageNumber) && currentValueNumber > optionalBodilyInjuryCoverageNumber) {
              // console.log('%c ERRROR', 'color:red', optionalBodilyInjuryCoverageNumber);
              parsedItem.additionalData.hasError = true;
              parsedItem.additionalData.errorMessage = 'Limits may not exceed limits for Optional Bodily Injury.';
              errorMsg.push(parsedItem.description + ': ' + parsedItem.additionalData.errorMessage);
            }
          }
        }
        break;
      }

    return {
      coverageItemParsed: parsedItem,
      errors: errorMsg
    };
  }

}
