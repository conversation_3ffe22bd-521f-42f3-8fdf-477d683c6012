<section class="section section--compact u-spacing--2-5">
    <div class="row">
      <h1 class="o-heading" style="color: #0b71ac; padding-left: 20px;">Garaging Address </h1>
    </div>
    <div class="row u-spacing--1">
      <div class="col-xs-12">
        <div class="box box--silver">
            <div class="row">
                <div class="col-md-2 label-width" [ngClass]="{'is-required-field': getReadyEligible() && garagingAddress.type === ''}">
                    <label for="type">Type:</label>
                </div>
                <div class="col-md-4" [ngClass]="{'is-required-field': getReadyEligible() && garagingAddress.type === ''}">
                         <sm-autocomplete #refRTAType [options]="typeOptions" [name]="'garagingType'" [id]="'type'"
                  [(ngModel)]="garagingAddress.type">
                </sm-autocomplete>
                </div>  
               
        </div> 
        <div *ngIf="garagingAddress.type === 'New'">
            <div class="row">
                    <div class="col-md-2 label-width">
                        <label for="address">Address:</label>
                    </div>
                    <div class="col-md-5">
                        <input [(ngModel)]="garagingAddress.street" name="garagingStreet" placeholder="Street">
                    </div>
                    <div class="col-md-5">
                        <input [(ngModel)]="garagingAddress.unitOrApt" name="garagingUnit" placeholder="Unit/Apt">
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-2 label-width"></div>
                    <div class="col-md-5">
                        <input [(ngModel)]="garagingAddress.city" name="garagingCity" placeholder="City">
                    </div>
                    <div class="col-md-3">
                        <sm-autocomplete style="width: 60px;" [options]="stateOptions" [(ngModel)]="garagingAddress.state" [activeOption]="'MA'" name="titleState"></sm-autocomplete>  
                    </div>
                    <div class="col-md-2">
                        <input [(ngModel)]="garagingAddress.zip" name="garagingZip" placeholder="Zip">
                    </div>
                </div>
                </div>
                
        </div>
      </div>
    </div>
  </section>