import { saveAs } from 'file-saver';
import { RmvServicesUserEnteredData } from './../../../../../app-model/PrefillRMV';
import { OwnerLookupResponse } from 'app/app-model/owner-lookup';
import { PLATE_TYPES } from './../../../../app-services/get-ready-dropdown';
import {
  Component,
  OnInit,
  Input,
  EventEmitter,
  Output,
  ViewChild,
  OnDestroy,
  Optional
} from '@angular/core';
import { Router } from '@angular/router';
import { LookupsService } from '../../../../app-services/lookups.service';

import { OverlayLoaderService } from '../../../../../shared/services/overlay-loader.service';
import { RmvService } from '../../../../app-services/rmv.service';
import {
  Payer,
  PaymentDefinition,
  LineItem,
  Attribute,
  PaymentCreateRequestEasy,
  RegistrationOwner,
  PaymentMethodType,
} from '../../../../../app-model/payment-request';
import { RmvServicesResponse } from '../../../../../app-model/rmvservices-response';
import { AgencyUserService } from '../../../../../shared/services/agency-user.service';
import { ApiService } from '../../../../../shared/services/api.service';
import { FeatureService } from 'app/shared/services/feature.service';
import { Owner } from 'app/dashboard/rmv-services/rta-prefill/get-ready.model';
import { DomSanitizer } from '@angular/platform-browser';
import { ModalboxComponent } from 'app/shared/components/modalbox/modalbox.component';
import { ToastrService } from 'ngx-toastr';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { delay } from 'rxjs/operators';
import {format, subDays} from 'date-fns';
@Component({
    selector: 'app-aside-registration-response',
    templateUrl: './aside-registration-response.component.html',
    styleUrls: ['./aside-registration-response.component.scss'],
    standalone: false
})
export class AsideRegistrationResponseComponent implements OnInit, OnDestroy {
  @Input() data: RmvServicesResponse;
  @Input() modalbox;
  @Input() transaction;
  @Input() fromEstamp;
  @Input() ownerEmail = '';
  @Input() ownerDob = '';
  @Input() ownerFid = '';
  @Input() plateNumber;
  @Input() vehicle = null;
  @Input() owners: Owner[] = null;
  @Input() ownerValidation: OwnerLookupResponse[];
  @Input() fromRmvs = false;
  @Input() id;




  @Output() agencyChange = new EventEmitter<boolean>();

  paymentRequest: PaymentCreateRequestEasy;
  writingCompany = '';
  effectiveDate = '';
  providers = [];
  maxDate = format(new Date(), 'yyyy-MM-dd');
  minDate = format(subDays(new Date(), 30), 'yyyy-MM-dd');
  proceedClicked;
  proceedResponse;
  failureMessage = { name: '', value: '' };
  agentId;
  agencyId;
  AgentFeeData;
  AgentFeeOptions: { id: string; text: string }[];
  AgentFee = '0';
  activeFeatures;
  agentUsername: any;
  plateTypeOptions = PLATE_TYPES;
  plateType;
  ownerRetry: any;
  dateError = '';
  @Input() originalVehicle: any;
  assignedCarrier = '';
  arcList: any;
  arcOptions: any;
  isMaip: any;
  isAgencyBill = false;
  bscCreditCardFee = '0';
  bscAgencyBillFee = '0';
  creditCardFee = '0';
  confirmMessage = '';
  useAgencyBill;
  saveState;
  renewMessage: string;
  isEvrFull: boolean;
  isOrderMore: any;

  constructor(
    private router: Router,
    private lookupService: LookupsService,
    private agencyUserService: AgencyUserService,
    private overlayLoaderService: OverlayLoaderService,
    private rmvservice: RmvService,
    private apiService: ApiService,
    private rmvService: RmvService,
    private featureService: FeatureService,
    private sanitizer: DomSanitizer,
    private toastService: ToastrService,
    public ref?: DynamicDialogRef,
    public config?: DynamicDialogConfig
  ) {
    if (this.ref && this.config) {
    this.data = this.config.data.regData;
    this.transaction = this.config.data.transaction;
    this.plateNumber = this.config.data.plateNumber;
    this.ownerDob = this.config.data.ownerDob;
    this.ownerEmail = this.config.data.ownerEmail;
    this.ownerFid = this.config.data.ownerFid;
    this.owners = this.config.data.owners;
    this.ownerValidation = this.config.data.ownerValidation;
    this.fromRmvs = this.config.data.fromRmvs;
    this.vehicle = this.config.data.vehicle;
    this.originalVehicle = this.config.data.originalVehicle;
    }
  }

  ngOnDestroy(): void {
      this.onCancel();
  }

  ngOnInit() {
    this.rmvService.checkEvrLiteEligibility('PANPL').subscribe(x => this.isEvrFull = x.isEvrFullEligible);

    if (!this.id) {
     this.rmvService.SaveRmvServiceTransaction(this.saveData()).subscribe(x => this.id = x.rmvServicesRequestInfo.id);

    }
    this.agencyUserService.userData$.subscribe((agent) => {
      this.agencyId = agent.agencyId;
      this.agentId = agent.user.userId;
      this.agentUsername = agent.user.userName;
    });
    this.lookupService
      .getEstampsAsOptions()
      .subscribe((x) => (this.providers = x));

    this.AgentFeeData = this.data.agentFeeOptions;
    this.getAgentFeeOptions();
    this.featureService.activeFeatures.subscribe(
      (x) => (this.activeFeatures = x)
    );
    this.isAgencyBill = this.data.paymentDefinition?.payment?.agencyBillTotal && this.data.paymentDefinition?.payment?.agencyBillTotal?.toString() !== '0';

    this.bscCreditCardFee = this.data.paymentDefinition?.payment?.lineItems.find(item => item.type === 'BscCreditCardFee').amount;
    this.creditCardFee = this.data.paymentDefinition?.payment?.lineItems.find(item => item.type === 'CreditCardFee').amount;
    if (this.isAgencyBill) {
        this.bscAgencyBillFee = this.data.paymentDefinition?.payment?.lineItems.find(item => item.type === 'BscAgencyBillFee').amount;
      }

    this.lookupService.getEstamps(true).subscribe((x) => {
      this.arcList = x;
      this.arcOptions = x.map(item => ({id: item.companyCode, text: `${item.writingCompanyName} (${item.companyCode})`}));
    });

  }


  agencyChangeEventHandler() {
    this.agencyChange.emit(true);
  }

  redirectToServices() {
    const path =
      this.data.transaction.type === 'RegistrationRenewalDataValidation'
        ? 'PrefillRenewRegistration'
        : 'PrefillReinstateRegistration';
    this.router.navigate(['dashboard/rmv-services/rta-prefill'], {
      queryParams: { type: path },
    });
  }
  // AllowAgentFee
  public isFeatureEnabled(featureName): boolean {
    if (this.activeFeatures !== undefined && this.activeFeatures.length > 0) {
      const enabledFeatures = this.activeFeatures.filter(
        (ef) => ef.name === featureName
      );
      return (
        enabledFeatures !== undefined &&
        enabledFeatures != null &&
        enabledFeatures.length > 0
      );
    } else {
      return false;
    }
  }

  onCancel() {
    this.proceedClicked = false;
    this.clearValues();
    if (this.fromRmvs && !this.isOrderMore) {
      this.router.navigate(['/dashboard/rmv-services']);

    }
    this.ref?.close();
  }

  clearValues() {
    this.ownerDob = '';
    this.ownerEmail = '';
    this.effectiveDate = '';
    this.writingCompany = '';
  }

  setDate($ev) {
    this.ownerDob = $ev.formatedDate;
  }

setEffectiveDate($ev) {
  // Handle empty/null values first
  if (!$ev.date || $ev.date === '') {
    this.effectiveDate = '';
    this.dateError = '';
    return;
  }

  const inputDate = new Date($ev.date);

  // Check if the parsed date is invalid
  if (isNaN(inputDate.getTime())) {
    this.effectiveDate = '';
    this.dateError = '';
    return;
  }

  const today = new Date();
  today.setMinutes(today.getMinutes() + today.getTimezoneOffset());

  const thirtyDaysAgo = subDays(new Date(), 31);
  thirtyDaysAgo.setMinutes(thirtyDaysAgo.getMinutes() + thirtyDaysAgo.getTimezoneOffset());

  if (inputDate > today) {
    this.dateError = 'Effective Date cannot be in the future';
    this.effectiveDate = '';
  } else if (inputDate < thirtyDaysAgo) {
    this.dateError = 'Effective Date cannot be greater than 30 dates in the past';
    this.effectiveDate = '';
  } else {
    this.effectiveDate = format(inputDate, 'yyyy-MM-dd');
    this.dateError = '';
  }
}

  public confirmCanceled($event) {
    // console.log('Cancel: ', $event);
    // dummy function to close popup
  }

  getAgentFeeOptions() {
    if (!this.isFeatureEnabled('AllowAgentFee')) {
      return;
    }
    this.AgentFee = '0';
    this.AgentFeeOptions = this.AgentFeeData.map((fee) => ({
      id: fee.amount,
      text: fee.description,
    }));
  }
  getPayerInfo() {
    const firstName = this.data.transaction.metaData.find(
      (x) => x.name === 'firstName'
    );
    const lastName = this.data.transaction.metaData.find(
      (x) => x.name === 'lastName'
    );
    const businessName = this.data.transaction.metaData.find(
      (x) => x.name === 'businessName'
    );
    const payer: Payer = {
      firstName: firstName ? firstName.value : '',
      lastName: lastName ? lastName.value : '',
      businessName: businessName ? businessName.value : '',
      dob: this.ownerDob ? this.ownerDob : '',
      email: this.ownerEmail,
    };
    return payer;
  }

  convertPaymentToPennies(useAgencyBill) {
    if (
      this.data.paymentDefinition?.payment !== null
    ) {
      let aFee = 0;
      if (this.AgentFee && this.AgentFee != null) {
        aFee = +this.AgentFee;
      }
      const paymentDefinition: PaymentDefinition = {
        type: this.data.paymentDefinition.type,
        category: this.data.paymentDefinition.category,
        payment: {
          total: useAgencyBill ? Math.round(+this.data.paymentDefinition.payment.agencyBillTotal * 100 + aFee) : +this.data.paymentDefinition.payment.total * 100 + aFee,
          paymentMethod: useAgencyBill ? PaymentMethodType.AgencyBill : PaymentMethodType.None,
          bankTransferTotal: Math.round(
            +this.data.paymentDefinition.payment.bankTransferTotal * 100 + aFee
          ),
          agencyBillTotal: Math.round(+this.data.paymentDefinition.payment.agencyBillTotal * 100 + aFee),

          rmvFee: +this.data.paymentDefinition.payment.rmvFee * 100,
          lineItems: this.GetLineItems(),
        },
      };

      return paymentDefinition;
    }
  }

  GetLineItems() {
    const lineItems = [];
    this.data.paymentDefinition.payment.lineItems.forEach((item) => {
      const lineitem: LineItem = {
        description: item.description,
        amount: Math.round(+item.amount * 100),
        type: item.type
      };

      lineItems.push(lineitem);
    });

    if (
      this.isFeatureEnabled('AllowAgentFee') &&
      this.AgentFee !== null &&
      this.AgentFee !== '0'
    ) {
      const feeLintItem: LineItem = {
        description: 'Agent Service Fee',
        amount: +this.AgentFee,
        type: 'Standard'
      };

   //   lineItems.push(feeLintItem);
    }

    return lineItems;
  }

  getAttributes(useAgencyBill) {
    this.effectiveDate =
      this.effectiveDate === ''
        ? format(new Date(), 'yyyyMMdd')
        : this.effectiveDate;
    const attributes: Attribute[] = [];
    const atlasKey = this.data.transaction.metaData.find(
      (x) => x.name === 'atlasRegistrationKey'
    );
    const hasPriorInsuranceEffectiveDate = this.data.transaction.metaData.find(
      (x) => x.name === 'hasPriorInsuranceEffectiveDate'
    );
    attributes.push({ name: 'rmvServicesId', value: this.id.toString() });

    const vehicle = this.data.messages.find((x) => x.name.includes('Vehicle'));
    const owner = this.data.messages.find((x) => x.name.includes('Owner'));
    const validationRequestId = this.data.transaction.metaData.find(
      (x) => x.name === 'AuditId'
    );
    const vehicleMeta = this.getVehicle();
    if (this.transaction === 'RegistrationRenewal' || this.transaction === 'DuplicateRegistration') {
      attributes.push(
        this.data.transaction.metaData.find((x) => x.name === 'isSameOwner')
      );
    }
    attributes.push(atlasKey);
    if (hasPriorInsuranceEffectiveDate) {
      attributes.push(hasPriorInsuranceEffectiveDate);
    }
    attributes.push({
      name: 'vehicle',
      value: vehicle && vehicle.value !== null ? vehicle.value : '',
    });
    attributes.push({
      name: 'owner',
      value: owner && owner.value !== null ? owner.value : '',
    });
    if (this.writingCompany) {
    attributes.push({
      name: 'insuranceCompanyCode',
      value: this.writingCompany === '000' ? this.assignedCarrier : this.writingCompany,
    });
  }
  if (this.effectiveDate) {
    attributes.push({
      name: 'insuranceEffectiveDate',
      value: this.effectiveDate,
    });
  }
    attributes.push({ name: 'plateType', value: vehicleMeta.plateType });
    attributes.push({ name: 'plate', value: vehicleMeta.plateNumber });
    attributes.push({
      name: 'validationRequestId',
      value: validationRequestId ? validationRequestId.value : '',
    });
    attributes.push({name: 'agent', value: this.agentUsername});
    if (this.data.stampId) {
      attributes.push({ name: 'stampRequestId', value: this.data.stampId });
    }


    if (this.transaction === 'DuplicateRegistration') {
     attributes.push(...this.setAttributes());
    attributes.push({name: 'RegistrationVehicle-AtlasVehicleKey', value: this.vehicle.atlasVehicleKey});

     if (this.vehicle.primaryColor !== this.originalVehicle.primaryColor) {
      attributes.push({name: 'RegistrationVehicle-NewPrimaryColor', value: this.vehicle.primaryColor});
    }
    if (this.vehicle.secondaryColor && this.vehicle.secondaryColor !== this.originalVehicle.secondaryColor) {
      attributes.push({name: 'RegistrationVehicle-NewSecondaryColor', value: this.vehicle.secondaryColor});
    }
    }
    if (this.fromRmvs) {
    if (this.transaction === 'RegistrationRenewal' && this.isFeatureEnabled('SprApp_RenewalUpdates')) {
     attributes.push(...this.setAttributes());
     attributes.push(...this.setVehicleAttributes());
     }
    }

    const paymentDefinition = this.convertPaymentToPennies(useAgencyBill);
    attributes.push({
      name: 'rmvFeeCollected',
      value: String(paymentDefinition.payment.rmvFee),
    });

    return attributes;
  }

  getOwner() {
    const firstName = this.data.transaction.metaData.find(
      (x) => x.name === 'firstName'
    );
    const lastName = this.data.transaction.metaData.find(
      (x) => x.name === 'lastName'
    );
    const businessName = this.data.transaction.metaData.find((x) => x.name === 'businessName');
    const fid = this.ownerFid?.trim();
    const owner: RegistrationOwner = {
      firstName: businessName ? businessName?.value : firstName?.value,
      lastName: lastName ? lastName.value : '',
      fid: fid || '',
      dob: this.ownerDob ? this.ownerDob : '',
      email: this.ownerEmail,
    };
    return owner;
  }

  getVehicle() {
    const plateNumber = this.data.transaction.metaData.find(
      (x) => x.name === 'plate'
    ).value;
    const plateType = this.data.transaction.metaData.find(
      (x) => x.name === 'plateType'
    ).value;
    const vehicle = { plateNumber, plateType };
    return vehicle;
  }

  proceed(useAgencyBill = false) {
    if (this.ownerFid) {
      this.ownerFid = this.ownerFid.replace(/[_-]/g, '');
    }
    this.proceedClicked = true;
    this.overlayLoaderService.showLoader();
    this.paymentRequest = {
      payer: this.getPayerInfo(),
      owner: this.getOwner(),
      vehicle: this.getVehicle(),
      paymentDefinition: this.convertPaymentToPennies(useAgencyBill),
      transactionData: {
        attributes: this.getAttributes(useAgencyBill),
        transaction: this.transaction,
      },
      requester: {
        email: this.data.transaction.metaData.find(
          (x) => x.name === 'userEmail'
        ).value,
        ccOnEmail: true,
      },
    };

    this.rmvservice.paymentRequestEasy(this.paymentRequest).subscribe((x) => {

      this.paymentSub(x);

    });

}

private paymentSub(data) {
  if (data && data.name === 'Failure') {
    this.failureMessage = data;
    this.proceedResponse = null;
    if (this.failureMessage.value.includes('owner information does not match')) {
      this.ownerRetry = true;
    }
    this.overlayLoaderService.hideLoader();
  } else if (this.useAgencyBill) {
    this.rmvService.updatePaymentRequest({
      status: 'Paid',
      paymentMethod: PaymentMethodType.AgencyBill,
      paymentRequestId: data.paymentRequestId
    }).subscribe(x => {
      if (x.success === true) {

       if (x.registrationExpirationDate) {
        this.isEvrFull = true;
        this.renewMessage = `<p>You will issue a ${x.registrationExpirationDate.slice(0, 4)} decal for this Renewal and be left with ${x.decalsBalance} in inventory.</p>
        <br><p>IMPORTANT: Remember to Download Documents, print the Registration, and provide it to your consumer. This is your consumer’s new Registration. A new one will NOT be mailed by the RMV.</p>
        <br><p>After downloading your documents, you may choose to order more inventory or close this screen.</p>`;
        this.overlayLoaderService.hideLoader();
      } else {
        this.renewMessage = `The registration has been successfully renewed. Click the button below to download the Certificate of Registration
      and receipt from the RMV.`;
      this.isEvrFull = false;
      this.overlayLoaderService.hideLoader();
       }
      }
    });
  } else {
    this.proceedResponse = data;
    const regex = /https:\/\/\S+/g;
    this.proceedResponse.messages[1] = this.proceedResponse.messages[1].replace(
      regex,
      (url) => `<a style="color:blue" href="${url}" target="_blank">Click Here For Payment</a>`
    );

    this.proceedResponse.messages[1] = this.sanitizer.bypassSecurityTrustHtml(
      this.proceedResponse.messages[1]
    );
this.overlayLoaderService.hideLoader();
    this.ownerRetry = false;
    this.failureMessage = { name: '', value: '' };
  }
  this.ownerDob = '';
  if (!this.ownerRetry) {
  (this.ownerEmail = ''), (this.ownerFid = '');
  (this.effectiveDate = ''), (this.writingCompany = '');
  }

}

validateAgencyBill() {
  if (this.isAgencyBill) {
    return this.useAgencyBill === undefined;
  } else {
    return false;
  }
}

validateDob() {
  const dobRequired = this.data.requestAdditionalData.find(
    (x) => x.name === 'ownerDob'
  );
  if (dobRequired) {
    const dobValue = this.ownerDob === '' || this.ownerDob === null;
    return      dobValue || this.ownerEmail === '';
  }
}

  validateForm() {
    const dobRequired = this.data.requestAdditionalData.find(
      (x) => x.name === 'ownerDob'
    );
    const fidRequired = this.data.requestAdditionalData.find(
      (x) => x.name === 'ownerFid'
    );
    const effectiveDateRequired = this.transaction !== 'RegistrationRenewal' && this.transaction !== 'DuplicateRegistration';
    const writingCompanyRequired = this.transaction !== 'DuplicateRegistration';
    let returnValue = false;
    const dobValue = this.ownerDob === '' || this.ownerDob === null;
        if (dobRequired) {
      returnValue =
        dobValue || this.ownerEmail === '';
    }
    if (fidRequired) {
      returnValue =
        this.ownerFid === '' ||
        this.ownerEmail === '';
    }
    if (effectiveDateRequired) {
      returnValue =
        this.effectiveDate === '' ||
        this.effectiveDate > this.maxDate ||
        this.effectiveDate < this.minDate;
    }
    if (writingCompanyRequired) {
      returnValue =
      this.writingCompany === '';
    }

    if (this.writingCompany === '000') {
      returnValue = this.assignedCarrier === '';
    }
    return returnValue;
  }


  validEmail() {
    let returnValue = false;
    const regexp = new RegExp(
      /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
    );
    return (returnValue = regexp.test(this.ownerEmail));
  }

  validDate() {
    let isValid = false;
   if (this.transaction === 'DuplicateRegistration') {
   isValid = this.effectiveDate <= this.maxDate;

    }
    return true;
  }

  registrationValidation() {
     this.overlayLoaderService.showLoader();

    const data = {
       transaction: {type: this.data.transaction.type !== 'DuplicateRegistrationDataValidation' ?
       `${this.data.transaction.type}Easy` : this.data.transaction.type},
       vehicle: {
         plateNumber: this.plateNumber.trim(),
         plateType: this.plateType
       }
     };
     this.rmvService.Validation(data).subscribe(
       x => {
         this.overlayLoaderService.hideLoader();

         this.data = x;
    this.isAgencyBill = this.data.paymentDefinition?.payment?.agencyBillTotal && this.data.paymentDefinition?.payment?.agencyBillTotal?.toString() !== '0';
    if (this.isAgencyBill) {
        this.bscAgencyBillFee = this.data.paymentDefinition?.payment?.lineItems.find(item => item.type === 'BscAgencyBillFee').amount;
      }
         if (this.ownerRetry) {
        this.failureMessage = { name: '', value: '' };
          this.proceedClicked = false;
        }
        },
       err => {
         this.overlayLoaderService.hideLoader();

       }

     );

    // this.regModal.open();
   }

   isMultipleRecords() {
        return this.data.messages.find(x => x.value.includes('Multiple records')) ? true : false;
   }

   isNoRecords() {
    return this.data.messages.find(x => x.value.includes('No records found')) ? true : false;
}


  skipToForm() {
    let garage = {};
    const vehicle = this.vehicle ? this.vehicle : this.getVehicle();
    if (this.owners && this.owners.length > 0) {
    const {addressCity, addressState, addressStreet1, addressStreet2, addressUnit, addressUnitType, addressZIP} = this.owners[0]?.garagingAddress;
     garage = {
       city: addressCity,
        state: addressState,
        street: addressStreet1,
        street2: addressStreet2,
        unitOrApt: addressUnit,
        unitType: addressUnitType,
        zip: addressZIP

    };
    }
    const rmvPrefill = {
      transactionType: this.data.transaction.type === 'RegistrationRenewalDataValidation' ? 'PrefillRenewRegistration' : 'PrefillReinstateRegistration',
      vehicles: [{ ...this.getVehicle(), vin: '', lookupType: 'plate', id: 1 }],
      drivers: [],
      ownership: '',
    };
    this.lookupService.prefillRMVForm(rmvPrefill).subscribe((x) => {
      const formInfo = {
        transactionType: this.data.transaction.type === 'RegistrationRenewalDataValidation' ? 'PrefillRenewRegistration' : 'PrefillReinstateRegistration',
        agencyId: this.agencyId,
        creationUserId: this.agentId,
        number: 'RTA',
        clientId: '',
        rmvLookupData: x.rmvLookupData,
        rmvServicesUserEnteredData: { vehicles: [vehicle], owners: this.owners, garagingAddress: garage
        },
      };
      let formWindow: Window;
      const baseUrl =
        document.location.protocol +
        '//' +
        document.location.hostname +
        ':' +
        document.location.port +
        '/' +
        document.location.pathname;
      const loadingPage = baseUrl + '/assets/html-templates/loading.html';
      formWindow = window.open(loadingPage, '_blank');
      return new Promise((res, rej) => {
        this.rmvService.RMVForm(formInfo).subscribe((f) => {
          const url = `${formInfo.agencyId}/openform?agencyformid=${
            f.identifier
          }&agentId=${f.creationUserId}&ticket=${encodeURIComponent(
            f.authorizationToken.ticket
          )}&requestId=${f.authorizationToken.requestId}`;
          const formUrl = this.apiService.formAppUrl(url);
          formWindow.location.href = formUrl;
        });
        this.modalbox.close();
      });
    });
  }

  setAttributes() {
    if (!this.owners) {
      return;
    }

    const attributes = [];
    if (this.ownerValidation[0].editGaragingAddress ||
       this.ownerValidation[0].editMailingAddress || this.ownerValidation[0].editResidentialAddress) {
      attributes.push({name: 'AtlasEntity1-AddressChangeIndicator', value: 'Y'});
    }
    attributes.push({name: 'AtlasEntity1-AtlasEntityKey', value: this.owners[0].atlasEntityKey});
    this.owners[0].atlasEntityLocationKey ?
    attributes.push({name: 'AtlasEntity1-AtlasEntityLocationKey', value: this.owners[0].atlasEntityLocationKey}) : '' ;
    this.owners[0].ownershipRole ?
    attributes.push({name: 'AtlasEntity1-OwnershipRole', value: this.owners[0].ownershipRole}) : '' ;
    if (this.ownerValidation[0].leasedVehicleIndicator) {
      attributes.push({name: 'Ownership-LeasedVehicleIndicator', value: 'Y'});
    }
    this.setAddressAttributes('AtlasEntity1-MailAddress', 'mailAddress', attributes, 0);
    if (this.owners[0].entityType && this.owners[0].entityType === 'IND') {
    this.setAddressAttributes('RegistrationDetail-GarageAddress', 'garagingAddress', attributes, 0);
    }
    this.setAddressAttributes('AtlasEntity1-ResidentialAddress', 'residentialAddress', attributes, 0);

    if (this.owners.length > 1) {
      if (this.ownerValidation[1].editGaragingAddress || this.ownerValidation[1].editMailingAddress || this.ownerValidation[1].editResidentialAddress) {
        attributes.push({name: 'AtlasEntity2-AddressChangeIndicator', value: 'Y'});
      }
      attributes.push({name: 'AtlasEntity2-AtlasEntityKey', value: this.owners[1].atlasEntityKey});
      this.owners[1].ownershipRole ?
    attributes.push({name: 'AtlasEntity2-OwnershipRole', value: this.owners[1].ownershipRole}) : '' ;
    this.setAddressAttributes('AtlasEntity2-MailAddress', 'mailAddress', attributes, 1);
    this.setAddressAttributes('AtlasEntity2-ResidentialAddress', 'residentialAddress', attributes, 1);

    }

    return attributes;


  }

  setVehicleAttributes() {

    const attributes = [];
    attributes.push({name: 'RegistrationVehicle-AtlasVehicleKey', value: this.vehicle.atlasVehicleKey});
    if (this.vehicle.newNumberOfSeats !== this.originalVehicle.numberOfSeats) {
      attributes.push({name: 'RegistrationDetail-NumberOfSeats', value: this.vehicle.newNumberOfSeats});
    }
    if (this.vehicle.newRegisteredWeight !== this.originalVehicle.registeredWeight) {
      attributes.push({name: 'RegistrationDetail-RegisteredWeight', value: this.vehicle.newRegisteredWeight});
    }

    if (this.vehicle.grossVehicleWeight !== this.originalVehicle.grossVehicleWeight) {
      attributes.push({name: 'RegistrationVehicle-NewGrossVehicleWeight', value: this.vehicle.grossVehicleWeight});
    }
    if (this.vehicle.primaryColor !== this.originalVehicle.primaryColor) {
      attributes.push({name: 'RegistrationVehicle-NewPrimaryColor', value: this.vehicle.primaryColor});
    }
    if (this.vehicle.secondaryColor && this.vehicle.secondaryColor !== this.originalVehicle.secondaryColor) {
      attributes.push({name: 'RegistrationVehicle-NewSecondaryColor', value: this.vehicle.secondaryColor});
    }


    return attributes;

  }

  setAddressAttributes(key, type, attributes, index) {
    attributes.push({name: `${key}-AddressStreet1`, value: this.encodeValue(this.owners[index][type].addressStreet1)});
    this.owners[index][type].addressStreet2 ? attributes.push({name: `${key}-AddressStreet2`, value: this.encodeValue(this.owners[index][type].addressStreet2)}) : '';
    this.owners[index][type].addressUnitType ? attributes.push({name: `${key}-AddressUnitType`, value: this.owners[index][type].addressUnitType}) : '';
    this.owners[index][type].addressUnit ? attributes.push({name: `${key}-AddressUnit`, value: this.owners[index][type].addressUnit}) : '';
    attributes.push({name: `${key}-AddressCity`, value: this.encodeValue(this.owners[index][type].addressCity)});
    attributes.push({name: `${key}-AddressState`, value: this.owners[index][type].addressState});
    attributes.push({name: `${key}-AddressZip`, value: this.owners[index][type].addressZIP});
    attributes.push({name: `${key}-AddressCountry`, value: this.owners[index][type].addressCountry});
    return attributes;

  }

  encodeValue(value) {
    const div = document.createElement('div');
    div.textContent = value;
    return div.innerHTML;
  }

  undoARC() {
this.assignedCarrier = '';
this.writingCompany = '';
this.isMaip = false;
  }

  acceptARC() {
    this.isMaip = true;

  }

  getCompanyName() {
    if (this.assignedCarrier && this.providers) {
   const item = this.arcList.find(x => x.companyCode === this.assignedCarrier).insuranceCompanyName;
   return item.includes('Plymouth') && this.assignedCarrier !== '731' ?  'Pilgrim' : item;

    }
  }

  getHeader() {
    if (this.transaction !== 'DuplicateRegistration') {
      return 'Enter the information to confirm owner, select insurance carrier and send payment request';
    } else {
      return 'Enter the information to confirm owner and send payment request';
    }
  }

  getConfirmMessage() {

    const ret = '<p>You are confirming that you are in possession of the entire payment from the consumer and understand that Boston Software will be deducting this total from your account minus any fee owed you.</p>';
    switch (this.transaction) {
      case 'RegistrationReinstatement':
        return ret;
       case 'RegistrationRenewal':
        if (!this.isEvrFull) {
        return ret + ` <br><p>Once confirmed, the RMV will email the consumer a PDF receipt indicating that the Registration Renewal process is complete and the vehicle is registered.</p>
                <br>
                <p>If additional information, stickers, or plates are necessary, the RMV will handle it.</p>`;
              } else { return ret; }
      case 'DuplicateRegistration':
        return ret + `<br>
        <p>Once confirmed, you will be able to download a copy of the receipt and the Certificate of Registration.</p>`;
    }
  }

  saveData() {
    const owner = this.getOwner();
    const vehicle = this.getVehicle();
    let transaction = '';
    switch (this.transaction) {
      case 'DuplicateRegistration':
        transaction = this.transaction;
        break;
      case 'RegistrationRenewal':
        transaction = 'PrefillRenewRegistration';
        break;
      case 'RegistrationReinstatement':
        transaction = 'PrefillReinstateRegistration';
        break;

    }
    return {
      ownerFirstName: owner.firstName,
      ownerLastName: owner.lastName,
      agencyId: this.agencyId,
      workflowType: 'RmvServices',
      transactionType: transaction,
      rmvServicesUserEnteredData: {
        vehicles: [{plateNumber: vehicle.plateNumber, plateType: vehicle.plateType}],
      },
    };
  }

  goToInventory() {
    this.isOrderMore = true;
    this.router.navigate(['/dashboard/rmv-services/inventory-order']).then(() => {
      this.ref?.close();
    });
  }

  downloadDocuments() {
    const owner = this.getOwner();

    this.overlayLoaderService.showLoader();
    setTimeout(() => {
      this.rmvService.downloadDocuments(this.id).subscribe(x => {
      const blob = new Blob([x], {
        type: 'application/zip'
      });

        saveAs(blob, `${owner.firstName}_${owner.lastName}_RmvDocs` );

      this.overlayLoaderService.hideLoader();

      if (this.transaction !== 'RegistrationRenewal') {
        const url = this.fromRmvs ? '/dashboard/rmv-services' : '/dashboard';
         url ? this.router.navigate([url]) :
        this.router.navigate(['/dashboard/rmv-services']);
      }
      this.onCancel();


    }, err => {this.overlayLoaderService.hideLoader();
    this.toastService.warning('Sorry, Documents are not available yet, please try again.');
    });
  }, 10000);
    }




}
