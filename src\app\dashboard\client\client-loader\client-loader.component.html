<section class="section">
  <div class="row">
    <div class="col-xs-12 u-align-center" *ngIf="!clientLoadingError">
      <p>Loading Client... <span *ngIf="getProgressPercentage(loadingStatus) > 0">{{getProgressPercentage(loadingStatus)}}%</span></p>
    </div>
    <div class="col-xs-12 u-align-center" *ngIf="clientLoadingError">
      <br>
      <p>Sorry, Error occurred during Client loading.</p>
      <p *ngIf="clientLoadingErrorMessage" class="u-spacing--1-5">{{clientLoadingErrorMessage}}</p>
      <br>
      <p>
        Please
        <a class="o-link o-link--blue" (click)="$event.preventDefault(); initClientData(clientId)">try again</a>
        or load <a [routerLink]="'/dashboard/clients'" class="o-link o-link--blue">other Client </a>.
      </p>
    </div>
  </div>
</section>
