<section class="section section--compact u-spacing--2-5">
  <div class="row">
      <h1 class="o-heading" style="color: #0b71ac; padding-left: 20px;">Vehicle</h1>
    </div>
  <div class="box box--silver">
      <div [ngSwitch]="type">
 <!--RTA Template-->
<ng-template [ngSwitchCase]="'PrefillNewTitleAndRegistration'" #rta>
  <div class="row">
      <div class="col-md-2 label-width">
          <label for="vin" [ngClass]="{'is-required-field': vehicle.vin === ''}">Vehicle:</label>
      </div>
      <div class="col-md-2" [ngClass]="{'is-required-field': vehicle.vin === ''}">
          <input [(ngModel)]="vehicle.vin" name="vin" placeholder="VIN">
      </div>
      <!--<div class="col-md-2 offset-md-1 label-width" [ngClass]="{'is-required-field': vehicle.condition === ''}">
          <label for="condition">Condition:</label>
      </div>
      <div class="col-md-2" [ngClass]="{'is-required-field': vehicle.condition === ''}">
        <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
          <input type="radio" name="condition" value="New"
            [(ngModel)]="vehicle.condition">
          <i class="o-btn o-btn--radio"></i>
          <span style="margin-right: 10px;">New</span>
          </label>
          <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
            <input type="radio" name="condition" value="Used"
              [(ngModel)]="vehicle.condition">
            <i class="o-btn o-btn--radio"></i>
            <span>Used</span>
            </label>
      </div>-->
  </div>
  <div class="row">
      <div class="col-md-2 label-width" [ngClass]="{'is-required-field': vehicle.odometer === ''}">
          <label for="odometer">Odometer:</label>
      </div>
      <div class="col-md-2">
          <input [(ngModel)]="vehicle.odometer" [ngClass]="{'is-required-field': vehicle.odometer === ''}" name="odometer" type="number" placeholder="Miles">
      </div>
      <div class="col-md-2 offset-md-1 label-width">
          <label for="condition">Primary Color:</label>
      </div>
      <div class="col-md-2">
          <sm-autocomplete [(ngModel)]="vehicle.primaryColor" [activeOption]="vehicle.primaryColor" name="primaryColor" [options]="primaryColorOptions"></sm-autocomplete>
      </div>
  </div>
  <div class="row">
      <div class="col-md-2 label-width" [ngClass]="{'is-required-field': vehicle.registrationType === ''}">
          <label for="registration">Registration:</label>
      </div>
      <div class="col-md-2" [ngClass]="{'is-required-field': vehicle.registrationType === ''}">
          <sm-autocomplete [options]="registrationTypeOptions" name="registrationType" [(ngModel)]="vehicle.registrationType" [activeOption]="vehicle.registrationType"></sm-autocomplete>
      </div>
      <div class="col-md-2 offset-md-1 label-width">
          <label for="transmission">Transmission:</label>
      </div>
      <div class="col-md-2">
          <sm-autocomplete [options]="transmissionOptions" name="transmission" [activeOption]="vehicle.transmission" [(ngModel)]="vehicle.transmission"></sm-autocomplete>
      </div>
  </div>
  <div class="row">
      <div class="col-md-2 label-width">
          <label for="reassigned">Reassigned Plate:</label>
      </div>
      <div class="col-md-2">
        <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
          <input type="radio" name="reassigned" value="Y"
            [(ngModel)]="vehicle.reassignedPlate">
          <i class="o-btn o-btn--radio"></i>
          <span style="margin-right: 10px;">Yes</span>
          </label>
          <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
            <input type="radio" name="reassigned" value="N"
              [(ngModel)]="vehicle.reassignedPlate">
            <i class="o-btn o-btn--radio"></i>
            <span>No</span>
            </label>
      </div>
      <div class="col-md-2 offset-md-1 label-width">
          <label for="passenger">Passengers:</label>
      </div>
      <div class="col-md-2">
          <input [(ngModel)]="vehicle.passengers" name="passengers" placeholder="No. of Passengers">
      </div>
  </div>
  <div class="row" *ngIf="vehicle.reassignedPlate === 'Y'">
      <div class="col-md-2 label-width">
          <label for="plate">Plate:</label>
      </div>
      <div class="col-md-1">
          <input [(ngModel)]="vehicle.plateNumber" name="plate" placeholder="Plate" style="width:80px;padding-right:10px;">
      </div>
      <div class="col-md-1">
          <sm-autocomplete style="width: 100px;" name="plateType"
          [searchFromBegining]="false" [allowSearchById]="true"
          [(ngModel)]="vehicle.plateType" [activeOption]="vehicle.plateType" [options]="plateTypeOptions">
        </sm-autocomplete>
      </div>

  </div>
  <div class="row">
      <div class="col-md-2 label-width" [ngClass]="{'is-required-field': vehicle.ownership === ''}">
          <label for="vin">Ownership:</label>
      </div>
      <div class="col-md-2" [ngClass]="{'is-required-field': vehicle.ownership === ''}">
          <sm-autocomplete required [(ngModel)]="vehicle.ownership" name="ownership" [activeOption]="vehicle.ownership" [options]="ownershipType"></sm-autocomplete>
      </div>
      <div class="col-md-2 offset-md-1 label-width">
          <label for="title">Title Issue Date:</label>
      </div>
      <div class="col-md-2">
        <app-datepicker-input ngDefaultControl [(ngModel)]="vehicle.titleIssueDate" name="titleIssueDate" (onDateChange)="setDate($event,vehicle)"  [placeholder]="'MM/dd/yyyy'" [returnDateFormat]="'yyyy-MM-dd'"></app-datepicker-input>
      </div>
  </div>
  <div class="row">
    <div class="col-md-2 label-width">
        <label for="plate">Out of State Title:</label>
    </div>
    <div class="col-md-1">
      <input [(ngModel)]="vehicle.outOfStateTitleNumber" name="outOfStateTitle" style="width:100px">
    </div>
    <div class="col-md-1">
        <sm-autocomplete style="width: 60px;" [activeOption]="vehicle.titleState" [options]="stateOptions" [(ngModel)]="vehicle.titleState" name="titleState"></sm-autocomplete>
    </div>
  </div>
</ng-template>


<!--Transfer Plate Template-->
<ng-template [ngSwitchCase]="checkIfPlateToTransfer()" #transfer>
  <div class="row">
    <div class="col-md-2 label-width" [ngClass]="{'is-required-field':  vehicle.plateNumber === ''}">
        <label for="plate">Plate for Transfer:</label>
    </div>
    <div class="col-md-1" [ngClass]="{'is-required-field': vehicle.plateNumber === ''}">
        <input [(ngModel)]="vehicle.plateNumber" name="plate" placeholder="Plate" [required]="type === 'PrefillRegistrationTransfer'" style="width:80px;padding-right:10px;">
    </div>
    <div class="col-md-1" [ngClass]="{'is-required-field': vehicle.plateType === ''}">
        <sm-autocomplete style="width: 100px;" [activeOption]="vehicle.plateType"
        [searchFromBegining]="false" [allowSearchById]="true"
        [(ngModel)]="vehicle.plateType" name="plateType" [options]="plateTypeOptions" placeholder="Type" [required]="type === 'PrefillRegistrationTransfer'" ></sm-autocomplete>
    </div>
    <!--<div class="col-md-2 offset-md-1 label-width" [ngClass]="{'is-required-field': type === 'PrefillRegistrationTransfer' && vehicle.condition === ''}">
        <label for="condition">Condition:</label>
    </div>
    <div class="col-md-2" [ngClass]="{'is-required-field': type === 'PrefillRegistrationTransfer' && vehicle.condition === ''}">
      <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
        <input type="radio" name="condition" value="New"
          [(ngModel)]="vehicle.condition">
        <i class="o-btn o-btn--radio"></i>
        <span style="margin-right: 10px;">New</span>
        </label>
        <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
          <input type="radio" name="condition" value="Used"
            [(ngModel)]="vehicle.condition">
          <i class="o-btn o-btn--radio"></i>
          <span>Used</span>
          </label>
    </div>-->
</div>
<div class="row">
  <div class="col-md-2 label-width" [ngClass]="{'is-required-field': vehicle.vin === ''}">
      <label for="vin">Vehicle:</label>
  </div>
  <div class="col-md-2" [ngClass]="{'is-required-field': vehicle.vin === ''}">
      <input [(ngModel)]="vehicle.vin" name="vin" placeholder="VIN" [required]="type === 'PrefillRegistrationTransfer'" >
  </div>
  <div class="col-md-2 offset-md-1 label-width">
      <label for="condition">Primary Color:</label>
  </div>
  <div class="col-md-2">
    <sm-autocomplete [(ngModel)]="vehicle.primaryColor" [activeOption]="vehicle.primaryColor" name="primaryColor" [options]="primaryColorOptions"></sm-autocomplete>
  </div>
</div>
<div class="row">
  <div class="col-md-2 label-width" [ngClass]="{'is-required-field': type === 'PrefillRegistrationTransfer' && vehicle.odometer === ''}">
      <label for="registration">Odometer:</label>
  </div>
  <div class="col-md-2" [ngClass]="{'is-required-field': type === 'PrefillRegistrationTransfer' && vehicle.odometer === ''}">
    <input [(ngModel)]="vehicle.odometer" name="odometer" type="number" placeholder="Miles">
  </div>
  <div class="col-md-2 offset-md-1 label-width">
      <label for="condition">Transmission:</label>
  </div>
  <div class="col-md-2">
    <sm-autocomplete [activeOption]="vehicle.transmission" [options]="transmissionOptions" name="transmission" [(ngModel)]="vehicle.transmission"></sm-autocomplete>
  </div>
</div>
<div class="row">
  <div class="col-md-2 label-width" [ngClass]="{'is-required-field':type === 'PrefillRegistrationTransfer' && vehicle.registrationType === ''}">
      <label for="reassigned">Registration:</label>
  </div>
  <div class="col-md-2" [ngClass]="{'is-required-field':type === 'PrefillRegistrationTransfer' && vehicle.registrationType === ''}">
    <sm-autocomplete [options]="registrationTypeOptions" [activeOption]="vehicle.registrationType" name="registrationType" [(ngModel)]="vehicle.registrationType"></sm-autocomplete>
  </div>
  <div class="col-md-2 offset-md-1 label-width">
      <label for="condition">Passengers:</label>
  </div>
  <div class="col-md-2">
      <input [(ngModel)]="vehicle.passengers" name="passengers" placeholder="No. of Passengers">
  </div>
</div>
<div class="row">
  <div class="col-md-2 label-width">
      <label for="plate">Out of State Title:</label>
  </div>
  <div class="col-md-1">
    <input [(ngModel)]="vehicle.outOfStateTitleNumber" name="outOfStateTitle" style="width:100px">
  </div>
  <div class="col-md-1">
      <sm-autocomplete style="width: 60px;" [activeOption]="vehicle.titleState" [options]="stateOptions" [(ngModel)]="vehicle.titleState" name="titleState"></sm-autocomplete>
  </div>
  <div class="col-md-2 offset-md-1 label-width">
      <label for="condition">Title Issue Date:</label>
  </div>
  <div class="col-md-2">
    <app-datepicker-input ngDefaultControl [(ngModel)]="vehicle.titleIssueDate" name="titleIssueDate" (onDateChange)="setDate($event,vehicle)"  [placeholder]="'MM/dd/yyyy'" [returnDateFormat]="'yyyy-MM-dd'"></app-datepicker-input>
  </div>
</div>
</ng-template>

<!--Registration Only-->

<ng-template #registrationOnly [ngSwitchCase]="type === 'PrefillRegistrationOnly' || type === 'PrefillNonResidentShortTermRegistration'">
  <div class="row">
    <div class="col-md-2 label-width" [ngClass]="{'is-required-field': vehicle.vin === ''}">
        <label for="vin">Vehicle:</label>
    </div>
    <div class="col-md-2" [ngClass]="{'is-required-field': vehicle.vin === ''}">
        <input [(ngModel)]="vehicle.vin" name="vin" placeholder="VIN">
    </div>
    <!--<div class="col-md-2 offset-md-1 label-width" [ngClass]="{'is-required-field': type === 'PrefillRegistrationOnly' && vehicle.condition === ''}">
        <label for="condition">Condition:</label>
    </div>
    <div class="col-md-2" [ngClass]="{'is-required-field': type === 'PrefillRegistrationOnly' &&  vehicle.condition === ''}">
      <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
        <input type="radio" name="condition" value="New"
          [(ngModel)]="vehicle.condition">
        <i class="o-btn o-btn--radio"></i>
        <span style="margin-right: 10px;">New</span>
        </label>
        <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
          <input type="radio" name="condition" value="Used"
            [(ngModel)]="vehicle.condition">
          <i class="o-btn o-btn--radio"></i>
          <span>Used</span>
          </label>
    </div>-->
</div>
<div class="row">
    <div class="col-md-2 label-width" [ngClass]="{'is-required-field': type === 'PrefillRegistrationOnly' || type === 'PrefillNonResidentShortTermRegistration' &&  vehicle.odometer === ''}">
        <label for="odometer">Odometer:</label>
    </div>
    <div class="col-md-2" [ngClass]="{'is-required-field': type === 'PrefillRegistrationOnly' || type === 'PrefillNonResidentShortTermRegistration' &&  vehicle.odometer === ''}">
        <input [(ngModel)]="vehicle.odometer" name="odometer" type="number" placeholder="Miles">
    </div>
    <div class="col-md-2 offset-md-1 label-width">
        <label for="color">Primary Color:</label>
    </div>
    <div class="col-md-2">
      <sm-autocomplete [activeOption]="vehicle.primaryColor" [(ngModel)]="vehicle.primaryColor" name="primaryColor" [options]="primaryColorOptions"></sm-autocomplete>
    </div>
</div>
<div class="row">
    <div class="col-md-2 label-width" [ngClass]="{'is-required-field': type === 'PrefillRegistrationOnly' || type === 'PrefillNonResidentShortTermRegistration' &&  vehicle.registrationType === ''}">
        <label for="registration">Registration:</label>
    </div>
    <div class="col-md-2" [ngClass]="{'is-required-field': type === 'PrefillRegistrationOnly' || type === 'PrefillNonResidentShortTermRegistration' &&  vehicle.registrationType === ''}">
      <sm-autocomplete [activeOption]="vehicle.registrationType" [options]="registrationTypeOptions" name="registrationType" [(ngModel)]="vehicle.registrationType"></sm-autocomplete>
    </div>
    <div class="col-md-2 offset-md-1 label-width">
        <label for="condition">Transmission:</label>
    </div>
    <div class="col-md-2">
      <sm-autocomplete [activeOption]="vehicle.transmission" [options]="transmissionOptions" name="transmission" [(ngModel)]="vehicle.transmission"></sm-autocomplete>
    </div>
</div>
<div class="row">
    <div class="col-md-2 label-width" [ngClass]="{'is-required-field': type === 'PrefillRegistrationOnly' || type === 'PrefillNonResidentShortTermRegistration' &&  vehicle.registrationReason === ''}">
        <label for="reassigned">Registration Reason:</label>
    </div>
    <div class="col-md-2" [ngClass]="{'is-required-field': type === 'PrefillRegistrationOnly' || type === 'PrefillNonResidentShortTermRegistration' &&  vehicle.registrationReason === ''}">
        <sm-autocomplete [(ngModel)]="vehicle.registrationReason" [activeOption]="vehicle.registrationReason" name="reason" [options]="registrationReasonOptions"></sm-autocomplete>
    </div>
    <div class="col-md-2 offset-md-1 label-width">
        <label for="condition">Passengers:</label>
    </div>
    <div class="col-md-2">
        <input [(ngModel)]="vehicle.passengers" name="passengers" placeholder="No. of Passengers">
    </div>
</div>
<div class="row">
  <div class="col-md-2 label-width" [ngClass]="{'is-required-field': type === 'PrefillRegistrationOnly' || type === 'PrefillNonResidentShortTermRegistration' &&  vehicle.ownership === ''}">
      <label for="vin">Ownership:</label>
  </div>
  <div class="col-md-2" [ngClass]="{'is-required-field': type === 'PrefillRegistrationOnly' || type === 'PrefillNonResidentShortTermRegistration' &&  vehicle.ownership === ''}">
      <sm-autocomplete [(ngModel)]="vehicle.ownership" [activeOption]="vehicle.ownership" name="ownership" [options]="ownershipType"></sm-autocomplete>
  </div>
</div>
</ng-template>

<!--Title Only/ Salvage Title / Previously Titled-->

<ng-template #titleOnly [ngSwitchCase]="checkIfTitleOnly()">
  <div class="row">
    <div class="col-md-2 label-width" [ngClass]="{'is-required-field':  vehicle.vin === ''}">
        <label for="vin">Vehicle:</label>
    </div>
    <div class="col-md-2" [ngClass]="{'is-required-field': vehicle.vin === ''}">
        <input [(ngModel)]="vehicle.vin" name="vin" placeholder="VIN">
    </div>
    <!--<div class="col-md-2 offset-md-1 label-width" [ngClass]="{'is-required-field': type === 'PrefillTitleOnly' &&  vehicle.condition === ''}">
        <label for="condition">Condition:</label>
    </div>
    <div class="col-md-2" [ngClass]="{'is-required-field': type === 'PrefillTitleOnly' &&  vehicle.condition === ''}">
      <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
        <input type="radio" name="condition" value="New"
          [(ngModel)]="vehicle.condition">
        <i class="o-btn o-btn--radio"></i>
        <span style="margin-right: 10px;">New</span>
        </label>
        <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
          <input type="radio" name="condition" value="Used"
            [(ngModel)]="vehicle.condition">
          <i class="o-btn o-btn--radio"></i>
          <span>Used</span>
          </label>
    </div>-->
</div>
<div class="row">
    <div class="col-md-2 label-width" [ngClass]="{'is-required-field': type === 'PrefillTitleOnly' &&  vehicle.odometer === ''}">
        <label for="odometer">Odometer:</label>
    </div>
    <div class="col-md-2" [ngClass]="{'is-required-field': type === 'PrefillTitleOnly' &&  vehicle.odometer === ''}">
        <input [(ngModel)]="vehicle.odometer" name="odometer" type="number" placeholder="Miles">
    </div>
    <div class="col-md-2 offset-md-1 label-width">
        <label for="condition">Primary Color:</label>
    </div>
    <div class="col-md-2">
      <sm-autocomplete [activeOption]="vehicle.primaryColor" [(ngModel)]="vehicle.primaryColor" name="primaryColor" [options]="primaryColorOptions"></sm-autocomplete>
    </div>
</div>
<div class="row">
    <div class="col-md-2 label-width" >
        <label for="registration">Registration:</label>
    </div>
    <div class="col-md-2">
      <sm-autocomplete [activeOption]="vehicle.registrationType" [options]="registrationTypeOptions" name="registrationType" [(ngModel)]="vehicle.registrationType"></sm-autocomplete>
    </div>
    <div class="col-md-2 offset-md-1 label-width">
        <label for="condition">Transmission:</label>
    </div>
    <div class="col-md-2">
      <sm-autocomplete [activeOption]="vehicle.transmission" [options]="transmissionOptions" name="transmission" [(ngModel)]="vehicle.transmission"></sm-autocomplete>
    </div>
</div>
<div class="row">
    <div class="col-md-2 label-width">
        <label for="condition">Passengers:</label>
    </div>
    <div class="col-md-2">
        <input [(ngModel)]="vehicle.passengers" name="passengers" placeholder="No. of Passengers">
    </div>
    <div class="col-md-2 offset-md-1 label-width" [ngClass]="{'is-required-field': type === 'PrefillTitleOnly' &&  vehicle.ownership === ''}">
      <label for="ownership">Ownership:</label>
  </div>
  <div class="col-md-2" [ngClass]="{'is-required-field': type === 'PrefillTitleOnly' &&  vehicle.ownership === ''}">
      <sm-autocomplete [activeOption]="vehicle.ownership" [(ngModel)]="vehicle.ownership" name="ownership" [options]="ownershipType"></sm-autocomplete>
  </div>
</div>
</ng-template>

<!--Reinstate-->

<ng-template #reinstate [ngSwitchCase]="checkIfReinstateOnly()">
  <div class="row">
    <div class="col-md-2 label-width">
        <label for="plate">Vehicle:</label>
    </div>
    <div class="col-md-1">
        <input [(ngModel)]="vehicle.plateNumber" name="plate" placeholder="Plate" style="width:80px;padding-right:10px;">
    </div>
    <div class="col-md-1">
        <sm-autocomplete [activeOption]="vehicle.plateType"
        [searchFromBegining]="false" [allowSearchById]="true"
         style="width: 100px;" [options]="plateTypeOptions" [(ngModel)]="vehicle.plateType" name="plateType" placeholder="Type"></sm-autocomplete>
    </div>
    <!--<div class="col-md-2 offset-md-1 label-width">
        <label for="condition">Condition:</label>
    </div>
    <div class="col-md-2">
      <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
        <input type="radio" name="condition" value="New"
          [(ngModel)]="vehicle.condition">
        <i class="o-btn o-btn--radio"></i>
        <span style="margin-right: 10px;">New</span>
        </label>
        <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
          <input type="radio" name="condition" value="Used"
            [(ngModel)]="vehicle.condition">
          <i class="o-btn o-btn--radio"></i>
          <span>Used</span>
          </label>
    </div>-->
</div>
<div class="row">
  <div class="col-md-2 label-width">
      <label for="odometer">Odometer:</label>
  </div>
  <div class="col-md-2">
      <input [(ngModel)]="vehicle.odometer" name="odometer" placeholder="Miles">
  </div>
  <div class="col-md-2 offset-md-1 label-width">
      <label for="color">Primary Color:</label>
  </div>
  <div class="col-md-2">
    <sm-autocomplete [activeOption]="vehicle.primaryColor" [(ngModel)]="vehicle.primaryColor" name="primaryColor" [options]="primaryColorOptions"></sm-autocomplete>
  </div>
</div>
<div class="row">
  <div class="col-md-2 label-width">
      <label for="registration">Registration:</label>
  </div>
  <div class="col-md-2">
    <sm-autocomplete [activeOption]="vehicle.registrationType" [options]="registrationTypeOptions" name="registrationType" [(ngModel)]="vehicle.registrationType"></sm-autocomplete>
  </div>
  <div class="col-md-2 offset-md-1 label-width">
      <label for="condition">Transmission:</label>
  </div>
  <div class="col-md-2">
    <sm-autocomplete [activeOption]="vehicle.transmission" [options]="transmissionOptions" name="transmission" [(ngModel)]="vehicle.transmission"></sm-autocomplete>
  </div>
</div>
<div class="row">
  <div class="col-md-2 label-width">
      <label for="condition">Passengers:</label>
  </div>
  <div class="col-md-2">
      <input [(ngModel)]="vehicle.passengers" name="passengers" placeholder="No. of Passengers">
  </div>
</div>
</ng-template>
</div>
</div>
</section>
