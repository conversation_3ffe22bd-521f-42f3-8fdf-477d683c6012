<section class="section" *ngIf="showErrors">
  <div class="row u-spacing--1-5">
    <div class="col-xs-12">
      <div class="box box--warning">
        <p>Fill all the required Fields to save</p>
      </div>
    </div>
  </div>
</section>

<section class="section">
  <div class="u-flex u-flex--spread">
    <div class="">
      <h1 class="o-heading">Client Info</h1>
    </div>
  </div>

  <div class="row u-spacing--1-5">
    <div class="col-xs-12">
      <div class="box box--silver">
        <div class="row o-columns" id="collapsable-content">
          <div class="col-xs-12">
            <table class="form-table">
              <tr class="form-table__row">
                <td class="form-table__cell">
                  <table class="form-table">
                    <tbody class="form-table__tbody">
                      <tr class="form-table__row">
                        <td class="form-table__cell u-width-80px">
                          Customer #:
                        </td>
                        <td class="form-table__cell u-width-175px">
                          <input type="text" id="customerNo" name="customerNo" fieldAutofocus
                            [(ngModel)]="selectedClientDetails.customerNumber"
                            (blur)="actionUpdateSelectedClientDetails()">
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
                <td class="form-table__cell u-width-50px"></td>
                <td class="form-table__cell">
                  <table class="form-table">
                    <tbody class="form-table__tbody">
                      <tr class="form-table__row">
                        <td class="form-table__cell u-width-80px">
                          Client Type:
                        </td>
                        <td class="form-table__cell">
                          <label class="o-checkable" (click)="setClientType('personal')" [appDetectSystem]
                            [removeRadio]="true">
                            <input type="radio" name="radio-1" value="1" [checked]="showPersonalForm">
                            <i class="o-btn o-btn--radio"></i>
                            <span>Personal</span>
                          </label>

                          <label class="o-checkable u-spacing--left-2" (click)="setClientType('commercial')"
                            [appDetectSystem] [removeRadio]="true">
                            <input type="radio" name="radio-1" value="2" [checked]="showCommercialForm">
                            <i class="o-btn o-btn--radio"></i>
                            <span>Commercial</span>
                          </label>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </td>
              </tr>
            </table>

            <table class="form-table">
              <thead class="form-table__thead">
                <th class="form-table__cell u-width-80px">
                  <span *ngIf="showPersonalForm" class="o-heading o-heading--label">Insured</span>
                  <span *ngIf="showCommercialForm" class="o-heading o-heading--label">Contact</span>
                </th>
                <th class="form-table__cell u-width-175px">
                  <span class="o-heading o-heading--label">First Name</span>
                </th>
                <th class="form-table__cell u-width-175px">
                  <span class="o-heading o-heading--label u-padd--left-1-5">Last Name</span>
                </th>
                <th class="form-table__cell u-width-175px">
                  <span class="o-heading o-heading--label u-padd--left-1-5">Date of birth</span>
                </th>
              </thead>
              <tbody class="form-table__tbody">

                <tr class="form-table__row">
                  <td class="form-table__cell">
                    Primary
                  </td>
                  <td class="form-table__cell" [ngClass]="{'is-required-field': fieldPrimaryFirstNameIsRequired()}">
                    <input type="text" id="clientDetailsPrimaryFirstName" name="clientDetailsPrimaryFirstName" maxlength="45"
                      [(ngModel)]="selectedClientDetails.firstName" (blur)="actionUpdateSelectedClientDetails()">
                  </td>
                  <td class="form-table__cell" [ngClass]="{'is-required-field': fieldPrimaryLastNameIsRequired()}">
                    <input type="text" id="clientDetailsPrimaryLastName" name="clientDetailsPrimaryLastName" maxlength="45"
                      [(ngModel)]="selectedClientDetails.lastName" (blur)="actionUpdateSelectedClientDetails()">
                  </td>
                  <td class="form-table__cell">
                    <span class="u-padd--left-1-5 u-show-iblock">
                      <app-datepicker-input [id]="'clientDetailsPrimaryDOB'" [name]="'clientDetailsPrimaryDOB'"
                        [selectDate]="selectedClientDetails.dob"
                        (onDateChange)="actionUpdateDateSelectedClientDetails($event, 'dob')">
                      </app-datepicker-input>
                    </span>
                  </td>
                </tr>

                <tr class="form-table__row">
                  <td class="form-table__cell">
                    Secondary
                  </td>
                  <td class="form-table__cell">
                    <input type="text" id="clientSecondaryFirstName" name="clientSecondaryFirstName" maxlength="45"
                      [(ngModel)]="selectedClientDetails.secondaryFirstName"
                      (blur)="actionUpdateSelectedClientDetails()">
                  </td>
                  <td class="form-table__cell">
                    <input type="text" id="clientSecondaryLastName" name="clientSecondaryLastName" maxlength="45"
                      [(ngModel)]="selectedClientDetails.secondaryLastName"
                      (blur)="actionUpdateSelectedClientDetails()">
                  </td>
                  <td class="form-table__cell" [ngClass]="{'is-required-field': fieldSecondaryDOBIsRequired()}">
                    <span class="u-padd--left-1-5 u-show-iblock">
                      <app-datepicker-input #refPickerDateOfBirthPrimary [id]="'clientInfoSecondaryDOB'"
                        [name]="'clientInfoSecondaryDOB'" [required]="true"
                        [selectDate]="selectedClientDetails.secondaryDOB"
                        (onDateChange)="actionUpdateDateSelectedClientDetails($event, 'secondaryDOB')">
                      </app-datepicker-input>
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
            <table class="form-table" *ngIf="showCommercialForm">
              <tr class="form-table__row">
                <td class="form-table__cell u-width-90px">
                  Business Name:
                </td>
                <td class="form-table__cell u-width-560px"
                  [ngClass]="{'is-required-field': fieldBusinessNameIsRequired()}">
                  <input type="text" id="clientDetailsBusinessName" name="clientDetailsBusinessName" [required]="true"
                    [(ngModel)]="selectedClientDetails.business.name" (blur)="actionUpdateSelectedClientDetails()" maxlength="45">
                </td>
              </tr>
              <tr class="form-table__row">
                <td class="form-table__cell u-width-90px">
                  Description:
                </td>
                <td class="form-table__cell u-width-560px">
                  <textarea rows="5" cols="50" id="clientBusinessDesc" name="clientBusinessDesc"
                    [(ngModel)]="selectedClientDetails.business.description"
                    (blur)="actionUpdateSelectedClientDetails()"></textarea>
                </td>
              </tr>
              <tr class="form-table__row">
                <td class="form-table__cell" colspan="2">
                  <table class="form-table">
                    <tr class="form-table__row">
                      <td class="form-table__cell u-width-115px">
                        Website:
                      </td>
                      <td class="form-table__cell u-width-460px">
                        <input type="text" id="clientBusinessWebsite" name="clientBusinessWebsite"
                          [(ngModel)]="selectedClientDetails.business.website"
                          (blur)="actionUpdateSelectedClientDetails()">
                      </td>
                      <td class="form-table__cell u-width-80px">
                        SIC Code:
                      </td>
                      <td class="form-table__cell u-width-190px">
                        <input type="text" id="clientBusinessSiccode" name="clientBusinessSiccode"
                          [(ngModel)]="selectedClientDetails.business.sicCode"
                          (blur)="actionUpdateSelectedClientDetails()">
                      </td>
                    </tr>
                  </table>
                  <table class="form-table">
                    <tr class="form-table__row">
                      <td class="form-table__cell u-width-115px">
                        <label for="">Legal Entity:</label>
                      </td>
                      <td class="form-table__cell u-width-300px"
                        [ngClass]="{'is-required-field': fieldLegalEntityIsRequired()}">
                        <sm-autocomplete [options]="optionsLegalEntity"
                          [activeOption]="selectedClientDetails.business.legalEntity"
                          [id]="'clientDetailsBusinessLegalEntity'" [required]="true"
                          [name]="'clientDetailsBusinessLegalEntity'" [allowEmptyValue]="false"
                          (onSelect)="onSelect($event, selectedClientDetails.business, 'legalEntity')">
                        </sm-autocomplete>
                      </td>
                      <td class="form-table__cell u-width-65px">
                        Tax ID:
                      </td>
                      <td class="form-table__cell u-width-170px">
                        <input type="text" id="clientBusinessTaxId" name="clientBusinessTaxId"
                          [(ngModel)]="selectedClientDetails.business.taxId"
                          (blur)="actionUpdateSelectedClientDetails()">
                      </td>
                      <td class="form-table__cell u-width-70px">
                        Type:
                      </td>
                      <td class="form-table__cell u-width-125px">
                        <sm-autocomplete [options]="optionsTaxType"
                          [activeOption]="selectedClientDetails.business.taxType" [id]="'clientBusinessTaxType'"
                          [name]="'clientBusinessTaxType'" [allowEmptyValue]="false" [searchFromBegining]="true"
                          (onSelect)="onSelect($event, selectedClientDetails.business, 'taxType')">
                        </sm-autocomplete>
                      </td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<section class="section">
  <div class="u-flex u-flex--spread">
    <div class="">
      <h1 class="o-heading">Addresses</h1>
    </div>
    <div class="">
    </div>
  </div>


  <div class="row u-spacing--1-5 u-position--relative">
    <!--app-loader [loading]="loadingAddresses" [loadingText]="'Loading, please wait...'"></app-loader> -->

    <div class="col-xs-12">
      <div class="box box--silver">
        <div class="row o-columns">
          <div class="col-xs-6 u-width-405px">
            <h2 class="o-heading o-heading--label">Current</h2>
            <table class="form-table u-width-320px">
              <!-- tr class="form-table__row" [class.is-required-field]="true" -->
              <tr class="form-table__row">
                <td class="form-table__cell">
                  <input type="text" [id]="generateAddressFieldId(clientAddressCurrent, 'clientInfoAddrAddr1')"
                    [name]="generateAddressFieldId(clientAddressCurrent, 'clientInfoAddrAddr1')" maxlength="60"
                    [(ngModel)]="clientAddressCurrent.address1"
                    (blur)="actionUpdateClientAddress(clientAddressCurrent)">
                </td>
              </tr>
              <tr class="form-table__row">
                <td class="form-table__cell">
                  <input type="text" [id]="generateAddressFieldId(clientAddressCurrent, 'clientInfoAddrAddr2')"
                    [name]="generateAddressFieldId(clientAddressCurrent, 'clientInfoAddrAddr2')" maxlength="60"
                    [(ngModel)]="clientAddressCurrent.address2"
                    (blur)="actionUpdateClientAddress(clientAddressCurrent)">
                </td>
              </tr>
              <tr class="form-table__row">
                <td class="form-table__cell">
                  <span class="u-show-iblock u-width-170px">
                    <input type="text" [id]="generateAddressFieldId(clientAddressCurrent, 'clientInfoAddrCity')"
                      [name]="generateAddressFieldId(clientAddressCurrent, 'clientInfoAddrCity')" maxlength="45"
                      [(ngModel)]="clientAddressCurrent.city" (blur)="actionUpdateClientAddress(clientAddressCurrent)">
                  </span>

                  <span class="u-show-iblock u-spacing--left-0-5 u-width-70px">
                    <sm-autocomplete [options]="optionsStates" [activeOption]="clientAddressCurrent.state"
                      [id]="generateAddressFieldId(clientAddressCurrent, 'clientInfoAddrState')"
                      [name]="generateAddressFieldId(clientAddressCurrent, 'clientInfoAddrState')"
                      [searchFromBegining]="true" [placeholder]="'State'"
                      (onSelect)="actionOnSelectAddress($event, clientAddressCurrent, 'state')">
                    </sm-autocomplete>
                  </span>

                  <span class="u-show-iblock u-spacing--left-0-5 u-width-60px">
                    <input type="text" [id]="generateAddressFieldId(clientAddressCurrent, 'clientInfoAddrZip')"
                      [name]="generateAddressFieldId(clientAddressCurrent, 'clientInfoAddrZip')"
                      pattern="^[0-9]{5}(?:-[0-9]{4})?$|^[0-9]{9}$"
                      [(ngModel)]="clientAddressCurrent.zip" (blur)="actionUpdateClientAddress(clientAddressCurrent)">
                  </span>
                </td>
              </tr>

              <tr class="form-table__row">
                <td class="form-table__cell" *ngIf="showCommercialForm">
                  <span class="u-show-iblock u-width-130px">
                    Business Start Date:
                  </span>
                  <span class="u-show-iblock u-width-175px">
                    <app-datepicker-input #refPickerDateOfBirthPrimary [id]="'businessStartDt'"
                      [name]="'businessStartDt'" [required]="true"
                      [selectDate]="selectedClientDetails.business.businessStartDt"
                      (onDateChange)="onDateSelect($event, selectedClientDetails.business, 'businessStartDt')">
                    </app-datepicker-input>
                  </span>
                </td>
                <td class="form-table__cell" *ngIf="!showCommercialForm">
                  <span class="u-show-iblock u-width-130px" *ngIf="!showCommercialForm">
                    Move-In Date:
                  </span>
                  <span class="u-show-iblock u-width-175px">
                    <app-datepicker-input #refPickerDateOfBirthPrimary
                      [id]="generateAddressFieldId(clientAddressCurrent, 'clientInfoAddrResidencyDate')"
                      [name]="generateAddressFieldId(clientAddressCurrent, 'clientInfoAddrResidencyDate')"
                      [required]="true" [selectDate]="clientAddressCurrent.residencyDate"
                      (onDateChange)="actionUpdateDateClientAddress($event, clientAddressCurrent, 'residencyDate')">
                    </app-datepicker-input>
                  </span>
                </td>
              </tr>
            </table>
          </div>
          <!-- <tbody class="form-table__tbody"> -->

          <div class="col-xs-6">
            <h2 class="o-heading o-heading--label">Prior</h2>
            <table class="form-table u-width-320px">
              <tr class="form-table__row">
                <td class="form-table__cell">
                  <input type="text" [id]="generateAddressFieldId(clientAddressPrior, 'clientInfoAddrAddr1')"
                    [name]="generateAddressFieldId(clientAddressPrior, 'clientInfoAddrAddr1')" maxlength="60"
                    [(ngModel)]="clientAddressPrior.address1" (blur)="actionUpdateClientAddress(clientAddressPrior)">
                </td>
              </tr>
              <tr class="form-table__row">
                <td class="form-table__cell">
                  <input type="text" [id]="generateAddressFieldId(clientAddressPrior, 'clientInfoAddrAddr2')"
                    [name]="generateAddressFieldId(clientAddressPrior, 'clientInfoAddrAddr2')" maxlength="60"
                    [(ngModel)]="clientAddressPrior.address2" (blur)="actionUpdateClientAddress(clientAddressPrior)">
                </td>
              </tr>
              <tr class="form-table__row">
                <td class="form-table__cell">
                  <span class="u-show-iblock u-width-170px">
                    <input type="text" [id]="generateAddressFieldId(clientAddressPrior, 'clientInfoAddrCity')"
                      [name]="generateAddressFieldId(clientAddressPrior, 'clientInfoAddrCity')" maxlength="45"
                      [(ngModel)]="clientAddressPrior.city" (blur)="actionUpdateClientAddress(clientAddressPrior)">
                  </span>

                  <span class="u-show-iblock u-spacing--left-0-5 u-width-70px">
                    <sm-autocomplete [options]="optionsStates" [activeOption]="clientAddressPrior.state"
                      [id]="generateAddressFieldId(clientAddressPrior, 'clientInfoAddrState')"
                      [name]="generateAddressFieldId(clientAddressPrior, 'clientInfoAddrState')"
                      [searchFromBegining]="true" [placeholder]="'State'"
                      (onSelect)="actionOnSelectAddress($event, clientAddressPrior, 'state')">
                    </sm-autocomplete>
                  </span>

                  <span class="u-show-iblock u-spacing--left-0-5 u-width-60px">
                    <input type="text" [id]="generateAddressFieldId(clientAddressPrior, 'clientInfoAddrZip')"
                      [name]="generateAddressFieldId(clientAddressPrior, 'clientInfoAddrZip')"
                      pattern="^[0-9]{5}(?:-[0-9]{4})?$|^[0-9]{9}$"
                      [(ngModel)]="clientAddressPrior.zip" (blur)="actionUpdateClientAddress(clientAddressPrior)">
                  </span>
                </td>
              </tr>
            </table>
          </div>

        </div>
      </div>
    </div>
  </div>
</section>

<section class="section">
  <div class="u-flex u-flex--spread">
    <div class="">
      <h1 class="o-heading">Contact info</h1>
    </div>
    <div class="">
    </div>
  </div>

  <div class="row u-spacing--1-5 u-position--relative">
    <!-- app-loader [loading]="loadingContacts" [loadingText]="'Loading, please wait...'"></app-loader -->

    <div class="col-xs-12">
      <div class="box box--silver">
        <div class="row o-columns" id="collapsable-content">
          <div class="col-xs-12">

            <table class="form-table">
              <tbody class="form-table__tbody">
                <tr class="form-table__row">
                  <td class="form-table__cell u-width-215px"
                    [ngClass]="{'is-required-field': !isValidOrEmptyPhoneNumber(clientContactHome.value)}">
                    <div class="u-show-iblock u-width-60px">
                      Home:
                    </div>
                    <div class="u-show-iblock u-width-120px">
                      <input type="text" id="cliContactHome" name="cliContactHome" mask="(000)000-0000"
                        [(ngModel)]="clientContactHome.value" (blur)="actionUpdateClientContactMethod()">
                    </div>
                  </td>
                  <td class="form-table__cell u-width-210px"
                    [ngClass]="{'is-required-field': !isValidOrEmptyPhoneNumber(clientContactWork.value)}">
                    <div class="u-show-iblock u-width-50px">
                      Work:
                    </div>
                    <div class="u-show-iblock u-width-120px">
                      <input type="text" id="cliContactWork" name="cliContactWork" mask="(000)000-0000"
                        [(ngModel)]="clientContactWork.value" (blur)="actionUpdateClientContactMethod()">
                    </div>
                  </td>
                  <td class="form-table__cell u-width-310px"
                    [ngClass]="{'is-required-field': !isValidOrEmptyEmail(clientContactEmail.value)}">
                    <div class="u-show-iblock u-width-85px">
                      Email:
                    </div>
                    <div class="u-show-iblock u-width-210px">
                      <input type="text" id="cliContactEmail" name="cliContactEmail" maxlength="45"
                        [(ngModel)]="clientContactEmail.value" (blur)="actionUpdateClientContactMethod()">
                    </div>
                  </td>

                </tr>
                <tr class="form-table__row">
                  <td class="form-table__cell"
                    [ngClass]="{'is-required-field': !isValidOrEmptyPhoneNumber(clientContactMobile.value)}">
                    <div class="u-show-iblock u-width-60px">
                      Mobile:
                    </div>
                    <div class="u-show-iblock u-width-120px">
                      <input type="text" id="cliContactMobile" name="cliContactMobile" mask="(000)000-0000"
                        [(ngModel)]="clientContactMobile.value" (blur)="actionUpdateClientContactMethod()">
                    </div>
                  </td>
                  <td class="form-table__cell u-width-210px"
                    [ngClass]="{'is-required-field': !isValidOrEmptyPhoneNumber(clientContactFax.value)}">
                    <div class="u-show-iblock u-width-50px">
                      Fax:
                    </div>
                    <div class="u-show-iblock u-width-120px">
                      <input type="text" id="cliContactFax" name="cliContactFax" mask="(000)000-0000"
                        [(ngModel)]="clientContactFax.value" (blur)="actionUpdateClientContactMethod()">
                    </div>
                  </td>
                  <td class="form-table__cell u-width-210px">
                    <div class="u-show-iblock u-width-85px">
                      Preference:
                    </div>
                    <div class="u-show-iblock u-width-130px">
                      <sm-autocomplete [options]="optionsContactMethodsPreference"
                        [activeOption]="optionsContactMethodsPreferenceSelected" [searchFromBegining]="true"
                        (onSelect)="onContactPreferenceChange($event)">
                      </sm-autocomplete>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>

          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<section class="section">
  <div class="u-flex u-flex--spread">
    <div class="">
      <h1 class="o-heading">Comment</h1>
    </div>
    <div class="">
    </div>
  </div>

  <div class="row u-spacing--1-5">
    <div class="col-xs-12">
      <div class="box box--silver">
        <div class="row o-columns" id="collapsable-content">
          <div class="col-xs-12">
            <!-- Tom prefers better coverage over cheaper prices. -->
            <textarea maxlength="500" rows="5" cols="50" id="clientComment" name="clientComment" maxlength="500"
              [(ngModel)]="selectedClientDetails.comment" (blur)="actionUpdateSelectedClientDetails()"></textarea>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<app-leave-quote #leaveQuote [messageFor]="'client'"></app-leave-quote>
