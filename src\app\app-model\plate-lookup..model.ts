export interface Message {
  message: string;
}

export interface Owner {
  licenseNumber: string;
  licenseState: string;
  lastName: string;
  firstName: string;
  middleName: string;
  nameSuffix: string;
  dateOfBirth: string;
  garagingAddress: any;
  atlasEntityKey?: any;
  atlasEntityLocationKey?: any;
  ownershipRole: string;
  fid?: string;
  entityType?: string;
}

export interface PlateLookupResponse {
  isVehicleFoundInAtlas: boolean;
  isPersonalOwnership: boolean;
  entityType: string;
  leasedVehicleIndicator: boolean;
  messages: Message[];
  owners: Owner[];
  vehicle:any
}
