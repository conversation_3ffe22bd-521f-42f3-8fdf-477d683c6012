
import { take, first } from 'rxjs/operators';
import { FeatureService } from './../../shared/services/feature.service';
import { ActivatedRoute } from '@angular/router';
import { LeaveQuoteService } from './../../shared/services/leave-quote.service';
import * as _ from 'underscore';
import { Component, OnInit, ViewChild, Input, isDevMode } from '@angular/core';
import { Observable, SubscriptionLike as ISubscription } from 'rxjs';


import { AgencyForm } from 'app/app-model/form';
import { AgencyUserService, UserData, UserSubscriptionInformationAPIResopnseI } from 'app/shared/services/agency-user.service';
import { ApiService } from 'app/shared/services/api.service';
import { CrossApplicationService } from 'app/shared/services/cross-application.service';
import { CurrentPageService } from 'app/shared/services/current-page.service';
import { DateRange } from 'app/app-model/date';
import { DatesService } from '../app-services/dates.service';
import { DeleteFormModalComponent } from 'app/shared/components/delete-form/delete-form.component';
import { FilterOption } from 'app/app-model/filter-option';
import { FormsService } from '../app-services/forms.service';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { Quote } from 'app/app-model/quote';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { SubsService } from '../app-services/subs.service';
import { LeaveQuoteComponent } from 'app/shared/components/leave-quote/leave-quote.component';
import { RouteService } from 'app/shared/services/route.service';
import { ModalboxComponent } from 'app/shared/components/modalbox/modalbox.component';
import { LobTabbedNavComponent } from '../lob-tabbed-nav/lob-tabbed-nav.component';
import { SearchByNameComponent } from '../search-by-name/search-by-name.component';
import { FilterComponent } from 'app/shared/components/filter/filter.component';
import { UserSubscriptionRatingEmitDataI } from '../../shared/directives/user-subscription-rating.directive';
import { RmvService } from '../app-services/rmv.service';
import { FilterDatesComponent } from 'app/shared/components/filter-dates/filter-dates.component';
import { format, parseISO } from 'date-fns';



@Component({
    selector: 'app-page-forms',
    templateUrl: './page-forms.component.html',
    styleUrls: ['./page-forms.component.scss'],
    standalone: false
})

export class PageFormsComponent implements OnInit {
  @Input() isDashboard: boolean | null = null;
  @ViewChild(LobTabbedNavComponent) tabbedNav;
  @ViewChild('deleteFormModal') public deleteFormModal: DeleteFormModalComponent;
  @ViewChild('leaveQuote') leaveQuote: LeaveQuoteComponent;
  @ViewChild(SearchByNameComponent) searchProperties;
  @ViewChild('refModalNewForm') refModalNewForm: ModalboxComponent;
  @ViewChild('refFilterDates') public FilterDates: FilterComponent;
  @ViewChild(FilterDatesComponent) filterDates;

  private subscriptionSubscriptionInformation: ISubscription;
  public formsOnlyUser = false;
  clientType;
  rmvList = [];

  public paginationResultsCount = 0;
  public paginationResultLimit = 10;
  public paginationResultShowFrom = 0;
  public paginationCurrentPage = 1;

  public arrAgencyFormsAll: AgencyForm[];
  public arrAgencyFormsFiltered: AgencyForm[];
  private dataInitiated = false;

  public nameSearchQuery: string;
  public searchQuery: boolean;
  public nameType = 'Last';
  public filterLobOptions: FilterOption[] = [
    { id: 'all', text: 'All' },
    { id: 'AUTOP', text: 'Personal Auto' },
    { id: 'DFIRE', text: 'Dwelling Fire' },
    { id: 'HOME', text: 'Homeowners' },
    { id: 'PUMBR', text: 'Umbrella' }
  ];

  public subscriptionMessages: string[] = [];
  public proceed = true;
  private filterLobSelectedOption: FilterOption = this.filterLobOptions[0];
  public filterLobLabel: string;

  public filterFormsOptions: FilterOption[] = [{ id: 'all', text: 'All' }];
  private filterFormsSelectedOption: FilterOption = this.filterFormsOptions[0];
  public filterFormsLabel: string;

  public filterAgentsOptions: FilterOption[] = [{ id: 'all', text: 'All' }];
  private filterAgentsSelectedOption: FilterOption = this.filterAgentsOptions[0];
  public filterAgentsLabel: string;

  public filterLocationsOptions: FilterOption[] = [{ id: 'all', text: 'All' }];
  private filterLocationsSelectedOption: FilterOption = this.filterLocationsOptions[0];
  private filterLocationLabel: string;

  public dateRange: DateRange = new DateRange();

  public agencyFormsFilteredItems: Array<AgencyForm>;
  public agencyFormsVisibleItems: Array<AgencyForm>;
  public selectedFormsToDelete: Array<AgencyForm>;
  public selectedFormsToDeleteList: string;

  public sortOptionsStatus: Array<string>;
  public sortOptionsSource: Array<string>;
  public sortOptionsDates: Array<string>;
  public sortOptionsStatusChoosen = 'All';
  public sortOptionsSourceChoosen = 'All';
  public sortOptionsDatesChoosen = 'All';

  public isDeleteConfirmed = false;
  public refFormModalHandle: any;

  private agencyId: string;
  private getAgencyFormsSubscription: ISubscription;
  private getAgencyFormsRequestDataString = '';
  private userData: UserData;

  private agencyInfo;
  private subscriptionAgency;

  public selectedForms: string[] = [];
  public isAllSelected = false;
  private isRefreshRequest = false;

  private getAgencyUsersSubscription;
  private agencyUserServiceSubscription;
  private getRatingLobsServiceSubscription;
  private getRatingLocationsServiceSubscription;
  private formServiceSubscription;
  private dataToChange: any;
  private quoteId = '';
  private formCreated = false;
  splitList = [
    { text: 'Personal Forms', link: 'forms' },
    { text: 'Commercial Forms', link: 'forms', type: 'commercial' }
  ];
  PersonalCommercialSeparate;
  formsExpired;
  quoteIdentifier: string;
  rmvPaginationResultShowFrom: number;
  rmvPaginationCurrentPage: number;
  rmvSize: any;
  quoteLob: string;
  constructor(
    private agencyUserService: AgencyUserService,
    private apiService: ApiService,
    private crossApplicationService: CrossApplicationService,
    private currentPageService: CurrentPageService,
    private datesService: DatesService,
    private formsService: FormsService,
    private overlayLoaderService: OverlayLoaderService,
    private storageGlobalService: StorageGlobalService,
    private storageService: StorageService,
    private subsService: SubsService,
    private routeService: RouteService,
    private leaveQuoteService: LeaveQuoteService,
    private route: ActivatedRoute,
    private featureService: FeatureService,
    private rmvService: RmvService
  ) { }

  ngOnInit() {

    this.filterLobSelectedOption = this.filterLobOptions[0];
    this.subscribeSubscriptionInformation();
    this.getUser();
    this.getAgentData();
    this.setFilterFormsOptions();
    this.setFilterAgentsOptions();
    //  this.setFilterLobOptions();
    this.setFilterLocationsOptions();
    this.subscribeQuote();
    this.route.queryParams.subscribe(params => {
      params['lob'] ? this.clientType = 'Commercial' : this.clientType = '';
      this.getAgencyForms(true);
    });
    setTimeout(() => {
      this.PersonalCommercialSeparate = this.featureService.isCommercialSplit();
    }, 100);
    if (this.quoteLob === 'AUTOP') { this.getRmvTransactions(true); }
  }
  ngOnDestroy() {
    //        this.getAgencyFormsSubscription && this.getAgencyFormsSubscription.unsubscribe();
    this.formServiceSubscription && this.formServiceSubscription.unsubscribe();
    this.subscriptionAgency && this.subscriptionAgency.unsubscribe();
    this.subscriptionSubscriptionInformation && this.subscriptionSubscriptionInformation.unsubscribe();
  }

  // https://bostonsoftware.atlassian.net/browse/SPR-2778
  private subscribeSubscriptionInformation(): void {
    this.subscriptionSubscriptionInformation = this.agencyUserService.getSubscriptionInformation$
      .subscribe((res: UserSubscriptionInformationAPIResopnseI) => {
        this.formsOnlyUser = this.agencyUserService.checkIfUserSubscriptionAllowOnlyForms(res);
        this.formsExpired = res.items.find(x => x.code === 'Forms').expired;
      });
  }

  public filterByName(event) {
    this.nameSearchQuery = this.searchProperties.searchQuery;
    this.filterDates.searchQuery = this.nameSearchQuery;
    this.nameType = this.searchProperties.nameType;
    if ((event.key === 'Enter' && this.nameSearchQuery.length >= 3) || (event.target.tagName === 'BUTTON' && this.nameSearchQuery.length >= 3)) {
      this.filterDates.filterDatesLabel = 'All Dates';
      this.getAgencyForms(true);
    } else {
      this.resetSearch();
    }
  }

  public toggleForm(formIdentifier: string): void {
    const itemIndex = this.selectedForms.indexOf(formIdentifier);
    const isPresent = (itemIndex > -1);

    if (isPresent) {
      this.selectedForms.splice(itemIndex, 1);
    } else {
      this.selectedForms.push(formIdentifier);
    }
  }

  // to check/uncheck based on the selected form list
  public isSelected(formIdentifier: string): boolean {
    return (this.selectedForms.indexOf(formIdentifier) > -1 || this.isAllSelected);
  }

  public isQuoteForms(): boolean {
    return (this.currentPageService.routeData && this.currentPageService.routeData.additionalParam ? true : false);
  }

  public isSearchResults(): boolean {
    return (this.searchQuery ? true : false);
  }

  // Toggles all the forms with a single select.
  public toggleAll(): void {
    this.isAllSelected = !this.isAllSelected;

    if (this.isAllSelected) {
      this.arrAgencyFormsAll.forEach(form => {
        if (this.selectedForms.indexOf(form.agencyFormId) === -1) {
          this.selectedForms.push(form.agencyFormId);
        }
      });
    } else {
      this.selectedForms = [];
    }
  }

  // to check/uncheck using a property
  public isChecked(): boolean {
    return this.isAllSelected;
  }

  // This method only updates the form status to deleted
  // But will not physically remove the form from the database.
  // Physical deletion is done at a later stage through automation
  public deleteForms(): void {
    this.deleteFormModal.agencyFormListToDelete = this.selectedForms;
    this.deleteFormModal.modal.open();
  }

  public openFormSelectorModal(refModal) {
    if (this.routeService.inUrl && this.leaveQuote && !this.routeService.isMaipArc && !this.leaveQuoteService.checkIfTheQuoteIsSaved()) {
      this.leaveQuote.leavingText = 'In order to prefill the form with quote data and to save the form, your quote will need to be saved. Do you want to save your quote before proceeding?';
      this.leaveQuote.forceConfirmation = true;
      this.leaveQuote.confirm().then(answer => {
        if (answer) {
          this.refFormModalHandle = refModal;
          refModal.openModalbox();
        }
      });
    } else {
      this.refFormModalHandle = refModal;
      refModal.openModalbox();
    }

  }


  // This is triggered once a delete forms method is called from Delete-Form.component
  public refreshPage(event) {
    // this property is set to indicate to the getforms method to refresh the current page.
    this.isRefreshRequest = true;
    this.selectedForms = [];
    this.getAgencyForms();
  }

  // Deprecated - TODO:: remove
  public cancelClick($ev, refModal): void {
    this.refFormModalHandle.closeModalbox();
    if (this.formCreated) {
      this.refreshPage(new Event('click'));
      this.formCreated = false;
    }
  }
  // Deprecated - END - TODO:: remove


  // New Form Create Actions
  // ---------------------------------------------------------------------------
  public openNewFormCreateModal(): void {
    this.refModalNewForm && this.refModalNewForm.open();
  }

  public actionNewFormCreateFormCreated(): void {
    this.refModalNewForm && this.refModalNewForm.close();
    this.refreshPage(new Event('click'));
  }

  public actionNewFormCreateCancel(): void {
    this.refModalNewForm && this.refModalNewForm.close();
  }



  canDeactivate(): Observable<boolean> | boolean {
    console.log('Can Deactivate');
    this.leaveQuote.leavingText = 'Would you like to save this quote before you continue?';
    this.leaveQuote.detectConfirmation();
    return this.leaveQuote.detectConfirmationObservable.asObservable().pipe(first());
  }

  public encodeTicket(ticket: string): string {
    return encodeURIComponent(ticket);
  }

  public formAppUrl(uri: string): string {
    //      uri = uri + '&agentId=' + this.userData.user.userId;
    return this.apiService.formAppUrl(uri);
  }

  public openAgencyForm(agencyForm): Promise<any> {
    if (!this.formsExpired) {
      let formWindow: Window;
      const baseUrl = document.location.protocol + '//' + document.location.hostname + ':' + document.location.port + '/' + document.location.pathname;
      const loadingPage = baseUrl + '/assets/html-templates/loading.html';

      // formWindow = window.open('', '_blank');
      formWindow = window.open(loadingPage, '_blank');
      return new Promise<void>((resolve, reject) => {
        this.formsService.getAgencyFormAuthorization(this.agencyInfo.agencyId).pipe(
          take(1))
          .subscribe(authorizationToken => {
            const createdFormHref = this.agencyInfo.agencyId + '/openform?agencyformid=' + agencyForm.identifier +
              '&agentId=' + this.userData.user.userId +
              '&ticket=' + encodeURIComponent(authorizationToken.ticket) +
              '&requestId=' + authorizationToken.requestId;
            formWindow.location.href = this.apiService.formAppUrl(createdFormHref);
            resolve();
            this.formCreated = true;
          },
            err => {
              formWindow.location.href = baseUrl + '/assets/html-templates/loading-error.html';
              reject(err);
            }
          );
      });
    }
  }

  private getAgentData(): void {
    this.subscriptionAgency && this.subscriptionAgency.unsubscribe();
    this.subscriptionAgency = this.agencyUserService.userData$.subscribe(agency => {
      if (agency && agency.user) {
        this.agencyInfo = agency;
      }
    });
  }

  public get isDevEnv(): boolean {
    return isDevMode();
  }

  private subscribeQuote(): void {
    if (this.currentPageService.routeData && this.currentPageService.routeData.additionalParam) {
      const subscriptionQuote = this.storageService.getStorageData('selectedQuote')
        .subscribe((res: Quote) => {
          if (res && res.resourceId) {
            this.quoteId = res.resourceId;
            this.quoteIdentifier = res.quoteIdentifier;
            this.quoteLob = res.lob;
          }
        });
    }
  }

  public getAgencyForms(showResultsFromFirstPage: boolean = false): void {
    let formNumber, lob, agentId, locationId, dateType, startDate, endDate, name;
    this.searchQuery = false;

    if (showResultsFromFirstPage) {
      this.paginationResultShowFrom = 0;
      this.paginationCurrentPage = 1;
    }

    name = this.nameSearchQuery ? this.nameSearchQuery : '';
    this.searchQuery = this.nameSearchQuery ? true : false;

    formNumber = this.filterFormsSelectedOption.id;
    if (formNumber === 'all' || formNumber === 'All') {
      formNumber = '';
    } else {
      this.searchQuery = true;
    }

    lob = this.filterLobSelectedOption.id;
    if (lob === 'all' || lob === 'All') {
      lob = '';
    } else {
      this.searchQuery = true;
    }

    agentId = this.filterAgentsSelectedOption.id;
    if (agentId === 'all' || agentId === 'All') {
      agentId = '';
    } else {
      this.searchQuery = true;
    }

    locationId = this.filterLocationsSelectedOption.id;
    if (locationId === 'all' || locationId === 'All') {
      locationId = '';
    } else {
      this.searchQuery = true;
    }

    if (this.filterDates!=undefined) {
      dateType = this.filterDates.filterDatesRangeSelectedOption.id ?
        this.filterDates.filterDatesRangeSelectedOption.id.replace(/\b\w/g, l => l.toUpperCase()) : '';
    }

    if (dateType === 'all' || dateType === 'All') {
      dateType = '';
    } else {
      //  this.searchQuery = true;
    }

    startDate = this.dateRange.start ? format(new Date(this.dateRange.start), 'yyyy-MM-dd') : '';
    endDate = this.dateRange.end ? format(new Date(this.dateRange.end), 'yyyy-MM-dd') : '';

    this.searchQuery = startDate ? true : this.searchQuery;
    this.searchQuery = endDate ? true : this.searchQuery;



    // If request data hasn't changed, do not send request
    const currentRequestDataString = this.paginationResultShowFrom.toString()
      + this.paginationResultLimit.toString()
      + formNumber
      + name
      + agentId
      + lob
      + locationId
      + dateType
      + startDate
      + endDate
      + this.nameType
      + this.clientType;

    if (this.getAgencyFormsRequestDataString === currentRequestDataString && !(this.isRefreshRequest)) {
      return;
    }

    this.isRefreshRequest = false;

    this.getAgencyFormsRequestDataString = currentRequestDataString;

    if (this.getAgencyFormsSubscription) {
      this.overlayLoaderService.hideLoader();
      this.getAgencyFormsSubscription.unsubscribe();
    }

    this.overlayLoaderService.showLoader('Loading Results...');

    this.getAgencyFormsSubscription = this.formsService.getAgencyForms(
      this.paginationResultShowFrom.toString(),
      this.paginationResultLimit.toString(),
      name,
      agentId,
      lob,
      locationId,
      dateType,
      startDate,
      endDate,
      formNumber,
      '',
      this.quoteId,
      this.nameType,
      this.clientType
    ).pipe(take(1)).subscribe(agencyForms => {
      if (this.filterFormsOptions.length < 2) { this.getForms(); }
      if (this.filterAgentsOptions.length < 2) { this.getAgents(); }
      if (this.filterLocationsOptions.length < 2) { this.getLocations(); }

      console.log('FORMS::::', agencyForms);

      this.dataInitiated = true;
      this.arrAgencyFormsAll = agencyForms.items;
      this.arrAgencyFormsFiltered = agencyForms.items;
      this.paginationResultsCount = agencyForms.size;
      this.overlayLoaderService.hideLoader();
    }, reject => {
      this.overlayLoaderService.hideLoader();
    });
  }

  // public deleteAgencyForm(modalRef) {
  //  console.log('Delete:', this.choosenAgencyForm.name);
  //  this.deleteByName(this.choosenAgencyForm.name);
  //  modalRef.closeModalbox();
  // }

  // private deleteByName(name:string):void {
  //  let index = this.agencyForms.findIndex((el)=> el.name === name);

  //  if(index != -1) {
  //    this.agencyForms.splice(index, 1);
  //  }
  // }

  private getLocations() {
    const locations = '';
    if (locations.length) {
      return this.parseLocationsData(locations);
    } else {
      if (this.agencyId) {
        this.getRatingLocationsServiceSubscription = this.subsService.getAgencyLocations(this.agencyId).subscribe(data => {
          this.parseLocationsData(data.locations);
        });
      } else {
        this.agencyUserServiceSubscription = this.agencyUserService.userData$.subscribe(agent => {
          this.agencyId = agent.agencyId;
          this.getRatingLobsServiceSubscription = this.subsService.getAgencyLocations(this.agencyId).subscribe(data => {
            this.parseLocationsData(data.locations);
          });
        });
      }
    }
  }

  private parseLocationsData(locations) {
    let id, address, city, state;
    const tmpArr: FilterOption[] = [{ id: 'all', text: 'All' }];
    locations.map(location => {
      id = location.id ? location.id : false;
      address = location.address1 ? location.address1 + ', ' : '';
      city = location.city ? location.city + ', ' : '';
      state = location.state ? location.state : '';

      if (id) {
        tmpArr.push({ 'id': location.id, 'text': address + city + state });
      }
    });
    this.filterLocationsOptions = tmpArr;
    return tmpArr;
  }

  private getForms() { // : FilterOption[] {
    const tmpArr: FilterOption[] = [{ id: 'all', text: 'All Forms' }];
    this.formServiceSubscription = this.formsService.getForms().subscribe(
      forms => {
        const arrFormsAll = forms.items;
        arrFormsAll.map(form => {
          tmpArr.push({ 'id': form.number, 'text': form.name });
        });
        this.filterFormsOptions = tmpArr;
        return tmpArr;
      });

    //     this.filterFormsOptions = tmpArr;
    //     return tmpArr;
  }

  private getAgents(): FilterOption[] {
    const agents = this.storageGlobalService.takeSubs('users');
    if (agents.length) {
      return this.parseAgentsData(agents);
    } else {
      this.agencyUserServiceSubscription = this.agencyUserService.userData$.subscribe(agent => {
        this.agencyId = agent.agencyId;

        this.getAgencyUsersSubscription = this.subsService.getAgencyUsers(agent.agencyId).subscribe(agents => {
          this.storageGlobalService.setSubs('users', agents.items);
          return this.parseAgentsData(agents.items);
        });
      });
    }
  }

  private parseAgentsData(agents) {
    const tmpArr: FilterOption[] = [{ id: 'all', text: 'All Agents' }];
    agents.map(agent => {
      tmpArr.push({ 'id': agent.userId, 'text': agent.firstName + ' ' + agent.lastName });
    });
    this.filterAgentsOptions = tmpArr;
    return tmpArr;
  }

 public parseLastModifiedDate(date: string): string {
  return date ? format(new Date(date), 'MMM d, yyyy') : '';
}

  private setFilterFormsOptions(): void {
    this.filterFormsSelectedOption = this.filterFormsOptions[0];
  }

  private setFilterAgentsOptions(): void {
    this.filterAgentsSelectedOption = this.filterAgentsOptions[0];
  }

  // private setFilterLobOptions(): void {
  //   if (this.currentPageService.routeData && this.currentPageService.routeData.additionalParam) {
  //     const id = this.currentPageService.routeData.additionalParam;
  //     this.filterLobOptions.forEach(element => {
  //       if (element.id === id) {
  //         this.filterLobSelectedOption = element;
  //       }
  //     });
  //   } else {
  //     this.filterLobSelectedOption = this.filterLobOptions[0];
  //   }
  // }

  private setFilterLocationsOptions(): void {
    this.filterLocationsSelectedOption = this.filterLocationsOptions[0];
  }


  // Pagination
  // ------------------------------------------------------------------------------

  public paginationPageChange(data) {
    if (this.dataInitiated && this.paginationResultShowFrom !== data.startAt) {
      this.paginationCurrentPage = data.pageNumber;
      this.paginationResultShowFrom = data.startAt;
      this.getAgencyForms();
    }
  }

  public rmvPaginationPageChange(data) {
    if (this.dataInitiated && this.rmvPaginationResultShowFrom !== data.startAt) {
      this.rmvPaginationCurrentPage = data.pageNumber;
      this.rmvPaginationResultShowFrom = data.startAt;
      this.getRmvTransactions();
    }
  }


  private paginationSetResultLimit(intLimit: any) {
    this.paginationResultLimit = parseInt(intLimit, 10);
  }

  public onResultLimitChange($ev): void {
    setTimeout(() => {
      this.paginationSetResultLimit($ev.limit);
      this.getAgencyForms();
    });
  }

  private getUser() {
    this.agencyUserService.userData$.pipe(
      first())
      .subscribe(data => this.userData = data);
  }


  // Filters on the Page
  // ------------------------------------------------------------------------------
  private onLobChange(option: FilterOption): void {
    this.filterLobSelectedOption = option;
  }
  private onFormChange(option: FilterOption): void {
    this.filterFormsSelectedOption = option;
  }
  private onAgentChange(option: FilterOption): void {
    this.filterAgentsSelectedOption = option;
  }
  private onLocationChange(option: FilterOption): void {
    this.filterLocationsSelectedOption = option;
  }

  private filterDataChangeUpdate($ev): void {
    if (this.dataToChange && $ev.selectedOption.id !== this.dataToChange.selectedOption.id) {
      this.dataToChange = $ev;
      switch ($ev.filterId) {
        case 'filter_forms':
          this.onFormChange($ev.selectedOption);
          break;
        case 'filter_LOB':
          this.onLobChange($ev.selectedOption);
          break;
        case 'filter_agents':
          this.onAgentChange($ev.selectedOption);
          break;
        case 'filter_locations':
          this.onLocationChange($ev.selectedOption);
          break;
      }
      this.getAgencyForms(true);
    } else {
      this.dataToChange = $ev;
    }
  }

  public onFilterDataChange($ev): void {
    this.filterDataChangeUpdate($ev);
  }


  // Helpers
  // ------------------------------------------------------------------------------
  private updateFiltersLabels(): void {
    this.filterFormsLabel = this.filterFormsSelectedOption.text;
    this.filterLobLabel = this.filterLobSelectedOption.text;
    this.filterAgentsLabel = this.filterAgentsSelectedOption.text;
    this.filterLocationLabel = this.filterLocationsSelectedOption.text;
    this.filterDates.filterDatesLabel = this.filterDates.formatFilterLabelDateTypes();
  }

  resetSearch() {
    this.filterDates.prepareResetSearch();
    this.dateRange.start = null;
    this.dateRange.end = null;
    this.nameSearchQuery = null;
    this.filterFormsSelectedOption = this.filterFormsOptions[0];
    this.filterFormsLabel = this.filterFormsSelectedOption.text;
    this.filterAgentsSelectedOption = this.filterAgentsOptions[0];
    this.filterAgentsLabel = this.filterAgentsSelectedOption.text;
    this.getAgencyForms();
  }

  getFormsFromFilteredDates() {
    this.dateRange = this.filterDates.dateRange;

    let allowFiltering = this.filterDates.isFilteringAllowed();

    if (allowFiltering) {
      this.getAgencyForms(true);
    }

      this.FilterDates.tooltip.close();
  }

  public setMsg(data: UserSubscriptionRatingEmitDataI): void {
    if (data && data.messages) {
      this.subscriptionMessages = data.messages;
      this.proceed = data.proceed;

    }
  }

  getRmvTransactions(showResultsFromFirstPage = false) {
    if (showResultsFromFirstPage) {
      this.rmvPaginationResultShowFrom = 0;
      this.rmvPaginationCurrentPage = 1;
    }
    if (this.quoteIdentifier) {
      // const criteria = this.searchQuery !== '' && this.searchQuery !== undefined ? `?${this.searchType}=${this.searchQuery}` : '' ;
      const criteria = `?workflowType=RmvServices&quoteId=${this.quoteIdentifier}`;
      // criteria = `?offset=${this.paginationResultShowFrom}`;
      this.rmvService.getSavedRmvList(criteria, this.paginationResultShowFrom).pipe(take(1)).subscribe(x => {
        this.dataInitiated = true;
        this.rmvList = x.items.filter(res => res.quoteId !== 0); this.rmvSize = x.size;

      });
    }

  }

  deleteTransaction(id) {
    this.rmvService.deleteRmvTransactions([id]).subscribe(
      x => {
        this.rmvList = this.rmvList.filter(item => item.rmvServicesId !== id);
        this.rmvSize = this.rmvSize--;
      }
    );
  }

}

