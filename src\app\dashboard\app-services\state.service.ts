export const STATES =
  ['', 'AK', 'AL', 'AR', 'AZ', 'CA', 'CO', 'CT', 'DC', 'DE', 'FL', 'FR','GA', 'HI', 'IA', 'ID', 'IL', 'IN', 'KS', 'KY', 'LA', 'MA', 'MD', 'ME', 'MI', 'MN', 'MO', 'MS', 'MT', 'NC', 'ND', 'NE', 'NH', 'NJ', 'NM', 'NV', 'NY', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC', 'SD', 'TN', 'TX', 'UT', 'VA', 'VT', 'WA', 'WI', 'WV', 'WY']
  ;

export const PREFILL_STATES = [
  { id: 'MA', text: 'MA - Massachusetts' },
  { id: 'AK', text: 'AK - Alaska' },
  { id: 'AL', text: 'AL - Alabama' },
  { id: 'AR', text: 'AR - Arkansas' },
  { id: 'AS', text: 'AS - American Samoa' },
  { id: 'AZ', text: 'AZ - Arizona' },
  { id: 'CA', text: 'CA - California' },
  { id: 'CO', text: 'CO - Colorado' },
  { id: 'CT', text: 'CT - Connecticut' },
  { id: 'DC', text: 'DC - District of Columbia' },
  { id: 'DE', text: 'DE - Delaware' },
  { id: 'FL', text: 'FL - Florida' },
  { id: 'GA', text: 'GA - Georgia' },
  { id: 'HI', text: 'HI - Hawaii' },
  { id: 'IA', text: 'IA - Iowa' },
  { id: 'ID', text: 'ID - Idaho' },
  { id: 'IL', text: 'IL - Illinois' },
  { id: 'IN', text: 'IN - Indiana' },
  { id: 'KS', text: 'KS - Kansas' },
  { id: 'KY', text: 'KY - Kentucky' },
  { id: 'LA', text: 'LA - Louisiana' },
  { id: 'MD', text: 'MD - Maryland' },
  { id: 'ME', text: 'ME - Maine' },
  { id: 'MI', text: 'MI - Michigan' },
  { id: 'MN', text: 'MN - Minnesota' },
  { id: 'MO', text: 'MO - Missouri' },
  { id: 'MS', text: 'MS - Mississippi' },
  { id: 'MT', text: 'MT - Montana' },
  { id: 'NC', text: 'NC - North Carolina' },
  { id: 'ND', text: 'ND - North Dakota' },
  { id: 'NE', text: 'NE - Nebraska' },
  { id: 'NH', text: 'NH - New Hampshire' },
  { id: 'NJ', text: 'NJ - New Jersey' },
  { id: 'NM', text: 'NM - New Mexico' },
  { id: 'NV', text: 'NV - Nevada' },
  { id: 'NY', text: 'NY - New York' },
  { id: 'OH', text: 'OH - Ohio' },
  { id: 'OK', text: 'OK - Oklahoma' },
  { id: 'OR', text: 'OR - Oregon' },
  { id: 'PA', text: 'PA - Pennsylvania' },
  { id: 'RI', text: 'RI - Rhode Island' },
  { id: 'SC', text: 'SC - South Carolina' },
  { id: 'SD', text: 'SD - South Dakota' },
  { id: 'TN', text: 'TN - Tennessee' },
  { id: 'TX', text: 'TX - Texas' },
  { id: 'UT', text: 'UT - Utah' },
  { id: 'VA', text: 'VA - Virginia' },
  { id: 'VI', text: 'VI - Virgin Islands' },
  { id: 'VT', text: 'VT - Vermont' },
  { id: 'WA', text: 'WA - Washington' },
  { id: 'WI', text: 'WI - Wisconsin' },
  { id: 'WV', text: 'WV - West Virginia' },
  { id: 'WY', text: 'WY - Wyoming' },
  { id: 'AB', text: 'AB - Alberta' },
  { id: 'AG', text: 'AG - Aguascalientes' },
  { id: 'BA', text: 'BA - Baja California' },
  { id: 'BC', text: 'BC - British Columbia' },
  { id: 'BJ', text: 'BJ - Baja California Sur' },
  { id: 'BK', text: 'BK - Baker Island' },
  { id: 'CE', text: 'CE - Campeche' },
  { id: 'CG', text: 'CG - Caroline Island' },
  { id: 'CH', text: 'CH - Chihuahua' },
  { id: 'CI', text: 'CI - Chiapas' },
  { id: 'CL', text: 'CL - Colima' },
  { id: 'CN', text: 'CN - Canada' },
  { id: 'CU', text: 'CU - Coahuila de Zaragoza' },
  { id: 'D2', text: 'D2 - U.S. Department of Justice' },
  { id: 'DF', text: 'DF - Distrito Federal Mexico' },
  { id: 'DO', text: 'DO - Durango' },
  { id: 'DS', text: 'DS - U.S. Department of State' },
  { id: 'DT', text: 'DT - Department of Transportation' },
  { id: 'EM', text: 'EM - Estado de Mexico' },
  { id: 'FM', text: 'FM - Federal States of Micronesia' },
  { id: 'FN', text: 'FN - France' },
  { id: 'GE', text: 'GE - Germany' },
  { id: 'GM', text: 'GM - Guam' },
  { id: 'GR', text: 'GR - Guerrero' },
  { id: 'GU', text: 'GU - Guanajuato' },
  { id: 'HL', text: 'HL - Hidalgo' },
  { id: 'HO', text: 'HO - Howland Island' },
  { id: 'JI', text: 'JI - Johnston Atoll' },
  { id: 'JL', text: 'JL - Jalisco' },
  { id: 'JR', text: 'JR - Jarvis Island' },
  { id: 'KI', text: 'KI - Kingman Reef' },
  { id: 'KR', text: 'KR - South Korea' },
  { id: 'MB', text: 'MB - Manitoba' },
  { id: 'MC', text: 'MC - Michoacan de Ocampo' },
  { id: 'MH', text: 'MH - Marshal Islands' },
  { id: 'MK', text: 'MH - Mariana Island' },
  { id: 'MP', text: 'MP - Northern Mariana Islands' },
  { id: 'MR', text: 'MR - Morelos' },
  { id: 'MX', text: 'MX - Mexico (United Mexican States)' },
  { id: 'NA', text: 'NA - Nayarit' },
  { id: 'NB', text: 'NB - New Brunswick' },
  { id: 'NF', text: 'NF - Newfoundland and Labrador' },
  { id: 'NL', text: 'NL - Nuevo Leon' },
  { id: 'NS', text: 'NS - Nova Scotia' },
  { id: 'NT', text: 'NT - Northwest Territory' },
  { id: 'NU', text: 'NU - Nunavut' },
  { id: 'OA', text: 'OA - Oaxaca' },
  { id: 'ON', text: 'ON - Ontario' },
  { id: 'OT', text: 'OT - Other' },
  { id: 'PB', text: 'PB - Puebla' },
  { id: 'PE', text: 'PE - Prince Edward Island' },
  { id: 'PL', text: 'PL - Palmyra Atoll' },
  { id: 'PR', text: 'PR - Puerto Rico' },
  { id: 'PW', text: 'PW - Palau' },
  { id: 'PZ', text: 'PZ - Panamanian Canal Zone' },
  { id: 'QC', text: 'QC - Quebec' },
  { id: 'QM', text: 'QM -	Midway Islands' },
  { id: 'QR', text: 'QR - Quintana Roo' },
  { id: 'QU', text: 'QU - Queretaro de Arteaga' },
  { id: 'SI', text: 'SI - Sinaloa' },
  { id: 'SK', text: 'SK - Saskatchewan' },
  { id: 'SL', text: 'SL - San Luis Potosi' },
  { id: 'SO', text: 'SO - Sonora' },
  { id: 'TA', text: 'TA - Tamaulipas' },
  { id: 'TB', text: 'TB - Tabasco' },
  { id: 'TL', text: 'TL - Tlaxcala' },
  { id: 'TW', text: 'TW - Taiwan' },
  { id: 'VC', text: 'VC - Veracruz' },
  { id: 'VL', text: 'VL - Navassa Island' },
  { id: 'WK', text: 'WK - Wake Island' },
  { id: 'YT', text: 'YT - Yukon Territory' },
  { id: 'YU', text: 'YU - Yucatán' },

];
