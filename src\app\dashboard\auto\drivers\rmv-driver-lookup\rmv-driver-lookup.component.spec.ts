import { ComponentFixture, TestBed, async } from '@angular/core/testing';

import { CalendarComponent } from 'app/shared/components/calendar/calendar.component';
import { DatepickerInputComponent } from 'app/shared/components/datepicker-input/datepicker-input.component';
import { DriversService } from 'app/dashboard/app-services/drivers.service';
import { FormsModule } from '@angular/forms';
import { InputMaskDirective } from './../../../../shared/directives/input-mask.directive';
import { ModalboxComponent } from 'app/shared/components/modalbox/modalbox.component';

import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { OverlayRouteService } from 'app/overlay/services/overlay-route.service';
import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { RmvDriverLookupComponent } from './rmv-driver-lookup.component';
import { RmvService } from 'app/dashboard/app-services/rmv.service';
import { RouterTestingModule } from '@angular/router/testing';
import { SmPopupsModule } from 'app/shared/modules/sm-popups/sm-popups.module';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { StorageService } from 'app/shared/services/storage-new.service';


describe('RmvDriverLookupComponent', () => {
  let component: RmvDriverLookupComponent;
  let fixture: ComponentFixture<RmvDriverLookupComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [  FormsModule, SmPopupsModule, RouterTestingModule ],
      declarations: [ RmvDriverLookupComponent, DatepickerInputComponent, CalendarComponent, InputMaskDirective, ModalboxComponent ],
      providers: [
        StorageService,
        RmvService,
        OverlayLoaderService,
        OverlayRouteService,
        DriversService,
        QuotesService,
        SpecsService
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(RmvDriverLookupComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
