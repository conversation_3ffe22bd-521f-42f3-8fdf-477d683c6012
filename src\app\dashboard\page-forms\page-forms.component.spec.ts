import { StubConfirmboxComponent } from 'testing/stubs/components/confirmbox.component';
import { StubModalboxComponent } from 'testing/stubs/components/modalbox.component';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { RouterTestingModule } from '@angular/router/testing';

import { StubAutocompleteComponent } from 'testing/stubs/components/autocomplete.component';
import { StubDatepickerInputComponent } from 'testing/stubs/components/datepicker-input.component';
import { StubFilterComponent } from 'testing/stubs/components/filter.component';
import { StubPaginationComponent } from 'testing/stubs/components/pagination.component';
import { StubResultsLimiterComponent } from 'testing/stubs/components/results-limiter.component';
import { StubOverlayLoaderServiceProvider } from 'testing/stubs/services/overlay-loader.service.provider';

import { PageFormsComponent } from './page-forms.component';
import { StubDetectSystemDirective } from 'testing/stubs/directives/detect-system.directive';
import { StubHintsAndWarningsLinkComponent } from 'testing/stubs/components/hints-and-warnings-link.component';
import { StubPerfectScrollbarComponent } from 'testing/stubs/components/perfect-scrollbar.component';
import { StubFormsServiceProvider } from 'testing/stubs/services/forms.service.provider';
import { StubAgencyUserServiceProvider } from 'testing/stubs/services/agency-user.service.provider';


describe('PageFormsComponent', () => {
  let component: PageFormsComponent;
  let fixture: ComponentFixture<PageFormsComponent>;

  beforeEach(async(() => {
      TestBed.configureTestingModule({
        imports: [FormsModule, RouterTestingModule],
        declarations: [
          PageFormsComponent,
          StubFilterComponent,
          StubAutocompleteComponent,
          StubDatepickerInputComponent,
          StubPaginationComponent,
          StubResultsLimiterComponent,
          StubModalboxComponent,
          StubConfirmboxComponent,
          StubDetectSystemDirective,
          StubHintsAndWarningsLinkComponent,
          StubPerfectScrollbarComponent,
          StubResultsLimiterComponent
        ],
        providers: [
          StubOverlayLoaderServiceProvider,
          StubFormsServiceProvider,
          StubAgencyUserServiceProvider
        ]
      })
          .compileComponents();
  }));

  beforeEach(() => {
      fixture = TestBed.createComponent(PageFormsComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
  });

  it('should create', () => {
      expect(component).toBeTruthy();
  });
});
