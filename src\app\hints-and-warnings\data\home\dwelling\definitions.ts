import { parseISO, differenceInYears } from 'date-fns';
import { AdditionalDataHomeDwelling } from './../../../model/warnings';
import { Quote } from 'app/app-model/quote';
import {
  WARNING_GROUPS,
  WarningDefinitionI,
  AdditionalDataI
} from 'app/hints-and-warnings/model/warnings';
import { Dwelling, DwellingLocation } from 'app/app-model/dwelling';
import { Validate } from 'app/hints-and-warnings/validators';
import { CoverageItemParsed } from 'app/app-model/coverage';


function requiredField(value): boolean {
  return Validate.isEmptyValue(value);
}

function validateZipCode(zip): boolean {
  if (zip === '' || zip === null) {
    return false;
  }

  const zipPattern = /^\d{5}(?:-?\d{4})?$/;
  return !zipPattern.test(zip);
}

function generateViewUrl(fullObj: DwellingLocation | Dwelling): string {
  return '/dashboard/home/<USER>/' + fullObj.quoteSessionId + '/dwelling';
}

/**
 * Validation for Dwelling Data
 * For Model: Dwelling Location
 */

const dwellingAddress: WarningDefinitionI = {
  id: 'address1',
  deepId: 'address1',
  viewUri: generateViewUrl,
  viewFieldId: 'dwellingAddress',
  warnings: [
    {
      label: (value, fullObj) => 'Required Dwelling Address.',
      condition: requiredField,
      group: WARNING_GROUPS.general,
      carriers: []
    }
  ]
};

const dwellingCity: WarningDefinitionI = {
  id: 'city',
  deepId: 'city',
  viewUri: generateViewUrl,
  viewFieldId: 'dwellingCity',
  warnings: [
    {
      label: (value, fullObj) => 'Required Dwelling City.',
      condition: requiredField,
      group: WARNING_GROUPS.general,
      carriers: []
    }
  ]
};

const dwellingState: WarningDefinitionI = {
  id: 'state',
  deepId: 'state',
  viewUri: generateViewUrl,
  viewFieldId: 'dwellingState',
  warnings: [
    {
      label: (value, fullObj) => 'Required Dwelling State.',
      condition: requiredField,
      group: WARNING_GROUPS.general,
      carriers: []
    }
  ]
};

const dwellingZip: WarningDefinitionI = {
  id: 'zip',
  deepId: 'zip',
  viewUri: generateViewUrl,
  viewFieldId: 'dwellingZip',
  warnings: [
    {
      label: (value, fullObj) => 'Required Dwelling Zip.',
      condition: requiredField,
      group: WARNING_GROUPS.general,
      carriers: []
    },
    {
      label: (value, fullObj) => 'Zip Code is not Valid.',
      condition: validateZipCode,
      group: WARNING_GROUPS.general,
      carriers: []
    }
  ]
};

const dwellingMFDCarriers: string[] = ['319'];
const dwellingFireDistrct: WarningDefinitionI = {
  id: 'fireDistrict',
  deepId: 'fireDistrict',
  viewUri: generateViewUrl,
  viewFieldId: 'fireDistricts',
  warnings: [
    {
      label: (value, fullObj) => 'Required Fire District to be set.',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        const valueToCheck = value === 'Unassigned' ? null : value;
        const dwellingMFDCarriersValues = additionalData.quoteSelectedPlans
          .filter(x => x.ratingFlow.toUpperCase() === 'MFD')
          .map(y => y.ratingPlanId);
        for (let i = 0; i < dwellingMFDCarriersValues.length; i++) {
          dwellingMFDCarriers.push(dwellingMFDCarriersValues[i]);
        }
        return Validate.isRequiredForSelectedPlansIfEmptyValue(
          valueToCheck,
          dwellingMFDCarriers,
          additionalData.quoteSelectedPlansIds
        );
      },
      group: WARNING_GROUPS.carrier,
      carriers: dwellingMFDCarriers
    }
  ]
};

/*******************************************************************************
 * For Model: Dwelling
 ******************************************************************************/

// Rule 1:: (https://bostonsoftware.atlassian.net/browse/SPRC-365) (https://app.smartsheet.com/b/home?lx=wrx37F1hxf1XQrMBnVN7QA)
// For Plans: Barnstable County Mutual  - Preferred [94], Barnstable County Mutual - Protector Plus [69]
// Condition:
// H03 form type - Minimum Year Built accepted >=1987
// H05 form type - Minimum Year Built accepted >=1987
const validateConstructionYearRule1FormTypes = ['HO3', 'HO5'];
const validateConstructionYearRule1Carriers = ['94', '69'];
function validateConstructionYearRule1(
  dwelling: Dwelling,
  selectedPlansIds: string[],
  selectedFormTypes: string[]
): boolean {
  const requiredForFormTypes = validateConstructionYearRule1FormTypes;
  const requiredForPlans = validateConstructionYearRule1Carriers;
  let isInvalid = false;

  const allowValidationForFormTypes = requiredForFormTypes.some(
    item => selectedFormTypes.indexOf(item) !== -1
  );
  const allowValidationForSelectedPlans = Validate.isRequiredForSelectedPlans(
    requiredForPlans,
    selectedPlansIds
  );

  if (allowValidationForFormTypes && allowValidationForSelectedPlans) {
    if (dwelling.constructionYear) {
      const tmpConstructionYear = Number(dwelling.constructionYear);
      isInvalid = !isNaN(tmpConstructionYear) && tmpConstructionYear < 1987;
      // console.log('The Year Built can not be lower than 1987');
    }
  }

  return isInvalid;
}

// Rule 2:: (https://bostonsoftware.atlassian.net/browse/SPRC-365) (https://app.smartsheet.com/b/home?lx=wrx37F1hxf1XQrMBnVN7QA)
// For Plans: Barnstable County Mutual  - Preferred [94], Barnstable County Mutual - Protector Plus [69]
// Condition:
// Construction Year <=30 Years from Quote effective date
const validateConstructionYearRule2Carriers = ['94', '69'];
function validateConstructionYearRule2(
  dwelling: Dwelling,
  selectedPlansIds: string[],
  selectedFormTypes: string[],
  quote: Quote
): boolean {
  const requiredForPlans = validateConstructionYearRule2Carriers;
  let isInvalid = false;

  const allowValidationForSelectedPlans = Validate.isRequiredForSelectedPlans(
    requiredForPlans,
    selectedPlansIds
  );
  if (allowValidationForSelectedPlans) {
  const tmpDifference = differenceInYears(
  parseISO(this.quote.effectiveDate),
  new Date(dwelling.constructionYear, 0, 1)
);
    if (tmpDifference > 30) {
      isInvalid = true;
      // console.log('The Year Built difference between Quote Effective Date can not be more than 30 years');
    }
  }

  return isInvalid;
}

// Rule 3:: (https://bostonsoftware.atlassian.net/browse/SPRC-365) (https://app.smartsheet.com/b/home?lx=wrx37F1hxf1XQrMBnVN7QA)
// For Plans: Tower National Insurance [77]
// Condition:
// Construction Year > 47 Yrs not accepted
const validateConstructionYearRule3Carriers = ['77'];
function validateConstructionYearRule3(
  dwelling: Dwelling,
  selectedPlansIds: string[],
  selectedFormTypes: string[],
  quote: Quote
): boolean {
  const requiredForPlans = validateConstructionYearRule3Carriers;
  let isInvalid = false;

  const allowValidationForSelectedPlans = Validate.isRequiredForSelectedPlans(
    requiredForPlans,
    selectedPlansIds
  );
  if (allowValidationForSelectedPlans) {
  const tmpDifference = differenceInYears(
  new Date(),
  new Date(dwelling.constructionYear, 0, 1)
);
    if (tmpDifference > 47) {
      isInvalid = true;
      // console.log('The Year Built can not be older than 47 years from now.')
    }
  }

  return isInvalid;
}

// Rule 4:: (https://bostonsoftware.atlassian.net/browse/SPRC-365) (https://app.smartsheet.com/b/home?lx=wrx37F1hxf1XQrMBnVN7QA)
// For Plans: Tower National Preferred [78]
// Condition:
// Construction Year > 47 Yrs not accepted
const validateConstructionYearRule4Carriers = ['78'];
function validateConstructionYearRule4(
  dwelling: Dwelling,
  selectedPlansIds: string[],
  selectedFormTypes: string[],
  quote: Quote
): boolean {
  const requiredForPlans = validateConstructionYearRule4Carriers;
  let isInvalid = false;

  const allowValidationForSelectedPlans = Validate.isRequiredForSelectedPlans(
    requiredForPlans,
    selectedPlansIds
  );
  if (allowValidationForSelectedPlans) {
   const tmpDifference = differenceInYears(
  new Date(),
  new Date(dwelling.constructionYear, 0, 1)
);
    if (tmpDifference > 70) {
      isInvalid = true;
      // console.log('The Year Built can not be older than 70 years from now.')
    }
  }

  return isInvalid;
}

// Rule 5:: (https://bostonsoftware.atlassian.net/browse/SPRC-365) (https://app.smartsheet.com/b/home?lx=wrx37F1hxf1XQrMBnVN7QA)
// For Plans: Yankee Risk solutions - Preferred [99]
// Condition:
// Dwell age >30 years not accepted
const validateConstructionYearRule5Carriers = ['99'];
function validateConstructionYearRule5(
  dwelling: Dwelling,
  selectedPlansIds: string[],
  selectedFormTypes: string[],
  quote: Quote
): boolean {
  const requiredForPlans = validateConstructionYearRule5Carriers;
  let isInvalid = false;

  const allowValidationForSelectedPlans = Validate.isRequiredForSelectedPlans(
    requiredForPlans,
    selectedPlansIds
  );
  if (allowValidationForSelectedPlans) {
  const tmpDifference = differenceInYears(
  parseISO(this.quote.effectiveDate),
  new Date(dwelling.constructionYear, 0, 1)
);
    if (tmpDifference > 30) {
      isInvalid = true;
      // console.log('The Year Built can not be older than 30 years from now.')
    }
  }

  return isInvalid;
}

const dwellingConstructionMaterial: WarningDefinitionI = {
  id: 'constructionMaterialTypeCode',
  deepId: 'constructionMaterialTypeCode',
  viewUri: generateViewUrl,
  viewFieldId: 'constructionMaterial',
  warnings: [
    {
      label: (value, fullObj) => 'Required Dwelling Construction Material.',
      // condition: requiredField,
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        const valueToCheck: string =
          !value || value === 'Unassigned' ? '' : value;
        return requiredField(valueToCheck);
      },
      group: WARNING_GROUPS.general,
      carriers: []
    }
  ]


};

const dwellingConstructionYear: WarningDefinitionI = {
  id: 'constructionYear',
  deepId: 'constructionYear',
  viewUri: generateViewUrl,
  viewFieldId: 'constructionYear',
  warnings: [
    {
      label: (value, fullObj) => 'Required Dwelling Construction Year.',
      condition: requiredField,
      group: WARNING_GROUPS.general,
      carriers: []
    },
    // {
    //   label: (value, fullObj) => 'Dwelling is too old for this form type.',
    //   condition: (value, fullObj: Dwelling, additionalData: AdditionalDataHomeDwelling) => {
    //     return validateConstructionYearRule1(fullObj, additionalData.quoteSelectedPlansIds, additionalData.quoteFormTypes)
    //   },
    //   group: WARNING_GROUPS.carrier,
    //   carriers: validateConstructionYearRule1Carriers
    // },
    {
      label: (value, fullObj) => 'Dwelling is too old for this form type.',
      condition: (
        value,
        fullObj: Dwelling,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        return validateConstructionYearRule2(
          fullObj,
          additionalData.quoteSelectedPlansIds,
          additionalData.quoteFormTypes,
          additionalData.quote
        );
      },
      group: WARNING_GROUPS.carrier,
      carriers: validateConstructionYearRule2Carriers
    },
    {
      label: (value, fullObj) => 'Dwelling is too old for this form type.',
      condition: (
        value,
        fullObj: Dwelling,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        return validateConstructionYearRule3(
          fullObj,
          additionalData.quoteSelectedPlansIds,
          additionalData.quoteFormTypes,
          additionalData.quote
        );
      },
      group: WARNING_GROUPS.carrier,
      carriers: validateConstructionYearRule3Carriers
    },
    {
      label: (value, fullObj) => 'Dwelling is too old for this form type.',
      condition: (
        value,
        fullObj: Dwelling,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        return validateConstructionYearRule4(
          fullObj,
          additionalData.quoteSelectedPlansIds,
          additionalData.quoteFormTypes,
          additionalData.quote
        );
      },
      group: WARNING_GROUPS.carrier,
      carriers: validateConstructionYearRule4Carriers
    },
    {
      label: (value, fullObj) => 'Dwelling is too old for this form type.',
      condition: (
        value,
        fullObj: Dwelling,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        return validateConstructionYearRule5(
          fullObj,
          additionalData.quoteSelectedPlansIds,
          additionalData.quoteFormTypes,
          additionalData.quote
        );
      },
      group: WARNING_GROUPS.carrier,
      carriers: validateConstructionYearRule5Carriers
    }
  ]
};

// Required for:
// National General, Safeco, Safety (Standard), Union Mutual of Vermont
const dwellingSquareFootageCarriers: string[] = ['173', '91', '93', '305', '306'];
// Required for: Travelers New (Plan ID 283)
const dwellingSquareFootageCarriers2: string[] = ['283', '119', '332'];
// Required for: N&D PRODUCT-2975
const dwellingSquareFootageCarriers3: string[] = ['314', '105'];
const dwellingSquareFootage: WarningDefinitionI = {
  id: 'livingSpaceArea',
  deepId: 'livingSpaceArea',
  viewUri: generateViewUrl,
  viewFieldId: 'livingSpaceArea',
  warnings: [
    {
      label: (value, fullObj) => 'Required Dwelling Square Footage.',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        return Validate.isRequiredForSelectedPlansIfEmptyValue(
          value,
          dwellingSquareFootageCarriers,
          additionalData.quoteSelectedPlansIds
        );
      },
      group: WARNING_GROUPS.carrier,
      carriers: dwellingSquareFootageCarriers
    },
    {
      label: (value, fullObj) => 'Required Dwelling Square Footage.',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        return (
          Validate.isRequiredForSelectedFormTypes(
            ['HO3', 'HO5'],
            additionalData.quoteFormTypes
          ) &&
          Validate.isRequiredForSelectedPlansIfEmptyValue(
            value,
            dwellingSquareFootageCarriers2,
            additionalData.quoteSelectedPlansIds
          )
        );
      },
      group: WARNING_GROUPS.carrier,
      carriers: dwellingSquareFootageCarriers2
    },
    {
      label: (value, fullObj) => 'Required Dwelling Square Footage.',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        return (
          Validate.isRequiredForSelectedFormTypes(
            ['HO3', 'HO5', 'HO6'],
            additionalData.quoteFormTypes
          ) &&
          Validate.isRequiredForSelectedPlansIfEmptyValue(
            value,
            dwellingSquareFootageCarriers3,
            additionalData.quoteSelectedPlansIds
          )
        );
      },
      group: WARNING_GROUPS.carrier,
      carriers: dwellingSquareFootageCarriers3
    }
  ]
};

const dwellingUtilizationTypeCode: WarningDefinitionI = {
  id: 'utilizationTypeCode',
  deepId: 'utilizationTypeCode',
  viewUri: generateViewUrl,
  viewFieldId: 'utilizationTypeCode',
  warnings: [
    {
      label: (value, fullObj) => 'Required Dwelling Occupancy.',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        const valueToCheck: string =
          !value || value === 'Unassigned' ? '' : value;
        return requiredField(valueToCheck);
      },
      group: WARNING_GROUPS.general,
      carriers: []
    }
  ]
};

// Required for:
// ASI, National General, Safeco, Union Mutual of Vermont, Hanover
const dwellingStoriesCountCarriers: string[] = [
  '105',
  '119',
  '173',
  '93',
  '282'
];
const dwellingStoriesCount: WarningDefinitionI = {
  id: 'storiesCount',
  deepId: 'storiesCount',
  viewUri: generateViewUrl,
  viewFieldId: 'storiesCount',
  warnings: [
    {
      label: (value, fullObj) => 'Required Dwelling Num. of Stories.',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        const valueToCheck: string =
          !value || value === 'Unassigned' ? '' : value;

        return Validate.isRequiredForSelectedPlansIfEmptyValue(
          // value,
          valueToCheck,
          dwellingStoriesCountCarriers,
          additionalData.quoteSelectedPlansIds
        );
      },
      group: WARNING_GROUPS.carrier,
      carriers: dwellingStoriesCountCarriers
    }
  ]
};

// Required for:
// Quincy Mutual Group, Safety (Standard)
const dwellingMortgageesCountCarriers: string[] = ['92', '91'];
const dwellingMortgageesCount: WarningDefinitionI = {
  id: 'mortgageeCount',
  deepId: 'mortgageeCount',
  viewUri: generateViewUrl,
  viewFieldId: 'mortgageeCount',
  warnings: [
    {
      label: (value, fullObj) => 'Required Dwelling Mortgagees.',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        return (
          Validate.isRequiredForSelectedPlansIfEmptyValue(
            value,
            dwellingMortgageesCountCarriers,
            additionalData.quoteSelectedPlansIds
          ) && additionalData.quoteFormTypes.indexOf('HO4') < 0
        );
      },
      group: WARNING_GROUPS.carrier,
      carriers: dwellingMortgageesCountCarriers
    }
  ]
};

// Required for:
// Quincy Mutual Group
// and Arbella if AdditionalSurcharge is set.
const dwellingPoolCarriers: string[] = ['92', '303', '316'];

// Required for:
// Arbella if AdditionalSurcharge is set.
const dwellingPoolArbellaCarriers: string[] = ['31', '32', '75', '76'];
// Arbella 291 (PRODUCT-1985 requirement) required if HO3 or HO5
const dwellingPoolArbellaH03H05Carrier = ['290'];
const dwellingPool: WarningDefinitionI = {
  id: 'poolOnPremisesInd',
  deepId: 'poolOnPremisesInd',
  viewUri: generateViewUrl,
  viewFieldId: 'poolOnPremisesInd',
  warnings: [
    {
      label: (value, fullObj) => 'Required Dwelling Pool to be set.',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        return (
          Validate.isRadioInputValueNotSet(value) &&
          Validate.isRequiredForSelectedPlans(
            dwellingPoolCarriers,
            additionalData.quoteSelectedPlansIds
          )
        );
      },
      group: WARNING_GROUPS.carrier,
      carriers: dwellingPoolCarriers
    },
    {
      label: (value, fullObj) =>
        'Required Dwelling Pool to be set when additional surcharge is selected.',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        return (
          Validate.isRadioInputValueNotSet(value) &&
          Validate.isRequiredForSelectedPlans(
            dwellingPoolArbellaCarriers,
            additionalData.quoteSelectedPlansIds
          ) &&
          validateAdditionalSurchargeSet(additionalData)
        );
      },
      group: WARNING_GROUPS.carrier,
      carriers: dwellingPoolArbellaCarriers
    },
    {
      label: (value, fullObj) =>
        'Required Dwelling Pool to be set when HO3 or HO5 is selected',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        return (
          anyElementIsInArray(['HO3', 'HO5'], additionalData.quoteFormTypes) &&
          Validate.isRequiredForSelectedPlansIfEmptyValue(
            value,
            dwellingPoolArbellaH03H05Carrier,
            additionalData.quoteSelectedPlansIds
          )
        );
      },
      group: WARNING_GROUPS.carrier,
      carriers: dwellingPoolArbellaH03H05Carrier
    }
  ]
};

function validateAdditionalSurchargeSet(
  additionalData: AdditionalDataHomeDwelling
) {
  const carrierOptions = additionalData.quoteCarrierOptions;

  const additionalOption = false;

  if (carrierOptions != null && carrierOptions.length > 0) {
    const currentopts: CoverageItemParsed[] = carrierOptions.filter(
      opt => opt.coverageCode === 'BSC-HOME-022808'
    );

    if (currentopts && currentopts.length > 0) {
      if (
        currentopts[0].currentValue === 'Yes' ||
        currentopts[0].currentValue === 'No'
      ) {
        return true;
      }
    }
  }
  return false;
}

const dwellingTownhouseUnits: WarningDefinitionI = {
  id: 'townhouseUnitsCount',
  deepId: 'townhouseUnitsCount',
  viewUri: generateViewUrl,
  viewFieldId: 'townhouseUnitsCount',
  warnings: [
    {
      label: (value, fullObj) => 'Required Dwelling Townhouse Units.',
      condition: (
        value,
        fullObj: Dwelling,
        data: AdditionalDataHomeDwelling
      ) => {
        const townhouseCheckboxIsDisabled: boolean = ['HO4', 'HO6'].some(
          el => data.quoteFormTypes.indexOf(el) !== -1
        );
        const townhouseUnitsIsDisabled: boolean =
          !fullObj.designStyleTownhouseInd && !townhouseCheckboxIsDisabled;
        // const townhouseUnitsIsDisabled: boolean = !fullObj.designStyleTownhouseInd;

        if (townhouseCheckboxIsDisabled) {
          return false;
        }
        return !townhouseUnitsIsDisabled && !value;
      },
      group: WARNING_GROUPS.general,
      carriers: []
    }
  ]
};

const dwellingFamillies: WarningDefinitionI = {
  id: 'familiesCount',
  deepId: 'familiesCount',
  viewUri: generateViewUrl,
  viewFieldId: 'families',
  warnings: [
    {
      label: (value, fullObj) => 'Required Dwelling Families.',
      condition: requiredField,
      group: WARNING_GROUPS.general,
      carriers: []
    }
  ]
};

export const WARNINGS_DEFINITIONS_HOME_DWELLING_LOCATION: WarningDefinitionI[] = [
  dwellingAddress,
  dwellingCity,
  dwellingState,
  dwellingZip
];

export const WARNINGS_DEFINITIONS_HOME_DWELLING_FIRE_LOCATION: WarningDefinitionI[] = [
  dwellingFireDistrct
];

export const WARNINGS_DEFINITIONS_HOME_DWELLINGS: WarningDefinitionI[] = [
  dwellingConstructionMaterial,
  dwellingConstructionYear,
  dwellingSquareFootage,
  dwellingUtilizationTypeCode,
  dwellingStoriesCount,
  dwellingMortgageesCount,
  dwellingPool,
  dwellingTownhouseUnits,
  dwellingFamillies
];
// ------------------------------------------------------------------------------

/**
 * Validation for Dwelling Data Subsystems
 * For Model: DwellingSubsystems
 */

// Required for:
// MGM/NGM Insurance, National General, Providence Mutual, Travelers,
// Union Mutual of Vermont, Hanover New (Plan ID 282), Travelers New (Plan ID 283)
const dwellingSubSysPrimaryHeatCarriers: string[] = [
  '204',
  '119',
  '97',
  '95',
  '282',
  '283',
  '292',
  '303',
  '304',
  '305',
  '316',
  '332'
];
const dwellingSubSysPrimaryHeatCarriers2: string[] = [
  '93'
];
const dwellingSubSysPrimaryHeat: WarningDefinitionI = {
  id: 'heatingPrimaryTypeCode',
  deepId: 'heatingPrimaryTypeCode',
  viewUri: generateViewUrl,
  viewFieldId: 'heatingPrimaryTypeCode',
  warnings: [
    {
      label: (value, fullObj) => 'Required Dwelling Subsystems Primary Heat.',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        const valueToCheck = value === 'Unassigned' ? null : value;

        return Validate.isRequiredForSelectedPlansIfEmptyValue(
          valueToCheck,
          dwellingSubSysPrimaryHeatCarriers,
          additionalData.quoteSelectedPlansIds
        );
      },
      group: WARNING_GROUPS.carrier,
      carriers: dwellingSubSysPrimaryHeatCarriers
    },
    {
      label: (value, fullObj) => 'Required Dwelling Subsystems Primary Heat.',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        return (
          Validate.isRequiredForSelectedFormTypes(
            ['HO3', 'HO5'],
            additionalData.quoteFormTypes
          ) &&
          Validate.isRequiredForSelectedPlansIfEmptyValue(
            value,
            dwellingSubSysPrimaryHeatCarriers2,
            additionalData.quoteSelectedPlansIds
          )
        );
      },
      group: WARNING_GROUPS.carrier,
      carriers: dwellingSubSysPrimaryHeatCarriers2
    }
  ]
};

// Required for:
// National General, Providence Mutual, Union Mutual of Vermont
const dwellingSubSysSecondaryHeatCarriers: string[] = [
  '119',
  '97',
  '304'
];
const dwellingSubSysSecondaryHeatCarriers2: string[] = [
  '93',
];
const dwellingSubSysSecondaryHeat: WarningDefinitionI = {
  id: 'heatingSecondaryTypeCode',
  deepId: 'heatingSecondaryTypeCode',
  viewUri: generateViewUrl,
  viewFieldId: 'heatingSecondaryTypeCode',
  warnings: [
    {
      label: (value, fullObj) => 'Required Dwelling Subsystems Secondary Heat.',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        const valueToCheck = value === 'Unassigned' ? null : value;

        return Validate.isRequiredForSelectedPlansIfEmptyValue(
          valueToCheck,
          dwellingSubSysSecondaryHeatCarriers,
          additionalData.quoteSelectedPlansIds
        );
      },
      group: WARNING_GROUPS.carrier,
      carriers: dwellingSubSysSecondaryHeatCarriers
    },
    {
      label: (value, fullObj) => 'Required Dwelling Subsystems Secondary Heat.',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        return (
          Validate.isRequiredForSelectedFormTypes(
            ['HO3', 'HO5'],
            additionalData.quoteFormTypes
          ) &&
          Validate.isRequiredForSelectedPlansIfEmptyValue(
            value,
            dwellingSubSysSecondaryHeatCarriers2,
            additionalData.quoteSelectedPlansIds
          )
        );
      },
      group: WARNING_GROUPS.carrier,
      carriers: dwellingSubSysSecondaryHeatCarriers2
    }
  ]
};

// Required for:
// MSA/NGM Insurance, National General
const dwellingSubSysPlumbingCarriers: string[] = ['204', '306', '93'];
const dwellingSubSysPlumbingCarriers2: string[] = ['119'];
const dwellingSubSysPlumbing: WarningDefinitionI = {
  id: 'plumbingMaterialTypeCode',
  deepId: 'plumbingMaterialTypeCode',
  viewUri: generateViewUrl,
  viewFieldId: 'plumbingMaterialTypeCode',
  warnings: [
    {
      label: (value, fullObj) => 'Required Dwelling Subsystems Plumbing.',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        const valueToCheck = value === 'Unassigned' ? null : value;

        return Validate.isRequiredForSelectedPlansIfEmptyValue(
          valueToCheck,
          dwellingSubSysPlumbingCarriers,
          additionalData.quoteSelectedPlansIds
        );
      },
      group: WARNING_GROUPS.carrier,
      carriers: dwellingSubSysPlumbingCarriers
    },
    {
      label: (value, fullObj) => 'Required Dwelling Subsystems Plumbing.',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        return (
          Validate.isRequiredForSelectedFormTypes(
            ['HO3', 'HO5'],
            additionalData.quoteFormTypes
          ) &&
          Validate.isRequiredForSelectedPlansIfEmptyValue(
            value,
            dwellingSubSysPlumbingCarriers2,
            additionalData.quoteSelectedPlansIds
          )
        );
      },
      group: WARNING_GROUPS.carrier,
      carriers: dwellingSubSysPlumbingCarriers2
    }
  ]
};

// Required for:
const dwellingSubSysWiringTypeCarriers: string[] = ['292'];
const dwellingSubSysWiringType: WarningDefinitionI = {
  id: 'electricalWiringTypeCode',
  deepId: 'electricalWiringTypeCode',
  viewUri: generateViewUrl,
  viewFieldId: 'electricalWiringTypeCode',
  warnings: [
    {
      label: (value, fullObj) => 'Required Dwelling Subsystems Wiring Type.',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        const valueToCheck = value === 'Unassigned' ? null : value;

        return anyElementIsInArray(['HO3', 'HO5'], additionalData.quoteFormTypes) && Validate.isRequiredForSelectedPlansIfEmptyValue(
          valueToCheck,
          dwellingSubSysWiringTypeCarriers,
          additionalData.quoteSelectedPlansIds
        );
      },
      group: WARNING_GROUPS.carrier,
      carriers: dwellingSubSysWiringTypeCarriers
    }
  ]
};

// Required for:
// MAS/NGM Insurance, National General, Providence Mutual, Travelers
const dwellingSubSysRoofTypeCarriers: string[] = [
  '204',
  '97',
  '95',
  '303',
  '304',
  '316'
];
// Hanover New (Plan ID 282), Travelers New (Plan ID 283) // https://bostonsoftware.atlassian.net/browse/SPR-3015
const dwellingSubSysRoofTypeDependingFormType = ['282', '119', '283', '292', '306', '332']; // https://bostonsoftware.atlassian.net/browse/SPR-2540
// ASI (Plan ID 105)
const dwellingSubSysRoofTypeDependingFormType2 = ['105'];
const dwellingSubSysRoofTypeDependingFormType3 = ['314'];

const dwellingSubSysRoofType: WarningDefinitionI = {
  id: 'roofMaterialTypeCode',
  deepId: 'roofMaterialTypeCode',
  viewUri: generateViewUrl,
  viewFieldId: 'roofMaterialTypeCode',
  warnings: [
    {
      label: (value, fullObj) => 'Required Dwelling Subsystems Roof Type.',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        const valueToCheck = value === 'Unassigned' ? null : value;

        return Validate.isRequiredForSelectedPlansIfEmptyValue(
          valueToCheck,
          dwellingSubSysRoofTypeCarriers,
          additionalData.quoteSelectedPlansIds
        );
      },
      group: WARNING_GROUPS.carrier,
      carriers: dwellingSubSysRoofTypeCarriers
    },
    {
      label: (value, fullObj) => 'Required Dwelling Subsystems Roof Type.',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        const valueToCheck = value === 'Unassigned' ? null : value;

        return (
          anyElementIsInArray(['HO3', 'HO5'], additionalData.quoteFormTypes) &&
          Validate.isRequiredForSelectedPlansIfEmptyValue(
            valueToCheck,
            dwellingSubSysRoofTypeDependingFormType,
            additionalData.quoteSelectedPlansIds
          )
        );
      },
      group: WARNING_GROUPS.carrier,
      carriers: dwellingSubSysRoofTypeDependingFormType
    },
    {
      label: (value, fullObj) => 'Required Dwelling Subsystems Roof Type.',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        const valueToCheck = value === 'Unassigned' ? null : value;

        return (
          anyElementIsInArray(
            ['HO3', 'HO5', 'HO4'],
            additionalData.quoteFormTypes
          ) &&
          Validate.isRequiredForSelectedPlansIfEmptyValue(
            valueToCheck,
            dwellingSubSysRoofTypeDependingFormType2,
            additionalData.quoteSelectedPlansIds
          )
        );
      },
      group: WARNING_GROUPS.carrier,
      carriers: dwellingSubSysRoofTypeDependingFormType2
    },
    {
      label: (value, fullObj) => 'Required Dwelling Subsystems Roof Type.',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        const valueToCheck = value === 'Unassigned' ? null : value;

        return (
          anyElementIsInArray(
            ['HO3', 'HO5', 'HO6'],
            additionalData.quoteFormTypes
          ) &&
          Validate.isRequiredForSelectedPlansIfEmptyValue(
            valueToCheck,
            dwellingSubSysRoofTypeDependingFormType3,
            additionalData.quoteSelectedPlansIds
          )
        );
      },
      group: WARNING_GROUPS.carrier,
      carriers: dwellingSubSysRoofTypeDependingFormType3
    }
  ]
};

// Required for:
// Bunker Hill (PlanId 113), National General (PlanId 119), Travelers (PlanId 95)
// Travelers New (Plan ID 283)
const dwellingSubSysOilTankLocCarriers: string[] = [
  '113',
  '119',
  '95',
  '283',
  '303',
  '304',
  '316',
  '317'
];
const dwellingSubSysOilTankLoc: WarningDefinitionI = {
  id: 'oilStorageTankLocation',
  deepId: 'oilStorageTankLocation',
  viewUri: generateViewUrl,
  viewFieldId: 'oilStorageTankLocation',
  warnings: [
    {
      label: (value, fullObj) =>
        'Required Dwelling Subsystems Oil Tank Location.',
      condition: (
        value,
        fullObj,
        additionalData: AdditionalDataHomeDwelling
      ) => {
        const valueToCheck = value === 'Unassigned' ? null : value;

        let validateOilTankRequirements = false;

        if (
          fullObj.heatingPrimaryTypeCode === 'Oil' ||
          fullObj.heatingSecondaryTypeCode === 'Oil'
        ) {
          validateOilTankRequirements = true;
        }

        return (
          validateOilTankRequirements &&
          Validate.isRequiredForSelectedPlansIfEmptyValue(
            valueToCheck,
            dwellingSubSysOilTankLocCarriers,
            additionalData.quoteSelectedPlansIds
          )
        );
      },
      group: WARNING_GROUPS.carrier,
      carriers: dwellingSubSysOilTankLocCarriers
    }
  ]
};

export const WARNINGS_DEFINITIONS_HOME_DWELLINGS_SUBSYSTEMS: WarningDefinitionI[] = [
  dwellingSubSysPrimaryHeat,
  dwellingSubSysSecondaryHeat,
  dwellingSubSysPlumbing,
  dwellingSubSysWiringType,
  dwellingSubSysRoofType,
  dwellingSubSysOilTankLoc
];

// ------------------------------------------------------------------------------

/**
 * Validation for Dwelling Protection Class
 * For Model: DwellingProtectionClass
 */

const dwellingDistanceToFireStation: WarningDefinitionI = {
  id: 'distanceToFireStation',
  deepId: 'distanceToFireStation',
  viewUri: generateViewUrl,
  viewFieldId: 'distanceToFireStation',
  warnings: [
    {
      label: (value, fullObj) => 'Required Distance to Fire Station.',
      condition: (value, fullObj: Dwelling, data: AdditionalDataI) => {
        return Validate.isEmptyValue(value) || value === 'Unassigned';
        // return true;
      },
      group: WARNING_GROUPS.general,
      carriers: []
    }
  ]
};

const dwellingDistanceToFireHydrant: WarningDefinitionI = {
  id: 'distanceToFireHydrant',
  deepId: 'distanceToFireHydrant',
  viewUri: generateViewUrl,
  viewFieldId: 'distanceToFireHydrant',
  warnings: [
    {
      label: (value, fullObj) => 'Required Distance to Hydrant.',
      condition: (value, fullObj: Dwelling, data: AdditionalDataI) => {
        return Validate.isEmptyValue(value) || value === 'Unassigned';
        // return true;
      },
      group: WARNING_GROUPS.general,
      carriers: []
    }
  ]
};

const dwellingDistanceToBodyWater: WarningDefinitionI = {
  id: 'distanceToBodyWater',
  deepId: 'distanceToBodyWater',
  viewUri: generateViewUrl,
  viewFieldId: 'distanceToBodyWater',
  warnings: [
    {
      label: (value, fullObj) => 'Required Distance to Coast.',
      condition: (value, fullObj: Dwelling, data: AdditionalDataI) => {
        // console.log('IS it valid', Validate.isEmptyValue(value), value, fullObj);
        return Validate.isEmptyValue(value) || value === 'Unassigned';
        // return true;
      },
      group: WARNING_GROUPS.general,
      carriers: []
    }
  ]
};

export const WARNINGS_DEFINITIONS_HOME_DWELLINGS_PROTECTION_CLASS: WarningDefinitionI[] = [
  dwellingDistanceToFireStation,
  dwellingDistanceToFireHydrant,
  dwellingDistanceToBodyWater
];

// HELPERS
// ----------------------------------------------------------------------------
// Check if any element of an array (anyElementArr) is in another array (arrayToCheckIfIsIn)
function anyElementIsInArray(
  anyElementArr: string[],
  arrayToCheckIfIsIn: string[]
): boolean {
  return anyElementArr.some(el => arrayToCheckIfIsIn.indexOf(el) !== -1);
}
