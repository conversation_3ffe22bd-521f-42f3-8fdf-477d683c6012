import { Component, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { OptionsViewComponent } from 'app/shared/components/options-view/options-view.component';


// Services
import { StorageService } from 'app/shared/services/storage-new.service';

@Component({
    selector: 'app-home-general-options',
    templateUrl: './home-general-options.component.html',
    styleUrls: ['./home-general-options.component.scss'],
    standalone: false
})
export class HomeGeneralOptionsComponent implements AfterViewInit {

  @ViewChild('refOptionsViewGeneral') public refOptionsViewCarrier: OptionsViewComponent;

  constructor(
    private storageService: StorageService,
  ) { }

  ngAfterViewInit() {
    this.initOptionsView();
  }

  private initOptionsView(): void {
    this.refOptionsViewCarrier.setOptionsObservable(this.storageService.getStorageData('homeGeneralOptionsParsed'));
    this.refOptionsViewCarrier.setOptionsDefault(this.storageService.getStorageData('homeDefaultOptions'));
    this.refOptionsViewCarrier.setRelatedOptionsObservables([this.storageService.getStorageData('homeCarrierOptionsParsed')]);
    this.refOptionsViewCarrier.setGroupingType('noGroups');
    this.refOptionsViewCarrier.setDefaultType('general');
    this.refOptionsViewCarrier.reInit();
  }

  public onOptionsViewUpdated(data): void {
    this.storageService.setStorageData('homeGeneralOptionsParsed', data.policies);
  }
}
