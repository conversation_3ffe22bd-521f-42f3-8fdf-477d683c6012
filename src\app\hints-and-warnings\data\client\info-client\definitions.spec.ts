import {
    generateCarriers,
    generateLabels,
    generateViewFieldIds,
    generateViewURIs,
    runConditions,
} from 'testing/helpers/warning-definitions';

import { ClientAddress } from '../../../../app-model/client';
import { Quote<PERSON>lan } from '../../../../app-model/quote';
import { Helpers } from '../../../../utils/helpers';
import { AdditionalDataI } from '../../../model/warnings';
import { WARNINGS_DEFINITIONS_INFO_CLIENT_FOR_AUTO, WARNINGS_DEFINITIONS_INFO_CLIENT_ADDRESSES_FOR_AUTO } from 'app/hints-and-warnings/data/';


describe('Definitions: Client', () => {
    beforeEach(() => {
        jasmine.clock().mockDate(new Date(2017, 3, 27));
    });

    describe('when current client address validator is used', () => {
        let definitions: any[];
        let clientAddress: ClientAddress;
        let additionalData: AdditionalDataI;

        beforeEach(() => {
            definitions = WARNINGS_DEFINITIONS_INFO_CLIENT_ADDRESSES_FOR_AUTO;
            clientAddress = {
                address1: '',
                address2: '',
                city: '',
                addressType: 'StreetAddress',
                meta: {
                    href: '',
                    rel: []
                },
                parentId: '',
                quoteSessionId: '',
                residencyDate: '2017-03-10',
                resourceId: '',
                resourceName: '',
                state: '',
                zip: ''
            };
            additionalData = {
                quoteSelectedPlans: <QuotePlan[]> [],
                quoteSelectedPlansIds: [ '13' ],
                specLobTowns: []
            };
        });

        it('allows to check if current address required fields are provided', () => {
            let errors = runConditions(definitions, clientAddress, additionalData);

            expect(errors).toEqual([
                'address1', 'city', 'state', 'zip'
            ]);

            clientAddress.address1 = 'filled';
            clientAddress.state = 'filled';

            errors = runConditions(definitions, clientAddress, additionalData);

            expect(errors).toEqual([
                'city', 'zip'
            ]);

            clientAddress.city = 'filled';
            clientAddress.zip = 'filled';

            errors = runConditions(definitions, clientAddress, additionalData);

            expect(errors).toEqual([]);
        });

        it('allows to check if previous address required fields are provided', () => {
            const previousAddress = Helpers.deepClone(clientAddress);
            previousAddress.addressType = 'PreviousAddress';

            additionalData.quoteSelectedPlansIds = ['26'];
            additionalData.observedData = [clientAddress, previousAddress];

            const errors = runConditions(definitions, previousAddress, additionalData);

            expect(errors).toEqual([
                'address1', 'city', 'state', 'zip'
            ]);
        });

        it('generates viewFieldIds without errors', () => {
            expect(() => {
                generateViewFieldIds(definitions, clientAddress);
            }).not.toThrow();
        });

        it('generates viewURIs without errors', () => {
            expect(() => {
                generateViewURIs(definitions, clientAddress);
            }).not.toThrow();
        });

        it('generates labels without errors', () => {
            expect(() => {
                generateLabels(definitions, clientAddress);
            }).not.toThrow();
        });

        it('generates carriers without errors', () => {
            expect(() => {
                generateCarriers(definitions, clientAddress);
            }).not.toThrow();
        });
    });

    describe('when current client info validator is used', () => {
        let definitions: any[];
        let clientInfo: any;

        beforeEach(() => {
            definitions = WARNINGS_DEFINITIONS_INFO_CLIENT_FOR_AUTO;
            clientInfo = {
                firstName: '',
                lastName: '',
                dob: ''
            };
        });

        it('allows to check if firstName, lastName and date of birth is provided', () => {
            let errors = runConditions(definitions, clientInfo, {});

            expect(errors).toEqual(jasmine.arrayContaining([
                'firstName', 'lastName', 'dob'
            ]));

            clientInfo.firstName = 'filled';
            clientInfo.lastName = 'filled';
            clientInfo.dob = 'filled';

            errors = runConditions(definitions, clientInfo, {});

            expect(errors).toEqual([]);
        });

        it('generates viewFieldIds without errors', () => {
            expect(() => {
                generateViewFieldIds(definitions, clientInfo);
            }).not.toThrow();
        });

        it('generates viewURIs without errors', () => {
            expect(() => {
                generateViewURIs(definitions, clientInfo);
            }).not.toThrow();
        });

        it('generates labels without errors', () => {
            expect(() => {
                generateLabels(definitions, clientInfo);
            }).not.toThrow();
        });

        it('generates carriers without errors', () => {
            expect(() => {
                generateCarriers(definitions, clientInfo);
            }).not.toThrow();
        });
    });
});
