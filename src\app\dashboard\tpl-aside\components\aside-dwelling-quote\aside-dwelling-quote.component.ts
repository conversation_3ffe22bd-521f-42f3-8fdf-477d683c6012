
import {take, first} from 'rxjs/operators';
import { Component, OnInit, ViewChild } from '@angular/core';

import { FilterOption } from 'app/app-model/filter-option';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { PremiumsService } from 'app/dashboard/auto/premiums/premiums.service';
import { PlansSelectorComponent } from 'app/shared/components/plans-selector/plans-selector.component';
import { CoveragesService } from 'app/dashboard/app-services/coverages.service';
import { OptionsService } from 'app/dashboard/app-services/options.service';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import {
  AgencyUserService,
  UserData
} from 'app/shared/services/agency-user.service';

import { SharedStaticDwellingDataFormTypeOptions } from 'app/dashboard/dwelling/shared-static-data';
import { SubscriptionLike as ISubscription } from 'rxjs';
import { SubsService } from 'app/dashboard/app-services/subs.service';
import { Coverage } from '../../../../app-model/coverage';
import { DatepickerModalComponent } from 'app/shared/components/datepicker-modal/datepicker-modal.component';


import { addYears, format, parseISO } from 'date-fns';

@Component({
    selector: 'app-aside-dwelling-quote',
    templateUrl: './aside-dwelling-quote.component.html',
    styleUrls: ['./aside-dwelling-quote.component.scss'],
    standalone: false
})
export class AsideDwellingQuoteComponent implements OnInit {
  private subscription: ISubscription;
  private resetPlansSubscription: ISubscription;

  public quote;
  public effectiveDate;
  public plansOnQuotes: FilterOption[] = [];
  public plansCount = 0;
  public selectedPlan: any;
  public selectedPlansOnQuote: any;
  public formTypes: FilterOption[] = SharedStaticDwellingDataFormTypeOptions; // Array<string> = [];
  private selectedPlanObject;
  private currentPlanUrl;
  private currentPlanSubscriptionGet: ISubscription;
  private plansSubscription: ISubscription;
  private optionsUrl: string;
  private subscriptionOptions;
  private quoteOptions;
  public plansOnQuotesFiltered: FilterOption[] = [];

  @ViewChild('refPlansSelector', {static: true})
  private refPlansSelector: PlansSelectorComponent;
  @ViewChild('datepickerEffectiveDate') public datepickerEffectiveDate: DatepickerModalComponent;

  constructor(
    private storageService: StorageService,
    private quotesService: QuotesService,
    private overlayLoaderService: OverlayLoaderService,
    private premiumsService: PremiumsService,
    private coveragesService: CoveragesService,
    private optionsService: OptionsService,
    private storageGlobalService: StorageGlobalService,
    private apiCommonService: ApiCommonService,
    private agencyUserService: AgencyUserService,
    private subsService: SubsService
  ) {}

  ngOnInit() {
    // this.setPlansOnQuotes(this.storageGlobalService.takeSubs("plans"));
    this.processPlans();

    this.subscription = this.storageService
      .getStorageData('selectedQuote')
      .subscribe(res => {
        this.quote = res;
        this.getCurrentPlan(res.quotePlanList.href);
        this.filterPlansByState(this.quote.state);
        this.effectiveDate = res.effectiveDate;
      });

    // this.subscribtionCurrentPlans();
    this.getOptionsUrl();
    setTimeout(() => {
      this.showQuoteEffectiveDateInPastWarningModal();
    });
  }

  ngOnDestroy() {
    this.subscription && this.subscription.unsubscribe();
    this.currentPlanSubscriptionGet &&
      this.currentPlanSubscriptionGet.unsubscribe();
    this.plansSubscription && this.plansSubscription.unsubscribe();
    this.subscriptionOptions && this.subscriptionOptions.unsubscribe();
    this.resetPlansSubscription && this.resetPlansSubscription.unsubscribe();
  }

  public updateEffectiveDate(event) {
 const dateValue = event.date instanceof Date ? event.date : parseISO(event.date);

  this.quote.effectiveDate = format(dateValue, 'yyyy-MM-dd');
  this.quote.expirationDate = format(addYears(dateValue, 1), 'yyyy-MM-dd');
    this.overlayLoaderService.showLoader();
    this.quotesService
      .updateQuoteInfo(this.quote.resourceId, this.quote).pipe(
      first())
      .subscribe(quote => {
        this.quote = quote;
        this.effectiveDate = quote.effectiveDate;
        this.storageService.setStorageData('selectedQuote', this.quote);
        this.overlayLoaderService.hideLoader();
        this.premiumsService.rerateGeneral(window.location.href);
      });
  }

  private processPlans() {
    const plans = JSON.parse(
      JSON.stringify(this.storageGlobalService.takeSubs('plans'))
    );
    const plansFilteredByLob = this.filterByLob(plans, 'DFIRE');

    if (plansFilteredByLob.length > 0) {
      this.setPlansOnQuotes(plansFilteredByLob);
    } else {
      this.resetPlans();
    }
  }

  private resetPlans() {
    this.overlayLoaderService.showLoader('Loading Plans...');
    this.agencyUserService.userData$.subscribe(agent => {
      if (agent) {
        this.resetPlansSubscription = this.subsService
          .getRatingPlans(agent.agencyId)
          .subscribe(response => {
            this.overlayLoaderService.hideLoader();

            this.setPlansOnQuotes(this.filterByLob(response.items, 'DFIRE'));
          });
      }
    });
  }

  private filterByLob(plans, lob) {
    const filteredPlans = [];

    plans.forEach(plan => {
      if (plan.lob === lob) {
        filteredPlans.push(plan);
      }
    });

    return filteredPlans;
  }

  private setPlansOnQuotes(plans) {
    this.plansOnQuotes = [];
    plans.forEach(item => {
      if (item.lob === 'DFIRE') {
        this.plansOnQuotes.push({
          text: item.name,
          id: item.ratingPlanId,
          data: item
        });
      }
    });
    this.plansOnQuotes.sort((a, b) => {
      if (a.text < b.text) {
        return -1;
      } else if (a.text > b.text) {
        return 1;
      } else {
        return 0;
      }
    });

    this.plansCount = this.plansOnQuotes.length;

    if (this.plansCount) {
      this.selectedPlan = [];
      this.plansOnQuotes.forEach((plan, index) => {
        this.selectedPlan.push(plan);
      });
    }
  }

  private filterPlansByState(state: string) {
    if (this.plansOnQuotes && this.plansOnQuotes.length) {
      const filteredPlans = [];
      this.plansOnQuotes.forEach(plan => {
        if (plan && plan.data) {
          if (plan.data.state === state) {
            filteredPlans.push(plan);
          }
        }
      });
      this.plansOnQuotes = filteredPlans;
    }
  }

  private processCurrentPlans(res) {
    if (
      res &&
      res.items &&
      res.items.length &&
      res.items[0].meta &&
      res.items[0].meta.href
    ) {
      this.currentPlanUrl = res.items[0].meta.href;
    } else if (res && res.meta && res.meta.href) {
      this.currentPlanUrl = res.meta.href;
    }
    this.selectedPlanObject = res;
    this.selectedPlan = [];
    if (res.items[0].items.length) {
      res.items[0].items.forEach(item => {
        this.selectedPlan.push({
          data: item,
          id: item.ratingPlanId,
          text: item.name
        });
      });
    } else {
      this.plansOnQuotes.forEach(plan => {
        this.selectedPlan.push(plan);
      });
    }
    this.refPlansSelector.init();
  }

  private subscribtionCurrentPlans(): void {
    this.currentPlanSubscriptionGet = this.storageService
      .getStorageData('selectedPlan')
      .subscribe(res => {
        if (res && res.items && res.items.length) {
          this.processCurrentPlans(res);
        }
      });
  }

  private getCurrentPlan(uri: string) {
    this.plansSubscription && this.plansSubscription.unsubscribe();
    this.plansSubscription = this.storageService
      .getStorageData('selectedPlan')
      .subscribe(res => {
        if (res && res.items && res.items.length) {
          this.processCurrentPlans(res);
        } else {
          this.getCurrentPlanFromAPI(uri);
        }
      });
  }

  private getCurrentPlanFromAPI(uri): void {
    let getCurrentPlanSubscription;

    this.overlayLoaderService.showLoader();
    this.quotesService
      .getDataByUrl(uri).pipe(
      first())
      .subscribe(plan => {
        this.storageService.setStorageData('selectedPlan', plan);
        this.overlayLoaderService.hideLoader();
      });
  }

  public updateQuotePlan($event): void {
    this.selectedPlansOnQuote = [];

    $event.selectedOption.forEach((option, index) => {
      this.selectedPlansOnQuote.push(option.data);
    });
    const data = { items: this.selectedPlansOnQuote };

    if (this.currentPlanUrl) {
      this.overlayLoaderService.showLoader();
      this.quotesService
        .updateQuoteByUrl(this.currentPlanUrl, data, false)
        .subscribe(plans => {
          this.selectedPlan = $event.selectedOption;
          this.overlayLoaderService.hideLoader();
          if (
            this.selectedPlanObject &&
            this.selectedPlanObject.meta &&
            this.selectedPlanObject.meta.href
          ) {
            this.getCurrentPlanFromAPI(this.selectedPlanObject.meta.href);
          }
        });
    }
  }


  public updateQuoteFormType($event): void {
    this.overlayLoaderService.showLoader();
    this.quote.formType = $event.selectedOption.id;

    this.getQuoteCoverages(this.quote.resourceId)
      .then((coverages: Coverage[]) => {
        const quoteCoverages: Coverage[] = coverages;

        this.quotesService
          .updateQuoteInfo(this.quote.resourceId, this.quote).pipe(
          take(1))
          .subscribe(quote => {
            this.quote = quote;
            this.storageService.setStorageData('selectedQuote', this.quote);

            // Update storage QuoteSelectedFormTypes
            const quoteSelectedFormTypes = this.quotesService.getQuoteSelectedFormTypes(
              this.quote
            );
            this.storageService.setStorageData(
              'selectedQuoteFormTypes',
              quoteSelectedFormTypes
            );

            this.refPlansSelector.init();
            this.overlayLoaderService.hideLoader();

            let options = null;
            if (
              this.optionsService &&
              this.optionsService.defaultValuesDataSource
            ) {
              options = JSON.parse(
                JSON.stringify(this.optionsService.defaultValuesDataSource)
              );
            }
            if (
              this.quote.formType !== 'DP1' &&
              this.quote.formType !== 'Basic (DP 1)' &&
              options
            ) {
              options = this.optionsService.setDefaultsToOptionsOtherThanDP1();
            }
            if (
              this.quoteOptions &&
              this.quoteOptions.items &&
              this.quoteOptions.items.length &&
              this.quoteOptions.items[0].coverages
            ) {
              let frvValue = [];
              this.quoteOptions.items[0].coverages.forEach(option => {
                if (option.coverageCode === 'FRV') {
                  frvValue = option.values;
                }
              });
              options.coverages = options.coverages.map(option => {
                if (option.coverageCode === 'FRV') {
                  option.values = frvValue;
                }
                return option;
              });
            }


            // https://bostonsoftware.atlassian.net/browse/SPR-2475
            // There is only one resource for the Coverages,
            // To avoid value reset for General options, we need to get all
            // current coverages for the Quote, remove Coverages (defaultValuesDataSource)
            // and send them to the API
            const coveragesToOmitCodes: string[] = this.optionsService.defaultValuesDataSource.coverages
              .map((el: Coverage) => el.coverageCode);

            // remove Coverages (defaultValuesDataSource)
            const coveragesToKeep = quoteCoverages.filter((cov: Coverage) => {
              return coveragesToOmitCodes.indexOf(cov.coverageCode) === -1;
            });


            // Remove coverages without values, to remove (reset) them from API
            // It's forced by the way API works
            const coveragesToSendToAPI: Coverage[] = options.coverages.filter((el: Coverage) => {
              return el.values && el.values.length;
            });

            // options.coverages = coveragesToSendToAPI;
            options.coverages = [...coveragesToSendToAPI, ...coveragesToKeep];

            this.overlayLoaderService.showLoader();
            this.apiCommonService
              .putByUri(this.optionsUrl, options).pipe(
              take(1))
              .subscribe(
                res => {
                  if (
                    res &&
                    res.items &&
                    res.items.length &&
                    res.items[0].coverages
                  ) {
                    this.quoteOptions = JSON.parse(JSON.stringify(res));
                  } else if (
                    res &&
                    res.coverages &&
                    this.quoteOptions &&
                    this.quoteOptions.items &&
                    this.quoteOptions.items.length &&
                    this.quoteOptions.items[0] &&
                    this.quoteOptions.items[0].meta &&
                    this.quoteOptions.items[0].meta.href
                  ) {
                    this.quoteOptions.items[0].coverages = res.coverages;
                  }
                  this.storageService.setStorageData(
                    'dwellingQuoteOptions',
                    this.quoteOptions
                  );
                },
                err => this.overlayLoaderService.hideLoader(),
                () => this.overlayLoaderService.hideLoader()
              );

            this.premiumsService.rerateAll();
          });
      })
      .catch(err => console.log(err));
  }

  private getQuoteCoverages(quoteId): Promise<Coverage[]> {
    return new Promise((resolve, reject) => {
      this.optionsService
        .getPolicies(quoteId).pipe(
        take(1))
        .subscribe(
          res => {
            if (res.items[0].coverages && res.items[0].coverages.length) {
              resolve(res.items[0].coverages);
            } else {
              resolve([]);
            }
          },
          err => reject(err)
        );
    });
  }

  private getOptionsUrl() {
    this.subscriptionOptions = this.storageService
      .getStorageData('dwellingQuoteOptions')
      .subscribe(data => {
        if (
          data &&
          data.items &&
          data.items.length &&
          data.items[0].meta &&
          data.items[0].meta.href
        ) {
          this.optionsUrl = data.items[0].meta.href;
          this.quoteOptions = data;
          // } else if (data && data.meta && data.meta.href && data.coverages) {
        } else if (
          data &&
          data.meta &&
          data.meta.href &&
          data.items[0].coverages
        ) {
          this.optionsUrl = data.meta.href;
          this.quoteOptions['items'] = data;
        }
      });
  }

  public get clientNameToDisplay(): string {
    let name = '';

    if (this.quote && this.quote.client && this.quote.client.firstName) {
      name += this.quote.client.firstName;
    }

    if (this.quote && this.quote.client && this.quote.client.lastName) {
      name += ' ' + this.quote.client.lastName;
    }

    return name.trim() ? name : 'No clients name';
  }

  private showQuoteEffectiveDateInPastWarningModal(): void {
    this.storageService.getStorageData('quoteEffectiveDateInPastShowWarningAfterLoad').pipe(
      take(1))
      .subscribe((data: boolean) => {
        if (data) {
          this.datepickerEffectiveDate && this.datepickerEffectiveDate.open();
          this.storageService.setStorageData('quoteEffectiveDateInPastShowWarningAfterLoad', false);
        }
      });
  }
}
