import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { SavedRmvServicesTableComponent } from './saved-rmv-services-table.component';

describe('SavedRmvServicesTableComponent', () => {
  let component: SavedRmvServicesTableComponent;
  let fixture: ComponentFixture<SavedRmvServicesTableComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ SavedRmvServicesTableComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(SavedRmvServicesTableComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
