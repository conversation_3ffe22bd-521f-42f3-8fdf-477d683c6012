import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { RouterTestingModule } from '@angular/router/testing';

import { StubAutocompleteComponent } from 'testing/stubs/components/autocomplete.component';
import { StubDatepickerInputComponent } from 'testing/stubs/components/datepicker-input.component';

import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { OptionsService } from 'app/dashboard/app-services/options.service';

import { PolicyComponent } from './policy.component';


describe('PolicyComponent', () => {
  let component: PolicyComponent;
  let fixture: ComponentFixture<PolicyComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [FormsModule, RouterTestingModule],
      declarations: [ PolicyComponent, StubAutocompleteComponent, StubDatepickerInputComponent ],
      providers: [
        StorageService, QuotesService, OptionsService
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PolicyComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
