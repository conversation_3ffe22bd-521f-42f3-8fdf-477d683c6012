<section class="section section--compact u-spacing--0-5">
  <div class="u-flex u-flex--spread">
    <div class="">
      <h1 class="o-heading">Driver details</h1>
    </div>
    <div class="">
      <!-- *** -->
      <button (click)="addDriver()" class="o-btn o-btn--action o-btn--i_round-plus" id="add-driver-modal">Add
        Driver</button>

      <!-- *** -->
      <button class="o-btn o-btn--action o-btn--i_trash" id="delete-driver-modal">Delete Driver</button>
      <app-confirmbox [launcher]="'#delete-driver-modal'" [question]="'Are you sure you want to delete this driver?'"
        [askAbout]="prepareFullnameAskAbout(selectedDriver)" [confirmBtnText]="'Yes, delete driver'"
        (onAccept)="deleteDriver()" (onCancel)="confirmCanceled($event)">
      </app-confirmbox>

      <!-- *** -->
      <button class="o-btn o-btn--action o-btn" id="reorder-driver-modal"><i class="fas fa-arrows-alt"></i> Reorder Driver</button>
      <app-modalbox #modalReorderDriver [launcher]="'#reorder-driver-modal'" [css]="'u-width-420px'">
        <h2 class="o-heading o-heading--red">Reorder Driver</h2>
        <div class="box box--silver u-spacing--1-5">
          <p>Select the driver(s) you would like to reorder and use the arrows or drag and drop to move them to the desired order</p>
          <div style="margin-left:50px; text-align: center;">
            <p-orderList [value]="drivers" dragdrop="true" dragdropScope="drivers">
          <ng-template let-driver pTemplate="item" style="margin: 0 auto;">
            <div style="font-size:14px;margin:15px 5px 0 0">{{driver.firstName}} {{driver.lastName}}</div>
          </ng-template>
        </p-orderList>
          </div>

        </div>
        <div class="row u-spacing--2">
          <div class="col-xs-12 u-align-right">
            <button class="o-btn" (click)="saveReorder(modalReorderDriver)">Save</button>

            <button class="o-btn o-btn--idle u-spacing--left-2"
              (click)="modalReorderDriver.closeModalbox()">Cancel</button>
          </div>
        </div>
      </app-modalbox>
      <button class="o-btn o-btn--action o-btn--i_search" id="rmv-lookup-driver-modal">RMV Lookup</button>
      <app-modalbox #modalRmvLookupDriver [launcher]="'#rmv-lookup-driver-modal'" [css]="'u-width-620px'">
        <ng-template [ngIf]="modalRmvLookupDriver.isOpen">
          <h2 class="o-heading o-heading--red">RMV Driver Lookup</h2>

          <div class="box box--silver u-spacing--1-5">
            <app-rmv-driver-lookup #refDriverLookup [refModalbox]="modalRmvLookupDriver" (errorStatus)="checkForErrors($event)"></app-rmv-driver-lookup>
          </div>

          <div class="row u-spacing--2">
            <div class="col-xs-12 u-align-right">
              <button class="o-btn" [disabled]="disableButton" (click)="refDriverLookup.rmvLookup($event, modalRmvLookupDriver)">RMV
                Lookup</button>
              <button class="o-btn o-btn--outlined u-spacing--left-2"
                (click)="refDriverLookup.addAnotherDriver($event)">Add
                Another Driver</button>
              <button class="o-btn o-btn--idle u-spacing--left-2"
                (click)="modalRmvLookupDriver.closeModalbox()">Cancel</button>
            </div>
          </div>
        </ng-template>
      </app-modalbox>
    </div>
  </div>
  <div class="row u-spacing--1">
    <div class="col-xs-12">
      <div class="box box--silver">
        <form name="driverDetailForm" novalidate>
          <div class="row o-columns">
            <!-- Driver Detail form START -->
            <div class="col-xs-6 u-padd--right-1">

              <table class="form-table">
                <tr class="form-table__row" [ngClass]="{'is-required-field': refDriverFirstName.invalid}">
                  <td class="form-table__cell u-width-120px">
                    <label for="driverFirstName">First Name:</label>
                  </td>
                  <td class="form-table__cell u-width-160px">
                    <input #refDriverFirstName="ngModel" type="text" required fieldAutofocus name="driverFirstName"
                      id="driverFirstName" [(ngModel)]="selectedDriver.firstName" [dataToSave]="selectedDriver" maxlength="45"
                      (change)="onDriverDataChangeUpdateClient()"  changeDetectionDelay>
                  </td>
                  <td class="form-table__cell u-width-90px"></td>
                </tr>

                <tr class="form-table__row" [ngClass]="{'is-required-field': refDriverLastName.invalid}">
                  <td class="form-table__cell">
                    <label for="driverLasteName">Last Name:</label>
                  </td>
                  <td class="form-table__cell">
                    <input #refDriverLastName="ngModel" type="text" required name="driverLastName" id="driverLastName"
                      [(ngModel)]="selectedDriver.lastName" [dataToSave]="selectedDriver" maxlength="45"
                      (change)="onDriverDataChangeUpdateClient()"  changeDetectionDelay>
                  </td>
                  <td class="form-table__cell"></td>
                </tr>

                <tr class="form-table__row" [class.is-required-field]="refPickerDateOfBirth.ngModel?.invalid">
                  <td class="form-table__cell">
                    <label for="driverDOB">Date of Birth:</label>
                  </td>
                  <td class="form-table__cell">
                    <app-datepicker-input #refPickerDateOfBirth [name]="'driverDateOfBirth'" [id]="'driverDateOfBirth'"
                      [required]="true" [returnDateFormat]="'yyyy-MM-dd'" [selectDate]="selectedDriver.dateOfBirth"
                      (onDateChange)="updateDateObjectProperty($event, selectedDriver, 'dateOfBirth'); onDriverDataChangeUpdateClient($event)"
                      changeDetectionDelay>
                    </app-datepicker-input>
                  </td>
                  <td class="form-table__cell"></td>
                </tr>

                <tr class="form-table__row" [class.is-required-field]="refDriverGender.ngModel?.invalid">
                  <td class="form-table__cell">
                    Gender:
                  </td>
                  <td class="form-table__cell">
                    <sm-autocomplete #refDriverGender [options]="arrGenders" [activeOption]="selectedDriver.gender"
                      [name]="'driverGender'" [id]="'driverGender'" [required]="false" [searchFromBegining]="true"
                      (onSelect)="selected($event, selectedDriver, 'gender')" changeDetectionDelay>
                    </sm-autocomplete>

                  </td>
                  <td class="form-table__cell"></td>
                </tr>

                <tr class="form-table__row" [class.is-required-field]="validateIsRequiredDriverMaritalStatus()">
                  <td class="form-table__cell">
                    Marital Status:
                  </td>
                  <td class="form-table__cell">
                    <sm-autocomplete #refDriverMaritalStatus [options]="arrMaritalStatuts"
                      [activeOption]="selectedDriver.maritalStatus" [name]="'driverMaritalStatus'"
                      [id]="'driverMaritalStatus'" [required]="validateIsRequiredDriverMaritalStatus()"
                      [searchFromBegining]="true" (onSelect)="selected($event, selectedDriver, 'maritalStatus')"
                      changeDetectionDelay>
                    </sm-autocomplete>

                  </td>
                  <td class="form-table__cell"></td>
                </tr>

                <tr class="form-table__row" [class.is-required-field]="validateIsRequiredDriverRelationshipToInsured()">
                  <td class="form-table__cell">
                    Relationship to insured:
                  </td>
                  <td class="form-table__cell">
                    <sm-autocomplete #refDriverRelationship [options]="driverRelations"
                      [activeOption]="selectedDriver.relationshipToInsured" [name]="'driverRelationshipToInsured'"
                      [id]="'driverRelationshipToInsured'" [required]="validateIsRequiredDriverRelationshipToInsured()"
                      [searchFromBegining]="true" (onSelect)="selected($event, selectedDriver, 'relationshipToInsured')"
                      changeDetectionDelay>
                    </sm-autocomplete>
                  </td>
                </tr>
              </table>

            </div> <!-- col -->

            <div class="col-xs-6">

              <table class="form-table">
                <tr class="form-table__row is-required-field" [ngClass]="{'is-required-field': refLicenseNo.invalid ||checkValidMALicense(selectedDriver)}">
                  <td class="form-table__cell u-width-130px">
                    <label for="driverLicenseNumber">License #:</label>
                  </td>
                  <td class="form-table__cell u-width-140px">
                    <input #refLicenseNo="ngModel" type="text" required name="driverLicenseNumber"
                      id="driverLicenseNumber" [(ngModel)]="selectedDriver.licenseNumber" [dataToSave]="selectedDriver" maxlength="45"
                      (blur)="setDefaultDriverLicenseState()" changeDetectionDelay>
                  </td>
                  <td class="form-table__cell" [ngClass]="{'is-required-field': refDrCurrLicState.ngModel?.invalid}">
                    <div class="u-width-80px">
                      <sm-autocomplete #refDrCurrLicState [options]="licenseStates"
                        [activeOption]="selectedDriver.licenseState" [name]="'driverCurrentLicenseState'"
                        [id]="'driverCurrentLicenseState'" [placeholder]="'State'" [searchFromBegining]="true"
                        [required]="true" (onSelect)="selected($event, selectedDriver, 'licenseState')"
                        changeDetectionDelay>
                      </sm-autocomplete>
                    </div>
                  </td>
                </tr>

                <tr class="form-table__row">
                  <td class="form-table__cell"
                    [ngClass]="{'is-required-field': refDatepickDriverFirstLicense.ngModel?.invalid}">
                    <label for="datepickDriverFirstLicense">First Licensed:</label>
                  </td>
                  <td class="form-table__cell"
                    [ngClass]="{'is-required-field': refDatepickDriverFirstLicense.ngModel?.invalid}">
                    <app-datepicker-input #refDatepickDriverFirstLicense [name]="'driverFirstLicensed'"
                      [id]="'driverFirstLicensed'" [required]="true" [selectDate]="selectedDriver.firstLicensed"
                      [returnDateFormat]="'yyyy-MM-dd'"
                      (onDateChange)="updateDateObjectProperty($event, selectedDriver, 'firstLicensed')"
                      changeDetectionDelay>
                    </app-datepicker-input>
                  </td>
                  <td class="form-table__cell u-width-80px"></td>
                </tr>

                <tr class="form-table__row" [ngClass]="{'is-required-field': refSdip.invalid}">
                  <td class="form-table__cell">
                    <label for="sdip">SDIP:</label>
                  </td>
                  <td class="form-table__cell">
                    <input #refSdip="ngModel" required type="text" name="driverSdip" id="driverSdip" pattern="\d{1,2}" maxlength="2"
                    (keydown)="numbersOnly($event)"
                      [(ngModel)]="selectedDriver.sdip" [dataToSave]="selectedDriver" changeDetectionDelay>
                  </td>
                  <td class="form-table__cell"></td>
                </tr>

                <tr class="form-table__row">
                  <td class="form-table__cell">
                  </td>
                  <td class="form-table__cell" colspan="2">
                    <span class="u-cursor-default" (click)="checkboxCarrierModalHandler()">
                      <i class="o-btn o-btn--checkbox" [ngClass]="{'is-active': selectedDriver.overideCarrier}"></i>
                      Ignore Carrier SDIP
                    </span>

                    <app-confirmbox [launcher]="''" [question]="'Ignore Carrier SDIP?'"
                      [askAbout]="'Checking this box will skip the carrier\'s validation of the driver and could lead to inaccurate quotes.'"
                      [confirmBtnText]="'Yes, Ignore'" [templateVersion]="2" [isOpen]="checkboxCarrierModalIsOpen"
                      (onStateChange)="modalStateChange($event)" (onAccept)="ignoreCarrierModalAccept($event)">
                    </app-confirmbox>

                  </td>
                </tr>

                <tr class="form-table__row"
                  [ngClass]="{'is-required-field': checkIfInvalidRadioboxField(selectedDriver.deferredDriver)}">
                  <td class="form-table__cell">
                    Deferred Driver:
                  </td>
                  <td class="form-table__cell" colspan="2" id="driverDefferedDriver">
                    <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
                      <input type="radio" name="deferredDriver" [value]="true"
                        [(ngModel)]="selectedDriver.deferredDriver" (change)="selected(null, selectedDriver, null)">
                      <i class="o-btn o-btn--radio"></i>
                      <span>Yes</span>
                    </label>

                    <label class="o-checkable u-spacing--left-2" [appDetectSystem] [removeRadio]="true">
                      <input type="radio" name="deferredDriver" [value]="false"
                        [(ngModel)]="selectedDriver.deferredDriver" (change)="selected(null, selectedDriver, null)">
                      <i class="o-btn o-btn--radio"></i>
                      <span>No</span>
                    </label>
                  </td>
                </tr>

                <tr class="form-table__row" [ngClass]="{'is-required-field': validateIsRequiredMotorcycleData()}">
                  <td class="form-table__cell">
                    Motorcycle:
                  </td>
                  <td class="form-table__cell" colspan="2" id="driverMotorcycleDriver">
                    <label class="o-checkable" [appDetectSystem] [removeRadio]="true">
                      <input type="radio" name="motorcycleDirver" [value]="true" [(ngModel)]="selectedDriver.motorcycle"
                        (change)="selectMotorcycle(true)">
                      <i class="o-btn o-btn--radio"></i>
                      <span>Yes</span>
                    </label>

                    <label class="o-checkable u-spacing--left-2" [appDetectSystem] [removeRadio]="true">
                      <input type="radio" name="motorcycleDirver" [value]="false"
                        [(ngModel)]="selectedDriver.motorcycle" (change)="selectMotorcycle(false)">
                      <i class="o-btn o-btn--radio"></i>
                      <span>No</span>
                    </label>
                  </td>
                </tr>

                <tr class="form-table__row" *ngIf="selectedDriver.motorcycle">
                  <td class="form-table__cell"></td>
                  <td class="form-table__cell" colspan="2"
                    [ngClass]="{'is-required-field': refDrMotoType.ngModel?.invalid}">
                    <div class="u-show-iblock u-width-85px">
                      <div class="u-t-size--1-1rem u-t-upper u-spacing--bottom-0-5">
                        Type
                      </div>

                      <sm-autocomplete #refDrMotoType [options]="['Permit', 'License']"
                        [activeOption]="selectedDriver.motorcycleLicenseType" [name]="'driverMotorcycleLicenseType'"
                        [id]="'driverMotorcycleLicenseType'" [required]="true" [searchFromBegining]="true"
                        (onSelect)="selectMotorcycleLicenseType($event)" changeDetectionDelay>
                      </sm-autocomplete>

                    </div>

                    <div class="u-show-iblock u-width-110px u-spacing--left-0-5"
                      [ngClass]="{'is-required-field': datepickerMotocycleLicense.ngModel?.invalid}"
                      *ngIf="selectedDriver.motorcycleLicenseType == 'License'">

                      <div class="u-t-size--1-1rem u-t-upper u-spacing--bottom-0-5">
                        <label for="datepickerMotocycleLicenseDate">License Date</label>
                      </div>

                      <app-datepicker-input #datepickerMotocycleLicense [returnDateFormat]="'yyyy-MM-dd'"
                        [required]="true" [id]="'driverMotorcycleLicenseDate'" [name]="'driverMotorcycleLicenseDate'"
                        [selectDate]="selectedDriver.motorcycleLicenseDate"
                        (onDateChange)="selectMotorcycleLicenseDate($event)" changeDetectionDelay>
                      </app-datepicker-input>
                    </div>

                  </td>
                </tr>

                <tr class="form-table__row">
                  <td class="form-table__cell" [ngClass]="{'u-align-v-top': selectedDriver.exclusions?.length}">
                    Exclusions:
                  </td>
                  <td class="form-table__cell" colspan="2">
                    <span *ngIf="!selectedDriver.exclusions?.length">None -</span>
                    <div *ngIf="selectedDriver.exclusions?.length">
                      <div *ngFor="let item of excludedVehicles; let last=last" class="u-spacing--bottom-0-5"
                        [class.u-color-sunset]="item.isExcluded && item.canNotBeExcluded">
                        {{item.nameToDisplay}}
                        <ng-container *ngIf="item.isExcluded && item.canNotBeExcluded">
                          <button #refTooltipLaunch type="button"
                            class="o-btn o-btn--action o-btn--i_info local-exclusions-info-btn">
                            &nbsp;
                          </button>

                          <sm-tooltip [launcher]="refTooltipLaunch" [css]="'tooltip-new tooltip-new--tip'"
                            [position]="'my-center-at-bottom-center'" [positionLauncher]="refTooltipLaunch"
                            [onHover]="true">
                            This vehicle can not be excluded for the driver,
                            because the driver is set for {{item.nameToDisplay}} as operator.
                          </sm-tooltip>
                        </ng-container>
                        <span *ngIf="!last">,</span>
                      </div>
                    </div>

                    <!-- Exclusions management -->
                    <a class="o-link o-link--blue" (click)="exclusionsModalOpen($event)">Edit</a>
                    <app-modalbox #refModalExclusions>
                      <ng-container *ngIf="refModalExclusions.isOpen">
                        <h2 class="o-heading o-heading--red">Exclude These Vehicles</h2>
                        <div class="box box--silver u-spacing--1-5">
                          <p *ngIf="!modalExclusionsVehicles.length">No vehicles</p>

                          <table *ngIf="modalExclusionsVehiclesToExclude.length" class="form-table">
                            <tr class="form-table__row" *ngFor="let item of modalExclusionsVehiclesToExclude">
                              <td *ngIf="item" class="form-table__cell">
                                <span class="u-cursor-default" (click)="exclusionsActionToggleExclusion(item)">
                                  <i class="o-btn o-btn--checkbox" [ngClass]="{'is-active': item.exclude}"></i>
                                  {{item.nameToDisplay}}
                                </span>
                              </td>
                            </tr>
                          </table>

                          <div class="u-spacing--1" *ngIf="modalExclusionsVehiclesCanNotBeExcluded.length">
                            <p>
                              These vehicles CANNOT be excluded because driver is primary operator:
                            </p>

                            <p *ngFor="let item of modalExclusionsVehiclesCanNotBeExcluded"
                              class="u-spacing--1-5 u-spacing--left-3 u-spacing--bottom-1">
                              <span [ngClass]="{'u-color-sunset': item.exclude}">{{item.nameToDisplay}}</span>
                              <button *ngIf="item.exclude" type="button"
                                class="o-btn o-btn--link o-btn--action o-btn--i_round-minus u-float-right"
                                title="Remove from excluded" (click)="exclusionsActionRemoveExclusion(item)">Remove from
                                exclusions</button>
                            </p>
                          </div>
                        </div>

                        <div class="row u-spacing--2">
                          <div class="col-xs-12 u-align-right">
                            <button type="button" class="o-btn" (click)="exclusionsModalActionSave()">Save
                              Exclusions</button>
                            <button type="button" class="o-btn o-btn--idle u-spacing--left-2"
                              (click)="refModalExclusions.close()">Cancel</button>
                          </div>
                        </div>
                      </ng-container>
                    </app-modalbox>
                  </td>
                </tr>
              </table>

            </div> <!-- col -->

            <!-- Driver Detail form END -->
          </div>
        </form>
      </div>
    </div>
  </div>
</section>

<section class="section section--compact u-spacing--2-5">
  <div class="u-flex u-flex--spread">
    <div class="">
      <h1 class="o-heading">Incidents</h1>
    </div>
    <div class="">
      <button class="o-btn o-btn--action o-btn--i_round-plus"
        (click)="openAddIncidentModal(modalAddIncident, refDrIncType)">Add
        Incident</button>

      <app-modalbox #modalAddIncident>
        <h1 class="o-heading o-heading--red">Add Incident</h1>
        <div class="box box--silver u-spacing--1-5">
          <div class="row o-columns">
            <div class="col-xs-12">
              <table class="form-table form-table--fixed">
                <!-- tr class="form-table__row" [class.is-required-field]="addIncidentDataSelected.surchargeDate?.length === 0" -->
                <tr class="form-table__row" [class.is-required-field]="refPickerDateSurchargeAdd.ngModel?.invalid">
                  <td class="form-table__cell u-width-160px">
                    Surcharge Date:
                  </td>
                  <td class="form-table__cell">
                    <app-datepicker-input #refPickerDateSurchargeAdd [required]="true" [returnDateFormat]="'yyyy-MM-dd'"
                      [selectDate]="addIncidentDataSelected.surchargeDate"
                      (onDateChange)="updateDateObjectProperty($event, addIncidentDataSelected, 'surchargeDate', false)">
                    </app-datepicker-input>
                  </td>
                  <td class="form-table__cell"></td>
                </tr>

                <tr class="form-table__row">
                  <td class="form-table__cell u-width-160px">
                    Type of Incident:
                  </td>
                  <td class="form-table__cell" colspan="2">
                    <sm-autocomplete #refDrIncType [options]="addIncidentData.types" [name]="'dr-inc-type'"
                      [id]="'dr-inc-type'" [searchFromBegining]="true" [required]="true"
                      (onSelect)="selectedIncidentType($event, false, refDrIncDesc)">
                    </sm-autocomplete>
                  </td>
                </tr>

                <tr class="form-table__row"
                  [class.is-required-field]="incidentTypesNotRequireAmount.indexOf(addIncidentDataSelected.typeOfIncident) < 0 && addIncidentData?.amount.length < 1">
                  <td class="form-table__cell u-width-160px">
                    Amount Paid (if any):
                  </td>
                  <td class="form-table__cell">
                    <input type="text" [(ngModel)]="addIncidentData.amount" pattern="\d*"
                      (keydown)="numbersOnly($event)">
                  </td>
                  <td class="form-table__cell"></td>
                </tr>

                <tr class="form-table__row is-required-field"
                  [ngClass]="{'is-required-field': refDrIncDesc.ngModel?.invalid}">
                  <td class="form-table__cell u-width-160px">
                    Desc of Incident:
                  </td>
                  <td class="form-table__cell" colspan="2">
                    <sm-autocomplete #refDrIncDesc [options]="addIncidentData.desc"
                      [activeOption]="addIncidentDataSelected.descOfIncident" [name]="'dr-inc-desc'"
                      [id]="'dr-inc-desc'" [searchFromBegining]="true" [required]="true"
                      (onSelect)="selectedIncidentDesc($event)">
                    </sm-autocomplete>
                  </td>
                </tr>
              </table>
            </div>
          </div>
        </div>
        <div class="row u-spacing--2">
          <div class="col-xs-12 u-align-right">
            <button class="o-btn u-spacing--right-2" (click)="addIncident(modalAddIncident)"
              [class.disabled]="refDrIncDesc.ngModel?.invalid || (incidentTypesNotRequireAmount.indexOf(addIncidentDataSelected.typeOfIncident) < 0 && addIncidentData?.amount.length < 1) || addIncidentDataSelected.surchargeDate?.length === 0">
              Add incident
            </button>
            <button class="o-btn o-btn--idle" (click)="modalAddIncident.closeModalbox()">Cancel</button>
          </div>
        </div>
      </app-modalbox>
    </div>
  </div>

  <div class="row u-spacing--1">
    <div class="col-xs-12">
      <div class="box box--silver">
        <div class="row">
          <div class="col-xs-12" *ngIf="!incidents || incidents?.length <= 0">
            <p>None</p>
          </div>

          <div class="col-xs-12" *ngIf="incidents?.length > 0">
            <ul class="manage-list">
              <li *ngFor="let incident of incidents | orderBy: ['surchargeDate']" class="manage-list__item"
                [id]="incident.resourceId + '_incident'">
                <div class="manage-list__data">
                  <div class="manage-list__data-item manage-list__data-item--date">{{ incident.surchargeDate |
                    dateFormat:'M/d/yyyy' }}</div>
                  <div class="manage-list__data-item manage-list__data-item--price"><span
                      *ngIf="incident.amountPaid">$</span>{{
                    incident.amountPaid }}</div>
                  <div class="manage-list__data-item manage-list__data-item--type">{{ incident.typeOfIncident }}</div>
                  <div class="manage-list__data-item manage-list__data-item--state">{{ incident.descOfIncident }}</div>
                </div>
                <div class="manage-list__actions">
                  <a class="o-action o-action--icon-less manage-list__btn modal-manage-list-edit-incident"
                    (click)="editIncidentOpen(incident, modalEditIncident)"
                    [id]="'incident-btn_' + incident.resourceId">Edit</a>
                  <a class="o-action o-action--icon-less manage-list__btn confirm-manage-list-delete-incident"
                    (click)="deleteIncidentOpen(incident)">Delete</a>
                </div>
              </li>
            </ul>

            <app-confirmbox #modalDeleteIncident [launcher]="'.confirm-manage-list-delete-incident'"
              [question]="'Are you sure you want to delete this incident?'"
              [askAbout]="deleteIncidentDataSelectedAskAbout" [confirmBtnText]="'Yes, delete incident'"
              (onAccept)="deleteIncident($event, deleteIncidentDataSelected)" (onCancel)="confirmCanceled($event)">
            </app-confirmbox>

            <app-modalbox #modalEditIncident>
              <h1 class="o-heading o-heading--red">Edit Incident</h1>
              <div class="box box--silver u-spacing--1-5">
                <div class="row o-columns">
                  <div class="col-xs-12">
                    <table class="form-table form-table--fixed">
                      <tr class="form-table__row"
                        [class.is-required-field]="refPickerDateSurchargeEdit.ngModel?.invalid">
                        <td class="form-table__cell u-width-160px">
                          Surcharge Date:
                        </td>
                        <td class="form-table__cell">
                          <app-datepicker-input #refPickerDateSurchargeEdit [required]="true"
                            [returnDateFormat]="'yyyy-MM-dd'" [selectDate]="editIncidentDataSelected.surchargeDate"
                            (onDateChange)="updateDateObjectProperty($event, editIncidentDataSelected, 'surchargeDate', false)">
                          </app-datepicker-input>
                        </td>
                        <td class="form-table__cell"></td>
                      </tr>

                      <tr class="form-table__row">
                        <td class="form-table__cell u-width-160px">
                          Type of Incident:
                        </td>
                        <td class="form-table__cell" colspan="2">
                          <sm-autocomplete #refDrIncTypeEdit [options]="addIncidentData.types"
                            [activeOption]="helperForViewEditSelectedIncidentType" [name]="'dr-inc-type'"
                            [id]="'dr-inc-type'" [searchFromBegining]="true"
                            (onSelect)="selectedIncidentType($event, true, refDrIncDescEdit)">
                          </sm-autocomplete>
                        </td>
                      </tr>

                      <tr class="form-table__row"
                        [class.is-required-field]="incidentTypesNotRequireAmount.indexOf(editIncidentDataSelected.typeOfIncident) < 0 && editIncidentDataSelected?.amountPaid?.length < 1">
                        <td class="form-table__cell u-width-160px">
                          Amount Paid (if any):
                        </td>
                        <td class="form-table__cell">
                          <input type="text" [(ngModel)]="editIncidentDataSelected.amountPaid" pattern="\d*"
                            (keydown)="numbersOnly($event)">
                        </td>
                        <td class="form-table__cell"></td>
                      </tr>

                      <tr class="form-table__row is-required-field"
                        [ngClass]="{'is-required-field': refDrIncDescEdit.ngModel?.invalid}">
                        <td class="form-table__cell u-width-160px">
                          Desc of Incident:
                        </td>
                        <td class="form-table__cell" colspan="2">
                          <sm-autocomplete #refDrIncDescEdit [options]="addIncidentData.desc"
                            [activeOption]="editIncidentDataSelected.descOfIncident" [name]="'dr-inc-desc'"
                            [id]="'dr-inc-desc'" [searchFromBegining]="true" [required]="true"
                            (onSelect)="selectedIncidentDesc($event, true)">
                          </sm-autocomplete>
                        </td>
                      </tr>
                    </table>
                  </div>
                </div>
              </div>
              <div class="row u-spacing--2">
                <div class="col-xs-12 u-align-right">
                  <button class="o-btn u-spacing--right-2" (click)="editIncident(modalEditIncident)"
                    [class.disabled]="refDrIncDescEdit.ngModel?.invalid || (incidentTypesNotRequireAmount.indexOf(editIncidentDataSelected.typeOfIncident) < 0 && editIncidentDataSelected?.amountPaid?.length < 1)">Save</button>
                  <button class="o-btn o-btn--idle" (click)="modalEditIncident.closeModalbox()">Cancel</button>
                </div>
              </div>
            </app-modalbox>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>

<app-modalbox #modalQuoteEffectiveDate>
  <h1 class="o-heading o-heading--red u-spacing--bottom-1-5">Limits Information</h1>

      <div>
        <p>Prior Vehicle Coverage Limits that were on this quote were below the allowed minimums for this effective date.  SinglePoint has bumped the limits to the new minimums allowed.  Review and make changes if desired.</p>
      </div>
      <div class="row u-spacing--2">
        <div class="col-xs-12 u-align-right">
          <hr class="o-hr u-spacing--bottom-1-5"/>
          <button (click)="modalQuoteEffectiveDate.close()" class="o-btn o-btn">OK</button>
        </div>
      </div>
  </app-modalbox>

<app-drivers-options></app-drivers-options>

<app-leave-quote #leaveQuote></app-leave-quote>
