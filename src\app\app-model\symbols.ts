import { Vehicle, VehiclePlanSymbols, VehicleSymbol} from 'app/app-model/vehicle';

const _SYMBOL_DESCRIPTIONS = {
  iso75Coll: 'ISO 75 Collision',
  iso75Comp: 'ISO 75 Comprehensive',
  vrgColl: 'VRG Collision',
  vrgComp: 'VRG Comprehensive',
  iso27: 'ISO 1-27',
  iso27Safety: 'ISO 1-27 (Safety Conversion)',
  iso27Bsc: 'ISO 1-27 (BSC Conversion)',
}

const _SYMBOL_CODE_SOURCES = {
  manual: 'Manual',
  price: 'Price',
  default: 'VinMaster'
}

export const SYMBOL_DESCRIPTIONS = Object.freeze(_SYMBOL_DESCRIPTIONS);
export const SYMBOL_CODE_SOURCES = Object.freeze(_SYMBOL_CODE_SOURCES);
export const LOCAL_SYMBOL_CODE_SOURCE = SYMBOL_CODE_SOURCES.default; //'SPR App';

export class LocalSymbolData {
  constructor (
    public required:boolean = true,
    public symbol:VehicleSymbol = new VehicleSymbol(),
    public sourceSymbol:VehicleSymbol = new VehicleSymbol(),   // update only after mapping with cloned VehicleSymbol, to keep source values
    public requiredForPlans: string[] = []
  ) {}
}

export class SelectedVehicleSymbols {
  constructor(
    public iso75Coll:LocalSymbolData = helperGenerateLocalSymbolDataWithDefaultSymbolData('ISO75', 'Coll', SYMBOL_DESCRIPTIONS.iso75Coll),
    public iso75Comp:LocalSymbolData = helperGenerateLocalSymbolDataWithDefaultSymbolData('ISO75', 'Comp', SYMBOL_DESCRIPTIONS.iso75Comp),
    public vrgColl:LocalSymbolData = helperGenerateLocalSymbolDataWithDefaultSymbolData('POLK', 'Coll', SYMBOL_DESCRIPTIONS.vrgColl),
    public vrgComp:LocalSymbolData = helperGenerateLocalSymbolDataWithDefaultSymbolData('POLK', 'Comp', SYMBOL_DESCRIPTIONS.vrgComp),
    public iso27:LocalSymbolData = helperGenerateLocalSymbolDataWithDefaultSymbolData('ISO27', 'NA', SYMBOL_DESCRIPTIONS.iso27),
    public iso27Safety:LocalSymbolData = helperGenerateLocalSymbolDataWithDefaultSymbolData('ISO27', 'NA', SYMBOL_DESCRIPTIONS.iso27Safety),         // symbolType: ISO27, description: ISO 1-27 (Safety Conversion)
  ) {}
}

export class SelectedVehicleSymbolsForVehicle {
  constructor (
    public vehicle:Vehicle = new Vehicle(),
    public selectedVehicleSymbols:SelectedVehicleSymbols = new SelectedVehicleSymbols(),
    public priceNewValueIsRequired: boolean = true
  ) {}
}

// HELPERS
//-----------------------------------------------------------------

function helperGenerateLocalSymbolDataWithDefaultSymbolData(symbolType:string='', symbolCodeType:string='', symbolDescription:string='', symbolCodeSource:string=LOCAL_SYMBOL_CODE_SOURCE):LocalSymbolData {
  let localSymbolData:LocalSymbolData = new LocalSymbolData();
  let defaultSymbol:VehicleSymbol = new VehicleSymbol();

  defaultSymbol.symbolType = symbolType;
  defaultSymbol.codeType = symbolCodeType;
  defaultSymbol.description = symbolDescription;
  defaultSymbol.codeSource = symbolCodeSource;

  localSymbolData.required = false;
  localSymbolData.symbol = defaultSymbol;
  localSymbolData.requiredForPlans = [];

  return localSymbolData;
}