export const fieldNames: Map<string, string> = new Map([
  ['isFinanced', 'Is Financed'],
  ['isLeased', 'Is Leased'],
  ['garagingType', 'Garaging Type'],
  ['fid', 'Business FID'],
  ['firstName_1', 'Owner 1 First Name'],
  ['middleName_1', 'Owner 1 Middle Name'],
  ['lastName_1', 'Owner 1 Last Name'],
  ['dateOfBirth_1', 'Owner 1 Date of Birth'],
  ['ssn_1', 'Owner 1 Social Security'],
  ['ownerLicense_1', 'Owner 1 License'],
  ['ownerState_1', 'Owner 1 License State'],
  ['ownerPhone_1', 'Owner 1 Phone'],
  ['ownerphoneType_1', 'Owner 1 Phone Type'],
  ['ownerEmail_1', 'Owner 1 Email'],
  ['firstName_2', 'Owner 2 First Name'],
  ['middleName_2', 'Owner 2 Middle Name'],
  ['lastName_2', 'Owner 2 Last Name'],
  ['ssn_2', 'Owner 2 Social Security'],
  ['dateOfBirth_2', 'Owner 2 Date of Birth'],
  ['ownerLicense_2', 'Owner 2 License'],
  ['ownerState_2', 'Owner 2 License State'],
  ['ownerPhone_2', 'Owner 2 Phone'],
  ['ownerphoneType_2', 'Owner 2 Phone Type'],
  ['ownerEmail_2', 'Owner 2 Email'],
  ['purchaseDate', 'Purchase Date'],
  ['purchaseState', 'Purchase State'],
  ['businessName', 'Business Name'],
  ['firstName', 'Seller First Name'],
  ['lastName', 'Seller Last'],
  ['street', 'Seller Street Address'],
  ['unit', 'Seller Unit'],
  ['city', 'Seller City'],
  ['state', 'Seller State'],
  ['zip', 'Seller Zip'],
  ['taxExempt', 'Tax Exempt'],
  ['maResident', 'MA Resident'],
  ['salePrice', 'Sale Price'],
  ['nonMaTx', 'Non-MA Tax'],
  ['transactionType', 'Transaction Type'],
  ['purchaseType', 'Purchase Type'],
  ['reassigned', 'Reassigned'],
  ['ownership', 'Ownership Type'],
  ['vin', 'Vin'],
  ['year', 'Year'],
  ['make', 'Make'],
  ['model', 'Model'],
  ['modelNumber', 'Model Number'],
  ['bodyStyle', 'Body Style'],
  ['vehicleType', 'Vehicle Type'],
  ['color', 'Primary Color'],
  ['secondaryColor', 'Secondary Color'],
  ['transmission', 'Transmission'],
  ['cylinders', 'Number of Cylinders'],
  ['passengers', 'Passengers'],
  ['doors', 'Number of Doors'],
  ['fuelType', 'Fuel Type'],
  ['grossWeight', 'Gross Weight'],
  ['trim', 'Trim'],
  ['registrationType', 'Registration Type'],
  ['registeredWeight', 'Registered Weight'],
  ['numberOfSeats', 'Number of Seats'],
  ['condition', 'Condition'],
  ['odometer', 'Odometer'],
  ['odometerCode', 'Odometer Code'],
  ['plateType', 'Plate Type'],
  ['title', 'Title'],
  ['titleState', 'Title State'],
  ['titleIssueDate', 'Title Issue Date'],
  ['titleType', 'Title Type'],
  ['brandTypes', 'Brands'],
  ['signedBy', 'Signed By'],
  ['effectiveDate', 'Effective Date'],
  ['policyChangeDate', 'Policy Change Date'],
  ['carrier', 'Writing Company'],
  ['tradeVin', 'Trade-In 1 Vin'],
  ['tradeVin2', 'Trade-In 2 Vin'],
  ['allow1', 'Trade-In Allowance Vehicle 1'],
  ['allow2', 'Trade-In Allowance Vehicle 2'],
  ['make1', 'Trade-In Vehicle 1 Make'],
  ['make2', 'Trade-In Vehicle 2 Make'],
  ['model1', 'Trade-In Vehicle 1 Model'],
  ['model2', 'Trade-In Vehicle 2 Model'],
  ['year1', 'Trade-In Vehicle 1 Year'],
  ['year2', 'Trade-In Vehicle 2 Year'],
  ['vehicleType1', 'Trade-In Vehicle 1 Type'],
  ['vehicleType2', 'Trade-In Vehicle 2 Type'],
  ['auctionSale', 'Auction Sale'],
  ['dealerFid', 'Dealer FID'],
  ['tradeInCount', 'Trade In Count'],
  ['lessorFid', 'Lessor FID'],
  ['lessorName', 'Lessor Name'],
  ['lienholderCode', 'Lienholder Code'],
  ['lienholderName', 'Lienholder Name'],









]);
