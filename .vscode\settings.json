{"typescript.tsdk": "./node_modules/typescript/lib", "typescript.check.tscVersion": false, "tslint.autoFixOnSave": true, "importSorter.sortConfiguration.customOrderingRules.rules": [{"regex": "^@angular", "orderLevel": 0}, {"regex": "^@", "orderLevel": 10}, {"regex": "^testing", "orderLevel": 30}, {"regex": "^app", "orderLevel": 40}, {"regex": "^[.]", "orderLevel": 50}], "cSpell.words": ["AUTOP", "autob", "automanager", "datepicker", "lifecycle", "modalbox", "scrollbar", "sprc"], "movets.relativeToTsconfig": true, "files.autoSave": "off"}