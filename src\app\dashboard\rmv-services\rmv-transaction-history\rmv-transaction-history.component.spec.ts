import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { RmvTransactionHistoryComponent } from './rmv-transaction-history.component';

describe('RmvTransactionHistoryComponent', () => {
  let component: RmvTransactionHistoryComponent;
  let fixture: ComponentFixture<RmvTransactionHistoryComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ RmvTransactionHistoryComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(RmvTransactionHistoryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
