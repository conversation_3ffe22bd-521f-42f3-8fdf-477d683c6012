

export class LeadRecord {
  constructor(
  public id: string= '',
  public name: string = '',
  public address: string = '',
  public when: string = '',
  public origin: string = '',
  public isNew: boolean = true,
  public status: string = '',
  public icon: string = '',
  public email: string = '',
  public phone: string = '',
  public company: string = '',
  public message: string = '',
  ) {}
}

export class LeadDetail {
constructor(
public id: string= '',
public lob: string= '',
public contact: LeadContactDetail = new LeadContactDetail(),
public drivers: LeadDriverDetail[]= [],
public vehicles: LeadVehicleDetail[]= [],
public quotes: LeadQuoteDetail[]= [],
public discounts: LeadDiscountDetail = new LeadDiscountDetail(),

public home: HomeLeadDetail = new HomeLeadDetail(),
public coverage: CoverageLeadDetail = new CoverageLeadDetail()

) {}
}
// Home
export class HomeLeadDetail {
  constructor(
    public insuranceType: string= '',
    public yearBuilt: number= 0,
    public residence: string= '',
    public currentCoverage: string= ''
  ) {}
}
export class CoverageLeadDetail {
  constructor(
    public liability: string= '',
    public medicalPayments: number= 0,
    public dwellingReplacementCost: string= '',
    public contentReplacementCost: string= ''
  ) {}
}

export class LeadContactDetail {
  constructor(
    public name: string = '',
    public city: string = '',
    public leadPhone: LeadPhoneDetail = new LeadPhoneDetail(),
    public leadEmail: LeadEmailDetail = new LeadEmailDetail(),
    public primaryResidence: string = '',
    public contactMe: string = '',
    public notes: string = ''
  ) {}
}

export class LeadPhoneDetail {
constructor (
  public phone: string = '',
  public preferredContact: boolean= false
) {}
}

export class LeadEmailDetail {
  constructor (
    public email: string= '',
    public preferredContact: boolean = false
  ) {}
  }


export class LeadDriverDetail {
  constructor(
    public id: string = '',
    public firstName: string = '',
    public lastName: string = '',
    public dateOfBirth: string = '',
    public meritRatingPoints: number,
    public meritRatingPointsDesc: string = ''
  ) {}
}


export class LeadVehicleDetail {
  constructor(
    public id: string = '',
    public year: string = '',
    public make: string = '',
    public model: string = '',
    public primaryUse: string = '',
    public coverageRequested: string = '',
    public collisionDeductible: string = '',
    public comprehensiveDeductible: string = '',
    public propertyDamage: string = ''
  ) {}
}


export class LeadQuoteDetail {
  constructor(
    public name: string = '',
    public premium: string = ''
  ) {}
}


export class LeadDiscountDetail {
  constructor(
    public paymentMethod: string = '',
    public motorClub: string = ''
  ) {}
}
