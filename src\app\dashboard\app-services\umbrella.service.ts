
import {take, map} from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { ApiService } from 'app/shared/services/api.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

// Model
import { BasicPolicyInfo } from 'app/app-model/umbrella';


@Injectable()
export class UmbrellaService {

  // BasicPolicyInfo
  // -------------------------------------------------------------------------------

  constructor(
    private http: HttpClient,
    private apiService: ApiService,
    private storageService: StorageService,
    private apiCommonService: ApiCommonService
  ) { }

  //#region BasicPolicyInfo
  public getBasicPolicyInfo(uri: string): Observable<any> {
    return this.apiCommonService.getByUri(uri);
  }

  public createBasicPolicyInfoByUri(uri: string, basicPolicyInfo: BasicPolicyInfo): Observable<any> {
    return this.apiCommonService.postByUri(uri, basicPolicyInfo);
  }

  public updateBasicPolicyInfo(data): Observable<any> {
    return this.http.put(this.apiService.url(data.meta.href), data).pipe(map((res: any) => {
      this.storageService.setStorageData('basicPolicyInfo', res);
      return res;
    }));
  }

  public updateBasicPolicyInfoByUri(uri: string, basicPolicyInfoData: BasicPolicyInfo): Observable<any> {
    // let uri = 'quotes/' + quoteID + '/basicPolicyInfo';
    return this.apiCommonService.putByUri(uri, basicPolicyInfoData);
  }


  public createDefaultBasicPolicyInfo(url): Promise<BasicPolicyInfo> {
    // For the moment create basic policy info from stub
    const umbrellaBasicPolicyInfo: BasicPolicyInfo = new BasicPolicyInfo();
    umbrellaBasicPolicyInfo.policyLimits = '1Million';
    umbrellaBasicPolicyInfo.underlyingLimits = 'Upto250And500';

    return new Promise((resolve, reject) => {
      this.createBasicPolicyInfoByUri(url, umbrellaBasicPolicyInfo).pipe(take(1)).subscribe((response: BasicPolicyInfo) => {
        console.log('%c createDefaultBasicPolicyInfo', 'color:green', response);
        this.storageService.setStorageData('basicPolicyInfo', response);
        return resolve(response);
      });
    });
  }
}
