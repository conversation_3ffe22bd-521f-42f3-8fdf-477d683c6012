import {map, catchError} from 'rxjs/operators';
import { ApiService } from 'app/shared/services/api.service';
import { Driver, ComDriver } from 'app/app-model/driver';
import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, empty, throwError } from 'rxjs';
import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';

// import { DRIVERS } from 'app/app-mock/drivers';

export function generateDriverName(driver: Driver): string {
  let name = '';
  name += driver.firstName ? driver.firstName + ' ' : '';
  name += driver.lastName ? driver.lastName + ' ' : '';

  return name.trim() || 'New Driver';
}

@Injectable()
export class DriversService {
  public tempDriver: Driver;
  public newDriver = false;

  constructor(
    private http: HttpClient,
    private apiService: ApiService,
    private quotesService: QuotesService,
    private storageService: StorageService,
    private specsService: SpecsService,
    private apiCommonService: ApiCommonService
  ) { }

  // public getDriversFromAPI():Observable<any> {
  //   let newQuote = this.quotesService.newQuote;
  //   if (newQuote) {
  //     return this.apiCommonService.getByUri(newQuote.drivers.href)
  //   }
  // }

  // public getDrivers(uri):Observable<any> {
  //   return this.apiCommonService.getByUri(uri)
  // }

  // Create Driver
  public createDriver(quoteID: string, driverData: any= {}): Observable<Driver> {
    const uri = '/quotes/' + quoteID + '/drivers';
    return this.createDriverByUri(uri, driverData);
  }

  public createDriverByUri(uri: string, driverData: any= {}): Observable<Driver> {
    return this.http.post(this.apiService.url(uri), driverData).pipe(map((res: any) => {
      this.tempDriver = res;
      return res;
    }));
  }


  public deleteDriver(uri: string): Observable<any> {
    const newQuote = this.quotesService.newQuote;
    if (newQuote) {
      return this.http.delete(this.apiService.url(uri), {}).pipe(map((res: any) => {
        this.tempDriver = res;
        return this.tempDriver;
      }));
    }
    return empty();
  }

  public deleteDriverById(quoteID: string, driverID: string): Observable<any> {
    const uri = '/quotes/' + quoteID + '/drivers/' + driverID;

    return this.apiCommonService.deleteByUri(uri);
  }

  public updateDriver(data: any): Observable<any> {
    if (this.tempDriver) {
      return this.http.put(this.apiService.url(this.tempDriver.meta.href), data).pipe(
        map((res: any) => this.storageService.updateDriversListSingleItem(res.parentId, res)
      ),
        catchError((err: any) => this.handleError(err))
      );
    }
    return empty();
  }


  private handleError(err: any): any {
    if (err.status === 400 && err.error.message === 'Validation Failed') {
      console.error(`Validation error: ${err.error.errors[0].message}`);
      return null;
    }
    return err;
  }

  public updateDriverByUri(uri: string, driverData: Driver): Observable<any> {
    return this.apiCommonService.putByUri(uri, driverData).pipe(
      catchError(err => {
        if (err.status === 400 && err.error.message === 'Validation Failed') {
          alert(err.error.errors[0].message);
          return throwError(null);
        }
        return throwError(err);
      })
    );
  }

  public updateDriverIndependently(data): Observable<any> {
    if (data && data.resourceId) {
      return this.http.put(this.apiService.url(data.meta.href), data).pipe(map((res: any) => {
        this.storageService.updateDriversListSingleItem(res.parentId, res);

        return res;
      }),
      catchError((err: any) => this.handleError(err))
    )
    }
    return empty();
  }

  public setDriverOptions(data, option): Observable<any> {
    if (data && data.resourceId) {
      let options = [option];
      if (option && option[0]) {
        options = option;
      }
      return this.apiCommonService.postByUri(data.coverages.meta.href, {coverages: options});
    }
    return empty();
  }

  public updateDriverOptions(uri: string, options): Observable<any> {
    return this.apiCommonService.putByUri(uri, {coverages: options});
  }

  public getDriverOptions(uri: string): Observable<any> {
    return this.apiCommonService.getByUri(uri);
  }

  // Get Drivers list
  public getDriversList(quoteID: string): Observable<any> {
    const uri = 'quotes/' + quoteID + '/drivers';
    return this.getDriversListByUri(uri);
  }

  public getDriversListByUri(uri: string): Observable<any> {
    return this.apiCommonService.getByUri(uri);
  }


  public generateDriverName(driver: Driver): string {
    return generateDriverName(driver);
  }

  /*** Commercial  Driver Endpoints ****/
  public createComDriver(quoteID: string, driverData: any= {}): Observable<ComDriver> {
    const uri = '/quotes/' + quoteID + '/comdrivers';
    return this.createComDriverByUri(uri, driverData);
  }

  public createComDriverByUri(uri: string, driverData: any= {}): Observable<ComDriver> {
    return this.http.post(this.apiService.url(uri), driverData).pipe(map((res: any) => {
      return res;
    }));
  }

  public deleteComDriver(uri: string): Observable<any> {
    const newQuote = this.quotesService.newQuote;
    if (newQuote) {
      return this.http.delete(this.apiService.url(uri), {}).pipe(map((res: any) => {
        return res;
      }));
    }
    return empty();
  }

  public deleteComDriverById(quoteID: string, driverID: string): Observable<any> {
    const uri = '/quotes/' + quoteID + '/comdrivers/' + driverID;

    return this.apiCommonService.deleteByUri(uri);
  }

  public updateComDriver(quoteId: string, driverId: string, driverData: ComDriver): Observable<any> {
    const uri = '/quotes/' + quoteId + '/comdrivers/' + driverId;
      return this.updateComDriverByUri(uri, driverData);
  }

  public updateComDriverByUri(uri: string, driverData: ComDriver): Observable<any> {
    return this.apiCommonService.putByUri(uri, driverData);
  }

  // Get Drivers list
  public getComDriversList(quoteID: string): Observable<any> {
    const uri = 'quotes/' + quoteID + '/comdrivers';
    return this.getComDriversListByUri(uri);
  }

  public getComDriversListByUri(uri: string): Observable<any> {
    return this.apiCommonService.getByUri(uri);
  }
}
