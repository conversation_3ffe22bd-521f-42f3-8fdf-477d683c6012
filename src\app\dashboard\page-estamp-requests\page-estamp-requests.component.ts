import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';

import { take } from 'rxjs/operators';
import * as _ from 'underscore';

import { ActivatedRoute, NavigationEnd, NavigationExtras, Router } from '@angular/router';
import { Component, OnInit, ViewChild, ChangeDetectorRef, Input } from '@angular/core';

import { EstampRequest } from 'app/app-model/estamp-Request';
import { DateRange } from 'app/app-model/date';
import { DatesService } from '../app-services/dates.service';
import { FilterOption } from 'app/app-model/filter-option';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { EstampService } from '../app-services/estamp.service';
import { SubsService } from '../app-services/subs.service';
import { SubscriptionLike as ISubscription } from 'rxjs';
import { DeleteQuoteModalComponent } from 'app/shared/components/delete-quote/delete-quote.component';
import { SearchByNameComponent } from '../search-by-name/search-by-name.component';
import { MatTabGroup } from '@angular/material/tabs';
import { FilterComponent } from 'app/shared/components/filter/filter.component';
import { ModalboxComponent } from 'app/shared/components/modalbox/modalbox.component';
import { RmvServicesResponse } from '../../app-model/rmvservices-response';
import { AgencyUserService } from '../../shared/services/agency-user.service';
import { FilterDatesComponent } from 'app/shared/components/filter-dates/filter-dates.component';
import { AsideRegistrationResponseComponent } from '../tpl-aside/components/aside-buttons-menu/aside-registration-response/aside-registration-response.component';
import { format, parseISO } from 'date-fns';


@Component({
    selector: 'app-page-estamp-requests',
    templateUrl: './page-estamp-requests.component.html',
    styleUrls: ['./page-estamp-requests.component.scss'],
    standalone: false
})
export class PageEstampRequestsComponent implements OnInit {

  @ViewChild('deleteModalbox') public deleteModalbox: ModalboxComponent;
  @ViewChild('refModalQuoteInbox') public leadPopupModal: ModalboxComponent;
  @ViewChild('tabGroup') tabGroup: MatTabGroup;
  @ViewChild('refFilterDates') public FilterDates: FilterComponent;
  @ViewChild('registration') regModal: ModalboxComponent;
  @ViewChild(SearchByNameComponent) searchProperties;
  @ViewChild('paymentAlreadyRequestedModalbox') alreadyProcessed: ModalboxComponent;
  @ViewChild(FilterDatesComponent) filterDates;

  @Input() isDashboard: boolean | null = null;

  rmvExpired;
  unauthorizedError;
  validationData: RmvServicesResponse;
  transactionType;
  newQuotesSize: any;
  fromEmail;
  emailId;
  emailTransaction;
  orgId;
  selectedStampId;
  public modalboxLoadingData = false;
  public paginationResultsCount = 0;
  public paginationResultLimit = 10;
  public paginationResultShowEstamp = 0;
  public paginationCurrentPage = 1;
  public isDeleteConfirmed = false;
  public isAllSelected = false;

  public arrEstampRequestsAll: any[];
  public arrEstampRequestsFiltered: EstampRequest[];
  private dataInitiated = false;
  public selectedEstamps: string[] = [];

  public nameSearchQuery: string;
  public nameType = 'Last';

  public filterStatusOptions: FilterOption[] = [
    { id: '0', text: 'All' },
    { id: '1', text: 'Open' },
    { id: '2', text: 'Closed' }
  ];

  filterStatusSelectedOption: FilterOption = this.filterStatusOptions[1];

  public filterStatusLabel: string;

  public filterLocationsOptions: FilterOption[] = [{ id: 'all', text: 'All' }];
  private filterLocationsSelectedOption: FilterOption = this.filterLocationsOptions[0];
  private filterLocationLabel: string;

  public filterRequesterOptions: FilterOption[] = [
    { id: '0', text: 'All' },
    { id: '1', text: 'Dealer' },
    { id: '2', text: 'Buyer' },
  ];
  private filterDealerSelectedOption: FilterOption = this.filterRequesterOptions[0];
  private filterDealerLabel: string;

  public dateRange: DateRange = new DateRange();

  private agencyId: string;
  private getEstampSubscription: ISubscription;
  // private getEstampSubscription: any;
  private getEstampRequestDataString = '';
  private dataToChange: any;

  private isRefreshRequest = false;
 ref: DynamicDialogRef | undefined
  constructor(
    private datesService: DatesService,
    private estampService: EstampService,
    private overlayLoaderService: OverlayLoaderService,
    private route: ActivatedRoute,
    private router: Router,
    private subsService: SubsService,
    private agencyUserService: AgencyUserService,
    public dialogService:DialogService
  ) { }

  ngOnInit() {
    this.getEstampRequests();
    this.setFilterStatusOptions();
    this.setFilterDealerOptions();
    this.setFilterLocationsOptions();
    this.checkRmvAccess();
    this.openFromEmail();
  }

  checkRmvAccess() {
    this.agencyUserService.getSubscriptionInformation$.subscribe(res => {
      this.rmvExpired = res.items.find(x => x.code === 'RMV').expired;
    });
  }

  getOrgId() {
    this.agencyUserService.userData$.subscribe(
      x => this.orgId = x.agencyId
    );
  }

  openFromEmail() {
    this.route.queryParams.subscribe(async p => {
      await this.getOrgId();
      if (p.fromEmail) {
        this.fromEmail = p.fromEmail;
        this.checkStatus(p.id, p.transaction, p.requesterType);
      }
    });
  }


  public filterByName(event) {
    this.nameSearchQuery = this.searchProperties.searchQuery;
    this.filterDates.searchQuery = this.nameSearchQuery;
    this.nameType = this.searchProperties.nameType;
    if ((event.key === 'Enter' && this.nameSearchQuery.length >= 3) || (event.target.tagName === 'BUTTON' && this.nameSearchQuery.length >= 3)) {
      this.filterDates.filterDatesLabel = 'All Dates';
      this.getEstampRequests(true);
    } else {
      this.resetSearch();
    }
  }

  public refreshPage() {
    this.isRefreshRequest = true;
    this.getEstampRequests();
  }

  openModal() {
    this.deleteModalbox.open();
  }
  cancel() {
    this.deleteModalbox.close();
    this.alreadyProcessed.close();
  }

  public getEstampRequests(showResultsFromFirstPage: boolean = false): void {

    let status, dateType, startDate, endDate, location, name, requestorType;

    if (showResultsFromFirstPage) {
      this.paginationResultShowEstamp = 0;
      this.paginationCurrentPage = 1;
    }

    name = this.nameSearchQuery ? this.nameSearchQuery.replace(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g, '') : '';

    status = this.filterStatusSelectedOption.text;
    if (status === 'all' || status === 'All') {
      status = 'None';
    }

    location = this.filterLocationsSelectedOption.id;
    if (location === 'all' || location === 'All') {
      location = '';
    }

    requestorType = this.filterDealerSelectedOption.text;
    if (requestorType === 'all' || requestorType === 'All') {
      requestorType = '';
    }

    dateType = '';
    if (this.filterDates != undefined)
    {
      dateType = this.filterDates.filterDatesRangeSelectedOption.id ?
      this.filterDates.filterDatesRangeSelectedOption.id.replace(/\b\w/g, l => l.toUpperCase()) : '';
      if (dateType === 'all' || dateType === 'All') {
        dateType = '';
      }
    }

    startDate = this.dateRange.start ? format(new Date(this.dateRange.start),'yyyy-MM-dd') : '';
    endDate = this.dateRange.end ? format(new Date(this.dateRange.end),'yyyy-MM-dd') : '';

    // If request data hasn't changed, do not send request // query params for api endpoint
    const currentRequestDataString = this.paginationResultShowEstamp.toString()
      + this.paginationResultLimit.toString()
      + name
      + dateType
      + startDate
      + endDate
      + requestorType
      + status
      + location;

    if (this.getEstampRequestDataString === currentRequestDataString && !(this.isRefreshRequest)) {
      return;
    }

    this.isRefreshRequest = false;

    this.getEstampRequestDataString = currentRequestDataString;

    if (this.getEstampSubscription) {
      this.overlayLoaderService.hideLoader();
      this.getEstampSubscription.unsubscribe();
    }

    this.overlayLoaderService.showLoader('Loading Results...');
    const params = {
      offset: this.paginationResultShowEstamp.toString(),
      limit: this.paginationResultLimit.toString(),
      buyerName: name,
      dateType,
      startDate,
      endDate,
      requestorType,
      status,
      locationId: location
    };
    this.getEstampSubscription = this.estampService.getEstampRequests(params).pipe(take(1)).subscribe(estampRequests => {
      console.log('ESTAMP::::', estampRequests);
      if (this.filterLocationsOptions.length < 2) {
        this.getLocations();
      }
      this.dataInitiated = true;
      this.arrEstampRequestsAll = estampRequests.items;
      this.arrEstampRequestsFiltered = estampRequests.items;
      this.paginationResultsCount = estampRequests.size;
      this.overlayLoaderService.hideLoader();
    }, reject => {
      this.overlayLoaderService.hideLoader();
    });
  }


  private setFilterStatusOptions(): void {
    this.filterStatusSelectedOption = this.filterStatusOptions[1];
  }

  private setFilterLocationsOptions(): void {
    this.filterLocationsSelectedOption = this.filterLocationsOptions[0];
  }

  private setFilterDealerOptions(): void {
    this.filterDealerSelectedOption = this.filterRequesterOptions[0];
  }


  public toggleStamps(stampIdentifier: string): void {
    const itemIndex = this.selectedEstamps.indexOf(stampIdentifier);
    const isPresent = (itemIndex > -1);

    if (isPresent) {
      this.selectedEstamps.splice(itemIndex, 1);
    } else {
      this.selectedEstamps.push(stampIdentifier.toString());
    }
  }

  // to check/uncheck based on the selected form list
  public isSelected(stampIdentifier: string): boolean {
    return (this.selectedEstamps.indexOf(stampIdentifier) > -1 || this.isAllSelected);
  }

  // to check/uncheck using a property
  public isChecked(): boolean {
    return this.isAllSelected;
  }

  public deleteSelectedEstampRequest() {
    const deleteArray = [];
    this.selectedEstamps.forEach(x => { deleteArray.push(x); });
    this.estampService.deleteEstampRequests(deleteArray).subscribe(x => {
      this.cancel();
      this.refreshPage();
    });
  }

  public toggleAll(): void {
    this.isAllSelected = !this.isAllSelected;

    if (this.isAllSelected) {
      this.arrEstampRequestsAll.forEach(estamp => {
        if (this.selectedEstamps.indexOf(estamp.stampRequestId) === -1) {
          this.selectedEstamps.push(estamp.stampRequestId.toString());
        }
      });
    } else {
      this.selectedEstamps = [];
    }
  }

  public confirmCanceled($event) {
    // console.log('Cancel: ', $event);
    // dummy function to close popup
  }

  private parseLastModifiedDate(date: string): string {
    return format(date,'MMM d, yyyy');
  }

  checkStatus(stampId, transaction, requesterType) {
    if (requesterType === 'Buyer') {
      this.selectedStampId = stampId;
      this.overlayLoaderService.showLoader();
      this.estampService.validateStampRequest(stampId, this.orgId).subscribe(x => {
        this.overlayLoaderService.hideLoader();
        this.transactionType = transaction === 'Renew'
          ? 'RegistrationRenewal' : 'RegistrationReinstatement';
        this.validationData = x.response;
        this.validationData.stampId = stampId;
        this.validationData.ownerEmail = x.stampDetails.buyerInfo.email;
        this.validationData.ownerDob = x.stampDetails.buyerInfo.dob;
        this.validationData.ownerFid = x.stampDetails.buyerInfo.fid;

        this.arrEstampRequestsAll.forEach(stamp => {
          if (stamp.stampRequestId === stampId) { stamp.status = x.stampDetails.stampData.status; }
        });


        x.stampDetails.stampData.status === 'PaymentRequested' ? this.alreadyProcessed.open() : this.validatePlate();
      }, err => {
        this.overlayLoaderService.hideLoader();
        this.unauthorizedError = true;
        this.checkRmvAccess();
        this.regModal.open();
      });
    } else {
      this.redirectToDealerReview(stampId);
    }
  }

  public paginationPageChange(data) {
    if (this.dataInitiated && this.paginationResultShowEstamp !== data.startAt) {
      this.paginationCurrentPage = data.pageNumber;
      this.paginationResultShowEstamp = data.startAt;
      this.getEstampRequests();
    }
  }

  private paginationSetResultLimit(intLimit: any) {
    this.paginationResultLimit = parseInt(intLimit, 10);
  }

  public onResultLimitChange($ev): void {
    setTimeout(() => {
      this.paginationSetResultLimit($ev.limit);
      this.getEstampRequests(true);
    });
  }



  // Filters on the Page
  // ------------------------------------------------------------------------------
  private onStatusChange(option: FilterOption): void {
    this.filterStatusSelectedOption = option;
  }

  private onLocationChange(option: FilterOption): void {
    this.filterLocationsSelectedOption = option;
  }

  private onDealerChange(option: FilterOption): void {
    this.filterDealerSelectedOption = option;
  }

  private filterDataChangeUpdate($ev): void {
    if (this.dataToChange && ($ev.selectedOption.id !== this.dataToChange.selectedOption.id || $ev.selectedOption.id === 'all')) {
      this.dataToChange = $ev;
      switch ($ev.filterId) {
        case 'filter_Status':
          this.onStatusChange($ev.selectedOption);
          break;
        case 'filter_locations':
          this.onLocationChange($ev.selectedOption);
          break;
        case 'filter_requester':
          this.onDealerChange($ev.selectedOption);
          break;
      }
      this.getEstampRequests(true);
    } else {
      this.dataToChange = $ev;
    }
  }

  private onDateTypeChange(option: FilterOption): void {
    this.filterDates.filterDatesTypesSelectedOption = option;
  }

  public onFilterDataChange($ev): void {
    this.filterDataChangeUpdate($ev);
    // this.filterResults();
  }



  // Helpers
  // ------------------------------------------------------------------------------
  private updateFiltersLabels(): void {
    this.filterStatusLabel = this.filterStatusSelectedOption.text;
    this.filterLocationLabel = this.filterLocationsSelectedOption.text;
    this.filterDealerLabel = this.filterDealerSelectedOption.text;
    this.filterDates.filterDatesLabel = this.formatFilterLabelDateTypes();
  }

  private formatFilterLabelDateTypes(): string {
    let formatedLabel = 'All';
    const startDate = (this.dateRange.start) ? format(new Date(this.dateRange.start),'MMM d, yyyy') : null;
    const endDate = (this.dateRange.end) ? format(new Date(this.dateRange.end),'MMM d, yyyy') : null;

    if (!this.dateRange.start && !this.dateRange.end) {
      // formatedLabel = 'All';
      formatedLabel = 'Last 7 days';
    } else if (this.dateRange.start && this.dateRange.end) {
      formatedLabel = (startDate === endDate) ? `${startDate}` : `${startDate} - ${endDate}`;
    } else if (this.dateRange.start && !this.dateRange.end) {
      formatedLabel = `From ${startDate}`;
    } else if (!this.dateRange.start && this.dateRange.end) {
      formatedLabel = `To ${endDate}`;
    }

    if (this.filterDates.filterDatesTypesSelectedOption.id !== 'all') {
      formatedLabel += ` (${this.filterDates.filterDatesTypesSelectedOption.text})`;
    }

    return formatedLabel;
  }

  resetSearch() {
    this.filterDates.prepareResetSearch();
    this.dateRange.start = null;
    this.dateRange.end = null;
    this.nameSearchQuery = null;
    this.getEstampRequests();
  }

  getStampsFromFilteredDates() {
    this.dateRange = this.filterDates.dateRange;

    var allowFiltering = this.filterDates.isFilteringAllowed();

    if (allowFiltering) {
      this.getEstampRequests(true);
    }
  }


  validatePlate() {
    if (this.alreadyProcessed.isOpen) { this.alreadyProcessed.close(); }
    //this.regModal.open();
    this.ref = this.dialogService.open(AsideRegistrationResponseComponent, {
      width: '60%',
      data: {
        regData: this.validationData,
        transaction: this.transactionType,
        ownerDob: this.validationData.ownerDob,
        ownerEmail: this.validationData.ownerEmail,
        ownerFid: this.validationData.ownerFid
      }
     })
  }

  public closeModalBox() {
    this.tabGroup.selectedIndex = 0;
    this.leadPopupModal.close();
  }

  private redirectToDealerReview(stampId): void {
    const params: NavigationExtras = {
      queryParams: { overlay: 'estamp-review', stamp: stampId }
    };
    this.router.navigate([], params);
  }

  splitWords(word) {
    if (word === 'RequestSent') { word = 'Requested'; }
    return word.replace(/([A-Z])/g, ' $1').trim();
  }

  private getLocations() {
    const locations = ''; // todo: this.storageGlobalService.takeSubs('locations')
    if (locations.length) {
      return this.parseLocationsData(locations);
    } else {
      if (this.agencyId) {
        this.subsService
          .getAgencyLocations(this.agencyId)
          .subscribe(data => {
            this.parseLocationsData(data.locations);
          });
      } else {
        this.agencyUserService.userData$.subscribe(
          agent => {
            this.agencyId = agent.agencyId;
            this.subsService
              .getAgencyLocations(this.agencyId)
              .subscribe(data => {
                this.parseLocationsData(data.locations);
              });
          }
        );
      }
    }
  }

  private parseLocationsData(locations) {
    let id, address, city, state;
    const tmpArr: FilterOption[] = [{ id: 'all', text: 'All' }];
    locations.map(location => {
      id = location.id ? location.id : false;
      address = location.address1 ? location.address1 + ', ' : '';
      city = location.city ? location.city + ', ' : '';
      state = location.state ? location.state : '';

      if (id) {
        tmpArr.push({ id: location.id, text: address + city + state });
      }
    });
    this.filterLocationsOptions = tmpArr;
    return tmpArr;
  }
}

