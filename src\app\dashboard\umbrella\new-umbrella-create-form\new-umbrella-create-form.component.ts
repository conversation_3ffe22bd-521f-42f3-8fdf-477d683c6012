
import {take, first} from 'rxjs/operators';
import { CoveragesService } from 'app/dashboard/app-services/coverages.service';
import { UmbrellaService } from './../../app-services/umbrella.service';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import { AgencyUserService, UserData } from 'app/shared/services/agency-user.service';
import { SubscriptionLike as ISubscription ,  Observable } from 'rxjs';
import { StorageService }  from 'app/shared/services/storage-new.service';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { RouteService } from 'app/shared/services/route.service';

import { PlansSelectorComponent } from 'app/shared/components/plans-selector/plans-selector.component';
import { LeaveQuoteComponent } from 'app/shared/components/leave-quote/leave-quote.component';
import { FilterOption } from 'app/app-model/filter-option';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { SubsService }  from 'app/dashboard/app-services/subs.service';
import { QuotesService }  from 'app/dashboard/app-services/quotes.service';
import { Quote, QuotePlan } from 'app/app-model/quote';
import { ClientDetails, ClientContactMethod, ClientAddress } from 'app/app-model/client';
import { ClientsService, CLIENT_TYPES } from 'app/dashboard/app-services/clients.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { LeaveQuoteService } from 'app/shared/services/leave-quote.service';
import { BasicPolicyInfo } from '../../../app-model/umbrella';
import { format, addYears, isBefore, startOfDay, parseISO } from 'date-fns';


interface eventToEmitInterface {
  event: Event;
}

export interface IEventDataImportUmbrellaQuote {
  effectiveDate: string;
  selectedPlans: QuotePlan[];
}

@Component({
    selector: 'app-new-umbrella-create-form',
    templateUrl: './new-umbrella-create-form.component.html',
    styleUrls: ['./new-umbrella-create-form.component.scss'],
    standalone: false
})
export class NewUmbrellaCreateFormComponent implements OnInit {
  // @Input() public effectiveDate: string = null;
  @Input()
  public get effectiveDate(): string {return this.modelEffectiveDate; }
  public set effectiveDate(val: string) {
    if (val) {
      this.modelEffectiveDate = val;
    } else {
      this.modelEffectiveDate = null;
    }
  }

  constructor(
    private router: Router,
    private routeService: RouteService,
    private agencyUserService: AgencyUserService,
    private storageGlobalService: StorageGlobalService,
    private storageService: StorageService,
    private overlayLoaderService: OverlayLoaderService,
    private subsService: SubsService,
    private quotesService: QuotesService,
    private clientsService: ClientsService,
    private apiCommonService: ApiCommonService,
    private leaveQuoteService: LeaveQuoteService,
    private umbrellaService: UmbrellaService,
    private coverageService: CoveragesService
  ) { }

  public get disableCreateButton(): boolean {
    return this.selectedPlan.length <= 0 || !this.modelEffectiveDate;
  }

  // ---------------------------------------------------------------------------
  public get showWarningInThePast(): boolean {
    return this.modelEffectiveDate && this.dateIsInThePast();
  }

  public modelEffectiveDate: string = new Date().toISOString();
  private agencyInfo;
  public plansOnQuotes: FilterOption[] = [];
  public plansCount = 0;
  public selectedPlan: any[] = [];
  public invalidPlanQuoteField = false;

  private resetPlansSubscribtion: ISubscription;

  private createdClientDetails: ClientDetails;
  private createdClientContactMethod: ClientContactMethod;
  private createdClientAddress: ClientAddress;

  @Input() public useToImportQuote = false;

  @Output() public onCreateManuallyClick: EventEmitter<eventToEmitInterface> = new EventEmitter();
  @Output() public onCancelClick: EventEmitter<eventToEmitInterface> = new EventEmitter();
  @Output() public importQuoteClick: EventEmitter<IEventDataImportUmbrellaQuote> = new EventEmitter();

  @ViewChild('leaveQuote') leaveQuote: LeaveQuoteComponent;
  @ViewChild('refPlansSelector', {static: true}) refPlansSelector: PlansSelectorComponent;

  private selectedPlansOnQuote = [];

  ngOnInit() {
    this.agencyUserService.userData$.subscribe(agent => {
      this.agencyInfo = agent;
    });

    this.processPlans();
  }

  ngOnDestroy() {
    this.resetPlansSubscribtion && this.resetPlansSubscribtion.unsubscribe();
  }

  canDeactivate(): Observable<boolean> | boolean {
    this.leaveQuote.detectConfirmation();
    return this.leaveQuote.detectConfirmationObservable.asObservable().pipe(first());
  }

  private processPlans() {
    const plans = JSON.parse(JSON.stringify(this.storageGlobalService.takeSubs('plans')));
    const plansFilteredByLob = this.filterByLob(plans, 'PUMBR');

    if (plansFilteredByLob.length) {
      this.setPlansOnQuotes(plansFilteredByLob);
    } else {
      this.resetPlans();
    }
  }

  private filterByLob(plans, lob) {
    const filteredPlans = [];

    plans.forEach(plan => {
      if (plan.lob === lob) {
        filteredPlans.push(plan);
      }
    });

    return filteredPlans;
  }

  private resetPlans() {
    this.overlayLoaderService.showLoader('Loading Plans...');
    this.agencyUserService.userData$.subscribe(agent => {
      if (agent) {
        this.resetPlansSubscribtion = this.subsService.getRatingPlans(agent.agencyId).subscribe( response => {
          this.overlayLoaderService.hideLoader();

          this.setPlansOnQuotes(this.filterByLob(response.items, 'AUTOP'));
          this.refPlansSelector.init();
        });
      }
    });
  }

  private setPlansOnQuotes(plans) {
    this.plansOnQuotes = [];
    plans.forEach(item => {
      this.plansOnQuotes.push({text: item.name, id: item.ratingPlanId, data: item});
    });
    this.plansOnQuotes.sort((a, b) => {
      if (a.text < b.text) { return -1; } else if (a.text > b.text) { return 1; } else { return 0; }
    });

    this.plansCount = this.plansOnQuotes.length;

    if (this.plansCount) {
      this.selectedPlan = [];
      this.selectedPlansOnQuote = [];
      this.plansOnQuotes.forEach( (plan, index) => {
        this.selectedPlan.push(plan);
        this.selectedPlansOnQuote.push(plan.data);
      });
    }
  }
  public refreshValue($event) {
    this.selectedPlansOnQuote = [];
    for (const option of $event.selectedOption) {
      this.selectedPlansOnQuote.push(option.data);
    }

    this.selectedPlan = $event.selectedOption;
    this.invalidPlanQuoteField = false;
  }

 public onDateChange($ev: { date: string | Date | null }): void {
  try {
    if ($ev && $ev.date) {
      // Handle both string and Date objects
      const dateValue = typeof $ev.date === 'string' ? $ev.date : $ev.date.toISOString();
      this.modelEffectiveDate = format(parseISO(dateValue), 'yyyy-MM-dd');
    } else {
      this.modelEffectiveDate = null;
    }
  } catch (error) {
    console.error('Error formatting date:', error);
    this.modelEffectiveDate = null;
  }
}

  public handleCreateManuallyClick($ev): void {
    this.leaveQuote.forceConfirmation = true;
    this.leaveQuote.confirm().then( answer => {
      if (answer) {
        this.redirectToCreateQuoteView()
          .then(() => {
            this.createQuoteManually();
          });

        if (this.invalidPlanQuoteField) {
          return;
        }

        this.onCreateManuallyClick.emit({
          event: $ev
        });
      }
    });
  }

  public handleCancelClick($ev): void {
    this.onCancelClick.emit({
      event: $ev
    });
  }

  private redirectToCreateQuoteView(): Promise<any> {
    return this.router.navigateByUrl('/dashboard/quotes/new');
  }

  public createQuoteManually() {
    if (!this.selectedPlan.length) {
      this.invalidPlanQuoteField = true;
      return;
    }

    this.invalidPlanQuoteField = false;

    this.quotesService.setIsImportedQuote(false);
    this.quotesService.setIsMaipArcQuote(false);
    let quoteData;
    if (this.selectedPlansOnQuote.length) {
      quoteData = {
        lob: this.selectedPlansOnQuote[0].lob,
        state: this.selectedPlansOnQuote[0].state
      };
    }

    this.overlayLoaderService.showLoader();
    this.quotesService.createNewQuote({
      state: quoteData.state, // this.selectedState['id'],
      lob: 'PUMBR',
      effectiveDate: this.modelEffectiveDate,
      expirationDate: format(addYears(parseISO(this.modelEffectiveDate), 1), 'yyyy-MM-dd'),
      policyPeriod: 12
    }).subscribe(
      data => {

        let srcQuoteSessionId = null;
                  this.storageService
                    .getStorageData('srcQuoteSessionId')
                    .subscribe(sessionId => {
                      if (sessionId) {
                        srcQuoteSessionId = sessionId;
                      }
                    });

        // if new quote created manually
        this.storageService.clearStorage();
        this.storageService.clearQuoteStoredData();

        this.storageService.setStorageData('isNewQuote', true);

        console.log('CREATED QUOTE', data);

        let quote = data;
        let subsSubscriptionStates;
        let subsSubscriptionPlans;
        let subsSubscriptionLobs;

        // Set Quote Info
        quote = this.setQuoteInfo(quote);

        this.storageService.setStorageData('selectedQuote', quote);
        console.log('REQUEST DATA: ', this.agencyInfo.agencyId, quoteData.lob);
        subsSubscriptionStates = this.subsService.getRatingStates(this.agencyInfo.agencyId, quoteData.lob).subscribe(() => subsSubscriptionStates.unsubscribe());
        subsSubscriptionLobs = this.subsService.getRatingLobs(this.agencyInfo.agencyId).subscribe(() => subsSubscriptionLobs.unsubscribe());

        const quotePlanList = {
          items: this.selectedPlansOnQuote
        };
        if (!this.selectedPlansOnQuote.length) {
          // quotePlanList.items = [this.selectedPlan.data];
          quotePlanList.items = [...this.selectedPlan.map(el => el.data)];
        }

        this.quotesService.updateQuoteByUrl(quote.quotePlanList.href, quotePlanList).subscribe( updatedQuote => {
          // Reset data
          // BasicPolicyInfo and UmbrellaQuoteCoverages are property of Umbrella quote
          // So href from these properties would be utilized while creating new resources
          // Need to create basic policy info while creating a quote
          // this.basicPolicyInfo = null;
          // this.umbrellaQuoteCoverages = null;
          this.createdClientDetails = null;
          this.createdClientAddress = null;

          this.createClient(quote.resourceId)
            .then((clientDetails: ClientDetails) => {
              this.createdClientDetails = clientDetails;
              return this.createClientData(clientDetails);
            })
            .then(() => this.getQuoteClients(quote.resourceId))
            .then(() => this.createBasicPolicyInfo(quote.resourceId))
            .then(() => this.createCoverages(quote.resourceId))
            // .then(() => this.getBasicPolicyInfo(quote.resourceId)) // It's set during creating Basic Policy Info
            .then(() => {
              // Assign created client ID to Quote
              quote.client.resourceId = this.createdClientDetails.resourceId;
              this.storageService.setStorageData('selectedQuote',  quote);

              // 05.30.2018 - SA: New quote created from dashboard will not have a srcQuoteSessionId, hence check if value is not null or empty.
              if (srcQuoteSessionId) {
                    this.quotesService.copyQuoteInfo(quote.quoteSessionId, {quotesessionid: srcQuoteSessionId}).pipe(first()).subscribe(res => {
                      if (res) {
                        this.routeService.showSaveConfirmation = false;
                        this.leaveQuote.showSaveConfirmation = false;
                        this.leaveQuote.forceConfirmation = false;
                        // this.router.navigateByUrl('/dashboard/quotes/' + quote.quoteSessionId);
                        this.router.navigate(
                          ['/dashboard/quotes/', quote.quoteSessionId],
                          { queryParams: {newQuote: true}}
                        );
                      } else {
                        this.router.navigateByUrl('/dashboard/umbrella/quotes/' + quote.resourceId + '/basics');
                      }
                    });
                  } else {
                    this.router.navigateByUrl('/dashboard/umbrella/quotes/' + quote.resourceId + '/basics');
                  }

              this.leaveQuoteService.saveStorageQuoteDataSnapshot();
              this.overlayLoaderService.hideLoader();
            })
            // .then(() => this.getBasicPolicyInfo(quote.resourceId))
            // }).then(() => this.getBasicPolicyInfo(quote.resourceId))
            .catch(err => {
              console.log(err);
            });
        });
      },
      error => {
        console.log(error);
        this.overlayLoaderService.hideLoader();
      }
    );
  }

  private setQuoteInfo(quote: Quote): Quote {
    let userData: UserData;

    this.agencyUserService.userData$.pipe(
      first())
      .subscribe(data => userData = data);

    // Quote agency contact
    if (!quote.agent && userData) {
      quote.agent = userData.user.userId;
    }

    return quote;
  }

  private createClient(quoteID: string): Promise<any> {
    return this.clientsService.createQuoteClient(quoteID)
      .toPromise()
      .then((clientDetails: ClientDetails) => clientDetails)
      .catch(err => console.log(err));
  }

  // private CreateBasicPolicyInfo(quoteId: string): Observable<any> {
  //   const uri = '/quotes/' + quoteId + '/basicPolicyInfo/';
  //   return this.umbrellaService.createDefaultBasicPolicyInfo(uri);
  // }

  private createBasicPolicyInfo(quoteId: string): Promise<BasicPolicyInfo> {
    const uri = '/quotes/' + quoteId + '/basicPolicyInfo/';
    return this.umbrellaService.createDefaultBasicPolicyInfo(uri);
  }

  private createCoverages(quoteId: string): Promise<any> {
    return new Promise((resolve, reject) => {
      const uri = '/quotes/' + quoteId + '/coverages';
      this.coverageService.createUmbrellaCoverageByUriWithDefaultValues(uri).pipe(take(1)).subscribe(
        res => resolve(res),
        err => reject(err)
      );
    });
  }

  private getBasicPolicyInfo(quoteId: any): Promise<any> {
    return new Promise<void>((resolve, reject) => {
      const uri = '/quotes/' + quoteId + '/basicPolicyInfo/';
      this.umbrellaService.getBasicPolicyInfo(uri).pipe(
        take(1))
        .subscribe(
          res => {
            console.log('%c getBasicPolicyInfo', 'color:red', res);
            resolve();
          },
          err => reject(err)
        );
    });
  }

  // private getCoverages(quoteId: any): any {
  //   const uri = '/quotes/' + quoteId + '/coverages/';
  //   return this.coverageService.getHomeQuoteCoverages$(uri);
  // }

  private createClientData(client: ClientDetails): Promise<any> {
    const promiseAddresses = this.clientsService.getClientAddressesAndCreateNotExisting(client)
      .then((res: ClientAddress[]) => {
        this.storageService.setStorageData('clientAddresses', JSON.parse(JSON.stringify(res)));
      })
      .catch(err => console.log(err));

    const promiseContactMethods = this.clientsService.getClientContactMethodsAndCreateNotExisting(client)
      .then((res: ClientContactMethod[]) => {
        this.storageService.setStorageData('clientContactMethods', JSON.parse(JSON.stringify(res)));
      })
      .catch(err => console.log(err));

    return Promise.all([promiseAddresses, promiseContactMethods]);
  }

  private getQuoteClients(quoteId: string): Promise<any> {
    return this.clientsService.getClientsList(quoteId).pipe(
      take(1))
      .toPromise()
      .then(res => {
        if (res.items[0] && !res.items[0].type) {
          res.items[0].type = CLIENT_TYPES.personal;
        }
        this.storageService.setStorageData('selectedClient', res.items[0]);
        this.storageService.setStorageData('clients', res.items);
      })
      .catch(err => console.log(err => console.log('CLIENTS ERR:', err)));
  }

  private updateClientDetails(clientDetailsData: ClientDetails): Promise<ClientDetails> {
    return this.apiCommonService.putByUri(clientDetailsData.meta.href, clientDetailsData)
      .toPromise()
      .then(res => res)
      .catch(err => console.log(err));
  }

  // Import Quote
  // ---------------------------------------------------------------------------
  public handleImportQuote(): void {
    // this.leaveQuote.forceConfirmation = true;
    if (this.invalidPlanQuoteField) {
      return;
    }

    this.importQuoteClick.next({
      effectiveDate: this.modelEffectiveDate,
      selectedPlans: this.selectedPlansOnQuote,
    });
  }

  private dateIsInThePast(): boolean {
    return this.modelEffectiveDate && isBefore(parseISO(this.modelEffectiveDate), startOfDay(new Date()));
  }

}
