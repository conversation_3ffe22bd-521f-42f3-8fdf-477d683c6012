import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';

import { StubFilterComponent } from 'testing/stubs/components/filter.component';
import {
    StubHintsAndWarningsLinkComponent
} from 'testing/stubs/components/hints-and-warnings-link.component';
import { StubLoaderComponent } from 'testing/stubs/components/loader.component';
import { StubModalboxComponent } from 'testing/stubs/components/modalbox.component';
import {
    StubPerfectScrollbarComponent
} from 'testing/stubs/components/perfect-scrollbar.component';
import { StubPositionFixedDirective } from 'testing/stubs/directives/position-fixed.directive';
import { StubOrderByPipe } from 'testing/stubs/pipes/order-by.pipe';
import { StubLookupsServiceProvider } from 'testing/stubs/services/lookups.service.provider';
import {
    StubOverlayLoaderServiceProvider
} from 'testing/stubs/services/overlay-loader.service.provider';
import { StubPremiumsServiceProvider } from 'testing/stubs/services/premiums.service.provider';
import { StubRateServiceProvider } from 'testing/stubs/services/rate.service.provider';

import { PlansService } from 'app/dashboard/app-services/plans.service';
import { SymbolsService } from 'app/dashboard/app-services/symbols.service';
import {
    HintsAndWarningsService
} from 'app/hints-and-warnings/services/hints-and-warnings.service';
import { StorageService } from 'app/shared/services/storage-new.service';

import { PremiumsTableComponent } from './premiums-table.component';
import { MaipArcComponent } from 'app/shared/components/maip-arc/maip-arc.component';


describe('PremiumsTableComponent', () => {
  let component: PremiumsTableComponent;
  let fixture: ComponentFixture<PremiumsTableComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      declarations: [
        PremiumsTableComponent,
        StubPositionFixedDirective,
        StubFilterComponent,
        StubLoaderComponent,
        StubOrderByPipe,
        StubModalboxComponent,
        StubPerfectScrollbarComponent,
        StubHintsAndWarningsLinkComponent,
        MaipArcComponent
      ],
      providers: [
        SymbolsService,
        PlansService,
        StubLookupsServiceProvider,
        HintsAndWarningsService,
        StubOverlayLoaderServiceProvider,
        StorageService,
        StubRateServiceProvider,
        StubPremiumsServiceProvider
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PremiumsTableComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
