import { LessorLookupComponent } from './lessor-lookup/lessor-lookup.component';
import { LookupsService } from 'app/dashboard/app-services/lookups.service';
import { HttpClient } from '@angular/common/http';
import { Component, OnInit, ViewChild } from '@angular/core';
import { Lessor, LessorLookupData } from 'app/app-model/PrefillRMV';
import { ControlContainer, NgForm } from '@angular/forms';

@Component({
    selector: 'app-lessor-selection',
    templateUrl: './lessor-selection.component.html',
    viewProviders: [{ provide: ControlContainer, useExisting: NgForm }],
    styleUrls: ['./lessor-selection.component.scss'],
    standalone: false
})
export class LessorSelectionComponent implements OnInit {

  constructor(private lookupService: LookupsService) { }
name;
fid;
lessor: LessorLookupData;

@ViewChild(LessorLookupComponent) lessorLookup;
@ViewChild('modalLessorLookup') modal;
  ngOnInit() {
  }

setSelectedLessor(l) {
  this.lessor = l;
  this.name = this.lessor.name;
  this.fid = this.lessor.fid;
}

onChangeName() {
  if (this.name) {
  this.lessorLookup.searchQuery = this.name;
  this.lessorLookup.searchType = 'name';
  this.modal.open();
  this.lessorLookup.filter();
  }
}

onChangeFid() {
  if (this.fid) {
  this.lessorLookup.searchQuery = this.fid;
  this.lessorLookup.searchType = 'fid';
  this.modal.open();
  this.lessorLookup.filter();
  }
}
}
