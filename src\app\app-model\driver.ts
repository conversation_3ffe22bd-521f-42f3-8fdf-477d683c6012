import { FilterOption } from './filter-option';
import { CoverageItemParsed } from 'app/app-model/coverage'; // -CHANGED
import { ApiResponse } from './_common';

export class DriverRmv {
  constructor(
    public checked: boolean = false,
    public firstName: string = '',
    public middleName: string = '',
    public lastName: string = '',
    public license: string = '',
    public dob: string = '',
    public resourceId: string = ''
  ) {}
}

export class DriverRmvToSend {
  constructor(
    public firstName: string = '',
    // public middleName: string = '',
    public lastName: string = '',
    public dateOfBirth: string = '',
    public license: string = '',
    public uuid: string = Math.floor((1 + Math.random()) * 0x10000000000).toString(16)
  ) {}
}

export class DriverRmvToSendWithResourceId {
  constructor(
    public resourceId: string = '',
    public firstName: string = '',
    // public middleName: string = '',
    public lastName: string = '',
    public dateOfBirth: string = '',
    public license: string = ''
  ) {}
}

export class DriverIncident {
  constructor(
    public incidentDate: string = '',
    public surchargeDate: string = '',
    public desc: string = ''
  ) {}
}

export class Driver {
  constructor(
    public coverages = {
      meta: {
        href: <string>''
      }
    },
    public dateOfBirth: string = '',
    public deferredDriver: boolean = false,
    public exclusions: any = null,
    public firstLicensed: string = '',
    public firstLicensedState: string = '',
    public firstName: string = '',
    public gender: string = '',
    public incidents = {
      meta: {
        href: <string>''
      }
    },
    public lastName: string = '',
    public licenseDate: string = '',
    public licenseNumber: string = '',
    public licenseState: string = '',
    public maritalStatus: string = '',
    public meta = { href: <string>'' },
    public middleName: string = '',
    public motorcycle: boolean = false,
    public motorcycleLicenseType: string = '',
    public motorcycleLicenseDate: string = '',
    public overideCarrier: boolean = false,
    public parentId: string = '',
    public quoteSessionId: string = '',
    public relationshipToInsured: string = '',
    public resourceId: string = '',
    public resourceName: string = 'Driver',
    public sdip: any = ''
  ) {}
  orderIndex: number;
}

export class ComDriver {
  constructor(
    public resourceId: string = '',
    public resourceName: string = 'ComDriver',
    public meta = { href: <string>'' },
    public firstName: string = '',
    public lastName: string = '',
    public dateOfBirth: string = '',
    public licenseNumber: string = '',
    public licenseState: string = '',
    public driveOtherCar: boolean = false,
    public parentId: string = '',
    public quoteSessionId: string = ''
  ) {}
  orderIndex;
}

export class ComDriverApiResponseData extends ApiResponse<ComDriver> { }

export class RMVFirstLicenseOptions {
  constructor(
    public type: string = null,
    public label: string = null,
    public date: string = null,
    public selected: boolean = false
  ) {}
}

export class RmvDriverResult {
  constructor(
    public resourceId: string = null,
    public firstName: string = null,
    public lastName: string = null,
    public middleName: string = null,
    public dateOfBirth: string = null,
    public gender: string = null,
    public licenseNumber: string = null,
    public licenseFirstDate: string = null,
    public licenseState: string = null,
    public sdip: string = null,
    public unpaid: string = null,
    public totalPremium: string = null,
    public address1: string = null,
    public address2: string = null,
    public city: string = null,
    public state: string = null,
    public zip: string = null,
    public licenseStatus: string = null,
    public licenseExpirationDate: string = null,
    public trainingDate: string = null,
    public trainingStatus: boolean = false,
    public policyNumber: string = null,
    public policyStatus: string = null,
    public policyCarrier: string = null,
    public policyEffectiveDate: string = null,
    public policyExpirationDate: string = null,
    public incidents: Array<RmvDriverIncident> = [],
    public updDetails: RmvUpdDetail[] = [],
    public messages: RmvDriverResultMessage[] = [],
    public firstLicenseOptions: RMVFirstLicenseOptions[] = []
  ) {}
}

export class RmvDriverResultMessage {
  constructor(message: string = null) {}
}

export class RmvDriverIncident {
  constructor(
    public incidentDate: string = null,
    public surchargeDate: string = null,
    public typeOfIncident: string = null,
    public amountPaid: string = null,
    public descOfIncident: string = null
  ) {}
}

export class RmvUpdDetail {
  constructor(
    public number: string = null,
    public type: string = null,
    public unpaid: string = null,
    public carrier: string = null,
    public status: string = null,
    public statusEffectiveDate: string = null,
    public effectiveDate: string = null,
    public expirationDate: string = null,
    public upthDetails: RmvUpthDetail[] = []
  ) {}
}

export class RmvUpthDetail {
  constructor(
    public status: string = null,
    public reason: string = null,
    public effectiveDate: string = null,
    public unpaid: string = null,
    public agentCode: string = null,
    public transactionDate: string = null
  ) {}
}

export class DriverOptionsAndCoverages {
  constructor(
    public driverResourceId: string = '',
    public diver: Driver = new Driver(),
    public options: CoverageItemParsed[] = []
  ) {}
}
export class DFLDriver {
  constructor(init?: Partial<DFLDriver>) {
    Object.assign(this, init);
  }
  public resourceId: string;
  public firstname: string;
  public lastname: string;
  public licenseNumber: string;
  public options: FilterOption[];
  public selectedOption: FilterOption;
  public customDate: string;

}
