import { fakeAsync, inject, TestBed, tick } from '@angular/core/testing';

import { data as DRIVER_INCIDENT_TYPES } from 'testing/data/specs/driver-incidents-types';
import { data as INTEGRATION_VENDORS } from 'testing/data/specs/integration-vendors';
import { data as LOSS_HISTORY_REQUIREMENTS } from 'testing/data/specs/loss-history-requirements';
import { data as DRIVER_RATING_COVERAGES } from 'testing/data/specs/rating-coverages/AUTOP/driver';
import {
    HOME_GENERAL_POLICY_OPTIONS
} from 'testing/data/specs/rating-coverages/HOME/policy-options-general';
import { MA_TOWNS } from 'testing/data/specs/rating-states/ma-towns';
import { DataCustomMatchers, expectLastConnectionUrl } from 'testing/helpers/all';
import { MockBackend, setupMockBackend } from 'testing/setups/mock-backend';

import { SpecsService } from './specs.service';

describe('Service: Specs', () => {
  let service: SpecsService;
  let mockBackend: MockBackend;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        SpecsService
      ]
    });

    jasmine.addMatchers(DataCustomMatchers);
  });

  beforeEach(inject(
    [SpecsService],
    (_service: SpecsService) => {
      service = _service;
    }));

  it('can instantiate service when inject service', () => {
    expect(service).toBeTruthy();
  });

  it('provides a way to retrieve possible driver incident types', fakeAsync(() => {
    mockBackend = setupMockBackend(DRIVER_INCIDENT_TYPES);

    service.getIncidentsSpecsTypes().subscribe();
    tick();

    expect(mockBackend.connectionsArray.length).toEqual(1);
    expectLastConnectionUrl(mockBackend).toEndWith('/specs/driverIncidents/types');
  }));

  it('provides a way to retrieve rating coverages', fakeAsync(() => {
    mockBackend = setupMockBackend(DRIVER_RATING_COVERAGES);

    service.getRatingCoverages('state', 'lob').subscribe();
    tick();

    expect(mockBackend.connectionsArray.length).toEqual(1);
    expectLastConnectionUrl(mockBackend).toEndWith('/specs/ratingCoverages/state/state/lob/lob?coverageGroups=&ratingPlans=');
  }));

  it('provides a way to retrieve rating coverages for specified form type', fakeAsync(() => {
    mockBackend = setupMockBackend(HOME_GENERAL_POLICY_OPTIONS);

    service.getRatingCoverages('state', 'lob', undefined, undefined, 'HO3').subscribe();
    tick();

    expect(mockBackend.connectionsArray.length).toEqual(1);
    expectLastConnectionUrl(mockBackend).toEndWith('/specs/ratingCoverages/state/state/lob/lob?coverageGroups=&ratingPlans=&formType=HO3');
  }));

  it('provides a way to retrieve towns in states', fakeAsync(() => {
    mockBackend = setupMockBackend(MA_TOWNS);

    service.getTownsList('state').subscribe();
    tick();

    expect(mockBackend.connectionsArray.length).toEqual(1);
    expectLastConnectionUrl(mockBackend).toEndWith('/specs/ratingStates/state/towns?lobs=');
  }));

  it('provides a way to retrieve integration vendors export options', fakeAsync(() => {
    mockBackend = setupMockBackend(INTEGRATION_VENDORS);

    // why everywhere else optional lob is empty string, but here is 'autop'?
    service.getExportOptions().subscribe();
    tick();

    expect(mockBackend.connectionsArray.length).toEqual(1);
    expectLastConnectionUrl(mockBackend).toEndWith('/specs/integrationVendors?lob=autop&direction=export');
  }));

  it('provides a way to retrieve loss history requirements', fakeAsync(() => {
    mockBackend = setupMockBackend(LOSS_HISTORY_REQUIREMENTS);

    service.getLossHistoryRequirements().subscribe();
    tick();

    expect(mockBackend.connectionsArray.length).toEqual(1);
    expectLastConnectionUrl(mockBackend).toEndWith('/specs/lossHistoryRequirements?ratingPlans=');
  }));
});
