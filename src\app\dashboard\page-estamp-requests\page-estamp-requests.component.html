<section class="section section--thick">
  <div class="row">
    <div class="col-xs-12">
      <div class="u-flex u-flex--to-middle">
        <app-search-by-name (filterByName)="filterByName($event)" [lob]="'Commercial'" (resetSearch)="resetSearch()">
  </app-search-by-name>
      </div>
    </div>
  </div>
  <span style="color: red" *ngIf="nameSearchQuery && nameSearchQuery.length < 3">Minimum search length is 3</span>
</section>

<section class="section">
  <div class="row">
    <div class="col-xs-12">

      <app-filter *ngIf="filterStatusOptions?.length" [id]="'filter_Status'" [name]="'Status'" [hasSearch]="false" [options]="filterStatusOptions" [selectedOption]="filterStatusSelectedOption" [label]="filterStatusLabel" (onChange)="onFilterDataChange($event)"></app-filter>

      <app-filter-dates (getByFilter)="getStampsFromFilteredDates()"></app-filter-dates>

      <app-filter *ngIf="filterLocationsOptions?.length" [id]="'filter_locations'" [name]="'Locations'"
        [hasSearch]="true" [options]="filterLocationsOptions" (onChange)="onFilterDataChange($event)"></app-filter>

      <span class="u-float-right" style="margin-right: -10px;"><i class="fa fa-retweet"></i>
          <a class="u-color-slate-grey u-t-upper" (click)="refreshPage()">Refresh List</a>
      </span>

    </div>

    <div class="u-float-right" *ngIf="!isDashboard">


      <div class="trash trash--select u-float-left">
        <span class="u-t-upper u-color-slate-grey">
          <label class="o-checkable">
            <input type="checkbox" class="o-btn--checkbox u-show-inline" (click)="toggleAll()"
              [checked]="isChecked()" />
            <i class="tooltip__menu-icon tooltip__menu-icon_inner o-btn o-btn--checkbox "></i>
            Select All
          </label>
        </span>
      </div>
      <div class="trash trash--img u-float-right">
        <a class="u-t-upper u-color-slate-grey" (click)="openModal()">Delete</a>
      </div>
    </div>
    <app-modalbox #deleteModalbox>
      <h2 class="u-t-size--1-5rem u-color-sunset u-align-left">Delete Estamp Request?</h2>
      <p class="u-spacing--1-5 u-spacing--bottom-2 u-align-left">
        Are you sure you want to delete these eStamp Requests?
      </p>

      <hr>

      <div class="row u-spacing--2">
        <div class="col-xs-12 u-align-right">
          <button (click)="deleteSelectedEstampRequest()" class="o-btn u-spacing--right-1">Yes, Delete Requests</button>
          <button (click)="cancel()" class="o-btn o-btn--idle">Cancel</button>
        </div>
      </div>
    </app-modalbox>
  </div>
</section>

<section class="section">
  <div class="row">
    <div class="col-xs-12">
      <table class="table table--compact table--hoverable-grey">
        <thead class="table__thead">
          <tr class="">
            <th class="table__th u-width-45px" *ngIf="!isDashboard"></th>
            <th class="table__th ">Name</th>
            <th class="table__th ">Date</th>
            <th class="table__th ">Requester</th>
            <th class="table__th ">Type</th>
            <th class="table__th ">Status</th>
          </tr>
        </thead>
        <tbody class="table__tbody" *ngIf="arrEstampRequestsAll">
          <tr class="table__tr" *ngFor="let row of arrEstampRequestsAll">
            <td class="table__td" *ngIf="!isDashboard">
              <label class="o-checkable u-float-right">
                <input type="checkbox" class="o-btn--checkbox" [checked]="isSelected(row.stampRequestId)"
                  (click)="toggleStamps(row.stampRequestId)" />
                <i class="o-btn o-btn--checkbox"></i>
              </label>
            </td>
            <td class="table__td u-color-pelorous">
                <a (click)="checkStatus(row.stampRequestId, row.transactionType, row.requesterType)">{{ row.customerFirstName }} {{ row.customerLastName }}
                  <span *ngIf="row.customerCompanyName">({{row.customerCompanyName}})</span>
                </a>
                <app-modalbox #paymentAlreadyRequestedModalbox>
                  <h2 class="o-heading">Insurance Verified</h2>
        <div class="box box--silver u-spacing--1-5">
          <p>Insurance has already been verified and a payment request has been sent.</p>
          <p>Would you still like to proceed with this transaction?</p>


        </div>
        <div class="row u-spacing--2">
          <div class="col-xs-12 u-align-right">
            <button class="o-btn" (click)="validatePlate()">Yes</button>

            <button class="o-btn o-btn--idle u-spacing--left-2"
              (click)="cancel()">No</button>
          </div>
        </div>
                </app-modalbox>
              </td>
                <td class="table__td">{{ parseLastModifiedDate(row.receiveDate) }}</td>
                <td class="table__td">
                  <span *ngIf="row.requesterType === 'Buyer'"> {{row.customerFirstName}} {{row.customerLastName}}</span>
                  <span *ngIf="row.requesterType === 'Dealer'">{{row.dealerName}}</span>
                </td>
                <td class="table__td">{{ row.transactionType }}</td>
                <td class="table__td">{{splitWords(row.status) }}</td>
          </tr>
        </tbody>
        <tbody class="table__tbody" *ngIf="!arrEstampRequestsAll?.length">
          <tr class="table__tr">
            <td colspan="5">
              <p class="u-padd--bottom-1 u-padd--1">
                There are no results that match your search.
              </p>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</section>

<section class="section">
  <div class="u-flex u-flex--to-middle">
    <div class="">
      <app-pagination
        [currentPage]="paginationCurrentPage"
        [totalRecords]="paginationResultsCount"
        [recordsLimit]="paginationResultLimit"
        (onPageChange)="paginationPageChange($event)"
      ></app-pagination>
    </div>
    <div class="" style="padding-left:10rem">
      <app-results-limiter
        [description]="'Stamps to show'"
        (onChange)="onResultLimitChange($event)"
      ></app-results-limiter>
    </div>
  </div>
  <app-modalbox #registration>
    <div *ngIf="!rmvExpired && !unauthorizedError; then rmvResponse else noAccess"></div>
    <ng-template #rmvResponse>
       <app-aside-registration-response (agencyChange)="getAgencies.open();registration.close()" [fromEstamp]="true" *ngIf="validationData"[data]="validationData" [transaction]="transactionType" [modalbox]="registration"
    [ownerDob]="validationData.ownerDob" [ownerEmail]="validationData.ownerEmail" [ownerFid]="validationData.ownerFid"
    ></app-aside-registration-response>
    </ng-template>
    <ng-template #noAccess>
      <h1 class="o-heading o-heading--red">Unauthorized</h1>
      <div class="box box--silver">
        <p>You do not have access to view this information.</p>
      </div>
      <div class="row u-spacing--2">
        <div class="col-xs-12 u-align-right">
            <button (click)="registration.close()" class="o-btn o-btn--idle u-spacing--left-1-5">Close</button>
        </div>
    </div>
    </ng-template>

  </app-modalbox>

  <app-modalbox #getAgencies [css]="'u-width-750px horizontal-centered'" (click)="$event.stopPropagation()">
    <app-change-agency (agencyChange)="this.refreshPage()" [modalbox]="getAgencies" [stampId]="selectedStampId" [requesterType]="'Buyer'"></app-change-agency>
  </app-modalbox>



