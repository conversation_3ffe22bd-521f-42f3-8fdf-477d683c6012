import { async, ComponentFixture, inject, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { RouterTestingModule } from '@angular/router/testing';

import { MA_TOWNS } from 'testing/data/specs/rating-states/ma-towns';
import { StubAutocompleteComponent } from 'testing/stubs/components/autocomplete.component';
import { StubConfirmboxComponent } from 'testing/stubs/components/confirmbox.component';
import { StubModalboxComponent } from 'testing/stubs/components/modalbox.component';
import { StubClientsServiceProvider } from 'testing/stubs/services/clients.service.provider';
import { StubCoveragesServiceProvider } from 'testing/stubs/services/coverages.service.provider';
import { StubDwellingServiceProvider, StubDwellingService } from 'testing/stubs/services/dwelling.service.provider';
import {
    StubOverlayLoaderServiceProvider
} from 'testing/stubs/services/overlay-loader.service.provider';
import {
    StubSpecsService, StubSpecsServiceProvider
} from 'testing/stubs/services/specs.service.provider';
import { StubSubsServiceProvider } from 'testing/stubs/services/subs.service.provider';

import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Helpers } from 'app/utils/helpers';

import { DwellingDwellingInformationComponent } from './dwelling-dwelling-information.component';
import { DWELLING_LOCATION } from 'testing/data/quotes/locations/dwelling';
import { DWELLING_QUOTE_PLAN_LIST } from 'testing/data/quotes/quote-plan-list/dwelling';
import { DWELLINGS } from 'testing/data/quotes/dwellings';
import { DWELLING_QUOTE } from 'testing/data/quotes/quote-dwelling';
import { DWELLING_PROTECTION_CLASS } from 'testing/data/dwellings/protection-class';
import { DWELLING_COVERAGES } from 'testing/data/quotes/coverages/dwelling';
import { DwellingService } from 'app/dashboard/app-services/dwelling.service';
import { By } from '@angular/platform-browser';
import { changeTextInputValue } from 'testing/helpers/all';
import { HOME_COVERAGE_DEFAULTS } from 'testing/data/subs/coverage-defaults/home-policy';

describe('Component: DwellingDwellingInformation', () => {
  let component: DwellingDwellingInformationComponent;
  let fixture: ComponentFixture<DwellingDwellingInformationComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [
        FormsModule,
        RouterTestingModule
      ],
      declarations: [
        DwellingDwellingInformationComponent,
        StubAutocompleteComponent,
        StubModalboxComponent,
        StubConfirmboxComponent
      ],
      providers: [
        StorageService,
        StubSubsServiceProvider,
        StubDwellingServiceProvider,
        StubOverlayLoaderServiceProvider,
        StubSpecsServiceProvider,
        StubClientsServiceProvider,
        StubCoveragesServiceProvider
      ]
    })
    .compileComponents();
  }));

  beforeEach(fakeAsync(inject([StorageService, SpecsService],
    (specsService: StubSpecsService,   storageService: StorageService) => {
      specsService.townsList = Helpers.deepClone(MA_TOWNS);
      storageService.setStorageData('dwellingLocation', Helpers.deepClone(DWELLING_LOCATION.items[0]));
      storageService.setStorageData('homeQuoteCoverages', Helpers.deepClone(DWELLING_COVERAGES));
      storageService.setStorageData('homeDefaultOptions', Helpers.deepClone(HOME_COVERAGE_DEFAULTS));
      storageService.setStorageData('selectedPlan', Helpers.deepClone(DWELLING_QUOTE_PLAN_LIST));
      storageService.setStorageData('selectedQuoteFormTypes', Helpers.deepClone(['']));
      storageService.setStorageData('dwelling', Helpers.deepClone(DWELLINGS.items[0]));
      storageService.setStorageData('selectedQuote', Helpers.deepClone(DWELLING_QUOTE));
      storageService.setStorageData('dwellingProtectionClass', Helpers.deepClone(DWELLING_PROTECTION_CLASS.items[0]));

      fixture = TestBed.createComponent(DwellingDwellingInformationComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
      tick();
  })));

  it('should be created', () => {
    expect(component).toBeTruthy();
  });

  it('should destroy without errors', () => {
    expect(() => {
      fixture.destroy();
    }).not.toThrow();
  });

  describe('when updating dwelling information', () => {
    const inputs: { [k: string]: HTMLInputElement } = {};
    const autocompletes: { [k: string]: StubAutocompleteComponent } = {};
    let dwellingService: StubDwellingService;

    beforeEach(inject([DwellingService], (_dwellingService: StubDwellingService) => {
      dwellingService = _dwellingService;

      spyOn(dwellingService, 'updateDwellingLocation').and.callThrough();
      spyOn(dwellingService, 'updateDwellingProtectionClass').and.callThrough();
      spyOn(dwellingService, 'updateDwelling').and.callThrough();

      ['#dwellingCounty', '#dwellingZip', '#isopc', '#constructionYear', '#dwellingAge', '#isVacant',
        '#isNonOwnerOccupancy', '#isTownhouse', '#isUnderConstruction', '#isMobileHome'].forEach((item) => {
          inputs[item] = fixture.debugElement.query(By.css(item)).nativeElement;
        });

      ['#dwellingCity', '#constructionMaterial', '#utilizationTypeCode', '#families', '#townhouseUnitsCount'].forEach((item) => {
          autocompletes[item] = fixture.debugElement.query(By.css(item)).componentInstance;
        });
    }));

    it('updates dwellingProtectionClass and dwellingLocation if city changes', fakeAsync(() => {
      autocompletes['#dwellingCity'].activeOption = component.cities[0];

      fixture.detectChanges();
      tick(50);

      expect(dwellingService.updateDwellingProtectionClass).toHaveBeenCalled();
      expect(dwellingService.updateDwellingLocation).toHaveBeenCalled();
    }));

    it('updates dwellingProtectionClass and dwellingLocation if zip changes', fakeAsync(() => {
      changeTextInputValue(inputs['#dwellingZip'], '54321', fixture);

      expect(component.dwellingLocation.zip).toEqual('54321');
      expect(dwellingService.updateDwellingProtectionClass).toHaveBeenCalled();
      expect(dwellingService.updateDwellingLocation).toHaveBeenCalled();
    }));

    it('updates dwelling if construction changes', fakeAsync(() => {
      autocompletes['#constructionMaterial'].activeOption = component.construction[0];

      fixture.detectChanges();
      tick(50);

      fixture.detectChanges();
      tick(300);

      expect(dwellingService.updateDwelling).toHaveBeenCalled();
    }));

    it('updates dwelling if construction year changes', fakeAsync(() => {
      changeTextInputValue(inputs['#constructionYear'], '1879', fixture);

      expect(component.dwelling.constructionYear).toEqual('1879');

      fixture.detectChanges();
      tick(300);

      expect(dwellingService.updateDwelling).toHaveBeenCalled();
    }));

    it('updates dwelling age if construction year changes', fakeAsync(() => {
      changeTextInputValue(inputs['#constructionYear'], '1879', fixture);

      fixture.detectChanges();
      tick(300);

      expect(inputs['#dwellingAge'].value).toEqual(((new Date()).getFullYear() - 1879).toString());
    }));

    it('updates dwelling if construction age changes', fakeAsync(() => {
      changeTextInputValue(inputs['#dwellingAge'], '50', fixture);

      expect(component.dwelling.constructionAge).toEqual('50');

      fixture.detectChanges();
      tick(300);

      expect(dwellingService.updateDwelling).toHaveBeenCalled();
    }));

    it('updates construction year if dwelling age changes', fakeAsync(() => {
      changeTextInputValue(inputs['#dwellingAge'], '50', fixture);

      fixture.detectChanges();
      tick(300);

      expect(inputs['#constructionYear'].value).toEqual(((new Date()).getFullYear() - 50).toString());
    }));
  });
});
