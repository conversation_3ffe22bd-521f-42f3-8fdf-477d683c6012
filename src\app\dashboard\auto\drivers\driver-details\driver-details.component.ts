import { take, first } from 'rxjs/operators';
import { STATES } from './../../../app-services/state.service';
import { Component, OnInit, OnDestroy, ViewChild } from '@angular/core';
import { Observable, SubscriptionLike as ISubscription } from 'rxjs';
import { Router, ActivatedRoute } from '@angular/router';
import { DriversService } from 'app/dashboard/app-services/drivers.service';
import { IncidentsService } from 'app/dashboard/app-services/incidents.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { VehiclesService } from 'app/dashboard/app-services/vehicles.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Driver, DriverRmv } from 'app/app-model/driver';
import {
  Incident,
  IncidentsResponse,
  IncidentTypeResponse
} from 'app/app-model/incident';
import { Vehicle, VehicleCoverages } from 'app/app-model/vehicle';
import { Quote, QuotePlan } from 'app/app-model/quote';
import { ClientDetails } from 'app/app-model/client';
import { ClientsService } from 'app/dashboard/app-services/clients.service';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { RouteService } from 'app/shared/services/route.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { PremiumsService } from 'app/dashboard/auto/premiums/premiums.service';

import { Validate } from 'app/hints-and-warnings/validators';
import { LeaveQuoteComponent } from 'app/shared/components/leave-quote/leave-quote.component';
import { ModalboxComponent } from 'app/shared/components/modalbox/modalbox.component';
import { differenceInYears, format, parseISO } from 'date-fns';



interface IVehicleToExclude extends Vehicle {
  exclude: boolean;
  canNotBeExcluded: boolean;
  isExcluded: boolean;
  nameToDisplay: string;
}

interface IDriverExclusionItem {
  meta: { href: string };
}

@Component({
    selector: 'app-driver-details',
    templateUrl: './driver-details.component.html',
    styleUrls: ['./driver-details.component.scss'],
    standalone: false
})
export class DriverDetailsComponent implements OnInit, OnDestroy {

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private driversService: DriversService,
    private incidentsService: IncidentsService,
    private specsService: SpecsService,
    private storageService: StorageService,
    private vehiclesService: VehiclesService,
    private clientsService: ClientsService,
    private overlayLoaderService: OverlayLoaderService,
    private routeService: RouteService,
    private apiCommonService: ApiCommonService,
    private premiumsService: PremiumsService
  ) {}
  private subscriptionIncidentsTypesAndDesc: ISubscription;

  private quote: Quote;
  private quoteId = '';
  private driverId = '';

  private userId = '0';
  public drivers: Driver[] = [];
  private modalTmpDrivers: Driver[] = this.drivers;
  public selectedDriver: Driver = new Driver();
  public licenseStates = STATES;
  public arrGenders: Array<string> = ['Female','Male'];
  public arrMaritalStatuts: Array<string> = ['Married', 'Single'];
  public driverRelations = [
    'Child',
    'Husband',
    'Insured',
    'Other Relationship',
    'Parent',
    'Spouse',
    'Wife'
  ];
  public modalDriversInsuranceUpdate: Array<any>;
  public driverWithoutSelectedOne: Driver[] = [];
  public modalDriverWithoutSelectedOneOptions = [];
  public incidents: Array<Incident> = [];
  private dateNow = new Date().toISOString();
  public incidentTypes: Array<any> = [];

  public addIncidentData = {
    date: '',
    types: [],
    amount: '',
    desc: []
  };

  public addIncidentDataSelected = {
    surchargeDate: '',
    typeOfIncident: '',
    amountPaid: '',
    descOfIncident: ''
  };

  public editIncidentDataSelected = this.addIncidentDataSelected;
  public deleteIncidentDataSelected;
  public deleteIncidentDataSelectedAskAbout;
  public incidentTypesNotRequireAmount = ['Minor Violation', 'Major Violation'];

  // public vehicles: any = {};
  public vehicles: Vehicle[] = [];
  public vehiclesExclusionAvailable = [];
  public vehiclesExclusionUnavailable = [];

  public rmvLookupFormDrivers: DriverRmv[] = [];

  private subscription;
  private vehiclessubscription;
  private vehiclesStorageSubscription;
  private driverStorageIncidentsSubscription;
  private storageDriversSubscription;
  private routeSubsciptionSelectDriver;
  private routeSubscription;
  private routerSubscription;
  private importedSubscription;
  private quotePlasSubscription: ISubscription;
  private subscriptionClients: ISubscription;
  private loadingIncidents = false;

  private quoteSelectedPlans: QuotePlan[] = [];
  private quoteSelectedPlansIds: string[] = [];

  private isImported = false;
  disableButton = false;
  effectiveDateMsgAlreadyDisplayed: any;

  @ViewChild('leaveQuote') leaveQuote: LeaveQuoteComponent;
  @ViewChild('refModalExclusions') refModalExclusions: ModalboxComponent;
  @ViewChild('modalQuoteEffectiveDate') public modalQuoteEffectiveDay: ModalboxComponent;

  private showSaveConfirmation = false;

  private arrDriversRMV = [
    {
      name: 'James Smith',
      dateOfBirth: '1972-07-05T10:25:43.511Z', // '7/5/1972',
      licenseNumber: '*********',
      selected: true
    },
    {
      name: 'John Clubber',
      dateOfBirth: '1959-01-18T10:25:43.511Z', // '1/18/1959',
      licenseNumber: '*********',
      selected: true
    }
  ];

  public checkboxCarrierModalIsOpen = false;

  private allowDriverDataUpdate = true;

  public helperForViewEditSelectedIncidentType = null; // HOT FIX: This helper should be removed in the future and functionality needs to be adjusted.

  // Handle Quote Client Menagment
  // -------------------------------------------------------------------------
  private clientAllowUpdateClientData = false;
  private clientDetails: ClientDetails;
  private clientDetailsAssignedToQuote = false;

  // Vehicle Exclusions
  // ---------------------------------------------------------------------------
  public excludedVehicles: IVehicleToExclude[] = [];
  public modalExclusionsVehicles: IVehicleToExclude[] = [];
  public modalExclusionsVehiclesToExclude: IVehicleToExclude[] = [];
  public modalExclusionsVehiclesCanNotBeExcluded: IVehicleToExclude[] = [];

  canDeactivate(): Observable<boolean> | boolean {
    console.log('Can Deactivate');
    this.leaveQuote.detectConfirmation();
    return this.leaveQuote.detectConfirmationObservable
      .asObservable()
      .pipe(first());
  }
  ngOnInit() {
    this.subscribeSelectedPlans();
    this.routeService.showSaveConfirmation$.subscribe(
      data => (this.showSaveConfirmation = data)
    );
    this.subscription = this.storageService
      .getStorageData('selectedQuote')
      .subscribe(res => {
        this.quote = res;
        if (res && res.resourceId) {
          this.quoteId = res.resourceId;
  const comparisonDate = new Date('2025-07-01');
          const QuoteEffectiveDate = new Date(this.quote.effectiveDate);
          this.storageService.getStorageData('isNewQuote').subscribe((isNewQuote: boolean) => {
          this.storageService.getStorageData('autoCoveragesStandardForVehicles').subscribe((res: VehicleCoverages[]) => {
            const BI = res[0].options.find(x => x.coverageCode === 'BI').currentValue === '25/50';

            if (QuoteEffectiveDate >= comparisonDate && !this.effectiveDateMsgAlreadyDisplayed && BI && !isNewQuote) {
            this.modalQuoteEffectiveDay?.open();

            this.effectiveDateMsgAlreadyDisplayed = true;
          }

        });
      });

          this.subscribeVehicles();
          this.subscribeDrivers();
          this.loadCurrentDriverFromUrl();
          this.rmvInitDefaultForm();
          this.trimDriverNameSpaces();

          // TODO-FIX::
          // (this.selectedDriver && this.selectedDriver.meta.href.length > 0) ? this.saveDriverData(this.selectedDriver) : null;

          this.getCurrentDriverIncidents();
          // this.subscribeRouter();
          // this.getIncidentsTypesAndDescs();
          this.subscribeIncidentsTypesAndDesc();
          this.handleQuoteClient();

          this.getDriversFromAPI();
        }
        // console.log('LOADED QUOTE INIT:', this.quoteId, this.quote);
      });
      const isGenderX = JSON.parse(localStorage.getItem('features')).find(x => x.name === 'SprApp_GenderUpdates');
     if(isGenderX) { this.arrGenders.push('Non-Binary','Unknown')}
  }

  ngOnDestroy() {
    this.subscription && this.subscription.unsubscribe();
    this.vehiclessubscription && this.vehiclessubscription.unsubscribe();
    this.vehiclesStorageSubscription &&
      this.vehiclesStorageSubscription.unsubscribe();
    this.routerSubscription && this.routerSubscription.unsubscribe();
    this.storageDriversSubscription &&
      this.storageDriversSubscription.unsubscribe();
    this.routeSubsciptionSelectDriver &&
      this.routeSubsciptionSelectDriver.unsubscribe();
    this.routeSubscription && this.routeSubscription.unsubscribe();
    this.driverStorageIncidentsSubscription &&
      this.driverStorageIncidentsSubscription.unsubscribe();
    this.importedSubscription && this.importedSubscription.unsubscribe();
    this.quotePlasSubscription && this.quotePlasSubscription.unsubscribe();
    this.subscriptionClients && this.subscriptionClients.unsubscribe();
    this.subscriptionIncidentsTypesAndDesc &&
      this.subscriptionIncidentsTypesAndDesc.unsubscribe();
  }

  private subscribeSelectedPlans(): void {
    this.quotePlasSubscription = this.storageService
      .getStorageData('selectedPlan')
      .subscribe(plans => {
        if (plans && plans.items && plans.items.length) {
          this.quoteSelectedPlans = plans.items[0].items;
          this.quoteSelectedPlansIds = this.quoteSelectedPlans.map(
            plan => plan.ratingPlanId
          );
        }
      });
  }

  private loadCurrentDriverFromUrl() {
    if (this.route && this.route.params) {
      this.routeSubsciptionSelectDriver = this.route.params.subscribe(
        params => {
          this.driverId = params['id'];
          if (this.driverId && this.driverId.length > 0) {
            this.selectDriver(this.driverId);
          }
        }
      );
    }
  }

  public prepareFullnameAskAbout(selectedDriver) {
    let firstName = '';
    let lastName = '';
    if (selectedDriver.firstName) {
      firstName = selectedDriver.firstName;
    }
    if (selectedDriver.lastName) {
      lastName = ' ' + selectedDriver.lastName;
    }
    return firstName + lastName;
  }

  public setDefaultDriverLicenseState() {
    if (
      !this.selectedDriver.licenseState ||
      this.selectedDriver.licenseState === ''
    ) {
      this.selectedDriver.licenseState = 'MA';
      this.saveDriverDataIndependently(this.selectedDriver);
    }
  }

  public checkValidMALicense(selectedDriver: Driver) {
    const regex = /^[Ss](A|a)[0-9]{7}$|^[Ss][0-9]{8}$/;
      const isValid = regex.test(selectedDriver.licenseNumber);

      if(!isValid && selectedDriver.licenseState === 'MA') {
        return true
      }
      return false
  }

  private reloadCurrentDriverOnDriversUpdate(): void {
    if (
      this.selectedDriver.resourceId &&
      this.selectedDriver.resourceId === this.driverId
    ) {
      const updatedDriver = this.drivers.find(
        driver => this.driverId === driver.resourceId
      );

      if (
        updatedDriver &&
        JSON.stringify(this.selectedDriver) !== JSON.stringify(updatedDriver)
      ) {
        this.selectDriver(this.driverId);
      }
    }
  }

  private subscribeDrivers() {
    this.storageDriversSubscription &&
      this.storageDriversSubscription.unsubscribe();
    this.storageDriversSubscription = this.storageService
      .getStorageData('driversList')
      .subscribe(data => {
        this.drivers = data;

        this.reloadCurrentDriverOnDriversUpdate();
      });
  }

  private subscribeVehicles() {
    this.vehiclesStorageSubscription &&
      this.vehiclesStorageSubscription.unsubscribe();
    this.vehiclesStorageSubscription = this.storageService
      .getStorageData('vehiclesList')
      .subscribe(vehicles => {
        this.vehicles = vehicles;
        this.setExcludedVehicles(); // Consider if this is needed here
      });
  }

  private selectDriver(id: string) {
    if (this.drivers && this.drivers.length) {
      const tmpDriver: Driver[] = this.drivers.filter(
        driver => driver.resourceId === id
      );
      if (tmpDriver.length) {
        this.selectedDriver = tmpDriver[0];
        this.driversService.tempDriver = this.selectedDriver;
        this.setExcludedVehicles();
        this.getCurrentDriverIncidents();
        this.setDefaultDriverLicenseState();
        // console.log('SelectedDriver:', Object.assign({},this.selectedDriver));
      } else {
        if (
          this.drivers.length &&
          this.drivers[0] &&
          this.drivers[0].meta.href
        ) {
          this.router.navigateByUrl(
            '/dashboard/auto' + this.drivers[0].meta.href
          );
        }
      }
    }
  }

  private filterDriversWithoutSelected(): void {
    this.driverWithoutSelectedOne = this.drivers.filter(
      driver => driver.resourceId !== this.selectedDriver.resourceId
    );
    this.modalDriverWithoutSelectedOneOptions = [];

    this.driverWithoutSelectedOne.forEach(el => {
      this.modalDriverWithoutSelectedOneOptions.push({
        id: el.resourceId,
        text: el.firstName + ' ' + el.lastName
      });
    });
  }

  public checkboxCarrierModalHandler() {
    if (!this.selectedDriver.overideCarrier) {
      this.checkboxCarrierModalIsOpen = true;
    } else {
      this.selectedDriver.overideCarrier = false;
      this.saveDriverDataIndependently(this.selectedDriver);
    }
  }

  public modalStateChange($event) {
    this.checkboxCarrierModalIsOpen = $event.isOpen;
  }

  public ignoreCarrierModalAccept($ev) {
    this.selectedDriver.overideCarrier = true;
    this.saveDriverDataIndependently(this.selectedDriver);
  }
  private saveDriverData(data) {
    // this.driversService.updateDriver(data).subscribe()
    if (this.allowDriverDataUpdate) {
      this.allowDriverDataUpdate = false;
      setTimeout(() => {
        // console.log('Call UPDATE DRIVER');
        this.driversService.updateDriver(data).subscribe(
          res => {
            this.allowDriverDataUpdate = true;
          },
          err => (this.allowDriverDataUpdate = true),
          () => (this.allowDriverDataUpdate = true)
        );
      }, 300);
    }
  }

  public saveDriverDataIndependently(data) {
    if (data && data.resourceId) {
      this.driversService.updateDriverIndependently(data).subscribe();
    }
  }

  public updateDateObjectProperty(
    $event,
    obj,
    property,
    updateDriver: boolean = true
  ) {
    obj[property] = $event.formatedDate || '';
    if (($event.selectedManually || $event.selectByClick) && updateDriver) {
      this.saveDriverData(obj);
    }
  }

  public selectMotorcycle(isMotorcycleDriver: boolean) {
    if (!isMotorcycleDriver) {
      this.selectedDriver.motorcycleLicenseDate = '';
      this.selectedDriver.motorcycleLicenseType = '';
      // this.saveDriverData(this.selectedDriver);
    } else {
      this.selectedDriver.motorcycle = isMotorcycleDriver;
    }
    this.saveDriverData(this.selectedDriver);
  }

  public selectMotorcycleLicenseType($event) {
    this.selectedDriver.motorcycleLicenseType = $event.id;
    if (this.selectedDriver.motorcycleLicenseType === 'Permit') {
      this.selectedDriver.motorcycleLicenseDate = '';
      // this.saveDriverData(this.selectedDriver);
    }
    this.saveDriverData(this.selectedDriver);
  }

  public selectMotorcycleLicenseDate($event) {
    if (this.selectedDriver.motorcycleLicenseType === 'License') {
      if (this.selectedDriver.motorcycleLicenseDate !== $event.formatedDate) {
        this.selectedDriver.motorcycleLicenseDate = $event.formatedDate;
        this.saveDriverData(this.selectedDriver);
      }
    }
    // this.saveDriverData(this.selectedDriver);
  }

  public addDriver() {
    this.overlayLoaderService.showLoader();
    this.driversService.createDriver(this.quoteId).subscribe(data => {
      // this.selectedDriver = data;
      this.drivers.push(data);
      this.storageService.setStorageData('driversList', this.drivers);
      this.driversService.setDriverOptions(data, {}).subscribe(res => {
        this.overlayLoaderService.hideLoader();
        this.router.navigateByUrl('/dashboard/auto' + data.meta.href);
      });
    });
  }

  public confirmCanceled($event) {
    // console.log('Cancel: ', $event);
    // dummy function to close popup
  }

  public deleteDriver() {
    this.overlayLoaderService.showLoader();
    this.driversService
      .deleteDriver(this.selectedDriver.meta.href)
      .subscribe(data => {
        let driverToRemove;
        let driverToRemoveIndex;
        // driverToRemove = this.drivers.filter( (element, index) => {
        //   if (element.resourceId === this.selectedDriver.resourceId) {
        //     driverToRemoveIndex = index;
        //   }
        // });

        driverToRemove = this.drivers.find((element, index) => {
          if (element.resourceId === this.selectedDriver.resourceId) {
            driverToRemoveIndex = index;
            return true;
          }
        });

        // Remove owner and operator from vehicles
        this.removeDeletedDriverAsVehicleOwnerAndOperator(driverToRemove);

        this.drivers.splice(driverToRemoveIndex, 1);
        this.storageService.setStorageData('driversList', this.drivers);
        this.overlayLoaderService.hideLoader();
        let url;
        if (this.drivers && this.drivers.length) {
          url = this.drivers[0].meta.href;
        } else {
          url = '/quotes/' + this.quoteId + '/drivers';
        }
        // this.router.navigate(['/dashboard' + url]);
        this.router.navigate(['/dashboard/auto' + url]);

        // update primary insured when the last added driver is deleted
        let clientDetails;
        this.storageService.getStorageData('selectedClient').subscribe(data => {
          clientDetails = data;
          clientDetails.firstName = '';
          clientDetails.lastName = '';
        });

      if(this.selectedDriver === this.drivers[0]) {  this.updateClientInfoData(clientDetails, this.quote); }
      });
  }

  private updateClientInfoData(
    selectedClient: ClientDetails,
    quote: Quote
  ): Promise<any> {
    return this.apiCommonService
      .putByUri(selectedClient.meta.href, selectedClient)
      .toPromise()
      .then(res => {
        this.storageService.updateClientsSingleItem(res);
        if (quote) {
          quote.client.firstName = res.firstName;
          quote.client.lastName = res.lastName;
          this.storageService.setStorageData('selectedQuote', quote);
        }
      })
      .catch(err => console.log('UPDATE CLIENT ERR', err));
  }

  private removeDeletedDriverAsVehicleOwnerAndOperator(driver: Driver): void {
    const vehiclesWithDriverAsOwnerDoUpdate: Vehicle[] = this.vehicles.filter(
      vehicle => {
        if (
          vehicle.owner &&
          vehicle.owner.meta &&
          vehicle.owner.meta.href &&
          driver &&
          driver.meta &&
          driver.meta.href
        ) {
          return vehicle.owner.meta.href === driver.meta.href;
        }
        return false;
      }
    );

    if (vehiclesWithDriverAsOwnerDoUpdate.length) {
      vehiclesWithDriverAsOwnerDoUpdate.forEach(vehicle => {
       vehicle.operator = vehicle.operator.meta.href === driver.meta.href ? null : vehicle.operator;
        vehicle.owner = null;

        this.storageService.updateVehiclesListSingleItem(
          vehicle.parentId,
          vehicle
        );
        this.vehiclesService.updateVehicle(this.quoteId, vehicle.resourceId, vehicle, true).subscribe();
      });
    }

    const vehiclesWithDriverAsOperatorDoUpdate: Vehicle[] = this.vehicles.filter(
      vehicle => {
        if (
          vehicle.operator &&
          vehicle.operator.meta &&
          vehicle.operator.meta.href &&
          driver &&
          driver.meta &&
          driver.meta.href
        ) {
          return vehicle.operator.meta.href === driver.meta.href;
        }
        return false;
      }
    );

    if (vehiclesWithDriverAsOperatorDoUpdate.length) {
      vehiclesWithDriverAsOperatorDoUpdate.forEach(vehicle => {
        vehicle.operator = null;
        this.storageService.updateVehiclesListSingleItem(
          vehicle.parentId,
          vehicle
        );
        this.vehiclesService.updateVehicle(this.quoteId, vehicle.resourceId, vehicle, true).subscribe();
      });
    }
  }

  private trimDriverNameSpaces() {
    const driverFirstName = this.selectedDriver?.firstName?.trim();
    const driverLastName = this.selectedDriver?.lastName?.trim();

    if(driverFirstName !== null && driverFirstName?.trim() === driverFirstName) {
          this.selectedDriver.firstName = driverFirstName;
    }

    if(driverLastName !== null && driverLastName?.trim() === driverLastName) {
      this.selectedDriver.lastName = driverLastName;
    }
  }

  private rmvToggleDriverSelection(driver) {
    driver.selected = !driver.selected;
  }

  private rmvInitDefaultForm() {
    const initRmvDriver = new DriverRmv();
    initRmvDriver.checked = true;
    this.rmvLookupFormDrivers.push(initRmvDriver);
  }

  private rmvAddAntherDriver($ev) {
    $ev.preventDefault();
    const tmpDriver = new DriverRmv();
    tmpDriver.checked = true;

    this.rmvLookupFormDrivers.push(tmpDriver);
  }

  private rmvLookupSubmitForm($ev, refNgForm) {
    console.log(refNgForm);
  }

  // Incidents
  public addIncident(modalAddIncident): void {
    this.addIncidentDataSelected.amountPaid = this.addIncidentData.amount;

    this.overlayLoaderService.showLoader();
    this.incidentsService
      .addIncident(this.selectedDriver.resourceId, this.addIncidentDataSelected)
      .subscribe(res => {
        this.storageService.updateSingleIncident(
          this.selectedDriver.incidents.meta.href,
          res
        );
        modalAddIncident.closeModalbox();
        this.overlayLoaderService.hideLoader();
        // Rerate plans
        this.premiumsService.rerateAll();
      });
  }

  private deleteIncident(modalDeleteIncident): void {
    this.overlayLoaderService.showLoader();
    this.incidentsService
      .deleteIncident(this.deleteIncidentDataSelected.meta.href)
      .subscribe(res => {
        this.overlayLoaderService.hideLoader();
        if (res) {
          this.storageService.removeIncident(
            this.selectedDriver.incidents.meta.href,
            this.deleteIncidentDataSelected
          );
          this.deleteIncidentDataSelected = null;
          // Rerate plans
          this.premiumsService.rerateAll();
        }
      });
  }

  private getCurrentDriverIncidents() {
    if (!this.loadingIncidents) {
      this.driverStorageIncidentsSubscription &&
        this.driverStorageIncidentsSubscription.unsubscribe();
      this.driverStorageIncidentsSubscription = this.storageService
        .getStorageData('driverIncidents')
        .subscribe(incidents => {
          let returnIncidents: any = false;
          if (incidents) {
            incidents.filter(incidentsObj => {
              if (
                incidentsObj.meta.href ===
                this.selectedDriver.incidents.meta.href
              ) {
                returnIncidents = incidentsObj.items;
              }
            });
          }
          this.incidents = returnIncidents;
          if (!returnIncidents) {
            // no local incidents
            this.getIncidentsFromAPI();
          }
        });
    }
  }

  private getIncidentsFromAPI(): void {
    if (
      this.selectedDriver &&
      this.selectedDriver.incidents &&
      this.selectedDriver.incidents.meta &&
      this.selectedDriver.incidents.meta.href
    ) {
      this.overlayLoaderService.showLoader();
      this.loadingIncidents = true;
      this.incidentsService
        .getIncidents(this.selectedDriver.incidents.meta.href)
        .pipe(take(1))
        .subscribe(
          (res: IncidentsResponse) => {
            if (this.incidents && this.incidents.length > 0) {
              this.incidents.concat(res.items);
            } else {
              this.incidents = res.items;
            }
            this.loadingIncidents = false;
            this.storageService.updateDriverIncidentsList(res);
            this.overlayLoaderService.hideLoader();
          },
          err => {
            this.loadingIncidents = false;
            this.overlayLoaderService.hideLoader();
          }
        );
    }
  }

  private subscribeIncidentsTypesAndDesc(): void {
    this.subscriptionIncidentsTypesAndDesc &&
      this.subscriptionIncidentsTypesAndDesc.unsubscribe();
    this.subscriptionIncidentsTypesAndDesc = this.storageService
      .getStorageData('autoDriverIncidentsTypesAndDesc')
      .subscribe((res: IncidentTypeResponse) => {
        this.incidentTypes = res && res.items ? res.items : [];
        this.setIncidentsTypesAndDescOptions();
      });
  }

  private setIncidentsTypesAndDescOptions(): void {
    const tmpOptions = [];
    this.incidentTypes.forEach(item => {
      tmpOptions.push({
        id: item.driverIncidentTypeId,
        text: this.toCamelCase(item.name)
      });
    });
    this.addIncidentData.types = tmpOptions;
    this.selectedIncidentType(this.addIncidentData.types[0]);
  }

  // private getIncidentsTypesAndDescs(): void {
  //   this.specsService.getIncidentsSpecsTypes().subscribe(
  //     res => {
  //       this.incidentTypes = res.items;
  //       // const tmpOptions = [];
  //       // this.incidentTypes.forEach( item => {
  //       //   tmpOptions.push({id: item.driverIncidentTypeId, text: this.toCamelCase(item.name)});
  //       // });
  //       // this.addIncidentData.types = tmpOptions;
  //       // this.selectedIncidentType(this.addIncidentData.types[0]);
  //       this.setIncidentsTypesAndDescOptions();

  //       console.log('Incident Types:', res);
  //     }
  //   );
  // }

  private setIncidentDescriptionOptionsByTypeOfIncident(desc: string): void {
    const result = this.incidentTypes.find(option => {
      return option.name.toLowerCase() === desc.toLowerCase();
    });

    if (result && result.driverIncidentTypeId) {
      const tmpOptions = [];
      result.options.forEach(option => {
        tmpOptions.push({ id: option.name, text: option.name });
      });
      this.addIncidentData.desc = tmpOptions;
    }
  }

  public selectedIncidentType($event, edit = false, descRef = null): void {
    if (descRef && !edit) {
      descRef.activeOption = null;
    }

    this.addIncidentData.desc = [];
    this.incidentTypes.filter(type => {
      if (type.driverIncidentTypeId === $event.id) {
        type.options.forEach(option => {
          this.addIncidentData.desc.push({
            id: option.name,
            text: option.name
          });
        });

        // https://bostonsoftware.atlassian.net/browse/SPR-2812
        const setDefaultValueForDesc: boolean =
          this.addIncidentData.desc && this.addIncidentData.desc.length === 1;

        if (edit) {
          this.editIncidentDataSelected.typeOfIncident = this.toCamelCase(
            $event.text
          );
          this.editIncidentDataSelected.descOfIncident = setDefaultValueForDesc
            ? this.addIncidentData.desc[0].text
            : '';
        } else {
          this.addIncidentDataSelected.typeOfIncident = this.toCamelCase(
            $event.text
          );
          this.addIncidentDataSelected.descOfIncident = setDefaultValueForDesc
            ? this.addIncidentData.desc[0].text
            : '';
        }
      }
    });
  }

  public selectedIncidentDesc($event, edit = false): void {
    this.addIncidentDataSelected.descOfIncident = $event.text;
    if (edit) {
      this.editIncidentDataSelected.descOfIncident = $event.text;
    }
  }

  private selectedIncidentDate($event): void {
    this.addIncidentDataSelected.surchargeDate = $event.dateIso;
  }

  private selectedIncidentDateEdit($event): void {
    this.editIncidentDataSelected.surchargeDate = $event.dateIso;
  }

  private getDriversFromAPI() {
    if (this.drivers && this.drivers.length === 0) {
      this.driversService
        .getDriversListByUri('/quotes/' + this.quoteId + '/drivers')
        .pipe(take(1))
        .subscribe(res => {
          this.drivers = res.items;
          this.storageService.setStorageData('driversList', this.drivers);
          if (res.items && res.items.length === 0) {
            this.addDriver();
          }
        });
    }
  }

  private toCamelCase(str: string): string {
    let newStr = str.split(' ');
    newStr = newStr.map(s => s.charAt(0).toUpperCase() + s.slice(1));

    return newStr.join(' ');
  }
  private editIncidentOpen(incident, modalEditIncident): void {
    this.editIncidentDataSelected = Object.assign({}, incident);
    this.selectedIncidentType(
      this.editIncidentDataSelected.typeOfIncident,
      true
    );
    this.setIncidentDescriptionOptionsByTypeOfIncident(
      this.editIncidentDataSelected.typeOfIncident
    );
    this.helperForViewEditSelectedIncidentType = this.addIncidentData.types.find(
      type => type.text === this.editIncidentDataSelected.typeOfIncident
    );
    modalEditIncident.openModalbox();
  }

 private deleteIncidentOpen(incident): void {
    this.deleteIncidentDataSelected = incident;
    this.deleteIncidentDataSelectedAskAbout =
      format(parseISO(incident.surchargeDate), 'MMM d, yyyy') +
      ' - ' +
      incident.typeOfIncident;
}

  private editIncident(modalEditIncident): void {
    this.overlayLoaderService.showLoader();
    this.incidentsService
      .editIncident(
        this.selectedDriver.resourceId,
        this.editIncidentDataSelected
      )
      .subscribe(res => {
        this.incidents = this.incidents.map(incident => {
          if (incident.resourceId === res.resourceId) {
            return res;
          } else {
            return incident;
          }
        });
        modalEditIncident.closeModalbox();
        this.overlayLoaderService.hideLoader();
        this.storageService.updateSingleIncident(
          this.selectedDriver.incidents.meta.href,
          res
        );

        // Rerate plans
        this.premiumsService.rerateAll();
      });
  }

  public openAddIncidentModal(
    modalAddIncidentRef,
    typeOfIncidentDropdown
  ): void {
    this.resetIncidentModalData(typeOfIncidentDropdown);
    this.selectedIncidentType(this.addIncidentData.types[0]);
    modalAddIncidentRef.openModalbox();
  }

  private resetIncidentModalData(typeOfIncidentDropdown): void {
    this.addIncidentData.date = '';
    this.addIncidentData.amount = '';
    // this.addIncidentData.desc = [];
    this.addIncidentDataSelected = {
      surchargeDate: '',
      typeOfIncident: '',
      amountPaid: '',
      descOfIncident: ''
    };
    typeOfIncidentDropdown.selectFirstOption();
  }

  // -----

  public refreshValue($event, obj, property) {
    if (obj !== undefined && property !== undefined) {
      obj[property] = $event.id;
    }
  }

  public selected($event, data, field, update = true) {
    if ($event && field) {
      data[field] = $event.id;
    }
    if (update) {
      this.saveDriverData(data);
    }
  }

  private handleQuoteClient(): void {
    this.subscribeClients();
    this.routeHandlerForQuoteClient();
    // this.initQuoteClientDetails();
  }

  private subscribeClients(): void {
    let localClients = [];

    this.subscriptionClients && this.subscriptionClients.unsubscribe();
    this.subscriptionClients = this.storageService
      .getStorageData('clients')
      .subscribe(clients => {
        if (
          clients.length &&
          clients[0].addresses !== undefined &&
          clients[0].addresses.href
        ) {
          localClients = clients;
          this.clientDetails = JSON.parse(JSON.stringify(localClients[0]));
        } else {
          this.clientsService
            .getClientsList(this.quoteId)
            .pipe(take(1))
            .subscribe(clients => {
              localClients = clients.items;
              this.clientDetails = JSON.parse(JSON.stringify(localClients[0]));
            });
        }

        // console.log('%c CLIENT DETAILS:', 'color:blue', this.clientOfTheQuote);
      });
  }

  private initQuoteClientDetails(): void {
    this.checkIfClientAssignedToQuote();

    if (this.clientDetailsAssignedToQuote) {
      const subscription = this.clientsService
        .getClient(this.quoteId, this.quote.client.resourceId)
        .pipe(take(1))
        .subscribe(res => {
          this.clientDetails = res;
          // console.log('LOADED CLIENT DATA', res);
        });
    }
  }

  private routeHandlerForQuoteClient(): void {
    if (this.route && this.route.params) {
      this.routeSubscription = this.route.params.subscribe(params => {
        this.checkIfClientAssignedToQuote();
        this.setPermitionToEditClientDetails();

        // console.log('LOADED QUOTE:', this.quote.resourceId, this.quote);
        // console.log('CLIENT ASSIGNED TO QUOTE:', this.clientDetailsAssignedToQuote);
        // console.log('CLIENT ALLOW EDIT:', this.clientAllowUpdateClientData);
      });
    }
  }

  private checkIfClientAssignedToQuote(): void {
    this.clientDetailsAssignedToQuote = !!this.quote.client.resourceId;
  }

  // When useing initQuoteClientDetails()
  // private setPermitionToEditClientDetails():void {
  //   if (!this.quote.client.resourceId || this.helperDriverIsAClient() || (!this.quote.client.firstName && !this.quote.client.lastName) ) {
  //     this.clientAllowUpdateClientData = true;
  //   } else {
  //     this.clientAllowUpdateClientData = false;
  //   }
  // }

  // When useing subscribeClients()
  private setPermitionToEditClientDetails(): void {
    if (this.helperDriverIsAClient()) {
      this.clientAllowUpdateClientData = true;
    } else {
      this.clientAllowUpdateClientData = false;
    }
  }

  private helperDriverIsAClient(): boolean {
    let isClient = false;
    // This should be checked based on driver relation to quote client
    // Required relation information (to client) in selectedDriver object - there is not such property in Driver Model
    // Currently not available

    // Currently checking by firstName and lastName
    if (
      (this.quote.client.firstName?.trim() === this.selectedDriver.firstName?.trim() &&
        this.quote.client.lastName?.trim() === this.selectedDriver.lastName?.trim()) ||
      (!this.clientDetails.firstName &&
        !this.clientDetails.lastName &&
        !this.quote.client.firstName &&
        !this.quote.client.lastName &&
        !this.clientDetails.dob)
    ) {
      isClient = true;
    }
    // console.info('Driver is a client', isClient);
    return isClient;
  }

  public onDriverDataChangeUpdateClient(event?, lastName?): void {
     // trim spaces in driver name before updating the client
    this.trimDriverNameSpaces();





    if (
      event &&
      'date' in event &&
      !event.selectByClick &&
      !event.selectedManually
    ) {
      return;
    }

    if (this.clientAllowUpdateClientData && this.clientDetails) {
      this.clientDetails.firstName = this.quote.client.firstName = this.selectedDriver.firstName.trim();
      this.clientDetails.lastName = this.quote.client.lastName = this.selectedDriver.lastName.trim();
      this.clientDetails.dob = this.selectedDriver.dateOfBirth;

      const subscription = this.apiCommonService
        .putByUri(this.clientDetails.meta.href, this.clientDetails)
        .pipe(take(1))
        .subscribe(res => {
          // console.log('UPDATED CLIENT:', res);
          this.storageService.updateClientsSingleItem(res);
          subscription && subscription.unsubscribe();

          // Update quote but only in storage to retain client info if the page was refreshed.
          // this.storageService.setStorageData('selectedQuote', this.quote);
        });

        // this.apiCommonService.putByUri(this.selectedDriver.meta.href, this.selectedDriver).pipe(
        //   take(1))
        //   .subscribe(res => {
        //     console.log(res);
        //     setTimeout(() => {
        //       this.storageService.updateDriversListSingleItem(this.quoteId, res);
        //     }, 1000);
        //   });
    }
  }

  // TODO:: SYNC DRIVER DATA WITH CLIENT DATA IF SELECTED DRIVER IS THE CLIENT
  // Required relation information (to client) in selectedDriver object
  // Currently not available

  // Validators
  // -------------------------------------------------------------------------
  public validateIsRequiredDriverMaritalStatus(): boolean {
    // Required for: Hanover New
    const requiredForPlans = ['281'];

    return Validate.isRequiredForSelectedPlansIfEmptyValue(
      this.selectedDriver.maritalStatus,
      requiredForPlans,
      this.quoteSelectedPlansIds
    );
  }

  public validateIsRequiredDriverRelationshipToInsured(): boolean {
    // Required for: Hanover, Safeco, Progressive, Travelers, Hanover New, Nat Gen Value
    // Safety
    const requiredForPlans = ['20', '25', '26', '27', '281', '11', '309', '321'];

    return Validate.isRequiredForSelectedPlansIfEmptyValue(
      this.selectedDriver.relationshipToInsured,
      requiredForPlans,
      this.quoteSelectedPlansIds
    );
  }

  public validateIsRequiredMotorcycleData(): boolean {
    let driverIsMotorcycleOperator = false;

    if (this.vehicles && this.vehicles.length) {
      const motorcycleVehiclesWithDriverAsOperator: Vehicle[] = this.vehicles.filter(
        (vehicle: Vehicle) => {
          return (
            vehicle.vehicleType === 'Motorcycle' &&
            vehicle.operator &&
            vehicle.operator.meta &&
            vehicle.operator.meta.href &&
            this.selectedDriver.meta &&
            this.selectedDriver.meta.href &&
            vehicle.operator.meta.href === this.selectedDriver.meta.href &&
            this.selectedDriver.motorcycle !== true
          );
        }
      );

      driverIsMotorcycleOperator =
        motorcycleVehiclesWithDriverAsOperator.length > 0;
    }

    return (
      driverIsMotorcycleOperator ||
      this.checkIfInvalidRadioboxField(this.selectedDriver.motorcycle)
    );
  }

  public exclusionsModalOpen(ev: Event): void {
    ev && ev.preventDefault();

    this.exclusionsSetModalExclusionsVehicles();
    this.exclusionsSetModalExclusionsVehiclesToExclude();
    this.exclusionsSetModalExclusionsVehiclesCanNotBeExcluded();

    this.refModalExclusions && this.refModalExclusions.open();
  }

  private exclusionsSetModalExclusionsVehicles(): void {
    this.modalExclusionsVehicles = [];
    this.modalExclusionsVehicles = this.vehicles.map((v: Vehicle) => {
      const tmpVehicle: IVehicleToExclude = JSON.parse(JSON.stringify(v));
      tmpVehicle.exclude = this.exclusionsCheckIfVehicleIsExcluded(v);
      tmpVehicle.isExcluded = this.exclusionsCheckIfVehicleIsExcluded(v);
      tmpVehicle.canNotBeExcluded = this.exclusionsCheckIfVehicleCanNotBeExcluded(
        v
      );
      tmpVehicle.nameToDisplay = this.vehiclesService.vehicleNameToDisplay(v);
      return tmpVehicle;
    });
  }

  private exclusionsCheckIfVehicleCanNotBeExcluded(v: Vehicle): boolean {
    return (
      v.operator &&
      v.operator.meta &&
      v.operator.meta.href &&
      this.selectedDriver.meta &&
      this.selectedDriver.meta.href &&
      v.operator.meta.href === this.selectedDriver.meta.href
    );
  }

  private exclusionsCheckIfVehicleIsExcluded(v: Vehicle): boolean {
    let vehicleExcluded = -1;

    if (
      v &&
      v.meta &&
      v.meta.href &&
      this.selectedDriver.exclusions &&
      this.selectedDriver.exclusions.length
    ) {
      vehicleExcluded = this.selectedDriver.exclusions.findIndex(
        el => el.meta.href === v.meta.href
      );
    }

    return vehicleExcluded !== -1;
  }

  private exclusionsSetModalExclusionsVehiclesCanNotBeExcluded(): void {
    this.modalExclusionsVehiclesCanNotBeExcluded = [];

    this.modalExclusionsVehiclesCanNotBeExcluded = this.modalExclusionsVehicles.filter(
      (v: IVehicleToExclude) => {
        return v.canNotBeExcluded;
      }
    );
  }

  private exclusionsSetModalExclusionsVehiclesToExclude(): void {
    this.modalExclusionsVehiclesCanNotBeExcluded = [];

    this.modalExclusionsVehiclesToExclude = this.modalExclusionsVehicles.filter(
      (v: IVehicleToExclude) => {
        return !v.canNotBeExcluded;
      }
    );
  }

  public exclusionsActionToggleExclusion(v: IVehicleToExclude): void {
    v.exclude = !v.exclude;
  }

  public exclusionsActionRemoveExclusion(v: IVehicleToExclude): void {
    v.exclude = false;
  }

  public exclusionsModalActionSave(): void {
    const vehiclesToExclude: IVehicleToExclude[] = this.modalExclusionsVehicles.filter(
      (v: IVehicleToExclude) => v.exclude
    );

    const exclusionsForDriverToSave: IDriverExclusionItem[] = vehiclesToExclude.map(
      (v: IVehicleToExclude) => {
        return {
          meta: { href: v.meta.href }
        };
      }
    );

    this.selectedDriver.exclusions = [...exclusionsForDriverToSave];
    this.saveDriverDataIndependently(this.selectedDriver);
    this.refModalExclusions && this.refModalExclusions.close();
  }

  public setExcludedVehicles(): IVehicleToExclude[] {
    let excludeVehiclesList: IVehicleToExclude[] = [];

    if (this.vehicles && this.vehicles.length) {
      excludeVehiclesList = [...this.vehicles]
        .filter((v: Vehicle) => this.exclusionsCheckIfVehicleIsExcluded(v))
        .map((v: Vehicle) => {
          const tmpVehicle: IVehicleToExclude = JSON.parse(JSON.stringify(v));
          tmpVehicle.exclude = this.exclusionsCheckIfVehicleIsExcluded(v);
          tmpVehicle.isExcluded = this.exclusionsCheckIfVehicleIsExcluded(v);
          tmpVehicle.canNotBeExcluded = this.exclusionsCheckIfVehicleCanNotBeExcluded(
            v
          );
          tmpVehicle.nameToDisplay = this.vehiclesService.vehicleNameToDisplay(
            v
          );
          return tmpVehicle;
        });
    }

    this.excludedVehicles = excludeVehiclesList;
    return excludeVehiclesList;
  }

  // Helpers
  // -------------------------------------------------------------------------

  // Return formated neme of the Driver
  public driverNameToDisplay(driver: Driver): string {
    return this.driversService.generateDriverName(driver);
  }

  // For testing purposes of datepicker-input
  private testPickerDateChange($ev): void {
    console.log('DRIVER DETAILS - DATE CHANGED:', $ev);
  }

  // If 'First Licensed' is within the last 6 years, not required.
  public checkIfRequiredCurrentLicenseDate(): boolean {
    if (this.selectedDriver.firstLicensed) {
      const currDate: any = new Date();
      currDate.setHours(0);
      currDate.setMinutes(0);
      currDate.setSeconds(0);
      currDate.setMilliseconds(0);
      let licenseDate = null;
      let difference = null;

      try {
        licenseDate = new Date(this.selectedDriver.firstLicensed);
        difference = differenceInYears(currDate, licenseDate);
      } catch (err) {}

      if (difference != null && difference >= 6) {
        return true;
      }
    }

    return false;
  }

  public checkIfInvalidRadioboxField(value: any): boolean {
    return value === true || value === false ? false : true;
  }

  public numbersOnly(event) {
    const digits = /^[0-9]{1}$/;
    const multiKey = event.ctrlKey || event.metaKey;
    const keyNormalized = event.key.toLocaleLowerCase();

    if (
      !(
        keyNormalized === 'backspace' ||
        keyNormalized === 'delete' ||
        keyNormalized === 'tab' ||
        keyNormalized === 'arrowleft' ||
        keyNormalized === 'left' ||
        keyNormalized === 'arrowright' ||
        keyNormalized === 'right' ||
        (keyNormalized === 'a' && multiKey) ||
        (keyNormalized === 'z' && multiKey) ||
        (keyNormalized === 'c' && multiKey) ||
        (keyNormalized === 'v' && multiKey) ||
        digits.test(event.key)
      )
    ) {
      event.preventDefault();
    }
  }

  checkForErrors($event) {
    $event ? (this.disableButton = true) : (this.disableButton = false);
  }

  saveReorder(modal) {
    let index = 1;
    this.drivers.forEach(driver => {
      driver.orderIndex = index;
      index++;
      this.saveDriverDataIndependently(driver);
    });
    setTimeout(() => {
      this.storageService.setStorageData('driversList', this.drivers);
    }, 1000);
    modal.closeModalbox();
  }
}
