  <h2 class="o-heading o-heading--red">Vin Lookup</h2>

  <div class="box box--silver u-spacing--1-5">
    <div>
      <label class="o-checkable" (click)="setVinLookupType('vin')" [appDetectSystem] [removeRadio]="true">
        <input type="radio" name="radio-1" value="1" [checked]="showVinForm">
        <i class="o-btn o-btn--radio"></i>
        <span>VIN</span>
      </label>

      <label class="o-checkable u-spacing--left-2"  (click)="setVinLookupType('ymm')" [appDetectSystem] [removeRadio]="true">
        <input type="radio" name="radio-1" value="2" [checked]="showYearMakeModelForm">
        <i class="o-btn o-btn--radio"></i>
        <span>Year, Make, and Model</span>
      </label>
    </div>

    <table *ngIf="showVinForm" class="form-table form-table--fixed u-spacing--2">
      <tr class="form-table__row">
        <td class="form-table__cell u-width-30px">
          VIN:
        </td>
        <td class="form-table__cell u-width-240px">         
          <input type="text" id="carr-1" name="lookUpVin"
            #refInputVin
            [ngModel]="lookupVinNumber"
            (ngModelChange)="lookupVinNumberOnChange($event, refInputVin)"
            (focus)="resetVinValidationStatus()">
        </td>
        <td class="form-table__cell u-width-240px">
          <span class="u-color-sunset" *ngIf="(lookupVinNumber && lookupVinNumber.length && !vinValidate(lookupVinNumber)) || vinNotFound">{{message}}</span>
        </td>
      </tr>
    </table>

    <table *ngIf="showYearMakeModelForm" class="form-table form-table--fixed u-spacing--2">
        <tr class="form-table__row">
            <td class="form-table__cell form-table__cell--label  u-width-130px">
              <label for="vehicleYear">Year:</label>
            </td>
            <td class="form-table__cell u-width-220px">
              <sm-autocomplete
                #refVehicleYear
                [options]="optionsYear"
                [activeOption]="selectedYear"
                [name]="'vehicleYear'"
                [id]="'vehicleYear'"
                [searchFromBegining]="false"
                [allowEmptyValue]="false"
                [allowCustomText]="false"
                [required]="true"
                (onSelect)="actionOnYearFieldSelect($event, 'year')">
              </sm-autocomplete>

            </td>
          </tr>

          <tr class="form-table__row">
            <td class="form-table__cell form-table__cell--label  u-width-130px">
              <label for="vehicleMake">Make:</label>
            </td>
            <td class="form-table__cell u-width-220px">
              <sm-autocomplete
                #refVehicleMake
                [options]="optionsMake"
                [activeOption]="selectedMake"
                [name]="'vehicleMake'"
                [id]="'vehicleMake'"
                [searchFromBegining]="false"
                [allowEmptyValue]="false"
                [allowCustomText]="false"
                [delayedOptions]="true"
                [required]="true"
                [readonly]="fieldMakeDisabled"
                (onSelect)="actionOnMakeFieldSelect($event, 'make')">
              </sm-autocomplete>

            </td>
          </tr>     

          <tr *ngIf="!fieldModelSwitchToText" class="form-table__row">

            <td class="form-table__cell form-table__cell--label  u-width-130px">
              <label for="vehicleModel">Model:</label>
            </td>
            <td class="form-table__cell u-width-220px">
              <div>
                <sm-autocomplete
                  #refVehicleModel
                  [options]="optionsModel"
                  [activeOption]="selectedModel"
                  [name]="'vehicleModel'"
                  [id]="'vehicleModel'"
                  [searchFromBegining]="false"
                  [allowEmptyValue]="false"
                  [delayedOptions]="true"
                  [required]="true"
                  [readonly]="fieldModelDisabled"
                  (onSelect)="actionOnModelFieldSelect($event, 'model')">
                </sm-autocomplete>
              </div>
            </td>
          </tr>

          <tr *ngIf="fieldModelSwitchToText" class="form-table__row">
            <td class="form-table__cell form-table__cell--label  u-width-130px">
              <label for="vehicleModel">Model:</label>
            </td>
            <td class="form-table__cell u-width-220px">
              <div>
                <input
                  #refVehicleModel="ngModel"
                  type="text"
                  name="vehicleModel"
                  id="vehicleModel"
                  [(ngModel)]="selectedModel"
                  [required]="true"
                  [readonly]="fieldModelDisabled">
              </div>
            </td>
          </tr>

          <tr class="form-table__row" *ngIf="!fieldTrimIsHidden">

              <td class="form-table__cell form-table__cell--label  u-width-130px">
                <label for="vehicleTrim">Trim:</label>
              </td>
              <td class="form-table__cell u-width-220px">

                <div>              
                  <sm-autocomplete
                    #refVehicleTrim
                    [options]="optionsTrim"
                    [activeOption]="selectedTrimLevel"
                    [name]="'vehicleTrim'"
                    [id]="'vehicleTrim'"
                    [css]="(trimFieldIsInTextMode) ? 'no-dropdown-btn' : ''"
                    [searchFromBegining]="false"
                    [allowEmptyValue]="false"
                    [allowCustomText]="trimFieldIsInTextMode"
                    [delayedOptions]="true"
                    [required]="!trimFieldIsInTextMode"
                    [readonly]="fieldTrimDisabled && !trimFieldIsInTextMode"
                    (onSelect)="actionOnTrimFieldSelect($event, 'trim')">
                  </sm-autocomplete>                  
                </div>
              </td>
            </tr>
    </table>

  </div> 

  <div class="row u-spacing--1-5">
    <div class="col-xs-12 u-align-right u-remove-letter-spacing">
      <button type="button" (click)="actionRetrieveVehicle()" class="o-btn" [disabled]="(!isRetrieveEnabled)">Retrieve</button>      
      <button type="button" (click)="actionClose()" class="o-btn o-btn--idle u-spacing--left-0-5">Close</button>
    </div>
  </div>
