import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { AutoStandardCoveragesAutomanagerComponent } from './auto-standard-coverages-automanager.component';

// Services
import { OptionsService } from 'app/dashboard/app-services/options.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { SubsService } from 'app/dashboard/app-services/subs.service';
import { AgencyUserService } from 'app/shared/services/agency-user.service';
import { VehiclesService } from 'app/dashboard/app-services/vehicles.service';
import { CoveragesService } from 'app/dashboard/app-services/coverages.service';

describe('AutoStandardCoveragesAutomanagerComponent', () => {
  let component: AutoStandardCoveragesAutomanagerComponent;
  let fixture: ComponentFixture<AutoStandardCoveragesAutomanagerComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ AutoStandardCoveragesAutomanagerComponent ],
      providers: [
        OptionsService,
        SpecsService,
        StorageService,
        SubsService,
        AgencyUserService,
        VehiclesService,
        CoveragesService
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(AutoStandardCoveragesAutomanagerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
