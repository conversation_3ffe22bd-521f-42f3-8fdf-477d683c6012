export interface TrimItem {
	id: number;
	name: string;
	value: string;
	msrp: string;
}

export interface Vehicle {
	id: number;
	lookupType: string;
	usage: string;
	plateNumber: string;
	vin: string;
	plateType: string;
	odometer: string;
	odometerCode: string;
	registrationType: string;
	reassignedPlate: string;
	registrationReason: string;
	ownership: string;
	condition: string;
	primaryColor: string;
	transmission: string;
	passengers: string;
	outOfStateTitleNumber: string;
	titleState: string;
	titleIssueDate: string;
	atlasVehicleIndicator: string;
	atlasVehicleKey: string;
	pseudoTrailerVINIndicator: string;
	bodyStyle: string;
	vehicleType: string;
	secondaryColor: string;
	year: string;
	make: string;
	model: string;
	modelNumber: string;
	cylinders: string;
	doors: string;
	numberOfSeats: string;
	fuelType: string;
	trim: string;
	msrp: string;
	grossVehicleWeight: string;
  registeredWeight: string;
	trimItems: TrimItem[];
	titleType?: string;
	titleBrands?: TitleBrands[];
}

export interface TitleBrands {
	brandType: string;
}

export interface Message {
	message: string;
}

export interface EvrVehicle {
	isVehicleFoundInAtlas: boolean;
	vehicles: Vehicle[];
	messages: Message[];
}
