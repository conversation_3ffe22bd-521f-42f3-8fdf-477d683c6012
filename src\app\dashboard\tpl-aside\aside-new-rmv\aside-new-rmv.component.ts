
import {first} from 'rxjs/operators';
import { Component, OnInit } from '@angular/core';

import { AgencyUserService }  from 'app/shared/services/agency-user.service';
import { LookupsService } from 'app/dashboard/app-services/lookups.service';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { SubsService }  from 'app/dashboard/app-services/subs.service';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { QuoteNewRMVData } from 'app/app-model/quote';
import { SubscriptionLike as ISubscription } from 'rxjs';


import { format } from 'date-fns';

@Component({
    selector: 'app-aside-new-rmv',
    templateUrl: './aside-new-rmv.component.html',
    styleUrls: ['./aside-new-rmv.component.scss'],
    standalone: false
})
export class AsideNewRmvComponent implements OnInit {
  private subscription: ISubscription;
  private clientSubscription: ISubscription;
  private currentPlanSubscription: ISubscription;
  public newRmvQuote: QuoteNewRMVData;
  public effectiveDate;
  public plansOnQuotes;
  public plansCount = 0;
  public selectedPlan: any = {};
  private selectedPlanObject;
  private currentPlanUrl;
  public client;

  private plansFromStorage;
  private plansSubscription;

  constructor(
    private agencyUserService: AgencyUserService,
    private subsService: SubsService,
    private storageService: StorageService,
    private quotesService: QuotesService,
    private overlayLoaderService: OverlayLoaderService,
    private lookupsService: LookupsService,
    private storageGlobalService: StorageGlobalService
  ) { }

  ngOnInit() {
    this.getClient();
    this.plansFromStorage = this.storageGlobalService.takeSubs('plans');
    if (this.plansFromStorage) {
      this.setPlansOnQuotes(this.plansFromStorage);
    } else {
      this.agencyUserService.userData$.subscribe(agent => {
        if (agent) {
          this.overlayLoaderService.showLoader();
          this.plansSubscription = this.subsService.getRatingPlans(agent.agencyId).pipe(first()).subscribe( () => {
            this.plansFromStorage = this.storageGlobalService.takeSubs('plans');
            this.setPlansOnQuotes(this.plansFromStorage);
            this.overlayLoaderService.hideLoader();
          });
        }
      });
    };
    this.subscription = this.storageService.getStorageData('newRmvQuoteData').subscribe( res => {
      console.log('NEWRmvData::', res);
      this.newRmvQuote = res;
    });
  }

  ngOnDestroy() {
    this.subscription && this.subscription.unsubscribe();
    this.currentPlanSubscription && this.currentPlanSubscription.unsubscribe();
    this.clientSubscription && this.clientSubscription.unsubscribe();
  }

  public updateEffectiveDate(event) {
    if (event.date) {
      this.newRmvQuote.effectiveDate = format(new Date(event.date),'yyyy-MM-dd');
    }
  }

  private setPlansOnQuotes(plans) {
    this.plansOnQuotes = [];
    plans.forEach(item => {
      if (item.lob === 'AUTOP') {
        this.plansOnQuotes.push({text: item.name, id: item.ratingPlanId, data: item});
      }
    });
    this.plansOnQuotes.sort((a, b) => {
      if (a.text < b.text) { return -1; } else if (a.text > b.text) { return 1; } else { return 0; }
    });

    this.plansCount = this.plansOnQuotes.length;

    if (this.plansCount) {
      this.selectedPlan = [];
      if (this.plansOnQuotes && this.plansOnQuotes.length) {
        this.plansOnQuotes.forEach( (plan, index) => {
          if (index > 0) {
            this.selectedPlan.push(plan);
          }
        });
      }
    }
  }

  public updateQuotePlan($event) {
    const data = { items: $event.selectedOption.data };
    if (this.newRmvQuote.plans) {
      if (data.items && !data.items.length) {
        data.items = [$event.selectedOption.data];
      }
      this.newRmvQuote.plans = $event.selectedOption;
    }

    this.storageService.setStorageData('newRmvQuoteData', this.newRmvQuote);
  }

  private getClient() {
    this.clientSubscription = this.lookupsService.getNewRmvClient$.subscribe( client => {
      this.client = client;
    });
    // return (this.lookupsService.newRmvClientInfo.firstName) ? (this.lookupsService.newRmvClientInfo.firstName + ' ' + this.lookupsService.newRmvClientInfo.lastName) : 'No clients name';
  }

  public displayClientFullname(client): string {
    let fullname = client.firstName + ' ' + client.lastName;
    if ((client.firstName && client.firstName.length === 0) && (client.lastName && client.lastName.length === 0) || (!client.firstName && !client.lastName)) {
      fullname = 'No clients name';
    }
    return fullname;
  }
}
