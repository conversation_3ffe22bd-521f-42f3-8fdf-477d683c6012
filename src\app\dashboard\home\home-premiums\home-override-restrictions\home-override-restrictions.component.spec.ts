import { expect } from 'testing/helpers/all';
import { async, ComponentFixture, inject, TestBed, fakeAsync, tick } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';

import { data as GUIDELINE_OVERRIDES_DATA } from 'testing/data/quotes/guideline-overrides';
import { HOME_QUOTE } from 'testing/data/quotes/quote-home';
import { HOME_QUOTE_PLAN_LIST } from 'testing/data/quotes/quote-plan-list/home';
import { StubLoaderComponent } from 'testing/stubs/components/loader.component';
import { StubModalboxComponent } from 'testing/stubs/components/modalbox.component';
import {
    StubPerfectScrollbarComponent
} from 'testing/stubs/components/perfect-scrollbar.component';
import { StubPremiumsServiceProvider } from 'testing/stubs/services/premiums.service.provider';
import {
    StubQuotesService, StubQuotesServiceProvider
} from 'testing/stubs/services/quotes.service.provider';

import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Helpers } from 'app/utils/helpers';

import { HomeOverrideRestrictionsComponent } from './home-override-restrictions.component';
import { By } from '@angular/platform-browser';

describe('Component: HomeOverrideRestrictions', () => {
  let component: HomeOverrideRestrictionsComponent;
  let fixture: ComponentFixture<HomeOverrideRestrictionsComponent>;
  let modalBox: StubModalboxComponent;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [
        FormsModule
      ],
      declarations: [
        HomeOverrideRestrictionsComponent,
        StubLoaderComponent,
        StubPerfectScrollbarComponent,
        StubModalboxComponent
      ],
      providers: [
        StubQuotesServiceProvider,
        StorageService,
        StubPremiumsServiceProvider
      ]
    })
    .compileComponents();
  }));

  beforeEach(fakeAsync(inject(
    [StorageService, QuotesService],
    (storageService: StorageService, quotesService: StubQuotesService) => {
      storageService.setStorageData('selectedQuote', Helpers.deepClone(HOME_QUOTE));
      storageService.setStorageData('selectedPlan', Helpers.deepClone(HOME_QUOTE_PLAN_LIST));
      quotesService.guidelineOverrides = Helpers.deepClone(GUIDELINE_OVERRIDES_DATA);

      fixture = TestBed.createComponent(HomeOverrideRestrictionsComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
      tick();

      // browser repaint
      fixture.detectChanges();
      tick();

      modalBox = fixture.debugElement.query(By.directive(StubModalboxComponent)).componentInstance;
      spyOn(modalBox, 'openModalbox').and.callThrough();
      spyOn(modalBox, 'closeModalbox').and.callThrough();
  })));

  it('should be created', () => {
    expect(component).toBeTruthy();
  });

  it('should allow to open modalbox', fakeAsync(() => {
    const btn = fixture.debugElement.query(By.css('button.o-btn')).nativeElement;
    btn.click();
    fixture.detectChanges();
    tick();

    expect(modalBox.openModalbox).toHaveBeenCalled();
  }));

  it('should update overrides on save and close modalbox', fakeAsync(inject(
    [QuotesService], (quotesService: StubQuotesService) => {
      spyOn(quotesService, 'updateGuidelineOverrides').and.callThrough();

      const btn = fixture.debugElement.query(By.directive(StubModalboxComponent)).query(By.css('button.o-btn')).nativeElement;
      btn.click();
      fixture.detectChanges();
      tick();

      expect(quotesService.updateGuidelineOverrides).toHaveBeenCalled();
      expect(modalBox.closeModalbox).toHaveBeenCalled();
    })));

  it('should close modalbox on cancel', fakeAsync(inject(
    [QuotesService], (quotesService: StubQuotesService) => {
      spyOn(quotesService, 'updateGuidelineOverrides').and.callThrough();

      const btn = fixture.debugElement.query(By.directive(StubModalboxComponent)).query(
        By.css('button.o-btn + button.o-btn')).nativeElement;
      btn.click();
      fixture.detectChanges();
      tick();

      expect(quotesService.updateGuidelineOverrides).not.toHaveBeenCalled();
      expect(modalBox.closeModalbox).toHaveBeenCalled();
    })));
});
