<section class="section section--thick">
  <div class="row">
    <div class="col-xs-6">
      <h1 class="o-heading">Policy History</h1>
    </div>
  </div>

  <div class="row u-spacing--1-5">
    <div class="col-xs-12">
      <div class="box box--silver">
        <div class="row o-columns">

          <div class="col-xs-6 u-width-50">
            <table class="form-table">
              <tr class="form-table__row" [class.is-required-field]="selectRefPriorOrRenewingCarrier.ngModel?.invalid">
                <td class="form-table__cell u-width-185px">Prior or Renewing Carrier:</td>
                <td class="form-table__cell u-width-190px">
                  <sm-autocomplete
                    #selectRefPriorOrRenewingCarrier
                    [id]="'priorOrRenewingCarrier'"
                    [name]="'priorOrRenewingCarrier'"
                    [required]="true"
                    [disabled]="false"
                    [options]="policyItemsData.priorOrRenewingCarrier.options"
                    [activeOption]="policyHistoryData.priorOrRenewingCarrier"
                    [searchFromBegining]="true"
                    fieldAutofocus
                    (onSelect)="selected($event, 'priorOrRenewingCarrier')">
                  </sm-autocomplete>
                </td>
              </tr>

              <tr class="form-table__row" [class.is-required-field]="refPickerDatePolicy.ngModel?.invalid">
                <td class="form-table__cell u-width-185px">Prior Policy Expiration Date:</td>
                <td class="form-table__cell u-width-190px">
                  <app-datepicker-input
                    #refPickerDatePolicy
                    [id]="'priorPolicyExpirationDate'"
                    [name]="'priorPolicyExpirationDate'"
                    [required]="fieldPriorPolicyExpirationDateIsRequired()"
                    [disabled]="disabledIfPriorOrRenewingCarrierNotSet()"
                    [selectDate]="policyHistoryData.priorPolicyExpirationDate"
                    [returnDateFormat]="'yyyy-MM-dd'"
                    (onDateChange)="selectedDate($event, 'priorPolicyExpirationDate')">
                  </app-datepicker-input>
                </td>
              </tr>

              <tr class="form-table__row" [class.is-required-field]="fieldHasUnsetValue('yearsWithThisCarrier') && !disabledIfPriorOrRenewingCarrierNotSet()">
                <td class="form-table__cell u-width-185px">Years with this Carrier:</td>
                <td class="form-table__cell u-width-190px">
                  <sm-autocomplete
                    #selectRefYearsWithThisCarrier
                    [id]="'yearsWithThisCarrier'"
                    [name]="'yearsWithThisCarrier'"
                    [required]="requiredIfPriorOrRenewingCarrierNotSet()"
                    [readonly]="disabledIfPriorOrRenewingCarrierNotSet()"
                    [options]="policyItemsData.yearsWithThisCarrier.options"
                    [activeOption]="policyHistoryData.yearsWithThisCarrier"
                    [searchFromBegining]="true"
                    (onSelect)="selected($event, 'yearsWithThisCarrier')">
                  </sm-autocomplete>
                  <!-- [disabled]="disabledIfPriorOrRenewingCarrierNotSet()" -->
                </td>
              </tr>

              <tr class="form-table__row" [class.is-required-field]="fieldHasUnsetValue('quoteThisCarrierAs') && !disabledIfPriorOrRenewingCarrierNotSet()">
                <td class="form-table__cell u-width-185px">Quote this Carrier as:</td>
                <td class="form-table__cell u-width-190px">
                  <sm-autocomplete
                    #selectRefQuoteThisCarrierAs
                    [id]="'quoteThisCarrierAs'"
                    [name]="'quoteThisCarrierAs'"
                    [required]="requiredIfPriorOrRenewingCarrierNotSet()"
                    [readonly]="disabledIfPriorOrRenewingCarrierNotSet()"
                    [options]="policyItemsData.quoteThisCarrierAs.options"
                    [activeOption]="policyHistoryData.quoteThisCarrierAs"
                    [searchFromBegining]="true"
                    (onSelect)="selected($event, 'quoteThisCarrierAs')">
                  </sm-autocomplete>
                </td>
              </tr>
            </table>
          </div>

          <div class="col-xs-6 u-width-50">
            <table class="form-table">
              <tr class="form-table__row" [class.is-required-field]="fieldPolicyNumberIsRequired() && fieldPolicyNumberIsAvailable()">
                <td class="form-table__cell u-width-185px">Policy #:</td>
                <td class="form-table__cell u-width-130px">
                  <input
                    *ngIf="!displayPriorPolicyPolicyNumber"
                    #refPriorPolicyPolicyNumber="ngModel"
                    [id]="'priorPolicyPolicyNumber'"
                    [name]="'priorPolicyPolicyNumber'"
                    maxlength="45"
                    [readonly]="!fieldPolicyNumberIsAvailable()"
                    [(ngModel)]="policyHistoryData.priorPolicyPolicyNumber"
                    (blur)="selected({text: policyHistoryData.priorPolicyPolicyNumber}, 'priorPolicyPolicyNumber', true)">
                  <input
                    *ngIf="displayPriorPolicyPolicyNumber"
                    #refPriorPolicyPolicyNumber
                    [id]="'priorPolicyPolicyNumber'"
                    [name]="'priorPolicyPolicyNumber'"
                    maxlength="45"
                    [value]="'N/A'"
                    [readonly]="!fieldPolicyNumberIsAvailable()"
                    (blur)="selected({text: policyHistoryData.priorPolicyPolicyNumber}, 'priorPolicyPolicyNumber', true)">
                </td>
              </tr>

              <tr class="form-table__row" [class.is-required-field]="selectRefYearsWithCurrentAgency.ngModel?.invalid">
                <td class="form-table__cell u-width-185px">Years with Current Agency:</td>
                <td class="form-table__cell u-width-130px">
                  <sm-autocomplete
                    #selectRefYearsWithCurrentAgency
                    [id]="'yearsWithCurrentAgency'"
                    [name]="'yearsWithCurrentAgency'"
                    [required]="true"
                    [readonly]="false"
                    [options]="policyItemsData.yearsWithCurrentAgency.options"
                    [activeOption]="policyHistoryData.yearsWithCurrentAgency"
                    [searchFromBegining]="true"
                    (onSelect)="selected($event, 'yearsWithCurrentAgency')">
                  </sm-autocomplete>
                </td>
              </tr>

              <tr class="form-table__row" [class.is-required-field]="fieldLapsedDaysLast12MonthsIsRequired()">
                <td class="form-table__cell u-width-185px">Lapsed Days Last 12 Months:</td>
                <td class="form-table__cell u-width-130px">
                  <sm-autocomplete
                    #selectRefLapsedDaysLast12Months
                    [id]="'lapsedDaysLast12Months'"
                    [name]="'lapsedDaysLast12Months'"
                    [required]="fieldLapsedDaysLast12MonthsIsRequired()"
                    [readonly]="disabledIfPriorOrRenewingCarrierNotSet()"
                    [options]="policyItemsData.lapsedDaysLast12Months.options"
                    [activeOption]="policyHistoryData.lapsedDaysLast12Months"
                    [searchFromBegining]="true"
                    (onSelect)="selected($event, 'lapsedDaysLast12Months')">
                  </sm-autocomplete>
                </td>
              </tr>

              <tr class="form-table__row" [class.is-required-field]="selectRefPriorBodilyInjurylimits.ngModel?.invalid">
                <td class="form-table__cell u-width-185px">Prior Bodily Injury Limits:</td>
                <td class="form-table__cell u-width-130px">
                  <sm-autocomplete
                    #selectRefPriorBodilyInjurylimits
                    [id]="'priorBodilyInjurylimits'"
                    [name]="'priorBodilyInjurylimits'"
                    [required]="validateIsRequiredPriorBodilyInjurylimits()"
                    [readonly]="disabledIfPriorOrRenewingCarrierNotSet()"
                    [options]="policyItemsData.priorBodilyInjurylimits.options"
                    [activeOption]="policyHistoryData.priorBodilyInjurylimits"
                    [searchFromBegining]="true"
                    (onSelect)="selected($event, 'priorBodilyInjurylimits')">
                  </sm-autocomplete>
                </td>
              </tr>
            </table>
          </div>

        </div> <!-- / .row -->
      </div> <!-- / .box -->
    </div>
  </div>
</section>
