import { AgencyUserService } from './../../../shared/services/agency-user.service';
import {
  RmvServicesUserEnteredData,
  PrefillRmv,
  Vehicle,
  Lessor,
  Lienholder,
  RMVCreateForm,
  BusinessOwner
} from './../../../app-model/PrefillRMV';
import { RmvResponseModalComponent } from './rmv-response-modal/rmv-response-modal.component';
import { LienholderSelectionComponent } from './lienholder-selection/lienholder-selection.component';
import { LessorSelectionComponent } from './lessor-selection/lessor-selection.component';
import { BusinessOwnerSelectionComponent } from './business-owner-selection/business-owner-selection.component';
import { OwnersSelectionComponent } from './owners-selection/owners-selection.component';
import { VehicleSelectionComponent } from './vehicle-selection/vehicle-selection.component';
import { TypeSelectionComponent } from './type-selection/type-selection.component';
import { Component, OnInit, ViewChild } from '@angular/core';
import { LookupsService } from 'app/dashboard/app-services/lookups.service';
import { RmvService } from '../../app-services/rmv.service';
import { ApiService } from '../../../shared/services/api.service';
import { FormsService } from 'app/dashboard/app-services/forms.service';
import { FormData } from '../../../app-model/form';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { NgForm, ControlContainer } from '@angular/forms';
import { EStampGetReadyComponent } from './e-stamp-get-ready/e-stamp-get-ready.component';
import { PurchaseAndSalesComponent } from './purchase-and-sales/purchase-and-sales.component';
import { GaragingAddressComponent } from './garaging-address/garaging-address.component';
import { GetReadyRequest, GetReadyUserEnteredData } from './get-ready.model';

@Component({
    selector: 'app-rta-prefill',
    templateUrl: './rta-prefill.component.html',
    viewProviders: [{ provide: ControlContainer, useExisting: NgForm }],
    styleUrls: ['./rta-prefill.component.scss'],
    standalone: false
})
export class RtaPrefillComponent implements OnInit {
  @ViewChild('form') form: NgForm;
  @ViewChild(TypeSelectionComponent, { static: true }) typeSelection: TypeSelectionComponent; // type
  @ViewChild(VehicleSelectionComponent)
  vehicleSelection: VehicleSelectionComponent; // plate vin ownership
  @ViewChild(OwnersSelectionComponent) ownerSelection: OwnersSelectionComponent; // owners[0] owners[1]
  @ViewChild(BusinessOwnerSelectionComponent)
  businessOwnerSelection: BusinessOwnerSelectionComponent;
  @ViewChild(LessorSelectionComponent)
  lessorSelection: LessorSelectionComponent; // lessor
  @ViewChild(LienholderSelectionComponent)
  lienholderSelection: LienholderSelectionComponent; // lienholder
  @ViewChild(RmvResponseModalComponent) rmvResponse: RmvResponseModalComponent;
  @ViewChild('rmvResponseModal') responseModal;
  @ViewChild('getReadyResponseModal') getReadyResponseModal;
  @ViewChild('getReadyErrorModal') getReadyErrorModal;
  @ViewChild(EStampGetReadyComponent) estampSelection: EStampGetReadyComponent;
  @ViewChild(PurchaseAndSalesComponent) purchaseAndSalesSelection: PurchaseAndSalesComponent;
  @ViewChild(GaragingAddressComponent) garagingAddressSelection: GaragingAddressComponent;
  createFormModel;
  rmvPrefill: PrefillRmv;
  agencyId;
  agentId;
  messages;
  notifications;
  rmvLookupData;
  IsGetReadyEligible;
  getReadyRequest;
  getReadyResponse;
  constructor(
    private agencyUserService: AgencyUserService,
    private lookupService: LookupsService,
    private formsService: FormsService,
    private apiService: ApiService,
    private overlayLoaderService: OverlayLoaderService,
    private rmvService: RmvService
  ) { }

  ngOnInit() {
    this.agencyUserService.userData$.subscribe(x => {
      this.agencyId = x.agencyId;
      this.agentId = x.user.userId;
    });
  }

  hideButtonsUntilConditionMet() {
    if (!this.typeSelection.type) {
      return true;
    }
  }

  resetModels() {
    this.vehicleSelection.resetForm();
    if (this.ownerSelection && this.ownerSelection.owners) { this.ownerSelection.reset(); }
    this.estampSelection.reset();
    this.purchaseAndSalesSelection.resetModel();

  }

  SkpToForm() {
    const formData: FormData = new FormData(
      this.agentId,
      this.agencyId,
      'RTA',
      '',
      0,
      '',
      this.ownerSelection && this.ownerSelection.owners[0].firstName
        ? this.ownerSelection.owners[0].firstName
        : '',
      this.ownerSelection && this.ownerSelection.owners[0].lastName
        ? this.ownerSelection.owners[0].lastName
        : ''
    );
    let formWindow: Window;
    const baseUrl =
      document.location.protocol +
      '//' +
      document.location.hostname +
      ':' +
      document.location.port +
      '/' +
      document.location.pathname;
    const loadingPage = baseUrl + '/assets/html-templates/loading.html';
    formWindow = window.open(loadingPage, '_blank');

    const owners = this.createOwners();
    const vehicles = this.createVehicles();
    const data = this.createModel(owners, vehicles);

    return new Promise((res, rej) => {
      this.rmvService.RMVForm(data).subscribe(x => {
        const url = `${data.agencyId}/openform?agencyformid=${
          x.identifier
          }&agentId=${x.creationUserId}&ticket=${encodeURIComponent(
            x.authorizationToken.ticket
          )}&requestId=${x.authorizationToken.requestId}`;
        const formUrl = this.apiService.formAppUrl(url);
        formWindow.location.href = formUrl;
      });
    });
  }

  createOwners() {
    let owners =
      this.ownerSelection && this.ownerSelection.owners[0].firstName
        ? [...this.ownerSelection.owners]
        : [];
      if (this.vehicleSelection.vehicle.ownership === 'BusinessOwned') {
         this.businessOwnerSelection.company.fid =
          this.businessOwnerSelection.company.fid.replace(/[_-]/g, '');
        owners = [this.businessOwnerSelection.company]; }

    if (this.vehicleSelection.vehicle.ownership === 'Leased') {owners[0].fid = this.lessorSelection.fid; }
      if (this.ownerSelection && this.ownerSelection.owners[1].firstName === '') {
      owners.pop();
    }
    return owners;
  }

  createVehicles() {
    const lookupType = this.vehicleSelection.vin ? 'vin' : 'plate';

    const vehicles: Vehicle[] = [
      {
        id: 1,
        lookupType: lookupType,
        vin: this.vehicleSelection.vehicle.vin.trim(),
        plateType: this.vehicleSelection.transferPlate ? '' : this.vehicleSelection.vehicle.plateType,
        plateNumber: this.vehicleSelection.transferPlate ? '' : this.vehicleSelection.vehicle.plateNumber.trim()
      }
    ];
    if (this.vehicleSelection.transferPlate) {
      const veh: Vehicle = {
        id: 2,
        vin: '',
        lookupType: lookupType,
        plateType: this.vehicleSelection.vehicle.plateType,
        plateNumber: this.vehicleSelection.vehicle.plateNumber.trim(),
        usage: 'secondary'
      };
      vehicles.push(veh);
    }
    return vehicles;
  }

  createModel(owners, vehicles) {
    const lessors: Lessor[] = this.lessorSelection
      ? [{ id: 1,
           fid: this.lessorSelection.fid,
           atlasEntityKey: this.lessorSelection.lessor?.atlasEntityKey,
           atlasEntityLocationKey: this.lessorSelection.lessor?.atlasEntityLocationKey }]
      : [];
    const lienholders: Lienholder[] = this.lienholderSelection
      ? [{ id: 1, code: this.lienholderSelection.code }]
      : [];
    const businessOwners: any = this.businessOwnerSelection
      ? this.businessOwnerSelection
      : [];

    // const rmvServiceUserEnteredData: RmvServicesUserEnteredData = {
    //   owners: owners,
    //   lienholders: lienholders,
    //   lessors: lessors,
    //   vehicles: vehicles,
    //   businessowners: businessOwners
    // };
    const vehicle1 = { ...this.vehicleSelection.vehicle };
      const vehiclesGetReady = [vehicle1];

    const rmvServiceUserEnteredData: RmvServicesUserEnteredData = {
      owners: this.ownerSelection && this.ownerSelection.owners ? this.ownerSelection.owners : [],
      vehicles: vehiclesGetReady,
      garagingAddress: this.garagingAddressSelection.garagingAddress,
      purchaseAndSalesInformation: this.purchaseAndSalesSelection.purchaseAndSalesInfo,
      eStampInfo: this.estampSelection.estampInfo,
      businessowners: this.businessOwnerSelection && this.businessOwnerSelection.company ? [this.businessOwnerSelection.company] : [],
      lessors: lessors,
      lienholders: lienholders

    };
    return {
      transactionType: this.typeSelection.type,
      ownership: this.vehicleSelection.ownership,
      agencyId: this.agencyId,
      creationUserId: this.agentId,
      number: 'RTA',
      clientId: '',
      rmvLookupData: this.rmvLookupData,
      businessOwnerLookupData: this.businessOwnerSelection
        ? [this.businessOwnerSelection.company]
        : '',
      lienholderLookupData: this.lienholderSelection
        ? [this.lienholderSelection.lienholder]
        : '',
      lessorLookupData: this.lessorSelection
        ? [this.lessorSelection.lessor]
        : '',
      rmvServicesUserEnteredData: rmvServiceUserEnteredData
    };
  }

  prefillFromRMV() {
    // Copy owners from ownerselection, if owner 2 does not have a name, pop it from array to avoid bad data passed to api.
    // Copy so it will not remove text field from form
    const owners = this.createOwners();
        // setup vehicle object needed for prefillRMVForm Endpoint
    const vehicles: Vehicle[] = this.createVehicles();
    // RMV endpoint model
    this.rmvPrefill = {
      transactionType: this.typeSelection.type,
      vehicles: vehicles,
      ownership: this.vehicleSelection.vehicle.ownership,
      drivers: owners
    };
    this.overlayLoaderService.showLoader();
    this.lookupService.prefillRMVForm(this.rmvPrefill).subscribe(x => {
      this.messages = x.messages;
      this.rmvLookupData = x.rmvLookupData;
      this.overlayLoaderService.hideLoader();
      this.responseModal.open();
      this.IsGetReadyEligible = x.isGetReadyEligible;
      this.notifications = x.notifications;
      // CreateForm Endpoint model
      this.createFormModel = this.createModel(owners, vehicles);

      // Go to RTA model
if (this.businessOwnerSelection && this.businessOwnerSelection.company) { this.businessOwnerSelection.company.fid =
  this.businessOwnerSelection.company.fid.replace(/[_-]/g, ''); }
      const lessors: Lessor[] = this.lessorSelection
        ? [{ id: 1,
            fid: this.lessorSelection.fid,
            atlasEntityKey: this.lessorSelection.lessor?.atlasEntityKey,
            atlasEntityLocationKey: this.lessorSelection.lessor?.atlasEntityLocationKey }]
        : [];
      const lienholders: Lienholder[] = this.lienholderSelection
        ? [{ id: 1, code: this.lienholderSelection.code }]
        : [];
      const businessowners: any = this.businessOwnerSelection
        ? [this.businessOwnerSelection.company]
        : [];
      const plate = this.vehicleSelection.vehicle.plateNumber;
      const platetype = this.vehicleSelection.vehicle.plateType;
      const vehicle1 = { ...this.vehicleSelection.vehicle };
      const vehiclesGetReady = [vehicle1];

      if (this.vehicleSelection.transferPlate) {
        vehiclesGetReady[0].plateNumber = '';
        vehiclesGetReady[0].plateType = '';
        const veh = {
          id: 2, lookupType: '', usage: 'secondary',
          ownership: '', condition: '', primaryColor: '', transmission: '',
          passengers: '', outOfStateTitleNumber: '', titleIssueDate: '', titleState: '',
          plateNumber: plate, vin: '', plateType: platetype, odometer: '',
          registrationReason: '', reassignedPlate: '', registrationType: ''
        };
        vehiclesGetReady.push(veh);
      }

      if (this.purchaseAndSalesSelection.purchaseAndSalesInfo.dealerFid !== null) {
        this.purchaseAndSalesSelection.purchaseAndSalesInfo.dealerFid = this.purchaseAndSalesSelection.purchaseAndSalesInfo.dealerFid.replace(/[_-]/g, ''); }

      const getReadyUserEnteredData: GetReadyUserEnteredData = {
        owners: owners,
        vehicles: vehiclesGetReady,
        garagingAddress: this.garagingAddressSelection.garagingAddress,
        purchaseAndSalesInformation: this.purchaseAndSalesSelection.purchaseAndSalesInfo,
        eStampInfo: this.estampSelection.estampInfo,
        businessowners,
        indicators: [],
        lessors,
        lienholders

      };
      this.getReadyRequest = {
        transactionType: this.typeSelection.type === 'PrefillNewTitleAndRegistration' &&
          this.vehicleSelection.vehicle.reassignedPlate === 'Y'
          ? 'Reassign'
          : this.typeSelection.type.replace('Prefill', ''),
        ownership: this.vehicleSelection.vehicle.ownership,
        agencyId: this.agencyId,
        creationUserId: this.agentId,
        rmvLookupData: this.rmvLookupData,
        businessOwnerLookupData: this.businessOwnerSelection
          ? [this.businessOwnerSelection.company]
          : '',
        lienholderLookupData: this.lienholderSelection
          ? [this.lienholderSelection.lienholder]
          : '',
        lessorLookupData: this.lessorSelection
          ? [this.lessorSelection.lessor]
          : '',
        rmvServicesUserEnteredData: getReadyUserEnteredData

      };

    }, err => {alert('There was a problem, please try again'); this.overlayLoaderService.hideLoader(); });
  }

  submitToRmv() {
    this.overlayLoaderService.showLoader();
    this.rmvService.getReadySubmit(this.getReadyRequest).subscribe(x => {
      this.overlayLoaderService.hideLoader();
      this.getReadyResponse = x;
      this.responseModal.close();
      this.getReadyResponse.resultStatus.status === 'Success' ?
        this.getReadyResponseModal.open() : this.getReadyErrorModal.open();
    }, err => {alert('There was a problem, please try again'); this.overlayLoaderService.hideLoader(); });



  }

}
