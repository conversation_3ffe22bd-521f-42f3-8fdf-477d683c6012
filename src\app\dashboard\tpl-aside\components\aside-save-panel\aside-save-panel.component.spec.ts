import { async, ComponentFixture, fakeAsync, inject, TestBed, tick } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { RouterTestingModule } from '@angular/router/testing';

import { AUTO_QUOTE as QUOTE_DATA } from 'testing/data/quotes/quote-auto';
import { expectLastCallArgs } from 'testing/helpers/data-expect';
import { StubLeaveQuoteComponent } from 'testing/stubs/components/leave-quote.component';
import { StubModalboxComponent } from 'testing/stubs/components/modalbox.component';
import { StubCoveragesServiceProvider } from 'testing/stubs/services/coverages.service.provider';
import {
    StubOverlayLoaderServiceProvider
} from 'testing/stubs/services/overlay-loader.service.provider';
import {
    StubOverlayRouteService, StubOverlayRouteServiceProvider
} from 'testing/stubs/services/overlay-route.service.provider';
import {
    StubQuotesService, StubQuotesServiceProvider
} from 'testing/stubs/services/quotes.service.provider';

import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { OverlayRouteService } from 'app/overlay/services/overlay-route.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Helpers } from 'app/utils/helpers';

import { AsideSavePanelComponent } from './aside-save-panel.component';

describe('Component: AsideSavePanel', () => {
  let component: AsideSavePanelComponent;
  let fixture: ComponentFixture<AsideSavePanelComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [
        FormsModule,
        RouterTestingModule
      ],
      declarations: [
        AsideSavePanelComponent,
        StubModalboxComponent,
        StubLeaveQuoteComponent
      ],
      providers: [
        StorageService,
        StubQuotesServiceProvider,
        StubOverlayLoaderServiceProvider,
        StubOverlayRouteServiceProvider,
        StubCoveragesServiceProvider,
        StubLeaveQuoteComponent
      ]
    })
    .compileComponents();
  }));

  describe('when quote data is available', () => {
    beforeEach(fakeAsync(inject([StorageService], (storageService: StorageService) => {
      storageService.setStorageData('selectedQuote', Helpers.deepClone(QUOTE_DATA));

      fixture = TestBed.createComponent(AsideSavePanelComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
      tick();
    })));

    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should destroy without errors', () => {
      expect(() => {
        fixture.destroy();
      }).not.toThrow();
    });

    it('should handle save quote request', fakeAsync(inject([QuotesService, StorageService],
      (quotesService: StubQuotesService, storageService: StorageService) => {
        spyOn(quotesService, 'saveQuote').and.callThrough();
        spyOn(storageService, 'setStorageData').and.callThrough();

        component.saveQuote();
        tick();

        expect(quotesService.saveQuote).toHaveBeenCalled();
        expect(storageService.setStorageData).toHaveBeenCalledWith('selectedQuote', QUOTE_DATA);
        // expect(storageService.setStorageData).toHaveBeenCalledWith('isQuoteSaved', true);
      })));

    it('should require basic client data to be known before saving', fakeAsync(inject([QuotesService, StorageService, OverlayRouteService],
      (quotesService: StubQuotesService, storageService: StorageService, overlayService: StubOverlayRouteService) => {
        const modifiedQuote = Helpers.deepClone(QUOTE_DATA);
        modifiedQuote.client.lastName = '';

        storageService.setStorageData('selectedQuote', modifiedQuote);

        spyOn(quotesService, 'saveQuote').and.callThrough();
        spyOn(storageService, 'setStorageData').and.callThrough();
        spyOn(overlayService, 'go');

        component.saveQuote();
        tick();

        expect(quotesService.saveQuote).not.toHaveBeenCalled();
        // expect(storageService.setStorageData).not.toHaveBeenCalledWith('isQuoteSaved', jasmine.anything());
        expect(overlayService.go).toHaveBeenCalled();
      })));

    it('should allow to save a quote copy with other description', fakeAsync(inject([QuotesService],
      (quotesService: StubQuotesService) => {
        spyOn(quotesService, 'saveAsQuote').and.callThrough();

        const desc = 'Save as description';
        const textarea = fixture.debugElement.query(By.css('.aside-save-panel__modal-quote-description')).nativeElement;
        textarea.value = desc;
        textarea.dispatchEvent(new Event('input'));
        fixture.detectChanges();
        tick();

        const btn = fixture.debugElement.query(By.css('.aside-save-panel__btn'));
        btn.triggerEventHandler('click', {});

        expectLastCallArgs(quotesService.saveAsQuote).toEqual([
          jasmine.any(String),
          jasmine.objectContaining({
            description: desc
          })
        ]);
      })));

    it('should prettify lastModifiedDate string', () => {
      expect(component.lastModifiedDate).toEqual('Monday, May 15, 2017 at 4:38 AM');
    });
  });

  describe('when lastModifiedDate is not available', () => {
    beforeEach(inject([StorageService], (storageService: StorageService) => {
      const modifiedQuote = Helpers.deepClone(QUOTE_DATA);
      modifiedQuote.lastModifiedDate = undefined;
      storageService.setStorageData('selectedQuote', modifiedQuote);

      fixture = TestBed.createComponent(AsideSavePanelComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
    }));

    it('should create', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('when lastModifiedDate is today', () => {
    beforeEach(inject([StorageService], (storageService: StorageService) => {
      storageService.setStorageData('selectedQuote', Helpers.deepClone(QUOTE_DATA));

      jasmine.clock().mockDate(new Date(QUOTE_DATA.lastModifiedDate));

      fixture = TestBed.createComponent(AsideSavePanelComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
    }));

    it('should have "today" in lastModifiedDate property', () => {
      expect(component.lastModifiedDate.toLowerCase()).toContain('today');
    });
  });

  describe('when lastModifiedDate is yesterday', () => {
    beforeEach(inject([StorageService], (storageService: StorageService) => {
      storageService.setStorageData('selectedQuote', Helpers.deepClone(QUOTE_DATA));

      const date = new Date(QUOTE_DATA.lastModifiedDate);
      date.setDate(date.getDate() + 1);
      jasmine.clock().mockDate(date);

      fixture = TestBed.createComponent(AsideSavePanelComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
    }));

    it('should have "yesterday" in lastModifiedDate property', () => {
      expect(component.lastModifiedDate.toLowerCase()).toContain('yesterday');
    });
  });
});
