import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { QuoteRecallStoredComponent } from './quote-recall-stored.component';
import { OverlayLoaderService } from '../../shared/services/overlay-loader.service';
import { QuotesService } from '../app-services/quotes.service';
import { RouterTestingModule } from '@angular/router/testing';
import { StorageService } from '../../shared/services/storage-new.service';

describe('QuoteImporterComponent', () => {
  let component: QuoteRecallStoredComponent;
  let fixture: ComponentFixture<QuoteRecallStoredComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      declarations: [ QuoteRecallStoredComponent ],
      providers: [QuotesService, StorageService, OverlayLoaderService]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(QuoteRecallStoredComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
