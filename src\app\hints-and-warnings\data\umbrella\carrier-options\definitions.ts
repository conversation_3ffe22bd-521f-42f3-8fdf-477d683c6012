import { WARNING_GROUPS, WarningDefinitionI, AdditionalDataI } from 'app/hints-and-warnings/model/warnings';
import { Validate } from 'app/hints-and-warnings/validators';
import { CoverageItemParsed } from 'app/app-model/coverage';

function validPolicyItemParsed(value, fullObj:CoverageItemParsed):boolean {
  // return fullObj.isRequired && !fullObj.isActive && !fullObj.isDisabled;
  return fullObj.isRequired && !fullObj.isDisabled && Validate.isEmptyValue(fullObj.currentValue);
}

function generateUqOptionViewId(policy:CoverageItemParsed):string {
  return policy.viewUqId;
}

// Umbrella Carrier Options Tab,
//------------------------------------------------------------------------------
/**
 * Validation for Umbrella Carrier Options Data
 * For Model: CoverageItemParsed
 */

function generateViewUrlCarrierOptions(fullObj: CoverageItemParsed):string {
  return '/dashboard/umbrella/quotes/' + fullObj.quoteResourceId + '/options';
}

const policyItemUmbrellaParsedCarrierOption:WarningDefinitionI = {
  id: 'currentValue',
  deepId: 'currentValue',
  viewUri: generateViewUrlCarrierOptions,
  viewFieldId: generateUqOptionViewId,
  warnings: [{
    label: (val, fullObj:CoverageItemParsed) => 'Required ' + fullObj.description  + '.',
    condition: validPolicyItemParsed,
    group: WARNING_GROUPS.carrier,
    carriers: (fullObj: CoverageItemParsed) => {
      if (fullObj.additionalData && fullObj.additionalData.requiredForCarriers && fullObj.additionalData.requiredForCarriers.length) {
        return fullObj.additionalData.requiredForCarriers;
      } else {
        return fullObj.ratingPlanId ? fullObj.ratingPlanId.split(',') : [];
      }
    }
  }]
};

export const WARNINGS_DEFINITIONS_UMBRELLA_OPTIONS_CARRIER_OPTIONS: WarningDefinitionI[] = [
  policyItemUmbrellaParsedCarrierOption
];