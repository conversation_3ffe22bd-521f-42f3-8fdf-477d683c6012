import { AgencyUserService } from './../../shared/services/agency-user.service';
import { CoverageApiResponseData, CoveragesData, Coverage, CoverageItemValue, CoverageValue, CoverageItem } from 'app/app-model/coverage';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { Injectable } from '@angular/core';
import { Observable,  throwError as throwError } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { HttpErrorResponse, HttpClient, HttpHeaders } from '@angular/common/http';
import { CoverageApiSpecResponseData, QuoteCoverageApiResponseData } from 'app/app-model/com-coverage';
import { Quote } from 'app/app-model/quote';

@Injectable()
export class ComAutoPolicyCoverageService {
  private coverageApiResponseData: CoverageApiSpecResponseData;
  private quoteCoverageApiResponseData: QuoteCoverageApiResponseData;
  private coveragesData: CoveragesData;
  // private specUri = 'assets/data/policy-standard-coverages.json';
  private specUri = '/specs/ratingCoverages/state/MA/lob/AUTOB?coverageGroups=PolicyCoveragesStandard';
  private DOCspecUri = '/specs/ratingCoverages/state/MA/lob/AUTOB?coverageGroups=driveOtherCar';
  private CSLspecUri = '/specs/CslCoverageOptions?&state=MA&lob=autob';
  private quoteCoverageBaseUri = '/quotes/id/coverages';
  private quoteDriveOtherCarBaseUri = `/quotes/id/quotecoveragesDriveOtherCar`;


  private httpOptions = {
    headers: new HttpHeaders({ 'Content-Type': 'application/json', 'Authorization': 'c31z' })
  };

  constructor(
    private apiCommonService: ApiCommonService,
    private httpClient: HttpClient,
    private agencyUserService: AgencyUserService
  ) { }

  // Get Spec coverage for coverage group: 'PolicyStandardCoverage'
  public GetQuoteCoverageSpecByUri(): Observable<CoverageApiSpecResponseData> {
    // return this.httpClient.get<CoverageApiSpecResponseData>(this.specUri)
    return this.apiCommonService.getByUri(this.specUri, CoverageApiResponseData)
    .pipe(
      catchError(this.handleError)
    );
  }

  public GetQuoteDOCCoverageSpecByUri(): Observable<CoverageApiSpecResponseData> {
    // return this.httpClient.get<CoverageApiSpecResponseData>(this.specUri)
    return this.apiCommonService.getByUri(this.DOCspecUri, CoverageApiResponseData)
    .pipe(
      catchError(this.handleError)
    );
  }

  public GetQuoteCoverageCSLSpecByUri(planIds: string[]): Observable<any[]> {
    const planIdParam = planIds.join(',');
    // return this.httpClient.get<CoverageApiSpecResponseData>(this.specUri)
    let uri = '';
    if (planIds != null && planIdParam !== '') {
      uri = this.CSLspecUri + '&planIds=' + planIds;
    }

    return this.apiCommonService.getByUri(uri, CoverageApiResponseData)
    .pipe(
      catchError(this.handleError)
    );
  }

    // Get Policy level coverage (Quote Coverage)
    public GetQuoteCoverages(quoteId: string): Observable<QuoteCoverageApiResponseData> {
      const uri = this.quoteCoverageBaseUri.replace(/id/gi, quoteId);
      return this.apiCommonService.getByUri(uri, CoveragesData).pipe(catchError(this.handleError));
    }

  // Create Policy level coverage (Quote Coverage)
  public CreateQuoteCoverages(quoteId: string, data: any= {}): Observable<any> {
    const uri = this.quoteCoverageBaseUri.replace(/id/gi, quoteId);
    return this.apiCommonService.postByUri(uri, data).pipe(catchError(this.handleError));
  }

  // Update Policy level coverage (Quote Coverage)
  public UpdateQuoteCoverages(quoteId: string, data: CoveragesData): Observable<CoveragesData> {
    const uri = data.meta.href;
    return this.apiCommonService.putByUri(uri, data).pipe(catchError(this.handleError));
  }

  public GetDriveOtherCarCoverage(quoteid) {
    const uri = this.quoteDriveOtherCarBaseUri.replace(/id/gi, quoteid);
    return this.apiCommonService.getByUri(uri, CoveragesData).pipe(catchError(this.handleError));
  }

  public CreateDriveOtherCarCoverage(quoteid, data) {
    const uri = this.quoteDriveOtherCarBaseUri.replace(/id/gi, quoteid);
    return this.apiCommonService.postByUri(uri, data).pipe(catchError(this.handleError));
  }

  public UpdateDriveOtherCarCoverage(quoteid, data) {
    const uri = data.meta.href;
    return this.apiCommonService.putByUri(uri, data).pipe(catchError(this.handleError));
  }

  // Delete Policy level coverage (Quote Coverage)
  public DeleteQuoteCoverages(quoteId: string): Observable<any> {
    const uri = this.quoteCoverageBaseUri.replace(/id/gi, quoteId);
    return this.apiCommonService.deleteByUri(uri).pipe(catchError(this.handleError));
  }

  //#region Private Methods
  private handleError(err: HttpErrorResponse) {
    let errorMessage = '';
    if (err.error instanceof ErrorEvent) {
      errorMessage = `An error occurred: ${err.error.message}`;
    } else {
      errorMessage = `Server returned code: ${err.status}, error message is: ${err.message}`;
    }

    console.log(errorMessage);

    return throwError(errorMessage);
  }
  //#endregion


  // CREATE POLICY INFO COVERAGES
  // ---------------------------------------------------------------------------

  public buildInitialQuoteCoverageFromSpecWithDefault(specQuoteCoverages: CoverageItem[], driveOtherCarSpec = false): CoveragesData {
    const quoteCoverages: CoveragesData = new CoveragesData();
    let agencyId = '';
   this.agencyUserService.userData$.subscribe(agency => agencyId = agency.agencyId);
   let uri = `/subs/${agencyId}/Coveragedefaults/autobpolicy`;
   if(driveOtherCarSpec) { uri = `/subs/${agencyId}/Coveragedefaults/driveOtherCar`;}
   this.apiCommonService.getByUri(uri).subscribe(data => {
     data.items.map(cov => {
        const coverage: Coverage = new Coverage;
        coverage.values = cov.values;
        coverage.coverageCode = cov.coverageCode;
        coverage.coverageDescription = cov.coverageDescription;
        quoteCoverages.coverages.push(coverage);
      });

   })

    return quoteCoverages;
  }


  public createPolicyInfoCoveragesWithDefaults(quote: Quote, specQuoteCoverages: CoverageItem[]): Promise<CoveragesData> {
    return new Promise((resolve, reject) => {
      const coverageData: CoveragesData =  this.buildInitialQuoteCoverageFromSpecWithDefault(specQuoteCoverages);
      setTimeout(() => {
 this.CreateQuoteCoverages(quote.resourceId, coverageData)
        .subscribe((res: CoveragesData) => {
          resolve(res);
        }, err => reject(err));
      }
      ,1000)

    });
  }
}
