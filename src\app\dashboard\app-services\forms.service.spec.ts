import { inject, TestBed } from '@angular/core/testing';

import { DataCustomMatchers, expect } from 'testing/helpers/all';

import { FormsService } from './forms.service';

describe('Service: Forms', () => {
  let service: FormsService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        FormsService
      ]
    });

    jasmine.addMatchers(DataCustomMatchers);
  });

  beforeEach(inject(
    [FormsService],
    (_service: FormsService) => {
      service = _service;
    }));

  it('can instantiate service when inject service', () => {
    expect(service).toBeTruthy();
  });

});
