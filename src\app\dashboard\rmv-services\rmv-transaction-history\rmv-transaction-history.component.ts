import { Component, OnInit, ViewChild } from '@angular/core';
import { SearchByNameComponent } from 'app/dashboard/search-by-name/search-by-name.component';
import { FilterOption } from 'app/app-model/filter-option';
import { StorageGlobalService } from '../../../shared/services/storage-global.service';
import { AgencyUserService } from 'app/shared/services/agency-user.service';
import { SubsService } from 'app/dashboard/app-services/subs.service';
import { DateRange } from 'app/app-model/date';
import { DatesService } from '../../app-services/dates.service';
import { FilterComponent } from '../../../shared/components/filter/filter.component';
import {format} from 'date-fns';
@Component({
    selector: 'app-rmv-transaction-history',
    templateUrl: './rmv-transaction-history.component.html',
    styleUrls: ['./rmv-transaction-history.component.scss'],
    standalone: false
})
export class RmvTransactionHistoryComponent implements OnInit {
  constructor(private storageGlobalService: StorageGlobalService,
              private subsService: SubsService,
              private datesService: DatesService,
              private agencyUserService: AgencyUserService) { }
  @ViewChild(SearchByNameComponent) searchProperty;
  @ViewChild('refFilterDates') public FilterDates: FilterComponent;

  public paginationResultsCount = 0;
  public paginationResultLimit = 10;
  public paginationResultShowFrom = 0;
  public paginationCurrentPage = 1;

  searchQuery;
  arrTransactionsAll;

  // filter setups
  public filterAgentsOptions: FilterOption[] = [{ id: 'all', text: 'All' }];
  private filterAgentsSelectedOption: FilterOption = this
    .filterAgentsOptions[0];
  public filterAgentsLabel: string;

  public filterStatusOptions: FilterOption[] = [{ id: 'all', text: 'All' }];
  private filterStatusSelectedOption: FilterOption = this
    .filterStatusOptions[0];
  private filterStatusLabel: string;

  public filterTransactionOptions: FilterOption[] = [{ id: 'all', text: 'All' }];
  private filterTransactionSelectedOption: FilterOption = this
    .filterStatusOptions[0];
  private filterTransactionLabel: string;

  public filterDatesLabel: string;
  public filterDatesTypesOptions: FilterOption[] = this.datesService.getDatesTypesOptions();

  public filterDatesTypesSelectedOption: FilterOption = this
    .filterDatesTypesOptions[0];

  public filterDatesRangeOptions: FilterOption[] = this.datesService.getDateRangesOptions(this.filterDatesTypesOptions[0]);
  public filterDatesRangeSelectedOption: FilterOption = this
    .filterDatesRangeOptions[0];

  public dateRange: DateRange = new DateRange();
  public dateRangeCustom: DateRange = new DateRange();

  getAgencyUsersSubscription;
  agencyUserServiceSubscription;
  agencyId;

  private dataToChange: any;

  ngOnInit() {
  }

  generateRouterLinkValue() {
    // TODO::
  }

  filterByName() {
    // TODO
  }

  private getAgents(): FilterOption[] {
    const agents = this.storageGlobalService.takeSubs('users');
    if (agents.length) {
      return this.parseAgentsData(agents);
    } else {
      this.agencyUserServiceSubscription = this.agencyUserService.userData$.subscribe(
        agent => {
          this.agencyId = agent.agencyId;

          this.getAgencyUsersSubscription = this.subsService
            .getAgencyUsers(agent.agencyId)
            .subscribe(agents => {
              this.storageGlobalService.setSubs('users', agents.items);
              return this.parseAgentsData(agents.items);
            });
        }
      );
    }
  }

  private parseAgentsData(agents) {
    const tmpArr: FilterOption[] = [{ id: 'all', text: 'All Agents' }];
    agents.map(agent => {
      tmpArr.push({
        id: agent.userId,
        text: agent.firstName + ' ' + agent.lastName
      });
    });
    this.filterAgentsOptions = tmpArr;
    return tmpArr;
  }

  public onFilterDataChange($ev): void {
    this.filterDataChangeUpdate($ev);
    // this.filterResults();
  }
  private filterDataChangeUpdate($ev): void {
    if (
      this.dataToChange &&
      ($ev.selectedOption.id !== this.dataToChange.selectedOption.id ||
        $ev.selectedOption.id === 'all')
    ) {
      this.dataToChange = $ev;
      switch ($ev.filterId) {
        case 'filter_transactions':
        //  this.onLobChange($ev.selectedOption);
          break;
        case 'filter_agents':
        //  this.onAgentChange($ev.selectedOption);
          break;
        case 'filter_status':
        //  this.onLocationChange($ev.selectedOption);
          break;
      }

      // this.getTransactions(false, true);
    } else {
      this.dataToChange = $ev;
    }
  }

  private onDateTypeChange(option: FilterOption): void {
    this.filterDatesTypesSelectedOption = option;
  }

  private onDateRangeChange($ev: FilterOption): void {
    this.dateRange = this.getDateRangeFromSelectedOption(
      $ev,
      this.dateRangeCustom
    );
    this.filterDatesRangeSelectedOption = $ev;
  }


  private updateDateRangeOptionIfCustomRange(): void {
    this.filterDatesRangeSelectedOption = this.filterDatesRangeOptions.filter(
      opt => opt.id === 'custom'
    )[0];
  }

  public onDatesParamsChangeHandler($ev: any, updateField: string): void {
    // https://bostonsoftware.atlassian.net/browse/SPRC-560
    // When Date Range selection is Custom, filtering shouldn't begin until Ending date is entered
    let allowFiltering = true;
    switch (updateField) {
      case 'dateType':
        this.onDateTypeChange($ev);
        break;
      case 'dateRange':
        this.onDateRangeChange($ev);
        break;
      case 'dateRangeStart':
        this.updateRangeDateProperty($ev, 'start');
        $ev.selectByClick && this.updateDateRangeOptionIfCustomRange();
        if (!this.dateRange.start || !this.dateRange.end) {
          allowFiltering = false;
        }
        break;
      case 'dateRangeEnd':
        this.updateRangeDateProperty($ev, 'end');
        $ev.selectByClick && this.updateDateRangeOptionIfCustomRange();
        if (!this.dateRange.start || !this.dateRange.end) {
          allowFiltering = false;
        }
        break;
    }

    this.updateFilterDatesLabel();
  }

  getTransactionsFromFilteredDates() {
    let allowFiltering = true;
    if (
      this.filterDatesRangeSelectedOption.id !== 'all' &&
      (!this.dateRange.start || !this.dateRange.end)
    ) {
      allowFiltering = false;
    }
    if (allowFiltering) {
      // TODO:: this.getTransactions(false, true);
    }

    this.FilterDates.tooltip.close();
  }

  resetSearch() {
    this.filterDatesRangeSelectedOption = this.filterDatesRangeOptions[0];
    this.dateRange.start = null;
    this.dateRange.end = null;
    this.searchQuery = null;
   // this.getQuotes(false, true);
  }

  private updateFilterDatesLabel(): void {
    this.filterDatesLabel = this.formatFilterLabelDateTypes();
  }

  private formatFilterLabelDateTypes(): string {
    let formatedLabel = 'Last 7 days';
    const startDate = this.dateRange.start
      ? format(new Date(this.dateRange.start), 'MMM d, yyyy')
      : null;
    const endDate = this.dateRange.end
      ? format(new Date(this.dateRange.end), 'MMM d, yyyy')
      : null;

    if (!this.dateRange.start && !this.dateRange.end) {
      formatedLabel = 'Last 7 days';
    } else if (this.dateRange.start && this.dateRange.end) {
      formatedLabel =
        startDate === endDate ? `${startDate}` : `${startDate} - ${endDate}`;
    } else if (this.dateRange.start && !this.dateRange.end) {
      formatedLabel = `From ${startDate}`;
    } else if (!this.dateRange.start && this.dateRange.end) {
      formatedLabel = `To ${endDate}`;
    }

    if (this.filterDatesTypesSelectedOption.id !== 'all') {
      formatedLabel += ` (${this.filterDatesTypesSelectedOption.text})`;
    }

    return formatedLabel;
  }

  private getDateRangeFromSelectedOption(
    $ev: FilterOption,
    customDateRange: DateRange
  ): DateRange {
    switch ($ev.id) {
      case 'all':
        return new DateRange(null, null);
      case 'custom':
        return customDateRange;
      case 'today':
        return this.datesService.getRangeToday();
      case 'yesterday':
        return this.datesService.getRangeYesterday();
      case 'thisweek':
        return this.datesService.getRangeThisWeek();
      case 'thismonth':
        return this.datesService.getRangeThisMonth();
      case 'thisyear':
        return this.datesService.getRangeThisYear();
      case 'lastweek':
        return this.datesService.getRangeLastWeek();
      case 'lastmonth':
        return this.datesService.getRangeLastMonth();
      case 'last30days':
        return this.datesService.getRangeLast30days();
      case 'lastyear':
        return this.datesService.getRangeLastYear();
      case 'nextweek':
        return this.datesService.getRangeNextWeek();
      case 'nextmonth':
        return this.datesService.getRangeNextMonth();
      case 'nextyear':
        return this.datesService.getRangeNextYear();
      default:
        return new DateRange();
    }
  }

  private updateRangeDateProperty($ev, fieldName: string): void {
    let tmpDate = $ev.date;

    switch (fieldName) {
      case 'start':
        tmpDate = this.datesService.getDateStartRangeFromDate($ev.date);
        this.dateRangeCustom.start = $ev.selectByClick
          ? tmpDate
          : this.dateRangeCustom.start;
        break;
      case 'end':
        tmpDate = this.datesService.getDateEndRangeFromDate($ev.date);
        this.dateRangeCustom.end = $ev.selectByClick
          ? tmpDate
          : this.dateRangeCustom.end;
        break;
    }

    this.dateRange[fieldName] = tmpDate;
  }

  private paginationSetResultLimit(intLimit: any) {
    this.paginationResultLimit = parseInt(intLimit, 10);
  }

  parseLastModifiedDate(date: string): string {
    return format(new Date(date), 'MMM d, yyyy');
  }

  public onResultLimitChange($ev): void {
    setTimeout(() => {
      this.paginationSetResultLimit($ev.limit);
     // this.getTransactions();
    });
  }

}
