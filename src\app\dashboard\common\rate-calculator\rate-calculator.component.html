  <app-loader [loading]="loading" [cssClass]="'loader--with-opacity'" [loadingText]="'Calculating, please wait...'"></app-loader>

  <h2 class="o-heading">Prorate / Short Rate Calculator</h2>

  <div class="box box--silver u-spacing--1-5">
    <div class="row">
      <div class="col-xs-12">
        <table class="form-table">
          <tbody>
            <tr class="form-table__row">
              <td class="form-table__cell u-width-150px">Effective Date:</td>
              <td class="form-table__cell">
                <app-datepicker-input
                  #refModelEffectiveDate
                  [returnDateFormat]="'MM/dd/yyyy'"
                  [required]="true"
                  [selectDate]="effectiveDate"
                  (onDateChange)="effectiveDateChange($event)">
                </app-datepicker-input>
              </td>
            </tr>
            <tr class="form-table__row">
              <td class="form-table__cell u-width-150px">Change Date:</td>
              <td class="form-table__cell">
                <app-datepicker-input
                  #refModelEffectiveDate
                  [returnDateFormat]="'MM/dd/yyyy'"
                  [required]="true"
                  [selectDate]="changeDate"
                  (onDateChange)="changeDateChange($event)">
                </app-datepicker-input>
              </td>
            </tr>
            <tr class="form-table__row">
              <td class="form-table__cell u-width-150px">Original Amount:</td>
              <td class="form-table__cell">
                <input #ref="ngModel" required name="originalAmount" id="driverSdip" [(ngModel)]="originalAmount" mask="separator.0" thousandSeparator="," decimalMarker="." [allowNegativeNumbers]="false" prefix="$">
              </td>
            </tr>
          </tbody>
        </table>

      </div>
    </div>
  </div>

  <div class="row u-spacing--2 u-spacing--bottom-2">
    <div class="col-xs-12 u-align-center u-color-sunset">
      <p *ngIf="errorMessage">{{errorMessage}}</p>
      <p *ngIf="!errorMessage && calculationResult != null " [ngSwitch]="responseTypeSelected">
        <span *ngSwitchCase="responseTypes.proRate">Prorated amount is</span>
        <span *ngSwitchCase="responseTypes.shortRate">Short rate amount is</span>
        {{numberToMoneyValue(calculationResult)}}
      </p>
    </div>
  </div>

  <div class="row u-spacing--1-5">
    <div class="col-xs-12 u-align-right u-remove-letter-spacing">
      <button type="button" (click)="actionProrate()" class="o-btn">Prorate</button>
      <button type="button" (click)="actionShortRate()" class="o-btn u-spacing--left-0-5">Short Rate</button>
      <button type="button" (click)="actionReset()" class="o-btn o-btn--outlined u-spacing--left-0-5">Reset</button>
      <button type="button" (click)="actionClose()" class="o-btn o-btn--idle u-spacing--left-0-5">Close</button>
    </div>
  </div>
