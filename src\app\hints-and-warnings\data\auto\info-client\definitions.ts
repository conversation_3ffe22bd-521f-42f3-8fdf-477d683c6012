import { WARNING_GROUPS, WarningDefinitionI, AdditionalDataI, AdditionalDataAutoClientInfo} from 'app/hints-and-warnings/model/warnings';
import { ClientAddress, ClientContactMethod, ClientDetails } from 'app/app-model/client';
import { Validate } from 'app/hints-and-warnings/validators';
import { CoverageItemParsed } from 'app/app-model/coverage';

import { subMonths } from 'date-fns';

function requiredField(value): boolean {
  if (value === undefined || value == null) {
    return true;
  }
  value = String(value);

  return value.trim().length <= 0 || !value;
}

function validateZipCode(zip): boolean {
  if (zip === '' || zip === null) {
    return false;
  }

  const zipPattern = /^\d{5}(?:-?\d{4})?$/;
  return !zipPattern.test(zip);
}

function generateViewUrl(): string {
  return  '{{current_url}}?overlay=info&type=client';

}

/**
 * Validation for Client Info Data
 * For Model: ClientDetails
 */


const clientInfoFirstName: WarningDefinitionI = {
  id: 'firstName',
  deepId: 'firstName',
  viewUri: generateViewUrl,
  viewFieldId: 'clientInfoPrimaryFirstName',
  warnings: [{
    label: 'Required First Name for client.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const clientInfoLastName: WarningDefinitionI = {
  id: 'lastName',
  deepId: 'lastName',
  viewUri: generateViewUrl,
  viewFieldId: 'clientInfoPrimaryLastName',
  warnings: [{
    label: 'Required Last Name for client',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const clientInfoDOB: WarningDefinitionI = {
  id: 'dob',
  deepId: 'dob',
  viewUri: generateViewUrl,
  viewFieldId: 'clientInfoPrimaryDOB',
  warnings: [{
    label: 'Required Date of Birth for client.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const clientInfoSecondaryDOB: WarningDefinitionI = {
  id: 'secondaryDOB',
  deepId: 'secondaryDOB',
  viewUri: generateViewUrl,
  viewFieldId: 'clientInfoSecondaryDOB',
  warnings: [{
    label: 'Required Date of Birth for secondary client.',
    condition: (val, fullObj: ClientDetails, additionalData: AdditionalDataI) => {
      return (fullObj.secondaryFirstName && fullObj.secondaryLastName && !fullObj.secondaryDOB);
    },
    group: WARNING_GROUPS.general,
    carriers: []
  },
  {
    label: (val, fullObj) => 'Required Date of Birth for secondary client.',
    condition: (val, fullObj: ClientDetails, additionalData) => {
      if ((fullObj.secondaryFirstName || fullObj.secondaryLastName) && !fullObj.secondaryDOB) {
       return true;
      }
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }
]
};

const clientInfoSecondaryFirstName: WarningDefinitionI = {
  id: 'secondaryFirstName',
  deepId: 'secondaryFirstName',
  viewUri: generateViewUrl,
  viewFieldId: 'clientSecondaryFirstName',
  warnings: [{
    label: 'Required First Name for secondary client',
    condition: (val, fullObj: ClientDetails, additionalData: AdditionalDataI) => {
      return((fullObj.secondaryLastName || fullObj.secondaryDOB) && !fullObj.secondaryFirstName);
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const clientInfoSecondaryLastName: WarningDefinitionI = {
  id: 'secondaryLastName',
  deepId: 'secondaryLastName',
  viewUri: generateViewUrl,
  viewFieldId: 'clientSecondaryLastName',
  warnings: [{
    label: 'Required Last Name for secondary client',
    condition: (val, fullObj: ClientDetails, additionalData: AdditionalDataI) => {
      return((fullObj.secondaryFirstName || fullObj.secondaryDOB) && !fullObj.secondaryLastName);
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};



export const WARNINGS_DEFINITIONS_INFO_CLIENT_FOR_AUTO: WarningDefinitionI[] = [
  clientInfoFirstName,
  clientInfoLastName,
  clientInfoDOB,
  clientInfoSecondaryDOB,
  clientInfoSecondaryLastName,
  clientInfoSecondaryFirstName
];


// Client Addresses
// ------------------------------------------------------------------------------

/**
 * Validation for Client Info Addresses Data
 * For Model: ClientAddress
 */

function generateAddressFieldId(addr: ClientAddress, customName: string): string {
  return (addr && addr.addressType) ? customName + '_' + addr.addressType : customName;
}

function checkIfMoveInDateIsLessThanTwoMonths(addresses: ClientAddress[]): boolean {
  let result = false;

  const currentAddress = addresses.find(addr => addr.addressType === 'StreetAddress');

  if (currentAddress && currentAddress.residencyDate) {
   const differenceFromNow = subMonths(new Date(), 2);

const moveIn = new Date(currentAddress.residencyDate);
    const isLessThanTwoMonths = moveIn > differenceFromNow;
    result = isLessThanTwoMonths;
  }

  return result;
}

function validatePriorAddressBasedOnMoveInDate(value: string, fullObj: ClientAddress, additionalData: AdditionalDataAutoClientInfo): boolean {
  if (fullObj.addressType === 'PreviousAddress') {
    const isLessThanTwoMonth = (additionalData.observedData && additionalData.observedData.length) ? checkIfMoveInDateIsLessThanTwoMonths(<ClientAddress[]>additionalData.observedData) : false;

    return isLessThanTwoMonth && Validate.isRequiredForSelectedPlansIfEmptyValue(
      value,
      priorAddresDataRequiredForCarriers,
      additionalData.quoteSelectedPlansIds
    );
  }

  return false;
}

const priorAddresDataRequiredForCarriers = ['26']; // Progressive


const clientInfoAddrAddr1: WarningDefinitionI = {
  id: 'address1',
  deepId: 'address1',
  viewUri: generateViewUrl,
  viewFieldId: (fullObj: ClientAddress) => generateAddressFieldId(fullObj, 'clientInfoAddrAddr1'),
  warnings: [{
    label: 'Required Current Client Address',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataAutoClientInfo) => {
      return fullObj.addressType === 'StreetAddress' && Validate.isEmptyValue(value);
    },
    group: WARNING_GROUPS.general,
    carriers: []
  },
  {
    label: 'Required Current Client Prior Address',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataAutoClientInfo) => {
      return validatePriorAddressBasedOnMoveInDate(value, fullObj, additionalData);
    },
    group: WARNING_GROUPS.carrier,
    carriers: priorAddresDataRequiredForCarriers
  },
  {
    label: 'Required Current Client Prior Address Street',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataAutoClientInfo) => {
      return((fullObj.zip || fullObj.state || fullObj.city) && !fullObj.address1 && fullObj.addressType === 'PreviousAddress');
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }, ]
};


const clientInfoAddrCity: WarningDefinitionI = {
  id: 'city',
  deepId: 'city',
  viewUri: generateViewUrl,
  viewFieldId: (fullObj: ClientAddress) => generateAddressFieldId(fullObj, 'clientInfoAddrCity'),
  warnings: [{
    label: 'Required Current Client City',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataAutoClientInfo) => {
      return fullObj.addressType === 'StreetAddress' && Validate.isEmptyValue(value);
    },
    group: WARNING_GROUPS.general,
    carriers: []
  },
  {
    label: 'Required Current Client Prior Address City',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataAutoClientInfo) => {
      return validatePriorAddressBasedOnMoveInDate(value, fullObj, additionalData);
    },
    group: WARNING_GROUPS.carrier,
    carriers: priorAddresDataRequiredForCarriers
  },
  {
    label: 'Required Current Client Prior Address City',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataAutoClientInfo) => {
      return((fullObj.address1 || fullObj.state || fullObj.zip) && !fullObj.city && fullObj.addressType === 'PreviousAddress');
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const clientInfoAddrState: WarningDefinitionI = {
  id: 'state',
  deepId: 'state',
  viewUri: generateViewUrl,
  viewFieldId: (fullObj: ClientAddress) => generateAddressFieldId(fullObj, 'clientInfoAddrState'),
  warnings: [{
    label: 'Required Current Client State',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataAutoClientInfo) => {
      return fullObj.addressType === 'StreetAddress' && Validate.isEmptyValue(value);
    },
    group: WARNING_GROUPS.general,
    carriers: []
  },
  {
    label: 'Required Current Client Prior Address State',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataAutoClientInfo) => {
      return validatePriorAddressBasedOnMoveInDate(value, fullObj, additionalData);
    },
    group: WARNING_GROUPS.carrier,
    carriers: priorAddresDataRequiredForCarriers
  },
  {
    label: 'Required Current Client Prior Address State',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataAutoClientInfo) => {
      return((fullObj.address1 || fullObj.zip || fullObj.city) && !fullObj.state && fullObj.addressType === 'PreviousAddress');
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

const clientInfoAddrZip: WarningDefinitionI = {
  id: 'zip',
  deepId: 'zip',
  viewUri: generateViewUrl,
  viewFieldId: (fullObj: ClientAddress) => generateAddressFieldId(fullObj, 'clientInfoAddrZip'),
  warnings: [{
    label: 'Required Current Client Zip',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataAutoClientInfo) => {
      return fullObj.addressType === 'StreetAddress' && Validate.isEmptyValue(value);
    },
    group: WARNING_GROUPS.general,
    carriers: []
  },
  {
    label: 'Required Current Client Prior Address ZIP',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataAutoClientInfo) => {
      return validatePriorAddressBasedOnMoveInDate(value, fullObj, additionalData);
    },
    group: WARNING_GROUPS.carrier,
    carriers: priorAddresDataRequiredForCarriers
  },
  {
    label: 'Required Current Client Prior Address ZIP',
    condition: (value: any, fullObj: ClientAddress, additionalData: AdditionalDataAutoClientInfo) => {
      return((fullObj.address1 || fullObj.state || fullObj.city) && !fullObj.zip && fullObj.addressType === 'PreviousAddress');
    },
    group: WARNING_GROUPS.general,
    carriers: []
  },
  {
    label: 'Client Prior Address Zip code is not Valid',
    condition: (value: any, fullObj: ClientAddress) => {
      if (fullObj && fullObj.addressType !== null && fullObj.addressType === 'PreviousAddress') {
        return validateZipCode(value);
      }
    },
    group: WARNING_GROUPS.general,
    carriers: []
  },
  {
    label: 'Client Current Address Zip code is not Valid',
    condition: (value: any, fullObj: ClientAddress) => {
      if (fullObj && fullObj.addressType !== null && fullObj.addressType === 'StreetAddress') {
        return validateZipCode(value);
      }
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }
]
};



export const WARNINGS_DEFINITIONS_INFO_CLIENT_ADDRESSES_FOR_AUTO: WarningDefinitionI[] = [
  clientInfoAddrAddr1,
  clientInfoAddrCity,
  clientInfoAddrState,
  clientInfoAddrZip
];



// Client Contact Methods
// ------------------------------------------------------------------------------

/**
 * Validation for Client Info Addresses Data
 * For Model: ClientContactMethod
 */

function generateContactMethodViewFieldId(fullObj: ClientContactMethod): string {
  return fullObj.type ? 'cliContact' + fullObj.type : 'no_id';
}


// Required for:
// Concord Group
const clientInfoContactMethodEmailCarriers = ['21'];
const clientInfoContactMethodEmail: WarningDefinitionI = {
  id: 'value',
  deepId: 'value',
  viewUri: generateViewUrl,
  viewFieldId: generateContactMethodViewFieldId, // 'cliContactEmail',
  warnings: [{
    label: 'Required Email address for client.',
    condition: (value: any, fullObj: ClientContactMethod, additionalData: AdditionalDataAutoClientInfo) => {

      if (fullObj.type === 'Email') {
        // https://bostonsoftware.atlassian.net/browse/SPR-2069
        let tmpEDocumentDiscountOption: CoverageItemParsed;

        if (additionalData.carrierOptionsParsed.length) {
          tmpEDocumentDiscountOption = additionalData.carrierOptionsParsed
            .find(el => el.coverageCode === 'BSC-AUTO-002003');
        }

        if (tmpEDocumentDiscountOption && tmpEDocumentDiscountOption.isActive) {
          return Validate.isEmptyValue(fullObj.value);
        }
      }

      return false;
    },
    group: WARNING_GROUPS.carrier,
    carriers: clientInfoContactMethodEmailCarriers
  },
  {
    label: (val: string, fullObj: ClientContactMethod) => 'Client\'s Phone number incorrect: ' + fullObj.type,
    condition: (value: string, fullObj: ClientContactMethod) => {

      switch (fullObj.type) {
        case 'MobilePhone':
        case 'HomePhone':
        case 'BusinessPhone':
          if (value && !Validate.isValidPhoneNumber(value)) {
            return true;
          }
          break;
        default:
          return false;
      }

      return false;
    },
    group: WARNING_GROUPS.general,
    carriers: [],
  },
  {
    label: (val: string, fullObj: ClientContactMethod) => 'Client\'s Email address incorrect.',
    condition: (value: string, fullObj: ClientContactMethod) => {
      if (fullObj.type === 'Email' && value && !Validate.isValidEmail(value)) {
        return true;
      }

      return false;
    },
    group: WARNING_GROUPS.general,
    carriers: [],
  },
  {
    label: (val: string, fullObj: ClientContactMethod) => 'Client\'s Fax Number incorrect.',
    condition: (value: string, fullObj: ClientContactMethod) => {
      if (fullObj.type === 'Fax' && value && !Validate.isValidPhoneNumber(value)) {
        return true;
      }

      return false;
    },
    group: WARNING_GROUPS.general,
    carriers: [],
  }]
};


export const WARNINGS_DEFINITIONS_INFO_CLIENT_CONTACT_METHODS_FOR_AUTO: WarningDefinitionI[] = [
  clientInfoContactMethodEmail
];
