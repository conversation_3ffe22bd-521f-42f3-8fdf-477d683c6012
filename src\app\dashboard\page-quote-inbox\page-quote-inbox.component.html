<section class="section section--thick">
  <div class="row">
    <div class="col-xs-12">
      <div class="u-flex u-flex--to-middle">
        <div class="search">
          <input #searchInput [id]="'filter_NAME'" [(ngModel)]="searchQuery" class="search__input search__input--md"
            type="text" placeholder="Search By Name">
          <button *ngIf="searchQuery && searchQuery.length > 0" (click)="resetSearch()" name="cancel"
            class="cancel__button"></button>
        </div>

        <button (click)="filterByName($event)" name="searchbtn" class="o-btn u-spacing--left-2"
          [disabled]="searchQuery && searchQuery.length < 3">Search</button>
      </div>
    </div>
  </div>
  <span style="color: red" *ngIf="searchQuery && searchQuery.length < 3">Minimum search length is 3</span>
</section>

<section class="section">
  <div class="row">
    <div class="col-xs-12">

      <app-filter *ngIf="filterStatusOptions?.length" [id]="'filter_Status'" [name]="'Status'" [hasSearch]="false"
        [selectedOption]="filterStatusSelectedOption"
        [options]="filterStatusOptions" [label]="filterStatusLabel"
        (onChange)="onFilterDataChange($event)"></app-filter>

      <app-filter *ngIf="filterSourceOptions?.length" [id]="'filter_Source'" [name]="'Source'" [hasSearch]="false"
      [selectedOption]="filterSourceSelectedOption"
      [options]="filterSourceOptions" [label]="filterSourceLabel"
        (onChange)="onFilterDataChange($event)"></app-filter>

      <app-filter-dates (getByFilter)="getLeadsFromFilteredDates()"></app-filter-dates>

    </div>
    <div class="u-float-right">
      <!--<div class="trash trash--select u-float-left"><span class="u-t-upper u-color-slate-grey" ><input type="checkbox" class="o-btn--checkbox u-show-inline" (click)="toggleAll()" [checked]="isChecked()"/>Select All</span></div> -->
      <div class="trash trash--select u-float-left">
        <span class="u-t-upper u-color-slate-grey">
          <label class="o-checkable">
            <input type="checkbox" class="o-btn--checkbox u-show-inline" (click)="toggleAll()"
              [checked]="isChecked()" />
            <i class="o-btn o-btn--checkbox"></i>
            Select All
          </label>
        </span>
      </div>
      <div class="trash trash--img u-float-right"><a id="delete-leads-modal"
          class="u-t-upper u-color-slate-grey">Delete</a></div>
      <app-confirmbox [launcher]="'#delete-leads-modal'" [question]="'Are you sure you want to delete selected Leads?'"
        [confirmBtnText]="'Yes, delete'" (onAccept)="deleteSelectedQuotes()" (onCancel)="confirmCanceled($event)">
      </app-confirmbox>
    </div>
  </div>
</section>

<section class="section">
  <div class="row">
    <div class="col-xs-12">
      <table class="table table--compact table--hoverable-grey">
        <thead class="table__thead">
          <tr class="">
            <th class="table__th ">LOB</th>
            <th class="table__th ">Name</th>
            <th class="table__th ">City &amp; State</th>
            <th class="table__th ">Date</th>
            <th class="table__th ">Source</th>
            <th class="table__th ">Labels</th>
            <th class="table__th ">Status</th>
            <th class="table__th ">Open</th>
            <th class="table__th u-align-right">Select</th>
          </tr>
        </thead>
        <tbody class="table__tbody" *ngIf="arrLeadsAll">
          <!-- tr class="table__tr" *ngFor="let row of quoteInbox | limitTo:paginationResultLimit:paginationResultShowFrom; let i = index;" -->
          <tr class="table__tr" *ngFor="let row of arrLeadsAll;">
            <td class="table__td ">
              <i class="o-icon o-icon--md o-icon--i-{{row.lob}} table__td-icon"></i>
            </td>
            <td class="table__td u-color-pelorous">
              <a (click)="setShowLead(row)" class="modal-launcher-quote-inbox">{{row.insuredName}}</a>
            </td>
            <td class="table__td ">{{row.city}}{{row.city ? ', ' : ''}} {{row.state}}</td>
            <td class="table__td">{{row.receiveDate | date:'mediumDate'}}</td>
            <td class="table__td">{{row.source}}</td>
            <td class="table__td">{{row.labels}}</td>
            <td class="table__td">{{row.viewStatus}}</td>
            <td class="table__td">
              <a class="o-link o-link--blue" (click)="rate(row)">Open</a>
            </td>
            <td class="table__td u-align-right">
              <label class="o-checkable">
                <input type="checkbox" class="o-btn--checkbox" [checked]="isSelected(row.leadId)"
                  (click)="toggleLead(row.leadId)" />
                <i class="o-btn o-btn--checkbox"></i>
              </label>
            </td>
          </tr>
        </tbody>
      </table>

      <app-modalbox #refModalQuoteInbox [launcher]="'.modal-launcher-quote-inbox'">

        <app-loader [loading]="modalboxLoadingData" [cssClass]="''"
          [loadingText]="'Loading Quote Details...'"></app-loader>

        <h1 class="o-heading o-heading--red">{{selectedLeadDetail.contact.name}}</h1>
        <hr>
        <mat-tab-group *ngIf="selectedLeadDetail.contact" @.disabled disableRipple mat-stretch-tabs #tabGroup>
          <mat-tab @.disabled label="Contact">
            <div class="box box--silver u-spacing--1-5">

              <table class="table table--compact table--flex table--no-edge-borders">
                <tbody class="table__tbody">
                  <tr class="table__tr">
                    <td class="table__td  u-width-150px">Name:</td>
                    <td *ngIf="selectedLeadDetail.contact.name" class="table__td u-color-charcoal">
                      {{selectedLeadDetail.contact.name}}</td>
                  </tr>
                  <tr class="table__tr">
                    <td class="table__td u-width-150px">City:</td>
                    <td *ngIf="selectedLeadDetail.contact.city" class="table__td u-color-charcoal">
                      {{selectedLeadDetail.contact.city}}</td>
                  </tr>
                  <tr class="table__tr">
                    <td class="table__td u-width-150px">Phone:</td>
                    <td *ngIf="selectedLeadDetail.contact.leadPhone" class="table__td u-color-charcoal">
                      {{selectedLeadDetail.contact.leadPhone.phone}}</td>
                  </tr>
                  <tr class="table__tr">
                    <td class="table__td u-width-150px">Email:</td>
                    <td *ngIf="selectedLeadDetail.contact.leadEmail" class="table__td u-color-charcoal">
                      {{selectedLeadDetail.contact.leadEmail.email}}</td>
                  </tr>
                  <!--<tr class="table__tr">
                            <td class="table__td u-width-150px">Primary Residence:</td>
                            <td class="table__td u-color-charcoal">{{selectedLeadDetail.contact.primaryResidence}}</td>
                          </tr>-->
                </tbody>
              </table>
            </div>
          </mat-tab>
          <mat-tab *ngIf="selectedLeadDetail.drivers" label="Driver">
            <div *ngFor="let driver of selectedLeadDetail.drivers;let idx = index"
              class="box box--silver u-spacing--1-5">
              <table *ngIf="driver" class="table table--compact table--flex table--no-edge-borders">
                <tr class="table__tr" style="background-color: #1989C9">
                  <td class="table__td  u-width-150px" style="color: white"> Driver: {{idx+1}}</td>
                </tr>
                <tr class="table__tr">
                  <td class="table__td  u-width-150px">FirIstname:</td>
                  <td class="table__td u-color-charcoal">{{driver.firstName}}</td>
                </tr>
                <tr class="table__tr">
                  <td class="table__td u-width-150px">Lastname:</td>
                  <td class="table__td u-color-charcoal">{{driver.lastName}}</td>
                </tr>
                <tr class="table__tr">
                  <td class="table__td u-width-150px">Date Of Birth:</td>
                  <td class="table__td u-color-charcoal">{{driver.dateOfBirth}}</td>
                </tr>
                <tr class="table__tr">
                  <td class="table__td u-width-150px">Merit:</td>
                  <td class="table__td u-color-charcoal">{{driver.meritRatingPoints}}</td>
                </tr>
              </table>

            </div>
          </mat-tab>
          <mat-tab *ngIf="selectedLeadDetail.vehicles" label="Vehicle">
            <div *ngFor="let vehicle of selectedLeadDetail.vehicles;let idx = index"
              class="box box--silver u-spacing--1-5">
              <table class="table table--compact table--flex table--no-edge-borders">
                <tbody *ngIf="vehicle" class="table__tbody">
                  <tr class="table__tr" style="background-color: #1989C9">
                    <td class="table__td  u-width-150px" style="color: white">Vehicle: {{idx+1}}</td>
                  </tr>
                  <tr class="table__tr">
                    <td class="table__td  u-width-150px">Year:</td>
                    <td class="table__td u-color-charcoal">{{vehicle.year}}</td>
                  </tr>
                  <tr class="table__tr">
                    <td class="table__td u-width-150px">Make:</td>
                    <td class="table__td u-color-charcoal">{{vehicle.make}}</td>
                  </tr>
                  <tr class="table__tr">
                    <td class="table__td u-width-150px">Primary Use:</td>
                    <td class="table__td u-color-charcoal">{{vehicle.primaryUse}}</td>
                  </tr>
                  <tr class="table__tr">
                    <td class="table__td u-width-150px">Optional BI:</td>
                    <td class="table__td u-color-charcoal">{{vehicle.coverageRequested}}</td>
                  </tr>
                  <tr class="table__tr">
                    <td class="table__td u-width-150px">Collision Deductible:</td>
                    <td class="table__td u-color-charcoal">{{vehicle.collisionDeductible}}</td>
                  </tr>
                  <tr class="table__tr">
                    <td class="table__td u-width-150px">Comprehensive Deductible:</td>
                    <td class="table__td u-color-charcoal">{{vehicle.comprehensiveDeductible}}</td>
                  </tr>
                  <tr class="table__tr">
                    <td class="table__td u-width-150px">Property Damage:</td>
                    <td class="table__td u-color-charcoal">{{vehicle.propertyDamage}}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </mat-tab>
          <mat-tab *ngIf="selectedLeadDetail.home" label="Home">
            <div class="box box--silver u-spacing--1-5">
              <table class="table table--compact table--flex table--no-edge-borders">
                <tbody class="table__tbody">
                  <tr class="table__tr">
                    <td class="table__td  u-width-150px">Insurance Type</td>
                    <td class="table__td u-color-charcoal">{{selectedLeadDetail.home.insuranceType}}</td>
                  </tr>
                  <tr class="table__tr">
                    <td class="table__td  u-width-150px">Year Built</td>
                    <td class="table__td u-color-charcoal">{{selectedLeadDetail.home.yearBuilt}}</td>
                  </tr>
                  <tr class="table__tr">
                    <td class="table__td  u-width-150px">Residence</td>
                    <td class="table__td u-color-charcoal">{{selectedLeadDetail.home.residence}}</td>
                  </tr>
                  <tr class="table__tr">
                    <td class="table__td  u-width-150px">Current Coverage</td>
                    <td class="table__td u-color-charcoal">{{selectedLeadDetail.home.currentCoverage}}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </mat-tab>
          <mat-tab *ngIf="selectedLeadDetail.coverage" label="Coverage">
            <div class="box box--silver u-spacing--1-5">

              <table class="table table--compact table--flex table--no-edge-borders">
                <tbody class="table__tbody">
                  <tr class="table__tr">
                    <td class="table__td  u-width-150px">Liability</td>
                    <td class="table__td u-color-charcoal">{{selectedLeadDetail.coverage.liability}}</td>
                  </tr>
                  <tr class="table__tr">
                    <td class="table__td  u-width-150px">Medical Payments</td>
                    <td class="table__td u-color-charcoal">{{selectedLeadDetail.coverage.medicalPayments}}</td>
                  </tr>
                  <tr class="table__tr">
                    <td class="table__td  u-width-150px">Dwelling Replacement Cost</td>
                    <td class="table__td u-color-charcoal">{{selectedLeadDetail.coverage.dwellingReplacementCost}}</td>
                  </tr>
                  <tr class="table__tr">
                    <td class="table__td  u-width-150px">Content Replacement Cost</td>
                    <td class="table__td u-color-charcoal">{{selectedLeadDetail.coverage.contentReplacementCost}}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </mat-tab>
          <mat-tab *ngIf="selectedLeadDetail.quotes" label="Quotes">
            <div class="box box--silver u-spacing--1-5">
              <table class="table table--compact table--flex table--no-edge-borders">
                <tbody class="table__tbody">
                  <tr *ngFor="let quote of selectedLeadDetail.quotes" class="table__tr">
                    <td *ngIf="quote" class="table__td  u-width-150px">{{quote.name}}</td>
                    <td *ngIf="quote" class="table__td u-color-charcoal">{{quote.premium}}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </mat-tab>
          <mat-tab *ngIf="selectedLeadDetail.discounts" label="Discounts">
            <div class="box box--silver u-spacing--1-5">

              <table class="table table--compact table--flex table--no-edge-borders">
                <tbody class="table__tbody">
                  <tr class="table__tr">
                    <td class="table__td  u-width-150px">Payment Method</td>
                    <td class="table__td u-color-charcoal">{{selectedLeadDetail.discounts.paymentMethod}}</td>
                  </tr>
                  <tr class="table__tr">
                    <td class="table__td  u-width-150px">Motor Club</td>
                    <td class="table__td u-color-charcoal">{{selectedLeadDetail.discounts.motorClub}}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </mat-tab>

        </mat-tab-group>



        <div class="row u-spacing--2 u-align-right">
          <div class="col-xs-12">
            <button class="o-btn u-spacing--right-2" (click)="rate(showLead)">Open</button>
            <button class="o-btn o-btn--outlined u-spacing--right-2" id="delete-lead-modal">Delete</button>
            <button class="o-btn o-btn--idle" (click)="closeModalBox()">Cancel</button>

            <app-confirmbox [launcher]="'#delete-lead-modal'"
              [question]="'Are you sure you want to delete selected Lead?'" [askAbout]="selectedLeadDetail.contact.name"
              [confirmBtnText]="'Yes, delete lead'" (onAccept)="deleteSelectedQuote(showLead, refModalQuoteInbox)"
              (onCancel)="confirmCanceled($event)">
            </app-confirmbox>
          </div>
        </div>
      </app-modalbox>

    </div>
  </div>
</section>

<section class="section">
  <div class="u-flex u-flex--to-middle">
    <div class="">
      <app-pagination [totalRecords]="paginationResultsCount" [recordsLimit]="paginationResultLimit"
        (onPageChange)="paginationPageChange($event)"></app-pagination>
    </div>
    <div class="" style="padding-left:10rem">
      <app-results-limiter (onChange)="onResultLimitChange($event)"></app-results-limiter>
    </div>
  </div>
</section>
