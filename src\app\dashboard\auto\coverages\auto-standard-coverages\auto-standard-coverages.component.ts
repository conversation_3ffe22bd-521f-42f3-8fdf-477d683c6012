import { Quote } from './../../../../app-model/quote';

import { take } from 'rxjs/operators';
import { Component, OnInit, OnDestroy, ViewChild, AfterViewInit } from '@angular/core';
import { StorageService } from 'app/shared/services/storage-new.service';
import { SubscriptionLike as ISubscription } from 'rxjs';
import { QuoteAuto, QuotePlan, QuotePlanListAPIResponse } from 'app/app-model/quote';
import { Vehicle, VehicleCoverages } from 'app/app-model/vehicle';
import { CoveragesData, CoverageItem, CoverageItemParsed, CoverageItemValue, Coverage } from 'app/app-model/coverage';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { AgencyUserService } from 'app/shared/services/agency-user.service';
import { VehiclesService } from 'app/dashboard/app-services/vehicles.service';
import { OptionsService } from 'app/dashboard/app-services/options.service';
import { ModalboxComponent } from 'app/shared/components/modalbox/modalbox.component';
import { PremiumsService } from 'app/dashboard/auto/premiums/premiums.service';
import { CoveragesService } from '../../../app-services/coverages.service';


class CoveragesTableData {
  constructor(
    public rows: CoveragesTableRow[] = []
  ) { }
}

class CoveragesTableRow {
  public label = '';
  public cells: CoveragesTableRowCell[] = [];
}

class CoveragesTableRowCell {
  constructor(
    public coverageId: string = '',
    public vehicleCoverages: VehicleCoverages = null,
    public policyParsedToDisplay: CoverageItemParsed = new CoverageItemParsed(),
    public coverageOptions: string[] = [],
    public onlyOneAvailableOption: boolean = false,
  ) { }
}

/**
 * key - vehicles resourceId
 * value - 'Default' | 'Custom'
 */
interface IActiveOptionOverallCoverage {
  [key: string]: string;
}

type TOverallCoverageOptions = 'Default' | 'Custom';
type TOverallCoverageActiveOption = '' | 'Default' | 'Custom';



@Component({
    selector: 'app-auto-standard-coverages',
    templateUrl: './auto-standard-coverages.component.html',
    styleUrls: ['./auto-standard-coverages.component.scss'],
    standalone: false
})
export class AutoStandardCoveragesComponent implements OnInit, OnDestroy, AfterViewInit {
  effectiveDateMsgAlreadyDisplayed: boolean;

  constructor(
    private storageService: StorageService,
    private specsService: SpecsService,
    private agencyUserService: AgencyUserService,
    private vehiclesService: VehiclesService,
    private optionsService: OptionsService,
    private premiumsService: PremiumsService,
    private coverageService: CoveragesService
  ) { }

  @ViewChild('modalCoveragesValidation') public modalCoveragesValidation: ModalboxComponent;
  @ViewChild('modalCoveragesAdditionalInformation') public modalCoveragesAdditionalInformation: ModalboxComponent;
  @ViewChild('modalQuoteEffectiveDate') public modalQuoteEffectiveDay: ModalboxComponent;

  private subscriptionQuote: ISubscription;
  private subscriptionSelectedPlans: ISubscription;
  private subscriptionVehicles: ISubscription;
  private subscriptionAutoCoveragesStandardForVehicles: ISubscription;
  private subscriptionAutoCoveragesAdditionalForVehicles: ISubscription;


  private quote: QuoteAuto = new QuoteAuto();
  private selectedPlans: QuotePlan[] = [];
  private selectedPlansIds: string[] = [];
  private vehicles: Vehicle[] = [];
  public vehiclesStandardCoverages: VehicleCoverages[] = [];
  public coveragesTableData: CoveragesTableData = new CoveragesTableData();

  public optionsOverallCoverages = ['Custom'];
  public activeOptionsOverallCoverages: IActiveOptionOverallCoverage = {};
  public activeDefaultOptions = '';
  public errorMsg: string[] = [];

  private vehiclesAdditionalCoverages: VehicleCoverages[] = [];

  // Additional Coverages Management
  // ---------------------------------------------------------------------------
  private additionalVehicleCoveragesToAdjust: VehicleCoverages = null;
  private additionalCoverageToUpdate: CoverageItemParsed = null;

ngAfterViewInit() {

}

  ngOnInit() {
    let agencyId;
    this.agencyUserService.userData$.pipe(take(1)).subscribe(agent => agencyId = agent.agencyId);
    this.coverageService.getCoveragesAsOptions(agencyId).subscribe(x => {
      this.optionsOverallCoverages = x;
    } );

  this.activeDefaultOptions = localStorage.getItem('autoDefault');

    Promise.all([
      this.subscribeQuote(),
      this.subscribeSelectedPlans(),
      this.subscribeVehicles()
    ])
      .then(() => this.subscribeAutoCoveragesAdditionalForVehicles())
      .then(() => {
        this.subscribeAutoCoveragesStandardForVehicles();
      }).catch(err => console.log(err));

  }

  ngOnDestroy() {
    this.subscriptionQuote && this.subscriptionQuote.unsubscribe();
    this.subscriptionSelectedPlans && this.subscriptionSelectedPlans.unsubscribe();
    this.subscriptionVehicles && this.subscriptionVehicles.unsubscribe();
    this.subscriptionAutoCoveragesStandardForVehicles && this.subscriptionAutoCoveragesStandardForVehicles.unsubscribe();
    this.subscriptionAutoCoveragesAdditionalForVehicles && this.subscriptionAutoCoveragesAdditionalForVehicles.unsubscribe();
  }

  // get Selected Quote
  private subscribeQuote(): Promise<QuoteAuto> {
    return new Promise((resolve, reject) => {

      this.subscriptionQuote = this.storageService.getStorageData('selectedQuote')
        .subscribe((quote: QuoteAuto) => {
          const comparisonDate = new Date('2025-07-01');
          const QuoteEffectiveDate = new Date(quote.effectiveDate);
          this.storageService.getStorageData('isNewQuote').subscribe((isNewQuote: boolean) => {
          this.storageService.getStorageData('autoCoveragesStandardForVehicles').subscribe((res: VehicleCoverages[]) => {
            const BI = res[0].options.find(x => x.coverageCode === 'BI').currentValue === '25/50';
          console.log(BI);
            if (QuoteEffectiveDate >= comparisonDate && !this.effectiveDateMsgAlreadyDisplayed && BI && !isNewQuote) {
            this.modalQuoteEffectiveDay?.open();
            this.effectiveDateMsgAlreadyDisplayed = true;
          }
          resolve(this.quote);
        });
      })
      }
      );
    });
  }

  // get selected plans
  private subscribeSelectedPlans(): Promise<QuotePlan[]> {
    return new Promise((resolve, reject) => {
      this.subscriptionSelectedPlans = this.storageService.getStorageData('selectedPlan')
        .subscribe((res: QuotePlanListAPIResponse) => {
          if (res && res.items && res.items.length) {
            this.selectedPlans = JSON.parse(JSON.stringify(res.items[0].items));
            this.selectedPlansIds = this.selectedPlans.map(plan => plan.ratingPlanId);
            resolve(this.selectedPlans);
          }
        });
    });
  }

  private subscribeVehicles(): void {
    this.subscriptionVehicles = this.storageService.getStorageData('vehiclesList').subscribe(data => {
      this.vehicles = data;
    });
  }


  // Get Standard coverages for Vehicles
  private subscribeAutoCoveragesStandardForVehicles(): void {
    this.subscriptionAutoCoveragesStandardForVehicles = this.storageService.getStorageData('autoCoveragesStandardForVehicles')
      .subscribe((res: VehicleCoverages[]) => {
        this.vehiclesStandardCoverages = this.sortVehiclesStandardCoverages(res, this.vehicles);
        // TODO:: sort according to the vehicle list

        this.setOverallCoverageActiveOptions(this.vehiclesStandardCoverages);
        this.generateCoveragesTableDataBasedOnVehicleCoverages(this.vehiclesStandardCoverages);
        // console.log('>>>', this.vehiclesStandardCoverages);
      });
  }

  // Get Additional coverages for Vehicles
  private subscribeAutoCoveragesAdditionalForVehicles(): Promise<void> {
    return new Promise((resolve) => {
      this.subscriptionAutoCoveragesAdditionalForVehicles = this.storageService.getStorageData('autoCoveragesAdditionalForVehicles')
        .subscribe((res: VehicleCoverages[]) => {
          this.vehiclesAdditionalCoverages = res;
          // console.log('this.vehiclesAdditionalCoverages', this.vehiclesAdditionalCoverages);
          resolve();
          // this.vehiclesAdditionalCoverages = this.sortVehiclesAdditionalCoverages(res, this.vehicles);
        });
    });
  }


  // Collecting Data
  // ----------------------------------------------------------------------------

  private getAvailableCoveragesFromAPI(): Promise<CoverageItem[]> {
    const selectedPlansIdsString = this.selectedPlansIds.join(',');
    const states = 'MA';
    const quoteEffectiveDate = (this.quote && this.quote.effectiveDate) ? this.quote.effectiveDate : '';

    let agencyId;
    this.agencyUserService.userData$.pipe(take(1)).subscribe(agent => agencyId = agent.agencyId);

    return this.specsService.getRatingCoverages(states, this.quote.lob, 'vehicleStandard', selectedPlansIdsString, '', quoteEffectiveDate)
      .toPromise()
      .then((res) => {
        return res.items;
      });
  }


  private getAvailableCoverages(vehicleCoverages?: VehicleCoverages): Promise<CoverageItem[]> {
    return new Promise((resolve, reject) => {
      if (vehicleCoverages && vehicleCoverages.options && vehicleCoverages.options.length) {
        resolve(vehicleCoverages.options);
      } else {
        this.getAvailableCoveragesFromAPI()
          .then(res => resolve(res))
          .catch(err => reject(err));
      }
    });
  }

  // Parsing Data
  // ----------------------------------------------------------------------------
  private setOverallCoverageActiveOptions(vehicleCoverages: VehicleCoverages[]): void {
    this.activeOptionsOverallCoverages = {};
    vehicleCoverages.forEach(el => {
      const areCustomOptions = el.options.find(option => option.currentValue !== option.defaultValue);
      (areCustomOptions) ? console.log('NotDefaultCoverageOption' + areCustomOptions.description) : console.log('custom', vehicleCoverages);
       this.activeOptionsOverallCoverages[el.vehicleResourceId] = this.activeDefaultOptions;
    });
  }


  private generateCoveragesTableDataBasedOnVehicleCoverages(vehicleCoverages: VehicleCoverages[]) {
    const coveragesTableData: CoveragesTableData = new CoveragesTableData();

    // Make Sure that all the coverages for each Vehicle are in the same order
    vehicleCoverages.forEach((item: VehicleCoverages) => {
      item.options = this.sortItems(item.options);
    });

    this.getAvailableCoverages(vehicleCoverages[0])
      .then((res: CoverageItem[]) => {
        const tableRowsList: CoverageItem[] = this.sortItems(res);

        // Set up table Data
        tableRowsList.forEach(rowData => {
          const tableDataRow: CoveragesTableRow = new CoveragesTableRow();

          vehicleCoverages.forEach((data: VehicleCoverages) => {
            const tableDataCell: CoveragesTableRowCell = new CoveragesTableRowCell();
            tableDataRow.label = rowData.description;

            const relatedCoverageParsed: CoverageItemParsed = data.options.find(item => item.coverageCode === rowData.coverageCode);

            if (relatedCoverageParsed) {
              tableDataCell.coverageId = rowData.coverageCode;
              tableDataCell.coverageOptions = relatedCoverageParsed.values.map((val: CoverageItemValue) => val.value);
              tableDataCell.vehicleCoverages = data;
              tableDataCell.policyParsedToDisplay = relatedCoverageParsed;
              tableDataCell.onlyOneAvailableOption = tableDataCell.coverageOptions.length === 1;
            }

            tableDataRow.cells.push(tableDataCell);
          });

          coveragesTableData.rows.push(tableDataRow);
        });

        this.coveragesTableData = coveragesTableData;
      });
  }


  // View Methods
  // ----------------------------------------------------------------------------
  public displayVehicleNameByResourceId(vehicleResourceId: string): string {
    const vehicle = this.vehicles.find(item => item.resourceId === vehicleResourceId);
    const vehicleToDisplay = vehicle || new Vehicle();

    return this.vehiclesService.vehicleNameToDisplay(vehicleToDisplay);
  }

  public disabledSetOverallCoverageField(vehicle: Vehicle): boolean {
    return !vehicle.vehicleType || (vehicle.vehicleType && vehicle.vehicleType !== 'Private Passenger') ? true : false;
  }

  public actionOnOptionSelect($ev, policyParsed: CoverageItemParsed, vehiclesCoverages: VehicleCoverages): void {
    policyParsed.currentValue = $ev.id;
    this.validateAndAdjustVehicleCoveragesValues(policyParsed, vehiclesCoverages);
    vehiclesCoverages.defaultOption = 'Custom';
    localStorage.setItem('autoDefault', this.activeDefaultOptions);
    // TEST
    this.adjustAdditionalCoverages(policyParsed, vehiclesCoverages);
    // TEST --

    this.updateCoverages(vehiclesCoverages);
    this.premiumsService.rerateAll();
  }


  private updateCoverages(vehiclesCoverages: VehicleCoverages) {
    const allPoliciesParsedToConvert = vehiclesCoverages.options;
    const policiesToUpdate: Coverage[] = this.optionsService.convertPolicyItemParsedArrayToPolicyArray(allPoliciesParsedToConvert);
    const endpointUrl = vehiclesCoverages.options[0].endpointUrl;

    const dataToSend = {
      coverages: policiesToUpdate
    };

    this.optionsService.updatePoliciesByUri(endpointUrl, dataToSend).pipe(
      take(1))
      .subscribe(
        res => {
          // Update Storage
          this.storageService.updateAutoCoveragesStandardForVehiclesSingleItem(vehiclesCoverages);
        },
        err => console.log('ERROR Updating Coverages: ', err)
      );
  }

  public getOverallCoverageActiveOption(vehicleResourceId: string): string {
    return this.activeOptionsOverallCoverages[vehicleResourceId];
  }

  public actionOnSelectedOverallCoverage(event, vehicleCoverages: VehicleCoverages): void {
    if (event.id !== 'Custom') {
      let agencyId = '';
      this.agencyUserService.userData$.pipe(take(1)).subscribe(agent => agencyId = agent.agencyId);
       this.coverageService.getCoverage(agencyId, event.id).subscribe(
         x => {
           vehicleCoverages.options.forEach(el => {
        const defaultValue = x.items.find(cov => cov.coverageCode === el.coverageCode);

      if (defaultValue) {
        const hasValue = el.values.some((v: CoverageItemValue) => v.value === defaultValue.value);
        if (hasValue) {
          el.currentValue = defaultValue.value;
        } else {
          el.currentValue = null;
          el.additionalData.hasError = true;
          el.additionalData.errorMessage = 'Required';
        }
      }

      });
      localStorage.setItem('autoDefault', this.activeDefaultOptions);
      vehicleCoverages.defaultOption = event.id;
      this.activeOptionsOverallCoverages[vehicleCoverages.vehicleResourceId] = event.id;
      this.updateCoverages(vehicleCoverages);
         });

      this.premiumsService.rerateAll();
    }
  }

  // HELPER
  // ----------------------------------------------------------------------------
  private sortItems<T>(items: T[]): T[] {
    let result: T[] = [];

    result = items.sort((a, b) => {
      let valA, valB;

      valA = parseInt(a['description'], 10);
      valB = parseInt(b['description'], 10);

      if (isNaN(valA) && isNaN(valB)) {
        valA = a['description'].toString().toLowerCase();
        valB = b['description'].toString().toLowerCase();
      }

      return (valA < valB) ? -1 : (valA > valB) ? 1 : 0;
    });

    return result;
  }

  private sortVehiclesStandardCoverages(vehicleCoverages: VehicleCoverages[], vehicles: Vehicle[]): VehicleCoverages[] {
    const sorted: VehicleCoverages[] = [];

    vehicles.forEach(veh => {
      const item = vehicleCoverages.find(el => veh.resourceId === el.vehicleResourceId);

      if (item) {
        sorted.push(item);
      }
    });

    return sorted;
  }

  // private validateAndAdjustVehicleCoveragesValues(updatedPolicyParsed: CoverageItemParsed, vehiclesCoverages: VehicleCoverages): void {
  //   this.errorMsg = [];
  //   let result: IValidateVehiclesStandardCoveragesOptionResult = this.autoStandardCoveragesAutomanagerService
  //     .validateVehiclesStandardCoveragesOption(updatedPolicyParsed, vehiclesCoverages.options);
  //   this.errorMsg = result.errors;

  //   if (this.errorMsg.length) {
  //     this.modalCoveragesValidation.open();
  //   }
  // }


  private helpGetCoverageByCoverageCode(vehiclesCoverages: VehicleCoverages, coverageCode: string): CoverageItemParsed {
    const coverage = vehiclesCoverages.options.find(opt => opt.coverageCode === coverageCode);
    return coverage;
  }

  private helpGetCoverageValueAsNumber(coverage: CoverageItemParsed): number {
    if (coverage.currentValue === 'Omit') {
      return -1;
    } else {
      const coverageValueStringToConvert = coverage.currentValue.replace('/', '');
      return parseInt(coverageValueStringToConvert, 10);
    }
  }

  private validateAndAdjustVehicleCoveragesValues(updatedPolicyParsed: CoverageItemParsed, vehiclesCoverages: VehicleCoverages): void {
    this.errorMsg = [];
    const currentValueNumber = this.helpGetCoverageValueAsNumber(updatedPolicyParsed);

    const optionalBodilyInjuryCoverage = this.helpGetCoverageByCoverageCode(vehiclesCoverages, 'OPTBI');
    const optionalBodilyInjuryCoverageNumber = this.helpGetCoverageValueAsNumber(optionalBodilyInjuryCoverage);

    switch (updatedPolicyParsed.coverageCode) {
      /* Uninsured */
      case 'UM':
        if (optionalBodilyInjuryCoverage) {

          if (!isNaN(currentValueNumber)) {
            if ((currentValueNumber === 2040 || currentValueNumber === 2550) && optionalBodilyInjuryCoverageNumber === -1) {
              // MA requires Uninsured of 20/40 so when OPTBI is set to Omit we need to factor that in (this combination is permissible).
            } else if (!isNaN(optionalBodilyInjuryCoverageNumber) && currentValueNumber > optionalBodilyInjuryCoverageNumber) {
              this.errorMsg.push(updatedPolicyParsed.description + ': Limits may not exceed limits for Optional Bodily Injury.');
            }
          }
        }
        break;

      /* Optional Bodily */
      case 'OPTBI':
        const uninsuredCoverage = this.helpGetCoverageByCoverageCode(vehiclesCoverages, 'UM');
        const underinsiuredCoverage = this.helpGetCoverageByCoverageCode(vehiclesCoverages, 'UNDUM');

        if (uninsuredCoverage) {
          const uninsuredCoverageValue = this.helpGetCoverageValueAsNumber(uninsuredCoverage);
          const underinsiuredCoverageValue = this.helpGetCoverageValueAsNumber(underinsiuredCoverage);

          // https://bostonsoftware.atlassian.net/browse/SPRC-605
          if (!isNaN(currentValueNumber)) {
            let showMessage = false;
            let uninsuredPartMsg = '';
            let underinsuredPartMsg = '';
            let connector = '';

            if (!isNaN(uninsuredCoverageValue) && (uninsuredCoverageValue > currentValueNumber)) {
              if ((uninsuredCoverageValue === 2040 || uninsuredCoverageValue === 2550) && currentValueNumber === -1) {
                // MA requires Uninsured of 20/40 so when OPTBI is set to Omit we need to factor that in (this combination is permissible).
              } else {
                showMessage = true;
                uninsuredPartMsg = 'Uninsured';
              }
            }

            if (!isNaN(underinsiuredCoverageValue) && (underinsiuredCoverageValue > currentValueNumber)) {
              console.log(underinsiuredCoverageValue, currentValueNumber);
              if ((underinsiuredCoverageValue === 2040 || underinsiuredCoverageValue === 2550) && currentValueNumber === -1) {
                // MA requires Uninsured of 20/40 so when OPTBI is set to Omit we need to factor that in (this combination is permissible).
              } else {
              showMessage = true;
              underinsuredPartMsg = 'Underinsured';
              }
            }

            if (uninsuredPartMsg && underinsuredPartMsg) {
              connector = ' & ';
            }

            if (showMessage) {
              const msg = `Limits for ${uninsuredPartMsg}${connector}${underinsuredPartMsg} may not exceed Optional Bodily Injury.`;
              this.errorMsg.push(updatedPolicyParsed.description + ': ' + msg);
              // this.errorMsg.push(updatedPolicyParsed.description + ': Limits for Uninsured & Underinsured may not exceed Optional Bodily Injury.');
            }
          }

        }
        break;

      /* Collision */
      case 'COLL':
        const limitedCollisionCoverage = this.helpGetCoverageByCoverageCode(vehiclesCoverages, 'LCOLL');

        if (limitedCollisionCoverage && limitedCollisionCoverage.currentValue !== 'Omit') {
          this.errorMsg.push(updatedPolicyParsed.description + ': Limits cannot be set for both Collision and Limited. Limited is set to Omit.');
          limitedCollisionCoverage.currentValue = 'Omit';
          // // We need to adjust Additional coverages 'Waiver of Deductible'
          // this.adjustAdditionalCoverages(limitedCollisionCoverage, vehiclesCoverages);
        }

        break;

      /* Limited */
      case 'LCOLL':
        const collisionCoverage = this.helpGetCoverageByCoverageCode(vehiclesCoverages, 'COLL');

        if (collisionCoverage && collisionCoverage.currentValue !== 'Omit') {
          this.errorMsg.push(updatedPolicyParsed.description + ': Limits cannot be set for both Collision and Limited. Collision is set to Omit.');
          collisionCoverage.currentValue = 'Omit';
        }

        break;

      /* Underinsured */
      case 'UNDUM':
          if (optionalBodilyInjuryCoverage) {
          if (!isNaN(currentValueNumber)) {
            if ((currentValueNumber === 2040 || currentValueNumber === 2550) && optionalBodilyInjuryCoverageNumber === -1) {
              // MA requires Uninsured of 20/40 so when OPTBI is set to Omit we need to factor that in (this combination is permissible).
            } else if (!isNaN(currentValueNumber) &&
             !isNaN(optionalBodilyInjuryCoverageNumber) && currentValueNumber > optionalBodilyInjuryCoverageNumber) {
            this.errorMsg.push(updatedPolicyParsed.description + ': Limits may not exceed Optional Bodily Injury');
          }
        }
      }
        break;
    }

    if (this.errorMsg.length) {
      this.modalCoveragesValidation.open();
    }

  }


  private updateAdditionalCoverages(vehiclesCoverages: VehicleCoverages, carrierIds: Array<string> = null): void {
    const activePoliciesParsed: CoverageItemParsed[] = vehiclesCoverages.options.filter(item => item.isActive);
    const policiesToUpdate: Coverage[] = this.optionsService.convertPolicyItemParsedArrayToPolicyArray(activePoliciesParsed);
    const endpointUrl = vehiclesCoverages.options[0].endpointUrl;

    const dataToSend = {
      coverages: policiesToUpdate
    };


    if (carrierIds && carrierIds.length) {
      this.premiumsService.rerateById(carrierIds);
    }

    this.optionsService.updatePoliciesByUri(endpointUrl, dataToSend).pipe(
      take(1))
      .subscribe(
        (res: CoveragesData) => {
          // Update Storage
          this.storageService.updateAutoCoveragesAdditionalForVehiclesSingleItem(vehiclesCoverages);
        },
        err => console.log('ERROR Updating Additional Coverages: ', err)
      );
  }

  // https://bostonsoftware.atlassian.net/browse/SPR-2705

  private adjustAdditionalCoverages(policyParsed: CoverageItemParsed, standardVehicleCoverages: VehicleCoverages): void {
    const additionalVehicleCoveragesToAdjust: VehicleCoverages = this.vehiclesAdditionalCoverages.find((data: VehicleCoverages) => {
      return data.vehicleResourceId === policyParsed.additionalData.forVehicle.resourceId;
    });

    if (!additionalVehicleCoveragesToAdjust) {
      return console.log('Related Additional Coverages for vehicle not found');
    }

    if (policyParsed.coverageCode === 'LCOLL' || policyParsed.coverageCode === 'COLL') {
      this.handleWaiverOfDeductibleAdjust(additionalVehicleCoveragesToAdjust, standardVehicleCoverages, policyParsed.coverageCode);
    }

  }

  private handleWaiverOfDeductibleAdjust(additionalVehicleCoveragesToAdjust: VehicleCoverages, standardVehicleCoverages: VehicleCoverages, coverageCode: string): void {
    this.additionalVehicleCoveragesToAdjust = null;
    this.additionalCoverageToUpdate = null;

    if (additionalVehicleCoveragesToAdjust.options && additionalVehicleCoveragesToAdjust.options.length) {
      this.additionalVehicleCoveragesToAdjust = additionalVehicleCoveragesToAdjust;

      const tmpCoverageToUpdate: CoverageItemParsed = additionalVehicleCoveragesToAdjust.options.find((item: CoverageItemParsed) => {
        return item.coverageCode === 'BSC-AUTO-000037'; // Waiver of Deductible
      });

      if (tmpCoverageToUpdate) {
        this.additionalCoverageToUpdate = tmpCoverageToUpdate;

        const collisionCoverage = this.helpGetCoverageByCoverageCode(standardVehicleCoverages, 'COLL');
        const limitedCollisionCoverage = this.helpGetCoverageByCoverageCode(standardVehicleCoverages, 'LCOLL');

        if (collisionCoverage && limitedCollisionCoverage) {
          if (collisionCoverage.currentValue === 'Omit') {
            // console.log('Set Waiver of Deductible to false', tmpCoverageToUpdate);
            this.additionalCoverageToUpdate.isActive = false;
            this.additionalCoverageToUpdate.currentValue = 'false';
            const carriersToRerate: string[] = [this.additionalCoverageToUpdate.ratingPlanId];
            this.updateAdditionalCoverages(this.additionalVehicleCoveragesToAdjust, carriersToRerate);
          } else if (!this.additionalCoverageToUpdate.isActive && coverageCode !== 'LCOLL') {
            // console.log('ASK if the Waiver of Deductible should be set to true');
            this.modalCoveragesAdditionalInformation.open();
          }
        }
      }
    }
  }

  // private adjustAdditionalCoverages(policyParsed: CoverageItemParsed): void {
  //   const additionalVehicleCoveragesToAdjust: VehicleCoverages = this.vehiclesAdditionalCoverages.find((data: VehicleCoverages) => {
  //     return data.vehicleResourceId === policyParsed.additionalData.forVehicle.resourceId;
  //   });

  //   if (!additionalVehicleCoveragesToAdjust) {
  //     return console.log('Related Additional Coverages for vehicle not found');
  //   }

  //   switch(policyParsed.coverageCode) {
  //     case 'LCOLL':
  //       this.handleWaiverOfDeductibleAdjust(policyParsed, additionalVehicleCoveragesToAdjust);
  //       break;
  //   }

  // }

  // private handleWaiverOfDeductibleAdjust(policyParsedStandardCov: CoverageItemParsed, additionalVehicleCoveragesToAdjust: VehicleCoverages): void {
  //   this.additionalVehicleCoveragesToAdjust = null;
  //   this.policyParsedStandardCovCurrent = null;
  //   this.additionalCoverageToUpdate = null;

  //   if (additionalVehicleCoveragesToAdjust.options && additionalVehicleCoveragesToAdjust.options.length) {
  //     this.additionalVehicleCoveragesToAdjust = additionalVehicleCoveragesToAdjust;
  //     this.policyParsedStandardCovCurrent = policyParsedStandardCov;

  //     const tmpCoverageToUpdate: CoverageItemParsed = additionalVehicleCoveragesToAdjust.options.find((item: CoverageItemParsed) => {
  //       return item.coverageCode === 'BSC-AUTO-000037'; // Waiver of Deductible
  //     });

  //     if (tmpCoverageToUpdate) {
  //       this.additionalCoverageToUpdate = tmpCoverageToUpdate;

  //       if (policyParsedStandardCov.currentValue === 'Omit') {
  //         // console.log('Set Waiver of Deductible to false', tmpCoverageToUpdate);
  //         this.additionalCoverageToUpdate.isActive = false;
  //         this.additionalCoverageToUpdate.currentValue = 'false';
  //         const carriersToRerate: string[] = [this.additionalCoverageToUpdate.ratingPlanId];
  //         this.updateAdditionalCoverages(this.additionalVehicleCoveragesToAdjust, carriersToRerate);
  //       } else if (!this.additionalCoverageToUpdate.isActive) {
  //         // console.log('ASK if the Waiver of Deductible should be set to true');
  //         this.modalCoveragesAdditionalInformation.open();
  //       }
  //     }
  //   }
  // }

  public actionYesHandleWaiverOfDeductibleAdjust(ev: Event): void {
    ev.preventDefault();

    if (this.additionalVehicleCoveragesToAdjust &&
      this.additionalCoverageToUpdate) {
      this.additionalCoverageToUpdate.isActive = true;
      this.additionalCoverageToUpdate.currentValue = 'true';
      const carriersToRerate: string[] = [this.additionalCoverageToUpdate.ratingPlanId];
      this.updateAdditionalCoverages(this.additionalVehicleCoveragesToAdjust, carriersToRerate);
      this.modalCoveragesAdditionalInformation && this.modalCoveragesAdditionalInformation.close();
    }
  }

  trackByRow(index: number, row: any): number {
    return row.id;
  }

  trackByCell(index: number, cell: any): number {
    return cell.coverageId;
  }

}

