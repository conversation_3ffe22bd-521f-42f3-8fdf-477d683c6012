import { async, ComponentFixture, fakeAsync, inject, TestBed, tick } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';

import { DWELLING_PROTECTION_DEVICES } from 'testing/data/dwellings/protection-devices';
import { DWELLINGS } from 'testing/data/quotes/dwellings';
import { selectCheckbox } from 'testing/helpers/all';
import {
    StubDwellingService, StubDwellingServiceProvider
} from 'testing/stubs/services/dwelling.service.provider';
import {
    StubOverlayLoaderServiceProvider
} from 'testing/stubs/services/overlay-loader.service.provider';

import { ApiResponse } from 'app/app-model/_common';
import { DwellingProtectionDevices } from 'app/app-model/dwelling';
import { DwellingService } from 'app/dashboard/app-services/dwelling.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Helpers } from 'app/utils/helpers';

import { DwellingAlarmsComponent } from './dwelling-alarms.component';

describe('Component: DwellingAlarms', () => {
  let component: DwellingAlarmsComponent;
  let fixture: ComponentFixture<DwellingAlarmsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [ FormsModule ],
      declarations: [ DwellingAlarmsComponent ],
      providers: [
        StorageService,
        StubDwellingServiceProvider,
        StubOverlayLoaderServiceProvider
      ]
    })
    .compileComponents();
  }));

  describe('when all data is available in storage', () => {
    beforeEach(fakeAsync(inject([StorageService], (storageService: StorageService) => {
      storageService.setStorageData('dwelling', Helpers.deepClone(DWELLINGS.items[0]));
      storageService.setStorageData('dwellingProtectionDevices', Helpers.deepClone(DWELLING_PROTECTION_DEVICES.items[0]));

      fixture = TestBed.createComponent(DwellingAlarmsComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
      tick();
    })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });

    it('should update protection devices when checkbox status changes', fakeAsync(inject([DwellingService],
      (dwellingService: StubDwellingService) => {
        spyOn(dwellingService, 'updateDwellingProtectionDevices').and.callThrough();

        selectCheckbox('input', fixture);

        expect(dwellingService.updateDwellingProtectionDevices).toHaveBeenCalled();
    })));
  });

  describe('when only basic data is available in storage', () => {
    beforeEach(fakeAsync(inject([StorageService, DwellingService],
      (storageService: StorageService, dwellingService: StubDwellingService) => {
        storageService.setStorageData('dwelling', Helpers.deepClone(DWELLINGS.items[0]));
        dwellingService.dwellingProtectionDevices = Helpers.deepClone(DWELLING_PROTECTION_DEVICES);

        fixture = TestBed.createComponent(DwellingAlarmsComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
        tick();
      })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('when there are no protection devices in dwelling service', () => {
    beforeEach(fakeAsync(inject([StorageService, DwellingService],
      (storageService: StorageService, dwellingService: StubDwellingService) => {
        storageService.setStorageData('dwelling', Helpers.deepClone(DWELLINGS.items[0]));
        dwellingService.dwellingProtectionDevices = new ApiResponse<DwellingProtectionDevices>();

        fixture = TestBed.createComponent(DwellingAlarmsComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
        tick();
      })));

    it('should be created', () => {
      expect(component).toBeTruthy();
    });
  });
});
