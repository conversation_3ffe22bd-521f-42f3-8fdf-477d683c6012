import { ApiResponse, MetaData } from './_common';

export class Watercraft {
  constructor(
    public meta: MetaData = {href: '', rel: ['']},
    public propulsionType: string = null,
    public horsePower: string = null,
    public length: string = null,
    public speed: string = null,
    public quoteSessionId: string,
    public resourceId: string,
    public resourceName: string,
    public parentId: string
  ) { }
}

export class WatercraftsResponse extends ApiResponse<Watercraft> { }
