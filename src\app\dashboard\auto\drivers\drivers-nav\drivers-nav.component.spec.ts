import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';

import { SpecsService } from 'app/dashboard/app-services/specs.service';

import { StorageService } from '../../../../shared/services/storage-new.service';
import { DriversService } from '../../../app-services/drivers.service';
import { QuotesService } from '../../../app-services/quotes.service';

import { DriversNavComponent } from './drivers-nav.component';


describe('DriversNavComponent', () => {
  let component: DriversNavComponent;
  let fixture: ComponentFixture<DriversNavComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      declarations: [ DriversNavComponent ],
      providers: [
        SpecsService,
        DriversService,
        QuotesService,
        StorageService
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(DriversNavComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
