<div class="o-tpl o-tpl-dashboard">
  <header class="o-tpl__row o-tpl__header o-tpl-dashboard__header">
    <div class="o-tpl__size">
      <app-header></app-header>
    </div>
  </header>

  <main class="o-tpl__row  o-tpl__main o-tpl-dashboard__main">
    <aside class="o-tpl__col o-tpl__aside o-tpl-dashboard__aside" [ngStyle]="{'width': isRmvDashboard()? '0' : '' }">
      <section class="">
        <p-scrollPanel>
          <!-- app-nav-secondary></app-nav-secondary -->
          <router-outlet name="aside"></router-outlet>
        </p-scrollPanel>
      </section>
    </aside>

    <div class="o-tpl__col o-tpl__col--full o-tpl__content">
      <section class="o-tpl__col-section o-tpl-dashboard__main-content__head">
        <div class="o-tpl__size-content">
          <app-maip-arc-banner></app-maip-arc-banner>
          <app-notification *ngIf="plans" [plans]="plans"></app-notification>
          <router-outlet (activate)="updateNotification()" name="contentHead"></router-outlet>
        </div>
      </section>

      <section class="o-tpl__col-section">
        <div class="o-tpl__size-content">
          <router-outlet name="contentBox1"></router-outlet>
        </div>
      </section>

      <section class="o-tpl__col-section o-tpl__col-section--scroll js-state-change-scroll-top"
        id="dashboardMainScrollContainer">
        <div class="o-tpl__col-section-scroll o-tpl__col-section-scroll--add-bottom-space">
          <div class="o-tpl__size-content o-tpl__add-transition" id="warnings-component-interact"
          [class.o-tpl__add-warnings-space]="panelWarningsIsOpen">
          <router-outlet></router-outlet>
        </div>


        </div>
      </section>
      <div>
        <app-modalbox #evrLitePermitExpiration [isOpen]="showEvrLiteExpirationMessage">
          <div class="row">
            <div style="text-align:left; float:left;">
                 <p><b>{{evrLitePermitTopLeftMessage}}</b></p>
            </div>
            <div style="text-align:right; float: right;">
              <img style="width:auto;" src="assets/images/common/rmv_logo.png" alt="">
            </div>

          </div>

          <div class="box box--silver u-spacing--1-5">
            <div class="row" *ngIf="!isEvrLitePermitExpired">
              <p style="text-align: justify;">
                According to our records your EVR Permit, which is granted to your agency by the MA RMV to process vehicle Registrations and Titles, is set to expire in {{evrLitePermitExpirationMessage}}.
              </p>
              <br/>
              <p style="text-align: justify;">
                 Your agency will no longer be able to process EVR transactions beyond that point.  We recommend you contact your RMV Compliance Officer to renew your Permit. This message is provided to you as a courtesy.
              </p>
            </div>
            <div class="row" *ngIf="isEvrLitePermitExpired">
              <p style="text-align: justify;">
                Your RMV EVR Permit has expired.  You will not be able to process any EVR transactions in SinglePoint until you renew your permit.
                Please contact your RMV Compliance Officer for assistance.
              </p>
            </div>
          </div>
          <div class="row u-spacing--2">
            <div class="col-xs-12 u-align-right">
              <button
                (click)="evrLitePermitExpiration.closeModalbox()"
                class="o-btn">OK</button>
            </div>
          </div>
        </app-modalbox>
      </div>
    </div>
  </main>
</div>


<!-- NEW:: Hints And Warnings -->
<router-outlet name="warnings"></router-outlet>

<!-- Overlay -->
<app-overlay></app-overlay>
