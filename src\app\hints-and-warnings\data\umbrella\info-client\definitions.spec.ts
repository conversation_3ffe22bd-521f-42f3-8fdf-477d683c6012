import {
    generateCarriers, generateLabels, generateViewFieldIds, generateViewURIs, runConditions
} from 'testing/helpers/warning-definitions';

import { ClientAddress, ClientDetails } from 'app/app-model/client';
import { Helpers } from 'app/utils/helpers';

import {
    WARNINGS_DEFINITIONS_INFO_CLIENT_ADDRESSES_FOR_UMBRELLA, WARNINGS_DEFINITIONS_INFO_CLIENT_FOR_UMBRELLA
} from './definitions';

describe('Definitions: ClientInfo', () => {
    describe('when client info validator is used', () => {
        let definitions: any[];
        let clientInfo: ClientDetails;

        beforeEach(() => {
            definitions = WARNINGS_DEFINITIONS_INFO_CLIENT_FOR_UMBRELLA;
            clientInfo = Object.assign(new ClientDetails(), {
                meta: {
                    href: ''
                },
                quoteSessionId: '',
                resourceId: '',
                parentId: '',
                resourceName: '',
                addresses: {
                    href: ''
                },
                clientIdentifier: '',
                comment: '',
                contactMethods: {
                    href: ''
                },
                creationDate: '',
                customerNumber: '',
                customerSinceDate: '',
                dob: '',
                firstName: '',
                lastName: '',
                secondaryDOB: '',
                secondaryFirstName: '',
                secondaryLastName: '',
                secondarySSN: '',
                ssn: ''
            });
        });

        it('allows to check if required fields are provided', () => {
            const errors = runConditions(definitions, clientInfo, {});

            expect(errors).toEqual([]);
        });

        it('generates viewFieldIds without errors', () => {
            expect(() => {
                generateViewFieldIds(definitions, clientInfo);
            }).not.toThrow();
        });

        it('generates viewURIs without errors', () => {
            expect(() => {
                generateViewURIs(definitions, clientInfo);
            }).not.toThrow();
        });

        it('generates labels without errors', () => {
            expect(() => {
                generateLabels(definitions, clientInfo);
            }).not.toThrow();
        });

        it('generates carriers without errors', () => {
            expect(() => {
                generateCarriers(definitions, clientInfo);
            }).not.toThrow();
        });
    });

    describe('when client address validator is used', () => {
        let definitions: any[];
        let clientAddress: ClientAddress;
        let previousAddress: ClientAddress;
        let additionalData: any;

        beforeEach(() => {
            definitions = WARNINGS_DEFINITIONS_INFO_CLIENT_ADDRESSES_FOR_UMBRELLA;
            clientAddress = {
                meta: {
                    href: '',
                    rel: []
                },
                quoteSessionId: '',
                resourceId: '',
                parentId: '',
                resourceName: '',
                address1: '',
                address2: '',
                addressType: 'StreetAddress',
                city: '',
                residencyDate: '',
                state: '',
                zip: ''
            };

            previousAddress = Helpers.deepClone(clientAddress);
            previousAddress.addressType = 'PreviousAddress';

            additionalData = {
                observedData: [
                    clientAddress,
                    previousAddress
                ],
                quoteSelectedPlansIds: ['112', '119'],
                quote: {
                    effectiveDate: '2017-11-14'
                }
            };
        });

        it('allows to check if required fields are provided', () => {
            const errors = runConditions(definitions, clientAddress, additionalData);
            expect(errors).toEqual([]);
        });

        it('generates viewFieldIds without errors', () => {
            expect(() => {
                generateViewFieldIds(definitions, clientAddress);
            }).not.toThrow();
        });

        it('generates viewURIs without errors', () => {
            expect(() => {
                generateViewURIs(definitions, clientAddress);
            }).not.toThrow();
        });

        it('generates labels without errors', () => {
            expect(() => {
                generateLabels(definitions, clientAddress);
            }).not.toThrow();
        });

        it('generates carriers without errors', () => {
            expect(() => {
                generateCarriers(definitions, clientAddress);
            }).not.toThrow();
        });
    });
});
