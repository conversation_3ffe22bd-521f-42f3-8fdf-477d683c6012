import { parseISO } from 'date-fns/parseISO';

import {take, first} from 'rxjs/operators';
import { ModalboxComponent } from 'app/shared/components/modalbox/modalbox.component';
import {
  ClientAddress,
  ClientContactMethod,
  ClientDetails
} from 'app/app-model/client';
import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
  ViewChild
} from '@angular/core';
import { Observable } from 'rxjs';

import { ClientsService, CLIENT_TYPES } from 'app/dashboard/app-services/clients.service';
import {
  Dwelling,
  DwellingProtectionClass,
  DwellingProtectionClassOverrides,
  DwellingProtectionClassApiResponse,
  DwellingProtectionClassOverridesApiResponse
} from 'app/app-model/dwelling';
import { DwellingService } from 'app/dashboard/app-services/dwelling.service';
import { FilterOption } from 'app/app-model/filter-option';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import {
  Quote,
  QuoteHome,
  QuoteGuidelineOverridesApiResponse,
  QuoteGuidelineOverrides,
  QuotePlan
} from 'app/app-model/quote';
import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { RouteService } from 'app/shared/services/route.service';
import { Router } from '@angular/router';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { SubsService } from 'app/dashboard/app-services/subs.service';
import { PlansSelectorComponent } from 'app/shared/components/plans-selector/plans-selector.component';
import { LeaveQuoteComponent } from 'app/shared/components/leave-quote/leave-quote.component';
import { LocationData } from 'app/app-model/location';
import {
  CoverageApiResponseData,
  CoveragesData,
  PolicyItemDefaultDataAPIResponse,
  Coverage
} from 'app/app-model/coverage';
import { CoveragesService } from 'app/dashboard/app-services/coverages.service';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';

import { SharedStaticHomeDataFormTypeOptionsAlt, IFilterOptionsForHomePolicyAndFormType } from '../shared-static-data';
import {
  AgencyUserService,
  UserData,
  UserSubscriptionInformationAPIResopnseI,
  SubscriptionInformationProductI,
  SubscriptionInformationProductExpirationDataI
} from 'app/shared/services/agency-user.service';
import { LeaveQuoteService } from 'app/shared/services/leave-quote.service';
import { AgreedValueSupplementService } from 'app/shared/services/agreed-value-supplement.service';
import { addYears, differenceInDays, format, isBefore, startOfDay } from 'date-fns';


interface eventToEmitInterface {
  event: Event;
}

export interface UserSubscriptionDataI {
  code: string | string[];
  warningPopup: ModalboxComponent;
}

export interface StateDataI {
  id: string;
  text: string;
}

export interface IEventDataImportHomeQuote {
  effectiveDate: string;
  selectedPlans: QuotePlan[];
  state: FilterOption;
  policyType: string;
  formType: string;
}

@Component({
    selector: 'app-new-home-create-form',
    templateUrl: './new-home-create-form.component.html',
    styleUrls: ['./new-home-create-form.component.scss'],
    standalone: false
})
export class NewHomeCreateFormComponent implements OnInit, OnDestroy {

  @Input()
  public get effectiveDate(): string {return this.modelEffectiveDate; }
  public set effectiveDate(val: string) {
    if (val) {
      this.modelEffectiveDate = val;
    } else {
      this.modelEffectiveDate = null;
    }
  }

  @Input()
  public get quoteState(): string {return this._quoteState; }
  public set quoteState(val: string) {
    if (val) {
      this._quoteState = val;
      this.selectedState = val;
    } else {
      this.selectedState = null;
      this._quoteState = null;
    }
  }

  @Input()
  public get quotePolicyType(): string {return this._quotePolicyType; }
  public set quotePolicyType(val: string) {
    this._quotePolicyType = val || null;

    if (this.componentInitialized) {
      this.setSelectedPolicyAndFormTypeOption();
    }
  }

  @Input()
  public get quoteFormType(): string {return this._quoteFormType; }
  public set quoteFormType(val: string) {
    this._quoteFormType = val || null;

    if (this.componentInitialized) {
      this.setSelectedPolicyAndFormTypeOption();
    }
  }
  constructor(
    private router: Router,
    private agencyUserService: AgencyUserService,
    private subsService: SubsService,
    private quotesService: QuotesService,
    private storageService: StorageService,
    private clientsService: ClientsService,
    private overlayLoaderService: OverlayLoaderService,
    private specsService: SpecsService,
    private routeService: RouteService,
    private dwellingService: DwellingService,
    private coveragesService: CoveragesService,
    private storageGlobalService: StorageGlobalService,
    private apiCommonService: ApiCommonService,
    private leaveQuoteService: LeaveQuoteService,
    private agreedValueSupplementService: AgreedValueSupplementService
  ) {
    this.getSubscriptionInformation();
  }

  // ---------------------------------------------------------------------------
  public get showWarningInThePast(): boolean {
    return this.modelEffectiveDate && this.dateIsInThePast();
  }
  private componentInitialized = false;

  public states: Array<StateDataI> = [];
  public stateOptions: any = [];
  public selectedState: any;
  public subscriptionMessages: string[] = [];

  public policyAndFormTypeOptions: IFilterOptionsForHomePolicyAndFormType[] = [];
  public selectedPolicyAndFormTypeOption: IFilterOptionsForHomePolicyAndFormType;
  public selectedPolicyType: string;
  public selectedFormType: string;

  private agencyInfo;
  private subscription;
  private resetPlansSubscribtion;
  private userDataSubscription;
  public modelEffectiveDate: string = new Date().toISOString();
  public plansCount = 0;
  public selectedPlan: any;
  public plansOnQuotes: FilterOption[] = [];
  public plansOnQuotesFiltered: FilterOption[] = [];
  public invalidPlanQuoteField = false;
  public validUserInput = false;
  public updatingDefaultOptions = false;

  private createdDwelling: Dwelling;
  private createdClientDetails: ClientDetails;
  private homeDefaultOptionsValues: PolicyItemDefaultDataAPIResponse;

  private viewInited = false;

  private userSubscription: UserSubscriptionInformationAPIResopnseI = null;

  private _quoteState: string = null;
  private _quotePolicyType: string = null;
  private _quoteFormType: string = null;

  @Input() public useToImportQuote = false;
  policyType: any;

  @Output() public onCreateManuallyClick: EventEmitter<
    eventToEmitInterface
  > = new EventEmitter();
  @Output() public onCancelClick: EventEmitter<
    eventToEmitInterface
  > = new EventEmitter();
  @Output() public importQuoteClick: EventEmitter<IEventDataImportHomeQuote> = new EventEmitter();

  @ViewChild('refPlansSelector', {static: true}) refPlansSelector: PlansSelectorComponent;
  @ViewChild('refSubscriptionHomeInfoPopup')
  subscriptionHomeInfoPopup: ModalboxComponent;

  @ViewChild('leaveQuote') leaveQuote: LeaveQuoteComponent;

  private selectedPlansOnQuote = [];

  ngOnInit() {
    this.setPolicyAndFormTypeOptions();
    this.setSelectedPolicyAndFormTypeOption();

    this.userDataSubscription = this.agencyUserService.userData$.subscribe(
      agent => {
        this.agencyInfo = agent;
        this.initFormDefaultOptions();
      }
    );

    this.validateUserInput();

    setTimeout(() => {
      this.processPlans();
    });
    this.componentInitialized = true;
  }

  ngAfterViewInit() {
    this.viewInited = true;
  }

  ngOnDestroy() {
    this.subscription && this.subscription.unsubscribe();
    this.resetPlansSubscribtion && this.resetPlansSubscribtion.unsubscribe();
    this.userDataSubscription && this.userDataSubscription.unsubscribe();
  }
  canDeactivate(): Observable<boolean> | boolean {
    this.leaveQuote.detectConfirmation();
    return this.leaveQuote.detectConfirmationObservable.asObservable().pipe(first());
  }

  private setPolicyAndFormTypeOptions(): void {
    this.policyAndFormTypeOptions = SharedStaticHomeDataFormTypeOptionsAlt;
  }

  private setSelectedPolicyAndFormTypeOption(policyType?: string, formType?: string): void {
    let currentlySelected: IFilterOptionsForHomePolicyAndFormType = null;
    const tmpLookForPolicyType: string = policyType || this.quotePolicyType;
    const tmpLookForFormType: string = formType || this.quoteFormType;

    const simplifiedFormType: string[] = (tmpLookForFormType)
      ? this.quotesService.simplifyFormType(tmpLookForFormType)
      : null;

    if (this.selectedPolicyAndFormTypeOption) {
      currentlySelected = JSON.parse(JSON.stringify(this.selectedPolicyAndFormTypeOption));
    }

    const optionBaseOnPolicyTypeAndFormType: IFilterOptionsForHomePolicyAndFormType = this.policyAndFormTypeOptions
      .find((option: IFilterOptionsForHomePolicyAndFormType) => {
        const tmpSimlifiedFormTypeOpt: string[] = this.quotesService.simplifyFormType(option.id);
        return option.data.forPolicyType === tmpLookForPolicyType
          && JSON.stringify(tmpSimlifiedFormTypeOpt) === JSON.stringify(simplifiedFormType);
      });

    const optionBaseOnFormType: IFilterOptionsForHomePolicyAndFormType = this.policyAndFormTypeOptions
      .find((option: IFilterOptionsForHomePolicyAndFormType) => {
        const tmpSimlifiedFormTypeOpt: string[] = this.quotesService.simplifyFormType(option.id);
        return JSON.stringify(tmpSimlifiedFormTypeOpt) === JSON.stringify(simplifiedFormType);
      });

    const optionBaseOnPolicyType: IFilterOptionsForHomePolicyAndFormType = this.policyAndFormTypeOptions
      .find((option: IFilterOptionsForHomePolicyAndFormType) => {
        return option.data.forPolicyType === tmpLookForPolicyType;
      });

    if (optionBaseOnPolicyTypeAndFormType) {
      this.selectedPolicyAndFormTypeOption = optionBaseOnPolicyTypeAndFormType;
      // console.log('selectedPolicyAndFormTypeOption:: Found by policyType and formType');
    } else if (optionBaseOnFormType) {
      this.selectedPolicyAndFormTypeOption = optionBaseOnFormType;
      // console.log('selectedPolicyAndFormTypeOption:: Found by formType');
    } else if (optionBaseOnPolicyType) {
      this.selectedPolicyAndFormTypeOption = optionBaseOnPolicyType;
      // console.log('selectedPolicyAndFormTypeOption:: Found by policyType');
    } else {
      if (currentlySelected) {
        this.selectedPolicyAndFormTypeOption = currentlySelected;
        // console.log('selectedPolicyAndFormTypeOption:: Not found, selecting previously set value');
      } else {
        this.selectedPolicyAndFormTypeOption = (this.policyAndFormTypeOptions && this.policyAndFormTypeOptions.length)
          ? this.policyAndFormTypeOptions[0]
          : null;
        // console.log('selectedPolicyAndFormTypeOption:: Not found, selecting first available value');
      }
    }

    this.selectPolicyAndFormType(this.selectedPolicyAndFormTypeOption, this.refPlansSelector);
  }

  private setDefaultOptions(data: PolicyItemDefaultDataAPIResponse): void {
    let tmpPolicyType: string = null;
    let tmpFormType: string = null;

    if (data && data.items) {
      // Get Default PolicyType
      const policyTypeDefault = data.items.find(
        el => el.coverageCode === 'PolicyType'
      );

      if (
        policyTypeDefault &&
        policyTypeDefault.values &&
        policyTypeDefault.values[0] &&
        policyTypeDefault.values[0].value
      ) {
        tmpPolicyType = policyTypeDefault.values[0].value;
      } else {
        tmpPolicyType = null;
      }

      // Get Default FormType
      const formTypeDefault = data.items.find(
        el => el.coverageCode === 'FormType'
      );

      if (
        formTypeDefault &&
        formTypeDefault.values &&
        formTypeDefault.values[0] &&
        formTypeDefault.values[0].value
      ) {
        tmpFormType = formTypeDefault.values[0].value;
      } else {
        tmpFormType = null;
      }

      // Input data - policyType and formType needs to have higher priority over default values;
      const tmpPolicyTypeToSet: string = this.quotePolicyType || tmpPolicyType;
      const tmpFormTypeToSet: string = this.quoteFormType || tmpFormType;

      this.setSelectedPolicyAndFormTypeOption(tmpPolicyTypeToSet, tmpFormTypeToSet);
    }
  }


  private initFormDefaultOptions(): void {
    this.updatingDefaultOptions = true;
    Promise.all([
      this.getHomeDefaultOptions(this.agencyInfo.agencyId, false),
      this.getAndSetRatingStates(this.agencyInfo.agencyId)
    ])
      .then((data: [PolicyItemDefaultDataAPIResponse, any]) => {
        this.setDefaultOptions(data[0]);
        this.updatingDefaultOptions = false;
        this.validateUserInput();
      })
      .catch(err => {
        console.log('ERR::', err);
        this.updatingDefaultOptions = false;
      });
  }

  public validateUserInput(): boolean {
    const stateExists = this.selectedState ? true : false;
    const dateExists = this.modelEffectiveDate ? true : false;
    const plansExists = this.selectedPlan ? true : false;
    const formPolicyTypeExists = this.selectedPolicyAndFormTypeOption ? true : false;

    if (
      stateExists &&
      dateExists &&
      plansExists &&
      formPolicyTypeExists
    ) {
      this.validUserInput = true;
      return true;
    }
    this.validUserInput = false;
    return false;
  }

  private processPlans() {
    const plans = JSON.parse(
      JSON.stringify(this.storageGlobalService.takeSubs('plans'))
    );
    const plansFilteredByLob = this.filterByLob(plans, 'HOME');

    if (plansFilteredByLob.length > 0) {
      this.setPlansOnQuotes(plansFilteredByLob);
    } else {
      this.resetPlans();
    }
  }

  private filterByLob(plans, lob) {
    const filteredPlans = [];

    plans.forEach(plan => {
      if (plan.lob === lob) {
        filteredPlans.push(plan);
      }
    });

    return filteredPlans;
  }

  private resetPlans() {
    this.overlayLoaderService.showLoader('Loading Plans...');
    this.agencyUserService.userData$.subscribe(agent => {
      if (agent) {
        this.resetPlansSubscribtion = this.subsService
          .getRatingPlans(agent.agencyId)
          .subscribe(response => {
            this.overlayLoaderService.hideLoader();

            this.setPlansOnQuotes(this.filterByLob(response.items, 'HOME'));
            this.refPlansSelector.init();
          });
      }
    });
  }

  private setPlansOnQuotes(plans) {
    this.plansOnQuotes = [];
    this.plansOnQuotesFiltered = [];

    plans.forEach(item => {
      this.plansOnQuotes.push({
        text: item.name,
        id: item.ratingPlanId,
        data: item
      });
      if (item.state === this.selectedState) {
      this.plansOnQuotesFiltered.push({
        text: item.name,
        id: item.ratingPlanId,
        data: item
      });
    }
    });

    this.plansOnQuotes.sort((a, b) => {
      if (a['text'].toLowerCase() < b['text'].toLowerCase()) {
        return -1;
      } else if (a['text'].toLowerCase() > b['text'].toLowerCase()) {
        return 1;
      } else {
        return 0;
      }
    });

    this.plansOnQuotesFiltered.sort((a, b) => {
      if (a['text'].toLowerCase() < b['text'].toLowerCase()) {
        return -1;
      } else if (a['text'].toLowerCase() > b['text'].toLowerCase()) {
        return 1;
      } else {
        return 0;
      }
    });

    this.plansCount = this.plansOnQuotes.length;

    if (this.plansCount) {
      this.selectedPlan = [];
      this.selectedPlansOnQuote = [];
      this.plansOnQuotes.forEach((plan, index) => {
        this.selectedPlan.push(plan);
        this.selectedPlansOnQuote.push(plan.data);
      });
    }
  }
  public refreshValue($event) {
    this.selectedPlansOnQuote = [];
    for (const option of $event.selectedOption) {
      this.selectedPlansOnQuote.push(option.data);
    }
    this.selectedPlan = $event.selectedOption;
    this.invalidPlanQuoteField = false;

    this.validateUserInput();
  }

  public onDateChange($ev: { date: string | Date | null }): void {
  try {
    if ($ev && $ev.date) {
      // Handle both string and Date objects
      const dateValue = typeof $ev.date === 'string' ? $ev.date : $ev.date.toISOString();
      this.modelEffectiveDate = format(parseISO(dateValue), 'yyyy-MM-dd');
    } else {
      this.modelEffectiveDate = null;
    }
  } catch (error) {
    console.error('Error formatting date:', error);
    this.modelEffectiveDate = null;
  }
}

  public createQuoteManually() {
    if (this.validUserInput) {
      this.quotesService.setIsImportedQuote(false);
      this.quotesService.setIsMaipArcQuote(false);
      let quoteData;
      if (this.selectedPlansOnQuote.length) {
        quoteData = {
          lob: this.selectedPlansOnQuote[0].lob,
          state: this.selectedPlansOnQuote[0].state
        };
      }

      this.overlayLoaderService.showLoader();
      this.quotesService
        .createNewQuote({
          state: this.selectedState['id'],
          lob: 'HOME',
          effectiveDate: this.modelEffectiveDate,
         expirationDate: format(addYears(parseISO(this.modelEffectiveDate), 1), 'yyyy-MM-dd'),
          policyType: this.selectedPolicyType,
          formType: this.selectedFormType,
          policyPeriod: 12
        })
        .subscribe(
          data => {
            let srcQuoteSessionId = null;
            this.storageService
              .getStorageData('srcQuoteSessionId')
              .subscribe(sessionId => {
                if (sessionId) {
                  srcQuoteSessionId = sessionId;
                }
              });

            // if new quote created manually
            this.storageService.clearStorage();
            this.storageService.clearQuoteStoredData();

            this.storageService.setStorageData('isNewQuote', true);

            let quote = data;

            // Set Quote Info
            quote = this.setQuoteInfo(quote);

            this.storageService.setStorageData('selectedQuote', quote);
            this.subsService
              .getRatingStates(this.agencyInfo.agencyId, quoteData.lob).pipe(
              take(1))
              .subscribe();
            this.subsService
              .getRatingLobs(this.agencyInfo.agencyId).pipe(
              take(1))
              .subscribe();

            // Set the selected State in session storage
            this.storageService.setStorageData(
              'selectedState',
              this.selectedState.id
            );

            const quotePlanList = {
              items: this.selectedPlansOnQuote
            };

            // Update storage QuoteSelectedFormTypes
            const quoteSelectedFormTypes = this.quotesService.getQuoteSelectedFormTypes(
              quote
            );
            this.storageService.setStorageData(
              'selectedQuoteFormTypes',
              quoteSelectedFormTypes
            );

            if (!this.selectedPlansOnQuote.length) {
              quotePlanList.items = [this.selectedPlan.data];
            }

            this.quotesService
              .updateQuoteByUrl(quote.quotePlanList.href, quotePlanList)
              .subscribe(updatedQuote => {
                // Reset data
                this.createdDwelling = null;
                this.createdClientDetails = null;

                // Create Dwelling and Client for the Quote
                this.createDwelling(quote.dwellings.href)
                  .then(dwelling => {
                    this.createdDwelling = dwelling;
                    return dwelling;
                  })
                  .then(dwelling => {
                    return this.createDwellingLocations(dwelling);
                  })
                  .then(dwelling => {
                    return this.createClient(quote.resourceId);
                  })
                  .then((clientDetails: ClientDetails) => {
                    this.createdClientDetails = clientDetails;
                    return this.createClientData(clientDetails);
                  })
                  .then(() => this.getQuoteClients(quote.resourceId))
                  .then(() =>
                    this.createLossHistoryCollection(quote.resourceId, quote)
                  )
                  .then(() =>
                    this.getQuoteLossHistoryRequirements(quotePlanList.items)
                  )
                  .then(() => this.createScheduledProperty(quote))

                  .then(() =>
                    this.getQuoteGuidelineOverridesAndSetDefaultValues(
                      quote.resourceId
                    )
                  )
                  .then(() =>
                    this.getHomeDefaultOptions(this.agencyInfo.agencyId)
                  )
                  .then(() =>
                    this.getOrCreateProtectionClass(
                      quote.resourceId,
                      this.createdDwelling
                    )
                  )
                  .then((data: DwellingProtectionClass) => {
                    if (data.town.id) {
                      return this.setDefaultValuesForProtectionClass(
                        data,
                        this.homeDefaultOptionsValues
                      );
                    }
                  })
                  .then(() =>
                    this.getOrCreateProtectionClassOverrides(
                      quote.resourceId,
                      this.createdDwelling
                    )
                  )
                  .then(() => {
                    return this.getOrCreateHomeQuoteCoverages(
                      quote,
                      this.homeDefaultOptionsValues.items
                    );
                  })
                  .then(() => {
                    // Assign created client ID to Quote
                    quote.client.resourceId = this.createdClientDetails.resourceId;
                    this.storageService.setStorageData(
                      'dwelling',
                      this.createdDwelling
                    );
                    this.storageService.setStorageData('selectedQuote', quote);

                    // 05.30.2018 - SA: New quote created from dashboard will not have a srcQuoteSessionId, hence check if value is not null or empty.
                    if (srcQuoteSessionId) {
                      this.quotesService
                        .copyQuoteInfo(quote.quoteSessionId, {
                          quotesessionid: srcQuoteSessionId
                        }).pipe(
                        first())
                        .subscribe(res => {
                          if (res) {
                            this.routeService.showSaveConfirmation = false;
                            this.leaveQuote.showSaveConfirmation = false;
                            this.leaveQuote.forceConfirmation = false;
                            // this.router.navigateByUrl(
                            //   '/dashboard/quotes/' + quote.quoteSessionId
                            // );
                            this.router.navigate(
                              ['/dashboard/quotes/', quote.quoteSessionId],
                              { queryParams: {newQuote: true}}
                            );
                          } else {
                            this.router.navigateByUrl(
                              '/dashboard/home/<USER>/' +
                                quote.resourceId +
                                '/dwelling'
                            );
                          }
                        });
                    } else {
                      this.router.navigateByUrl(
                        '/dashboard/home/<USER>/' +
                          quote.resourceId +
                          '/dwelling'
                      );
                    }

                    this.leaveQuoteService.saveStorageQuoteDataSnapshot();
                    this.overlayLoaderService.hideLoader();
                  })
                  .catch(err => {
                    console.log(err);
                  });
              });
          },
          error => {
            console.log(error);
            this.overlayLoaderService.hideLoader();
          }
        );
    }
  }

  private createDwelling(uri: string): Promise<any> {
    const tmpDwelling = new Dwelling();
    tmpDwelling.constructionAge = null;
    tmpDwelling.constructionYear = null;
    tmpDwelling.livingSpaceArea = null;
    tmpDwelling.mortgageeCount = null;
    tmpDwelling.poolOnPremisesInd = null;

    return this.dwellingService
      .createDwellingByUri(uri, tmpDwelling)
      .toPromise()
      .then((dwelling: Dwelling) => dwelling)
      .catch(err => console.log(err));
  }

  private createDwellingLocations(dwelling: Dwelling): Promise<Dwelling> {
    return new Promise((resolve, reject) => {
      this.dwellingService
        .getDwellingLocation(dwelling.dwellingLocation.href)
        .toPromise()
        .then(res => {
          // If there is no location, create new one
          if (!res.items || !res.items.length) {
            return this.dwellingService
              .createDwellingLocation(dwelling.dwellingLocation.href)
              .toPromise()
              .then(resNewDwellingLocation => {
                return resNewDwellingLocation;
              });
          } else {
            return res.items[0];
          }
        })
        .then((dwellingLocation: LocationData) => {
          let requireUpdate = false;

          // Update Location with default values if necessary
          if (!dwellingLocation.state) {
            if (this.selectedState && this.selectedState.id) {
              dwellingLocation.state = this.selectedState.id;
            } else {
              dwellingLocation.state = 'MA';
            }
            requireUpdate = true;
          }

          if (requireUpdate) {
            return this.dwellingService
              .updateDwellingLocation(dwellingLocation)
              .toPromise()
              .then((data: LocationData) => data);
          } else {
            return dwellingLocation;
          }
        })
        .then((dwellLocation: LocationData) => {
          resolve(dwelling);
        })
        .catch(err => reject(err));
    });
  }

  private createClient(quoteID: string): Promise<any> {
    return this.clientsService
      .createQuoteClient(quoteID)
      .toPromise()
      .then((clientDetails: ClientDetails) => clientDetails)
      .catch(err => console.log(err));
  }

  private createClientData(client: ClientDetails): Promise<any> {
    const promiseAddresses = this.clientsService
      .getClientAddressesAndCreateNotExisting(client)
      .then((res: ClientAddress[]) => {
        this.storageService.setStorageData(
          'clientAddresses',
          JSON.parse(JSON.stringify(res))
        );
      })
      .catch(err => console.log(err));

    const promiseContactMethods = this.clientsService
      .getClientContactMethodsAndCreateNotExisting(client)
      .then((res: ClientContactMethod[]) => {
        this.storageService.setStorageData(
          'clientContactMethods',
          JSON.parse(JSON.stringify(res))
        );
      })
      .catch(err => console.log(err));

    return Promise.all([promiseAddresses, promiseContactMethods]);
  }

  private createLossHistoryCollection(quoteId: string, quote: Quote) {
    return this.dwellingService
      .createLossHistory(quoteId)
      .toPromise()
      .then(response => {
        // NEW F
        response = this.dwellingService.helpUpdateLossHistoryForHintsAndWarnings(
          response,
          quote
        );
        this.storageService.setStorageData('dwellingLossHistory', response);
      })
      .catch(err => console.log(err));
  }

  private createScheduledProperty(quote: Quote): Promise<void> {
    return new Promise((resolve, reject) => {
      const uri = '/quotes/' + quote.resourceId + '/scheduledProperty';
      this.dwellingService
        .createScheduledProperty(uri).pipe(
        first())
        .subscribe(response => {
          const resourceId = response.resourceId;
          this.getHomeScheduledProperty(quote.resourceId, resourceId)
            .then(() => resolve())
            .catch(() => reject());
        });
    });
  }

  private getHomeScheduledProperty(quoteId, resourceId): Promise<void> {
    return new Promise((resolve, reject) => {
      this.dwellingService
        .getScheduledProperty(quoteId, resourceId).pipe(
        take(1))
        .subscribe(
          response => {
            this.storageService.setStorageData(
              'dwellingScheduledPropertyAPIResponse',
              response
            );

            if (response.items.length) {
                            this.storageService.setDwellingScheduledProperty(response.items);
                          }

            resolve();
          },
          err => reject(err)
        );
    });
  }

  private getQuoteLossHistoryRequirements(
    selectedPlans: QuotePlan[]
  ): Promise<any> {
    const quotePlansIds: string[] = selectedPlans.map(
      plan => plan.ratingPlanId
    );
    const quotePlansIdsString: string = quotePlansIds.join(',');

    return this.specsService
      .getLossHistoryRequirements(quotePlansIdsString)
      .toPromise()
      .then(res => {
        this.storageService.setStorageData(
          'dwellingLossHistoryRequirementsNew',
          res.items
        );
      });
  }

  private getQuoteClients(quoteId: string): Promise<any> {
    return this.clientsService
      .getClientsList(quoteId).pipe(
      take(1))
      .toPromise()
      .then(res => {
        if (res.items[0] && !res.items[0].type) {
          res.items[0].type = CLIENT_TYPES.personal;
        }
        this.storageService.setStorageData('selectedClient', res.items[0]);
        this.storageService.setStorageData('clients', res.items);
      })
      .catch(err => console.log(err => console.log('CLIENTS ERR:', err)));
  }

  private updateClientDetails(
    clientDetailsData: ClientDetails
  ): Promise<ClientDetails> {
    return this.apiCommonService
      .putByUri(clientDetailsData.meta.href, clientDetailsData)
      .toPromise()
      .then(res => res)
      .catch(err => console.log(err));
  }

  private setQuoteInfo(quote: Quote): Quote {
    let userData: UserData;

    this.agencyUserService.userData$.pipe(
      first())
      .subscribe(data => (userData = data));

    // Quote agency contact
    if (!quote.agent && userData) {
      quote.agent = userData.user.userId;
    }

    return quote;
  }

  public handleCreateManuallyClick($ev): void {
    if (this.selectedPlansOnQuote && this.selectedPlansOnQuote.length) {
      const hasListAgreedValueSupplement = this.selectedPlan.some(plan =>
        this.agreedValueSupplementService.agreedValueSupplementPlanIds.includes(plan.id)
      );
      const hasListNoAgreedValueSupplement = this.selectedPlan.some(plan =>
        this.agreedValueSupplementService.noAgreedValueSupplementPlanIds.includes(plan.id)
      );

      this.agreedValueSupplementService.updatePlanSelection(hasListAgreedValueSupplement, hasListNoAgreedValueSupplement);
    }

    this.leaveQuote.forceConfirmation = true;
    this.leaveQuote.confirm().then(answer => {
      if (answer) {
        this.redirectToCreateQuoteView().then(() => {
          this.createQuoteManually();
        });

        if (this.invalidPlanQuoteField) {
          return;
        }

        this.onCreateManuallyClick.emit({
          event: $ev
        });
      }
    });
  }

  public handleCancelClick($ev): void {
    this.onCancelClick.emit({
      event: $ev
    });
  }

  private redirectToCreateQuoteView(): Promise<any> {
    return this.router.navigateByUrl('/dashboard/quotes/new');
  }

  private getQuoteGuidelineOverridesAndSetDefaultValues(
    quoteId: string
  ): Promise<QuoteGuidelineOverrides> {
    return new Promise((resolve, reject) => {
      this.quotesService
        .getGuidelineOverrides(quoteId).pipe(
        take(1))
        .subscribe((res: QuoteGuidelineOverridesApiResponse) => {
          const overrides: QuoteGuidelineOverrides[] = [];
          // Set Default Values
          if (res.items && res.items[0]) {
            res.items[0].applyNewHomeCredit = true;
            res.items[0].applyPreferredOrSuperiorHomeCredit = true;

            this.quotesService
              .updateGuidelineOverrides(quoteId, res.items[0]).pipe(
              take(1))
              .subscribe((response: QuoteGuidelineOverrides) => {
                resolve(response);
              });
          } else {
            // Create Guideline Overrides
            const tmpOverrides = new QuoteGuidelineOverrides();
            tmpOverrides.applyNewHomeCredit = true;
            tmpOverrides.applyPreferredOrSuperiorHomeCredit = true;
            const dataToSend = [tmpOverrides];

            this.quotesService
              .createGuidelineOverrides(quoteId, dataToSend).pipe(
              take(1))
              .subscribe(response => {
                // Needs to be updated with default values
                response.applyNewHomeCredit = true;
                response.applyPreferredOrSuperiorHomeCredit = true;

                this.quotesService
                  .updateGuidelineOverrides(quoteId, response).pipe(
                  take(1))
                  .subscribe((updatedResponse: QuoteGuidelineOverrides) => {
                    resolve(updatedResponse);
                  });
              });
          }
        });
    });
  }

  private getHomeDefaultOptions(
    agencyId,
    saveToStorage: boolean = true
  ): Promise<PolicyItemDefaultDataAPIResponse | null> {
    return new Promise((resolve, reject) => {
      this.subsService
        .getDefaultHomeCarrierOptions(agencyId, this.selectedFormType).pipe(
        take(1))
        .subscribe(
          (res: PolicyItemDefaultDataAPIResponse) => {
            if (saveToStorage) {
              this.storageService.setStorageData('homeDefaultOptions', res);
            }
            this.homeDefaultOptionsValues = res;
            resolve(res);
          },
          err => {
            console.log('ERR: ', err);
            resolve(null);
          }
        );
    });
  }

  private getOrCreateProtectionClass(
    quoteId: string,
    createdDwelling: Dwelling
  ): Promise<DwellingProtectionClass> {
    return new Promise((resolve, reject) => {
      this.dwellingService
        .getDwellingProtectionClass(createdDwelling.protectionClass.href).pipe(
        take(1))
        .subscribe(
          (res: DwellingProtectionClassApiResponse) => {
            if (res && res.items && res.items[0]) {
              this.storageService.setStorageData(
                'dwellingProtectionClass',
                res.items[0]
              );
              resolve(res.items[0]);
            } else {
              this.dwellingService
                .createDwellingProtectionClass(res.meta.href).pipe(
                take(1))
                .subscribe(
                  (response: DwellingProtectionClass) => {

                    let defaultValue = this.homeDefaultOptionsValues.items.find(cov => cov.coverageCode === 'DistancetoFireStation');
                    if (defaultValue !== undefined && defaultValue.values[0] !== null) {
                         response.distanceToFireStation = defaultValue.values[0].value;
                    }
                    defaultValue = this.homeDefaultOptionsValues.items.find(cov => cov.coverageCode === 'DistancetoBodyWater');
                    if (defaultValue !== undefined &&  defaultValue.values[0] !== null) {
                      response.distanceToBodyWater = defaultValue.values[0].value;
                    }
                    defaultValue = this.homeDefaultOptionsValues.items.find(cov => cov.coverageCode === 'DistancetoFireHydrant');
                    if (defaultValue !== undefined && defaultValue.values[0] !== null) {
                      response.distanceToFireHydrant = defaultValue.values[0].value;
                    }

                    this.storageService.setStorageData(
                      'dwellingProtectionClass',
                      response
                    );
                    resolve(response);
                  },
                  err => reject(err)
                );
            }
          },
          err => reject(err)
        );
    });
  }

  private setDefaultValuesForProtectionClass(
    protectionClass: DwellingProtectionClass,
    homeDefaultValues: PolicyItemDefaultDataAPIResponse
  ): Promise<DwellingProtectionClass> {
    return new Promise((resolve, reject) => {
      const homeDefaultOptions = homeDefaultValues.items;

      homeDefaultOptions.forEach((item: Coverage) => {
        switch (item.coverageCode) {
          case 'DistancetoFireStation':
            if (item.values && item.values[0]) {
              protectionClass['distancetoFireStation'] = item.values[0].value;
              // protectionClass['distancetoFireStation'] = '';
            }
            break;
          case 'DistancetoFireHydrant':
            if (item.values && item.values[0]) {
              protectionClass['distanceToFireHydrant'] = item.values[0].value;
              // protectionClass['distanceToFireHydrant'] = '';
            }
            break;
          case 'DistancetoBodyWater':
            if (item.values && item.values[0]) {
              protectionClass['distanceToBodyWater'] = item.values[0].value;
              // protectionClass['distanceToBodyWater'] = '';
            }
            break;
        }
      });

      this.dwellingService
        .updateDwellingProtectionClass(
          protectionClass.meta.href,
          protectionClass
        ).pipe(
        take(1))
        .subscribe(
          (res: DwellingProtectionClass) => {
            this.storageService.setStorageData('dwellingProtectionClass', res);
            resolve(res);
          },
          err => reject(err)
        );
    });
  }

  private getOrCreateProtectionClassOverrides(
    quoteId: string,
    createdDwelling: Dwelling
  ): Promise<DwellingProtectionClassOverrides> {
    return new Promise((resolve, reject) => {
      this.dwellingService
        .getDwellingProtectionClassOverrides(createdDwelling.protectionClassOverrides.href).pipe(
        take(1))
        .subscribe(
          (res: DwellingProtectionClassOverridesApiResponse) => {
              this.dwellingService
                .createDwellingProtectionClassOverrides(res.meta.href).pipe(
                take(1))
                .subscribe(
                  (response: DwellingProtectionClassOverrides) => {
                    this.storageService.setStorageData(
                      'dwellingProtectionClassOverrides',
                      response
                    );
                    resolve(response);
                  },
                  err => reject(err)
                );
          },
          err => reject(err)
        );
    });
  }

  // private getOrCreateHomeQuoteCoverages(quote: QuoteHome, homeDefaultData: Coverage[]| Policy[]): Promise<CoverageApiResponseData> {
  private getOrCreateHomeQuoteCoverages(
    quote: QuoteHome,
    homeDefaultData: Coverage[]
  ): Promise<CoverageApiResponseData> {
    return new Promise((resolve, reject) => {
      this.coveragesService
        .getHomeQuoteCoverages$(quote.coveragesStandard.href).pipe(
        take(1))
        .subscribe(
          (res: CoverageApiResponseData) => {
            // storage is updated directly in getHomeQuoteCoverages$ method
            if (
              res.items &&
              res.items[0] &&
              res.items[0].meta &&
              res.items[0].meta.href
            ) {
              resolve(res);
            } else {
              // Create Home Quote Coverages
              this.coveragesService
                .createHomeCoverageByUriWithDefaultValues(
                  quote.coveragesStandard.href,
                  homeDefaultData
                ).pipe(
                take(1))
                .subscribe(
                  (res: CoveragesData) => {
                    // Save to storage
                    const tmpRes = new CoverageApiResponseData();
                    tmpRes.items = [res];
                    this.storageService.setStorageData(
                      'homeQuoteCoverages',
                      tmpRes
                    );
                    console.log('CREATED COVERAGES::: ', tmpRes);
                    resolve(tmpRes);
                  },
                  err => reject(err)
                );
            }
          },
          err => reject(err)
        );
    });
  }
  public actionSelectPolicyAndFormType($event, refPlansSelector): void {
    this.selectPolicyAndFormType($event.selectedOption, refPlansSelector);
  }

  public selectPolicyAndFormType(option: IFilterOptionsForHomePolicyAndFormType, refPlansSelector): void {

    if (!option) {
      console.warn('No data defined to be able to select');
      return;
    }

    this.selectedPolicyAndFormTypeOption = option;
    this.selectedPolicyType = option.data.forPolicyType;
    this.selectedFormType = option.id;

    this.validateUserInput();
    this.filterPlans(option.text);
    if (this.viewInited) {
      refPlansSelector.init();
    }
    this.refreshValue({ selectedOption: this.plansOnQuotesFiltered });
  }


  private filterPlans(formType: string) {
    if (this.plansOnQuotes && this.plansOnQuotes.length) {
      const formTypes = this.quotesService.simplifyFormType(formType);
      this.plansOnQuotesFiltered = [];
      this.plansOnQuotes.forEach(plan => {
        this.plansOnQuotesFiltered.push(JSON.parse(JSON.stringify(plan)));
      });
      const filteredPlans = [];
      this.selectedPlan = [];
      this.plansOnQuotesFiltered.forEach(plan => {
        if (
          plan &&
          plan.data &&
          plan.data.allowedFormTypesCode &&
          plan.data.state === this.selectedState.id
        ) {
          let allowType = false;
          formTypes.forEach(fType => {
            if (plan.data.allowedFormTypesCode.indexOf(fType) > -1) {
              allowType = true;
            }
          });
          if (allowType) {
            filteredPlans.push(plan);
            this.selectedPlan.push(plan);
          }
        }
      });
      this.plansOnQuotesFiltered = filteredPlans;
    }
  }

  private getAndSetRatingStates(agencyId): Promise<any> {
    return new Promise<void>((resolve, reject) => {
      const lob = '';

      this.subsService.getRatingStates(this.agencyInfo.agencyId, lob).subscribe(
        data => {
          if (data && data.items && data.items.length) {
            data.items.forEach(item => {
              this.states.push({ text: item.name, id: item.code });
            });
            /**Logic to get default state
             * Get the default state from RatingSubscription[0].defaultState
             */
            this.setDefaultState('HOME');
          }
          resolve();
        },
        err => reject(err)
      );
    });
  }

  private setDefaultState(lob: string) {
    let defaultState = null;
    this.getSubscriptionInformation().then(() => {
      if (
        this.userSubscription &&
        this.userSubscription.ratingSubscriptions &&
        this.userSubscription.ratingSubscriptions.length
      ) {
        const selectedRatingSubscriptionByLob = this.userSubscription.ratingSubscriptions.find(
          singleRatingSubscriptionData =>
            singleRatingSubscriptionData.lob.toString() === lob
        );
        if (selectedRatingSubscriptionByLob) {
          defaultState = selectedRatingSubscriptionByLob.defaultState;
        }

        // Added - set default state base on input
        if (this.quoteState) {
          defaultState = this.quoteState;
        }

        if (defaultState) {
          const selectedItem = this.states.find(
            state => state.id === defaultState
          );
          if (selectedItem) {
            this.selectedState = selectedItem;
          } else {
            this.selectedState = this.states[0];
          }
        } else {
          this.selectedState = this.states[0];
        }
      }
    });
  }

  public selectState(
    $event,
    input,
    warningPopup: ModalboxComponent,
    refPlansSelector
  ) {
    let blockCreationOfNewQuote = false;
    /* Logic to handle state selection
     * Part 1
     * ngOnInit: GetAgencySubscription;
     * Check subscription by code: MAHome/RIHome (selectedState+Home)
     * Use existing logic in userSubscription directive
     * Part 2
     * If subscription is good then get plans by selected state
     * And Update 'SelectedPlan'
     */
    const currentStateSelection = this.selectedState;

    const userDataInput: UserSubscriptionDataI = {
      code: $event.id + 'Home',
      warningPopup: warningPopup
    };

    this.getSubscriptionInformation().then(() => {
      if (
        this.userSubscription &&
        this.userSubscription.items &&
        this.userSubscription.items.length
      ) {
        blockCreationOfNewQuote = this.checkSubscriptionsExpiration(
          this.userSubscription,
          userDataInput
        );

        if (!blockCreationOfNewQuote) {
          // Now update Selected plans as well based on selected state
          this.selectedState = $event;
          input.activeOption = this.selectedState;

          this.validateUserInput();

          this.filterPlansByState($event.id);

          if (this.viewInited) {
            refPlansSelector.init();
          }
          this.refreshValue({ selectedOption: this.plansOnQuotesFiltered });
        } else {
          input.activeOption = currentStateSelection;
        }
      }
    });
  }

  private filterPlansByState(state: string) {
    if (this.plansOnQuotes && this.plansOnQuotes.length) {
      this.plansOnQuotesFiltered = [];
      this.plansOnQuotes.forEach(plan => {
        this.plansOnQuotesFiltered.push(JSON.parse(JSON.stringify(plan)));
      });
      const filteredPlans = [];
      this.selectedPlan = [];
      this.plansOnQuotesFiltered.forEach(plan => {
        if (plan && plan.data) {
          if (plan.data.state === state) {
            filteredPlans.push(plan);
            this.selectedPlan.push(plan);
          }
        }
      });
      this.plansOnQuotesFiltered = filteredPlans;
    }
  }

  private getSubscriptionInformation(): Promise<
    UserSubscriptionInformationAPIResopnseI | boolean
  > {
    if (!this.userSubscription) {
      this.overlayLoaderService.showLoader();

      return this.getSubscriptionInformationWithFallback().then(
        (res: UserSubscriptionInformationAPIResopnseI) => {
          this.userSubscription = JSON.parse(JSON.stringify(res));
          this.overlayLoaderService.hideLoader();
          return Promise.resolve(this.userSubscription);
        },
        err => {
          console.log('ERRR', err);
          this.overlayLoaderService.hideLoader();
          return Promise.resolve(false);
        }
      );
    } else {
      return Promise.resolve(this.userSubscription);
    }
  }

  private getSubscriptionInformationWithFallback(): Promise<
    UserSubscriptionInformationAPIResopnseI
  > {
    return new Promise((resolve, reject) => {
      this.agencyUserService.getSubscriptionInformation$.pipe(
        take(1))
        .subscribe((res: UserSubscriptionInformationAPIResopnseI) => {
          if (res && res.items && res.items.length) {
            resolve(res);
          } else {
            this.agencyUserService
              .getUserSubscriptionInformationFromAPI().pipe(
              take(1))
              .subscribe((data: UserSubscriptionInformationAPIResopnseI) => {
                resolve(data);
              });
          }
        });
    });
  }

  private checkSubscriptionsExpiration(
    userSubscriptions: UserSubscriptionInformationAPIResopnseI,
    subscriptionInputData: UserSubscriptionDataI
  ): boolean {
    let subscriptionToCheck: string[] = [];
    const subscriptionExpirationMessages: string[] = [];
    let showMessage = false;
    let blockCreationOfNewQuote = false;

    if (subscriptionInputData && subscriptionInputData.code) {
      subscriptionToCheck = Array.isArray(subscriptionInputData.code)
        ? subscriptionInputData.code
        : [subscriptionInputData.code];
    }

    subscriptionToCheck.forEach(subscriptionName => {
      if (
        userSubscriptions &&
        userSubscriptions.items &&
        userSubscriptions.items.length
      ) {
        const subscriptionProductFound = userSubscriptions.items.find(
          singleSubscriptionData =>
            singleSubscriptionData.code === subscriptionName
        );

        if (subscriptionProductFound) {
          const tmpResult = this.checkSubscriptionProductIsExpired(
            subscriptionProductFound
          );

          // Set showMessage if one or more products should display warning
          showMessage = !showMessage ? tmpResult.showWarning : showMessage;

          // Set Messages
          if (
            tmpResult.showWarning &&
            tmpResult.product &&
            tmpResult.product.message
          ) {
            subscriptionExpirationMessages.push(tmpResult.product.message);
          }

          // Set if the new Quote can be created - block if even one of the products has expired
          blockCreationOfNewQuote = !blockCreationOfNewQuote
            ? tmpResult.isExpired
            : blockCreationOfNewQuote;
        }
      } else {
        console.log('Subscriptions not found, looking for: ', subscriptionName);
      }
    });

    if (showMessage && blockCreationOfNewQuote) {
      // console.log('SHOW AND BLOCK');
      this.subscriptionMessages = subscriptionExpirationMessages;
      // this.showPopup.emit({proceed: false, messages: subscriptionExpirationMessages});
      subscriptionInputData.warningPopup.openModalbox();
    } else if (showMessage && !blockCreationOfNewQuote) {
      // console.log('SHOW ONLY');
      // this.showPopup.emit({proceed: true, messages: subscriptionExpirationMessages});
      this.subscriptionMessages = subscriptionExpirationMessages;
      subscriptionInputData.warningPopup.openModalbox();
    } else {
      // Everything looks groovy go to part 2 and select the state specific plans
    }

    return blockCreationOfNewQuote;
  }

  private checkSubscriptionProductIsExpired(
    product: SubscriptionInformationProductI
  ): SubscriptionInformationProductExpirationDataI {
    const now = new Date();
    let diff: number = null;

    const result: SubscriptionInformationProductExpirationDataI = {
      product: JSON.parse(JSON.stringify(product)),
      ratingProduct: null,
      showWarning: false,
      isExpired: false
    };

    if (!product.expirationDate || product.expirationDate.length === 0) {
      result.showWarning = true;
      result.isExpired = true;
    } else {
     diff = differenceInDays(new Date(product.expirationDate), now);

      if (diff >= 15) {
        result.showWarning = false;
        result.isExpired = false;
      } else if (diff < 15 && diff >= -14) {
        result.showWarning = true;
        result.isExpired = false;
      } else {
        result.showWarning = true;
        result.isExpired = true;
      }
    }

    return result;
  }
  //#endregion

  // Import Quote
  // ---------------------------------------------------------------------------
  public handleImportQuote(): void {
    // this.leaveQuote.forceConfirmation = true;
    if (this.invalidPlanQuoteField) {
      return;
    }

    this.importQuoteClick.next({
      effectiveDate: this.modelEffectiveDate,
      selectedPlans: this.selectedPlansOnQuote,
      state: this.selectedState,
      policyType: this.selectedPolicyType,
      formType: this.selectedFormType,
    });
  }

  private dateIsInThePast(): boolean {
    return this.modelEffectiveDate && isBefore(
  parseISO(this.modelEffectiveDate),
  startOfDay(new Date()));
  }
}
