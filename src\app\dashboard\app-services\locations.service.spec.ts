import { expectLastConnectionUrl, expectLastConnectionPayload, DataCustomMatchers } from 'testing/helpers/all';
import { inject, TestBed } from '@angular/core/testing';
import { MockBackend } from 'testing/setups/mock-backend';

import { data as LOCATIONS } from 'testing/data/quotes/locations/auto';
import { setupMockBackend } from 'testing/setups/mock-backend';
import { LocationsService } from './locations.service';

describe('Service: Locations', () => {
  let service: LocationsService;
  let mockBackend: MockBackend;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        LocationsService
      ]
    });

    jasmine.addMatchers(DataCustomMatchers);
  });

  beforeEach(inject([LocationsService], (_service: LocationsService) => {
    service = _service;
  }));

  it('can instantiate service when inject service', () => {
    expect(service).toBeTruthy();
  });

  describe('when working with locations list', () => {
    beforeEach(() => {
      mockBackend = setupMockBackend(LOCATIONS);
    });

    it('allows to retrieve locations list by URI', () => {
      service.getLocationsListByUri('/quotes/quote-id/locations').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/locations');
    });

    it('allows to retrieve locations list by ID', () => {
      service.getLocationsList('quote-id').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/locations');
    });
  });

  describe('when working with single location', () => {
    beforeEach(() => {
      mockBackend = setupMockBackend(LOCATIONS.items[0]);
    });

    it('allows to retrieve location by URI', () => {
      service.getLocationByUri('/quotes/quote-id/locations/location-id').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/locations/location-id');
    });

    it('allows to retrieve location by ID', () => {
      service.getLocation('quote-id', 'location-id').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/locations/location-id');
    });

    it('allows to create location by URI', () => {
      service.createLocationByUri('/quotes/quote-id/locations', LOCATIONS.items[0]).subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/locations');
      expectLastConnectionPayload(mockBackend).toEqual(LOCATIONS.items[0]);
    });

    it('allows to create location by ID', () => {
      service.createLocation('quote-id', LOCATIONS.items[0]).subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/locations');
      expectLastConnectionPayload(mockBackend).toEqual(LOCATIONS.items[0]);
    });

    it('allows to update location by URI', () => {
      service.updateLocationByUri('/quotes/quote-id/locations/location-id', LOCATIONS.items[0]).subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/locations/location-id');
      expectLastConnectionPayload(mockBackend).toEqual(LOCATIONS.items[0]);
    });

    it('allows to update location by ID', () => {
      service.updateLocation('quote-id', 'location-id', LOCATIONS.items[0]).subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/locations/location-id');
      expectLastConnectionPayload(mockBackend).toEqual(LOCATIONS.items[0]);
    });
  });

  describe('when deleting location', () => {
    beforeEach(() => {
      mockBackend = setupMockBackend({});
    });

    it('allows to delete location by URI', () => {
      service.deleteLocationByUri('/quotes/quote-id/locations/location-id').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/locations/location-id');
    });

    it('allows to delete location by ID', () => {
      service.deleteLocation('quote-id', 'location-id').subscribe();

      expectLastConnectionUrl(mockBackend).toEndWith('/quotes/quote-id/locations/location-id');
    });
  });
});
