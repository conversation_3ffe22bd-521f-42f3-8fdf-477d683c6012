<section class="section">
  <div class="u-flex u-flex--spread">
    <div class="">
      <h1 class="o-heading">Import Summary</h1>
    </div>
    <div class=""></div>
  </div>

  <div class="row u-spacing--1-5">
    <div class="col-xs-12">
      <div class="box box--warning">
        <p *ngFor="let message of rmvData?.messages">{{ message.message }}</p>
      </div>
    </div>
  </div>
</section>

<section class="section">
  <div class="u-flex u-flex--spread">
    <div class="">
      <h1 class="o-heading">Drivers Reports</h1>
    </div>
    <div class=""></div>
  </div>

  <div class="row u-spacing--1-5">
    <div class="col-xs-12">
      <div class="box box--silver">
        <ul class="manage-list">
          <li class="manage-list__item" *ngFor="let driver of rmvData.drivers">
            <div class="manage-list__data">
              <div class="manage-list__data-item u-width-180px">
                <h2 class="u-t-size--1-3rem u-color-pelorous u-t-weight-bold">
                  {{ driver.firstName }} {{ driver.lastName }}
                </h2>
              </div>
              <div *ngIf="
                  driver?.messages !== undefined && driver?.messages?.length > 0
                " class="manage-list__data-item">
                <p *ngFor="let driverMessage of driver.messages">
                  <span *ngIf="doNotDisplayGeneralMessage(driverMessage?.message)">
                    {{ driverMessage.message }}
                  </span>
                </p>
              </div>
              <div *ngIf="driver?.messages === undefined" class="manage-list__data-item">
                <p>No messages</p>
              </div>
            </div>
            <div class="manage-list__actions">
              <a [routerLink]="[]" [queryParams]="{
                  overlay: 'rmvreports',
                  type: 'driver',
                  id: generateRmvReportDriverId(driver)
                }" class="o-action o-action--icon-less manage-list__btn">View</a>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</section>

<section class="section">
  <div class="u-flex u-flex--spread">
    <div class="">
      <h1 class="o-heading">Vehicle Reports</h1>
    </div>
    <div class="">
    </div>
  </div>

  <div class="row u-spacing--1-5">
    <div class="col-xs-12">
      <div class="box box--silver">
        <ul class="manage-list">
          <li class="manage-list__item" *ngFor="let vehicle of rmvData.vehicles">
            <div class="manage-list__data">

              <div class="manage-list__data-item">
                  <input type="checkbox"
                    [name]="'checkbox_' + vehicle.resourceId"
                    [(ngModel)]='vehicle.isIncluded'
                    [checked]='vehicle.isIncluded'
                    class="o-btn--checkbox u-show-inline" >
              </div>
              <div class="manage-list__data-item u-width-180px">
                <h2 class="u-t-size--1-3rem u-color-pelorous u-t-weight-bold">
                  {{ vehicle.year }} {{ vehicle.make }} {{ vehicle.model }}
                </h2>
              </div>
              <div class="manage-list__data-item">
                <p *ngFor="let message of vehicle.messages; let index = index"
                  [ngClass]="{ 'u-spacing--1': index > 0 }">
                  {{ message.message }}
                </p>
              </div>
            </div>
            <div class="manage-list__actions">
              <button (click)="showRMVvehicleReport(vehicle)" class="o-action o-action--icon-less manage-list__btn">
                View
              </button>
            </div>
          </li>
        </ul>

      </div>
    </div>
  </div>
</section>

<section class="section">
  <div class="row">
    <div class="col-xs-12">
      <a *ngIf="rmvData?.meta?.href" (click)="getDFLInfo()" class="o-btn u-spacing--right-2">Go to Quote</a>
      <a [routerLink]="['/dashboard/auto/new-rmv-quote']" class="o-btn o-btn--idle">Cancel</a>
    </div>
  </div>
  <!-- [routerLink]="['/dashboard/auto/rmv-load' + rmvData?.meta?.href]" -->
  <app-modalbox #modalRmvResultsDFL [launcher]="'#rmv-lookup-driver-modal'" [css]="'u-width-500px'">
    <ng-template [ngIf]="modalRmvResultsDFL.isOpen">
      <h2 class="o-heading o-heading--red">REVIEW DATE FIRST LICENSED</h2>

      <div class="box box--silver u-spacing--1-5">
        <p class="u-spacing--bottom-1-5 ">
          Please verify or select Date First Licensed to use for each driver.
        </p>
        <table class="form-table">
          <tr class="form-table__row" *ngFor="let dfldriver of dateFirstLicenseData">
            <td class="form-table__cell">
              <h2 class="u-t-size--1-3rem">
                {{ dfldriver.firstname }} {{ dfldriver.lastname }}
              </h2>
            </td>
            <td class="form-table__cell u-width-250px">
              <sm-autocomplete #refDriverDateFirstLic [options]="dfldriver.options" [name]="'driverFirstLic'"
                [id]="'driverFirstLic'" [required]="false" [activeOption]="dfldriver.selectedOption"
                [searchFromBegining]="true" [allowOnlySelect]="true" (onSelect)="selectDriverDFL($event, dfldriver)" changeDetectionDelay>
              </sm-autocomplete>

              <app-modalbox #modalCustomDFL [launcher]="'#rmv-lookup-driver-modal'" [css]="'u-width-400px'">
                <table class="form-table">
                  <tr class="form-table__row">
                    <td class="form-table__cell">
                      <label for="driverDOB">Custom Date :</label>
                    </td>
                    <td class="form-table__cell">
                      <app-datepicker-input #refPickerDateFirstLic [name]="'driverDateFirstLic'"
                        [id]="'driverDateFirstLic'" [required]="true" [returnDateFormat]="'yyyy-MM-dd'"
                        [selectDate]="dfldriver.customDate" (onDateChange)="updateCustomDateChange($event.formatedDate)"
                        changeDetectionDelay>
                      </app-datepicker-input>
                    </td>
                  </tr>
                </table>
                <div class="row u-spacing--2">
                  <div class="col-xs-12 u-align-right">
                    <button class="o-btn" (click)="updateCustomDate(dfldriver)">
                      SAVE
                    </button>
                    <button class="o-btn o-btn--idle u-spacing--left-2" (click)="closeModalboxCustomDate()">
                      Cancel
                    </button>
                  </div>
                </div>
              </app-modalbox>
            </td>
          </tr>
        </table>
      </div>

      <div class="row u-spacing--2">
        <div class="col-xs-12 u-align-right">
          <button class="o-btn" (click)="saveDFLRouteToQuote()">
            Go To Quote
          </button>
          <button class="o-btn o-btn--idle u-spacing--left-2" (click)="modalRmvResultsDFL.closeModalbox()">
            Cancel
          </button>
        </div>
      </div>
    </ng-template>
  </app-modalbox>
</section>
