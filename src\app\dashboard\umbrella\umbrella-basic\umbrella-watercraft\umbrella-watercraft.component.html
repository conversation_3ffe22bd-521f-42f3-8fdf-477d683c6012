<section class="section section--compact u-spacing--2-5">
  <div class="u-flex u-flex--spread">
    <div class="">
      <h1 class="o-heading">Watercraft</h1>
    </div>
    <div class="">
      <button class="o-btn o-btn--action o-btn--i_round-plus" (click)="openAddWatercraftModal(modalAddWatercraft)">Add Watercraft</button>

      <app-modalbox #modalAddWatercraft>
        <h1 class="o-heading o-heading--red">Add Watercraft</h1>
        <div class="box box--silver u-spacing--1-5">
          <div class="row o-columns">
            <div class="col-xs-12">
              <table class="form-table form-table--fixed">
                <tr class="form-table__row" [ngClass]="{'is-required-field': refWatercraftType.ngModel?.invalid}">
                    <td class="form-table__cell u-width-160px">
                      Type of Watercraft:
                    </td>
                    <td class="form-table__cell" colspan="1">
                      <sm-autocomplete
                        #refWatercraftType
                        [options]="addWatercraftData.types"
                        [activeOption] ="addWatercraftDataSelected.propulsionType"
                        [name]="'wc-prop-type'"
                        [id]="'wc-prop-type'"
                        [searchFromBegining]="true"
                        [required]="true"
                        (onSelect)="selectedPropulsionType($event)">
                      </sm-autocomplete>
                    </td>
                </tr>
                <tr class="form-table__row" [class.is-required-field]="isHorsepowerRequiredAdd()">
                  <td class="form-table__cell u-width-160px">
                    Engine Horsepower:
                  </td>
                  <td class="form-table__cell">
                    <input type="text"
                      pattern="\d*"
                      [readOnly]="verifyPropulsionTypeSailAdd()"
                      maxlength="8"
                      (keydown)="numbersOnly($event)"
                     [(ngModel)]="addWatercraftData.horsePower">
                  </td>
                  <td class="form-table__cell"></td>
                </tr>
                <tr class="form-table__row" [class.is-required-field]="addWatercraftData?.length.length <1">
                  <td class="form-table__cell u-width-160px">
                    Length of Hull:
                  </td>
                  <td class="form-table__cell">
                    <input type="text"
                    pattern="\d*"
                    maxlength="8"
                    (keydown)="numbersOnly($event)"
                    [(ngModel)]="addWatercraftData.length">
                  </td>
                  <td class="form-table__cell"></td>
                </tr>
                <tr class="form-table__row" [class.is-required-field]="addWatercraftData?.speed.length <1">
                  <td class="form-table__cell u-width-160px">
                    Vessel Speed:
                  </td>
                  <td class="form-table__cell">
                    <input type="text"
                    maxlength="8"
                    pattern="\d*"
                    (keydown)="numbersOnly($event)"
                    [(ngModel)]="addWatercraftData.speed">
                  </td>
                </tr>
              </table>
            </div>
          </div>
        </div>
        <div class="row u-spacing--2">
          <div class="col-xs-12 u-align-right">
            <button class="o-btn u-spacing--right-1" (click)="addWatercraft(modalAddWatercraft,false)"
            [class.disabled]="verifyWatercraftModelAdd(modalAddWatercraft)">
              SAVE AND NEW
            </button>
            <button class="o-btn u-spacing--right-1" (click)="addWatercraft(modalAddWatercraft,true)"
            [class.disabled]="verifyWatercraftModelAdd(modalAddWatercraft)">
              SAVE
            </button>
            <button class="o-btn o-btn--idle" (click)="modalAddWatercraft.closeModalbox()">Cancel</button>
          </div>
        </div>
      </app-modalbox>
    </div>
  </div>
  <div class="row u-spacing--1">
    <div class="col-xs-12">
      <div class="box box--silver">
        <div class="row" >
          <div class="col-xs-12" *ngIf="!watercrafts || watercrafts?.length <= 0">
            <p>None</p>
          </div>

          <div class="col-xs-12" *ngIf="watercrafts?.length > 0">
            <ul class="manage-list">
              <li *ngFor="let watercraft of watercrafts" class="manage-list__item">
                <div class="manage-list__data">
                  <div class="manage-list__data-item manage-list__data-item--date">{{ watercraft.propulsionType }}</div>
                  <div class="manage-list__data-item manage-list__data-item--price">{{ watercraft.horsePower }}</div>
                  <div class="manage-list__data-item manage-list__data-item--type">{{ watercraft.length }}</div>
                  <div class="manage-list__data-item manage-list__data-item--state">{{ watercraft.speed }}</div>
                </div>
                <div class="manage-list__actions">
                  <a class="o-action o-action--icon-less manage-list__btn modal-manage-list-edit-watercraft" (click)="editWatercraftOpen(watercraft, modalEditWatercraft)">Edit</a>
                  <a class="o-action o-action--icon-less manage-list__btn confirm-manage-list-delete-watercraft" (click)="deleteWatercraftOpen(watercraft)">Delete</a>
                </div>
              </li>
            </ul>

            <app-modalbox #modalEditWatercraft>
              <h1 class="o-heading o-heading--red">Edit Watercraft</h1>
              <div class="box box--silver u-spacing--1-5">
                <div class="row o-columns">
                  <div class="col-xs-12">
                    <table class="form-table form-table--fixed">
                      <tr class="form-table__row" [ngClass]="{'is-required-field': refWatercraftTypeEdit.ngModel?.invalid}">
                          <td class="form-table__cell u-width-160px">
                            Type of Watercraft:
                          </td>
                          <td class="form-table__cell" colspan="1">
                            <sm-autocomplete
                              #refWatercraftTypeEdit
                              [options]="addWatercraftData.types"
                              [activeOption] = "editWatercraftDataSelected.propulsionType"
                              [name]="'wc-prop-type'"
                              [id]="'wc-prop-type'"
                              [searchFromBegining]="true"
                              [required]="true"
                              (onSelect)="selectedPropulsionType($event,true)">
                            </sm-autocomplete>
                          </td>
                      </tr>
                      <tr class="form-table__row" [class.is-required-field]="isHorsepowerRequiredEdit()">
                        <td class="form-table__cell u-width-160px">
                          Engine Horsepower:
                        </td>
                        <td class="form-table__cell">
                          <input type="text"
                          pattern="\d*"
                          [readOnly]="verifyPropulsionTypeSailEdit()"
                          (keydown)="numbersOnly($event)"
                          [(ngModel)]="editWatercraftDataSelected.horsePower">
                        </td>
                        <td class="form-table__cell"></td>
                      </tr>
                      <tr class="form-table__row"  [class.is-required-field]="editWatercraftDataSelected?.length.length <1" >
                        <td class="form-table__cell u-width-160px">
                          Length of Hull:
                        </td>
                        <td class="form-table__cell">
                          <input type="text"
                          pattern="\d*"
                          (keydown)="numbersOnly($event)"
                          [(ngModel)]="editWatercraftDataSelected.length">
                        </td>
                        <td class="form-table__cell"></td>
                      </tr>
                      <tr class="form-table__row" [class.is-required-field]="editWatercraftDataSelected?.speed.length <1">
                        <td class="form-table__cell u-width-160px">
                          Vessel Speed:
                        </td>
                        <td class="form-table__cell">
                          <input type="text"
                          pattern="\d*"
                          (keydown)="numbersOnly($event)"
                          [(ngModel)]="editWatercraftDataSelected.speed">
                        </td>
                      </tr>
                    </table>
                  </div>
                </div>
              </div>
              <div class="row u-spacing--2">
                <div class="col-xs-12 u-align-right">
                  <button class="o-btn u-spacing--right-2" (click)="editWatercraft(modalEditWatercraft)" [class.disabled]="verifyWatercraftModelEdit(modalEditWatercraft)">
                    Edit watercraft</button>
                  <button class="o-btn o-btn--idle" (click)="modalEditWatercraft.closeModalbox()">Cancel</button>
                </div>
              </div>
            </app-modalbox>

            <app-confirmbox #modalDeleteWatercraft
              [launcher]="'.confirm-manage-list-delete-watercraft'"
              [question]="'Are you sure you want to delete this watercraft?'"
              [askAbout]="deleteWatercraftDataSelectedAskAbout"
              [confirmBtnText]="'Yes, delete watercraft'"
              (onAccept)="deleteWatercraft($event, deleteWatercraftDataSelected)"
              (onCancel)="confirmCanceled($event)">
            </app-confirmbox>



          </div>
        </div>
      </div>
    </div>
  </div>
</section>
