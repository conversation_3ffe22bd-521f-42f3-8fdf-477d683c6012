<div class="row">
  <div class="col-xs-12">


    <div class="summarybox" *ngFor="let vehicle of arrVehicles;">
      <h3 class="summarybox__head">{{vehicle.vehicleName}}</h3>
      <div class="summarybox__body">
        <div class="summarybox__body-col u-width-60">
          <div class="summarybox__item">
            <div class="summarybox__item-label u-width-90px">Garaged:</div>
            <div class="summarybox__item-value u-width-auto">{{vehicle.garaged}}</div>
          </div>
          <div class="summarybox__item">
            <div class="summarybox__item-label u-width-90px">Usage:</div>
            <div class="summarybox__item-value u-width-auto">{{vehicle.usage}}</div>
          </div>
          <div class="summarybox__item">
            <div class="summarybox__item-label u-width-90px">Operator:</div>
            <div class="summarybox__item-value u-width-auto">{{vehicle.operator}}</div>
          </div>
          <div class="summarybox__item">
            <div class="summarybox__item-label u-width-90px">Mileage:</div>
            <div class="summarybox__item-value u-width-auto">{{vehicle.mileage}}</div>
          </div>
        </div>
        <div class="summarybox__body-col u-width-auto">
          <div class="summarybox__item">
            <div class="summarybox__item-value u-width-100">Coverages:</div>
          </div>
          <div class="summarybox__item" *ngFor="let item of vehicle.coverages">
            <div class="summarybox__item-value u-width-100 u-padd--left-1">{{item}}</div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>