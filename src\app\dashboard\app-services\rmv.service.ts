import { request } from 'https';
import { AvailableInventoryResponse, InventoryOrderRequest } from './../../app-model/inventory';
import { Injectable } from '@angular/core';
import { ApiService } from 'app/shared/services/api.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { Observable, of } from 'rxjs';
import { RmvServicesList } from '../../app-model/rmv-services-list.model';
import { RmvServicesSaveState } from '../../app-model/rmv-services-add.model';
import { Vehicle, GaragingAddres } from '../rmv-services/rta-prefill/get-ready.model';
import { DocumentsToDestroyParameters, DocumentDestructionRequest } from '../../app-model/document-destruction';
import { DriverRmvToSendWithResourceId, RmvDriverResult } from 'app/app-model/driver';
import { InventoryConfirmRequest } from 'app/inventory-confirm';
import { AssignedPlate } from 'app/app-model/evr-inventory';

@Injectable()
export class RmvService {
  constructor(
    private apiService: ApiService,
    private apiCommonService: ApiCommonService
  ) {}

  // Driver
  // -------------------------------------------------------------------------------
  public updateDriversByLicense(
    quoteId: string,
    policyEffectiveDate: string,
    drivers: DriverRmvToSendWithResourceId[],
    lob: string = 'autop'
  ): Observable<any> {
    const uri = '/lookups/rmv/drivers/licenses';
    const data = {
      quoteId: quoteId,
      policyEffectiveDate: policyEffectiveDate,
      drivers: drivers,
      lob: lob
    };

    return this.apiCommonService.putByUri(uri, data);
  }

  // Helpers
  // -------------------------------------------------------------------------------
  public generateRmvReportDriverId(driver: RmvDriverResult): string {
    const {firstName, lastName, licenseNumber} = driver;
    const fname = firstName ? firstName.toLocaleLowerCase() : firstName;
    const lname = lastName ? lastName.toLocaleLowerCase() : lastName;
    const plate = licenseNumber ? licenseNumber.toLocaleLowerCase() : licenseNumber;
    return fname + '-' + lname + '-' + plate;
  }

  public RMVForm(data) {
    const uri = `rmvservices/agencyforms`;
    return this.apiCommonService.postByUri(uri, data);
  }

  public Validation(data) {
    const uri = `rmv/validations`;
    return this.apiCommonService.postByUri(uri, data);
  }

  public CheckRmvAccess() {
    const uri = `/checkrmvaccess`;
    return this.apiCommonService.getByUri(uri);
  }

  public paymentRequest(data) {
    const uri = `paymentrequests`;
    return this.apiCommonService.postByUri(uri, data);
  }

  public updatePaymentRequest(data) {
    const uri = `updatePaymentRequest`;
    return this.apiCommonService.postByUri(uri,data);
  }

  public paymentRequestEasy(data) {
    const uri = `paymentrequestseasy`;
    return this.apiCommonService.postByUri(uri, data);
  }

  public getReadySubmit(data) {
    const uri = 'rmv/getready';
    return this.apiCommonService.postByUri(uri, data);
  }

  public getSavedRmvList(criteria, offset, limit=0) {
    if (criteria === '') { criteria = `?offset=${offset}`; } else { criteria += `&offset=${offset}`; }
    if (criteria === '') { criteria = `?limit=${limit}`; } else { criteria += `&limit=${limit}`; }
    const uri = `rmvservices${criteria}`;
    return this.apiCommonService.getByUri(uri);
  }

  public SaveRmvServiceTransaction(data) {
    const uri = 'rmvservices';
    return this.apiCommonService.postByUri(uri, data);
  }

  public getRmvServiceDetails(id) {
    const uri = `rmvservices/${id}`;
    return this.apiCommonService.getByUri(uri);
  }

  public updateRmvServiceDetails(id, data) {
    const uri = `rmvservices/${id}`;
    return this.apiCommonService.putByUri(uri, data);
  }

  public deleteRmvTransactions(data) {
    const uri = 'rmvservices';
    return this.apiCommonService.putByUri(uri, data);
  }

  public evrValidate(data) {
    const uri = 'rmv/evrValidate';
    return this.apiCommonService.postByUri(uri, data);
  }

  deleteRmvDocs(id, data) {
    const uri = `rmvservices/${id}/deletedocs`;
  return this.apiCommonService.putByUri(uri, data);
  }

getEvrDetails(id) {
  const uri = `rmvservices/${id}?workflow=EvrLite&alldocs=true`;
  return this.apiCommonService.getByUri(uri);
}

printValidatedRTA(data) {
  const uri = 'rmv/printdocument';
  return this.apiCommonService.postByUri(uri, data);
}

evrProcess(id) {
  const uri = `rmv/evrProcess/${id}`;
  return this.apiCommonService.postByUri(uri, {});
}

getVehicleReport(data) {
  const uri = `rmv/generatevehiclereport`;
  return this.apiCommonService.postByUri(uri, data);
}

refreshToken() {
  return this.apiCommonService.getByUri('refreshToken');
}

GetDocumentsToDestroy(params: DocumentsToDestroyParameters) {

  let uri = 'rmv/getDocumentsToDestroy';
  Object.keys(params).forEach((key, index) => {
    if (index === 0) {
      uri += `?${key}=${params[key]}`;
    } else {
      uri += `&${key}=${params[key]}`;
    }
  });
   return this.apiCommonService.getByUri(uri);
}

DestroyDocuments(data: DocumentDestructionRequest) {
  const uri = `rmv/destroyDocuments`;
  return this.apiCommonService.postByUri(uri, data);
}

GetDocumentDestructionDetails() {
  const uri = `rmv/getDocumentDestructionDetails`;
  return this.apiCommonService.getByUri(uri);
}

getAvailableInventoryItems() {
  return this.apiCommonService.getByUri('/rmv/GetAvailableInventoryItems');
}

getInventoryOrderList() {
  return this.apiCommonService.getByUri('/rmv/GetInventoryOrderList');
}

confirmInventoryOrder(request) {
  return this.apiCommonService.postByUri('/rmv/ConfirmInventoryOrder', request);
}

orderInventory(request) {
  return this.apiCommonService.postByUri('/rmv/OrderInventory', request);
}

updateStatus(request) {
  return this.apiCommonService.putByUri(`rmvservices/${request.rmvServicesId}/statusUpdate`, request);
}

downloadDocuments(id) {
  return this.apiCommonService.getByUri(`rmvservices/${id}/documents`, {responseType: 'arraybuffer'});
}


initializeVehicle(data: Vehicle) {
const {id, lookupType, usage, bodyStyle, ownership, condition, primaryColor, transmission,
  passengers, outOfStateTitleNumber, titleIssueDate, titleState, plateNumber, vin, plateType, odometer,
  odometerCode, registrationReason, reassignedPlate, registeredWeight, registrationType, year, make, model,
  modelNumber, atlasVehicleIndicator, atlasVehicleKey, pseudoTrailerVINIndicator, vehicleType, secondaryColor, cylinders, doors, fuelType, trim,
  numberOfSeats, msrp, grossVehicleWeight, titleBrands, titleType
} = data;
    return {id: 1, lookupType: lookupType ?? '', usage: usage ?? '', bodyStyle: bodyStyle ?? '', ownership: ownership ?? '',
    condition: condition ?? '', primaryColor: primaryColor ?? '', transmission: transmission ?? '',  passengers: passengers ?? '',
    outOfStateTitleNumber: outOfStateTitleNumber ?? '', titleIssueDate: titleIssueDate ?? '', titleState: titleState ?? '', msrp: msrp ?? '',
    plateNumber: plateNumber ?? '', vin: vin ?? '', plateType: plateType ?? '', odometer: odometer ?? '', odometerCode: odometerCode ?? '',
    registrationReason: registrationReason ?? '', reassignedPlate: reassignedPlate ?? '', registrationType: registrationType ?? '',
    year: year ?? '', make: make ?? '', model: model ?? '',  modelNumber: modelNumber ?? '', atlasVehicleIndicator: atlasVehicleIndicator ?? '',
    atlasVehicleKey: atlasVehicleKey ?? '', pseudoTrailerVINIndicator : pseudoTrailerVINIndicator ?? '', vehicleType: vehicleType ?? '',
    secodaryColor: secondaryColor ?? '', cylinders: cylinders ?? '', doors: doors ?? '', fuelType: fuelType ?? '', trim: trim ?? '',
    numberOfSeats: numberOfSeats ?? '', grossVehicleWeight : grossVehicleWeight ?? '', registeredWeight: registeredWeight ?? '',
    titleBrands: titleBrands ?? [], titleType: titleType ?? ''};
  }

  initializeGarage(data: GaragingAddres) {
  const {type, referenceId, referenceType, state, street, street2, unitOrApt, unitType, city, zip} = data;
    return {
      type: type ?? '', referenceId: referenceId ?? '', referenceType: type ?? '', state: state ?? '',
       street: street ?? '', street2: street2 ?? '' , unitOrApt: unitOrApt ?? '', unitType: unitType ?? '' , city: city ?? '',  zip: zip ?? '' };
  }

  initializeInsurance(data) {
   return {effectiveDate: data.effectiveDate ?? '', writingCompanyName: data.writingCompanyName ?? '',
    companyCode: data.companyCode ?? 0, agencyName: data.agencyName ?? '', signedBy: data.signedBy ?? '',
     policyChangeDate: data.policyChangeDate ?? '', dateType: 'EffectiveDate'};
  }

  initializePurchaseAndSales(data) {
    const year = data.purchaseDate.substring(0, 4);
    const month = data.purchaseDate.substring(4, 6);
    const day = data.purchaseDate.substring(6, 8);

   const pdate = year ? new Date(year, month - 1, day) : '';
    return {purchaseDate: pdate.toString() ?? '', purchaseState: data.purchaseState ?? '',
    taxExempt: data.taxExempt ?? '', taxExemptType: data.taxExemptType ?? '',
     purchaseType: data.purchaseType ?? '', dealerFid: data.dealerFid ?? '',
    totalSalePrice: data.totalSalePrice ?? '', auctionSale: data.auctionSale ?? '',
     maResidentAtTimeOfPurchase: data.maResidentAtTimeOfPurchase ?? '',
     maSalesTaxPreviouslyPaid: data.maSalesTaxPreviouslyPaid ?? '',
    nonMASalesTaxPreviouslyPaid: data.nonMaSalesTaxPreviouslyPaid ?? '', proofOfNoTaxRequired: data.proofOfNoTaxRequired ?? '',
    seller: {businessName: data.seller.businessName ?? '', firstName: data.seller.firstName ?? '', lastName: data.seller.lastName ?? '',
    address: {street: data.seller.address.street ?? '', unitOrApt: data.seller.address.unitOrApt ?? '',
     city: data.seller.address.city ?? '', state: data.seller.address.state ?? '', zip: data.seller.address.zip ?? ''}}};
  }

  initializeOwner(data) {

    return [
      { id: 1, firstName: data[0]?.firstName ?? '', lastName: data[0]?.lastName ?? '', middleName: data[0]?.middleName ?? '',
      license: data[0]?.license ?? '', dateOfBirth: data[0]?.dateOfBirth ?? '', licenseState: data[0]?.licenseState ?? 'MA',
       fid: data[0]?.fid ?? '', ssn: data[0]?.ssn ?? '', email: data[0]?.email ?? '', phone: data[0].phone ?? '', phoneType: data[0].phoneType ?? '',
       mailAddress: data[0].mailAddress ?? {} , residentialAddress: data[0].residentialAddress ?? {} },
      { id: 2, firstName: data[1]?.firstName ?? '', lastName: data[1]?.lastName ?? '', middleName: data[1]?.middleName ?? '',
      license: data[1]?.license ?? '', dateOfBirth: data[1]?.dateOfBirth ?? '', licenseState: data[1]?.licenseState ?? 'MA',
      mailAddress: data[1]?.mailAddress ?? {} , residentialAddress: data[1]?.residentialAddress ?? {},
       fid: data[1]?.fid ?? '', ssn: data[1]?.ssn ?? '', email: data[1]?.email ?? '',
       phone: data[1]?.phone ?? '', phoneType: data[1]?.phoneType ?? '' }
    ];
  }

  checkEvrLiteEligibility(plateType='') {
    return this.apiCommonService.getByUri(`rmv/checkEvrLiteEligiblity/${plateType}`);
  }

  createRmvTransactionFromQuote(data, quoteId, driverIndexes, vehicleIndex, quoteIdentifier) {
    const url = `rmvservices/direct?quoteId=${quoteId}&vehicleIndex=${vehicleIndex}&driverIndices=${driverIndexes}&quoteIdentifier=${quoteIdentifier}`;
    return this.apiCommonService.postByUri(url, data);
  }

  getAvailablePlate(plateType) {
    return this.apiCommonService.getByUri(`rmv/getavailableplate?plateType=${plateType}`)

  }

  updateInventoryStatus(request) {
    return this.apiCommonService.postByUri('rmv/updateInventoryStatus',request);
  }

  updateQuantityInventoryStatus(request) {
    return this.apiCommonService.postByUri(`rmv/updateQuantityInventoryStatus`,request)
  }

  getInventoryReport(data) {
    const uri = `rmv/generateinventoryreport`;
    return this.apiCommonService.getByUri(uri, data);
  }

  getSummaryInventoryReport(data) {
    const uri = `rmv/GenerateSummaryInventoryReport`;
    return this.apiCommonService.getByUri(uri, data);
  }
    getBusinessList(fid) {
    const uri = `rmv/getbusinesslist?fid=${fid}`
    return this.apiCommonService.getByUri(uri)

  }


}
