import { StubPremiumsServiceProvider } from 'testing/stubs/services/premiums.service.provider';
import { inject } from '@angular/core/testing';
import { async, ComponentFixture, fakeAsync, TestBed, tick } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { RouterTestingModule } from '@angular/router/testing';

import { data as AGENCY_LOCATIONS_DATA } from 'testing/data/agencies/locations';
import { data as AGENCY_USER } from 'testing/data/agencies/user';
import { data as AGENCY_USERS_DATA } from 'testing/data/subs/agency-users';
import { data as QUOTES_DATA } from 'testing/data/quotes/quotes';
import { simulateTextInput } from 'testing/helpers/dom';

import { StubAutocompleteComponent } from 'testing/stubs/components/autocomplete.component';
import { StubDatepickerInputComponent } from 'testing/stubs/components/datepicker-input.component';
import { StubFilterComponent } from 'testing/stubs/components/filter.component';
import { StubPaginationComponent } from 'testing/stubs/components/pagination.component';
import { StubResultsLimiterComponent } from 'testing/stubs/components/results-limiter.component';
import { StubAgencyUserService, StubAgencyUserServiceProvider } from 'testing/stubs/services/agency-user.service.provider';
import { StubDriversServiceProvider } from 'testing/stubs/services/drivers.service.provider';
import { StubLocationsServiceProvider } from 'testing/stubs/services/locations.service.provider';
import { StubOverlayLoaderServiceProvider } from 'testing/stubs/services/overlay-loader.service.provider';
import { StubQuotesService } from 'testing/stubs/services/quotes.service.provider';
import { StubQuotesServiceProvider } from 'testing/stubs/services/quotes.service.provider';
import { StubSpecsServiceProvider } from 'testing/stubs/services/specs.service.provider';

import { StubSubsService } from 'testing/stubs/services/subs.service.provider';
import { StubSubsServiceProvider } from 'testing/stubs/services/subs.service.provider';
import { StubVehiclesServiceProvider } from 'testing/stubs/services/vehicles.service.provider';

import { DatesService } from 'app/dashboard/app-services/dates.service';
import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { SubsService } from 'app/dashboard/app-services/subs.service';
import { AgencyUserService } from 'app/shared/services/agency-user.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { Helpers } from 'app/utils/helpers';

import { PageQuotesComponent } from './page-quotes.component';

function getFilterAndEmit(fixture, filterId, option) {
  const filter = fixture.debugElement.queryAll(By.directive(StubFilterComponent)).filter((de) => {
    return de.componentInstance.id === filterId;
  })[0].componentInstance;
  filter.onChange.emit({
    selectedOption: option,
    filterId: filterId
  });

  fixture.detectChanges();
  tick();
}

describe('Component: PageQuotes', () => {
  let component: PageQuotesComponent;
  let fixture: ComponentFixture<PageQuotesComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [FormsModule, RouterTestingModule],
      declarations: [
        PageQuotesComponent,
        StubFilterComponent,
        StubAutocompleteComponent,
        StubDatepickerInputComponent,
        StubPaginationComponent,
        StubResultsLimiterComponent
      ],
      providers: [
        StorageGlobalService,
        StubQuotesServiceProvider,
        DatesService,  // not stubbed, just utility service
        StubDriversServiceProvider,
        StubVehiclesServiceProvider,
        StubLocationsServiceProvider,
        StubSpecsServiceProvider,
        StubSubsServiceProvider,
        StorageService,
        StubAgencyUserServiceProvider,
        StubOverlayLoaderServiceProvider,
        StubPremiumsServiceProvider
      ]
    })
      .compileComponents();

    jasmine.clock().mockDate(new Date(2017, 10, 27));
  }));

  describe('when only default data is available', () => {
    beforeEach(fakeAsync(inject([AgencyUserService, SubsService],
      (agencyUserService: StubAgencyUserService, subsService: StubSubsService) => {
        agencyUserService.userData = Helpers.deepClone(AGENCY_USER);
        subsService.agencyUsers = Helpers.deepClone(AGENCY_USERS_DATA);
        subsService.agencyLocations = Helpers.deepClone(AGENCY_LOCATIONS_DATA);

        fixture = TestBed.createComponent(PageQuotesComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
        tick();
      })));

    it('should create', () => {
      expect(component).toBeTruthy();
    });

    it('should destroy without errors', () => {
      expect(() => {
        fixture.destroy();
      }).not.toThrow();
    });
  });

  describe('when agency users data is available in storage', () => {
    beforeEach(fakeAsync(inject([AgencyUserService, SubsService, StorageGlobalService],
      (agencyUserService: StubAgencyUserService, subsService: StubSubsService,
        storageGlobalService: StorageGlobalService) => {
        agencyUserService.userData = Helpers.deepClone(AGENCY_USER);
        subsService.agencyLocations = Helpers.deepClone(AGENCY_LOCATIONS_DATA);
        storageGlobalService.setSubs('users', Helpers.deepClone(AGENCY_USERS_DATA.items));

        fixture = TestBed.createComponent(PageQuotesComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
        tick();
      })));

    it('should create', () => {
      expect(component).toBeTruthy();
    });
  });

  describe('when quotes are available', () => {
    beforeEach(fakeAsync(inject([AgencyUserService, SubsService, QuotesService],
      (agencyUserService: StubAgencyUserService, subsService: StubSubsService,
      quotesService: StubQuotesService) => {
        agencyUserService.userData = Helpers.deepClone(AGENCY_USER);
        subsService.agencyUsers = Helpers.deepClone(AGENCY_USERS_DATA);
        subsService.agencyLocations = Helpers.deepClone(AGENCY_LOCATIONS_DATA);
        quotesService.quotes = Helpers.deepClone(QUOTES_DATA);

        spyOn(quotesService, 'getQuotes').and.callThrough();

        fixture = TestBed.createComponent(PageQuotesComponent);
        component = fixture.componentInstance;
        fixture.detectChanges();
        tick();
      })));

    it('should have a list of all quotes', () => {
      expect(component.arrQuotesAll.length).toEqual(3);
    });

    it('should request quotes from service only once', fakeAsync(inject([QuotesService],
      (quotesService: StubQuotesService) => {
        // console.log((<jasmine.Spy>quotesService.getQuotes).calls.all());
        expect(quotesService.getQuotes).toHaveBeenCalledTimes(1);
    })));

    it('should filter quotes by name', fakeAsync(inject([QuotesService],
      (quotesService: StubQuotesService) => {
        quotesService.quotes = {
          items: [
            Helpers.deepClone(QUOTES_DATA.items[1])
          ]
        };

        component.searchQuery = 'newton';  // update ngModel
        const input = fixture.debugElement.query(By.css('#filter_NAME')).nativeElement;
        simulateTextInput(fixture, input, 'newton', true);

        expect((<jasmine.Spy>quotesService.getQuotes).calls.mostRecent().args[2]).toEqual('newton');
        expect(component.arrQuotesFiltered.length).toEqual(1);
      })));

    it('should filter quotes by LOB', fakeAsync(inject([QuotesService],
      (quotesService: StubQuotesService) => {
        quotesService.quotes = {
          items: [
            Helpers.deepClone(QUOTES_DATA.items[0]),
            Helpers.deepClone(QUOTES_DATA.items[1])
          ]
        };

        getFilterAndEmit(fixture, 'filter_LOB', {
          id: 'AUTOP',
          text: 'Personal Auto'
        });

        expect((<jasmine.Spy>quotesService.getQuotes).calls.mostRecent().args[4]).toEqual('AUTOP');
        expect(component.arrQuotesFiltered.length).toEqual(2);
      })));

    it('should filter quotes by agent', fakeAsync(inject([QuotesService],
      (quotesService: StubQuotesService) => {
        quotesService.quotes = {
          items: [
            Helpers.deepClone(QUOTES_DATA.items[0]),
            Helpers.deepClone(QUOTES_DATA.items[2])
          ]
        };

        getFilterAndEmit(fixture, 'filter_agents', {
          id: '34080',
          text: 'Janet Fraiser'
        });

        expect((<jasmine.Spy>quotesService.getQuotes).calls.mostRecent().args[3]).toEqual('34080');
        expect(component.arrQuotesFiltered.length).toEqual(2);
      })));

    it('should filter quotes by agency location', fakeAsync(inject([QuotesService],
      (quotesService: StubQuotesService) => {
        quotesService.quotes = {
          items: [
            Helpers.deepClone(QUOTES_DATA.items[0]),
            Helpers.deepClone(QUOTES_DATA.items[2])
          ]
        };

        getFilterAndEmit(fixture, 'filter_locations', {
          id: '34765',
          text: 'Widget World'
        });

        expect((<jasmine.Spy>quotesService.getQuotes).calls.mostRecent().args[5]).toEqual('34765');
        expect(component.arrQuotesFiltered.length).toEqual(2);
      })));

    it('should filter quotes by date type', fakeAsync(inject([QuotesService],
      (quotesService: StubQuotesService) => {
        quotesService.quotes = {
          items: [
            Helpers.deepClone(QUOTES_DATA.items[1])
          ]
        };

        const autoComplete = fixture.debugElement.queryAll(By.directive(StubAutocompleteComponent))[0].componentInstance;
        autoComplete.onSelect.emit({
          id: 'effective',
          text: 'Effective'
        });

        fixture.detectChanges();
        tick();

        expect((<jasmine.Spy>quotesService.getQuotes).calls.mostRecent().args[6]).toEqual('Effective'); // API expects uppercase
        expect(component.arrQuotesFiltered.length).toEqual(1);
      })));

    it('should filter quotes by date range', fakeAsync(inject([QuotesService],
      (quotesService: StubQuotesService) => {
        quotesService.quotes = {
          items: [
            Helpers.deepClone(QUOTES_DATA.items[2])
          ]
        };

        const filters = fixture.debugElement.queryAll(By.directive(StubAutocompleteComponent));
        filters[0].componentInstance.onSelect.emit({
          id: 'effective',
          text: 'Effective'
        });
        filters[1].componentInstance.onSelect.emit({
          id: 'thismonth',
          text: 'This month'
        });

        fixture.detectChanges();
        tick();

        expect((<jasmine.Spy>quotesService.getQuotes).calls.mostRecent().args[7]).toEqual('2017-11-01');
        expect((<jasmine.Spy>quotesService.getQuotes).calls.mostRecent().args[8]).toEqual('2017-11-30');
        expect(component.arrQuotesFiltered.length).toEqual(1);
      })));

    it('should filter quotes by custom date range', fakeAsync(inject([QuotesService],
      (quotesService: StubQuotesService) => {
        quotesService.quotes = {
          items: [
            Helpers.deepClone(QUOTES_DATA.items[2])
          ]
        };

        const filters = fixture.debugElement.queryAll(By.directive(StubAutocompleteComponent));
        filters[0].componentInstance.onSelect.emit({
          id: 'effective',
          text: 'Effective'
        });

        fixture.detectChanges();
        tick();

        const datePickers = fixture.debugElement.queryAll(By.directive(StubDatepickerInputComponent));
        datePickers[0].componentInstance.onDateChange.emit({
          date: new Date(2017, 10, 1),
          selectByClick: true
        });

        fixture.detectChanges();
        tick();

        datePickers[1].componentInstance.onDateChange.emit({
          date: new Date(2017, 10, 30),
          selectByClick: true
        });

        fixture.detectChanges();
        tick();

        expect((<jasmine.Spy>quotesService.getQuotes).calls.mostRecent().args[7]).toEqual('2017-11-01');
        expect((<jasmine.Spy>quotesService.getQuotes).calls.mostRecent().args[8]).toEqual('2017-11-30');
        expect(component.arrQuotesFiltered.length).toEqual(1);
    })));

    it('should handle page change', fakeAsync(inject([QuotesService],
      (quotesService: StubQuotesService) => {
        const pagination = fixture.debugElement.query(By.directive(StubPaginationComponent)).componentInstance;
        pagination.onPageChange.emit({
          startAt: 10
        });

        fixture.detectChanges();
        tick();

        expect((<jasmine.Spy>quotesService.getQuotes).calls.mostRecent().args[0]).toEqual('10');
      })));

    it('should handle results limit change', fakeAsync(inject([QuotesService],
      (quotesService: StubQuotesService) => {
        const limiter = fixture.debugElement.query(By.directive(StubResultsLimiterComponent)).componentInstance;
        limiter.onChange.emit({
          limit: 20
        });

        fixture.detectChanges();
        tick();

        fixture.detectChanges();
        tick();

        expect((<jasmine.Spy>quotesService.getQuotes).calls.mostRecent().args[1]).toEqual('20');
      })));
  });
});
