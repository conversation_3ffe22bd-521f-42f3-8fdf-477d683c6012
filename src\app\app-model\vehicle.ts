import { FilterOption } from 'app/app-model/filter-option';
import { LocationData } from 'app/app-model/location';
import { CoverageItemParsed } from './coverage';
import { extend } from 'webdriver-js-extender';

export class Vehicle {
  constructor(
    public meta = { href: <string>'', rel: <any[]>['vehicle']},
    public vehicleType: string = null,
    public usage: string = null,
    public year: string = null,
    public make: string = null,
    public model: string = null,
    public bodyStyle: string = null,
    public vin: string = '',
    public licensePlate: string = '',
    public annualMiles: string = null,
    public priceValue: string = null,
    public ignoreCarrierCityAndMiles: boolean = false,
    public options: VehicleOptions = new VehicleOptions(),
    public symbols: VehicleSymbol[] = null,
    public garagingAddress: any = null,
    public owner = {
      meta: { href: <string>'' }
    },
    public operator = {
      meta: { href: <string>'' }
    },
    public standardCoverages = {
      meta: { href: <string>''}
    },
    public additionalCoverages = {
      meta: { href: <string>''}
    },
    public quoteSessionId: string = '',
    public resourceId: string = '',
    public resourceName: string = 'Vehicle',
    public parentId: string = '',
    public trimLevel = null,
    public displacement: string = null,
  ) {}
  orderIndex;
}

export class VehicleOptions {
  constructor (
    public highTheft: boolean = false,
    public leased: boolean = false,
    public antilockBrakes: boolean = false,
    public hybrid: boolean = false,
    public alarm: boolean = false,
    public activeDisabling: boolean = false,
    public passiveDisabling: boolean = false,
    public vehicleRecovery: boolean = false,
    public otherMACategoryI: boolean = false,
    public otherMACategoryII: boolean = false,
    public otherMACategoryIII: boolean = false,
    public otherMACategoryIV: boolean = false,
    public otherMACategoryV: boolean = false,
    public driverAirbag: boolean = false,
    public passengerAirbag: boolean = false,
    public driverAutomaticBelt: boolean = false,
    public passengerAutomaticBelt: boolean = false,
  ) {}
}

export class VehicleGeneralDetails {
  constructor (
    public collisionSymbol: string = null,
    public comprehensiveSymbol: string = null,
    public detail: VehicleSpecificDetails = null,
    public manufacturer: VehicleManufacturer = null,
    public massAntiTheftCode: string = null,
    public massHighTheftCode: string = null,
    public massBodyStyleGroup: string = null,
    public meta: any = null,
    public polkRestraintCode: string = null,
    public resourceName: string = null,
    public seriesName: string = null,
    public symbol: string = null,
    public vin: string = null,
    public vrgDetail: VehicleVRGDetail = null,
    public year: string = null,
    public safetyCustomSymbol: string = null,
    public success: boolean = null,
    public message: string = null
  ) {}
}

export class VehicleDetailReport {
  constructor(
    public year: string = null,
    public make: string = null,
    public model: string = null,
    public trimLevel: string = null,
    public vin: string = null,
    public comprehensiveSymbol: string = null,
    public collisionSymbol: string = null,
    public antiTheftDevicesDesc: string = null,
    public restraintIndicatorDesc: string = null,
    public highTheftCode: string = null,
    public antiLockBrakeCodeDesc: string = null,
    public cylinders: string = null,
    public bodyStyle: string = null,
    public vsrSymbol: string = null,
    public stateException: string = null
  ) {}
}
export class VehicleGeneralDetailsHelper {
  constructor (
    public vehicleResourceId: string = null,
    public vehicleGeneralDetails: VehicleGeneralDetails = null,
    public returnedEmptyVehicleGeneralDetails: boolean = null,
    public validVinNumber: boolean = null,
    public validPartialVinNumber: boolean = null
  ) {}
}

export class VehicleVRGDetail {
  constructor(
    public coll_Sym: string = null,
    public comp_Sym: string = null
  ) {}
}


export class VehicleSpecificDetails {
  constructor (
    public abbModNam: string = null,
    public antiTheftDevices: string = null,
    public antiLockBrakeCode: string = null,
    public antiTheftDevicesDesc: string = null,
    public restraintIndicatorDesc: string = null,
    public antiLockBrakeCodeDesc: string = null,
    public bodyDescription: any = null,
    public bodyInfo: string = null,
    public bodyStyle: string = null,
    public bodyStyleDescription: string = null,
    public collisionSymbol: string = null,
    public comprehensiveSymbol: string = null,
    public cost: string = null,
    public cylinderNumDescription: string = null,
    public engineDescription: any = null,
    public engineSizeDescription: string = null,
    public engnInfo: string = null,
    public engnSize: string = null,
    public engn_Cyln: string = null,
    public idVinMast: number = null,
    public iin: string = null,
    public manufact: string = null,
    public modelDescription: any = null,
    public modelInfo: string = null,
    public modelName: string = null,
    public notClasInf: string = null,
    public otherDescription: any = null,
    public restInfo: string = null,
    public restraintDescription: any = null,
    public restraintIndicator: string = null,
    public specialInfoSelector: any = null,
    public splinfoSel: string = null,
    public stateException: string = null,
    public transInfo: string = null,
    public transmissionDescription: any = null,
    public vin: string = null,
    public vsrSymbol: string = null
  ) {}
}

export class VehicleSymbol {
  constructor (
    public codeSource: string = '',
    public codeType: string = '',
    public conversionType: string = '',
    public description: string = '',
    public symbolType: string = '',
    public symbolValue: string = ''
  ) {}
}

export class VehicleSymbolRequestData {
  constructor (
    public price: string = null,
    public policyEffectiveDt: string = null,
    public ratingPlans: string = null,
    public vrgDetailComprehensive: string = null,
    public vrgDetailCollision: string = null,
    public symbol: string = null,
    public collisionSymbol: string = null,
    public comprehensiveSymbol: string = null,
    public safetyCustomSymbol: string = null,
    // public bodyStyleGroup:string = null,
    public massBodyStyleGroup: string = null,
  ) {}
}

export class VehiclePlanSymbols {
  constructor (
    public meta: any = null,
    public ratingPlanId: string = null,
    public resourceName: string = 'PlanSymbols',
    public symbols: VehicleSymbol[] = []
  ) {}
}


export class VehicleManufacturer {
  constructor (
    public code: string = '',
    public name: string = ''
  ) {}
}


export class VehicleOptionsForVehicle {
  constructor (
    public vehicleResourceId: string = null,
    public options: FilterOption[] = []
  ) {}
}

export class VehicleLocationDataForVehicle {
  constructor (
    public vehicleResourceId: string = '',
    public vehicleQuoteSessionId: string = '',
    public vehicleMeta: any = {href: <string>''},
    public vehicleYear: string = '',
    public vehicleMake: string = '',
    public vehicleModel: string = '',
    public location: LocationData = null
  ) {}
}

// RMV
export class VehicleRMVLookupData {
  constructor (
    public garagingLocation: string = '',
    public garagingResourceId: string = '',
    public inspections: {date: string, type: string, odometer: string}[] = [],
    public lessors: any[] = [],
    public licensePlate: string = '',
    public make: string = '',
    public messages: {message: string}[] = [],
    public model: string = '',
    public owners: any[] = [],
    public plateType: string = '',
    public registrationDate: string = '',
    public registrationStatus: string = '',
    public registrationStatusDesc: string = '',
    public registrationExpirationDate: string = '',
    public resourceId: string = '',
    public trimLevel: string = '',
    public vin: string = '',
    public year: string = '',
    public isIncluded: boolean = true
    
  ) {}
  orderIndex: number;
}

export class VehicleCoverages {
  constructor (
    public vehicleResourceId: string = '',
    public vehicle: Vehicle = new Vehicle(),
    public options: CoverageItemParsed[] = []
  ) {}
  defaultOption: string;
}

export class CommercialType {
  constructor(
    public id = '',
    public name = '',
    public value = ''
  ) { }
}

export class CommercialVehicle extends Vehicle {

  constructor(
    public bodyTypeCode: string = '',
    public unitDescription: string = '',
    public vehicleWeight: string = '',
    public licensePlate: string = '',
    public plateType: string = '',
    public radius: string = '',
    public classCode: string = '',
    public secondaryClassCd: string = '',
    public secondaryClassUsage: string = ''
  ) { super(); }
}


export class CommercialClassCodeLookup  {

  constructor(
    public classCode: string = '',
    public vehicleType: string = '',
    public unitDescription: string = '',
    public vehicleWeight: string = '',
    public radius: string = '',
    public usage: string = '',
    public secondaryClassCd: string = '',
    public secondaryClassUsage: string = ''
  ) {  }
}



