
import {map} from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { ApiService } from 'app/shared/services/api.service';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';

import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';

const SUBS_URL = '/subs/';

@Injectable()
export class SubsService {
  public subs;

  constructor(
    private http: HttpClient,
    private apiService: ApiService,
    private storageGlobalService: StorageGlobalService,
    private apiCommonService: ApiCommonService
  ) { }

  public getRatingPlans(agencyId: string): Observable<any> {
    return this.http.get(this.apiService.url(SUBS_URL + agencyId + '/ratingplans?lobs=')).pipe(map((res: any) => {
      this.storageGlobalService.setSubs('plans', res.items);
      console.log('%c Rating plans: ', 'color: green', res);

      return res;
    }));
  }

  public getRatingStates(agencyId: string, lobs: string = ''): Observable<any> {
    return this.http.get(this.apiService.url(SUBS_URL + agencyId + '/ratingstates?lobs=' + lobs)).pipe(map((res: any) => {
      this.storageGlobalService.setSubs('states', res.items);

      return res;
    }));
  }

  public getRatingLobs(agencyId: string): Observable<any> {
    const {agencyId: aId} = JSON.parse(localStorage.getItem('userData'));
    agencyId = agencyId && +agencyId > 0 ? agencyId : aId;
    return this.http.get(this.apiService.url(SUBS_URL + agencyId + '/ratinglobs')).pipe(map((res: any) => {
      this.storageGlobalService.setSubs('lobs', res.items);

      return res;
    }));
  }

  public getAgencyUsers(agencyId: string): Observable<any> {
    return this.http.get(this.apiService.url(SUBS_URL + agencyId + '/users')).pipe(map((res: any) => {
      this.storageGlobalService.setSubs('users', res.items);

      return res;
    }));
  }

  public getAgencyLocations(agencyId: string): Observable<any> {
    return this.apiCommonService.getByUri('/agencies/' + agencyId);
  }

  public getAgencyCodesForRatingPlan(agencyId: string, ratingPlanId: string, lobs: string = ''): Observable<any> {
    return this.apiCommonService.getByUri(SUBS_URL + agencyId + '/agencyCodes?ratingPlanId=' + ratingPlanId + '&lob=' + lobs);
  }

  // Coverage Defaults Auto Policy
  public getDefaultAutoCarrierOptions(agencyId: string): Observable<any> {
    return this.apiCommonService.getByUri(SUBS_URL + agencyId + '/coverageDefaults/autoPolicy');
  }

  // Coverage Defaults Home Policy
  public getDefaultHomeCarrierOptions(agencyId: string, formtype): Observable<any> {
    return this.apiCommonService.getByUri(SUBS_URL + agencyId + '/coverageDefaults/homePolicy');
  }

  public getDwellDefaults(agencyId: string): Observable<any> {
    return this.apiCommonService.getByUri(SUBS_URL + agencyId + '/coverageDefaults/DFIRE_CARRIERS');
  }

  public getDefaultAutoAdditionalCoverages(agencyId: string): Observable<any> {
    return this.apiCommonService.getByUri(SUBS_URL + agencyId + '/coverageDefaults/vehicle');
  }

  log(log) {
  return this.apiCommonService.postByUri('/log', log);
  }
}
