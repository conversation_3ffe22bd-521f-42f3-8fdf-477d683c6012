import { filter, switchMap, tap, refCount } from 'rxjs/operators';
import { Component, OnInit, ViewChild, OnDestroy } from '@angular/core';
import { PrefillServiceTypeComponent } from '../../../rmv-services/new-rmv-prefill/prefill-service-type/prefill-service-type.component';
import { Router, ActivatedRoute, NavigationEnd } from '@angular/router';
import { RmvService } from '../../app-services/rmv.service';
import { OverlayLoaderService } from '../../../shared/services/overlay-loader.service';
import { PrefillVehicleComponent } from '../../../rmv-services/new-rmv-prefill/prefill-vehicle/prefill-vehicle.component';
import { NgForm } from '@angular/forms';
import { PrefillOwnerComponent } from '../../../rmv-services/new-rmv-prefill/prefill-owner/prefill-owner.component';
import { PrefillPurchaseComponent } from '../../../rmv-services/new-rmv-prefill/prefill-purchase/prefill-purchase.component';
import { PrefillInsuranceComponent } from '../../../rmv-services/new-rmv-prefill/prefill-insurance/prefill-insurance.component';
import { AgencyUserService } from '../../../shared/services/agency-user.service';
import { FormData } from 'app/app-model/form';
import { ApiService } from '../../../shared/services/api.service';
import {
  Lessor,
  Lienholder,
  PrefillRmv,
  RmvServicesUserEnteredData,
  Vehicle,
} from 'app/app-model/PrefillRMV';
import { LookupsService } from '../../app-services/lookups.service';
import { RmvResponseModalComponent } from '../rta-prefill/rmv-response-modal/rmv-response-modal.component';
import { GetReadyUserEnteredData } from '../rta-prefill/get-ready.model';
import {
  RmvServicesSaveState,
  Indicator,
} from '../../../app-model/rmv-services-add.model';
import { FeatureService } from '../../../shared/services/feature.service';
import { SpecsService } from '../../app-services/specs.service';
import { fieldNames } from './mapFieldNames';
import { ToastrService } from 'ngx-toastr';
import { LeaveTransactionComponent } from './leave-transaction/leave-transaction.component';
import { AsideRegistrationResponseComponent } from 'app/dashboard/tpl-aside/components/aside-buttons-menu/aside-registration-response/aside-registration-response.component';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';

@Component({
    selector: 'app-new-rmv-prefill',
    templateUrl: './new-rmv-prefill.component.html',
    styleUrls: ['./new-rmv-prefill.component.scss'],
    standalone: false
})
export class NewRmvPrefillComponent implements OnInit, OnDestroy {
  showImage = true;
  saveAndClose: any;
  fromEvr: boolean;
  requireVinLookup = true;
  constructor(
    private agencyUserService: AgencyUserService,
    private apiService: ApiService,
    private lookupService: LookupsService,
    private rmvService: RmvService,
    private route: ActivatedRoute,
    private router: Router,
    private featureService: FeatureService,
    private specsService: SpecsService,
    private overlayLoaderService: OverlayLoaderService,
    private toastr: ToastrService,
    public dialogService: DialogService
  ) {
    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe((event: NavigationEnd) => { this.fromEvr = event.url === '/dashboard/rmv-services/saved-evr-lite'; });

  }

  // subscriptions
  vehicleBrandSubsription;
  rmvServiceDetailsSubscription;
  lessorSubscription: any;
  lienholderSubscription: any;
  featurServiceSubscription: any;

  createFormModel;
  activeFeatures;
  rmvPrefill: PrefillRmv;
  requiredItems = [];
  agencyId;
  agentId;
  messages;
  notifications;
  rmvLookupData;
  IsGetReadyEligible;
  IsEvrLiteEligible;
  evrLiteMessages;
  evrValidateData;
  rmvServicesRequestId;
  getReadyRequest;
  getReadyResponse;
  regData;
  transactionType;
  savedId;
  ownerFirstName = '';
  ownerLastName = '';
  data: RmvServicesSaveState;
  saveState: Partial<RmvServicesSaveState> = {};
  plateInquiryClicked = false;
  verifyAcknowledgment = { documents: false, payment: false, complete: false };
  submitText = 'Go To Get Ready';
  tradeInVehicle1;
  tradeInVehicle2;
  tradeInVehicleList;
  returnToMainDashboard = true;
  ref: DynamicDialogRef | undefined;

  @ViewChild('form') form: NgForm;
  @ViewChild('registrationSuccess') regModal;
  @ViewChild('serviceType') serviceType: PrefillServiceTypeComponent;
  @ViewChild('vehicleSelection') vehicleForm: PrefillVehicleComponent;
  @ViewChild('ownerForm') ownerForm: PrefillOwnerComponent;
  @ViewChild('purchaseForm') purchaseForm: PrefillPurchaseComponent;
  @ViewChild('insuranceForm') insuranceForm: PrefillInsuranceComponent;
  @ViewChild(RmvResponseModalComponent) rmvResponse: RmvResponseModalComponent;
  @ViewChild('rmvResponseModal') responseModal;
  @ViewChild('getReadyResponseModal') getReadyResponseModal;
  @ViewChild('getReadyErrorModal') getReadyErrorModal;
  @ViewChild('evrLiteErrorModal') evrLiteErrorModal;
  @ViewChild('evrLiteResponse') evrLiteModal;
  @ViewChild('requiredWarningModal') requiredModal;
  @ViewChild('inventoryNotFoundModal') inventoryNotFoundModal;
  @ViewChild('leaving') leavingModal;
  @ViewChild('leave') leave: LeaveTransactionComponent;

  ngOnInit(): void {
    this.subscribeToData();
  }

  private subscribeToData(): void {
    this.featurServiceSubscription = this.featureService.activeFeatures.subscribe(x => this.activeFeatures = x);
    this.agencyUserService.userData$.subscribe(x => {
      this.agencyId = x.agencyId;
      this.agentId = x.user.userId;
    });
    this.specsService.getMakesAsOptions().subscribe(x => {
      this.vehicleForm.makeOptions = x.slice().sort((a, b) => a.text.localeCompare(b.text));
      if (this.purchaseForm?.makeOptions) {
      this.purchaseForm.makeOptions = x.slice().sort((a, b) => a.text.localeCompare(b.text));
      }

    });
    this.route.queryParams.pipe(
      filter(p => !!p.id),
      tap(p => this.savedId = p.id),
      switchMap(p => this.rmvService.getRmvServiceDetails(p.id))
    ).subscribe(x => {
      this.data = x;
      this.serviceType.serviceType.plateReactivateIndicator = this.data.rmvServicesUserEnteredData.vehicles[0].plateReactivateIndicator;
      this.rmvServicesRequestId = this.savedId;
      this.insuranceForm.eStampInfo = this.rmvService.initializeInsurance(this.data.rmvServicesUserEnteredData.estampInfo ?? '');
      this.initializeForms();
      this.serviceType.serviceType.purchaseType = this.data.rmvServicesUserEnteredData.purchaseAndSalesInformation?.purchaseType ?? '';
      this.serviceType.addOwnershipType();
      this.serviceType.serviceType.ownership = this.data.ownership;
      this.serviceType.serviceType = {
        ownership: this.data.ownership ? this.data.ownership :  this.data.rmvServicesUserEnteredData.owners[0].lastName !== '' ? 'PersonallyOwned' : 'BusinessOwned' ,
        transactionType: this.data.transactionType,
        purchaseType: this.data.rmvServicesUserEnteredData.purchaseAndSalesInformation?.purchaseType ?? '',
        plateReactivateIndicator: this.data.rmvServicesUserEnteredData.vehicles[0].plateReactivateIndicator,
        reassignedPlate: this.data.rmvServicesUserEnteredData.vehicles[0].reassignedPlate,
      };
    });
  }



  ngOnDestroy() {
    this.vehicleBrandSubsription?.unsubscribe();
    this.rmvServiceDetailsSubscription?.unsubscribe();
    this.lessorSubscription?.unsubscribe();
    this.lienholderSubscription?.unsubscribe();
    this.featurServiceSubscription?.unsubscribe();
  }

  canDeactivate(rmvButtonClicked, redirect = false) {
    this.returnToMainDashboard = !rmvButtonClicked;

    if (!this.saveAndClose && !this.fromEvr && !this.renewOrReinstate() && this.serviceType.serviceType.transactionType !== 'DuplicateRegistration') {
      this.overlayLoaderService.hideLoader();
      this.leavingModal.open();
      return this.leave.isDeactivated;
    }
    if (redirect) { this.redirect(); }

    return true;
  }

  nonSaveTransaction() {
    return this.serviceType.serviceType.transactionType === 'PrefillReinstate';
  }

  checkForRequiredItems() {
    this.requiredItems = [];
    const poBoxRegex = /^(?!.*(?:POB|P\.O\.B|box)).*$/i;

    Object.keys(this.form.controls).forEach(x => {
      if (this.form.controls[x] && !this.form.controls[x].valid && !this.form.controls[x].disabled) {
        if (x !== undefined) {
          const mapped = fieldNames.get(x);
          this.requiredItems.push(mapped);
        }
      }
    });



    const checkOwnerLookup = (ownerIndex: number, ownerType: string) => {
      if (this.ownerForm?.owners[ownerIndex]?.firstName && this.serviceType.serviceType.ownership !== 'BusinessOwned') {
        if (!this.ownerForm.ownerValidation[ownerIndex].lookupDone) {
          this.requiredItems.push(`${ownerType} Lookup`);
        }
      }
    };

    const checkBusinessLookup = () => {
      if (this.ownerForm?.owners[0]?.fid && this.serviceType.serviceType.ownership === 'BusinessOwned' && this.ownerForm.isGetBusinessList && this.serviceType.serviceType.transactionType !== 'PrefillRegistrationTransfer' && this.serviceType.serviceType.transactionType !== 'PrefillReassign' && this.serviceType.serviceType.transactionType !== 'PrefillRenewRegistration') {
        if (!this.ownerForm.selectedBusiness) {
          this.requiredItems.push('FID Lookup');
        }
      }
    };

    const checkPlateLookup = () => {
      if ((this.serviceType.serviceType.transactionType === 'PrefillRegistrationTransfer' || this.serviceType.serviceType.transactionType === 'PrefillReassign' || this.serviceType.serviceType.transactionType === 'SurvivingSpouseNewTitleTransferPlate') && !this.vehicleForm.plateLookupDone) {
        this.requiredItems.push('Plate Lookup Required');
      }

      if (this.vehicleForm.evrEligible && !this.vehicleForm.assignedPlate && (this.serviceType.serviceType.transactionType === 'PrefillNewTitleAndRegistration' || this.serviceType.serviceType.transactionType === 'SurvivingSpouseNewTitleNewPlate')) {
        this.requiredItems.push('Assigned Plate Required');
      }

      if ((this.serviceType.serviceType.transactionType === 'PrefillRegistrationTransfer' || this.serviceType.serviceType.transactionType === 'PrefillRenewRegistration' || this.serviceType.serviceType.transactionType === 'PrefillReassign') && this.serviceType.serviceType.ownership !== 'BusinessOwned' && this.vehicleForm.checkRenewFeature()) {
        if (!this.vehicleForm.plateLookupDone) {
          this.requiredItems.push('Plate Lookup');
        }
      }
    };

    const checkInsurance = () => {
      if (this.insuranceForm && this.insuranceForm.isFieldRequired('writingCompany', this.insuranceForm.eStampInfo.writingCompanyName)) {
        this.requiredItems.push('Writing Company');
      }
      if (this.insuranceForm && this.insuranceForm.eStampInfo.writingCompanyName.includes('MAIP')) {
        if (!this.insuranceForm.assignedCarrier) {
          this.requiredItems.push('Assigned Carrier');
        }
      }
    };

    const checkGaragingAddress = () => {
      const index = this.requiredItems.indexOf('POB, P.O.B or Box in Street Address is invalid');
      if (poBoxRegex.test(this.ownerForm?.owners[0]?.garagingAddress?.addressStreet1)) {
        if (index !== -1) {
          this.requiredItems.splice(index, 1);
        }
      } else if (index === -1) {
        this.requiredItems.push('POB, P.O.B or Box in Street Address is invalid');
      }

      if (this.ownerForm?.owners[0]?.garagingAddress && this.ownerForm.owners[0].garagingAddress?.addressState !== '' && this.ownerForm?.owners[0].garagingAddress?.addressState !== 'MA') {
        this.requiredItems.push('Garaging address must be in MA');
      }

      if (this.ownerForm && this.ownerForm?.garagingAddress?.state !== '' && this.ownerForm?.garagingAddress?.state !== 'MA') {
        this.requiredItems.push('Garaging address must be in MA');

      }
    };
    this.ownerForm?.owners?.forEach((owner, index) => {
      if (owner.firstName && this.serviceType.serviceType.ownership !== 'BusinessOwned') {
        checkOwnerLookup(index, this.serviceType.serviceType.ownership);
      const addressType = [{type: 'residentialAddress', label: 'Residential'}, {type: 'mailAddress', label: 'Mailing'}];
        addressType.forEach(type => {
          if (owner[type.type].addressUnitType !== '' && (owner[type.type].addressUnit === '' || owner[type.type].addressUnit === undefined || owner[type.type].addressUnit === null)) {
            this.requiredItems.push(`${type.label} Unit is required`);
          }
          if ((owner[type.type].addressUnitType === '' || owner[type.type].addressUnitType === undefined || owner[type.type].addressUnitType === null) && owner[type.type].addressUnit !== '') {
            this.requiredItems.push(`${type.label} Unit Type is required`);
          }
          if (owner[type.type].addressZIP.length < 5 || (owner[type.type].addressZIP.length  > 5 && owner[type.type].addressZIP.length < 9)) {
            this.requiredItems.push(`${type.label} Zip is invalid`);
          }
        });
        if (this.ownerForm.garagingAddress.type === 'New' && this.ownerForm.garagingAddress.unitType !== '' && (this.ownerForm.garagingAddress.unitOrApt === '' || this.ownerForm.garagingAddress.unitOrApt === undefined || this.ownerForm.garagingAddress.unitOrApt === null)) {
          this.requiredItems.push('Garaging Unit is required');
        }
        if (this.ownerForm.garagingAddress.type === 'New' && (this.ownerForm.garagingAddress.unitType === '' || this.ownerForm.garagingAddress.unitType === undefined || this.ownerForm.garagingAddress.unitType === null) && this.ownerForm.garagingAddress.unitOrApt !== '') {
          this.requiredItems.push('Garaging Unit Type is required');
        }

      }
    });

    checkBusinessLookup();
    checkPlateLookup();
    checkInsurance();
    checkGaragingAddress();
    this.checkVINLookupRequired();
    return this.requiredItems.length > 0;
  }

  checkVINLookupRequired() {
    if ((this.serviceType.serviceType.transactionType === 'PrefillNewTitleAndRegistration' || this.serviceType.serviceType.transactionType === 'PrefillRegistrationTransfer' || this.serviceType.serviceType.transactionType.includes('SurvivingSpouseNewTitle') || this.serviceType.serviceType.transactionType === 'PrefillReassign') && this.requireVinLookup) {
      if (this.vehicleForm.vehicle.make === '' && this.vehicleForm.vehicle.model === '' && this.vehicleForm.vehicle.year === '') {
      this.requiredItems.push('VIN Lookup is required');
    }
  }
}

  checkForUnitAddress() {
    this.requiredItems = [];
    const addressType = [{type: 'residentialAddress', label: 'Residential'}, {type: 'mailAddress', label: 'Mailing'}, {type: 'garagingAddress', label: 'Garaging'}];
    this.vehicleForm.owners.forEach((owner, index) => {
      addressType.forEach(type => {
        if (owner[type.type]?.addressUnitType !== '' && owner[type.type]?.addressUnit === '') {
          this.requiredItems.push(`${type.label} Unit is required`);
        }
        if (owner[type.type]?.addressUnitType === '' && owner[type.type]?.addressUnit !== '') {
          this.requiredItems.push(`${type.label} Unit Type is required`);
        }
      });
    });
    return this.requiredItems.length > 0;
  }

  checkForValidZip() {
    this.requiredItems = [];
    const addressType = [{type: 'residentialAddress', label: 'Residential'}, {type: 'mailAddress', label: 'Mailing'}, {type: 'garagingAddress', label: 'Garaging'}];
    this.vehicleForm.owners.forEach((owner, index) => {
      addressType.forEach(type => {
        if (owner[type.type]?.addressZIP.length < 5 || (owner[type.type]?.addressZIP.length  > 5 && owner[type.type]?.addressZIP.length < 9)) {
          this.requiredItems.push(`${type.label} Zip is invalid`);
        }
      });
    });
    return this.requiredItems.length > 0;
  }


  private initializeForms(): void {
    this.initializeVehicleForm();
    this.initializeOwnerForm();
    this.initializePurchaseForm();
  }

  private initializeVehicleForm(): void {
    this.initializeVehicle();
    if (this.data.rmvServicesUserEnteredData.garagingAddress) {
      this.initializeGarage();
    }
    if (this.data.assignedPlate) {
      this.vehicleForm.assignedPlate = this.data.assignedPlate;
      this.vehicleForm.assignedPlateFound = true;
    }
  }

  private getEvrLitePermitStatus(): void {
    this.agencyUserService.getEvrLitePermitStatus().subscribe(x => {
       if (x.isEvrUser && x.isExpired) {
          this.IsEvrLiteEligible = false;
        }
    });

  }

  private initializeVehicle(): void {
    const vehicleData = this.data.rmvServicesUserEnteredData.vehicles[0];
    this.vehicleForm.vehicle = this.rmvService.initializeVehicle(vehicleData);
    if (this.vehicleForm.vehicle.vin && this.vehicleForm.vehicle.model) {
      this.vehicleForm.showDetails = true;
    }
    if (vehicleData.year && vehicleData.make && vehicleData.model) {
      this.requireVinLookup = false; }

    this.initializeSelectedBrandTypes();
    this.rmvService.checkEvrLiteEligibility(this.vehicleForm.vehicle.plateType).subscribe(x => { this.vehicleForm.evrEligible = x.isEvrFullEligible; });
  }

  private initializeSelectedBrandTypes(): void {
    const titleBrands = this.vehicleForm.vehicle.titleBrands || [];
    this.vehicleBrandSubsription = this.specsService
      .getVehicleBrandTypes()
      .subscribe((brandTypes) => {
        this.vehicleForm.selectedBrandTypes = titleBrands.map((v) =>
          brandTypes.items.find((x) => x.type === v.brandType)
        );
      });
  }

  private initializeGarage(): void {
    const garageData = this.data.rmvServicesUserEnteredData.garagingAddress;
    const garage = this.rmvService.initializeGarage(garageData);
    if (this.ownerForm) {
      this.ownerForm.garagingAddress = garage;
    } else {
      this.vehicleForm.garagingAddress = garage;
    }
  }

  private initializeOwnerForm(): void {
    const { owners, businessOwners, lessors, lienholders, indicators } = this.data.rmvServicesUserEnteredData;
    const { ownerForm, vehicleForm } = this;
    if (owners.length > 0 && ownerForm) {
      ownerForm.owners = this.serviceType.serviceType.transactionType !== 'PrefillRegistrationTransfer' &&
      this.serviceType.serviceType.transactionType !== 'SurvivingSpouseNewTitleTransferPlate' &&
      this.serviceType.serviceType.transactionType !== 'PrefillReassign' ? this.rmvService.initializeOwner(owners) : ownerForm.owners;
      ownerForm.owners.forEach((o, i) => {
        if (this.serviceType.serviceType.transactionType === 'PrefillRegistrationTransfer' ||
          this.serviceType.serviceType.transactionType === 'PrefillReassign' ||
          this.serviceType.serviceType.transactionType === 'SurvivingSpouseNewTitleTransferPlate') {
          if (o.firstName !== '') {
            ownerForm.ownerValidation[i].lookupDone = true;
            ownerForm.ownerValidation[i].isOwnerValidated = true;
            vehicleForm.plateLookupDone = true;
          }
        }

      });
      if (businessOwners.length > 0) { ownerForm.owners[0].fid = businessOwners[0].fid.toString(); }
    }

    if (lessors.length > 0) {
      const { fid } = lessors[0];
      ownerForm.lessorLookupData.fid = fid;
      this.lessorSubscription = this.lookupService.getLessors(fid, 'nameorFid').subscribe((x) => {

        const firstStageResults = x.items.filter(item => item.atlasEntityKey === lessors[0].atlasEntityKey);
        const secondStageResults = firstStageResults.filter(item => item.atlasEntityLocationKey === lessors[0].atlasEntityLocationKey);
        // Old lessors may not have atlasEntityKey and atlasEntityLocationKey, so we fallback to the first item
        if (secondStageResults.length > 0) {
          ownerForm.lessor = secondStageResults[0];
        } else if (firstStageResults.length > 0) {
          ownerForm.lessor = firstStageResults[0];
        } else {
          ownerForm.lessor = x.items[0];
        }
        ownerForm.lessorLookupData.fid = x.items[0]?.fid;
        ownerForm.lessorLookupData.name = x.items[0]?.name;
      });
    }

    if (lienholders.length > 0) {
      const { code, atlasEntityKey } = lienholders[0];
      ownerForm.lienholderLookupData.code = code;
      this.lienholderSubscription = this.lookupService.getLienholders(code, 'nameOrCode').subscribe((x) => {
        const lienholder = x.items.find(l => l.atlasEntityKey === atlasEntityKey);
        ownerForm.lienholder = lienholder;
        ownerForm.lienholderLookupData.code = lienholder?.code;
        ownerForm.lienholderLookupData.name = lienholder?.name;
      });
    }
    if (indicators.length > 0 && ownerForm) {
      ownerForm.isFinanced = indicators.find((x) => x.name === 'Financed')?.value ?? '';
      ownerForm.isLeased = indicators.find((x) => x.name === 'Leased')?.value ?? '';
    }
  }

  private initializePurchaseForm(): void {
    if (
      this.data.rmvServicesUserEnteredData.purchaseAndSalesInformation &&
      this.purchaseForm
    ) {
      this.purchaseForm.purchaseAndSalesInformation =
        this.rmvService.initializePurchaseAndSales(
          this.data.rmvServicesUserEnteredData.purchaseAndSalesInformation
        );
      if (
        this.data.rmvServicesUserEnteredData.purchaseAndSalesInformation
          .purchaseType === 'DEALER'
      ) {
        const tradeins = this.data.rmvServicesUserEnteredData.vehicles.filter(
          (x) => x.usage === 'tradein'
        );
        this.purchaseForm.tradeInCount = tradeins.length.toString();
        this.purchaseForm.showTradeInDetails = tradeins.length;
        switch (+this.purchaseForm.tradeInCount) {
          case 1:
            this.purchaseForm.tradeInVehicle1 = tradeins[0];
            break;
          case 2:
            this.purchaseForm.tradeInVehicle1 = tradeins[0];
            this.purchaseForm.tradeInVehicle2 = tradeins[1];
            break;
          default:
            break;
        }
      }
    }
  }

  public isFeatureEnabled(featureName): boolean {
    if (this.activeFeatures !== undefined && this.activeFeatures.length > 0) {
      const enabledFeatures = this.activeFeatures.filter(
        (ef) => ef.name === featureName
      );
      return (
        enabledFeatures !== undefined &&
        enabledFeatures != null &&
        enabledFeatures.length > 0
      );
    } else {
      return false;
    }
  }

  isGetReady(transactionType, value) {
    if (this.vehicleForm?.isLVNMPL && this.vehicleForm.vehicle.plateType !== 'LVNMPL' && this.vehicleForm.vehicle.plateType !== 'LVLUPL' && this.vehicleForm.vehicle.plateType !== 'LVVNTY' && this.vehicleForm.vehicle.plateType !== 'LVRSPL' ) {
      return false;
    }

    switch (transactionType) {
      case 'PrefillReinstateRegistration':
      case 'PrefillApplyForSalvageTitle':
      case 'PrefillTransferPlateBetweenTwoVehicles':
      case 'PrefillRegisterPreviouslyTitledVehicle':
      case 'PrefillTitlePreviouslyRegisteredVehicle':
      case 'PrefillTransferVehicleToSurvivingSpouse':
      case 'PrefillChangePlateOnExistingVehicle':
      case 'PrefillRenewRegistration':
      case 'PrefillAmendRegistration':
      case 'PrefillNonResidentShortTermRegistration':
        return false;
      default:
        return value;
    }

  }

  checkVehicleFormOwner() {
    const owners = [];
    return owners;
  }

  ownerNotValidated() {

    if (this.serviceType && (this.serviceType.serviceType.transactionType === 'PrefillRegistrationTransfer' || this.serviceType.serviceType.transactionType === 'SurvivingSpouseNewTitleTransferPlate'
      || this.serviceType.serviceType.transactionType === 'PrefillReassign')
      &&
      this.serviceType.serviceType.ownership !== 'BusinessOwned') {
      return !this.vehicleForm.plateLookupDone;
    }
    if (
      this.ownerForm &&
      this.serviceType.serviceType.ownership !== 'BusinessOwned'
    ) {
      if (this.ownerForm.owners[1].firstName !== '') {
        return (
          !this.ownerForm.ownerValidation[0].isOwnerValidated ||
          !this.ownerForm.ownerValidation[1].isOwnerValidated
        );
      }
      return !this.ownerForm.ownerValidation[0].isOwnerValidated;
    }


    return false;
  }

  saveIndicators() {
    const indicators: Indicator[] = [];
    if (this.ownerForm) {
      if (this.ownerForm.isLeased) {
        indicators.push({ value: this.ownerForm.isLeased, name: 'Leased' });
      }
      if (this.ownerForm.isFinanced) {
        indicators.push({ value: this.ownerForm.isFinanced, name: 'Financed' });
      }
    }
    return indicators;
  }

  saveData(workFlow = 'RmvServices', saveForLater = false, redirect = true, saveAndClose = false, url = '') {
    this.setOwnerFormAddressIndicator();
    this.saveAndClose = saveAndClose;
    const lienholder = this.setLienholder();
    const lessor = this.setBusiness('lessor');
    const businessOwner = this.setBusiness('businessOwned');

    this.vehicleForm.vehicle.reassignedPlate = this.serviceType.serviceType.reassignedPlate;

    if (this.purchaseForm?.purchaseAndSalesInformation) {
      this.purchaseForm.purchaseAndSalesInformation.purchaseType = this.serviceType.serviceType.purchaseType;
    }


    this.saveState = {
      ownerFirstName: this.getFirstName(),
      ownerLastName: this.getLastName(),
      agencyId: this.agencyId,
      workflowType: workFlow,
      assignedPlate: this.vehicleForm.assignedPlate,
      ownership: this.serviceType.serviceType.ownership,
      transactionType: this.serviceType.serviceType.transactionType,
      rmvServicesUserEnteredData: {
        owners: this.ownerForm?.owners || this.checkVehicleFormOwner(),
        indicators: this.saveIndicators(),
        garagingAddress: this.ownerForm?.garagingAddress || null,
        vehicles: [this.vehicleForm.vehicle],
        purchaseAndSalesInformation: this.purchaseForm?.purchaseAndSalesInformation || null,
        eStampInfo: this.insuranceForm?.eStampInfo,
        lessors: lessor,
        lienholders: lienholder,
        businessOwners: businessOwner,
      },
    };

    if (this.saveState.rmvServicesUserEnteredData.purchaseAndSalesInformation?.purchaseType === 'DEALER') {
      const tradeInCount = this.purchaseForm.tradeInCount;
      if (tradeInCount === '1') {
        this.saveState.rmvServicesUserEnteredData.vehicles.push(this.purchaseForm.tradeInVehicle1);
      } else if (tradeInCount === '2') {
        this.saveState.rmvServicesUserEnteredData.vehicles.push(this.purchaseForm.tradeInVehicle1, this.purchaseForm.tradeInVehicle2);
      }
    }

    if (!this.saveState.rmvServicesUserEnteredData.purchaseAndSalesInformation && this.serviceType.serviceType.purchaseType) {
      this.initializePurchaseAndSalesInfo();
    }

    if (workFlow === 'EvrLite' && !saveForLater) {
      this.evrSave();
    }

    if (saveForLater) {
      const savedId = this.savedId;
      const dataStatus = this.data?.status?.toLowerCase();
      if (savedId > 0 && dataStatus !== 'expired') {
        this.rmvService.updateRmvServiceDetails(savedId, this.saveState).subscribe((x) => redirect ? this.redirect(url) : this.saveWithoutRedirect(savedId));
      } else {
        this.rmvService.SaveRmvServiceTransaction(this.saveState).subscribe(
          (x) => workFlow !== 'EvrLite' && redirect ? this.redirect(url) : this.saveWithoutRedirect(x.rmvServicesRequestInfo.id),
          (err) => {
            this.overlayLoaderService.hideLoader();
            alert('There was a problem saving the transaction, please try again');
          }
        );
      }
    }
  }

   getFirstName = () => {
    if (this.serviceType.serviceType.transactionType === 'PrefillReinstateRegistration' || this.serviceType.serviceType.transactionType === 'PrefillRenewRegistration') {
      const businessName = this.regData.transaction.metaData.find(
        (x) => x.name === 'businessName')?.value ?? '';
      const firstName =
        this.regData.transaction.metaData.find(
          (x) => x.name === 'firstName')?.value ?? '';

      return firstName ?? businessName;
    } else if (this.vehicleForm.owners.length > 0 && this.vehicleForm.owners[0]?.firstName && this.serviceType.serviceType.ownership === 'BusinessOwned' || this.serviceType.serviceType.transactionType === 'DuplicateRegistration') {
      return this.vehicleForm.owners[0].firstName ?? '';
    } else {
      if (this.ownerForm?.selectedBusiness?.businessName) {
        return this.ownerForm.selectedBusiness.businessName;
      }
      return this.ownerForm?.owners[0]?.firstName ? this.ownerForm.owners[0].firstName : '';
    }
  }

   getLastName = () => {
    if (this.serviceType.serviceType.transactionType === 'PrefillReinstateRegistration' || this.serviceType.serviceType.transactionType === 'PrefillRenewRegistration') {
      return this.regData.transaction.metaData.find(
        (x) => x.name === 'lastName')?.value;
    }
    if (this.serviceType.serviceType.transactionType === 'DuplicateRegistration') {
      return this.vehicleForm.owners[0].lastName;
    } else {
      return this.ownerForm?.owners[0]?.lastName && this.serviceType.serviceType.ownership !== 'BusinessOwned' ? this.ownerForm.owners[0].lastName : '';
    }
  }


  initializePurchaseAndSalesInfo() {
    const purchaseAndSalesInformation = {
      purchaseDate: '',
      purchaseState: '',
      seller: null,
      taxExempt: '',
      taxExemptType: '',
      dealerFid: '',
      auctionSale: '',
      purchaseType: '',
      totalSalePrice: '',
      maResidentAtTimeOfPurchase: '',
      maSalesTaxPreviouslyPaid: '',
      nonMASalesTaxPreviouslyPaid: '',
      proofOfNoTaxRequired: ''
    };

    this.saveState.rmvServicesUserEnteredData.purchaseAndSalesInformation = purchaseAndSalesInformation;
    this.saveState.rmvServicesUserEnteredData.purchaseAndSalesInformation.purchaseType = this.serviceType.serviceType.purchaseType;
  }

  saveWithoutRedirect(id) {
    this.savedId = id;
    this.toastr.success('Saved!');

  }

  redirect(url = '') {
    url = this.returnToMainDashboard && !url ? '/dashboard' : url;
    url ? this.router.navigate([url]) :
      this.router.navigate(['/dashboard/rmv-services']);
  }

  setBusiness(type) {
    switch (type) {
      case 'lessor':
        return this.ownerForm?.lessorLookupData?.fid
          ? [{ id: 1,
               fid: this.ownerForm?.lessorLookupData.fid,
               atlasEntityKey: this.ownerForm?.lessor?.atlasEntityKey,
               atlasEntityLocationKey: this.ownerForm?.lessor?.atlasEntityLocationKey }]
          : [];
      case 'businessOwned':
        return this.serviceType.serviceType.ownership === 'BusinessOwned'
          ? [{ id: 1, fid: this.ownerForm?.owners[0].fid }]
          : [];
    }
  }

  setLienholder() {
    return this.ownerForm?.lienholderLookupData?.code
      ? [{ id: 1, code: this.ownerForm.lienholderLookupData.code, atlasEntityKey: this.ownerForm.lienholder.atlasEntityKey }]
      : [];
  }

  setOwnerName() {
    if (
      this.saveState.rmvServicesUserEnteredData.owners.length > 0 &&
      this.saveState.ownership === 'BusinessOwned'
    ) {
      this.saveState.rmvServicesUserEnteredData.owners[0].firstName =
        this.ownerFirstName;
    }
    if (
      this.saveState.rmvServicesUserEnteredData.owners.length > 0 &&
      this.saveState.rmvServicesUserEnteredData.owners[0].firstName === ''
    ) {
      this.saveState.rmvServicesUserEnteredData.owners[0].firstName =
        this.ownerFirstName;
      this.saveState.rmvServicesUserEnteredData.owners[1].lastName =
        this.ownerLastName;
    }
  }

  setOwnerFormAddressIndicator() {
    if (this.ownerForm) {
      if (
        (this.ownerForm.ownerValidation &&
          this.ownerForm.ownerValidation[0].editMailingAddress) ||
        this.ownerForm.ownerValidation[0].editResidentialAddress
      ) {
        this.ownerForm.owners[0].addressChangeIndicator = 'Y';
      }
      if (
        (this.ownerForm.ownerValidation &&
          this.ownerForm.ownerValidation[1].editMailingAddress) ||
        this.ownerForm.ownerValidation[1].editResidentialAddress
      ) {
        this.ownerForm.owners[1].addressChangeIndicator = 'Y';
      }
    }
  }

  evrSave() {
    this.saveState.validateDetails = this.evrValidateData.responseDetail;
    this.saveState.atlasValidatedTransactionKey =
      this.evrValidateData.atlasValidatedTransactionKey;
    this.saveState.atlasRegistrationKey =
      this.evrValidateData.atlasRegistrationKey;
    this.saveState.atlasTransactionKey =
      this.evrValidateData.atlasTransactionKey;
    this.saveState.validateDetails.requiredDocuments =
      this.evrValidateData.responseDetail.requiredEvrLiteDocuments;
    if (this.saveState.rmvServicesUserEnteredData.owners.length === 0) {
      this.saveState.rmvServicesUserEnteredData.owners.push({
        firstName: this.ownerFirstName,
        lastName: this.ownerLastName,
        id: 1,
        dateOfBirth: '',
        license: '',
        licenseState: '',
      });
    }
    this.setOwnerName();

    this.rmvService.SaveRmvServiceTransaction(this.saveState).subscribe(
      (x) => {
        this.rmvServicesRequestId = x.rmvServicesRequestInfo.id;
        this.saveAndClose = true;
        this.evrLiteModal.open();
        this.responseModal.close();
      },
      (err) => {
        alert('There was a problem saving the transaction, please try again');
        this.overlayLoaderService.hideLoader();
      }
    );
  }

  splitWords(word) {
    return word
      ? word
        .replace(/([A-Z])/g, ' $1')
        .replace('Prefill', '')
        .trim()
      : '';
  }


  submit() {
    if (this.checkForRequiredItems()) {
      this.requiredModal.open();
    } else if (this.vehicleForm.evrEligible && !this.vehicleForm.assignedPlate && !this.vehicleForm.InventoryFound && this.serviceType.serviceType.transactionType === 'PrefillNewTitleAndRegistration') {
      this.inventoryNotFoundModal.open();
 } else {
      // Copy owners from ownerselection, if owner 2 does not have a name, pop it from array to avoid bad data passed to api.
      // Copy so it will not remove text field from form
      let owners = this.createOwners();
      // setup vehicle object needed for prefillRMVForm Endpoint
      const vehicles: Vehicle[] = this.createVehicles();
      if (this.serviceType.serviceType.ownership === 'BusinessOwned' && this.ownerForm.owners[0].fid) {
        this.ownerForm.owners[0].fid = this.ownerForm.owners[0].fid.replace(
          /[_-]/g,
          ''
        );
      }

      const setBusiness = () => {
        return this.ownerForm?.owners[0]?.fid ? [{ id: 1, fid: this.ownerForm.owners[0]?.fid }] : [];
      };

      const lessors: Lessor[] =
        (this.serviceType.serviceType.ownership.includes('Leased') &&
          this.serviceType.serviceType.transactionType !==
          'PrefillRegistrationTransfer' && this.serviceType.serviceType.transactionType !== 'PrefillReassign') ||
          (this.ownerForm != null && this.ownerForm.isLeased === 'Yes')
          ? [{ id: 1,
              fid: this.ownerForm.lessorLookupData.fid,
              atlasEntityKey: this.ownerForm.lessor?.atlasEntityKey,
              atlasEntityLocationKey: this.ownerForm.lessor?.atlasEntityLocationKey}]
          : [];
      const lienholders: Lienholder[] =
        this.serviceType.serviceType.ownership === 'Financed' ||
          (this.ownerForm != null && this.ownerForm.isFinanced === 'Yes')
          ? [{ id: 1, code: this.ownerForm.lienholderLookupData.code,
            city: this.ownerForm.lienholder.city, name: this.ownerForm.lienholder.name }]
          : [];
      const businessowners: any =
        this.serviceType.serviceType.ownership === 'BusinessOwned'
          ? this.ownerForm?.owners[0].fid
            ? [{ id: 1, fid: this.ownerForm.owners[0].fid }]
            : []
          : [];
        const transactionType = this.getTransactionType();
      // RMV endpoint model
      this.rmvPrefill = {
        transactionType: transactionType,
        vehicles: vehicles,
        ownership: this.serviceType.serviceType.ownership,
        drivers: owners,
        businessowners,
        lessors,
        lienholders,
        indicators: this.saveIndicators(),
      };
      this.overlayLoaderService.showLoader();
      this.saveData('RmvServices', true, false, false);
      this.lookupService.prefillRMVForm(this.rmvPrefill).subscribe(
        (x) => {
          this.messages = x.messages;
          this.ownerFirstName = x.ownerFirstName;
          this.ownerLastName = x.ownerLastName;
          owners = this.createOwners();

        const condition =
          this.vehicleForm.vehicle.condition !== ''
            ? this.vehicleForm.vehicle.condition
            : '';
        if (this.vehicleForm.vehicle.condition === '') {
          this.vehicleForm.vehicle.condition = condition;
        }
        this.evrLiteMessages = x.eligibilityMessages;
        this.IsEvrLiteEligible = x.isEvrFullEligible ? this.EVR639(x.isEvrFullEligible) : this.EVR639(x.isEvrLiteEligible);
        this.getEvrLitePermitStatus();
        this.rmvLookupData = x.rmvLookupData;
        this.overlayLoaderService.hideLoader();
        this.responseModal.open();
        this.IsGetReadyEligible = this.isGetReady(
          this.serviceType.serviceType.transactionType,
          x.isGetReadyEligible
        );

        if (this.IsGetReadyEligible) { this.notifications = x.notifications; }
        // CreateForm Endpoint model
        this.createFormModel = this.createModel(owners, vehicles);
        // Go to RTA model

          const plate = this.vehicleForm.vehicle.plateNumber.trim();
          const platetype = this.vehicleForm.vehicle.plateType;
          this.vehicleForm.vehicle.vin = this.vehicleForm.vehicle.vin.trim();
          const vehicle1 = { ...this.vehicleForm.vehicle };

          vehicle1.ownership = this.serviceType.serviceType.ownership;
          vehicle1.plateReactivateIndicator =
            this.serviceType.serviceType.plateReactivateIndicator;
          if (this.vehicleForm.assignedPlate) {
            vehicle1.newAssignedPlateNumber = this.vehicleForm.assignedPlate;
            vehicle1.newPlateType = this.vehicleForm.vehicle.plateType;
            vehicle1.plateAssignedIndicator = 'Y';
          }
          const vehiclesGetReady = [vehicle1];

          if (
            this.serviceType.serviceType.reassignedPlate === 'Y' ||
            this.serviceType.serviceType.transactionType ===
            'PrefillRegistrationTransfer' ||
            this.serviceType.serviceType.transactionType ===
            'PrefillReassign' ||
            this.serviceType.serviceType.transactionType === 'SurvivingSpouseNewTitleTransferPlate'
          ) {
            vehiclesGetReady[0].plateNumber = '';
            vehiclesGetReady[0].plateType = '';
            const veh = {
              id: 2,
              lookupType: '',
              usage: 'secondary',
              ownership: '',
              condition: '',
              primaryColor: '',
              transmission: '',
              passengers: '',
              outOfStateTitleNumber: '',
              titleIssueDate: '',
              titleState: '',
              plateNumber: plate,
              vin: '',
              plateType: platetype,
              odometer: '',
              registrationReason: '',
              reassignedPlate: '',
              registrationType: '',
              plateReactivateIndicator: '',
            };
            vehiclesGetReady.push(veh);
          }

          if (this.serviceType.serviceType.purchaseType === 'DEALER') {
            if (this.purchaseForm.tradeInCount === '1') {
              vehiclesGetReady.push(this.purchaseForm.tradeInVehicle1);
            } else if (this.purchaseForm.tradeInCount === '2') {
              vehiclesGetReady.push(this.purchaseForm.tradeInVehicle1);
              vehiclesGetReady.push(this.purchaseForm.tradeInVehicle2);
            }
          }

          if (
            this.purchaseForm?.purchaseAndSalesInformation?.dealerFid !== null
          ) {
            this.purchaseForm.purchaseAndSalesInformation.dealerFid =
              this.purchaseForm.purchaseAndSalesInformation.dealerFid
                .toString()
                .replace(/[_-]/g, '');
          }

          if (this.purchaseForm?.purchaseAndSalesInformation?.taxExempt === 'Y') {
            if (
              this.purchaseForm.purchaseAndSalesInformation.taxExemptType !==
              'S' &&
              this.purchaseForm.purchaseAndSalesInformation.taxExemptType !== 'O'
            ) {
              this.purchaseForm.purchaseAndSalesInformation.totalSalePrice = '';
              this.purchaseForm.purchaseAndSalesInformation.nonMASalesTaxPreviouslyPaid =
                '';
            }
          }

          const getReadyUserEnteredData: GetReadyUserEnteredData = {
            owners: owners,
            vehicles: vehiclesGetReady,
            garagingAddress: this.ownerForm?.garagingAddress,
            purchaseAndSalesInformation:
              this.purchaseForm?.purchaseAndSalesInformation,
            eStampInfo: this.getEstampInfo(),
            businessowners,
            indicators: this.saveIndicators(),
            lessors,
            lienholders,
          };


          this.getReadyRequest = {
            transactionType:
            (this.serviceType.serviceType.transactionType ===
              'PrefillNewTitleAndRegistration' ||
              this.serviceType.serviceType.transactionType ===
              'PrefillRegistrationTransfer') &&
              this.serviceType.serviceType.reassignedPlate === 'Y'
              ? 'Reassign'
              : transactionType.replace(
                'Prefill',
                ''
              ),
            ownership: this.serviceType.serviceType.ownership || 'PersonallyOwned',
            agencyId: this.agencyId,
            creationUserId: this.agentId,
            rmvLookupData: this.rmvLookupData,
            businessOwnerLookupData:
              this.serviceType.serviceType.ownership === 'BusinessOwned'
                ? businessowners
                : '',
            lienholderLookupData: this.getLienHolder(),
            lessorLookupData: this.getLessor(),
            rmvServicesUserEnteredData: getReadyUserEnteredData,
          };
        },
        (err) => {
          alert('There was a problem, please try again');
          this.overlayLoaderService.hideLoader();
        }
      );
    }
  }

  getTransactionType() {

switch (this.serviceType.serviceType.transactionType) {
  case 'SurvivingSpouseNewTitleSamePlate':
  case 'SurvivingSpouseNewTitleNewPlate':
    return 'PrefillSSNewTitleAndRegistration';
  case 'SurvivingSpouseNewTitleTransferPlate':
    return 'PrefillSSNewTitleAndRegistrationTransfer';
  default:
    return  this.serviceType.serviceType.transactionType;

}
  }

  getEstampInfo() {
    const { assignedCarrier, eStampInfo: { companyCode, writingCompanyName, signedBy, effectiveDate, dateType, policyChangeDate, agencyName } } = this.insuranceForm;
    let newCompanyCode, newWritingCompanyName = '';
    if (this.insuranceForm.isMaip) {
      newCompanyCode = assignedCarrier;
      newWritingCompanyName = this.insuranceForm.getCompanyName();
    } else {
      newCompanyCode = companyCode;
      newWritingCompanyName = writingCompanyName;
    }

    const newValues = {
      companyCode: newCompanyCode,
      writingCompanyName: newWritingCompanyName,
      signedBy,
      effectiveDate,
      dateType,
      policyChangeDate,
      agencyName
    };
    return newValues;
  }

  EVR639(isEvrLiteEligible: any) {
    this.showImage = true;
    let returnValue = isEvrLiteEligible;
    if (isEvrLiteEligible) {
      if (this.vehicleForm.vehicle.titleType && this.vehicleForm.vehicle.titleType !== 'STD') {
        this.evrLiteMessages = [{ message: 'Titles that are not Clear or have Brands are not eligible for EVR at this time.  However, you can still process this transaction through GetReady.  GetReady produces a QR Coded RTA that ensures accuracy and greatly reduces wait time at RMV branches.' }];
        returnValue = false;
        this.showImage = false;
        this.submitText = 'Go to Get Ready';
      }

      let isForeignState = false;
      this.ownerForm.owners.forEach((owner, i) => {
        if (owner.licenseState === 'FR') {
           isForeignState = true;
        }
      });
      if (isForeignState) {
          returnValue = false;
          this.evrLiteMessages = [{ message: 'Eligible for GetReady'}];
          this.submitText = 'Go to Get Ready';
          this.showImage = false;
      }

      if (this.vehicleForm.vehicle.titleBrands && this.vehicleForm.vehicle.titleBrands.length > 0) {
        const brandList = this.vehicleForm.brandTypes.filter(x => x.type !== 'ODAC' && x.type !== 'ODDS' && x.type !== 'ODAL' && x.type !== 'ODRP');
        const vBrands = [];
        this.vehicleForm.vehicle.titleBrands.map(value => vBrands.push(value.brandType));
        brandList.forEach(brand => {
          if (vBrands.includes(brand.type)) {
            returnValue = false;
            this.evrLiteMessages = [{ message: 'Titles that are not Clear or have Brands are not eligible for EVR at this time.  However, you can still process this transaction through GetReady.  GetReady produces a QR Coded RTA that ensures accuracy and greatly reduces wait time at RMV branches.' }];
            this.submitText = 'Go to Get Ready';
            this.showImage = false;
          }
        });
      }
    }
    return returnValue;
  }

  createOwners() {
    if (this.ownerForm?.owners[0]?.firstName === '') {
      this.ownerForm.owners[0].firstName = this.ownerFirstName;
      this.ownerForm.owners[0].lastName = this.ownerLastName;
    }


    let owners = this.ownerForm ? [...this.ownerForm.owners] : [];
    if (this.ownerForm) {
      if (this.ownerForm.owners[0].license && this.ownerForm.owners[0].license !== '') {
        this.ownerForm.owners[0].license = this.ownerForm.owners[0].license.trim();
      }
      if (this.ownerForm.owners[1].license && this.ownerForm.owners[1].license !== '') {
        this.ownerForm.owners[1].license = this.ownerForm.owners[1].license.trim();
      }
      if (
        (this.ownerForm.ownerValidation &&
          this.ownerForm.ownerValidation[0].editMailingAddress) ||
        this.ownerForm.ownerValidation[0].editResidentialAddress
      ) {
        owners[0].addressChangeIndicator = 'Y';
      } else {
        owners[0].addressChangeIndicator = 'N';
      }
      if (
        (this.ownerForm.ownerValidation &&
          this.ownerForm.ownerValidation[1].editMailingAddress) ||
        this.ownerForm.ownerValidation[1].editResidentialAddress
      ) {
        owners[1].addressChangeIndicator = 'Y';
      } else {
        owners[1].addressChangeIndicator = 'N';
      }
    }
    if (this.ownerForm) {
      if (this.serviceType.serviceType.ownership === 'BusinessOwned' && this.ownerForm.owners[0].fid) {
        this.ownerForm.owners[0].fid = this.ownerForm.owners[0].fid.replace(
          /[_-]/g,
          ''
        );
        if (this.ownerForm.selectedBusiness?.locationAddress) {
          const { addressCity, addressState, addressStreet1, addressStreet2, addressUnit, addressUnitType, addressZIP, addressCountry } = this.ownerForm.selectedBusiness.locationAddress;
          owners = [...this.ownerForm.owners];
          owners[0].firstName = this.ownerForm.selectedBusiness.businessName;
          owners[0].mailAddress.addressCity = addressCity;
          owners[0].mailAddress.addressCountry = addressCountry;
          owners[0].mailAddress.addressState = addressState;
          owners[0].mailAddress.addressStreet1 = addressStreet1;
          owners[0].mailAddress.addressStreet2 = addressStreet2;
          owners[0].mailAddress.addressUnit = addressUnit;
          owners[0].mailAddress.addressUnitType = addressUnitType;
          owners[0].mailAddress.addressZIP = addressZIP;
        }



      }

      if (
        this.serviceType.serviceType.ownership.includes('Leased') &&
        this.serviceType.serviceType.transactionType !==
        'PrefillRegistrationTransfer' && this.serviceType.serviceType.transactionType !== 'PrefillReassign'
      ) {
        owners[0].fid = this.ownerForm.lessor.fid;
      }
      if (this.ownerForm && this.ownerForm.owners[1].firstName === '') {
        owners.pop();
      }
    }
    return owners;
  }

  createVehicles() {
    const lookupType = this.vehicleForm.vehicle.vin ? 'vin' : 'plate';

    const vehicles: Vehicle[] = [
      {
        id: 1,
        lookupType: lookupType,
        vin: this.vehicleForm.vehicle.vin.trim(),
        plateType: this.vehicleForm.vehicle.plateType,
        plateNumber: this.vehicleForm.vehicle.plateNumber.trim(),
      },
    ];
    if (
      this.serviceType.serviceType.reassignedPlate === 'Y' ||
      this.serviceType.serviceType.transactionType ===
      'PrefillRegistrationTransfer' || this.serviceType.serviceType.transactionType ===
      'PrefillReassign' ||
      this.serviceType.serviceType.transactionType === 'SurvivingSpouseNewTitleTransferPlate'
    ) {
      const veh: Vehicle = {
        id: 2,
        vin: '',
        lookupType: lookupType,
        plateType: this.vehicleForm.vehicle.plateType,
        plateNumber: this.vehicleForm.vehicle.plateNumber.trim(),
        usage: 'secondary',
      };
      vehicles[0].plateNumber = '';
      vehicles[0].plateType = '';
      vehicles.push(veh);
    }
    return vehicles;
  }

  submitToRmv() {
    if (this.evrValidateData?.resultStatus?.message[0]?.includes('exceed the maximum')) {

      this.getReadyRequest.rmvServicesUserEnteredData.vehicles[0].plateAssignedIndicator = 'N';
      this.getReadyRequest.rmvServicesUserEnteredData.vehicles[0].newPlateType = '';
      this.getReadyRequest.rmvServicesUserEnteredData.vehicles[0].newAssignedPlateNumber = '';

      this.vehicleForm.assignedPlate = '';
      this.saveData('RmvServices', true, false, false);


    }
    this.overlayLoaderService.showLoader();
    this.rmvService.getReadySubmit(this.getReadyRequest).subscribe(
      (x) => {
        this.overlayLoaderService.hideLoader();
        this.getReadyResponse = x;
        this.responseModal.close();
        this.getReadyResponse.resultStatus.status === 'Success'
          ? this.getReadyResponseModal.open()
          : this.getReadyErrorModal.open();
      },
      (err) => {
        alert('There was a problem, please try again');
        this.overlayLoaderService.hideLoader();
      }
    );
  }

  createModel(owners, vehicles) {
    let lessors: Lessor[] = [];
        const transactionType = this.getTransactionType();
    if (
      this.serviceType.serviceType.ownership.includes('Leased') &&
      this.serviceType.serviceType.transactionType !==
      'PrefillRegistrationTransfer' && this.serviceType.serviceType.transactionType !==
      'PrefillReassign'
    ) {
      lessors = [{
        id: 1,
        fid: this.ownerForm.lessorLookupData.fid,
        atlasEntityKey: this.ownerForm.lessor?.atlasEntityKey,
        atlasEntityLocationKey: this.ownerForm.lessor?.atlasEntityLocationKey}];
    }

    if (this.ownerForm != null && this.ownerForm.isLeased === 'Yes') {
      lessors = [{
        id: 1,
        fid: this.ownerForm.lessorLookupData.fid,
        atlasEntityKey: this.ownerForm.lessor?.atlasEntityKey,
        atlasEntityLocationKey: this.ownerForm.lessor?.atlasEntityLocationKey}];
    }
    const lienholders: Lienholder[] =
      this.serviceType.serviceType.ownership === 'Financed' ||
        (this.ownerForm != null && this.ownerForm.isFinanced === 'Yes')
        ? [{ id: 1, code: this.ownerForm.lienholderLookupData.code }]
        : [];
    const businessOwners: any =
      this.serviceType.serviceType.ownership === 'BusinessOwned'
        ? [{ id: 1, fid: this.ownerForm?.owners[0].fid }]
        : [];

    this.vehicleForm.vehicle.condition =
      this.vehicleForm.vehicle.condition !== '' ||
        this.vehicleForm.vehicle.condition !== null
        ? this.vehicleForm.vehicle.condition
        : '';
    const vehicle1 = { ...this.vehicleForm.vehicle };
    const vehiclesGetReady = [vehicle1];

    if (this.purchaseForm) {
      this.tradeInVehicle1 = this.purchaseForm.tradeInVehicle1;
      this.tradeInVehicle2 = this.purchaseForm.tradeInVehicle2;
      this.tradeInVehicleList = [this.tradeInVehicle1, this.tradeInVehicle2];
    }

    // refactor where p&s data came from
    if (this.purchaseForm?.purchaseAndSalesInformation) {
      this.purchaseForm.purchaseAndSalesInformation.purchaseType =
        this.serviceType.serviceType.purchaseType ?? '';
    }
    const rmvServiceUserEnteredData: RmvServicesUserEnteredData = {
      owners:
        this.ownerForm?.owners ? this.ownerForm.owners : [],
      vehicles: vehiclesGetReady,
      tradeInVehicles: this.tradeInVehicleList === undefined ? [] : this.tradeInVehicleList,
      garagingAddress: this.ownerForm?.garagingAddress,
      purchaseAndSalesInformation:
        this.purchaseForm?.purchaseAndSalesInformation
          ? this.purchaseForm.purchaseAndSalesInformation
          : null,
      eStampInfo:
        this.insuranceForm?.eStampInfo
          ? this.getEstampInfo()
          : null,
      businessowners:
        this.serviceType.serviceType.ownership === 'BusinessOwned'
          ? businessOwners
          : [],
      lessors: lessors,
      lienholders: lienholders,
    };
    return {
      transactionType: transactionType,
      ownership: this.serviceType.serviceType.ownership,
      agencyId: this.agencyId,
      creationUserId: this.agentId,
      number: 'RTA',
      clientId: '',
      rmvLookupData: this.rmvLookupData,
      businessOwnerLookupData:
        this.serviceType.serviceType.ownership === 'BusinessOwned'
          ? businessOwners
          : '',
      lienholderLookupData: this.getLienHolder(),
      lessorLookupData: this.getLessor(),
      rmvServicesUserEnteredData: rmvServiceUserEnteredData,
    };
  }

  getLienHolder() {
    if (
      this.serviceType.serviceType.ownership === 'Financed' ||
      (this.ownerForm != null && this.ownerForm.isFinanced === 'Yes')
    ) {
      return [this.ownerForm.lienholder];
    } else {
      return '';
    }
  }

  getLessor() {
    if (
      this.serviceType.serviceType.ownership.includes('Leased') ||
      (this.ownerForm != null && this.ownerForm.isLeased === 'Yes')
    ) {
      return [this.ownerForm.lessor];
    } else {
      return '';
    }
  }

  skipToForm() {
    const formData: FormData = new FormData(
      this.agentId,
      this.agencyId,
      'RTA',
      '',
      0,
      '',
      this.ownerForm?.owners[0]?.firstName
        ? this.ownerForm.owners[0].firstName
        : '',
      this.ownerForm?.owners[0]?.lastName
        ? this.ownerForm.owners[0].lastName
        : ''
    );
    let formWindow: Window;
    const baseUrl =
      document.location.protocol +
      '//' +
      document.location.hostname +
      ':' +
      document.location.port +
      '/' +
      document.location.pathname;
    const loadingPage = baseUrl + '/assets/html-templates/loading.html';
    formWindow = window.open(loadingPage, '_blank');

    const owners = this.createOwners();
    const vehicles = this.createVehicles();
    const data = this.createModel(owners, vehicles);
    if (data.transactionType === 'PrefillSSNewTitleAndRegistrationTransfer') {
      data.transactionType = 'PrefillSSNewTitleAndRegistration';
    }
    data.rmvServicesUserEnteredData.indicators = this.saveIndicators();
    const dataWithQuoteId = { ...data, quoteId: this.data?.quoteId ? this.data?.quoteId : 0 };
    return new Promise((res, rej) => {
      this.rmvService.RMVForm(dataWithQuoteId).subscribe((x) => {
        const url = `${data.agencyId}/openform?agencyformid=${x.agencyFormId
          }&agentId=${x.creationUserId}&ticket=${encodeURIComponent(
            x.authorizationToken.ticket
          )}&requestId=${x.authorizationToken.requestId}`;
        const formUrl = this.apiService.formAppUrl(url);
        formWindow.location.href = formUrl;
      });
    });
  }

  renewOrReinstate() {
    if (this.serviceType?.serviceType) {
      return (
        this.serviceType.serviceType.transactionType ===
        'PrefillRenewRegistration' ||
        this.serviceType.serviceType.transactionType ===
        'PrefillReinstateRegistration'
      );
    }
  }

  isPlateInitiatedNeeded() {
    if (this.serviceType.serviceType.transactionType === 'PrefillRegistrationTransfer' ||
      this.serviceType.serviceType.transactionType === 'PrefillReassign') {
      return !this.plateInquiryClicked;
    }
    return false;
  }

  registrationEasy() {
    if (this.checkForRequiredItems()) {
      this.requiredModal.open();
    } else {
      this.overlayLoaderService.showLoader();
      this.transactionType =
        this.serviceType.serviceType.transactionType.includes('Reinstate')
          ? 'RegistrationReinstatement'
          : 'RegistrationRenewal';
      const validation = {
        transaction: {
          type: this.serviceType.serviceType.transactionType.includes('Renew')
            ? 'RegistrationRenewalDataValidationEasy'
            : 'RegistrationReinstatementDataValidationEasy',
        },
        vehicle: {
          plateNumber: this.vehicleForm.vehicle.plateNumber.trim(),
          plateType: this.vehicleForm.vehicle.plateType,
        },
      };
      if (this.vehicleForm.checkRenewFeature()) {
        if (this.vehicleForm.vehicle.newNumberOfSeats !== this.vehicleForm.vehicle.numberOfSeats) {
          validation.vehicle['newNumberOfSeats'] = this.vehicleForm.vehicle.newNumberOfSeats;
        }
        if (this.vehicleForm.vehicle.newRegisteredWeight !== this.vehicleForm.vehicle.registeredWeight) {
          validation.vehicle['newRegisteredWeight'] = this.vehicleForm.vehicle.newRegisteredWeight;
        }
      }
      this.rmvService.Validation(validation).subscribe((x) => {
        this.overlayLoaderService.hideLoader();
        this.regData = x;
        this.ref = this.dialogService.open(AsideRegistrationResponseComponent, {
          width: '60%',
          data: {
            plateNumber: this.vehicleForm.vehicle.plateNumber.trim(),
            regData: this.regData,
            transaction: this.transactionType,
            owners: this.vehicleForm?.owners,
            ownerValidation: this.getOwnerValidation(),
            fromRmvs: true,
            vehicle: this.vehicleForm?.vehicle,
            originalVehicle: this.vehicleForm.transferVehicleResponse.vehicle

          }
        });
        // this.saveData('RmvServices', true, false, false,'')

      });
    }
  }

  duplicateReg() {
    if (this.checkForUnitAddress() || this.checkForValidZip()) {
      this.requiredModal.open();
    } else {
      this.overlayLoaderService.showLoader();
      this.transactionType = 'DuplicateRegistration';
      const validation = {
        transaction: {
          type: 'DuplicateRegistrationDataValidation'
        },
        vehicle: {
          plateNumber: this.vehicleForm.vehicle.plateNumber.trim(),
          plateType: this.vehicleForm.vehicle.plateType,
        },
      };
      this.saveData('RmvServices', true, false, false, '');
      this.rmvService.Validation(validation).subscribe((x) => {
        this.overlayLoaderService.hideLoader();
        this.regData = x;
        this.ref = this.dialogService.open(AsideRegistrationResponseComponent, {
          width: '60%',
          data: {
            plateNumber: this.vehicleForm.vehicle.plateNumber.trim(),
            regData: this.regData,
            transaction: this.transactionType,
            owners: this.vehicleForm.owners,
            ownerValidation: this.vehicleForm.ownerValidation,
            fromRmvs: true,
            fromEstamp: false,
            vehicle: this.vehicleForm.vehicle,
            originalVehicle: this.vehicleForm.transferVehicleResponse.vehicle
          }
        });

      });
    }
  }

  initiateEvrLite() {
    this.overlayLoaderService.showLoader();
    this.rmvService.evrValidate(this.getReadyRequest).subscribe(
      (x) => {
        this.overlayLoaderService.hideLoader();
        this.evrValidateData = x;
        if (this.evrValidateData.resultStatus.status === 'Success') {
          this.saveData('EvrLite');
          this.evrLiteModal.open();
        } else {
          this.evrLiteErrorModal.open();
        }

        this.responseModal.close();
      },
      (err) => this.overlayLoaderService.hideLoader()
    );
  }

  isReportPrinted(event) {
    this.plateInquiryClicked = event;
  }

  refreshToken() {
    this.rmvService.refreshToken().subscribe();
  }

  changeOwnerIfTransfer(event) {
    if (
      this.serviceType.serviceType.transactionType ===
      'PrefillRegistrationTransfer' || this.serviceType.serviceType.transactionType ===
      'PrefillReassign' || this.serviceType.serviceType.transactionType === 'DuplicateRegistration' || this.serviceType.serviceType.transactionType === 'SurvivingSpouseNewTitleTransferPlate' || this.vehicleForm.checkRenewFeature()
    ) {
      if (this.vehicleForm.owners && this.vehicleForm.owners.length > 0) {
        this.vehicleForm.owners.sort((a, b) => a.index > b.index ? 1 : -1);
        this.ownerForm.owners = this.ownerForm.initOwners();
        this.vehicleForm.owners.forEach((owner) => {
          this.ownerForm.owners[owner.index] = this.vehicleForm.owners[owner.index];
          if (owner.licenseState === '') {
            this.ownerForm.owners[owner.index].licenseState = 'MA';
          }
          this.ownerForm.owners[owner.index].license =
            this.vehicleForm.owners[owner.index].licenseNumber;
          this.ownerForm.owners[owner.index].phone = this.ownerForm.owners[owner.index]
            .phone
            ? this.ownerForm.owners[owner.index].phone
            : '';
          this.ownerForm.owners[owner.index].email = this.ownerForm.owners[owner.index]
            .email
            ? this.ownerForm.owners[owner.index].email
            : '';
          this.ownerForm.owners[owner.index].phoneType = this.ownerForm.owners[owner.index]
            .phoneType
            ? this.ownerForm.owners[owner.index].phoneType
            : '';
          this.ownerForm.ownerValidation[owner.index].isOwnerValidated = true;
          this.ownerForm.ownerValidation[owner.index].lookupDone = true;
          this.ownerForm.owners[owner.index].fid = this.vehicleForm.owners[owner.index].fid;
          this.ownerForm.ownerValidation[owner.index].person = {...owner};

          this.ownerForm.ownerValidation[owner.index].leasedVehicleIndicator =
            this.vehicleForm.transferVehicleResponse.leasedVehicleIndicator;


          if (this.ownerForm.garagingAddress.type !== 'New') { this.ownerForm.setGaraging(); }

          this.vehicleForm.plateLookupDone = true;


        });

      }
      if (this.serviceType.serviceType.transactionType === 'PrefillRenewRegistration') {
        if (this.ownerForm.owners[0].entityType === 'BUS') {
          this.ownerForm.ownership = 'BusinessOwned';
        } else {
          this.ownerForm.ownership = '';
        }
      }
    }
  }

  checkGetReady() {
    if (
      this.serviceType &&
      this.serviceType.serviceType.reassignedPlate === 'Y'
    ) {
      return true;
    } else {
      return this.IsGetReadyEligible && !this.IsEvrLiteEligible;
    }
  }

  converToGetReady() {
    if (this.serviceType.serviceType.transactionType === 'PrefillNewTitleAndRegistration') {
      this.getReadyRequest.rmvServicesUserEnteredData.vehicles[0].plateAssignedIndicator = 'N';
    }

    if (this.getReadyRequest.rmvServicesUserEnteredData.owners[0].licenseState !== 'MA') {
      this.getReadyRequest.rmvServicesUserEnteredData.garagingAddress.referenceType = 'New';
      this.getReadyRequest.rmvServicesUserEnteredData.garagingAddress.type = 'New';
    }

    this.IsEvrLiteEligible = !this.IsEvrLiteEligible;
    this.evrLiteMessages = [];
    this.responseModal.open();
  }

  ownershipTypeChange(ev) {
    if (ev) {
      this.ownerForm.resetGaraging();
      this.ownerForm.owners = this.ownerForm.initOwners();
      this.vehicleForm.owners = [];
    }
  }

  setCondition(ev) {
    this.vehicleForm.vehicle.condition = ev === 'CASUAL' ? 'USED' : '';

  }

  getOwnerValidation() {
    const ownerValidation = this.transactionType === 'DuplicateRegistration' ? this.vehicleForm?.ownerValidation
      : this.ownerForm?.ownerValidation;
    return ownerValidation ?? '';
  }

  setTaxExempt(ev) {
    if (ev.includes('SurvivingSpouseNewTitle')) {
      this.purchaseForm.purchaseAndSalesInformation.taxExempt = 'Y';
      this.purchaseForm.purchaseAndSalesInformation.taxExemptType = 'SS';
      this.purchaseForm.purchaseAndSalesInformation.proofOfNoTaxRequired = 'Y';
    }
  }

  setLeaseAndFinance(ev) {
    if (ev.includes('SurvivingSpouseNewTitle')) {
     this.ownerForm.isLeased = 'No';
    }
  }
}
