
import {take} from 'rxjs/operators';
import { Injectable } from '@angular/core';
import { BehaviorSubject ,  SubscriptionLike as ISubscription } from 'rxjs';

// Services
import { OptionsService } from 'app/dashboard/app-services/options.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { SubsService } from 'app/dashboard/app-services/subs.service';
import { AgencyUserService } from 'app/shared/services/agency-user.service';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';

// Models
import { QuoteHome, QuotePlan, QuotePlanListAPIResponse } from 'app/app-model/quote';
import {
  Coverage,
  PolicyCoveragesData,
  CoverageItem,
  CoverageItemParsed,
  PolicyItemDefaultDataAPIResponse
} from 'app/app-model/coverage';

interface TriggerSourceI {
  triggerBy?: 'SelectedPlans' | 'QuoteFormType' | 'QuoteEffectiveDate' | 'Basics' | 'Options';
}

interface PromiseDataAfterSettingDefaultValuesNew {
  policiesToUpdate: CoverageItemParsed[];
  policyItemsParsed: CoverageItemParsed[];
}

interface PromiseDataAfterSavingToApi {
  status: string;
  policyItemsParsed: CoverageItemParsed[];
}

@Injectable()
export class HomeGeneralOptionsAutomanagerService {
  private serviceIsInitialized = false;

  private subscriptionQuote: ISubscription;
  private subscriptionQuoteIsNew: ISubscription;
  private subscriptionSelectedPlans: ISubscription;
  private subscriptionSelectedQuoteTypes: ISubscription;
  private subscriptionParserTrigger: ISubscription;
  private subscriptionCarrierOptionsPolicyItemHomeParsed: ISubscription;

  private _parserTrigger: BehaviorSubject<TriggerSourceI> = new BehaviorSubject({});

  private quote: QuoteHome;
  private quoteId = '';
  private quoteLob = '';
  private quoteFormTypes: string[] = [];
  private quoteIsNew = false;
  private selectedPlans: QuotePlan[] = [];
  private selectedPlansIds: string[] = [];
  private quoteBeforeChange: QuoteHome = null;
formType;
  // Required for updating coverages - we need all options from every view
  private carrierOptionsPolicies: CoverageItemParsed[] = [];

  private preventParsingPoliciesUntilDataSavedToAPIServer = false;
  subscriptionDwellingData: any;
  homeQuoteCoverages: any;

  constructor(
    private storageService: StorageService,
    private agencyUserService: AgencyUserService,
    private specsService: SpecsService,
    private optionsService: OptionsService,
    private subsService: SubsService,
    private storageGlobalService: StorageGlobalService
  ) { }

  public initialize(): Promise<void> {
    if (this.serviceIsInitialized) {
      return Promise.resolve();
    }

    this.serviceIsInitialized = true;
    console.log('Initialize HomeGeneralOptionsAutomanagerService');

    return Promise.all([
      this.subscribeQuote(),
      this.subscribeQuoteIsNew(),
      this.subscribeSelectedPlans(),
      this.subscribeSelectedQuoteFormTypes(),
      this.subscribeCarrierOptionsPolicyItemHomeParsed(),
      this.subscribeQuoteCoverages()
    ]).then(() => {
      return this.initPolicyItemsParser();
    }).catch(err => {
      console.log(err);
    });
  }

  public destroy(): void {
    this.serviceIsInitialized = false;

    this.subscriptionQuote && this.subscriptionQuote.unsubscribe();
    this.subscriptionQuoteIsNew && this.subscriptionQuoteIsNew.unsubscribe();
    this.subscriptionSelectedPlans && this.subscriptionSelectedPlans.unsubscribe();
    this.subscriptionSelectedQuoteTypes && this.subscriptionSelectedQuoteTypes.unsubscribe();
    this.subscriptionParserTrigger && this.subscriptionParserTrigger.unsubscribe();
    this.subscriptionCarrierOptionsPolicyItemHomeParsed && this.subscriptionCarrierOptionsPolicyItemHomeParsed.unsubscribe();
  }

  private subscribeQuote(): Promise<QuoteHome> {
    return new Promise((resolve, reject) => {
      this.subscriptionQuote = this.storageService.getStorageData('selectedQuote')
        .subscribe((quote: QuoteHome) => {
          this.quote = JSON.parse(JSON.stringify(quote));
          this.quoteId = quote.resourceId;
          this.quoteLob = quote.lob;
          this.formType = quote.formType;
          resolve(this.quote);

          if (!this.quoteBeforeChange) {
            this.quoteBeforeChange = JSON.parse(JSON.stringify(quote));
          } else if (this.quoteBeforeChange.effectiveDate !== this.quote.effectiveDate) {
            this.quoteBeforeChange = JSON.parse(JSON.stringify(quote));

            // INIT POLICY ITEMS PARSING After Quote Effective date change
            // Emit Observable to Parse Items
            this._parserTrigger.next({triggerBy: 'QuoteEffectiveDate'});
          }
        });
    });
  }

  private subscribeSelectedQuoteFormTypes(): Promise<string[]> {
    return new Promise((resolve, reject) => {
      this.subscriptionSelectedQuoteTypes = this.storageService.getStorageData('selectedQuoteFormTypes')
        .subscribe((res: string[]) => {
          this.quoteFormTypes = res;
          this.formType = res[0];
          resolve(this.quoteFormTypes);

          // Emmit Observable to Parse Items
          this._parserTrigger.next({triggerBy: 'QuoteFormType'});
        });
    });
  }

  private subscribeQuoteIsNew(): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.subscriptionQuoteIsNew = this.storageService.getStorageData('isNewQuote')
        .subscribe((res: boolean) => {
          this.quoteIsNew = res;
          resolve(this.quoteIsNew);
        });
    });
  }

  private subscribeSelectedPlans(): Promise<QuotePlan[]> {
    return new Promise((resolve, reject) => {
      this.subscriptionSelectedPlans = this.storageService.getStorageData('selectedPlan')
        .subscribe((res: QuotePlanListAPIResponse) => {
          if (res && res.items && res.items.length) {
            this.selectedPlans = JSON.parse(JSON.stringify(res.items[0].items));
            this.selectedPlansIds = this.selectedPlans.map(plan => plan.ratingPlanId);
            resolve(this.selectedPlans);

            // INIT POLICY ITEMS PARSING
            // Emmit Observable to Parse Items
            this._parserTrigger.next({triggerBy: 'SelectedPlans'});
          }
        });
    });
  }

     private subscribeQuoteCoverages() {
    this.subscriptionDwellingData = this.storageService
      .getStorageData('homeQuoteCoverages')
      .subscribe(data => {
        if (data) {
          this.homeQuoteCoverages = JSON.parse(JSON.stringify(data));
          // INFO:: Please DO NOT invoke the 'this.getOptionsPoliciesListAndParseData' directly
          // Instead please use 'this._parserTrigger' Observable to avoid item parsing before all required data is ready.
          // Emmit Observable to Parse Items
          this._parserTrigger.next({ triggerBy: 'Basics' });
        }
      });
  }

  private subscribeCarrierOptionsPolicyItemHomeParsed(): void {
    this.subscriptionCarrierOptionsPolicyItemHomeParsed = this.storageService.getStorageData('homeCarrierOptionsParsed')
      .subscribe((items: CoverageItemParsed[]) => {
        this.carrierOptionsPolicies = items;
        this._parserTrigger.next({ triggerBy: 'Options' });
      });
  }

  private initPolicyItemsParser(): Promise<void> {
    const delay = 500;
    let timer;

    return new Promise((resolve, reject) => {
      this.subscriptionParserTrigger = this._parserTrigger.asObservable().subscribe(res => {
        timer && clearTimeout(timer);

        // Prevent geting Policies and parse data until data is saved to API with default values - if necessary
        if (this.preventParsingPoliciesUntilDataSavedToAPIServer) { return; }

        timer = setTimeout(() => {
          this.getOptionsPoliciesListAndParseData()
            .then(() => resolve())
            .catch(err => reject(err));
        }, delay);
      });
    });
  }

  private getOptionsPoliciesListAndParseData(): Promise<void> {
    const selectedPlansIdsString = this.selectedPlansIds.join(',');
    const states = this.quote.state;
    const quoteFormTypesString = this.quoteFormTypes.join(',');
    const quoteEffectiveDate = (this.quote && this.quote.effectiveDate) ? this.quote.effectiveDate : '';

    let agencyId;
    this.agencyUserService.userData$.pipe(take(1)).subscribe(agent => agencyId = agent.agencyId);

    return new Promise((resolve, reject) => {
      this.specsService.getRatingCoverages(states, this.quoteLob, 'policyOptionsGeneral', selectedPlansIdsString, quoteFormTypesString, quoteEffectiveDate).pipe(
        take(1))
        .subscribe(res => {
          let policiesItemsHome: CoverageItemParsed[] = [];
          if (res && res.items && res.items.length) {
            policiesItemsHome = [...res.items];
          }

          if (policiesItemsHome && this.quoteId) {
            let policyItemsHomeParsed: CoverageItemParsed[] = [];
            this.processPoliciesItemsParsing(
              policiesItemsHome,
              this.selectedPlansIds,
              this.quoteId,
              this.quoteFormTypes
            )
            .then((res: CoverageItemParsed[]) => {
              return policyItemsHomeParsed = JSON.parse(JSON.stringify(res));
            })
            .then((res: CoverageItemParsed[]) => {
              policyItemsHomeParsed = res;
              return this.setDefaultValuesForOptionsIfThisIsNewQuote(policyItemsHomeParsed, agencyId, this.quoteIsNew);
            })
            .then((res: PromiseDataAfterSettingDefaultValuesNew) => {
              // Save To Storage
              policyItemsHomeParsed = res.policyItemsParsed;
              this.storageService.setStorageData('homeGeneralOptionsParsed', policyItemsHomeParsed);

              // Update API with default Values
              return this.savePoliciesToApiIfNewPoliciesToUpdate(policyItemsHomeParsed, res.policiesToUpdate);
            })
            .then(() => resolve())
            .catch(err => {
              console.log(err);
              reject(err);
            });

          }
        },
        err => reject(err)
      );
    });


  }

  public processPoliciesItemsParsing(policies: CoverageItem[], selectedPlansIds: string[], quoteId: string, quoteSelectedFormTypes: string[]): Promise<CoverageItemParsed[]> {
    let arrPoliciesItemsParsed = this.optionsService.parsePoliciesItemsToPoliciesItemsParsed(policies);
    arrPoliciesItemsParsed = this.optionsService.orderObjectsArrayByProperty(arrPoliciesItemsParsed, 'description');

    // Required for setting default values if new Quote - to check if options
    // has been alredy saved in storage or not
    let policiesItemsFromStorage: CoverageItemParsed[] = [];

    this.storageService.getStorageData('homeGeneralOptionsParsed').pipe(
      take(1))
      .subscribe(res => policiesItemsFromStorage = res);
    // --------------------------------------------------------------------------

    return this.loadPolicyCoverages(quoteId)
      .then((data: PolicyCoveragesData) => {
        console.log('%c GENERAL OPTIONS, Loaded Coverages', 'color:blue', data);

        arrPoliciesItemsParsed = arrPoliciesItemsParsed.map(item => {
          item = this.setAdditionalDataForCoverageItemParsed(item);
          item = this.optionsService.sortPolicyItemParsedChildItems(item);
          item = this.optionsService.setPolicyItemStatusBasedOnPolicyCoverages(item, data.coverages);
          item = this.setPolicyItemStatusBasedOnRequirements(item, quoteSelectedFormTypes, selectedPlansIds);

          item.endpointUrl = data.endpointURL;
          item = this.optionsService.setPolicyItemParsedIsNewFromAPIValue(item, policiesItemsFromStorage);
          item.quoteResourceId = quoteId;

          if (item.inputType !== 'Dropdown') {
            item.values = this.optionsService.orderObjectsArrayByProperty(item.values, 'value');
          }

          return item;
        });
        return arrPoliciesItemsParsed;
      });
  }

  private loadPolicyCoverages(quoteId: string): Promise<PolicyCoveragesData> {
    const policyCoverageData: PolicyCoveragesData = new PolicyCoveragesData();

    return new Promise((resolve, reject) => {
      if (quoteId) {
        this.optionsService.getPolicies(quoteId).pipe(take(1)).subscribe(res => {
          if (res.items.length === 0 ) {
            console.log('%c GENERAL OPTION Creating Resource for coverages', 'color: red');
            this.optionsService.createCoverageByUri(res.meta.href).pipe(take(1)).subscribe((res) => {
              console.log('%c GENERAL OPTION Created Resource for coverages', 'color: green', res);
              policyCoverageData.coverages = res.coverages;
              policyCoverageData.endpointURL = res.meta.href;
              resolve(policyCoverageData);
            });
          } else {
            policyCoverageData.coverages = res.items[0].coverages;

            if (res.items[0].meta && res.items[0].meta.href) {
              policyCoverageData.endpointURL = res.items[0].meta.href;
            } else if (res.items[0]) {
              policyCoverageData.endpointURL = res.meta.href + '/' + res.items[0].resourceId;
            } else {
              policyCoverageData.endpointURL = 'api_does_not_returned_data_to_create_uri';
            }

            resolve(policyCoverageData);
          }
        });
      } else {
        reject('Error Load Policy Coverages, quoteId Not defined');
      }
    });
  }

  // Helper for method setDefaultValuesForOptionsIfThisIsNewQuote
  private helperSetDefaultValues(data: PolicyItemDefaultDataAPIResponse, policiesParsed: CoverageItemParsed[]): PromiseDataAfterSettingDefaultValuesNew {
    const policiesParsedWithDefaultValues = this.optionsService.setDefaultValuesForOptions(policiesParsed, data.items);
    const policiesToUpdate = policiesParsedWithDefaultValues.filter(policyParsed => data.items.some(item => item.coverageCode === policyParsed.coverageCode && policyParsed.isNewFromAPI));

    const promiseResponse: PromiseDataAfterSettingDefaultValuesNew = {
      policiesToUpdate: policiesToUpdate,
      policyItemsParsed: policiesParsedWithDefaultValues
    };

    return promiseResponse;
  }

  private setDefaultValuesForOptionsIfThisIsNewQuote(policiesParsed: CoverageItemParsed[], agencyId: string, isNewQuote: boolean): Promise<PromiseDataAfterSettingDefaultValuesNew> {
    return new Promise((resolve, reject) => {
      // console.log('Quote is new', isNewQuote);
      if (!isNewQuote) {
        const promiseResponse: PromiseDataAfterSettingDefaultValuesNew = {
          policiesToUpdate: [],
          policyItemsParsed: policiesParsed
        };
        return resolve(promiseResponse);
      }

      this.storageService.getStorageData('homeDefaultOptions').pipe(
        take(1))
        .subscribe(
          (res: PolicyItemDefaultDataAPIResponse) => {

            if (res && res.items && res.items.length) {
              const promiseResponse = this.helperSetDefaultValues(res, policiesParsed);
              resolve(promiseResponse);
            } else {
              // API Fallback
              this.subsService.getDefaultHomeCarrierOptions(agencyId, this.formType).pipe(
                take(1))
                .subscribe(
                  (res: PolicyItemDefaultDataAPIResponse) => {
                    const promiseResponse = this.helperSetDefaultValues(res, policiesParsed);
                    resolve(promiseResponse);
                  },
                  err => reject(err)
                );
            }
          },
          err => {
            reject(err);
          }
        );
    });
  }



  private savePoliciesToApiIfNewPoliciesToUpdate(policiesParsed: CoverageItemParsed[], policiesToUpdate: CoverageItemParsed[]): Promise<PromiseDataAfterSavingToApi> {
    return new Promise((resolve, reject) => {
      const promiseResponse: PromiseDataAfterSavingToApi = {
        status: '',
        policyItemsParsed: policiesParsed
      };

      // Do not send request to update if no new options with default values
      if (!policiesToUpdate || !policiesToUpdate.length) {
        promiseResponse.status = 'No new options to Update default values, Do not send API Request';
        return resolve(promiseResponse);
      }

      // We need to also update policies Carrier Options from the Carrier Options View
      // The mechanism of updating policeis (coverages) is strange - only options sent to API will be set as selected
      // (if the option was selected before and not sent to API during update, next time data is load, this option will be unselected )
      let allPoliciesToSearchActiveOptions: CoverageItemParsed[] = [];
      allPoliciesToSearchActiveOptions = [...policiesParsed, ...this.carrierOptionsPolicies];

      const endpointUrl = policiesParsed[0].endpointUrl;
      const activePoliciesToUpdate = allPoliciesToSearchActiveOptions.filter(item => item.isActive);
      const readyAllPoliciesToUpdate: Coverage[] = this.optionsService.convertPolicyItemParsedArrayToPolicyArray(activePoliciesToUpdate);

      // Update remote data
      const newCoverageData = {
        coverages: readyAllPoliciesToUpdate
      };

      this.optionsService.updatePoliciesByUri(endpointUrl, newCoverageData).pipe(
        take(1))
        .subscribe(
          res => {
            promiseResponse.status = 'Set and saved Policies default values for new options';
            // console.log(promiseResponse.status);
            resolve(promiseResponse);
          },
          err => {
            promiseResponse.status = 'Error occurred During updating, not set default values';
            // console.log(promiseResponse.status);
            resolve(promiseResponse);
          }
        );
    });
  }

  private setAdditionalDataForCoverageItemParsed(item: CoverageItemParsed): CoverageItemParsed {
    if (!item.additionalData) {
      item.additionalData = {};
    }

    return item;
  }


  // Check if any element of an array (anyElementArr) is in another array (arrayToCheckIfIsIn)
  private anyElementIsInArray(anyElementArr: string[], arrayToCheckIfIsIn: string[]): boolean {
    return anyElementArr.some(el => arrayToCheckIfIsIn.indexOf(el) !== -1);
  }

  // Requirements
  // https://bostonsoftware.atlassian.net/browse/SPRC-276
  // https://app.smartsheet.com/b/home?lx=CVsFzS2_Y_J5mboPzV7cVA
  // ----------------------------------------------------------------------------
  public setPolicyItemStatusBasedOnRequirements(policyItemHomeParsed: CoverageItemParsed, selectedQuoteFormTypes: string[], selectedPlansIds: string[]): CoverageItemParsed {
    switch (policyItemHomeParsed.coverageCode) {
      // Credit Check Authorization
      // Required for plans:
      // ASI	105
      // NGM - Real Time	204
      // Safeco	173
      // Travelers	95
      // Vermont Mutual	96
      // Cincinnati 267
      // New Travelers (Plan ID 283)
      // N&D 314
      // Openly
      case 'BSC-HOME-020063':
        if (
            this.anyElementIsInArray(['HO3', 'HO5', 'HO4', 'HO6'], selectedQuoteFormTypes)
            && this.anyElementIsInArray(['105', '204', '173', '95', '96', '267', '283', '310', '313', '317', '310', '332'], selectedPlansIds)
        ) {
          policyItemHomeParsed.isRequired = true;
          policyItemHomeParsed.additionalData.requiredForCarriers = ['95', '96', '105', '173', '204', '267', '283', '317', '310', '332'];
        }
        if (
          this.anyElementIsInArray(['HO3', 'HO5', 'HO4', 'HO6'], selectedQuoteFormTypes)
          && this.anyElementIsInArray(['314'], selectedPlansIds)
      ) {
        policyItemHomeParsed.isRequired = true;
        policyItemHomeParsed.additionalData.requiredForCarriers ? policyItemHomeParsed.additionalData.requiredForCarriers.push('314')
         : policyItemHomeParsed.additionalData.requiredForCarriers = ['314'];
      }
        break;

        case 'BSC-HOME-000427':
          if (
              this.anyElementIsInArray(['HO3', 'HO5', 'HO4', 'HO6'], selectedQuoteFormTypes)
              && (policyItemHomeParsed.currentValueData.values[0] !== 'Property Limit 25000'
              && policyItemHomeParsed.currentValueData.values[0] !== 'Property Limit 50000')
              || (policyItemHomeParsed.currentValueData.values[1] !== 'Liability Limit 50000'
              && policyItemHomeParsed.currentValueData.values[1] !== 'Liability Limit 100000')
          ) {
            policyItemHomeParsed.isActive = false;
          }
          break;
            case 'BSC-HOME-000456':
      if (
        policyItemHomeParsed.currentValueData &&
        Array.isArray(policyItemHomeParsed.currentValueData.keyValue)
      ) {
        // Count how many are set to "Yes"
        const yesCount = policyItemHomeParsed.currentValueData.keyValue.filter(
          ([, value]) => value === 'Yes'
        ).length;
        if (yesCount > 1) {
          policyItemHomeParsed.currentValueData.keyValue = [];
          policyItemHomeParsed.currentValueData.values = [];
          policyItemHomeParsed.currentValue = '';

          policyItemHomeParsed.isActive = false;

        }
      }
      break;

          case 'BSC-HOME-000312':
            if (
              this.anyElementIsInArray(['HO3', 'HO5', 'HO4', 'HO6'], selectedQuoteFormTypes)
              && (policyItemHomeParsed.currentValueData.values[0] !== '2500'
              && policyItemHomeParsed.currentValueData.values[0] !== '5000'
              && policyItemHomeParsed.currentValueData.values[0] !== '7500')
          ) {
            policyItemHomeParsed.isActive = false;
          }
          break;
    }

    return policyItemHomeParsed;
  }

}
