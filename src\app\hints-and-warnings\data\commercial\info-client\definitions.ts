import { WARNING_GROUPS, WarningDefinitionI, AdditionalDataI, AdditionalDataAutoClientInfo} from 'app/hints-and-warnings/model/warnings';
import { ClientAddress, ClientContactMethod, ClientDetails } from 'app/app-model/client';
import { Validate } from 'app/hints-and-warnings/validators';


function requiredField(value):boolean {
  if(value == undefined || value == null) {
    return true;
  }
  value = String(value);

  return value.trim().length <= 0 || !value;
}

function validateZipCode(zip): boolean {
  if (zip === '' || zip === null) {
    return false;
  }

  const zipPattern = /^\d{5}(?:-?\d{4})?$/;
  return !zipPattern.test(zip);
}

function generateViewUrl():string {
  return  '{{current_url}}?overlay=info&type=client';

}

const checkRequiredPhone = (value,fullObj, additionalData) => {

if(Validate.isRequiredForSelectedPlans(['298'], additionalData.quoteSelectedPlansIds)) {
let count= 0;
 additionalData.observedData.forEach(element => {
   if(element.type === 'Email' && element.type === 'Fax'){
   }else {
     element.value ? count+=1  : '';
   }

 });
 return count > 0 ?  false : true;
}
}


/**
 * Validation for Client Info Data
 * For Model: ClientDetails
 */



const clientDetailsBusinessName:WarningDefinitionI = {
  id: 'business.name',
  deepId: 'business.name',
  viewUri: generateViewUrl,
  viewFieldId: 'clientDetailsBusinessName',
  warnings: [{
    label: 'Required Business Name for client.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}

const clientDetailsLegalEntity:WarningDefinitionI = {
  id: 'business.legalEntity',
  deepId: 'business.legalEntity',
  viewUri: generateViewUrl,
  viewFieldId: 'clientBusinessLegalEntity',
  warnings: [{
    label: 'Required Legal Entity for client.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}

const clientDetailsSicCode:WarningDefinitionI = {
  id: 'business.sicCode',
  deepId: 'business.sicCode',
  viewUri: generateViewUrl,
  viewFieldId: 'clientBusinessSiccode',
  warnings: [{
    label: 'Required SIC Code for client.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}

const clientDetailsTaxId:WarningDefinitionI = {
  id: 'business.taxId',
  deepId: 'business.taxId',
  viewUri: generateViewUrl,
  viewFieldId: 'clientBusinessTaxId',
  warnings: [{
    label: 'Required Tax Id for client.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}

const clientDetailsTaxType:WarningDefinitionI = {
  id: 'business.taxType',
  deepId: 'business.taxType',
  viewUri: generateViewUrl,
  viewFieldId: 'clientBusinessTaxType',
  warnings: [{
    label: 'Required Tax Type for client.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}

const clientDetailsBusinessStartDate:WarningDefinitionI = {
  id: 'business.businessStartDt',
  deepId: 'business.businessStartDt',
  viewUri: generateViewUrl,
  viewFieldId: 'businessStartDt',
  warnings: [{
    label: 'Required Start Date for client.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}



export const WARNINGS_DEFINITIONS_INFO_CLIENT_AUTOB: WarningDefinitionI[] = [
  clientDetailsBusinessName,
  clientDetailsLegalEntity,
  clientDetailsSicCode,
  clientDetailsTaxId,
  clientDetailsTaxType,
  clientDetailsBusinessStartDate
];


//clientAddresses

const clientAddress:WarningDefinitionI = {
  id: 'address1',
  deepId: 'address1',
  viewUri: generateViewUrl,
  viewFieldId: 'clientInfoAddrAddr1_StreetAddress',
  warnings: [{
    label: 'Required Address for client.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}

const clientCity:WarningDefinitionI = {
  id: 'city',
  deepId: 'city',
  viewUri: generateViewUrl,
  viewFieldId: 'clientInfoAddrCity_StreetAddress',
  warnings: [{
    label: 'Required City for client.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}

const clientState:WarningDefinitionI = {
  id: 'state',
  deepId: 'state',
  viewUri: generateViewUrl,
  viewFieldId: 'clientInfoAddrState_StreetAddress',
  warnings: [{
    label: 'Required State for client.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
}

const clientZip:WarningDefinitionI = {
  id: 'zip',
  deepId: 'zip',
  viewUri: generateViewUrl,
  viewFieldId: 'clientInfoAddrZip_StreetAddress',
  warnings: [{
    label: 'Required Zip for client.',
    condition: requiredField,
    group: WARNING_GROUPS.general,
    carriers: []
  },
  {
    label: 'Client Prior Address Zip code is not Valid',
    condition: (value: any, fullObj: ClientAddress) => {
      if(fullObj.addressType === "PreviousAddress") {
        return validateZipCode(value);
      }
    },
    group: WARNING_GROUPS.general,
    carriers: []
  },
  {
    label: 'Client Current Address Zip code is not Valid',
    condition: (value: any, fullObj: ClientAddress) => {
      if(fullObj.addressType === "StreetAddress") {
        return validateZipCode(value);
      }
    },
    group: WARNING_GROUPS.general,
    carriers: []
  }
]
}


export const WARNINGS_DEFINITIONS_CLIENT_ADDRESS_AUTOB: WarningDefinitionI[] = [
  clientAddress,
  clientCity,
  clientState,
  clientZip

];

//Liberty Specific (298)
const requiredPhone:WarningDefinitionI = {
  id: 'value',
  deepId: 'value',
  viewUri: generateViewUrl,
  viewFieldId: 'cliContactHomePhone',
  warnings: [{
    label: 'Required at least 1 Phone number',
    condition: checkRequiredPhone,
    group: WARNING_GROUPS.carrier,
    carriers: ['298']
  }]
}

export const WARNINGS_DEFINITIONS_CLIENT_PHONE_AUTOB: WarningDefinitionI[] = [
  requiredPhone
]

