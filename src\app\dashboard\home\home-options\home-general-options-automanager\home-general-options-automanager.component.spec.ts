import { async, ComponentFixture, fakeAsync, inject, TestBed, tick } from '@angular/core/testing';

import { data as AGENCY_USER } from 'testing/data/agencies/user';
import { data as HOME_COVERAGES } from 'testing/data/quotes/coverages/home';
import { HOME_QUOTE } from 'testing/data/quotes/quote-home';
import { HOME_QUOTE_PLAN_LIST } from 'testing/data/quotes/quote-plan-list/home';
import {
    HOME_GENERAL_POLICY_OPTIONS
} from 'testing/data/specs/rating-coverages/HOME/policy-options-general';
import {
    StubAgencyUserService, StubAgencyUserServiceProvider
} from 'testing/stubs/services/agency-user.service.provider';
import {
    REAL_OPTIONS_SERVICE_TOKEN, StubOptionsService, StubOptionsServiceProvider
} from 'testing/stubs/services/options.service.provider';
import {
    StubSpecsService, StubSpecsServiceProvider
} from 'testing/stubs/services/specs.service.provider';
import { StubSubsServiceProvider } from 'testing/stubs/services/subs.service.provider';

import { OptionsService } from 'app/dashboard/app-services/options.service';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { AgencyUserService } from 'app/shared/services/agency-user.service';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { Helpers } from 'app/utils/helpers';

import {
    HomeGeneralOptionsAutomanagerComponent
} from './home-general-options-automanager.component';

describe('Component: HomeGeneralOptionsAutomanager', () => {
  let component: HomeGeneralOptionsAutomanagerComponent;
  let fixture: ComponentFixture<HomeGeneralOptionsAutomanagerComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [HomeGeneralOptionsAutomanagerComponent],
      providers: [
        StorageGlobalService,
        StorageService,
        StubAgencyUserServiceProvider,
        StubSpecsServiceProvider,
        StubOptionsServiceProvider,
        { provide: REAL_OPTIONS_SERVICE_TOKEN, useClass: OptionsService },
        StubSubsServiceProvider
      ]
    })
    .compileComponents();
  }));

  beforeEach(fakeAsync(inject(
    [StorageService, AgencyUserService, SpecsService, OptionsService],
    (storageService: StorageService, agencyUserService: StubAgencyUserService,
      specsService: StubSpecsService, optionsService: StubOptionsService) => {
      storageService.setStorageData('selectedQuote', Helpers.deepClone(HOME_QUOTE));
      storageService.setStorageData('selectedQuoteFormTypes', ['HO5']);
      storageService.setStorageData('selectedPlan', Helpers.deepClone(HOME_QUOTE_PLAN_LIST));
      agencyUserService.userData = Helpers.deepClone(AGENCY_USER);
      specsService.ratingCoverages = Helpers.deepClone(HOME_GENERAL_POLICY_OPTIONS);
      optionsService.coverages = Helpers.deepClone(HOME_COVERAGES);

      fixture = TestBed.createComponent(HomeGeneralOptionsAutomanagerComponent);
      component = fixture.componentInstance;
      fixture.detectChanges();
      tick(500);
  })));

  it('should be created', () => {
    expect(component).toBeTruthy();
  });

  it('should destroy without errors', () => {
    expect(() => {
      fixture.destroy();
    }).not.toThrow();
  });
});
