import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { async, ComponentFixture, TestBed } from '@angular/core/testing';


import { StubDatepickerInputComponent } from 'testing/stubs/components/datepicker-input.component';
import { StubLeaveQuoteComponent } from 'testing/stubs/components/leave-quote.component';
import { StubModalboxComponent } from 'testing/stubs/components/modalbox.component';
import { StubPlansSelectorComponent } from 'testing/stubs/components/plans-selector.component';
import { MockRouterProvider } from 'testing/stubs/router.provider';
import { StubAgencyUserServiceProvider } from 'testing/stubs/services/agency-user.service.provider';
import { StubClientsServiceProvider } from 'testing/stubs/services/clients.service.provider';
import { StubDriversServiceProvider } from 'testing/stubs/services/drivers.service.provider';
import {
    StubOverlayLoaderServiceProvider
} from 'testing/stubs/services/overlay-loader.service.provider';
import { StubQuotesServiceProvider } from 'testing/stubs/services/quotes.service.provider';
import { StubSpecsServiceProvider } from 'testing/stubs/services/specs.service.provider';
import { StubSubsServiceProvider } from 'testing/stubs/services/subs.service.provider';

import { ApiCommonService } from 'app/shared/services/api-common.service';
import { RouteService } from 'app/shared/services/route.service';
import { PlanSelectorComponent } from './plan-selector.component';
import { StubSelectComponent } from 'testing/stubs/components/select.component';
import { IterPipe } from 'app/shared/pipes/iter.pipe';
import { StubCrossApplicationServiceProvider } from 'testing/stubs/services/cross-aplication.service.provider';
import { StubFormsServiceProvider } from 'testing/stubs/services/forms.service.provider';

describe('PlanSelectorComponent', () => {
  let component: PlanSelectorComponent;
  let fixture: ComponentFixture<PlanSelectorComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [
        PlanSelectorComponent,
        StubDatepickerInputComponent,
        StubLeaveQuoteComponent,
        StubPlansSelectorComponent,
        StubSelectComponent,
        StubModalboxComponent,
        IterPipe
      ],
      providers: [
        StubAgencyUserServiceProvider,
        StubSubsServiceProvider,
        StubQuotesServiceProvider,
        StubDriversServiceProvider,
        StubClientsServiceProvider,
        StubOverlayLoaderServiceProvider,
        StorageService,
        StubSpecsServiceProvider,
        RouteService,
        StorageGlobalService,
        MockRouterProvider,
        StubCrossApplicationServiceProvider,
        StubFormsServiceProvider
      ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(PlanSelectorComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
