import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';

import { StubLeaveQuoteComponent } from 'testing/stubs/components/leave-quote.component';

import { HomeOptionsComponent } from './home-options.component';

describe('HomeOptionsComponent', () => {
  let component: HomeOptionsComponent;
  let fixture: ComponentFixture<HomeOptionsComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      imports: [RouterTestingModule],
      declarations: [ HomeOptionsComponent, StubLeaveQuoteComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(HomeOptionsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should be created', () => {
    expect(component).toBeTruthy();
  });
});
