import { WarningDefinitionI, WARNING_GROUPS } from './../../../model/warnings';
import { Validate } from 'app/hints-and-warnings/validators';

const generateViewUrl = (fullObj): string => {
  return  `/dashboard/commercial-auto/quotes/${fullObj.quoteSessionId}/coverages`; // + '?overlay=info&type=client';
};

const checkOLCfield = (value, fullObj, additionalData) => {
  let returnValue;
  if (Validate.isRequiredForSelectedPlans(['298'], additionalData.quoteSelectedPlansIds)) {
  fullObj.coverages.forEach(element => {
    if (element.coverageCode === 'OLC') {
      element.values[0].value === 'Split' ? returnValue = true : returnValue = false;
    }
  });
}
  return returnValue;
};

const checkCollisionCoverages = (val, fullObj, additional) => {
  if (Validate.isRequiredForSelectedPlans(['298'], additional.quoteSelectedPlansIds)) {
    const coll = val.find(x => x.coverageCode === 'COLL');
    const lcoll = val.find(x => x.coverageCode === 'LCOLL');
    const comp = val.find(x => x.coverageCode === 'COMP');
    let returnValue;
    if (comp.values[0].value === 'Omit' && (coll.values[0].value !== 'Omit' || lcoll.values[0].value !== 'Omit')) {
       returnValue = true;

    }
    return returnValue;

}
};

const checkSymbols = (val, fullObj, additional) => {
  let returnValue = false;
  val.forEach(symbol => {
    if (symbol.coveredAutoSymbolValues.length === 0) {
      additional.coverages.forEach(coverage => {
        if (coverage.coverageCode === symbol.linkedCoverageCode) {
        if (coverage.values[0].value !== 'Omit') {
          returnValue = true;
        }
        }
      }); }}
  );
return returnValue;
};



// Liberty Specific (298)
const OLCHasToBeCSL: WarningDefinitionI = {
  id: 'coverages',
  deepId: 'coverages',
  viewUri: generateViewUrl,
  viewFieldId: 'OLC',
  warnings: [{
    label: 'Optional Liability Coverage cannot be Split',
    condition: checkOLCfield,
    group: WARNING_GROUPS.carrier,
    carriers: ['298']
  }]
};

const CollistionWithoutComp: WarningDefinitionI = {
  id: 'coverages',
  deepId: 'coverages',
  viewUri: generateViewUrl,
  viewFieldId: 'OLC',
  warnings: [{
    label: 'Collision Coverage cannot be set if Comprehensive is Omit',
    condition: checkCollisionCoverages,
    group: WARNING_GROUPS.carrier,
    carriers: ['298']
  }]
};


const SymbolsRequiredForCoverage: WarningDefinitionI = {
  id: 'symbols',
  deepId: 'symbols',
  viewUri: generateViewUrl,
  viewFieldId: '',
  warnings: [{
    label: 'Symbols needs to be set if coverage is not Omitted',
    condition: checkSymbols,
    group: WARNING_GROUPS.general,
    carriers: []
  }]
};

export const WARNINGS_DEFINITIONS_COM_AUTO_COVERAGES: WarningDefinitionI[] = [
  OLCHasToBeCSL,
  CollistionWithoutComp

];

export const WARNINGS_DEFINITIONS_COM_AUTO_SYMBOL_COVERAGES: WarningDefinitionI[] = [
  SymbolsRequiredForCoverage
];
