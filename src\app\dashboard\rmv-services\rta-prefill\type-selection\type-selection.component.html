<section class="section section--compact u-spacing--2-5">
  <div class="row">
    <h1 class="o-heading" style="color: #0b71ac; padding-left: 20px;">Select RTA Transaction </h1>
  </div>
  <div class="row u-spacing--1">
    <div class="col-xs-12">
      <div class="box box--silver">
        <table class="form-table">
          <tr class="form-table__row">
            <td class="form-table__cell u-width-80px">
              <label for="">Type:</label>
            </td>
            <td class="form-table__cell u-width-240px">
              <sm-autocomplete #refRTAType [options]="typeOptions" [name]="'type'" [id]="'type'" [activeOption]="type"
                [(ngModel)]="type" (ngModelChange)="checkType()">
              </sm-autocomplete>
            </td>
            <td>
              <span style="color:#0b71ac;" *ngIf="getReadyEligible()">This transaction is enabled for GetReady. Completing the highlighted fields below will increase eligibility.</span>
            </td> 
          </tr>
        </table>
      </div>
    </div>
  </div>
</section>
<app-modalbox #registration>

  <div class="box box--silver u-spacing--1-5">
    <p style="white-space: pre-wrap;">This transaction may be eligible for immediate completion without the RTA Form via SinglePoint’s direct integration with the RMV.
      <p style="padding-top:1rem;">Would you like to try that instead?</p>
  </div>
  <div class="row u-spacing--1-5">
    <div class="col-xs-12 u-align-right u-remove-letter-spacing">
        <button type="button" class="o-btn"  (click)="redirect()">Yes</button>
        <button type="button" class="o-btn o-btn--idle u-spacing--left-2"  (click)="close()">No</button>
        
        </div>
        </div>
</app-modalbox>