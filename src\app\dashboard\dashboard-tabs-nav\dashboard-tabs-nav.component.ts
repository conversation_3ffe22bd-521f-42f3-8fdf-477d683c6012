
import { Component, OnInit  } from '@angular/core';
import { Router, NavigationEnd, ActivatedRoute, Scroll } from '@angular/router';
import { SubscriptionLike as ISubscription } from 'rxjs';
import { LeadsService } from '../app-services/leads.service';
import { EstampService } from '../app-services/estamp.service';
import { CurrentPageService } from 'app/shared/services/current-page.service';
import { FeatureService } from '../../shared/services/feature.service';

@Component({
    selector: 'app-dashboard-tabs-nav',
    templateUrl: './dashboard-tabs-nav.component.html',
    styleUrls: ['./dashboard-tabs-nav.component.scss'],
    standalone: false
})
export class DashboardTabsNavComponent implements OnInit {

  public uriQuotes = '';
  public uriForms = '';
  public activeTab = '';
  public uriClients = '';

  public uriLeads = '';

  private subscription: ISubscription;

  private subscriptionRoute: ISubscription;
  enableTabs;
  leadCount$ = this.leadsService.getLeadsCount();
  estampCount$ = this.estampService.getEstampCount();
  activeFeatures;
  constructor(
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private page: CurrentPageService,
    private featureService: FeatureService,
    private leadsService: LeadsService,
    private estampService: EstampService
  ) {
    this.subscribeRoute();
   }

  ngOnInit() {}


  get pageHeaderMain () {
    return this.page.routeData && this.page.routeData.pageTitle;
  }

  ngOnDestroy() {
    this.subscription && this.subscription.unsubscribe();
    this.subscriptionRoute && this.subscriptionRoute.unsubscribe();
  }

  private subscribeRoute(): void {
    // In Angular 4+ events object is a Subject Observable (not BehaviorSubject)
    this.subscriptionRoute = this.router.events.subscribe( event => this.setActiveTab(event) );
  }

  public isFeatureEnabled(featureName): boolean {
    if (this.activeFeatures !== undefined && this.activeFeatures.length > 0) {
      const enabledFeatures = this.activeFeatures.filter(ef => ef.name === featureName);
      return (enabledFeatures !== undefined && enabledFeatures != null && enabledFeatures.length > 0);
    } else {
      return false;
    }
  }


  private setActiveTab(event : any) {
   if(event instanceof Scroll) {
      event = event.routerEvent
   }
    let matchedTab = 'quotes';
      switch (true) {
      case /forms/.test(event.url):
        matchedTab = 'forms';
        break;
      case /quote-inbox/.test(event.url):
        matchedTab = 'leads';
        break;
      case /clients/.test(event.url):
        matchedTab = 'clients';
        break;
      case /eStamp-requests/.test(event.url):
        matchedTab = 'eStamp-requests';
        break;
      case /quotes/.test(event.url):
        matchedTab = 'quotes';
        break;
      default:
        matchedTab = 'quotes';
    }
    this.activeTab = matchedTab;
  }

}
