
import {first, take} from 'rxjs/operators';
import { <PERSON><PERSON>, ClientAddress, ClientContactMethod, ClientDetails } from 'app/app-model/client';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild, SimpleChange, SimpleChanges } from '@angular/core';
import { Observable ,  SubscriptionLike as ISubscription } from 'rxjs';

import { AgencyUserService, UserData } from 'app/shared/services/agency-user.service';
import { ApiService } from 'app/shared/services/api.service';
import { ClientsService } from 'app/dashboard/app-services/clients.service';
import { CrossApplicationService } from 'app/shared/services/cross-application.service';
import { Driver } from 'app/app-model/driver';
import { DriversService } from 'app/dashboard/app-services/drivers.service';
import { FilterOption } from 'app/app-model/filter-option';
import { Form } from 'app/app-model/form';
import { FormData } from 'app/app-model/form';
import { FormsService } from 'app/dashboard/app-services/forms.service';
import { OverlayLoaderService } from 'app/shared/services/overlay-loader.service';
import { Quote, QuoteAuto, QuotePolicyHistory, QuoteNewRMVData } from 'app/app-model/quote';
import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { RouteService } from 'app/shared/services/route.service';
import { NavigationExtras, Router } from '@angular/router';
import { SpecsService } from 'app/dashboard/app-services/specs.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { SubsService } from 'app/dashboard/app-services/subs.service';
import { PlansSelectorComponent } from 'app/shared/components/plans-selector/plans-selector.component';
import { LeaveQuoteComponent } from 'app/shared/components/leave-quote/leave-quote.component';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { IterPipe } from 'app/shared/pipes/iter.pipe';
import { constants } from 'fs';
import { PremiumsService } from 'app/dashboard/auto/premiums/premiums.service';
import { ModalboxComponent } from 'app/shared/components/modalbox/modalbox.component';
import { RmvService } from '../../app-services/rmv.service';


interface eventToEmitInterface {
  event: Event;
}

export interface IEventDataValidation {
  isValid: boolean;
  missingUserInputs: TInvalidFieldGroup[];
}

export type TInvalidFieldGroup = 'Rating Plan' | 'Vehicle' | 'Owner';

@Component({
    selector: 'app-plan-selector',
    templateUrl: './plan-selector.component.html',
    styleUrls: ['./plan-selector.component.scss'],
    standalone: false
})
export class PlanSelectorComponent implements OnInit, OnDestroy {

  constructor(
    private router: Router,
    private agencyUserService: AgencyUserService,
    private apiService: ApiService,
    private subsService: SubsService,
    private quotesService: QuotesService,
    private driversService: DriversService,
    private storageService: StorageService,
    private clientsService: ClientsService,
    private overlayLoaderService: OverlayLoaderService,
    private specsService: SpecsService,
    private routeService: RouteService,
    private storageGlobalService: StorageGlobalService,
    private apiCommonService: ApiCommonService,
    private crossApplicationService: CrossApplicationService,
    private formsService: FormsService,
    private premiumsService: PremiumsService,
    private rmvService: RmvService
) { }

  public createdFormHref = '';
  public modelEffectiveDate: string;
  public selectedPlanId: any;
  public selectedPlan: FilterOption;
  public plansCount = 0;
  public invalidPlanQuoteField = false;
  public vehicles: any = {};
  public quoteLob: any;
  public plansOnQuotes: FilterOption[] = [];
  public selectedQuote: Quote = null;
  public selectedClient: any;
  public selectedClientId: number;
  public maskPrivateInformation: false;

  public selectedVehicles: FilterOption[] = [];
  public quoteVehicles: FilterOption[] = [];
  public selectedVehicleIds = [];

  public selectedDrivers: FilterOption[] = [];
  public possibleDrivers: FilterOption[] = [];
  public quoteDrivers: FilterOption[] = [];

  public primaryOwnerId: string;
  public secondaryOwnerId: string;
  public formData: FormData = new FormData();

  public missingUserInputTitle = 'Missing user input';
  public missingUserInputs: TInvalidFieldGroup[] = [];
  public isViewable = false;

  // Subscriptions
  private subscription;
  private quoteSubscription;
  private resetPlansSubscription;
  private subscriptionQuotePlans;
  private subscriptionQuote;
  private getDriversListSubscription;
  private rateIndexSubscription;
  private subscriptionVehicles: ISubscription;

  private agencyInfo;
  private userData: UserData;


  @Output() public onNewFormAutoClick: EventEmitter<eventToEmitInterface> = new EventEmitter();
  @Output() public onCancelClick: EventEmitter<eventToEmitInterface> = new EventEmitter();
  @Output() public validationChecked: EventEmitter<IEventDataValidation> = new EventEmitter();

  @Input() public selectedForm: Form;
  @Input() public createButtonDisabled = false;
  @Input() public standAlone = true;
  @Input() public showInvalidField = false;

  @ViewChild('modalMissingUserInput') public modalMissingUserInput: ModalboxComponent;

  @ViewChild('leaveQuote') leaveQuote: LeaveQuoteComponent;

  ngOnInit() {

    this.agencyUserService.userData$.subscribe(agent => {
      this.agencyInfo = agent;
    });
    this.getCurrentRateIndex();
    this.getQuoteLob();
    this.getQuotePlans();
    this.getQuoteVehicles();
    this.getQuoteDrivers();
    this.getSelectedQuote();
    this.getSelectedClient();
    this.getSelectedPlan();
    this.getClient();
    this.getUser();
    this.checkMissingFieldsValidation();
  }

  ngOnDestroy() {
    this.subscription && this.subscription.unsubscribe();
    this.subscriptionQuote && this.subscriptionQuote.unsubscribe();
    this.subscriptionQuotePlans && this.subscriptionQuotePlans.unsubscribe();
    this.resetPlansSubscription && this.resetPlansSubscription.unsubscribe();
    this.getDriversListSubscription && this.getDriversListSubscription.unsubscribe();
    this.rateIndexSubscription && this.rateIndexSubscription.unsubscribe();
    this.subscriptionVehicles && this.subscriptionVehicles.unsubscribe();
  }

  ngOnChanges(change: SimpleChanges): void {

    // https://bostonsoftware.atlassian.net/browse/SPR-2987 - select Driver as default if only one driver available
    if ('selectedForm' in change) {
      if (change['selectedForm']['currentValue']['driverLimit'] > 0 && this.quoteDrivers && this.quoteDrivers.length === 1 && !this.selectedDrivers.length) {
        this.removeDuplicateSelectedDriver(0, this.quoteDrivers[0].id);
        this.getDriverIndexAndSetSelectedDriver(0, this.quoteDrivers[0].id);
      } else if (change['selectedForm']['currentValue']['driverLimit'] <= 0) {
        this.selectedDrivers = [];
      }

      if (!change['selectedForm']['previousValue'] || change['selectedForm']['currentValue']['vehicleLimit'] !== change['selectedForm']['previousValue']['vehicleLimit']) {
        this.selectedVehicleIds = [];
        this.getQuoteVehicles();
      }

      this.checkMissingFieldsValidation();
    }
  }

  public checkMissingFieldsValidation(): void {
    this.missingUserInputs = [];

    if (!this.selectedPlanId && this.selectedForm.number !== 'RTA') {
      this.missingUserInputs.push('Rating Plan');
    }

    if (this.quoteLob === 'AUTOP') {
      if (this.selectedForm.vehicleLimit > 0) {
        if (this.selectedVehicleIds.length === 0) {
          this.missingUserInputs.push('Vehicle');
        }
      }

      if (this.selectedForm.driverLimit > 0) {
        if (this.selectedDrivers.length === 0) {
          this.missingUserInputs.push('Owner');
        }
      }
    }

    this.validationChecked.next({
      isValid: this.missingUserInputs.length <= 0,
      missingUserInputs: this.missingUserInputs
    });
  }

  public handleNewFormCreateForm($ev): void {
    const baseUrl = document.location.protocol + '//' + document.location.hostname + ':' + document.location.port + '/' + document.location.pathname ;
    const loadingPage =  baseUrl + '/assets/html-templates/loading.html';

    this.checkMissingFieldsValidation();

    if (this.missingUserInputs.length > 0) {
      this.modalMissingUserInput.openModalbox();
    } else {
      let tmpWindow: Window;
      // tmpWindow = window.open('', '_blank');
      tmpWindow = window.open(loadingPage, '_blank');
     this.processAgencyFormRequest(tmpWindow);

     this.onNewFormAutoClick.emit({
      event: $ev
    });

    }
  }

  handleRmvServicesRedirect($ev) {
    const data = {
      transactionType: 'PrefillNewTitleAndRegistration',
      transactionTypeDesc: 'Register and title a new vehicle',
      workflowType: 'RmvServices',
      agencyId: this.userData.agencyId,
      userId: this.userData.user.userId,
    };
  const quoteSessionId = this.selectedQuote.quoteSessionId;
  const driverArray = [];
  this.selectedDrivers.forEach(driver => driverArray.push(driver.id));
  const params: NavigationExtras = {
    queryParams: {id: 'plan-summary'}
  };
  if (this.overlayLoaderService.isOpen) {this.overlayLoaderService.hideLoader(); }
  this.overlayLoaderService.showLoader();
  this.rmvService.createRmvTransactionFromQuote(data, quoteSessionId, driverArray.join(','),
   this.selectedVehicleIds, this.selectedQuote.quoteIdentifier).subscribe(
    x => {
      this.overlayLoaderService.hideLoader();
      this.router.navigate(['/dashboard/rmv-services/rta-prefill'], {queryParams: {id: x.rmvServicesRequestInfo.id, type: 'PrefillNewTitleAndRegistration'}});
    }, err => this.overlayLoaderService.hideLoader()
  );
  }

  public handleCancelClick($ev): void {
    this.onCancelClick.emit({
      event: $ev
    });
  }

  public silentCreateForm(): void {
    let tmpWindow: Window;
    const baseUrl = document.location.protocol + '//' + document.location.hostname + ':' +
                    document.location.port + '/' + document.location.pathname ;
    const loadingPage =  baseUrl + '/assets/html-templates/loading.html';
    tmpWindow = window.open(loadingPage, '_blank');

   this.processAgencyFormRequest(tmpWindow);
}
  private redirectToFormView(): Promise<any> {
    return this.router.navigateByUrl('/dashboard/quotes/new');
  }

  private getQuoteLob(): void {
    this.subscriptionQuote = this.storageService.getStorageData('selectedQuote').subscribe( quote => {
      if (quote && quote.resourceId) {
          this.quoteLob = quote.lob;
      }
    });
  }

  private getQuoteVehicles() {
    this.quoteVehicles = [];
    this.selectedVehicleIds = [];

    this.subscriptionVehicles && this.subscriptionVehicles.unsubscribe();
    this.subscriptionVehicles = this.storageService.getStorageData('vehiclesList').subscribe(data => {
      this.vehicles = data;
      let index = 1;
      this.vehicles.forEach(item => {
        this.quoteVehicles.push({text: item.make + ' ' + item.model, id: index.toString(), data: item});
        index++;
      });
      if (this.selectedForm.vehicleLimit > 0 && this.quoteVehicles.length === 1) {
        this.selectedVehicleIds.push(1);
      }
    });
  }

  public setSelectedVehicle(resourceId: any, index): void {
    if (this.selectedForm.vehicleLimit === 1) {
      this.selectedVehicleIds[0] = index + 1;
    } else {
      let found = false;
      for (let i = 0; i < this.selectedVehicleIds.length; i++) {
        if (this.selectedVehicleIds[i] === index + 1) {
          this.selectedVehicleIds.splice(i, 1);
          found = true;
        }
      }
      if (!found) {
        this.selectedVehicleIds.push(index + 1);
          this.selectedVehicleIds.sort();
      }
    }

    this.checkMissingFieldsValidation();
  }

  public isVehicleSelected(index): boolean {

    for (let i = 0; i < this.selectedVehicleIds.length; i++) {
      if (this.selectedVehicleIds[i] === index + 1) {
        return true;
      }
    }

    return false;
  }

  private getQuotePlans() {
    this.subscriptionQuotePlans = this.storageService.getStorageData('selectedPlan').subscribe(plans => {
    if (plans && plans.items && plans.items.length) {
      this.setPlansOnQuotes(plans.items[0].items);
    }
  });
  }

  private resetPlans() {
    this.overlayLoaderService.showLoader('Loading Plans...');
    this.agencyUserService.userData$.subscribe(agent => {
      if (agent) {
          this.resetPlansSubscription = this.subsService.getRatingPlans(agent.agencyId).subscribe( response => {
          this.overlayLoaderService.hideLoader();
        });
      }
    });
  }

  private setPlansOnQuotes(plans) {
    this.plansOnQuotes = [];
    plans.forEach(item => {
      this.plansOnQuotes.push({text: item.name, id: item.ratingPlanId, data: item});
    });
    this.plansOnQuotes.sort((a, b) => {
      if (a.text < b.text) { return -1;
      } else if (a.text > b.text) {
        return 1;
      } else { return 0; }
    });
  }

  private getQuoteDrivers(): void {
    this.getDriversListSubscription = this.storageService.getStorageData('driversList').subscribe(drivers => {
      let index = 1;
      drivers.forEach(item => {
        this.quoteDrivers.push({text: item.firstName + ' ' + item.lastName, id: index.toString(), data: item});
        index++;
      });
      if (this.selectedForm.driverLimit > 0 && drivers.length === 1) {
          this.selectedDrivers[0] = {text: this.quoteDrivers[0].text,
            id: this.quoteDrivers[0].id, data: this.quoteDrivers[0]};
      }
    });

    this.quoteDrivers.sort((a, b) => {
      if (a.text < b.text) { return -1;
      } else if (a.text > b.text) {
        return 1;
      } else { return 0; }
    });

    this.possibleDrivers = this.quoteDrivers.slice();
  }

  public actionOnSelectPlanId ($event) {
    this.selectedPlanId = $event.id;
    this.checkMissingFieldsValidation();
}

  public getDriverList(index): FilterOption[] {

    // if it is the first drop down
    // then send the full population list
    if (index === 0) {
      return this.quoteDrivers;
    } else {
      const possibleDrivers = this.quoteDrivers.slice();
      for (let i = 0; i < this.selectedDrivers.length; i++) {
        for (let j = 0; j < this.quoteDrivers.length; j++) {
          if ( this.selectedDrivers[i].id === this.quoteDrivers[j].id && i !== index ) {
             possibleDrivers.splice(j, 1);
           }
        }
      }
      return possibleDrivers;
    }
  }

  public actionOnSelectOwner($event, index) {
    this.removeDuplicateSelectedDriver(index, $event.id);
    this.getDriverIndexAndSetSelectedDriver(index, $event.id);
    this.checkMissingFieldsValidation();
   }


private processAgencyFormRequest(formWindow: Window) {

  this.formData.Number = this.selectedForm.number;
  this.formData.ratingPlanId = parseInt(this.selectedPlanId, 10);
  this.formData.drivers = [];
  this.formData.vehicles = [];

  this.selectedDrivers.forEach(item => {
    this.formData.drivers.push(item.id);
  });

  this.selectedVehicleIds.forEach(selectedVehicleId => {
    this.formData.vehicles.push(selectedVehicleId);
  });

  this.formData.clientId = this.selectedClientId;
  this.formData.quoteSessionId = this.selectedQuote.quoteSessionId;
  this.formData.maskPrivateInformation = this.maskPrivateInformation ? true : false;
  const results = this.createAgencyForm (this.formData, formWindow);
}

private removeDuplicateSelectedDriver(index, id) {

    for (let i = 0; i < this.selectedDrivers.length; i++) {
    if ( this.selectedDrivers[i].id === id) {
        this.selectedDrivers.splice(i, 1);
      }
  }
}

private getDriverIndexAndSetSelectedDriver(index, id) {
  for (let i = 0; i < this.quoteDrivers.length; i++) {
    if ( this.quoteDrivers[i].id === id) {
      this.selectedDrivers[index] = {text: this.quoteDrivers[i].text,
         id: this.quoteDrivers[i].id, data: this.quoteDrivers[i]};
    }
  }
}


private createAgencyForm(formData: FormData, formWindow: Window): Promise<any > {
  const baseUrl = document.location.protocol + '//' + document.location.hostname + ':' + document.location.port + '/' + document.location.pathname;

  return new Promise<void>((resolve, reject) => {
    this.overlayLoaderService.showLoader('Please wait...');
    this.formsService.createAgencyForm(formData).pipe(
    take(1))
    .subscribe(agencyForm => {
      this.overlayLoaderService.hideLoader();
      this.createdFormHref = this.agencyInfo.agencyId + '/openform?agencyformid=' +
        agencyForm.identifier + '&agentId=' + this.userData.user.userId  +
        '&ticket=' + encodeURIComponent(agencyForm.authorizationToken.ticket) +
        '&requestId=' + agencyForm.authorizationToken.requestId;
 //     this.crossApplicationService.openFormsAppUrl(this.createdFormHref, true);
      formWindow.location.href = this.apiService.formAppUrl(this.createdFormHref);
{}
      resolve();
      //  this.onCreateFormClick.emit({uri: this.createdFormHref});
      },
      err => {
          formWindow.location.href = baseUrl + '/assets/html-templates/loading-error.html';
          this.overlayLoaderService.hideLoader();
          reject(err);
        }
      );
  });
}

  private getSelectedQuote(): void {
    this.quoteSubscription = this.storageService.getStorageData('selectedQuote').subscribe(res => {
        this.selectedQuote = res;
    });
  }

private getSelectedClient(): void {
  const subscription = this.storageService.getStorageData('clients').subscribe(clients => {
    if (clients.length && clients[0].addresses !== undefined && clients[0].addresses.href) {
      this.selectedClient = clients[0];
    }
  });
}

private getSelectedPlan(): void {
  this.subscription = this.storageService.getStorageData('planSummary').subscribe( summary => {
    if (summary && summary.quoteResponses && summary.quoteResponses.length) {
      const sum = summary.quoteResponses;
      this.selectedPlan = {id: sum[0].ratingPlanId, text: sum[0].ratingPlanName, data: summary};
      this.selectedPlanId = this.selectedPlan.id;
      this.formData.rateResponseId = summary.rateRequestId;
    }
  });
}

private getCurrentRateIndex() {
  this.rateIndexSubscription =
    this.premiumsService.getCurrentRateIndex$.subscribe( currIndex => this.formData.rateResponseIndex = currIndex);
}

  private getClient(): void {
    let localClients = [];

    const subscriptionClients =
      this.clientsService.getClientsList(this.selectedQuote.resourceId).pipe(
        take(1))
        .subscribe(clients => {
          localClients = clients.items;
        });
  }

  private getUser() {
    this.agencyUserService.userData$.pipe(
      first())
      .subscribe(data => this.userData = data);
  }

  public highlightInvalidField(fieldGroupName: TInvalidFieldGroup): boolean {
    if (!this.showInvalidField) {
      return false;
    }

    if (this.missingUserInputs.some((el: TInvalidFieldGroup) => el === fieldGroupName)) {
      return true;
    } else {
      return false;
    }
  }
}
