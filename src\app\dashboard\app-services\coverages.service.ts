
import {take, map} from 'rxjs/operators';
import { Injectable } from '@angular/core';

import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { ApiService } from 'app/shared/services/api.service';
import { QuotesService } from 'app/dashboard/app-services/quotes.service';
import { StorageService } from 'app/shared/services/storage-new.service';
import { ApiCommonService } from 'app/shared/services/api-common.service';
import { SpecsService } from './specs.service';
import { StorageGlobalService } from 'app/shared/services/storage-global.service';
import { AgencyUserService } from 'app/shared/services/agency-user.service';


import {
  Coverage,
  CoverageValue,
  CoverageForVehicle,
  CoverageApiResponseData,
  CoveragesData,
  CoverageLevelDataAPIResponse,
  CoverageLevelData,
  CoverageLevelDataItem,
  CoveragesAutoDefaultStandardCoverages,
  CoveragesAutoDefaultStandardCoveragesItem,
} from 'app/app-model/coverage';
import { Vehicle } from '../../app-model/vehicle';




export class HomeCoveragesConfig {
  constructor(
    public state: string = '',
    public lob: string = '',
    public coverageGroups: string = '',
    public ratingPlans: string = '',
    public formType: string = ''
   ) {}
}

@Injectable()
export class CoveragesService {

  constructor(
    private http: HttpClient,
    private apiService: ApiService,
    private quotesService: QuotesService,
    private storageService: StorageService,
    private specsService: SpecsService,
    private apiCommonService: ApiCommonService,
    private storageGlobalService: StorageGlobalService,
    private agencyUserService: AgencyUserService,
  ) { }

  // Helpers
  // -------------------------------------------------------------------------------

  public filterEmptyCoverages(coveragesList) {
    const _arrFiltered = [];
    if (coveragesList) {
      coveragesList.map((coverage) => {
        if (coverage.values[0].value !== undefined && coverage.values[0].value !== '' && coverage.values[0].value !== 'Omit') {
          _arrFiltered.push(coverage);
        }
      });
    }
    return _arrFiltered;
  }

  // Auto Coverages
  // -------------------------------------------------------------------------------

  public createCoverage(vehicleURL: string, coverageData: any): Observable<any> {
    return this.createCoverageByUri(vehicleURL, coverageData);
  }

  public createCoverageByUri(vehicleURL: string, coverageData: any = {}): Observable<any> {
    return this.http.post(this.apiService.url(vehicleURL), coverageData).pipe(map((res: any) => {
      this.storageService.updateVehiclesListSingleItem(res.parentId, res);
      return res;
    }));
  }

  public convertCoverageToCoverageParsedData(coverage: Coverage, vehicleResourceId: string, quoteId: string = null): CoverageForVehicle {
    const tmpParsedCoverage: CoverageForVehicle = new CoverageForVehicle();
    tmpParsedCoverage.coverageCode = coverage.coverageCode;
    tmpParsedCoverage.coverageDescription = coverage.coverageDescription;
    tmpParsedCoverage.meta = coverage.meta;
    if (coverage.values && coverage.values.length) {
      tmpParsedCoverage.value = coverage.values[0].value;
      tmpParsedCoverage.valueId = coverage.values[0].valueId;
      tmpParsedCoverage.values = coverage.values;
    }
    tmpParsedCoverage.vehicleResourceId = vehicleResourceId;
    if (quoteId) {
      tmpParsedCoverage.quoteId = quoteId;
    }

    return tmpParsedCoverage;
  }

  public convertCoverageApiResponseDataToCoverageParsedDataArray(coverageApiResponseData: CoverageApiResponseData): CoverageForVehicle[] {
    let parsedCoverages: CoverageForVehicle[] = [];
    let coverages: Coverage[] = [];

    if (coverageApiResponseData.items && coverageApiResponseData.items[0] && coverageApiResponseData.items[0].coverages) {
      const vehicleResourceId = coverageApiResponseData.items[0].parentId;
      const quoteId = coverageApiResponseData.items[0].quoteSessionId;
      coverages = coverageApiResponseData.items[0].coverages;

      parsedCoverages = coverages.map((coverage: Coverage) => {
        return this.convertCoverageToCoverageParsedData(coverage, vehicleResourceId, quoteId);
      });
    }

    return parsedCoverages;
  }

  public convertCoverageApiResponseDataArrayToCoverageParsedDataArray(coverageApiResponseData: CoverageApiResponseData[]): CoverageForVehicle[] {
    let parsedCoverages: CoverageForVehicle[] = [];

    if (coverageApiResponseData && coverageApiResponseData.length) {
      coverageApiResponseData.forEach((data: CoverageApiResponseData) => {
        const tmpParsedCoverages: CoverageForVehicle[] = this.convertCoverageApiResponseDataToCoverageParsedDataArray(data);
        parsedCoverages = parsedCoverages.concat(tmpParsedCoverages);
      });
    }

    return parsedCoverages;
  }

  // Home Coverages
  // -------------------------------------------------------------------------------

  public createHomeCoverage(url: string, coverageData: any): Observable<CoveragesData> {
    return this.apiCommonService.postByUri(url, coverageData);
  }

  public getHomeQuoteCoverages$(uri: string): Observable<CoverageApiResponseData> {
    return this.http.get(this.apiService.url(uri)).pipe(map((res: any) => {
      this.storageService.setStorageData('homeQuoteCoverages', res);
      return res;
    }));
  }

  // New Method
  // public createHomeCoverageByUriWithDefaultValues(uri: string, defaultValuesDataSource: Coverage[]|Policy[] = []): Observable<CoveragesData> {
  public createHomeCoverageByUriWithDefaultValues(uri: string, defaultValuesDataSource: Coverage[] = [], forDFIRE: boolean = false): Observable<CoveragesData> {
    const defaultQuoteCoverages: Coverage[] = this.generateHomeDefaultStandardCoverages(forDFIRE);

    console.log('defaultQuoteCoverages', defaultQuoteCoverages);


    // Set default values based on defaultValuesDataSource
    if (defaultValuesDataSource && defaultValuesDataSource.length && defaultQuoteCoverages) {
      defaultQuoteCoverages.forEach(el => {
        const tmpDefaultValue = defaultValuesDataSource.find(data => data.coverageCode === el.coverageCode);

        if (tmpDefaultValue) {
          let valueToSet = '';

          switch (tmpDefaultValue.coverageCode) {
            case 'RCCONT':
            case 'AASPC':
              valueToSet = (tmpDefaultValue.values[0].value) ? tmpDefaultValue.values[0].value : 'No';
              break;
            default:
              valueToSet = tmpDefaultValue.values[0].value;
          }

          el.values[0].value = valueToSet;
          // el.values[0].value = tmpDefaultValue.values[0].value;
        }
      });
    }

    const dataToSend = { coverages: defaultQuoteCoverages };
    return this.apiCommonService.postByUri(uri, dataToSend);
  }


  public generateHomeDefaultStandardCoverages(forDFIRE: boolean = false): Coverage[] {
    const allPerilsDeductible = new Coverage();
    allPerilsDeductible.values = [new CoverageValue()];
    allPerilsDeductible.coverageCode = 'APDED';
    allPerilsDeductible.coverageDescription = 'All Perils Deductible';
    allPerilsDeductible.values[0].value = '';

    const dwelling = new Coverage();
    dwelling.values = [new CoverageValue()];
    dwelling.coverageCode = 'DWELL';
    dwelling.coverageDescription = 'Dwelling';
    dwelling.values[0].value = '';

    const lossOfUse = new Coverage();
    lossOfUse.values = [new CoverageValue()];
    lossOfUse.coverageCode = 'LOU';
    lossOfUse.coverageDescription = 'Loss Of Use';
    lossOfUse.values[0].value = '';
    // lossOfUse.values[0].value = '0';

    const medicalPayment = new Coverage();
    medicalPayment.values = [new CoverageValue()];
    medicalPayment.coverageCode = 'MEDPM';
    medicalPayment.coverageDescription = 'Medical Payment';
    medicalPayment.values[0].value = 'None';

    const otherStructures = new Coverage();
    otherStructures.values = [new CoverageValue()];
    otherStructures.coverageCode = 'OS';
    otherStructures.coverageDescription = 'Other Structures';
    otherStructures.values[0].value = '';
    // otherStructures.values[0].value = '0';

    const personalLiability = new Coverage();
    personalLiability.values = [new CoverageValue()];
    personalLiability.coverageCode = 'PL';
    personalLiability.coverageDescription = 'Personal Liability';
    personalLiability.values[0].value = 'None';

    const personalProperty = new Coverage();
    personalProperty.values = [new CoverageValue()];
    personalProperty.coverageCode = 'PP';
    personalProperty.coverageDescription = 'Personal Property';
    personalProperty.values[0].value = '';
    // personalProperty.values[0].value = '0';

    const replacementCostContents = new Coverage();
    replacementCostContents.values = [new CoverageValue()];
    replacementCostContents.coverageCode = 'RCCONT';
    replacementCostContents.coverageDescription = 'Replacement Cost Contents';
    replacementCostContents.values[0].value = '';

    const specialCoverageA = new Coverage();
    specialCoverageA.values = [new CoverageValue()];
    specialCoverageA.coverageCode = 'AASPC';
    specialCoverageA.coverageDescription = 'Unit Owners Coverage A Special Coverage';
    specialCoverageA.values[0].value = '';

    const replacementCostDwelling = new Coverage();
    replacementCostDwelling.values = [new CoverageValue()];
    replacementCostDwelling.coverageCode = 'RCDWELL';
    replacementCostDwelling.coverageDescription = 'Replacement Cost Dwelling';
    replacementCostDwelling.values[0].value = '';

    const windHailDeductible = new Coverage();
    windHailDeductible.values = [new CoverageValue()];
    windHailDeductible.coverageCode = 'WHDED';
    windHailDeductible.coverageDescription = 'Wind/Hail Deductible';
    windHailDeductible.values[0].value = '';

    const fairRentalValue = new Coverage();
    fairRentalValue.values = [new CoverageValue()];
    fairRentalValue.coverageCode = 'FRV';
    fairRentalValue.coverageDescription = 'Fair Rental Value';
    fairRentalValue.values[0].value = '';

    const NonSmokeValue = new Coverage();
    NonSmokeValue.values = [new CoverageValue()];
    NonSmokeValue.coverageCode = 'NONSMOKE';
    NonSmokeValue.coverageDescription = '';
    NonSmokeValue.values[0].value = '';

    let coverages: Coverage[] = [];

    if (forDFIRE) {
      dwelling.values[0].value = '0';
      otherStructures.values[0].value = '0';
      personalProperty.values[0].value = '0';
      fairRentalValue.values[0].value = '0';
      lossOfUse.values[0].value = '0';

      coverages = [
        allPerilsDeductible,
        dwelling,
        lossOfUse,
        medicalPayment,
        otherStructures,
        personalLiability,
        personalProperty,
        replacementCostContents,
        specialCoverageA,
        replacementCostDwelling,
        windHailDeductible,
        fairRentalValue
      ];
    } else {
      coverages = [
        allPerilsDeductible,
        dwelling,
        lossOfUse,
        medicalPayment,
        otherStructures,
        personalLiability,
        personalProperty,
        replacementCostContents,
        specialCoverageA,
        replacementCostDwelling,
        windHailDeductible,
        NonSmokeValue
      ];
    }

    return coverages;
  }

  //#region Umbrella Quote Coverages
  public createUmbrellaCoverage(url: string, coverageData: any): Observable<CoveragesData> {
    return this.apiCommonService.postByUri(url, coverageData);
  }

  public getUmbrellaQuoteCoverages$(uri: string): Observable<CoverageApiResponseData> {
    return this.http.get(this.apiService.url(uri)).pipe(map((res: any) => {
      if (res.items && res.items.length) {
      this.storageService.setStorageData('umbrellaQuoteCoverages', res);
    }
      return res;
    }));
  }

  public createUmbrellaCoverageByUriWithDefaultValues(uri: string, defaultValuesDataSource: Coverage[] = []): Observable<CoveragesData> {
    const defaultQuoteCoverages: Coverage[] = this.generateUmbrellaDefaultQuoteCoverages();


    // Set default values based on defaultValuesDataSource
    if (defaultValuesDataSource && defaultValuesDataSource.length && defaultQuoteCoverages) {
      defaultQuoteCoverages.forEach(el => {
        const tmpDefaultValue = defaultValuesDataSource.find(data => data.coverageCode === el.coverageCode);

        if (tmpDefaultValue) {
          el.values[0].value = tmpDefaultValue.values[0].value;
        }
      });
    }

    const dataToSend = { coverages: defaultQuoteCoverages };
    return this.apiCommonService.postByUri(uri, dataToSend);
  }

  public generateUmbrellaDefaultQuoteCoverages(): Coverage[] {
    const defaultQuoteCoverageList: Coverage[] = [];
    // defaultQuoteCoverageList.coverages.push(umbrellaTestQuoteCoverage);
    // Total Automobiles
    let umbrellaTestQuoteCoverage = new Coverage();
    umbrellaTestQuoteCoverage.values = [new CoverageValue()];
    umbrellaTestQuoteCoverage.coverageCode = 'BSC-PUMBR-000002';
    umbrellaTestQuoteCoverage.coverageDescription = 'Total Automobiles';
    umbrellaTestQuoteCoverage.values[0].value = '1';
    defaultQuoteCoverageList.push(umbrellaTestQuoteCoverage);

    // Autos - Included
    umbrellaTestQuoteCoverage = new Coverage();
    umbrellaTestQuoteCoverage.values = [new CoverageValue()];
    umbrellaTestQuoteCoverage.coverageCode = 'BSC-PUMBR-000501';
    umbrellaTestQuoteCoverage.coverageDescription = 'Autos - Included';
    umbrellaTestQuoteCoverage.values[0].value = '0';
    defaultQuoteCoverageList.push(umbrellaTestQuoteCoverage);

    // Autos - Initial Vehicle
    umbrellaTestQuoteCoverage = new Coverage();
    umbrellaTestQuoteCoverage.values = [new CoverageValue()];
    umbrellaTestQuoteCoverage.coverageCode = 'BSC-PUMBR-000502';
    umbrellaTestQuoteCoverage.coverageDescription = 'Autos - Initial Vehicle';
    umbrellaTestQuoteCoverage.values[0].value = '0';
    defaultQuoteCoverageList.push(umbrellaTestQuoteCoverage);

    // Autos - Additional Vehicles
    umbrellaTestQuoteCoverage = new Coverage();
    umbrellaTestQuoteCoverage.values = [new CoverageValue()];
    umbrellaTestQuoteCoverage.coverageCode = 'BSC-PUMBR-000503';
    umbrellaTestQuoteCoverage.coverageDescription = 'Autos - Additional Vehicles';
    umbrellaTestQuoteCoverage.values[0].value = '0';
    defaultQuoteCoverageList.push(umbrellaTestQuoteCoverage);

    // Inexp Operators < 3yrs
    umbrellaTestQuoteCoverage = new Coverage();
    umbrellaTestQuoteCoverage.values = [new CoverageValue()];
    umbrellaTestQuoteCoverage.coverageCode = 'BSC-PUMBR-000003';
    umbrellaTestQuoteCoverage.coverageDescription = 'Inexp Operators < 3yrs';
    umbrellaTestQuoteCoverage.values[0].value = '0';
    defaultQuoteCoverageList.push(umbrellaTestQuoteCoverage);

    // Inexp Operators < 6yrs
    umbrellaTestQuoteCoverage = new Coverage();
    umbrellaTestQuoteCoverage.values = [new CoverageValue()];
    umbrellaTestQuoteCoverage.coverageCode = 'BSC-PUMBR-000004';
    umbrellaTestQuoteCoverage.coverageDescription = 'Inexp Operators < 6yrs';
    umbrellaTestQuoteCoverage.values[0].value = '0';
    defaultQuoteCoverageList.push(umbrellaTestQuoteCoverage);

    // Additional Res (Owner Occ)
    umbrellaTestQuoteCoverage = new Coverage();
    umbrellaTestQuoteCoverage.values = [new CoverageValue()];
    umbrellaTestQuoteCoverage.coverageCode = 'BSC-PUMBR-020011';
    umbrellaTestQuoteCoverage.coverageDescription = 'Additional Res (Owner Occ)';
    umbrellaTestQuoteCoverage.values[0].value = '0';
    defaultQuoteCoverageList.push(umbrellaTestQuoteCoverage);

    // Additional Res (Total Rental Units)
    umbrellaTestQuoteCoverage = new Coverage();
    umbrellaTestQuoteCoverage.values = [new CoverageValue()];
    umbrellaTestQuoteCoverage.coverageCode = 'BSC-PUMBR-000070';
    umbrellaTestQuoteCoverage.coverageDescription = 'Additional Res (Total Rental Units)';
    umbrellaTestQuoteCoverage.values[0].value = '0';
    defaultQuoteCoverageList.push(umbrellaTestQuoteCoverage);

    // Rec Vehicle - Licensed (Other)
    umbrellaTestQuoteCoverage = new Coverage();
    umbrellaTestQuoteCoverage.values = [new CoverageValue()];
    umbrellaTestQuoteCoverage.coverageCode = 'BSC-PUMBR-000005';
    umbrellaTestQuoteCoverage.coverageDescription = 'Rec Vehicle - Licensed (Other)';
    umbrellaTestQuoteCoverage.values[0].value = '0';
    defaultQuoteCoverageList.push(umbrellaTestQuoteCoverage);

    // Rec Vehicle - Unlicensed
    umbrellaTestQuoteCoverage = new Coverage();
    umbrellaTestQuoteCoverage.values = [new CoverageValue()];
    umbrellaTestQuoteCoverage.coverageCode = 'BSC-PUMBR-000006';
    umbrellaTestQuoteCoverage.coverageDescription = 'Rec Vehicle - Unlicensed';
    umbrellaTestQuoteCoverage.values[0].value = '0';
    defaultQuoteCoverageList.push(umbrellaTestQuoteCoverage);

    // Incidental Occupancies
    umbrellaTestQuoteCoverage = new Coverage();
    umbrellaTestQuoteCoverage.values = [new CoverageValue()];
    umbrellaTestQuoteCoverage.coverageCode = 'BSC-PUMBR-000042';
    umbrellaTestQuoteCoverage.coverageDescription = 'Incidental Occupancies';
    umbrellaTestQuoteCoverage.values[0].value = '0';
    defaultQuoteCoverageList.push(umbrellaTestQuoteCoverage);

    // Business Pursuits
    umbrellaTestQuoteCoverage = new Coverage();
    umbrellaTestQuoteCoverage.values = [new CoverageValue()];
    umbrellaTestQuoteCoverage.coverageCode = 'BSC-PUMBR-000071';
    umbrellaTestQuoteCoverage.coverageDescription = 'Business Pursuits';
    umbrellaTestQuoteCoverage.values[0].value = '0';
    defaultQuoteCoverageList.push(umbrellaTestQuoteCoverage);

    // Farming
    umbrellaTestQuoteCoverage = new Coverage();
    umbrellaTestQuoteCoverage.values = [new CoverageValue()];
    umbrellaTestQuoteCoverage.coverageCode = 'BSC-PUMBR-000008';
    umbrellaTestQuoteCoverage.coverageDescription = 'Farming';
    umbrellaTestQuoteCoverage.values[0].value = 'No';
    defaultQuoteCoverageList.push(umbrellaTestQuoteCoverage);

    // Rec Vehicle - Motorhomes
    umbrellaTestQuoteCoverage = new Coverage();
    umbrellaTestQuoteCoverage.values = [new CoverageValue()];
    umbrellaTestQuoteCoverage.coverageCode = 'BSC-PUMBR-000010';
    umbrellaTestQuoteCoverage.coverageDescription = 'Rec Vehicle - Motorhomes';
    umbrellaTestQuoteCoverage.values[0].value = '0';
    defaultQuoteCoverageList.push(umbrellaTestQuoteCoverage);

    // Auto Liab Exclusion
    umbrellaTestQuoteCoverage = new Coverage();
    umbrellaTestQuoteCoverage.values = [new CoverageValue()];
    umbrellaTestQuoteCoverage.coverageCode = 'BSC-PUMBR-000012';
    umbrellaTestQuoteCoverage.coverageDescription = 'Auto Liab Exclusion';
    umbrellaTestQuoteCoverage.values[0].value = 'No';
    defaultQuoteCoverageList.push(umbrellaTestQuoteCoverage);

    // Swimming Pool
    umbrellaTestQuoteCoverage = new Coverage();
    umbrellaTestQuoteCoverage.values = [new CoverageValue()];
    umbrellaTestQuoteCoverage.coverageCode = 'BSC-PUMBR-000013';
    umbrellaTestQuoteCoverage.coverageDescription = 'Swimming Pool';
    umbrellaTestQuoteCoverage.values[0].value = 'No';
    defaultQuoteCoverageList.push(umbrellaTestQuoteCoverage);

    // Youthful Operators (under age 25)
    umbrellaTestQuoteCoverage = new Coverage();
    umbrellaTestQuoteCoverage.values = [new CoverageValue()];
    umbrellaTestQuoteCoverage.coverageCode = 'BSC-PUMBR-000014';
    umbrellaTestQuoteCoverage.coverageDescription = 'Youthful Operators (under age 25)';
    umbrellaTestQuoteCoverage.values[0].value = '0';
    defaultQuoteCoverageList.push(umbrellaTestQuoteCoverage);

    // Jet Skis
    umbrellaTestQuoteCoverage = new Coverage();
    umbrellaTestQuoteCoverage.values = [new CoverageValue()];
    umbrellaTestQuoteCoverage.coverageCode = 'BSC-PUMBR-000015';
    umbrellaTestQuoteCoverage.coverageDescription = 'Jet Skis';
    umbrellaTestQuoteCoverage.values[0].value = '0';
    defaultQuoteCoverageList.push(umbrellaTestQuoteCoverage);

    // Trailers
    umbrellaTestQuoteCoverage = new Coverage();
    umbrellaTestQuoteCoverage.values = [new CoverageValue()];
    umbrellaTestQuoteCoverage.coverageCode = 'BSC-PUMBR-000016';
    umbrellaTestQuoteCoverage.coverageDescription = 'Trailers';
    umbrellaTestQuoteCoverage.values[0].value = '0';
    defaultQuoteCoverageList.push(umbrellaTestQuoteCoverage);

    // Antique Autos
    umbrellaTestQuoteCoverage = new Coverage();
    umbrellaTestQuoteCoverage.values = [new CoverageValue()];
    umbrellaTestQuoteCoverage.coverageCode = 'BSC-PUMBR-000017';
    umbrellaTestQuoteCoverage.coverageDescription = 'Antique Autos';
    umbrellaTestQuoteCoverage.values[0].value = '0';
    defaultQuoteCoverageList.push(umbrellaTestQuoteCoverage);

    // Account Credit
    umbrellaTestQuoteCoverage = new Coverage();
    umbrellaTestQuoteCoverage.values = [new CoverageValue()];
    umbrellaTestQuoteCoverage.coverageCode = 'BSC-PUMBR-000011';
    umbrellaTestQuoteCoverage.coverageDescription = 'Account Credit';
    umbrellaTestQuoteCoverage.values[0].value = 'No';
    defaultQuoteCoverageList.push(umbrellaTestQuoteCoverage);

    return defaultQuoteCoverageList;
  }
  //#endregion




  // AUTO COVERAGES - NEW
  // ----------------------------------------------------------------------------

  public createAutoStandardAndAdditionalCoveragesForVehicle(vehicle: Vehicle): Promise<any> {
    return new Promise((resolve, reject) => {
      // this.createStandardCoveragesForVehicle(vehicle)
      this.getAutoDefaultStandardCoverages('')
        .then(defaultCoverages => {
          return Promise.all([
            this.createStandardCoveragesForVehicle(vehicle, defaultCoverages).then(res => {
              return res;
            }),
            this.createAdditionalCoveragesForVehicle(vehicle).then(res => {
              return res;
            })
          ]).then(([standardCoverages, additionalCoverages]) => {
            resolve(true);
          }).catch(err => reject(err));
        });
    });
  }


  // TODO:: create standard and additional coverages
  public createStandardCoveragesForVehicle(vehicle: Vehicle, data?: CoveragesAutoDefaultStandardCoverages): Promise<CoveragesData> {
    return new Promise((resolve, reject) => {
      const uri = vehicle.standardCoverages.meta.href;
      const dataToSend = data || {};
      this.apiCommonService.postByUri(uri, dataToSend).pipe(
        take(1))
        .subscribe((res: CoveragesData) => {
          // console.log('%c VEHICLE STANDARD COVERAGES RES AFTER CREATE: ', 'color:red', res);
          resolve(res);
        }, err => reject(err));
    });
  }

  public createAdditionalCoveragesForVehicle(vehicle: Vehicle, data?: CoveragesAutoDefaultStandardCoverages): Promise<CoveragesData> {
    return new Promise((resolve, reject) => {
      const uri = vehicle.additionalCoverages.meta.href;
      const dataToSend = data || {};
      this.apiCommonService.postByUri(uri, dataToSend).pipe(
        take(1))
        .subscribe((res: CoveragesData) => {
          // console.log('%c VEHICLE Additional COVERAGES RES AFTER CREATE: ', 'color:green', res);
          resolve(res);
        }, err => reject(err));
    });
  }


  // TODO:: get default Vehicle Coverages
  public getAutoDefaultStandardCoverages(level): Promise<CoveragesAutoDefaultStandardCoverages> {
    // let defaultCoverageValues: CoveragesAutoDefaultStandardCoverages = this.storageGlobalService.takeSubs('defaultStandardCoverages');
    let agencyId = '';

    this.agencyUserService.userData$.pipe(take(1)).subscribe(agent => agencyId = agent.agencyId);

    return new Promise((resolve, reject) => {

        this.getCoverage(agencyId, level).pipe(
          take(1))
          .subscribe((res: CoverageLevelData) => {
            const defaultCoverages = res.items.map(item => {
              return {coverageCode: item.coverageCode, values: [{value: item.value}]};
            });

            const defaultCoverageValues = new CoveragesAutoDefaultStandardCoverages();
            defaultCoverageValues.Coverages = defaultCoverages;

            // this.storageGlobalService.setSubs('defaultStandardCoverages', defaultCoverageValues);
            resolve(defaultCoverageValues);
          }, err => reject(err));
    });
  }

  // CoverageLevelData
  private getQuoteCoverageLevelPresetVehicleCoveragesValuesFromAPI(agencyId: string, lob: string,  type: string = 'Default'): Observable<CoverageLevelData> {
    const uri = '/subs/' + agencyId + '/quoteCoverageLevels/lobs/' + lob + '/levels?expand=';
    return this.apiCommonService.getByUri(uri).pipe(map((response: CoverageLevelDataAPIResponse) => {
      let result: CoverageLevelData = new CoverageLevelData();
      if (response && response.items && response.items.length) {
        // Temp fix to grab the first set of defaults for coverages instead of searching on the name, this will need to change when the new story
        // that will allow users to create their own names for the coverage defaults
        const tmpData: CoverageLevelData = response.items[0];

        if (tmpData) {
          result = tmpData;
        } else {
          console.log('There is no QuoteCoverageLevel with name: ', type);
          result.name = 'Not found in API response';
        }
      } else {
        result.name = 'API not respond with correct data.';
      }

      return result;
    }));
  }

  // Get Coverage Levels
  public getCoverageLevels(agencyId, type = 'autostandard') {
    const uri = `/subs/${agencyId}/coveragedefaultlevels/${type}`;
    return this.apiCommonService.getByUri(uri);
  }

  public getCoveragesAsOptions(agencyId) {
    return this.getCoverageLevels(agencyId).pipe(map(res => {
      let options = [];
      options = res.items.map(item => ({id: item.name, text: item.name}));
      options.push({id: 'Custom', text: 'Custom'});
      console.log(options);
      return options;
    }));
  }

  public getCoverage(agencyId, level, type = 'autostandard') {
    const uri = `/subs/${agencyId}/coveragedefaults/${type}?level=${level}`;
    return this.apiCommonService.getByUri(uri).pipe(map(res => {
      const result: CoverageLevelData = new CoverageLevelData();
      const items:  CoverageLevelDataItem[] = [];
       res.items.forEach(cov => {
        const value = cov.values[0].value;
        const coverage = new CoverageLevelDataItem(cov.coverageCode, value);
        items.push(coverage);

      });
      result.items = items;
      return result;
    }));

  }





}
